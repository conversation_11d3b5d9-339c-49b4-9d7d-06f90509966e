package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Veiculos;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class VeiculosDao {

    public void atualizarVeiculoSPM(Veiculos veiculo, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Veiculos SET Numero = ?, Placa = ?, CodFil = ?, Tipo = ?, Modelo = ?, Dt_Alter = ?, Hr_Alter = ?, Operador = ? \n"
                    + " WHERE Numero = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(veiculo.getNumero());
            consulta.setString(veiculo.getPlaca().toUpperCase());
            consulta.setBigDecimal(veiculo.getCodFil());
            consulta.setString(veiculo.getTipo());
            consulta.setInt(veiculo.getModelo());
            consulta.setString(veiculo.getDt_Alter());
            consulta.setString(veiculo.getHr_Alter());
            consulta.setString(veiculo.getOperador());
            consulta.setInt(veiculo.getNumero());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("VeiculosDao.inserirVeiculoSPM - " + e.getMessage() + "\r\n"
                    + " UPDATE Veiculos SET Numero = " + veiculo.getNumero() + ", Placa = " + veiculo.getPlaca() + ", "
                    + "CodFil = " + veiculo.getCodFil() + ", Tipo = " + veiculo.getTipo() + ", Modelo = " + veiculo.getModelo() + ", "
                    + "Dt_Alter = " + veiculo.getDt_Alter() + ", Hr_Alter = " + veiculo.getHr_Alter() + ", Operador = " + veiculo.getOperador() + " \n"
                    + " WHERE Numero = " + veiculo.getNumero());
        }
    }

    public void inserirVeiculoSPM(Veiculos veiculo, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Veiculos (Numero, Placa, CodFil, Tipo, Modelo, Dt_Alter, Hr_Alter, Operador) \n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(veiculo.getNumero());
            consulta.setString(veiculo.getPlaca().toUpperCase());
            consulta.setBigDecimal(veiculo.getCodFil());
            consulta.setString(veiculo.getTipo());
            consulta.setInt(veiculo.getModelo());
            consulta.setString(veiculo.getDt_Alter());
            consulta.setString(veiculo.getHr_Alter());
            consulta.setString(veiculo.getOperador());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("VeiculosDao.inserirVeiculoSPM - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Veiculos (Numero, Placa, CodFil, Tipo, Modelo, Dt_Alter, Hr_Alter, Operador) \n"
                    + " VALUES ((" + veiculo.getNumero() + ", " + veiculo.getPlaca() + ", "
                    + "" + veiculo.getCodFil() + ", " + veiculo.getTipo() + ", " + veiculo.getModelo() + ", "
                    + veiculo.getDt_Alter() + ", " + veiculo.getHr_Alter() + ", " + veiculo.getOperador() + ") ");
        }
    }

    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) total FROM Veiculos \n"
                    + " LEFT JOIN VeiculosMod ON VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " WHERE Veiculos.CodFil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosDao.contagem - " + e.getMessage());
        }
    }

    public List<Veiculos> listagemPaginada(int primeiro, int linhas, BigDecimal codPessoa, Map filtros, Persistencia persistencia) throws Exception {
        List<Veiculos> retorno = new ArrayList();
        try {
            String sql = "SELECT  * "
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY Numero ) AS RowNum, \n"
                    + "          Veiculos.*, VeiculosMod.Descricao DescricaoModelo \n"
                    + "          FROM Veiculos \n"
                    + "          LEFT JOIN VeiculosMod ON VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " WHERE Veiculos.CodFil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }
            sql = sql + ") AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ? \n"
                    + "    AND RowNum < ? \n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Veiculos veiculo;
            while (consulta.Proximo()) {
                veiculo = new Veiculos();
                veiculo.setNumero(consulta.getInt("Numero"));

                DecimalFormat df = new DecimalFormat("00000");
                String c = df.format(consulta.getInt("Numero"));
                veiculo.setNumeroFormatado(c);

                veiculo.setPlaca(consulta.getString("Placa"));
                veiculo.setUF_Placa(consulta.getString("UF_Placa"));
                veiculo.setMun_Placa(consulta.getString("Mun_Placa"));
                veiculo.setModelo(consulta.getInt("Modelo"));
                veiculo.setCategoria(consulta.getString("Categoria"));
                veiculo.setCodFil(consulta.getString("CodFil"));
                veiculo.setAno(consulta.getInt("Ano"));
                veiculo.setAnoModelo(consulta.getInt("AnoModelo"));
                veiculo.setChassis(consulta.getString("Chassis"));
                veiculo.setRENAVAN(consulta.getString("RENAVAN"));
                veiculo.setTipo(consulta.getString("Tipo"));
                veiculo.setCombust(consulta.getString("Combust"));
                veiculo.setPassageiros(consulta.getInt("Passageiros"));
                veiculo.setPotencia(consulta.getInt("Potencia"));
                veiculo.setVeicTerceiros(consulta.getInt("VeicTerceiros"));
                veiculo.setBlindCab(consulta.getString("BlindCab"));
                veiculo.setBlindTeto(consulta.getString("BlindTeto"));
                veiculo.setBlindAssoa(consulta.getString("BlindAssoa"));
                veiculo.setBlindCofre(consulta.getString("BlindCofre"));
                veiculo.setBlindVidro(consulta.getString("BlindVidro"));
                veiculo.setViagem(consulta.getString("Viagem"));
                veiculo.setBacen(consulta.getString("Bacen"));
                veiculo.setAeroporto(consulta.getString("Aeroporto"));
                veiculo.setVistoriaPF(consulta.getString("VistoriaPF"));
                veiculo.setDt_VisPF(consulta.getString("Dt_VisPF"));
                veiculo.setDt_VenPF(consulta.getString("Dt_VenPF"));
                veiculo.setPrefixo(consulta.getString("Prefixo"));
                veiculo.setCarroceria(consulta.getString("Carroceria"));
                veiculo.setApolice(consulta.getString("Apolice"));
                veiculo.setDt_SegIni(consulta.getString("Dt_SegIni"));
                veiculo.setDt_VencSeg(consulta.getString("Dt_VencSeg"));
                veiculo.setSeguradora(consulta.getString("Seguradora"));
                veiculo.setDt_Ipva(consulta.getString("Dt_Ipva"));
                veiculo.setAnoLicen(consulta.getInt("AnoLicen"));
                veiculo.setVistoriaQualid(consulta.getString("VistoriaQualid"));
                veiculo.setDt_VisQualid(consulta.getString("Dt_VisQualid"));
                veiculo.setDt_VenQualid(consulta.getString("Dt_VenQualid"));
                veiculo.setVistoriaConfor(consulta.getString("VistoriaConfor"));
                veiculo.setDt_VisConfor(consulta.getString("Dt_VisConfor"));
                veiculo.setDt_VenConfor(consulta.getString("Dt_VenConfor"));
                veiculo.setVistoriaFabric(consulta.getString("VistoriaFabric"));
                veiculo.setDt_VisFabric(consulta.getString("Dt_VisFabric"));
                veiculo.setDt_VenFabric(consulta.getString("Dt_VenFabric"));
                veiculo.setDt_Compra(consulta.getString("Dt_Compra"));
                veiculo.setObs(consulta.getString("Obs"));
                veiculo.setCodCidade(consulta.getString("CodCidade"));
                veiculo.setMatr_Mot(consulta.getString("Matr_Mot"));
                veiculo.setSituacao(consulta.getString("Situacao"));
                veiculo.setDt_Situac(consulta.getString("Dt_Situac"));
                veiculo.setID_Modulo(consulta.getString("ID_Modulo"));
                veiculo.setCCusto(consulta.getString("CCusto"));
                veiculo.setVistoriaQualidII(consulta.getString("VistoriaQualidII"));
                veiculo.setDt_VisQualidII(consulta.getString("Dt_VisQualidII"));
                veiculo.setDt_VenQualidII(consulta.getString("Dt_VenQualidII"));
                veiculo.setVistoriaConforII(consulta.getString("VistoriaConforII"));
                veiculo.setDt_VisConforII(consulta.getString("Dt_VisConforII"));
                veiculo.setDt_VenConforII(consulta.getString("Dt_VenConforII"));
                veiculo.setKMAuditoria(consulta.getString("KMAuditoria"));
                veiculo.setDtKmAuditoria(consulta.getString("DtKmAuditoria"));
                veiculo.setAuditor(consulta.getString("Auditor"));
                veiculo.setDtAuditoria(consulta.getString("DtAuditoria"));
                veiculo.setHrAuditoria(consulta.getString("HrAuditoria"));
                veiculo.setAlvara(consulta.getString("Alvara"));
                veiculo.setDt_Alvara(consulta.getString("Dt_Alvara"));
                veiculo.setDt_VencAlvara(consulta.getString("Dt_VencAlvara"));
                veiculo.setTacografo(consulta.getString("Tacografo"));
                veiculo.setDt_Tacografo(consulta.getString("Dt_Tacografo"));
                veiculo.setDt_VencTacografo(consulta.getString("Dt_VencTacografo"));
                veiculo.setOperador(consulta.getString("Operador"));
                veiculo.setDt_Alter(consulta.getString("Dt_Alter"));
                veiculo.setHr_Alter(consulta.getString("Hr_Alter"));

                veiculo.setDescricaoModelo(consulta.getString("DescricaoModelo"));

                retorno.add(veiculo);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosDao.listagemPaginada - " + e.getMessage());
        }
    }

    /**
     * Lista todos os veículos cadastrados por filial (numero, placa e modelo)
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Veiculos> getVeiculos(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Veiculos> retorno = new ArrayList<>();
            String sql = "select numero, placa, Matr_Mot, VeiculosMod.descricao, codfil, ccusto, combust \n"
                    + " from veiculos \n"
                    + " left join VeiculosMod on VeiculosMod.codigo = veiculos.modelo \n"
                    + " where veiculos.codfil = ? AND veiculos.Situacao = 'A' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil.replace(".0", ""));
            consulta.select();
            Veiculos veiculo;
            while (consulta.Proximo()) {
                veiculo = new Veiculos();
                veiculo.setCodFil(consulta.getString("codfil"));
                veiculo.setNumero(consulta.getInt("numero"));
                veiculo.setPlaca(consulta.getString("placa"));
                veiculo.setMatr_Mot(consulta.getString("Matr_Mot"));
                veiculo.setObs(consulta.getString("descricao"));
                veiculo.setCCusto(consulta.getString("ccusto"));
                veiculo.setCombust(consulta.getString("combust"));
                veiculo.setCodFil(consulta.getString("codfil"));
                retorno.add(veiculo);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("VeiculosDao.getVeiculos - " + e.getMessage() + "\r\n"
                    + "select numero, placa, Matr_Mot, VeiculosMod.descricao, codfil, ccusto, combust \n"
                    + " from veiculos \n"
                    + " left join VeiculosMod on VeiculosMod.codigo = veiculos.modelo \n"
                    + " where veiculos.codfil = " + codFil + " AND veiculos.Situacao = 'A' ");
        }
    }

    /**
     * Lista centro de custo do veiculo
     *
     * @param Veiculo - código do veículo
     * @param persistencia - conexão ao banco
     * @return - retorna lista contendo veiculo e centro de custo
     * @throws Exception
     */
    public List<Veiculos> buscaDadosVeiculo(int Veiculo, Persistencia persistencia) throws Exception {
        String sql;
        Veiculos veiculo;
        Consulta consulta;
        List<Veiculos> lveiculos = new ArrayList();
        sql = "select numero, placa, Matr_Mot, VeiculosMod.descricao, codfil, ccusto, combust \n"
                + " from veiculos \n"
                + " left join VeiculosMod on VeiculosMod.codigo = veiculos.modelo \n"
                + " where veiculos.numero = ? AND veiculos.Situacao = 'A' ";
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setInt(Veiculo);
            consulta.select();
            while (consulta.Proximo()) {
                veiculo = new Veiculos();
                veiculo.setCodFil(consulta.getString("codfil"));
                veiculo.setNumero(consulta.getInt("numero"));
                veiculo.setPlaca(consulta.getString("placa"));
                veiculo.setMatr_Mot(consulta.getString("Matr_Mot"));
                veiculo.setObs(consulta.getString("descricao"));
                veiculo.setCCusto(consulta.getString("ccusto"));
                veiculo.setCombust(consulta.getString("combust"));
                veiculo.setCodFil(consulta.getString("codfil"));
                lveiculos.add(veiculo);
            }
            consulta.Close();
            return lveiculos;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar centro de custo do veiculos - " + e.getMessage());
        }
    }

    public Integer buscaVeiculo(String sCodPessoa, Persistencia persistencia) throws Exception {
        String data_atual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String sql;
        Consulta consult;
        Integer iVeiculo = 0;

        sql = "Select Escala.Veiculo "
                + " from Pessoa "
                + " Left Join Escala on Escala.CodPessoaSup = Pessoa.Codigo "
                + " and Escala.Data = ? "
                + " Left Join Funcion on Funcion.Matr = Pessoa.Matr "
                + " Where Pessoa.Codigo = ? "
                + " and Escala.Veiculo <> 9999 ";
        try {
            consult = new Consulta(sql, persistencia);
            consult.setString(data_atual);
            consult.setString(sCodPessoa);
            consult.select();
            while (consult.Proximo()) {
                iVeiculo = consult.getInt("Veiculo");
            }
            consult.Close();
            return iVeiculo;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar veiculo - " + e.getMessage());
        }
    }

    public Integer buscaVeiculo(String sCodPessoa, String data_atual, Persistencia persistencia) throws Exception {
        String sql;
        Consulta consult;
        Integer iVeiculo = 0;

        sql = " Select Escala.Veiculo "
                + " from Pessoa "
                + " Left Join Funcion on Funcion.Matr = Pessoa.Matr "
                + " Left Join Escala on (Escala.MatrChe = Funcion.Matr or Escala.CodPessoaSup = Pessoa.Codigo) "
                + "        and Escala.Data = ? "
                + " Where Pessoa.Codigo = ? "
                + " and Escala.Veiculo <> 9999 ";
        try {
            consult = new Consulta(sql, persistencia);
            consult.setString(data_atual);
            consult.setString(sCodPessoa);
            consult.select();
            while (consult.Proximo()) {
                iVeiculo = consult.getInt("Veiculo");
            }
            consult.Close();
            return iVeiculo;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar veiculo - " + e.getMessage());
        }
    }

    /**
     * Lista veículos por filial
     *
     * @param codfil Código de filial
     * @param persistencia Conexão com o banco
     * @return Retorna uma Lista com os veículos
     * @throws Exception
     */
    public List<Veiculos> ListaVeiculo(String codfil, Persistencia persistencia) throws Exception {
        List<Veiculos> retorno = new ArrayList<>();
        Veiculos veiculos = new Veiculos();

        String sql = "SELECT numero, placa,uf_placa,mun_placa, Matr_Mot, "
                + "  modelo, categoria, categoria, codfil, "
                + " ano, anomodelo, chassis, renavan, "
                + " tipo, combust"
                + " FROM veiculos "
                + " WHERE veiculos.codfil = ? and situacao = 'A'";

        try {

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                veiculos = new Veiculos();
                veiculos.setNumero(consulta.getInt("numero"));
                veiculos.setPlaca(consulta.getString("placa"));
                veiculos.setUF_Placa(consulta.getString("uf_placa"));
                veiculos.setMatr_Mot(consulta.getString("Matr_Mot"));
                veiculos.setMun_Placa(consulta.getString("mun_placa"));
                veiculos.setModelo(consulta.getInt("modelo"));
                veiculos.setCategoria(consulta.getString("categoria"));
                veiculos.setCodFil(consulta.getString("codfil"));
                veiculos.setAno(consulta.getInt("ano"));
                veiculos.setAnoModelo(consulta.getInt("anomodelo"));
                veiculos.setChassis(consulta.getString("chassis"));
                veiculos.setRENAVAN(consulta.getString("renavan"));
                veiculos.setTipo(consulta.getString("tipo"));
                veiculos.setCombust(consulta.getString("combust"));

                retorno.add(veiculos);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list veiculo - " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Busca o veículo a partir do número
     *
     * @param numero Número do veículo
     * @param persistencia Conexão com o banco
     * @return O veículo selecionado
     * @throws Exception
     */
    public Veiculos BuscaVeiculoNumero(int numero, Persistencia persistencia) throws Exception {
        Veiculos veiculos = new Veiculos();

        String sql = "SELECT numero, placa,uf_placa,mun_placa, "
                + "  modelo, categoria, categoria, codfil, "
                + " ano, anomodelo, chassis, renavan, "
                + " tipo, combust"
                + " FROM veiculos "
                + " WHERE veiculos.numero = ?";

        try {

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(numero);
            consulta.select();

            while (consulta.Proximo()) {
                veiculos.setNumero(consulta.getInt("numero"));
                veiculos.setPlaca(consulta.getString("placa"));
                veiculos.setUF_Placa("uf_placa");
                veiculos.setMun_Placa(consulta.getString("mun_placa"));
                veiculos.setModelo(consulta.getInt("modelo"));
                veiculos.setCategoria(consulta.getString("categoria"));
                veiculos.setCodFil(consulta.getString("codfil"));
                veiculos.setAno(consulta.getInt("ano"));
                veiculos.setAnoModelo(consulta.getInt("anomodelo"));
                veiculos.setChassis(consulta.getString("chassis"));
                veiculos.setRENAVAN(consulta.getString("renavan"));
                veiculos.setTipo(consulta.getString("tipo"));
                veiculos.setCombust(consulta.getString("combust"));

            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list veiculo - " + e.getMessage());
        }

        return veiculos;
    }

    public Veiculos BuscaVeiculoPlaca(String Placa, Persistencia persistencia) throws Exception {
        Veiculos veiculos = new Veiculos();

        String sql = "SELECT numero, placa,uf_placa,mun_placa, "
                + "  modelo, categoria, categoria, codfil, "
                + " ano, anomodelo, chassis, renavan, "
                + " tipo, combust"
                + " FROM veiculos "
                + " WHERE veiculos.placa = ?";

        try {

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Placa);
            consulta.select();

            while (consulta.Proximo()) {
                veiculos.setNumero(consulta.getInt("numero"));
                veiculos.setPlaca(consulta.getString("placa"));
                veiculos.setUF_Placa("uf_placa");
                veiculos.setMun_Placa(consulta.getString("mun_placa"));
                veiculos.setModelo(consulta.getInt("modelo"));
                veiculos.setCategoria(consulta.getString("categoria"));
                veiculos.setCodFil(consulta.getString("codfil"));
                veiculos.setAno(consulta.getInt("ano"));
                veiculos.setAnoModelo(consulta.getInt("anomodelo"));
                veiculos.setChassis(consulta.getString("chassis"));
                veiculos.setRENAVAN(consulta.getString("renavan"));
                veiculos.setTipo(consulta.getString("tipo"));
                veiculos.setCombust(consulta.getString("combust"));

            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list veiculo - " + e.getMessage());
        }

        return veiculos;
    }

    /**
     * Busca o próximo número do veículo disponível
     *
     * @param persistencia Conexão com o banco de dados
     * @return Retorna o número disponível para veículo
     * @throws Exception
     */
    public int MaxNumero(Persistencia persistencia) throws Exception {
        int retorno = 0;
        int max;
        try {
            String sql = " Select Min(NumFaltante) NumVeic from \n"
                    + " (SELECT Numero\n"
                    + " ,Numero - (ROW_NUMBER() OVER(ORDER BY Numero)) AS DIF\n"
                    + " ,Numero - (Numero - (ROW_NUMBER() OVER(ORDER BY Numero))) NumFaltante\n"
                    + " FROM  Veiculos) a\n"
                    + " where a.DIF > 0";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getInt("NumVeic");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list veiculo - " + e.getMessage());
        }

        return retorno;
    }

    public void insereVeiculoSatMobWeb(Veiculos veiculos, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO veiculos (codfil, numero, situacao, ccusto, placa, ano, AnoModelo, mun_placa, uf_placa, modelo, \n"
                    + " Carroceria, Tipo, VeicTerceiros, Combust, chassis, dt_compra, renavan, dt_ipva, Obs, Seguradora, apolice, \n"
                    + " Dt_SegIni, Dt_VencSeg, \n"
                    + " Matr_Mot, Prefixo, ID_Modulo, \n"
                    + " BlindCab, BlindTeto, BlindAssoa, BlindCofre, BlindVidro, Viagem, Bacen, Aeroporto, \n"
                    + " VistoriaPF, Dt_VisPF, Dt_VenPF, VistoriaFabric, Dt_VisFabric, Dt_VenFabric, VistoriaQualid, Dt_VisQualid, Dt_VenQualid, \n"
                    + " VistoriaQualidII, Dt_VisQualidII, Dt_VenQualidII, VistoriaConfor, Dt_VisConfor, Dt_VenConfor, \n"
                    + " VistoriaConforII, Dt_VisConforII, Dt_VenConforII, Tacografo, Dt_Tacografo, Dt_VencTacografo, Alvara, Dt_Alvara, Dt_VencAlvara, \n"
                    + " operador, dt_alter, hr_alter, Dt_Situac)\n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + " ?, ?, \n"
                    + " ?, ?, ?, \n"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + " ?, ?, ?, ?, ?, ?, \n"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + " ?, ?, ?, ?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(veiculos.getCodFil());
            consulta.setInt(veiculos.getNumero());
            consulta.setString(veiculos.getSituacao());
            consulta.setString(veiculos.getCCusto());
            consulta.setString(veiculos.getPlaca());
            consulta.setInt(veiculos.getAno());
            consulta.setInt(veiculos.getAnoModelo());
            consulta.setString(veiculos.getMun_Placa());
            consulta.setString(veiculos.getUF_Placa());
            consulta.setInt(veiculos.getModelo());
            consulta.setString(veiculos.getCarroceria());
            consulta.setString(veiculos.getTipo());
            consulta.setInt(veiculos.getVeicTerceiros());
            consulta.setString(veiculos.getCombust());
            consulta.setString(veiculos.getChassis());
            consulta.setString(veiculos.getDt_Compra());
            consulta.setString(veiculos.getRENAVAN());
            consulta.setString(veiculos.getDt_Ipva());
            consulta.setString(veiculos.getObs());
            consulta.setString(veiculos.getSeguradora());
            consulta.setString(veiculos.getApolice());
            consulta.setString(veiculos.getDt_SegIni());
            consulta.setString(veiculos.getDt_VencSeg());
            consulta.setBigDecimal(veiculos.getMatr_Mot());
            consulta.setString(veiculos.getPrefixo());
            consulta.setString(veiculos.getID_Modulo());
            consulta.setString(veiculos.getBlindCab());
            consulta.setString(veiculos.getBlindTeto());
            consulta.setString(veiculos.getBlindAssoa());
            consulta.setString(veiculos.getBlindCofre());
            consulta.setString(veiculos.getBlindVidro());
            consulta.setString(veiculos.getViagem());
            consulta.setString(veiculos.getBacen());
            consulta.setString(veiculos.getAeroporto());
            consulta.setString(veiculos.getVistoriaPF());
            consulta.setString(veiculos.getDt_VisPF());
            consulta.setString(veiculos.getDt_VenPF());
            consulta.setString(veiculos.getVistoriaFabric());
            consulta.setString(veiculos.getDt_VisFabric());
            consulta.setString(veiculos.getDt_VenFabric());
            consulta.setString(veiculos.getVistoriaQualid());
            consulta.setString(veiculos.getDt_VisQualid());
            consulta.setString(veiculos.getDt_VenQualid());
            consulta.setString(veiculos.getVistoriaQualidII());
            consulta.setString(veiculos.getDt_VisQualidII());
            consulta.setString(veiculos.getDt_VenQualidII());
            consulta.setString(veiculos.getVistoriaConfor());
            consulta.setString(veiculos.getDt_VisConfor());
            consulta.setString(veiculos.getDt_VenConfor());
            consulta.setString(veiculos.getVistoriaConforII());
            consulta.setString(veiculos.getDt_VisConforII());
            consulta.setString(veiculos.getDt_VenConforII());
            consulta.setString(veiculos.getTacografo());
            consulta.setString(veiculos.getDt_Tacografo());
            consulta.setString(veiculos.getDt_VencTacografo());
            consulta.setString(veiculos.getAlvara());
            consulta.setString(veiculos.getDt_Alvara());
            consulta.setString(veiculos.getDt_VencAlvara());
            consulta.setString(veiculos.getOperador());
            consulta.setString(veiculos.getDt_Alter());
            consulta.setString(veiculos.getHr_Alter());
            consulta.setString(veiculos.getDt_Situac());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("VeiculosDao.insereVeiculoSatMobWeb - " + e.getMessage() + "\r\n"
                    + "INSERT INTO veiculos (codfil, numero, situacao, ccusto, placa, ano, AnoModelo, mun_placa, uf_placa, modelo, \n"
                    + " Carroceria, Tipo, VeicTerceiros, Combust, chassis, dt_compra, renavan, dt_ipva, Obs, Seguradora, apolice, \n"
                    + " Dt_SegIni, Dt_VencSeg, \n"
                    + " Matr_Mot, Prefixo, ID_Modulo, \n"
                    + " BlindCab, BlindTeto, BlindAssoa, BlindCofre, BlindVidro, Viagem, Bacen, Aeroporto, \n"
                    + " VistoriaPF, Dt_VisPF, Dt_VenPF, VistoriaFabric, Dt_VisFabric, Dt_VenFabric, VistoriaQualid, Dt_VisQualid, Dt_VenQualid, \n"
                    + " VistoriaQualidII, Dt_VisQualidII, Dt_VenQualidII, VistoriaConfor, Dt_VisConfor, Dt_VenConfor, \n"
                    + " VistoriaConforII, Dt_VisConforII, Dt_VenConforII, Tacografo, Dt_Tacografo, Dt_VencTacografo, \n"
                    + " operador, dt_alter, hr_alter, Dt_Situac)"
                    + "VALUES (" + veiculos.getCodFil() + "," + veiculos.getNumero() + "," + veiculos.getSituacao() + "," + veiculos.getCCusto() + ","
                    + veiculos.getPlaca() + "," + veiculos.getAno() + "," + veiculos.getAnoModelo() + "," + veiculos.getMun_Placa() + ","
                    + veiculos.getUF_Placa() + "," + veiculos.getModelo() + "," + veiculos.getCarroceria() + "," + veiculos.getTipo() + ","
                    + veiculos.getVeicTerceiros() + "," + veiculos.getCombust() + "," + veiculos.getChassis() + "," + veiculos.getDt_Compra() + ","
                    + veiculos.getRENAVAN() + "," + veiculos.getDt_Ipva() + "," + veiculos.getObs() + "," + veiculos.getSeguradora() + ","
                    + veiculos.getApolice() + "," + veiculos.getDt_SegIni() + "," + veiculos.getDt_VencSeg() + "," + veiculos.getMatr_Mot() + ","
                    + veiculos.getPrefixo() + "," + veiculos.getID_Modulo() + "," + veiculos.getBlindCab() + "," + veiculos.getBlindTeto() + ","
                    + veiculos.getBlindAssoa() + "," + veiculos.getBlindCofre() + "," + veiculos.getBlindVidro() + "," + veiculos.getViagem() + ","
                    + veiculos.getBacen() + "," + veiculos.getAeroporto() + "," + veiculos.getVistoriaPF() + "," + veiculos.getDt_VisPF() + ","
                    + veiculos.getDt_VenPF() + "," + veiculos.getVistoriaFabric() + "," + veiculos.getDt_VisFabric() + "," + veiculos.getDt_VenFabric() + ","
                    + veiculos.getVistoriaQualid() + "," + veiculos.getDt_VisQualid() + "," + veiculos.getDt_VenQualid() + ","
                    + veiculos.getVistoriaQualidII() + "," + veiculos.getDt_VisQualidII() + "," + veiculos.getDt_VenQualidII() + ","
                    + veiculos.getVistoriaConfor() + "," + veiculos.getDt_VisConfor() + "," + veiculos.getDt_VenConfor() + ","
                    + veiculos.getVistoriaConforII() + "," + veiculos.getDt_VisConforII() + "," + veiculos.getDt_VenConforII() + ","
                    + veiculos.getTacografo() + "," + veiculos.getDt_Tacografo() + "," + veiculos.getDt_VencTacografo() + "," + veiculos.getOperador() + ","
                    + veiculos.getDt_Alter() + "," + veiculos.getHr_Alter() + "," + veiculos.getDt_Situac());

        }
    }

    public void atualizarVeiculoSatMobWeb(Veiculos veiculos, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Veiculos SET codfil = ?, situacao = ?, ccusto = ?, placa = ?, ano = ?, AnoModelo = ?, mun_placa = ?, uf_placa = ?, \n"
                    + " modelo = ?, Carroceria = ?, Tipo = ?, VeicTerceiros = ?, Combust = ?, chassis = ?, dt_compra = ?, renavan = ?, dt_ipva = ?, Obs = ?, \n"
                    + " Seguradora = ?, apolice = ?, Dt_SegIni = ?, Dt_VencSeg = ?,  Matr_Mot = ?, Prefixo = ?, ID_Modulo = ?, \n"
                    + " BlindCab = ?, BlindTeto = ?, BlindAssoa = ?, BlindCofre = ?, BlindVidro = ?, Viagem = ?, Bacen = ?, Aeroporto = ?, \n"
                    + " VistoriaPF = ?, Dt_VisPF = ?, Dt_VenPF = ?, VistoriaFabric = ?, Dt_VisFabric = ?, Dt_VenFabric = ?, VistoriaQualid = ?, \n"
                    + " Dt_VisQualid = ?, Dt_VenQualid = ?, VistoriaQualidII = ?, Dt_VisQualidII = ?, Dt_VenQualidII = ?, \n"
                    + " VistoriaConfor = ?, Dt_VisConfor = ?, Dt_VenConfor = ?,  VistoriaConforII = ?, Dt_VisConforII = ?, Dt_VenConforII = ?, \n"
                    + " Tacografo = ?, Dt_Tacografo = ?, Dt_VencTacografo = ?, Alvara = ?, Dt_Alvara = ?, Dt_VencAlvara = ?, \n"
                    + " operador = ?, dt_alter = ?, hr_alter = ?, Dt_Situac = ? \n"
                    + " WHERE Numero = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(veiculos.getCodFil());
            consulta.setString(veiculos.getSituacao());
            consulta.setString(veiculos.getCCusto());
            consulta.setString(veiculos.getPlaca());
            consulta.setInt(veiculos.getAno());
            consulta.setInt(veiculos.getAnoModelo());
            consulta.setString(veiculos.getMun_Placa());
            consulta.setString(veiculos.getUF_Placa());
            consulta.setInt(veiculos.getModelo());
            consulta.setString(veiculos.getCarroceria());
            consulta.setString(veiculos.getTipo());
            consulta.setInt(veiculos.getVeicTerceiros());
            consulta.setString(veiculos.getCombust());
            consulta.setString(veiculos.getChassis());
            consulta.setString(veiculos.getDt_Compra());
            consulta.setString(veiculos.getRENAVAN());
            consulta.setString(veiculos.getDt_Ipva());
            consulta.setString(veiculos.getObs());
            consulta.setString(veiculos.getSeguradora());
            consulta.setString(veiculos.getApolice());
            consulta.setString(veiculos.getDt_SegIni());
            consulta.setString(veiculos.getDt_VencSeg());
            consulta.setBigDecimal(veiculos.getMatr_Mot());
            consulta.setString(veiculos.getPrefixo());
            consulta.setString(veiculos.getID_Modulo());
            consulta.setString(veiculos.getBlindCab());
            consulta.setString(veiculos.getBlindTeto());
            consulta.setString(veiculos.getBlindAssoa());
            consulta.setString(veiculos.getBlindCofre());
            consulta.setString(veiculos.getBlindVidro());
            consulta.setString(veiculos.getViagem());
            consulta.setString(veiculos.getBacen());
            consulta.setString(veiculos.getAeroporto());
            consulta.setString(veiculos.getVistoriaPF());
            consulta.setString(veiculos.getDt_VisPF());
            consulta.setString(veiculos.getDt_VenPF());
            consulta.setString(veiculos.getVistoriaFabric());
            consulta.setString(veiculos.getDt_VisFabric());
            consulta.setString(veiculos.getDt_VenFabric());
            consulta.setString(veiculos.getVistoriaQualid());
            consulta.setString(veiculos.getDt_VisQualid());
            consulta.setString(veiculos.getDt_VenQualid());
            consulta.setString(veiculos.getVistoriaQualidII());
            consulta.setString(veiculos.getDt_VisQualidII());
            consulta.setString(veiculos.getDt_VenQualidII());
            consulta.setString(veiculos.getVistoriaConfor());
            consulta.setString(veiculos.getDt_VisConfor());
            consulta.setString(veiculos.getDt_VenConfor());
            consulta.setString(veiculos.getVistoriaConforII());
            consulta.setString(veiculos.getDt_VisConforII());
            consulta.setString(veiculos.getDt_VenConforII());
            consulta.setString(veiculos.getTacografo());
            consulta.setString(veiculos.getDt_Tacografo());
            consulta.setString(veiculos.getDt_VencTacografo());
            consulta.setString(veiculos.getAlvara());
            consulta.setString(veiculos.getDt_Alvara());
            consulta.setString(veiculos.getDt_VencAlvara());
            consulta.setString(veiculos.getOperador());
            consulta.setString(veiculos.getDt_Alter());
            consulta.setString(veiculos.getHr_Alter());
            consulta.setString(veiculos.getDt_Situac());
            consulta.setInt(veiculos.getNumero());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("VeiculosDao.atualizarVeiculoSatMobWeb - " + e.getMessage() + "\r\n"
                    + " UPDATE  Veiculos SET codfil = " + veiculos.getCodFil() + ", situacao = " + veiculos.getSituacao() + ", ccusto = " + veiculos.getCCusto() + ", "
                    + " placa = " + veiculos.getPlaca() + ", ano = " + veiculos.getAno() + ", AnoModelo = " + veiculos.getAnoModelo() + ","
                    + " mun_placa = " + veiculos.getMun_Placa() + ", uf_placa = " + veiculos.getUF_Placa() + ", \n modelo = " + veiculos.getModelo() + ","
                    + " Carroceria = " + veiculos.getCarroceria() + ", Tipo = " + veiculos.getTipo() + ", VeicTerceiros = " + veiculos.getVeicTerceiros() + ","
                    + " Combust = " + veiculos.getCombust() + ", chassis = " + veiculos.getChassis() + ", dt_compra = " + veiculos.getDt_Compra() + ","
                    + " renavan = " + veiculos.getRENAVAN() + ", dt_ipva = " + veiculos.getDt_Ipva() + ", Obs = " + veiculos.getObs() + ", \n"
                    + " Seguradora = " + veiculos.getSeguradora() + ", apolice = " + veiculos.getApolice() + ", Dt_SegIni = " + veiculos.getDt_SegIni() + ","
                    + " Dt_VencSeg = " + veiculos.getDt_VencSeg() + ", Matr_Mot = " + veiculos.getMatr_Mot() + ", Prefixo = " + veiculos.getPrefixo() + ","
                    + " ID_Modulo = " + veiculos.getID_Modulo() + ", \n BlindCab = " + veiculos.getBlindCab() + ", BlindTeto = " + veiculos.getBlindTeto() + ","
                    + " BlindAssoa = " + veiculos.getBlindAssoa() + ", BlindCofre = " + veiculos.getBlindCofre() + ", BlindVidro = " + veiculos.getBlindVidro() + ","
                    + " Viagem = " + veiculos.getViagem() + ", Bacen = " + veiculos.getBacen() + ", Aeroporto = " + veiculos.getAeroporto() + ", \n"
                    + " VistoriaPF = " + veiculos.getVistoriaPF() + ", Dt_VisPF = " + veiculos.getDt_VisPF() + ", Dt_VenPF = " + veiculos.getDt_VenPF() + ","
                    + " VistoriaFabric = " + veiculos.getVistoriaFabric() + ", Dt_VisFabric = " + veiculos.getDt_VisFabric() + ", Dt_VenFabric = "
                    + veiculos.getDt_VenFabric() + ", VistoriaQualid = " + veiculos.getVistoriaQualid() + ", Dt_VisQualid = " + veiculos.getDt_VisQualid() + ", \n"
                    + " Dt_VenQualid = " + veiculos.getDt_VenQualid() + ", VistoriaQualidII = " + veiculos.getVistoriaQualidII() + ", Dt_VisQualidII = "
                    + veiculos.getDt_VisQualidII() + ", Dt_VenQualidII = " + veiculos.getDt_VenQualidII() + ", \n VistoriaConfor = " + veiculos.getVistoriaConfor() + ","
                    + " Dt_VisConfor = " + veiculos.getDt_VisConfor() + ", Dt_VenConfor = " + veiculos.getDt_VenConfor() + ", \n"
                    + " VistoriaConforII = " + veiculos.getVistoriaConforII() + ", Dt_VisConforII = " + veiculos.getDt_VisConforII() + ", Dt_VenConforII = "
                    + veiculos.getDt_VenConforII() + ", Tacografo = " + veiculos.getTacografo() + ", Dt_Tacografo = " + veiculos.getDt_Tacografo() + ","
                    + " Dt_VencTacografo = " + veiculos.getDt_VencTacografo() + ", Alvara = " + veiculos.getAlvara() + ", Dt_Alvara = " + veiculos.getDt_Alvara() + ","
                    + " Dt_VencAlvara = " + veiculos.getDt_VencAlvara() + ", \noperador = " + veiculos.getOperador() + ", dt_alter = " + veiculos.getDt_Alter() + ","
                    + " hr_alter = " + veiculos.getHr_Alter() + ", Dt_Situac = " + veiculos.getDt_Situac() + ", \n"
                    + " WHERE Numero = " + veiculos.getNumero());
        }
    }

    /**
     * Busca o veículo a partir do número
     *
     * @param numero Número do veículo
     * @param persistencia Conexão com o banco
     * @return O veículo selecionado
     * @throws Exception
     */
    public Veiculos buscarVeiculo(int numero, Persistencia persistencia) throws Exception {

        String sql = "SELECT * "
                + " FROM veiculos "
                + " WHERE veiculos.numero = ?";

        try {

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(numero);
            consulta.select();

            Veiculos veiculo = null;
            if (consulta.Proximo()) {
                veiculo = new Veiculos();
                veiculo.setNumero(consulta.getInt("Numero"));
                veiculo.setPlaca(consulta.getString("Placa"));
                veiculo.setUF_Placa(consulta.getString("UF_Placa"));
                veiculo.setMun_Placa(consulta.getString("Mun_Placa"));
                veiculo.setModelo(consulta.getInt("Modelo"));
                veiculo.setCategoria(consulta.getString("Categoria"));
                veiculo.setCodFil(consulta.getString("CodFil"));
                veiculo.setAno(consulta.getInt("Ano"));
                veiculo.setAnoModelo(consulta.getInt("AnoModelo"));
                veiculo.setChassis(consulta.getString("Chassis"));
                veiculo.setRENAVAN(consulta.getString("RENAVAN"));
                veiculo.setTipo(consulta.getString("Tipo"));
                veiculo.setCombust(consulta.getString("Combust"));
                veiculo.setPassageiros(consulta.getInt("Passageiros"));
                veiculo.setPotencia(consulta.getInt("Potencia"));
                veiculo.setVeicTerceiros(consulta.getInt("VeicTerceiros"));
                veiculo.setBlindCab(consulta.getString("BlindCab"));
                veiculo.setBlindTeto(consulta.getString("BlindTeto"));
                veiculo.setBlindAssoa(consulta.getString("BlindAssoa"));
                veiculo.setBlindCofre(consulta.getString("BlindCofre"));
                veiculo.setBlindVidro(consulta.getString("BlindVidro"));
                veiculo.setViagem(consulta.getString("Viagem"));
                veiculo.setBacen(consulta.getString("Bacen"));
                veiculo.setAeroporto(consulta.getString("Aeroporto"));
                veiculo.setVistoriaPF(consulta.getString("VistoriaPF"));
                veiculo.setDt_VisPF(consulta.getString("Dt_VisPF"));
                veiculo.setDt_VenPF(consulta.getString("Dt_VenPF"));
                veiculo.setPrefixo(consulta.getString("Prefixo"));
                veiculo.setCarroceria(consulta.getString("Carroceria"));
                veiculo.setApolice(consulta.getString("Apolice"));
                veiculo.setDt_SegIni(consulta.getString("Dt_SegIni"));
                veiculo.setDt_VencSeg(consulta.getString("Dt_VencSeg"));
                veiculo.setSeguradora(consulta.getString("Seguradora"));
                veiculo.setDt_Ipva(consulta.getString("Dt_Ipva"));
                veiculo.setAnoLicen(consulta.getInt("AnoLicen"));
                veiculo.setVistoriaQualid(consulta.getString("VistoriaQualid"));
                veiculo.setDt_VisQualid(consulta.getString("Dt_VisQualid"));
                veiculo.setDt_VenQualid(consulta.getString("Dt_VenQualid"));
                veiculo.setVistoriaConfor(consulta.getString("VistoriaConfor"));
                veiculo.setDt_VisConfor(consulta.getString("Dt_VisConfor"));
                veiculo.setDt_VenConfor(consulta.getString("Dt_VenConfor"));
                veiculo.setVistoriaFabric(consulta.getString("VistoriaFabric"));
                veiculo.setDt_VisFabric(consulta.getString("Dt_VisFabric"));
                veiculo.setDt_VenFabric(consulta.getString("Dt_VenFabric"));
                veiculo.setDt_Compra(consulta.getString("Dt_Compra"));
                veiculo.setObs(consulta.getString("Obs"));
                veiculo.setCodCidade(consulta.getString("CodCidade"));
                veiculo.setMatr_Mot(consulta.getString("Matr_Mot"));
                veiculo.setSituacao(consulta.getString("Situacao"));
                veiculo.setDt_Situac(consulta.getString("Dt_Situac"));
                veiculo.setID_Modulo(consulta.getString("ID_Modulo"));
                veiculo.setCCusto(consulta.getString("CCusto"));
                veiculo.setVistoriaQualidII(consulta.getString("VistoriaQualidII"));
                veiculo.setDt_VisQualidII(consulta.getString("Dt_VisQualidII"));
                veiculo.setDt_VenQualidII(consulta.getString("Dt_VenQualidII"));
                veiculo.setVistoriaConforII(consulta.getString("VistoriaConforII"));
                veiculo.setDt_VisConforII(consulta.getString("Dt_VisConforII"));
                veiculo.setDt_VenConforII(consulta.getString("Dt_VenConforII"));
                veiculo.setOperador(consulta.getString("Operador"));
                veiculo.setDt_Alter(consulta.getString("Dt_Alter"));
                veiculo.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.Close();
            return veiculo;
        } catch (Exception e) {
            throw new Exception("VeiculosDao.buscarVeiculo - " + e.getMessage() + "\r\n"
                    + "SELECT * "
                    + " FROM veiculos "
                    + " WHERE veiculos.numero = " + numero);
        }

    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Dados;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
public class Conecta {

    private Connection conexao = null;
    private String param = null;
    private String url, usuario, senha;

    /**
     * Abre a conexao do banco com os dados passados no construtor da classe
     * Pode-se passar os dados em branco "" para informar que deseja-se garantir
     * a conexão com os dados lidos anteriormente
     *
     * @param parametro
     * @throws java.lang.Exception
     */
    public void Abreconexao(String parametro) throws Exception {
        this.param = parametro;
//        gerarArquivoAberturaConexoes();
        this.validaConexao();
        try {
            if (conexao == null) {
                conexao = this.conecta(parametro);
            }
        } catch (Exception ex) {
            throw new Exception("Erro ao conectar - " + ex.getMessage());
        }
    }

    /**
     * Abre a conexao do banco com os dados passados no construtor da classe
     * Pode-se passar os dados em branco "" para informar que deseja-se garantir
     * a conexão com os dados lidos anteriormente
     *
     * @param url
     * @param usuario
     * @param senha
     * @throws java.lang.Exception
     */
    public void Abreconexao(String url, String usuario, String senha) throws Exception {
        this.validaConexao();

        try {
            if (conexao == null) {
                this.url = url;
                this.usuario = usuario;
                this.senha = senha;
                conexao = this.conecta(url, usuario, senha);
            }
        } catch (Exception ex) {
            throw new Exception("Erro ao conectar - " + ex.getMessage());
        }
    }

    /**
     * Fecha a conexao do objeto Sempre tratar com try catch(Exception)
     *
     * @throws java.lang.Exception
     */
    public void Fechaconexao() throws Exception {
        try {
            conexao.close();
//            gerarArquivoFechamentoConexoes();
        } catch (Exception e) {
            throw new Exception("Não foi possível fechar a conexao - " + e.getMessage());
        }
    }

    /**
     * Retorna um PreparedStatement do sql passado da conexao atual
     *
     * @param sql que sera usado Sempre tratar com try catch(Exception)
     * @return
     * @throws java.lang.Exception
     */
    public PreparedStatement getState(String sql) throws Exception {
        try {
            return conexao.prepareStatement(sql);
        } catch (Exception x) {
            try {
                conexao = this.conecta(param);
                return conexao.prepareStatement(sql);
            } catch (Exception ex) {
                try {
                    conexao = this.conecta(url, usuario, senha);
                    return conexao.prepareStatement(sql);
                } catch (Exception exx) {
                    throw new Exception("Erro criando PrepareStatement- " + ex.getMessage());
                }
            }
        }
    }

    /**
     * Verifica se a conexão está aberta e válida
     */
    private void validaConexao() {
        if (conexao != null) {
            boolean valido;
            try {
                valido = conexao.isValid(3);
            } catch (Exception e) {
                valido = false;
            }
            if (!valido) {
                conexao = null;
            }
        }
    }

    /**
     * Efetua a conexão
     *
     * @param Parametro - parametro de conexão (nome da empresa)
     * @return
     * @throws Exception
     */
    private Connection conecta(String parametro) throws Exception {
        Context ctx = new InitialContext();
        DataSource ds = (DataSource) ctx.lookup(parametro);
        return ds.getConnection();
    }

    /**
     * Efetua a conexão
     *
     * @param url - jdbc:sqlserver
     * @param usuario - usuario do BD
     * @param senha - senha de acesso ao BD
     * @return
     * @throws Exception
     */
    private Connection conecta(String url, String usuario, String senha) throws Exception {
        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        DriverManager.setLoginTimeout(5);
        return DriverManager.getConnection(url, usuario, senha);
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.XMLGtve;

/**
 *
 * <AUTHOR>
 */
public class XMLGTVEDao {

    /**
     * Grava dados da GTVe gerada
     *
     * @param xmlGTVE
     * @param persistencia
     * @return
     * @throws Exception
     */
    public void inserirXMLGtve(XMLGtve xmlGTVE, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
           int vQtdeReg = 0;
            // Valida para verificar se ja existe e nao permite Duplicar
           /*
            sql = "Select Count(*) Qtde from XMLGTVE \n"
                    + "where ChaveGTVE = '" + xmlGTVE.getChaveGTVE() + "'\n"
                    + " and Dt_envio >= GetDate()-10";
            Consulta qTmpX = new Consulta(sql, persistencia);
            qTmpX.select();
            int vQtdeReg = 0;
            if (qTmpX.Proximo()) {
                vQtdeReg = qTmpX.getInt("Qtde");                            
            }
            qTmpX.Close();
           */
            if (vQtdeReg == 0) {
                sql = "DECLARE @sequencia int;\n"
                        + " \n"
                        + " SET @sequencia = (SELECT ISNULL(MAX(CONVERT(bigint,sequencia)),0) + 1 FROM XMLGTVE); \n"
                        + " \n"
                        + " delete from XMLGTve where Dt_Envio >= GetDate()-2 and (Status = 'ERRO' or (Status is null and ChaveGTVE = '')); "
                        + " \n"
                        + " INSERT INTO XMLGTVE (Sequencia,\n"
                        + "                      CNPJ,\n"
                        + "                      Guia,\n"
                        + "                      Serie,\n"
                        + "                      CodCidade,\n"
                        + "                      Dt_GTVE,\n"
                        + "                      Hr_GTVE,\n"
                        + "                      XML_Envio,\n"
                        + "                      XML_Retorno,\n"
                        + "                      Protocolo,\n"
                        + "                      ChaveGTVE,\n"
                        + "                      Dt_Envio,\n"
                        + "                      Hr_Envio,\n"
                        + "                      Dt_Retorno,\n"
                        + "                      Hr_Retorno,\n"
                        + "                      Link)\n"
                        + " VALUES (@sequencia, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

                Consulta consulta = new Consulta(sql, persistencia);

                // Parametros de INSERT
                consulta.setString(xmlGTVE.getCNPJ());
                consulta.setString(xmlGTVE.getGuia());
                //consulta.setString(xmlGTVE.getSerie());
                consulta.setString(xmlGTVE.getOpenIdGtv());
                consulta.setString(xmlGTVE.getCodCidade());
                consulta.setString(xmlGTVE.getDt_GTVE());
                consulta.setString(xmlGTVE.getHr_GTVE());
                consulta.setString(xmlGTVE.getXML_Envio());
                consulta.setString(xmlGTVE.getXML_Retorno());
                consulta.setString(xmlGTVE.getProtocolo());
                consulta.setString(xmlGTVE.getChaveGTVE());
                consulta.setString(xmlGTVE.getDt_Envio());
                consulta.setString(xmlGTVE.getHr_Envio());
                consulta.setString(xmlGTVE.getDt_Retorno());
                consulta.setString(xmlGTVE.getHr_Retorno());
                consulta.setString(xmlGTVE.getLink());

                consulta.insert();
                consulta.close();
            } else {
                sql = "Update XMLGTVE Set Dt_envioCliente = null, Hr_envioCliente = null, RetornoCliente = null "
                    + "where ChaveGTVE = '" + xmlGTVE.getChaveGTVE() + "'\n"
                    + " and Dt_envio >= GetDate()-10";
               Consulta SQLPdr = new Consulta(sql, persistencia);
               SQLPdr.update();
               SQLPdr.Close();
            }
        } catch (Exception e) {
            throw new Exception("XMLGTVEDao.inserirXMLGtve - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public void inserirXMLGtveProsegur(XMLGtve xmlGTVE, Persistencia persistencia) throws Exception {
        String sql = "";
        XMLGtve retornoObj = buscarXMLGtve(xmlGTVE.getGuia(), xmlGTVE.getSerie(), persistencia);

        try {
            // Valida para verificar se ja existe e nao permite Duplicar
            sql = "Select Count(*) Qtde from XMLGTVE \n"
                    + "where ChaveGTVE = '" + xmlGTVE.getChaveGTVE() + "'\n"
                    + " and Dt_envio >= GetDate()-10";
            Consulta qTmpX = new Consulta(sql, persistencia);
            qTmpX.select();
            int vQtdeReg = 0;
            vQtdeReg = qTmpX.getInt("Qtde");
            qTmpX.Close();
            if (vQtdeReg == 0) {
            
            sql = "DECLARE @sequencia int;\n"
                    + " \n"
                    + " SET @sequencia = (SELECT ISNULL(MAX(CONVERT(bigint,sequencia)),0) + 1 FROM XMLGTVE); \n"
                    + " \n"
                    + " INSERT INTO XMLGTVE (Sequencia,\n"
                    + "                      CNPJ,\n"
                    + "                      Guia,\n"
                    + "                      Serie,\n"
                    + "                      CodCidade,\n"
                    + "                      Dt_GTVE,\n"
                    + "                      Hr_GTVE,\n"
                    + "                      XML_Envio,\n"
                    + "                      XML_Retorno,\n"
                    + "                      Protocolo,\n"
                    + "                      ChaveGTVE,\n"
                    + "                      Dt_Envio,\n"
                    + "                      Hr_Envio,\n"
                    + "                      Dt_Retorno,\n"
                    + "                      Hr_Retorno,\n"
                    + "                      Link,\n"
                    + "                      Status,\n"
                    + "                      FatIdGtv,\n"
                    + "                      OpenIdGtv,\n"
                    + "                      OpenIdFilial)\n"
                    + " VALUES (@sequencia,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

            Consulta consulta = new Consulta(sql, persistencia);

            // Parametros de INSERT
            consulta.setString(xmlGTVE.getCNPJ());
            consulta.setString(xmlGTVE.getGuia());
            consulta.setString(xmlGTVE.getSerie());
            consulta.setString(xmlGTVE.getCodCidade());
            consulta.setString(xmlGTVE.getDt_GTVE());
            consulta.setString(xmlGTVE.getHr_GTVE());
            consulta.setString(xmlGTVE.getXML_Envio());
            consulta.setString(xmlGTVE.getXML_Retorno());
            consulta.setString(xmlGTVE.getProtocolo());
            consulta.setString(xmlGTVE.getChaveGTVE());
            consulta.setString(xmlGTVE.getDt_Envio());
            consulta.setString(xmlGTVE.getHr_Envio());
            consulta.setString(xmlGTVE.getDt_Retorno());
            consulta.setString(xmlGTVE.getHr_Retorno());
            consulta.setString(xmlGTVE.getLink());

            consulta.setString(xmlGTVE.getStatus());
            consulta.setInt(xmlGTVE.getFatIdGtv());
            consulta.setString(xmlGTVE.getOpenIdGtv());
            consulta.setInt(xmlGTVE.getOpenIdFilial());

            consulta.insert();
            consulta.close();
            } else {
                sql = "Update XMLGTVE Set Dt_envioCliente = null, Hr_envioCliente = null, RetornoCliente = null "
                    + "where ChaveGTVE = '" + xmlGTVE.getChaveGTVE() + "'\n"
                    + " and Dt_envio >= GetDate()-10";
               Consulta SQLPdr = new Consulta(sql, persistencia);
               SQLPdr.update();
               SQLPdr.Close();
            }            
        } catch (Exception e) {
            throw new Exception("XMLGTVEDao.inserirXMLGtveProsegur - " + e.getMessage() + "\r\n"
                    + sql);
        }

    }

    /**
     * Busca notas pelo token (cnpj + sequencia)
     *
     * @param chaveNfe
     * @param dtRetorno
     * @param persistencia
     * @return
     * @throws Exception
     */
    public XMLGtve buscarXMLGtve(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            XMLGtve xmlGtve = null;
            sql = " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLGTVE \n"
                    + " WHERE \n"
                    + "     Guia = ? \n"
                    + "    AND Serie = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            if (consulta.Proximo()) {
                xmlGtve = new XMLGtve(consulta.getString("CNPJ"),
                        consulta.getString("Guia"),
                        consulta.getString("Serie"),
                        consulta.getString("CodCidade"),
                        consulta.getString("Dt_GTVE"),
                        consulta.getString("Hr_GTVE"),
                        consulta.getString("XML_Envio"),
                        consulta.getString("XML_Retorno"),
                        consulta.getString("Protocolo"),
                        consulta.getString("ChaveGTVE"),
                        consulta.getString("Dt_Envio"),
                        consulta.getString("Hr_Envio"),
                        consulta.getString("Dt_Retorno"),
                        consulta.getString("Hr_Retorno"),
                        consulta.getString("Link"));

            }

            consulta.close();
            return xmlGtve;
        } catch (Exception e) {
            throw new Exception(sql);
        }
    }

    public XMLGtve buscarXMLGtvePorProcolo(String protocolo, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            XMLGtve xmlGtve = null;
            sql = " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLGTVE \n"
                    + " WHERE \n"
                    + "     Protocolo = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(protocolo);
            consulta.select();

            if (consulta.Proximo()) {
                xmlGtve = new XMLGtve(consulta.getString("CNPJ"),
                        consulta.getString("Guia"),
                        consulta.getString("Serie"),
                        consulta.getString("CodCidade"),
                        consulta.getString("Dt_GTVE"),
                        consulta.getString("Hr_GTVE"),
                        consulta.getString("XML_Envio"),
                        consulta.getString("XML_Retorno"),
                        consulta.getString("Protocolo"),
                        consulta.getString("ChaveGTVE"),
                        consulta.getString("Dt_Envio"),
                        consulta.getString("Hr_Envio"),
                        consulta.getString("Dt_Retorno"),
                        consulta.getString("Hr_Retorno"),
                        consulta.getString("Link"));

                try {
                    xmlGtve.setFatIdGtv(consulta.getInt("FatIdGtv"));
                    xmlGtve.setOpenIdFilial(consulta.getInt("OpenIdFilial"));
                    xmlGtve.setOpenIdGtv(consulta.getString("OpenIdGtv"));
                    xmlGtve.setStatus(consulta.getString("Status"));
                } catch (Exception ex) {
                }
            }

            consulta.close();
            return xmlGtve;
        } catch (Exception e) {
            throw new Exception(sql);
        }
    }

    public XMLGtve buscarXMLGtvePorNCT(String nct, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            XMLGtve xmlGtve = null;
            sql = "SELECT * from XMLGTVE where Dt_envio = GetDate() and xml_envio like '%<nCT>" + nct + "</nCT>%' AND Protocolo IS NOT NULL AND Protocolo <> '' order by dt_gtve desc, hr_gtve desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            if (consulta.Proximo()) {
                xmlGtve = new XMLGtve(consulta.getString("CNPJ"),
                        consulta.getString("Guia"),
                        consulta.getString("Serie"),
                        consulta.getString("CodCidade"),
                        consulta.getString("Dt_GTVE"),
                        consulta.getString("Hr_GTVE"),
                        consulta.getString("XML_Envio"),
                        consulta.getString("XML_Retorno"),
                        consulta.getString("Protocolo"),
                        consulta.getString("ChaveGTVE"),
                        consulta.getString("Dt_Envio"),
                        consulta.getString("Hr_Envio"),
                        consulta.getString("Dt_Retorno"),
                        consulta.getString("Hr_Retorno"),
                        consulta.getString("Link"));

                try {
                    xmlGtve.setFatIdGtv(consulta.getInt("FatIdGtv"));
                    xmlGtve.setOpenIdFilial(consulta.getInt("OpenIdFilial"));
                    xmlGtve.setOpenIdGtv(consulta.getString("OpenIdGtv"));
                    xmlGtve.setStatus(consulta.getString("Status"));
                } catch (Exception ex) {
                }
            }

            consulta.close();
            return xmlGtve;
        } catch (Exception e) {
            throw new Exception(sql);
        }
    }

}

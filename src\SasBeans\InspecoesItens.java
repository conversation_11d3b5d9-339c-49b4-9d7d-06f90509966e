/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import br.com.sasw.pacotesuteis.sasbeans.InspecoesItensLista;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class InspecoesItens {

    private String Codigo;
    private String Sequencia;
    private String Pergunta;
    private String TipoResp;
    private String Obrigatorio;
    private String Foto;
    private String Video;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    
    private String OpcoesResposta;
    private String OpcoesRespostaHTML;

    public InspecoesItens() {
        this.itensLista = new ArrayList<>();
    }
    
    private List<InspecoesItensLista> itensLista;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getPergunta() {
        return Pergunta;
    }

    public void setPergunta(String Pergunta) {
        this.Pergunta = Pergunta;
    }

    public String getTipoResp() {
        return TipoResp;
    }

    public void setTipoResp(String TipoResp) {
        this.TipoResp = TipoResp;
    }

    public String getFoto() {
        return Foto;
    }

    public void setFoto(String Foto) {
        this.Foto = Foto;
    }

    public boolean isFotoChecked() {
        return this.Foto.equals("1");
    }

    public void setFotoChecked(boolean checked) {
        this.Foto = (checked ? "1" : "0");
    }

    public String getVideo() {
        return Video;
    }

    public void setVideo(String Video) {
        this.Video = Video;
    }

    public boolean isVideoChecked() {
        return this.Video.equals("1");
    }

    public void setVideoChecked(boolean checked) {
        this.Video = (checked ? "1" : "0");
    }

    public String getObrigatorio() {
        return Obrigatorio;
    }

    public void setObrigatorio(String Obrigatorio) {
        this.Obrigatorio = Obrigatorio;
    }

    public boolean isObrigatorioChecked() {
        return this.Obrigatorio.equals("1");
    }

    public void setObrigatorioChecked(boolean checked) {
        this.Obrigatorio = (checked ? "1" : "0");
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public List<InspecoesItensLista> getItensLista() {
        return itensLista;
    }

    public void setItensLista(List<InspecoesItensLista> itensLista) {
        this.itensLista = itensLista;
    }

    public String getOpcoesResposta() {
        return OpcoesResposta;
    }

    public void setOpcoesResposta(String OpcoesResposta) {
        this.OpcoesResposta = OpcoesResposta;
    }

    public String getOpcoesRespostaHTML() {
        return OpcoesRespostaHTML;
    }

    public void setOpcoesRespostaHTML(String OpcoesRespostaHTML) {
        this.OpcoesRespostaHTML = OpcoesRespostaHTML;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 37 * hash + Objects.hashCode(this.Codigo);
        hash = 37 * hash + Objects.hashCode(this.Sequencia);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final InspecoesItens other = (InspecoesItens) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        return true;
    }
}

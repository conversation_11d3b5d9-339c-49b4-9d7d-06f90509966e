/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.Guias.GuiasSatWeb;
import Controller.Pedidos.PedidosSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.CxForte;
import SasBeans.Filiais;
import SasBeans.GTVSeq;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.PedidoDN;
import SasBeans.PreOrder;
import SasBeans.PreOrderVol;
import SasBeans.SasPWFill;
import SasBeans.TesMoedas;
import SasBeansCompostas.BBPedidoAgencia;
import SasBeansCompostas.BBPedidoMalote;
import SasBeansCompostas.GTVPedidoOSClienteTesSaida;
import SasBeansCompostas.ImportacaoPedido;
import SasBeansCompostas.TesFechaGeracaoGTV;
import SasDaos.ClientesDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import br.com.sasw.lazydatamodels.operacoes.PedidosLazyList;
import br.com.sasw.pacotesuteis.sasbeans.compostas.Rt_PercRotasClientes;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import static br.com.sasw.utils.Mascaras.removeMascaraData;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.inject.Named;
import javax.servlet.http.HttpSession;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "pedido")
@javax.enterprise.context.SessionScoped
public class PedidosMB implements Serializable {

    private Persistencia persistencia;
    private String codfil, log, caminho, nomeFilial, banco, operador, numero, nomecli, codcli, mensagemImportacao;
    private final Calendar calendar;
    private Date dataSelecionada1;
    private BigDecimal codPessoa;
    private ArquivoLog logerro;
    private SasPWFill filial;
    private Filiais filiais;
    private Pedido pedidoSelecionado, pedidoPesquisa;
    private List<CxForte> cxForteLista;
    private Map filters;
    private final PedidosSatMobWeb pedidosSatMobWeb;
    private LazyDataModel<Pedido> pedidos = null;
    private List<Pedido> listaPedidos = null;
    private boolean mostrarFiliais, limparFiltros, limpar, editarComposicao, exibirExcluidos, pedirComposicoes;
    private OS_Vig osSelecionada;
    private Clientes clienteOrigem, clienteDestino;
    private List<Clientes> clienteOrigemList, clienteDestinoList;
    private Rt_PercRotasClientes clienteCancelamento;
    private List<Rt_PercRotasClientes> clienteCancelamentoList;
    private int flag, volumesPreOrder;
    private PedidoDN pedidoCedulaMoeda, pedidoCedulaMoedaNovo;
    private final GuiasSatWeb guiasweb;
    private List<PedidoDN> listaPedidoCedulaMoeda;
    private BigDecimal valorTotalListaGuias;
    private GTVPedidoOSClienteTesSaida guiaSelecionada, guiaEdicao;
    private GTVSeq gtvSeqSelecionado;
    private int numGuiaSelecionado;

    private List<PedidoDN> listaPedidoCedula;
    private List<PedidoDN> listaPedidoMoeda;
    private List<GTVPedidoOSClienteTesSaida> listaGuias;
    private List<GTVSeq> listaGTVSeq;
    private List<TesFechaGeracaoGTV> listaTesFecha, selectedTesFecha;

    private List<File> arquivosPedidos;
    private List<ImportacaoPedido> pedidosImportados;
    private List<BBPedidoAgencia> listaAgencias;
    private List<Integer> lotes;
    private List<TesMoedas> tesMoedasCD, tesMoedasMD, tesMoedasSelecao;
    private final RotasSatWeb rotassatweb;

    private List<PreOrder> pedidosRecentes, preOrderDetalhado;

    private Boolean acessoAdm;

    public PedidosMB() {
        log = new String();
        logerro = new ArquivoLog();
        pedidosSatMobWeb = new PedidosSatMobWeb();
        calendar = Calendar.getInstance();
//        calendar.setTime(Date.from(Instant.now()));
//        calendar.set(Calendar.DAY_OF_MONTH, 1);
        dataSelecionada1 = calendar.getTime();
//        d1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
//        dataSelecionada2 = calendar.getTime();
//        d2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        mostrarFiliais = false;
        limparFiltros = false;
        listaPedidoCedulaMoeda = new ArrayList<>();
        listaPedidoCedula = new ArrayList<>();
        listaPedidoMoeda = new ArrayList<>();
        listaGuias = new ArrayList<>();
        arquivosPedidos = new ArrayList<>();
        pedidosImportados = new ArrayList<>();
        tesMoedasCD = new ArrayList<>();
        tesMoedasMD = new ArrayList<>();
        tesMoedasSelecao = new ArrayList<>();
        listaPedidos = new ArrayList<>();
        guiasweb = new GuiasSatWeb();
        rotassatweb = new RotasSatWeb();
        exibirExcluidos = false;
        mostrarFiliais = false;
        acessoAdm = false;
    }

    public void persistencia(Persistencia pp) {
        try {
            FacesContext fc = FacesContext.getCurrentInstance();
            this.codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
            this.nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
            this.banco = (String) fc.getExternalContext().getSessionMap().get("banco");
            this.operador = (String) fc.getExternalContext().getSessionMap().get("nome");
            this.codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
            this.codcli = (String) fc.getExternalContext().getSessionMap().get("cliente");
            this.nomecli = (String) fc.getExternalContext().getSessionMap().get("nomeCliente");

            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                    + this.banco + "\\" + getDataAtual("SQL") + "\\" + this.codPessoa.toBigInteger() + ".txt";

            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.cxForteLista = this.pedidosSatMobWeb.listarCaixasForte(this.codfil, this.persistencia);
            this.pedidoPesquisa = new Pedido();

            String p = (String) fc.getExternalContext().getSessionMap().get("pedido");
            if (p == null) {
                this.pedidoSelecionado = null;
            }
            iniciarPagina();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void iniciarPagina() {
        try {
            this.filters = new HashMap();
            this.filters.put(" pedido.codfil = ? ", this.codfil);
            //this.filters.put(" pedido.Flag_Excl <> ? ", "*");
            this.filters.put(" pedido.data = ? ", this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault())
                    .toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            this.filters.put("CodCli", "");
            this.filters.put("CodPessoa", this.codPessoa.toPlainString().replace(".0", ""));
            this.filters.put(" pedido.Flag_Excl <> ?", "*");
            this.filiais = this.pedidosSatMobWeb.buscaInfoFilial(this.codfil, this.persistencia);

            this.tesMoedasCD = this.pedidosSatMobWeb.buscaCedulasMoedas("C", this.persistencia);
            this.tesMoedasMD = this.pedidosSatMobWeb.buscaCedulasMoedas("M", this.persistencia);

            if (this.pedidoSelecionado != null && this.pedidoSelecionado.getCodFil() != null) {
                String os = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("os");
                if (os != null) {
                    this.pedidoSelecionado.setOS(os);
                }
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().replace("pedido", null);
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().replace("os", null);
                preEdicao(null);
                this.flag = 1;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void buttonActionComposicaoNovo(ActionEvent actionEvent) {
        this.editarComposicao = false;
        pedidoCedulaMoedaNovo = new PedidoDN();
        pedidoCedulaMoeda = new PedidoDN();
        pedidoCedulaMoedaNovo.setTipo("C");
        this.tesMoedasSelecao = this.tesMoedasCD;

        PrimeFaces.current().resetInputs("formCadastroComposicao:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').show();");
    }

    public void buttonActionComposicaoCedulas(ActionEvent actionEvent) {
        this.editarComposicao = false;
        pedidoCedulaMoedaNovo = new PedidoDN();
        pedidoCedulaMoeda = new PedidoDN();
        pedidoCedulaMoedaNovo.setTipo("C");
        this.tesMoedasSelecao = this.tesMoedasCD;

        PrimeFaces.current().resetInputs("formCadastroComposicao:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').show();");
    }

    public void buttonActionComposicaoMoedas(ActionEvent actionEvent) {
        this.editarComposicao = false;
        pedidoCedulaMoedaNovo = new PedidoDN();
        pedidoCedulaMoeda = new PedidoDN();
        pedidoCedulaMoedaNovo.setTipo("M");
        this.tesMoedasSelecao = this.tesMoedasMD;

        PrimeFaces.current().resetInputs("formCadastroComposicao:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').show();");
    }

    public void salvarDadosComposicao() throws Exception {
        if ((null == this.pedidoCedulaMoedaNovo.getQtde() || this.pedidoCedulaMoedaNovo.getQtde().equals(""))
                && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().equals(""))) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("Obrigatorio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            if (!this.editarComposicao) {
                // NOVO 
                boolean ExisteCedulaMoeda = false;
                if (this.pedidoCedulaMoedaNovo.getTipo().equals("C")) {
                    // ADICIONAR VALOR A CEDULA JA EXISTENTE
                    for (int I = 0; I < listaPedidoCedula.size(); I++) {
                        if (listaPedidoCedula.get(I).getCodigo().trim().replace(".0", "").equals(this.pedidoCedulaMoedaNovo.getCodigo().trim().replace(".0", ""))) {
                            ExisteCedulaMoeda = true;

                            int TotalQtde = Integer.parseInt(this.listaPedidoCedula.get(I).getQtde().replace(".0", "")) + Integer.parseInt(this.pedidoCedulaMoedaNovo.getQtde().replace(".0", ""));
                            this.listaPedidoCedula.get(I).setQtde(Integer.toString(TotalQtde));

                            if (!this.pedidoCedulaMoedaNovo.getQtde().replace(".0", "").equals("")
                                    && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().replace(".0", "").equals(""))) {
                                this.pedidoCedulaMoedaNovo.setValor(Double.toString(new Float(this.pedidoCedulaMoedaNovo.getQtde().replace(".0", "")) * new Float(this.pedidoCedulaMoedaNovo.getCodigo().replace(".00", "").replace(".0", ""))));
                            }

                            double total = 0;
                            total = new Float(this.listaPedidoCedula.get(I).getValor().replace(".0", ""));
                            total += new Float(this.pedidoCedulaMoedaNovo.getValor().replace(".0", ""));

                            this.listaPedidoCedula.get(I).setValor(Double.toString(total));
                            break;
                        }
                    }
                } else {
                    // ADICIONAR VALOR A MOEDA JA EXISTENTE
                    DecimalFormat df = new DecimalFormat("0.00");

                    for (int I = 0; I < listaPedidoMoeda.size(); I++) {
                        if (listaPedidoMoeda.get(I).getCodigo().trim().replace(".0", "").equals(this.pedidoCedulaMoedaNovo.getCodigo().trim().replace(".0", ""))) {
                            ExisteCedulaMoeda = true;

                            int TotalQtde = Integer.parseInt(this.listaPedidoMoeda.get(I).getQtde().replace(".0", "")) + Integer.parseInt(this.pedidoCedulaMoedaNovo.getQtde().replace(".0", ""));
                            this.listaPedidoMoeda.get(I).setQtde(Integer.toString(TotalQtde));

                            if (!this.pedidoCedulaMoedaNovo.getQtde().replace(".0", "").equals("")
                                    && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().replace(".0", "").equals(""))) {
                                this.pedidoCedulaMoedaNovo.setValor(Double.toString(new Double(this.pedidoCedulaMoedaNovo.getQtde()) * new Double(this.pedidoCedulaMoedaNovo.getCodigo())));
                            }

                            double total = 0;
                            total = new Double(this.listaPedidoMoeda.get(I).getValor());
                            total += new Double(this.pedidoCedulaMoedaNovo.getValor());

                            this.listaPedidoMoeda.get(I).setValor(Double.toString(total));
                            break;
                        }
                    }
                }

                if (!ExisteCedulaMoeda) {
                    TratarDadosComposicao();

                    this.listaPedidoCedulaMoeda.add(this.pedidoCedulaMoedaNovo);

                    if (this.pedidoCedulaMoedaNovo.getTipo().equals("C")) {
                        this.listaPedidoCedula.add(this.pedidoCedulaMoedaNovo);
                    } else {
                        this.listaPedidoMoeda.add(this.pedidoCedulaMoedaNovo);
                    }
                } else {
                    this.listaPedidoCedulaMoeda = new ArrayList<>();

                    for (PedidoDN listaPedidoCedula1 : this.listaPedidoCedula) {
                        this.listaPedidoCedulaMoeda.add(listaPedidoCedula1);
                    }

                    for (PedidoDN listaPedidoMoeda1 : this.listaPedidoMoeda) {
                        this.listaPedidoCedulaMoeda.add(listaPedidoMoeda1);
                    }
                }
            } else {
                // EDICAO
                for (int I = 0; I < listaPedidoCedulaMoeda.size(); I++) {
                    if (listaPedidoCedulaMoeda.get(I).getNumero().equals(this.pedidoCedulaMoeda.getNumero())) {
                        this.listaPedidoCedulaMoeda.get(I).setTipoDesc(this.pedidoCedulaMoedaNovo.getTipo().equals("C") ? getMessageS("Cedula") : getMessageS("Moeda"));
                        this.listaPedidoCedulaMoeda.get(I).setCodigo(this.pedidoCedulaMoedaNovo.getCodigo());
                        this.listaPedidoCedulaMoeda.get(I).setQtde(this.pedidoCedulaMoedaNovo.getQtde());
                        this.listaPedidoCedulaMoeda.get(I).setTipo(this.pedidoCedulaMoedaNovo.getTipo());

                        if (!this.pedidoCedulaMoedaNovo.getQtde().equals("")
                                && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().equals(""))) {
                            this.pedidoCedulaMoedaNovo.setValor(Double.toString(new Double(this.pedidoCedulaMoedaNovo.getQtde()) * new Double(this.pedidoCedulaMoedaNovo.getCodigo())));
                        }

                        this.listaPedidoCedulaMoeda.get(I).setValor(this.pedidoCedulaMoedaNovo.getValor());
                        break;
                    }
                }

                TratarDadosComposicao();
            }

            pedidoCedulaMoedaNovo = new PedidoDN();
            pedidoCedulaMoeda = new PedidoDN();
            pedidoCedulaMoedaNovo.setTipo("C");
            PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').hide();");
            PrimeFaces.current().ajax().update("formCadastrar:panelComposicoes");
        }
    }

    private void TratarDadosComposicao() {
        for (int I = 0; I < listaPedidoCedulaMoeda.size(); I++) {
            this.listaPedidoCedulaMoeda.get(I).setNumero(Integer.toString(I));
        }

        this.pedidoCedulaMoedaNovo.setNumero(Integer.toString(this.listaPedidoCedulaMoeda.size() + 1));
        this.pedidoCedulaMoedaNovo.setTipoDesc(this.pedidoCedulaMoedaNovo.getTipo().equals("C") ? getMessageS("Cedula") : getMessageS("Moeda"));
        if (null != this.pedidoCedulaMoedaNovo.getQtde().replace(".0", "") && !this.pedidoCedulaMoedaNovo.getQtde().replace(".0", "").equals("")
                && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().replace(".0", "").equals(""))) {
            this.pedidoCedulaMoedaNovo.setValor(Double.toString(new Double(this.pedidoCedulaMoedaNovo.getQtde().replace(".0", "")) * new Double(this.pedidoCedulaMoedaNovo.getCodigo())));
        }
    }

    public void onRowComposicaoSelect(SelectEvent event) {
        this.pedidoCedulaMoedaNovo = (PedidoDN) event.getObject();
    }

    public void dblSelectComposicao(SelectEvent event) {
        this.pedidoCedulaMoedaNovo = (PedidoDN) event.getObject();
        buttonActionComposicao(null);
    }

    public void excluirItemComposicao() {
        if (null == this.pedidoCedulaMoedaNovo) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            for (int I = 0; I < listaPedidoCedulaMoeda.size(); I++) {
                if (listaPedidoCedulaMoeda.get(I).getNumero().equals(this.pedidoCedulaMoedaNovo.getNumero())
                        && listaPedidoCedulaMoeda.get(I).getTipo().equals(this.pedidoCedulaMoedaNovo.getTipo())) {
                    if (this.pedidoCedulaMoedaNovo.getTipo().equals("C")) {
                        if ((listaPedidoCedulaMoeda.get(I).getCodigo().replace(".0", "").equals(this.pedidoCedulaMoedaNovo.getCodigo()))) {
                            listaPedidoCedulaMoeda.remove(I);
                            for (int K = 0; K < listaPedidoCedula.size(); K++) {
                                if ((listaPedidoCedula.get(K).getCodigo().replace(".0", "").equals(this.pedidoCedulaMoedaNovo.getCodigo()))) {
                                    listaPedidoCedula.remove(K);
                                }
                            }
                        }
                    } else {
                        if (listaPedidoCedulaMoeda.get(I).getCodigo().equals(this.pedidoCedulaMoedaNovo.getCodigo())) {
                            listaPedidoCedulaMoeda.remove(I);
                            for (int K = 0; K < listaPedidoMoeda.size(); K++) {
                                if (listaPedidoMoeda.get(K).getCodigo().equals(this.pedidoCedulaMoedaNovo.getCodigo())) {
                                    listaPedidoMoeda.remove(K);
                                }
                            }
                        }
                    }
                }
            }
        }
        PrimeFaces.current().ajax().update("formCadastroComposicao:tabelaCedulaMoeda");
        PrimeFaces.current().ajax().update("formCadastrar:tabelaMoeda");
    }

    public void selecionarTipoMoedaCedula() {
        if (this.pedidoCedulaMoedaNovo.getTipo().equals("C")) {
            this.tesMoedasSelecao = this.tesMoedasCD;
        } else {
            this.tesMoedasSelecao = this.tesMoedasMD;
        }
    }

    public void buttonActionComposicao(ActionEvent actionEvent) {
        if (null == this.pedidoCedulaMoeda) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.editarComposicao = true;

                if (this.pedidoCedulaMoedaNovo.getTipo().equals("C")) {
                    this.tesMoedasSelecao = this.tesMoedasCD;
                } else {
                    this.tesMoedasSelecao = this.tesMoedasMD;
                }

                if (null != this.pedidoCedulaMoedaNovo.getQtde()
                        && !this.pedidoCedulaMoedaNovo.getQtde().equals("")) {
                    this.pedidoCedulaMoedaNovo.setValor("");
                }
                PrimeFaces.current().resetInputs("formCadastroComposicao:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void selectGuia() {
        if (null == this.guiaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                guiaEdicao = guiaSelecionada; // FIXME: clonar

                PrimeFaces.current().executeScript("PF('dlgEdicaoGuia').show();");
                PrimeFaces.current().resetInputs("formEdicaoGuia:panel");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void carregarListaPedidos() {
        try {
            // Tratar nível de acesso
            String nivel = "";

            try {
                FacesContext fc = FacesContext.getCurrentInstance();
                nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
                if (nivel == null) {
                    nivel = "0";
                }
            } catch (Exception e) {
                nivel = "0";
            }

            if (!nivel.equals("5")) {
                this.acessoAdm = true;
            } else {
                this.acessoAdm = false;
            }

            // Carregar pedidos
            this.listaPedidos = new ArrayList<>();
            this.listaPedidos = this.pedidosSatMobWeb.listagemPaginada(0, 10000, this.filters, this.cxForteLista, this.acessoAdm, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public LazyDataModel<Pedido> getPedidos() {
        try {

            if (this.pedidos == null || this.pedidos.getRowCount() == 0) {
                this.filters.replace(" pedido.codfil = ? ", this.codfil);
                this.filters.replace(" pedido.Flag_Excl <> ?", "*");
                this.filters.replace(" pedido.data = ? ", this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                this.pedidos = new PedidosLazyList(this.persistencia, this.filters, this.cxForteLista);
            } else {
                ((PedidosLazyList) this.pedidos).setFilters(this.filters);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.pedidos;
    }

    public void selecionarData(SelectEvent event) {
        try {
            this.dataSelecionada1 = (Date) event.getObject();
            this.filters.replace(" pedido.data = ? ", this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            //getPedidos();
            carregarListaPedidos();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataAnterior() {
        try {
            this.calendar.setTime(this.dataSelecionada1);
            this.calendar.add(Calendar.DAY_OF_WEEK, -1);
            this.dataSelecionada1 = this.calendar.getTime();
            this.filters.replace(" pedido.data = ? ", this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            //getPedidos();
            carregarListaPedidos();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void dataPosterior() {
        try {
            this.calendar.setTime(this.dataSelecionada1);
            this.calendar.add(Calendar.DAY_OF_WEEK, 1);
            this.dataSelecionada1 = this.calendar.getTime();
            this.filters.replace(" pedido.data = ? ", this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            //getPedidos();
            carregarListaPedidos();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void pesquisaPaginada() {
        //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");

        if (null != pedidoPesquisa.getCodFilPesquisa() && !pedidoPesquisa.getCodFilPesquisa().equals("")) {
            filters.replace(" pedido.codfil = ? ", pedidoPesquisa.getCodFilPesquisa());
        } else {
            filters.replace(" pedido.codfil = ? ", "");
        }

        if (!pedidoPesquisa.getNumeroPesquisa().equals("")) {
            filters.put(" pedido.numero = ? ", pedidoPesquisa.getNumeroPesquisa());
        }

        if (!pedidoPesquisa.getNRed1().equals("")) {
            filters.put(" pedido.nred1 like ? ", "%" + pedidoPesquisa.getNRed1() + "%");
        }

        if (!pedidoPesquisa.getNRed2().equals("")) {
            filters.put(" pedido.nred2 like ? ", "%" + pedidoPesquisa.getNRed2() + "%");
        }

        /*dt.setFilters(filters);
        getPedidos();
        dt.setFirst(0);*/
        carregarListaPedidos();

        PrimeFaces.current().ajax().update("msgs", "main", "cabecalho", "corporativo");
    }

    public void prePesquisa() {
        pedidoPesquisa = new Pedido();
        numero = new String();
    }

    public void selecionarDadaPedido(SelectEvent event) throws Exception {
        Date d = new SimpleDateFormat("yyyyMMdd").parse((String) event.getObject());
        this.pedidoSelecionado.setData(d.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (d.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                .equals(Date.from(Instant.now()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")))) {
            this.pedidoSelecionado.setClassifSrv("E");
        } else {
            this.pedidoSelecionado.setClassifSrv("V");
        }
    }

    public void preCadastro() {
        try {
            this.flag = 1;
            this.pedidoSelecionado = new Pedido();
            this.pedidoSelecionado.setTipoMoeda("0");
            this.pedidoSelecionado.setCodFil(this.codfil);
            this.pedidoSelecionado.setTipo("T");
            this.pedidoSelecionado.setSituacao("PD");
            this.pedidoSelecionado.setFlag_Excl("");
            this.pedidoSelecionado.setSolicitante("");
            this.pedidoSelecionado.setPedidoCliente("");
            this.pedidoSelecionado.setHora1O("08:00");
            this.pedidoSelecionado.setHora2O("12:00");
            this.pedidoSelecionado.setHora1D("12:00");
            this.pedidoSelecionado.setHora2D("18:00");
            this.pedidoSelecionado.setData(this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            if (this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .equals(Date.from(Instant.now()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")))) {
                this.pedidoSelecionado.setClassifSrv("E");
            } else {
                this.pedidoSelecionado.setClassifSrv("V");
            }
            this.pedidoSelecionado.setObs("");

            this.listaPedidoCedulaMoeda = new ArrayList<>();

            this.filial = this.pedidosSatMobWeb.buscaFilial(this.codfil, this.persistencia);

            this.clienteCancelamento = new Rt_PercRotasClientes();
            this.clienteCancelamentoList = new ArrayList<>();
            this.clienteCancelamentoList.add(this.clienteCancelamento);

            this.clienteOrigem = new Clientes();
            this.clienteOrigemList = new ArrayList<>();
            this.clienteOrigemList.add(this.clienteOrigem);

            this.clienteDestino = new Clientes();
            this.clienteDestinoList = new ArrayList<>();
            this.clienteDestinoList.add(this.clienteDestino);

            this.listaPedidoCedulaMoeda = new ArrayList<>();
            this.listaPedidoCedula = new ArrayList<>();
            this.listaPedidoMoeda = new ArrayList<>();

            this.osSelecionada = new OS_Vig();
            PrimeFaces.current().ajax().update("formCadastrar");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void dblSelect(SelectEvent event) {
        this.pedidoSelecionado = (Pedido) event.getObject();
        preEdicao(null);
    }

    public void preExcluir() {
        if (null == pedidoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            PrimeFaces.current().executeScript("ConfirmarExclusaoPedido(" + this.pedidoSelecionado.getNumero().toPlainString() + ");");
        }
    }

    private void calcularListaGuias() throws Exception {
        listaGuias = pedidosSatMobWeb.getGuiasByPedido(codfil, pedidoSelecionado.getNumero().toString(), persistencia);
        valorTotalListaGuias = BigDecimal.ZERO;
        for (int i = 0; i < listaGuias.size(); i++) {
            BigDecimal parcela = listaGuias.get(i).getTotalGeral();
            if (parcela != null) {
                BigDecimal soma = valorTotalListaGuias.add(parcela);
                valorTotalListaGuias = soma;
            }
        }
    }

    public void preEditar(Pedido itemEdicao) {
        this.pedidoSelecionado = itemEdicao;
        preEdicao(null);
    }

    public void preExclusao(Pedido itemExclusao) {
        this.pedidoSelecionado = itemExclusao;
        preExcluir();
    }

    public void preEdicao(ActionEvent actionEvent) {
        if (null == pedidoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.flag = 2;
                if (this.pedidoSelecionado.getNumero() != null) {
                    this.listaPedidoCedulaMoeda = this.pedidosSatMobWeb.buscarComposicoes(this.pedidoSelecionado.getNumero().toString(),
                            this.pedidoSelecionado.getCodFil().toString(), this.persistencia);// this.pedidoSelecionado.getListaPedidosComposicao();
                    this.listaPedidoCedula = this.pedidosSatMobWeb.buscarComposicoesDN(this.pedidoSelecionado.getNumero().toString(),
                            this.pedidoSelecionado.getCodFil().toString(), this.persistencia);// this.pedidoSelecionado.getListaPedidosComposicao();
                    this.listaPedidoMoeda = this.pedidosSatMobWeb.buscarComposicoesMD(this.pedidoSelecionado.getNumero().toString(),
                            this.pedidoSelecionado.getCodFil().toString(), this.persistencia);// this.pedidoSelecionado.getListaPedidosComposicao();
                    this.calcularListaGuias();

                } else {
                    this.listaPedidoCedulaMoeda = new ArrayList<>();
                    this.listaPedidoCedula = new ArrayList<>();
                    this.listaPedidoMoeda = new ArrayList<>();
                }
                this.filial = this.pedidosSatMobWeb.buscaFilial(this.pedidoSelecionado.getCodFil().toString(), this.persistencia);

                this.clienteOrigem = this.pedidosSatMobWeb.buscarCliente(this.pedidoSelecionado.getCodFil().toPlainString(),
                        this.pedidoSelecionado.getCodCli1(), this.persistencia);
                this.clienteOrigemList = new ArrayList<>();
                this.clienteOrigemList.add(this.clienteOrigem);

                this.clienteDestino = this.pedidosSatMobWeb.buscarCliente(this.pedidoSelecionado.getCodFil().toPlainString(),
                        this.pedidoSelecionado.getCodCli2(), this.persistencia);
                this.clienteDestinoList = new ArrayList<>();
                this.clienteDestinoList.add(this.clienteDestino);

                if (this.pedidoSelecionado.getTipo().equals("C")) {
                    this.clienteCancelamento = new Rt_PercRotasClientes();
                    this.clienteCancelamento.setClientes(this.clienteOrigem);
                    this.clienteCancelamentoList = new ArrayList<>();
                    this.clienteCancelamentoList.add(this.clienteCancelamento);
                }

                this.osSelecionada = this.pedidosSatMobWeb.buscarOS_Vig(this.pedidoSelecionado.getCodFil().toString(),
                        this.pedidoSelecionado.getOS(), this.persistencia);
                try {
                    PrimeFaces.current().resetInputs("formCadastrar");
                } catch (Exception ee) {
                }
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, this.caminho);
            }
        }
    }

    public void selecionarFilial(SelectEvent event) {
        //this.filial = ((SasPWFill) event.getObject());

        this.pedidoSelecionado = new Pedido();
        this.pedidoSelecionado.setCodFil(this.filial.getCodfilAc());
        this.pedidoSelecionado.setSituacao("PD");
        this.pedidoSelecionado.setFlag_Excl("");
        this.pedidoSelecionado.setSolicitante("");
        this.pedidoSelecionado.setPedidoCliente("");
        this.pedidoSelecionado.setObs("");
        this.pedidoSelecionado.setHora1O("08:00");
        this.pedidoSelecionado.setHora2O("12:00");
        this.pedidoSelecionado.setHora1D("12:00");
        this.pedidoSelecionado.setHora2D("18:00");
        this.pedidoSelecionado.setData(this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                .equals(Date.from(Instant.now()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")))) {
            this.pedidoSelecionado.setClassifSrv("E");
        } else {
            this.pedidoSelecionado.setClassifSrv("V");
        }

        this.listaPedidoCedulaMoeda = new ArrayList<>();

        this.clienteCancelamento = new Rt_PercRotasClientes();
        this.clienteCancelamentoList = new ArrayList<>();
        this.clienteCancelamentoList.add(this.clienteCancelamento);

        this.clienteOrigem = new Clientes();
        this.clienteOrigemList = new ArrayList<>();
        this.clienteOrigemList.add(this.clienteOrigem);

        this.clienteDestino = new Clientes();
        this.clienteDestinoList = new ArrayList<>();
        this.clienteDestinoList.add(this.clienteDestino);

        this.osSelecionada = new OS_Vig();
    }

    public List<Rt_PercRotasClientes> listarCliCancelamento(String query) {
        try {
            this.clienteCancelamentoList = this.pedidosSatMobWeb
                    .listarRotasCancelamento(query, this.pedidoSelecionado.getCodFil().toPlainString(), this.pedidoSelecionado.getData(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.clienteCancelamentoList;
    }

    public void selecionarCliCancelamento(SelectEvent event) throws Exception {
        this.clienteCancelamento = ((Rt_PercRotasClientes) event.getObject());
        this.clienteOrigem = this.clienteCancelamento.getClientes();
        this.pedidoSelecionado.setCodCli1(this.clienteOrigem.getCodigo());
        this.pedidoSelecionado.setNRed1(this.clienteOrigem.getNRed());
        this.pedidoSelecionado.setRegiao1(this.clienteOrigem.getRegiao());
        this.pedidoSelecionado.setHora1O(this.clienteCancelamento.getRt_Perc().getHora1());
        this.pedidoSelecionado.setHora2O("");
    }

    public List<Clientes> listarCliOri(String query) {
        try {
            this.clienteOrigemList = this.pedidosSatMobWeb.buscaClientes(this.pedidoSelecionado.getCodFil().toPlainString(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.clienteOrigemList;
    }

    public void reaproveitaClienteOrigem() throws Exception {
        if (null != this.pedidoSelecionado
                && null != this.pedidoSelecionado.getCodCliReaproveitaOrigem()
                && !this.pedidoSelecionado.getCodCliReaproveitaOrigem().equals("")) {
            ClientesDao clientesDao = new ClientesDao();

            this.clienteOrigem = new Clientes();
            this.clienteOrigem = clientesDao.consultaSimplesCliente(this.pedidoSelecionado.getCodCliReaproveitaOrigem(), this.persistencia);
            this.clienteOrigemList = new ArrayList<>();
            this.clienteOrigemList.add(this.clienteOrigem);

            this.pedidoSelecionado.setCodCli1(this.clienteOrigem.getCodigo());
            this.pedidoSelecionado.setNRed1(this.clienteOrigem.getNRed());
            this.pedidoSelecionado.setRegiao1(this.clienteOrigem.getRegiao());

            consularOSexistente();
        }
    }

    public void selecionarOrigem(SelectEvent event) throws Exception {
        this.clienteOrigem = ((Clientes) event.getObject());
        this.pedidoSelecionado.setCodCli1(this.clienteOrigem.getCodigo());
        this.pedidoSelecionado.setNRed1(this.clienteOrigem.getNRed());
        this.pedidoSelecionado.setRegiao1(this.clienteOrigem.getRegiao());

        consularOSexistente();
    }

    private void consularOSexistente() {
        if (null != this.pedidoSelecionado.getCodCli1()
                && !this.pedidoSelecionado.getCodCli1().equals("")
                && null != this.pedidoSelecionado.getCodCli2()
                && !this.pedidoSelecionado.getCodCli2().equals("")) {
            try {
                String CodigoCaixaforte = "";

                for (CxForte cxForteLista1 : this.cxForteLista) {
                    CodigoCaixaforte = cxForteLista1.getCodCli();
                    break;
                }

                this.osSelecionada = this.pedidosSatMobWeb.buscarOS_Pedido(this.pedidoSelecionado.getCodCli1(),
                        this.pedidoSelecionado.getCodCli2(), this.pedidoSelecionado.getCodFil().toString(), CodigoCaixaforte, this.persistencia);
                if (this.osSelecionada == null) {
                    this.pedidoSelecionado.setOS(null);
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("NaoExisteOS"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                } else {
                    this.pedidoSelecionado.setOS(this.osSelecionada.getOS());
                }
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, this.caminho);
            }
        }
    }

    public List<Clientes> listarCliDst(String query) {
        try {
            this.clienteDestinoList = this.pedidosSatMobWeb.buscaClientes(this.pedidoSelecionado.getCodFil().toPlainString(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.clienteDestinoList;
    }

    public void reaproveitaClienteDestino() throws Exception {
        if (null != this.pedidoSelecionado
                && null != this.pedidoSelecionado.getCodCliReaproveitaDestino()
                && !this.pedidoSelecionado.getCodCliReaproveitaDestino().equals("")) {
            ClientesDao clientesDao = new ClientesDao();

            this.clienteDestino = new Clientes();
            this.clienteDestino = clientesDao.consultaSimplesCliente(this.pedidoSelecionado.getCodCliReaproveitaDestino(), this.persistencia);
            this.clienteDestinoList = new ArrayList<>();
            this.clienteDestinoList.add(this.clienteDestino);

            this.pedidoSelecionado.setCodCli2(this.clienteDestino.getCodigo());
            this.pedidoSelecionado.setNRed2(this.clienteDestino.getNRed());
            this.pedidoSelecionado.setRegiao2(this.clienteDestino.getRegiao());

            consularOSexistente();
        }
    }

    public void selecionarDestino(SelectEvent event) {
        this.clienteDestino = ((Clientes) event.getObject());
        this.pedidoSelecionado.setCodCli2(this.clienteDestino.getCodigo());
        this.pedidoSelecionado.setNRed2(this.clienteDestino.getNRed());
        this.pedidoSelecionado.setRegiao2(this.clienteDestino.getRegiao());

        consularOSexistente();
    }

    public void pesquisarOS() {
        try {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("pedido", "0");
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("os", this.pedidoSelecionado.getOS());
            FacesContext.getCurrentInstance().getExternalContext().redirect("../faturamento/os_vig.xhtml");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void mostrarFiliais() {
        if (this.mostrarFiliais) {
            this.filters.replace(" pedido.codfil = ? ", "");
        } else {
            this.filters.replace(" pedido.codfil = ? ", this.codfil);
        }
        //getPedidos();
        carregarListaPedidos();
    }

    public void exibirExcluidos() {
        if (this.exibirExcluidos) {
            this.filters.replace(" pedido.Flag_Excl <> ?", "");
        } else {
            this.filters.replace(" pedido.Flag_Excl <> ?", "*");
        }
        //getPedidos();
        carregarListaPedidos();
    }

    public void limpaFiltros() {
        this.filters.replace(" pedido.codfil = ? ", this.codfil);
        this.filters.replace(" pedido.Flag_Excl <> ?", "*");
        this.filters.replace("CodCli", "");
        this.filters.replace("CodPessoa", this.codPessoa.toPlainString().replace(".0", ""));
        this.filters.replace(" pedido.Flag_Excl <> ?", "*");
        this.filters.replace(" pedido.numero = ? ", "");
        this.filters.replace(" pedido.nred1 like ? ", "");
        this.filters.replace(" pedido.nred2 like ? ", "");

        //getPedidos();
        carregarListaPedidos();

        this.pedidoPesquisa = new Pedido();
        
        PrimeFaces.current().ajax().update("msgs", "main", "corporativo");

        this.limparFiltros = false;
        this.mostrarFiliais = false;
    }

    public boolean validarHorarioTrajeto() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);
        LocalDate dia = LocalDate.parse(removeMascaraData(this.pedidoSelecionado.getData()), DateTimeFormatter.ofPattern("yyyyMMdd"));

        Calendar hora1O = Calendar.getInstance();
        Calendar hora2O = Calendar.getInstance();
        Calendar hora1D = Calendar.getInstance();
        Calendar hora2D = Calendar.getInstance();

        // validando horários origem
        hora1O.setTime(sdf.parse(this.pedidoSelecionado.getHora1O()));
        hora1O.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora1O.get(Calendar.HOUR_OF_DAY), hora1O.get(Calendar.MINUTE));

        hora2O.setTime(sdf.parse(this.pedidoSelecionado.getHora2O()));
        hora2O.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora2O.get(Calendar.HOUR_OF_DAY), hora2O.get(Calendar.MINUTE));

        if (hora2O.before(hora1O)) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("VerifiqueHorario"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            return false;
        }

        // validando horários destino
        if (null != this.pedidoSelecionado.getHora1D()
                && !this.pedidoSelecionado.getHora1D().equals("")) {
            hora1D.setTime(sdf.parse(this.pedidoSelecionado.getHora1D()));
            hora1D.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                    hora1D.get(Calendar.HOUR_OF_DAY), hora1D.get(Calendar.MINUTE));

            hora2D.setTime(sdf.parse(this.pedidoSelecionado.getHora2D()));
            hora2D.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                    hora2D.get(Calendar.HOUR_OF_DAY), hora2D.get(Calendar.MINUTE));

            if (hora2D.before(hora1D)) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("VerifiqueHorario"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
                return false;
            }

            if (hora1D.before(hora1O)) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("RecolhimentoAnteriorEntrega"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
                return false;
            }
        }

        return true;
    }

    public void cadastrarPedido(boolean validarComposicoes) {
        try {
            if (null != this.filial.getCodFil()
                    && !this.filial.getCodFil().equals("")) {
                this.pedidoSelecionado.setCodFil(this.filial.getCodFil());
                this.pedidoSelecionado.setCodFil(BigDecimal.valueOf(new Float(this.filial.getCodFil())));
            } else {
                this.pedidoSelecionado.setCodFil(this.filial.getCodfilAc());
                this.pedidoSelecionado.setCodFil(BigDecimal.valueOf(new Float(this.filial.getCodfilAc())));
            }

            this.pedirComposicoes = false;
            if (!this.pedidoSelecionado.getTipo().equals("C")
                    && (this.pedidoSelecionado.getOS() == null || this.pedidoSelecionado.getOS().equals(""))) {
                throw new Exception(getMessageS("Obrigatorio") + ": " + getMessageS("OS"));
            }
            if (this.pedidoSelecionado.getCodCli1() == null) {
                throw new Exception(getMessageS("Obrigatorio") + ": " + getMessageS("Origem"));
            }
            if (this.pedidoSelecionado.getTipo().equals("C")) {
                this.pedidoSelecionado.setHora1D("");
                this.pedidoSelecionado.setHora2D("");
                this.pedidoSelecionado.setValor("0");
                this.pedidoSelecionado.setOS("0");
            }

            if (!this.pedidoSelecionado.getTipo().equals("C")
                    && this.pedidoSelecionado.getCodCli2() == null) {
                throw new Exception(getMessageS("Obrigatorio") + ": " + getMessageS("Destino"));
            }

            // - Ao gravar verificar se Origem é Caixa Forte, caso sim solicitar composição;
            for (CxForte cxForte : this.cxForteLista) {
                if (validarComposicoes
                        && this.pedidoSelecionado.getCodCli1().equals(cxForte.getCodCli())
                        && this.pedidoSelecionado.getCodFil().toBigInteger().toString().equals(cxForte.getCodFil().replace(".0", ""))
                        && (null == this.listaPedidoCedulaMoeda || this.listaPedidoCedulaMoeda.isEmpty())) {
                    this.pedirComposicoes = true;
                    break;
                }
            }

            if (this.pedirComposicoes) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("InsiraComposicao"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
                PrimeFaces.current().executeScript("PF('dlgInformarComposicoes').show();");
            } else {
                this.pedidoSelecionado.setOperIncl(RecortaAteEspaço(this.operador, 0, 10));
                this.pedidoSelecionado.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                this.pedidoSelecionado.setDt_Incl(getDataAtual("SQL"));
                this.pedidoSelecionado.setDt_Alter(getDataAtual("SQL"));
                this.pedidoSelecionado.setHr_Incl(getDataAtual("HORA"));
                this.pedidoSelecionado.setHr_Alter(getDataAtual("HORA"));

                this.pedidoSelecionado.setSolicitante(this.pedidoSelecionado.getSolicitante().toUpperCase());
                this.pedidoSelecionado.setPedidoCliente(this.pedidoSelecionado.getPedidoCliente().toUpperCase());
                this.pedidoSelecionado.setObs(this.pedidoSelecionado.getObs().toUpperCase());

                if (null != this.listaPedidoCedulaMoeda && this.listaPedidoCedulaMoeda.size() > 0) {
                    for (PedidoDN listaPedidoCedulaMoeda1 : listaPedidoCedulaMoeda) {
                        listaPedidoCedulaMoeda1.setCodFil(this.pedidoSelecionado.getCodFil().toPlainString().replace(".0", ""));
                    }

                    this.pedidoSelecionado.setListaPedidosComposicao(this.listaPedidoCedulaMoeda);
                }

                if (!validarHorarioTrajeto()) {
                    return;
                }

                if (this.flag != 2) {
                    this.pedidosSatMobWeb.inserirPedido(this.pedidoSelecionado, this.persistencia);
                } else {
                    this.pedidosSatMobWeb.editarPedido(this.pedidoSelecionado, this.persistencia);
                }

                this.pedidoSelecionado = new Pedido();
                this.listaPedidoCedulaMoeda = new ArrayList<>();
                this.listaPedidoMoeda = new ArrayList<>();
                this.listaPedidoCedula = new ArrayList<>();
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);

                carregarListaPedidos();

                PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void realizarUploadPedido(FileUploadEvent event) {
        try {
            new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL")).mkdirs();
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + event.getFile().getFileName();
            File file = new File(arquivo);
            if (file.exists()) {
                arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + getDataAtual("HHMMSS") + event.getFile().getFileName();
                file = new File(arquivo);
            }

            FileOutputStream output = new FileOutputStream(file);
            output.write(event.getFile().getContents());
            output.close();
            this.arquivosPedidos.add(file);

            FileReader fileReader = new FileReader(file);
            BufferedReader bufferedReader = new BufferedReader(fileReader);
            String linha;
            String[] dados;
            this.pedidosImportados = new ArrayList<>();
            this.volumesPreOrder = 0;
            ImportacaoPedido importacao;
            Clientes cliente1, cliente2;
            PreOrderVol preOrderVol;
            while ((linha = bufferedReader.readLine()) != null) {
                this.logerro.Grava("leitura arquivo: " + linha, caminho);
                try {
                    dados = linha.split("\\|");
                    importacao = new ImportacaoPedido();
                    importacao.getPedido().setDtColeta(dados[0]);
                    importacao.getPedido().setHora1D(LocalTime.parse(dados[1]).minusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));
                    importacao.getPedido().setHora2D(LocalTime.parse(dados[1]).plusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));

                    cliente1 = this.guiasweb.buscarClientePedido(dados[2], this.codfil, this.persistencia);
                    importacao.getPedido().setCodCli1(cliente1.getCodigo());
                    importacao.getPedido().setNRed1(cliente1.getNRed());

                    cliente2 = this.guiasweb.buscarClientePedido(dados[3], this.codfil, this.persistencia);
                    importacao.getPedido().setCodCli2(cliente2.getCodigo());
                    importacao.getPedido().setNRed2(cliente2.getNRed());

                    importacao.getPedido().setValor(dados[4]);
                    importacao.getPedido().setObs(dados[5]);

                    importacao.getPedido().setHora1O("07:45");
                    importacao.getPedido().setHora2O("08:15");
                    importacao.getPedido().setSituacao("PD");
                    importacao.getPedido().setClassifSrv("V");
                    importacao.getPedido().setCodFil(this.codfil);
                    importacao.getPedido().setSolicitante(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    importacao.getPedido().setOperIncl(FuncoesString.RecortaAteEspaço(this.operador, 0, 30));
                    importacao.getPedido().setDt_Incl(getDataAtual("SQL"));
                    importacao.getPedido().setHr_Incl(getDataAtual("HORA"));
                    importacao.getPedido().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                    importacao.getPedido().setDt_Alter(getDataAtual("SQL"));
                    importacao.getPedido().setHr_Alter(getDataAtual("HORA"));
                    importacao.getPedido().setFlag_Excl("");

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[6]);
                    preOrderVol.setQtde(dados[7]);
                    preOrderVol.setCodFil(this.codfil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[8]);
                    preOrderVol.setQtde(dados[9]);
                    preOrderVol.setCodFil(this.codfil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[10]);
                    preOrderVol.setQtde(dados[11]);
                    preOrderVol.setCodFil(this.codfil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[12]);
                    preOrderVol.setQtde(dados[13]);
                    preOrderVol.setCodFil(this.codfil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[14]);
                    preOrderVol.setQtde(dados[15]);
                    preOrderVol.setCodFil(this.codfil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[16]);
                    preOrderVol.setQtde(dados[17]);
                    preOrderVol.setCodFil(this.codfil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    this.pedidosImportados.add(importacao);
                } catch (Exception e2) {
                    this.logerro.Grava("erro leitura: " + e2.getMessage(), caminho);
                }
            }

            fileReader.close();

            if (this.pedidosImportados.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ArquivoInvalido"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.mensagemImportacao = getMessageS("MensagemImportacaoPedido")
                        .replace("%s1", String.valueOf(this.pedidosImportados.size()));
                PrimeFaces.current().ajax().update("formPedidosImportados");
                PrimeFaces.current().executeScript("PF('dlgPedidosImportados').show();");

                carregarListaPedidos();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void verificarExistenciaPreOrder() {
        try {
            List<String> pedidos = new ArrayList<>();
            for (BBPedidoAgencia bb : this.listaAgencias) {
                for (BBPedidoMalote malote : bb.getListaMalotes()) {
                    if (!pedidos.contains(malote.getPedidoCliente())) {
                        pedidos.add(malote.getPedidoCliente());
                    }
                }
            }
            this.lotes = this.guiasweb.verificarExistenciaPreOrder(this.listaAgencias.get(0).getDtColeta(), this.codcli, pedidos, this.persistencia);
            if (this.lotes.size() == 1) {
                this.mensagemImportacao = getMessageS("MensagemConfirmacaoPreOrder")
                        .replace("%s1", FuncoesString.formatarString(
                                LocalDate.parse(this.listaAgencias.get(0).getDtColeta(),
                                        DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("ddMMyyyy")), "##/##/####"));

                PrimeFaces.current().executeScript("PF('dlgConfirmacao').show();");
            } else if (this.lotes.size() > 1) {
                this.mensagemImportacao = getMessageS("MensagemConfirmacaoPreOrders")
                        .replace("%s1", FuncoesString.formatarString(
                                LocalDate.parse(this.listaAgencias.get(0).getDtColeta(),
                                        DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("ddMMyyyy")), "##/##/####"))
                        .replace("%s2", this.lotes.size() + "");

                PrimeFaces.current().executeScript("PF('dlgConfirmacao').show();");
            } else {
                inserirPreOrders();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void inserirPedidos() {
        try {
            this.guiasweb.inserirPedidosImportados(this.pedidosImportados, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgPedidosImportados').hide();");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ImportacaoCompleta"), null);
            PrimeFaces.current().ajax().update("cabecalho:tabela");
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void inserirPreOrders() {
        try {
            if (this.persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
                this.guiasweb.inserirPedidosImportados(this.listaAgencias, this.codcli, this.lotes,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
            } else {
                this.guiasweb.inserirPedidosImportados(this.listaAgencias, this.codcli, this.lotes, this.codfil,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
            }
            this.pedidosRecentes = this.guiasweb.pedidosRecentes(this.codcli, this.codfil, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgConfirmacao').hide();");
            PrimeFaces.current().executeScript("PF('dlgPreOrders').hide();");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ImportacaoCompleta"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void editarPedido() {
//        try {
//            if (!validarInput()) {
//                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio"), null);
//                FacesContext.getCurrentInstance().addMessage(null, message);
//            } else {
//                SimpleDateFormat formatoHora1 = new SimpleDateFormat("HH:mm");
//                Calendar calendarioOrigem = Calendar.getInstance();
//                Calendar calendarioDestino = Calendar.getInstance();
//
//                calendarioOrigem.setTime(formatoHora1.parse(this.pedido.getHora1O()));
//                calendarioOrigem.add(Calendar.MINUTE, +60);
//
//                calendarioDestino.setTime(formatoHora1.parse(this.pedido.getHora1D()));
//                calendarioDestino.add(Calendar.MINUTE, +60);
//
//                pedido.setSolicitante(pedido.getSolicitante().toUpperCase());
//                pedido.setPedidoCliente(pedido.getPedidoCliente().toUpperCase());
//                pedido.setObs(pedido.getObs().toUpperCase());
//
//                pedido.setSituacao("PD");
//                pedido.setTipo("T");
//                pedido.setNRed1(osVigOrigem.getNRed());
//                pedido.setNRed2(osVigDestino.getNRed());
//                pedido.setCodFil(filial.getCodfilAc());
//                pedido.setCodCli1(osVigOrigem.getCliente());
//                pedido.setCodCli2(osVigDestino.getCliente());
//                pedido.setFlag_Excl("");
//                pedido.setHr_Incl(getDataAtual("HORA"));
//
//                pedido.setOperIncl(FuncoesString.RecortaAteEspaço(operador, 0, 10));
//                pedido.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
//
//                if (pedido.getDt_Incl().equals(pedido.getData())) {
//                    pedido.setClassifSrv("E");
//                } else {
//                    pedido.setClassifSrv("V");
//                }
//
//                pedidosSatMobWeb.editarPedido(pedido, persistencia);
//                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
//                FacesContext.getCurrentInstance().addMessage(null, message);
//
//                PrimeFaces.current().executeScript("PF('dlgEdicaoPedido').hide();");
//            }
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            logerro.Grava(log, caminho);
//        }
    }

    public void excluirPedido() {
        try {
            this.pedidoSelecionado.setTipo("T");
            this.pedidoSelecionado.setFlag_Excl("*");
            this.pedidoSelecionado.setDt_Excl(getDataAtual("SQL"));
            this.pedidoSelecionado.setHr_Excl(getDataAtual("HORA"));
            this.pedidoSelecionado.setOperExcl(RecortaAteEspaço(operador, 0, 10));

            try {
                PedidoDao pedidoDao = new PedidoDao();
                pedidoDao.tratarPedidoParadaExcluida(this.pedidoSelecionado.getNumero().toPlainString().replace(".0", ""), this.pedidoSelecionado.getCodFil().toPlainString().replace(".0", ""), this.persistencia);
            } catch (Exception ex) {

            }

            FacesMessage message;
            if (this.pedidoSelecionado.getSituacao().equals("PD")) {
                rotassatweb.editarPedido(this.pedidoSelecionado, persistencia);

                OS_VigDao osvigdao = new OS_VigDao();
                osvigdao.acrescentarGtvQtde(this.pedidoSelecionado.getOS(), this.pedidoSelecionado.getCodFil().toString(), this.persistencia);

                message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ExclusaoSucesso"),
                        getMessageS("Pedido") + ": " + this.pedidoSelecionado.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);

                this.pedidoSelecionado = new Pedido();
                this.listaPedidoCedulaMoeda = new ArrayList<>();
                this.listaPedidoMoeda = new ArrayList<>();
                this.listaPedidoCedula = new ArrayList<>();

                carregarListaPedidos();

                //PrimeFaces.current().executeScript("PF('dlgEdicaoPedido').hide();");
            } else {
                message = new FacesMessage(FacesMessage.SEVERITY_WARN, getMessageS("ExclusaoPedidoNaoPermitida"),
                        getMessageS("Pedido") + ": " + this.pedidoSelecionado.getNumero().toBigInteger());
                FacesContext.getCurrentInstance().addMessage(null, message);
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void verificaClienteOrigemPedido() {
        if (null == this.nomecli || this.nomecli.equals("")) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneUnicoCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            PrimeFaces.current().executeScript("PF('dlgUpload').show();");
        }
    }

    public void preImportacaoPedido() {
        PrimeFaces.current().ajax().update("formImportarPedidos");
        PrimeFaces.current().executeScript("PF('dlgImportarPedidos').show();");
    }

    public void preGeracaoGTV() {
        try {
            listaGTVSeq = pedidosSatMobWeb.getAllByCodFil(codfil, persistencia);
            listaTesFecha = pedidosSatMobWeb.getTesFechasGeracaoGTV(codfil, dataSelecionada1, persistencia);

            PrimeFaces.current().ajax().update("formTarefaGeracaoGTV");
            PrimeFaces.current().executeScript("PF('dlgTarefaGeracaoGTV').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void selecionarSequenciaGTV() {
        numGuiaSelecionado = gtvSeqSelecionado.getSequencia().intValue();
    }

    public void gerarSaidasTesouraria() {
        try {
            int quantidade = selectedTesFecha.stream()
                    .map(item -> item.getQtde().intValue())
                    .reduce(0, (subtotal, element) -> subtotal + element);

            if (quantidade == 0) {
                throw new Exception("SelecionePeloMenosUmaTesouraria");
            }

            Format formatter = new SimpleDateFormat("yyyyMMdd");
            String data = formatter.format(dataSelecionada1);
            String operadorRed = RecortaAteEspaço(RecortaString(operador, 0, 10), 0, 10);
            String serie = gtvSeqSelecionado.getSerie();
            String dataAtual = DataAtual.getDataAtual("SQL");
            String horaAtual = DataAtual.getDataAtual("HORA");
            pedidosSatMobWeb.gerarSaidasTesouraria(data, codfil, selectedTesFecha,
                    serie, numGuiaSelecionado, quantidade,
                    operadorRed, dataAtual, horaAtual, persistencia);

            carregarListaPedidos();

            PrimeFaces.current().executeScript("PF('dlgSuprimentosSucesso').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public Pedido getPedidoSelecionado() {
        return pedidoSelecionado;
    }

    public void setPedidoSelecionado(Pedido pedidoSelecionado) {
        this.pedidoSelecionado = pedidoSelecionado;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getCodFil() {
        return codfil;
    }

    public void setCodFil(String codfil) {
        this.codfil = codfil;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }

    public Clientes getClienteOrigem() {
        return clienteOrigem;
    }

    public void setClienteOrigem(Clientes clienteOrigem) {
        this.clienteOrigem = clienteOrigem;
    }

    public Clientes getClienteDestino() {
        return clienteDestino;
    }

    public void setClienteDestino(Clientes clienteDestino) {
        this.clienteDestino = clienteDestino;
    }

    public List<Clientes> getClienteOrigemList() {
        return clienteOrigemList;
    }

    public void setClienteOrigemList(List<Clientes> clienteOrigemList) {
        this.clienteOrigemList = clienteOrigemList;
    }

    public List<Clientes> getClienteDestinoList() {
        return clienteDestinoList;
    }

    public void setClienteDestinoList(List<Clientes> clienteDestinoList) {
        this.clienteDestinoList = clienteDestinoList;
    }

    public OS_Vig getOsSelecionada() {
        return osSelecionada;
    }

    public void setOsSelecionada(OS_Vig osSelecionada) {
        this.osSelecionada = osSelecionada;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public Pedido getPedidoPesquisa() {
        return pedidoPesquisa;
    }

    public void setPedidoPesquisa(Pedido pedidoPesquisa) {
        this.pedidoPesquisa = pedidoPesquisa;
    }

    public List<PedidoDN> getListaPedidoCedulaMoeda() {
        return listaPedidoCedulaMoeda;
    }

    public void setListaPedidoCedulaMoeda(List<PedidoDN> listaPedidoCedulaMoeda) {
        this.listaPedidoCedulaMoeda = listaPedidoCedulaMoeda;
    }

    public List<GTVPedidoOSClienteTesSaida> getListaGuias() {
        return listaGuias;
    }

    public PedidoDN getPedidoCedulaMoeda() {
        return pedidoCedulaMoeda;
    }

    public void setPedidoCedulaMoeda(PedidoDN pedidoCedulaMoeda) {
        this.pedidoCedulaMoeda = pedidoCedulaMoeda;
    }

    public PedidoDN getPedidoCedulaMoedaNovo() {
        return pedidoCedulaMoedaNovo;
    }

    public void setPedidoCedulaMoedaNovo(PedidoDN pedidoCedulaMoedaNovo) {
        this.pedidoCedulaMoedaNovo = pedidoCedulaMoedaNovo;
    }

    public boolean isLimpar() {
        return limpar;
    }

    public void setLimpar(boolean limpar) {
        this.limpar = limpar;
    }

    public boolean isEditarComposicao() {
        return editarComposicao;
    }

    public void setEditarComposicao(boolean editarComposicao) {
        this.editarComposicao = editarComposicao;
    }

    public boolean isExibirExcluidos() {
        return exibirExcluidos;
    }

    public void setExibirExcluidos(boolean exibirExcluidos) {
        this.exibirExcluidos = exibirExcluidos;
    }

    public String getNomecli() {
        return nomecli;
    }

    public void setNomecli(String nomecli) {
        this.nomecli = nomecli;
    }

    public String getMensagemImportacao() {
        return mensagemImportacao;
    }

    public void setMensagemImportacao(String mensagemImportacao) {
        this.mensagemImportacao = mensagemImportacao;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getCodcli() {
        return codcli;
    }

    public void setCodcli(String codcli) {
        this.codcli = codcli;
    }

    public List<CxForte> getCxForteLista() {
        return cxForteLista;
    }

    public void setCxForteLista(List<CxForte> cxForteLista) {
        this.cxForteLista = cxForteLista;
    }

    public int getVolumesPreOrder() {
        return volumesPreOrder;
    }

    public void setVolumesPreOrder(int volumesPreOrder) {
        this.volumesPreOrder = volumesPreOrder;
    }

    public List<File> getArquivosPedidos() {
        return arquivosPedidos;
    }

    public void setArquivosPedidos(List<File> arquivosPedidos) {
        this.arquivosPedidos = arquivosPedidos;
    }

    public List<ImportacaoPedido> getPedidosImportados() {
        return pedidosImportados;
    }

    public void setPedidosImportados(List<ImportacaoPedido> pedidosImportados) {
        this.pedidosImportados = pedidosImportados;
    }

    public List<BBPedidoAgencia> getListaAgencias() {
        return listaAgencias;
    }

    public void setListaAgencias(List<BBPedidoAgencia> listaAgencias) {
        this.listaAgencias = listaAgencias;
    }

    public List<Integer> getLotes() {
        return lotes;
    }

    public void setLotes(List<Integer> lotes) {
        this.lotes = lotes;
    }

    public List<PreOrder> getPedidosRecentes() {
        return pedidosRecentes;
    }

    public void setPedidosRecentes(List<PreOrder> pedidosRecentes) {
        this.pedidosRecentes = pedidosRecentes;
    }

    public List<PreOrder> getPreOrderDetalhado() {
        return preOrderDetalhado;
    }

    public void setPreOrderDetalhado(List<PreOrder> preOrderDetalhado) {
        this.preOrderDetalhado = preOrderDetalhado;
    }

    public Rt_PercRotasClientes getClienteCancelamento() {
        return clienteCancelamento;
    }

    public void setClienteCancelamento(Rt_PercRotasClientes clienteCancelamento) {
        this.clienteCancelamento = clienteCancelamento;
    }

    public List<Rt_PercRotasClientes> getClienteCancelamentoList() {
        return clienteCancelamentoList;
    }

    public void setClienteCancelamentoList(List<Rt_PercRotasClientes> clienteCancelamentoList) {
        this.clienteCancelamentoList = clienteCancelamentoList;
    }

    public List<TesMoedas> getTesMoedasSelecao() {
        return tesMoedasSelecao;
    }

    public void setTesMoedasSelecao(List<TesMoedas> tesMoedasSelecao) {
        this.tesMoedasSelecao = tesMoedasSelecao;
    }

    public List<PedidoDN> getListaPedidoCedula() {
        return listaPedidoCedula;
    }

    public void setListaPedidoCedula(List<PedidoDN> listaPedidoCedula) {
        this.listaPedidoCedula = listaPedidoCedula;
    }

    public List<PedidoDN> getListaPedidoMoeda() {
        return listaPedidoMoeda;
    }

    public void setListaPedidoMoeda(List<PedidoDN> listaPedidoMoeda) {
        this.listaPedidoMoeda = listaPedidoMoeda;
    }

    public BigDecimal getValorTotalListaGuias() {
        return valorTotalListaGuias;
    }

    public GTVPedidoOSClienteTesSaida getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(GTVPedidoOSClienteTesSaida guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public GTVPedidoOSClienteTesSaida getGuiaEdicao() {
        return guiaEdicao;
    }

    public void setGuiaEdicao(GTVPedidoOSClienteTesSaida guiaEdicao) {
        this.guiaEdicao = guiaEdicao;
    }

    public List<GTVSeq> getListaGTVSeq() {
        return listaGTVSeq;
    }

    public GTVSeq getGtvSeqSelecionado() {
        return gtvSeqSelecionado;
    }

    public void setGtvSeqSelecionado(GTVSeq gtvSeqSelecionado) {
        this.gtvSeqSelecionado = gtvSeqSelecionado;
    }

    public List<TesFechaGeracaoGTV> getListaTesFecha() {
        return listaTesFecha;
    }

    public List<TesFechaGeracaoGTV> getSelectedTesFecha() {
        return selectedTesFecha;
    }

    public void setSelectedTesFecha(List<TesFechaGeracaoGTV> selectedTesFecha) {
        this.selectedTesFecha = selectedTesFecha;
    }

    public int getNumGuiaSelecionado() {
        return numGuiaSelecionado;
    }

    public void setNumGuiaSelecionado(int numGuiaSelecionado) {
        this.numGuiaSelecionado = numGuiaSelecionado;
    }

    public List<Pedido> getListaPedidos() {
        return listaPedidos;
    }

    public void setListaPedidos(List<Pedido> listaPedidos) {
        this.listaPedidos = listaPedidos;
    }
}

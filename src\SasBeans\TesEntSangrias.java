/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class TesEntSangrias {

    private BigDecimal Guia;
    private String Serie;
    private String Docto;
    private LocalDate DtColeta;
    private LocalDate DtMovimento;
    private BigDecimal ValorDecl;
    private BigDecimal ValorDeclCH;
    private BigDecimal ValorApurado;
    private BigDecimal Difmaior;
    private BigDecimal DifMenor;
    private BigDecimal ChequesQtde;
    private BigDecimal ChequesValor;
    private BigDecimal ChequesPreValor;
    private BigDecimal TicketsQtde;
    private BigDecimal TicketsValor;
    private BigDecimal MoedasValor;
    private BigDecimal CedFalsaQtde;
    private BigDecimal CedFalsaValor;
    private String PDV;
    private String PDVOperador;
    private String Malote;
    private String Lacre;
    private String Situacao;
    private String OperRec;
    private LocalDate Dt_Rec;
    private String Hr_Rec;
    private BigDecimal CodConvTkt;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(BigDecimal Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getDocto() {
        return Docto;
    }

    public void setDocto(String Docto) {
        this.Docto = Docto;
    }

    public LocalDate getDtColeta() {
        return DtColeta;
    }

    public void setDtColeta(LocalDate DtColeta) {
        this.DtColeta = DtColeta;
    }

    public LocalDate getDtMovimento() {
        return DtMovimento;
    }

    public void setDtMovimento(LocalDate DtMovimento) {
        this.DtMovimento = DtMovimento;
    }

    public BigDecimal getValorDecl() {
        return ValorDecl;
    }

    public void setValorDecl(BigDecimal ValorDecl) {
        this.ValorDecl = ValorDecl;
    }

    public BigDecimal getValorDeclCH() {
        return ValorDeclCH;
    }

    public void setValorDeclCH(BigDecimal ValorDeclCH) {
        this.ValorDeclCH = ValorDeclCH;
    }

    public BigDecimal getValorApurado() {
        return ValorApurado;
    }

    public void setValorApurado(BigDecimal ValorApurado) {
        this.ValorApurado = ValorApurado;
    }

    public BigDecimal getDifmaior() {
        return Difmaior;
    }

    public void setDifmaior(BigDecimal Difmaior) {
        this.Difmaior = Difmaior;
    }

    public BigDecimal getDifMenor() {
        return DifMenor;
    }

    public void setDifMenor(BigDecimal DifMenor) {
        this.DifMenor = DifMenor;
    }

    public BigDecimal getChequesQtde() {
        return ChequesQtde;
    }

    public void setChequesQtde(BigDecimal ChequesQtde) {
        this.ChequesQtde = ChequesQtde;
    }

    public BigDecimal getChequesValor() {
        return ChequesValor;
    }

    public void setChequesValor(BigDecimal ChequesValor) {
        this.ChequesValor = ChequesValor;
    }

    public BigDecimal getChequesPreValor() {
        return ChequesPreValor;
    }

    public void setChequesPreValor(BigDecimal ChequesPreValor) {
        this.ChequesPreValor = ChequesPreValor;
    }

    public BigDecimal getTicketsQtde() {
        return TicketsQtde;
    }

    public void setTicketsQtde(BigDecimal TicketsQtde) {
        this.TicketsQtde = TicketsQtde;
    }

    public BigDecimal getTicketsValor() {
        return TicketsValor;
    }

    public void setTicketsValor(BigDecimal TicketsValor) {
        this.TicketsValor = TicketsValor;
    }

    public BigDecimal getMoedasValor() {
        return MoedasValor;
    }

    public void setMoedasValor(BigDecimal MoedasValor) {
        this.MoedasValor = MoedasValor;
    }

    public BigDecimal getCedFalsaQtde() {
        return CedFalsaQtde;
    }

    public void setCedFalsaQtde(BigDecimal CedFalsaQtde) {
        this.CedFalsaQtde = CedFalsaQtde;
    }

    public BigDecimal getCedFalsaValor() {
        return CedFalsaValor;
    }

    public void setCedFalsaValor(BigDecimal CedFalsaValor) {
        this.CedFalsaValor = CedFalsaValor;
    }

    public String getPDV() {
        return PDV;
    }

    public void setPDV(String PDV) {
        this.PDV = PDV;
    }

    public String getPDVOperador() {
        return PDVOperador;
    }

    public void setPDVOperador(String PDVOperador) {
        this.PDVOperador = PDVOperador;
    }

    public String getMalote() {
        return Malote;
    }

    public void setMalote(String Malote) {
        this.Malote = Malote;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getOperRec() {
        return OperRec;
    }

    public void setOperRec(String OperRec) {
        this.OperRec = OperRec;
    }

    public LocalDate getDt_Rec() {
        return Dt_Rec;
    }

    public void setDt_Rec(LocalDate Dt_Rec) {
        this.Dt_Rec = Dt_Rec;
    }

    public String getHr_Rec() {
        return Hr_Rec;
    }

    public void setHr_Rec(String Hr_Rec) {
        this.Hr_Rec = Hr_Rec;
    }

    public BigDecimal getCodConvTkt() {
        return CodConvTkt;
    }

    public void setCodConvTkt(BigDecimal CodConvTkt) {
        this.CodConvTkt = CodConvTkt;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

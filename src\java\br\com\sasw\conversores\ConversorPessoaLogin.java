/*
 */
package br.com.sasw.conversores;

import SasBeans.PessoaLogin;
import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorLogin")
public class ConversorPessoaLogin implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        PessoaLogin pl = new PessoaLogin();
        pl.setCodigo(new BigDecimal(value));
        return pl;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return ((PessoaLogin) value).getCodigo().toPlainString();
        } catch (Exception e) {
            return null;
        }
    }
}

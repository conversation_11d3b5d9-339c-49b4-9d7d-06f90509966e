package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RHPonto;
import SasBeans.RHPontoProcessa;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RHPontoDao {

    /**
     * <PERSON><PERSON><PERSON><PERSON> o último check in/check out do supervisor.
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public RHPonto validarCheckInPrestador(String matricula, Persistencia persistencia) throws Exception {
        try {
            RHPonto retorno = new RHPonto();
            String sql = " select top 1 rhpontodet.*, rhponto.hora "
                    + " from rhpontodet "
                    + " left join rhponto on rhponto.dtcompet = rhpontodet.dtcompet "
                    + "                      and rhponto.matr = rhpontodet.matr "
                    + "                    and rhponto.batida = rhpontodet.batida "
                    + " where rhpontodet.batida between 8000 and 9000 and rhpontodet.matr = ? "
                    + " order by rhpontodet.dtcompet desc, rhpontodet.batida desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();
            while (consulta.Proximo()) {
                // Se a batida for par, a próxima iteração é check in. Se for impar, carrega o posto que tem que fazer check out.
                int batida = new BigInteger(consulta.getString("batida")).intValue();
                if (batida % 2 == 0) {
                    retorno.setLocal("");
                } else {
                    retorno.setLocal(consulta.getString("local"));
                }
                retorno.setDtCompet(consulta.getString("dtcompet"));
                retorno.setHora(consulta.getString("hora"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RHPontoDao.validarCheckInPrestador - " + e.getMessage() + "\r\n"
                    + " select top 1 rhpontodet.*, rhponto.hora "
                    + " from rhpontodet "
                    + " left join rhponto on rhponto.dtcompet = rhpontodet.dtcompet "
                    + "                      and rhponto.matr = rhpontodet.matr "
                    + "                    and rhponto.batida = rhpontodet.batida "
                    + " where rhpontodet.batida between 8000 and 9000 and rhpontodet.matr = " + matricula
                    + " order by rhpontodet.dtcompet desc, rhpontodet.batida desc ");
        }
    }

    /**
     * Obtem informações da ultima hora da batida
     *
     * @param matricula
     * @param competencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String obterHoraUltimaBatida(BigDecimal matricula, String competencia, Persistencia persistencia) throws Exception {
        String horaUltima = "";
        try {
            String sql = "SELECT TOP 1 Hora FROM RHPonto WHERE Matr = ? AND DtCompet = ? AND Batida < 9000 ORDER BY Batida DESC;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.setString(competencia);
            consulta.select();

            while (consulta.Proximo()) {
                horaUltima = consulta.getString("Hora");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("ocorreu um erro: " + ex.getMessage());
        }
        return horaUltima;
    }

    /**
     * Realiza o salvmento da batida
     *
     * @param rhPonto Objeto contendo dados do ponto
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void salvarRegistro(RHPonto rhPonto, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO RHPonto (Matr, dtcompet, batida, tipo, "
                    + "hora, motivo, dtbatida, nSerie, operador, dt_alter, "
                    + "hr_alter) VALUES (?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rhPonto.getMatr());
            consulta.setString(rhPonto.getDtCompet());
            consulta.setBigDecimal(rhPonto.getBatida());
            consulta.setBigDecimal(rhPonto.getTipo());
            consulta.setString(rhPonto.getHora());
            consulta.setString(rhPonto.getMotivo());
            consulta.setString(rhPonto.getDtBatida());
            consulta.setString(rhPonto.getNSerie());
            consulta.setString(rhPonto.getOperador());
            consulta.setString(rhPonto.getDt_Alter());
            consulta.setString(rhPonto.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("ocorreu um erro: " + ex.getMessage());
        }
    }

    public void salvarRegistroProcessamento(RHPontoProcessa rhPontoProcessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO RHPontoProcessa (Sequencia, Parametro, Matr, CodFil, DtCompet, Batida, EnderecoPDF, EnderecoHTML, HtmlEmail, HtmlPDF, HtmlPagina, Gerado, EnviaEmail, EnviaWpp, Operador, Dt_Alter, Hr_Alter) VALUES("
                    + " (SELECT ISNULL((MAX(Sequencia) + 1),1) FROM RHPontoProcessa), "
                    + " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rhPontoProcessa.getParametro());
            consulta.setString(rhPontoProcessa.getMatr());
            consulta.setString(rhPontoProcessa.getCodFil());
            consulta.setString(rhPontoProcessa.getDtCompet());
            consulta.setString(rhPontoProcessa.getBatida());
            consulta.setString(rhPontoProcessa.getEnderecoPDF());
            consulta.setString(rhPontoProcessa.getEnderecoHTML());
            consulta.setString(rhPontoProcessa.getHtmlEmail());
            consulta.setString(rhPontoProcessa.getHtmlEmail());
            consulta.setString(rhPontoProcessa.getHtmlPagina());
            consulta.setString(rhPontoProcessa.getGerado());
            consulta.setString(rhPontoProcessa.getEnviaEmail());
            consulta.setString(rhPontoProcessa.getEnviaWpp());
            consulta.setString(rhPontoProcessa.getOperador());
            consulta.setString(rhPontoProcessa.getDt_Alter());
            consulta.setString(rhPontoProcessa.getHr_Alter());
            
            consulta.insert();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("ocorreu um erro: " + ex.getMessage());
        }
    }
    
    
    /**
     * Obtem informações sobre a quantidade de baitda
     *
     * @param matricula matricula do funcionario
     * @param dtCompet data da competencia
     * @param persistencia conexão com o banco de dados
     * @return quantidade de batidas
     * @throws Exception
     */
    public int obterQuantidadeBatida(BigDecimal matricula, String dtCompet, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = "SELECT COUNT(*) qtd FROM RHPonto WHERE Matr = ? AND DtCompet = ? AND Batida < 9000";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.setString(dtCompet);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.obterQuantidadeBatida - " + ex.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM RHPonto WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet + " AND Batida < 9000");
        }
        return quantidade;
    }

    /**
     * Obtem informações sobre a quantidade de baitda
     *
     * @param matricula matricula do funcionario
     * @param dtCompet data da competencia
     * @param persistencia conexão com o banco de dados
     * @return quantidade de batidas
     * @throws Exception
     */
    public int obterQuantidadeBatidaSecao(BigDecimal matricula, String dtCompet, String secao, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = " SELECT COUNT(*) qtd FROM RHPonto "
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr "
                    + "                   AND RHPontoDet.Batida = RHPonto.Batida "
                    + "                 AND RHPontoDet.DtCompet = RHPonto.DtCompet "
                    + " WHERE RHPonto.Matr = ? AND RHPonto.DtCompet = ? AND RHPonto.Batida < 9000 AND RHPontoDet.Secao = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.setString(dtCompet);
            consulta.setString(secao);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.obterQuantidadeBatida - " + ex.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM RHPonto WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet + " AND Batida < 9000");
        }
        return quantidade;
    }

    /**
     * Obtem informações sobre a quantidade de check ins
     *
     * @param matricula matricula do funcionario
     * @param dtCompet data da competencia
     * @param persistencia conexão com o banco de dados
     * @return quantidade de batidas
     * @throws Exception
     */
    public int obterProximoCheckIn(BigDecimal matricula, String dtCompet, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = "SELECT isnull(max(batida),9000) qtd FROM RHPonto WHERE Matr = ? AND DtCompet = ?  AND Batida > 9000";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.setString(dtCompet);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.obterQuantidadeCheckIns - " + ex.getMessage() + "\r\n"
                    + "SELECT isnull(max(batida),9000) qtd FROM RHPonto WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet + " AND Batida > 9000");
        }
        return quantidade;
    }

    /**
     * Obtem informações sobre a quantidade de check ins dos prestadores de
     * serviço
     *
     * @param matricula matricula do funcionario
     * @param dtCompet data da competencia
     * @param persistencia conexão com o banco de dados
     * @return quantidade de batidas
     * @throws Exception
     */
    public RHPonto obterProximoCheckInPrestador(BigDecimal matricula, String dtCompet, Persistencia persistencia) throws Exception {
        try {
            RHPonto retorno = new RHPonto();
            String sql = " SELECT TOP 1 RHPontoUltimo.DtCompet, RHPontoUltimo.Hora, "
                    + " (SELECT ISNULL(MAX(RHPontoHoje.batida),8000) Batida "
                    + "    FROM RHPonto RHPontoHoje "
                    + "    WHERE RHPontoHoje.Matr = RHPontoUltimo.Matr "
                    + "         AND RHPontoHoje.DtCompet = ?) Batida "
                    + " FROM RHPonto RHPontoUltimo "
                    + " WHERE RHPontoUltimo.matr = ?"
                    + " ORDER BY RHPontoUltimo.DtCompet DESC, RHPontoUltimo.Hora DESC ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setBigDecimal(matricula);
            consulta.select();

            while (consulta.Proximo()) {
                // Se a batida for par, a próxima iteração é check in. Se for impar, carrega o posto que tem que fazer check out.
                retorno.setBatida(consulta.getBigDecimal("Batida"));
                retorno.setDtCompet(consulta.getString("DtCompet"));
                retorno.setHora(consulta.getString("Hora"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.obterQuantidadeCheckIns - " + ex.getMessage() + "\r\n"
                    + "SELECT isnull(max(batida),8000) qtd FROM RHPonto WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet
                    + " AND Batida between 8000 and 9000");
        }
    }

    public int obterQuantidadeCheckIns(BigDecimal matricula, String dtCompet, String secao, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = "SELECT COUNT(*) qtd FROM RHPontoDet WHERE Matr = ? AND DtCompet = ? AND Batida > 9000 AND Secao = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.setString(dtCompet);
            consulta.setString(secao);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.obterQuantidadeCheckIns - " + ex.getMessage() + "\r\n"
                    + "SELECT isnull(max(batida),9000) qtd FROM RHPonto WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet + " AND Batida > 9000");
        }
        return quantidade;
    }

    /**
     * Obtem a quantidade de check ins que um prestador realizou no dia.
     *
     * @param matricula
     * @param dtCompet
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int obterQuantidadeCheckInsPrestador(BigDecimal matricula, String dtCompet, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = " select top 1 batida "
                    + " from rhponto "
                    + " where batida between 8000 and 9000 "
                    + "         and matr = ? "
                    + " order by dtcompet desc, batida desc ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("batida");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.obterQuantidadeCheckIns - " + ex.getMessage() + "\r\n"
                    + " select top 1 batida "
                    + " from rhponto "
                    + " where batida between 8000 and 9000 "
                    + "         and matr = " + matricula
                    + " order by dtcompet desc, batida desc");
        }
        return quantidade;
    }

    /**
     * Lista os pontos de um funcionário por data
     *
     * @param matricula
     * @param dtCompet
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHPonto> listaPontos(String matricula, String dtCompet, Persistencia persistencia) throws Exception {
        try {
            List<RHPonto> retorno = new ArrayList<>();
            String sql = " SELECT RHPonto.*, RHPontoGEO.latitude, \n"
                    + " RHPontoGEO.longitude, RHPontoDet.Secao, RHPontoImagem.url \n"
                    + "FROM RHPonto \n"
                    + "LEFT Join RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                    AND RHPontoGEO.Batida = RHPonto.Batida \n"
                    + "LEFT Join RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                    AND RHPontoDet.Batida = RHPonto.Batida "
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = rhponto.matr "
                    + "                         AND RHPontoImagem.dtcompet = rhponto.dtcompet "
                    + "                         AND RHPontoImagem.batida = rhponto.batida "
                    + " WHERE RHPonto.Matr = ? AND RHPonto.DtCompet = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.setString(dtCompet);
            consulta.select();
            RHPonto rhPonto;
            while (consulta.Proximo()) {
                rhPonto = new RHPonto();
                rhPonto.setMatr(consulta.getBigDecimal("Matr"));
                rhPonto.setDtCompet(consulta.getString("dtcompet"));
                rhPonto.setBatida(consulta.getBigDecimal("batida"));
                rhPonto.setTipo(consulta.getBigDecimal("tipo"));
                rhPonto.setHora(consulta.getString("hora"));
                rhPonto.setMotivo(consulta.getString("motivo"));
                rhPonto.setDtBatida(consulta.getString("dtbatida"));
                rhPonto.setNSerie(consulta.getString("nSerie"));
                rhPonto.setOperador(consulta.getString("operador"));
                rhPonto.setDt_Alter(consulta.getString("dt_alter"));
                rhPonto.setHr_Alter(consulta.getString("hr_alter"));
                rhPonto.setLatitude(consulta.getString("latitude"));
                rhPonto.setLongitude(consulta.getString("longitude"));
                rhPonto.setSecao(consulta.getString("Secao"));
                rhPonto.setURL(consulta.getString("url"));

                rhPonto.setFoto("https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa()
                        + "/ponto/" + rhPonto.getDtCompet() + "/" + trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg");

                retorno.add(rhPonto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.listaPontos - " + ex.getMessage() + "\r\n"
                    + " SELECT RHPonto.*, RHPontoGEO.latitude, RHPontoGEO.longitude, RHPontoDet.Secao\n"
                    + "FROM RHPonto \n"
                    + "LEFT Join RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "                    AND RHPontoGEO.Batida = RHPonto.Batida \n"
                    + "LEFT Join RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "                    AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "                    AND RHPontoDet.Batida = RHPonto.Batida "
                    + " WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet);
        }
    }

    /**
     * Busca a informação de um ponto
     *
     * @param matricula
     * @param dtCompet
     * @param batida
     * @param persistencia
     * @return
     * @throws Exception
     */
    public RHPonto buscaPonto(String dtCompet, String matricula, String batida, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT pstserv.local, funcion.nome, rhponto.*,  "
                    + " rhpontogeo.latitude, rhpontogeo.longitude, RHPontoImagem.url "
                    + " FROM rhponto "
                    + " LEFT join funcion ON funcion.matr = rhponto.matr "
                    + " LEFT join rhpontogeo ON rhpontogeo.matr = rhponto.matr "
                    + "                         AND rhpontogeo.batida = rhponto.batida "
                    + "                         AND rhpontogeo.dtcompet = rhponto.dtcompet "
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = rhponto.matr "
                    + "                         AND RHPontoImagem.dtcompet = rhponto.dtcompet "
                    + "                         AND RHPontoImagem.batida = rhponto.batida "
                    + " LEFT join rh_horas ON rh_horas.matr = rhponto.matr "
                    + "                    AND rh_horas.data = rhponto.dtcompet "
                    + " LEFT join pstserv ON pstserv.secao = rh_horas.secao "
                    + "                   AND pstserv.codfil = rh_horas.codfil "
                    + " WHERE RHPonto.DtCompet = ? AND RHPonto.Matr = ? AND RHPonto.Batida = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setString(matricula);
            consulta.setString(batida);
            consulta.select();
            RHPonto rhPonto = new RHPonto();
            if (consulta.Proximo()) {
                rhPonto.setFuncionario(consulta.getString("nome"));
                rhPonto.setLocal(consulta.getString("local"));
                rhPonto.setMatr(consulta.getBigDecimal("Matr"));
                rhPonto.setDtCompet(consulta.getString("dtcompet"));
                rhPonto.setBatida(consulta.getBigDecimal("batida"));
                rhPonto.setTipo(consulta.getBigDecimal("tipo"));
                rhPonto.setHora(consulta.getString("hora"));
                rhPonto.setMotivo(consulta.getString("motivo"));
                rhPonto.setDtBatida(consulta.getString("dtbatida"));
                rhPonto.setNSerie(consulta.getString("nSerie"));
                rhPonto.setOperador(consulta.getString("operador"));
                rhPonto.setDt_Alter(consulta.getString("dt_alter"));
                rhPonto.setHr_Alter(consulta.getString("hr_alter"));
                rhPonto.setLatitude(consulta.getString("latitude"));
                rhPonto.setLongitude(consulta.getString("longitude"));
                rhPonto.setURL(consulta.getString("url"));
                String url = URL_SASW_BLOB + 
                        persistencia.getEmpresa() + "/ponto/" + 
                        rhPonto.getDtCompet() + "/" + 
                        trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg";
                rhPonto.setURL(url);
                rhPonto.setFoto("https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa()
                        + "/ponto/" + rhPonto.getDtCompet() + "/" + trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg");
            }
            consulta.Close();
            return rhPonto;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.listaPontos - " + ex.getMessage() + "\r\n"
                    + " SELECT RHPonto.*, RHPontoGEO.latitude, RHPontoGEO.longitude"
                    + " FROM RHPonto "
                    + " LEFT Join RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                     AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                     AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " WHERE Matr = " + matricula + " AND DtCompet = " + dtCompet + " AND batida = " + batida);
        }
    }
    private static final String URL_SASW_BLOB = "https://saswsatellite.blob.core.windows.net/imagem/";

    /**
     * Lista os pontos de uma data. Se matrícula for uma string vazia, lista de
     * todas as matrículas.
     *
     * @param dtCompet
     * @param matricula
     * @param secao
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHPonto> listaTodosPontos(String dtCompet, String matricula, String secao, String codfil, Persistencia persistencia) throws Exception {
        try {
            List<RHPonto> retorno = new ArrayList<>();
            String sql = " SELECT \n"
                    + "     RHPontoDet.secao, pstserv.local, RHPonto.*, "
                    + "     Funcion.nome, RHPontoGEO.latitude, RHPontoGEO.longitude, \n"
                    + "     RHPontoImagem.url"
                    + "     CAST(CAST(dbo.fun_calcdistancia(clientes.Latitude, clientes.Longitude, RHPontoGEO.Latitude, RHPontoGEO.Longitude) AS DECIMAL(18,3)) AS VARCHAR(30)) Distancia \n"
                    + " FROM \n"
                    + "     RHPonto \n"
                    + " LEFT JOIN \n"
                    + "     Funcion ON Funcion.Matr = RHPonto.Matr \n"
                    + " LEFT JOIN \n"
                    + "     RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr \n"
                    + "         AND RHPontoGEO.DtCompet = RHPonto.DtCompet \n"
                    + "         AND RHPontoGEO.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN \n"
                    + "     RHPontoDet ON RHPontoDet.Matr = RHPonto.Matr \n"
                    + "         AND RHPontoDet.DtCompet = RHPonto.DtCompet \n"
                    + "         AND RHPontoDet.Batida = RHPonto.Batida \n"
                    + " LEFT JOIN \n"
                    + "     pstserv ON pstserv.secao = RHPontoDet.secao \n"
                    + "         AND pstserv.codfil = Funcion.codfil \n"
                    + " LEFT JOIN \n"
                    + "     clientes ON clientes.codigo = pstserv.CodCli \n"
                    + "         AND clientes.codfil = pstserv.codfil \n"
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = rhponto.matr "
                    + "                         AND RHPontoImagem.dtcompet = rhponto.dtcompet "
                    + "                         AND RHPontoImagem.batida = rhponto.batida "
                    + " WHERE \n"
                    + "     RHPonto.DtCompet = ? And pstserv.codfil = ? AND RHPonto.Batida < 9000 ";
            if (!matricula.equals("")) {
                sql += " AND RHPonto.matr = ? ";
            }
            if (!secao.equals("")) {
                sql += " AND RHPontoDet.Secao = ? ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setString(codfil);
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }
            consulta.select();
            RHPonto rhPonto;
            while (consulta.Proximo()) {
                rhPonto = new RHPonto();
                rhPonto.setLocal(consulta.getString("local"));
                rhPonto.setSecao(consulta.getString("secao"));
                rhPonto.setMatr(consulta.getBigDecimal("Matr"));
                rhPonto.setDtCompet(consulta.getString("dtcompet"));
                rhPonto.setBatida(consulta.getBigDecimal("batida"));
                rhPonto.setTipo(consulta.getBigDecimal("tipo"));
                rhPonto.setHora(consulta.getString("hora"));
                rhPonto.setMotivo(consulta.getString("motivo"));
                rhPonto.setDtBatida(consulta.getString("dtbatida"));
                rhPonto.setNSerie(consulta.getString("nSerie"));
                /**
                 * Salvando o nome do funcionário em operador
                 */
                rhPonto.setOperador(consulta.getString("nome"));
                rhPonto.setFuncionario(consulta.getString("nome"));
                rhPonto.setDt_Alter(consulta.getString("dt_alter"));
                rhPonto.setHr_Alter(consulta.getString("hr_alter"));
                rhPonto.setLatitude(consulta.getString("latitude"));
                rhPonto.setLongitude(consulta.getString("longitude"));
                rhPonto.setDistancia(consulta.getString("distancia"));
                rhPonto.setURL(consulta.getString("url"));

                rhPonto.setFoto("https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa()
                        + "/ponto/" + rhPonto.getDtCompet() + "/" + trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg");

                retorno.add(rhPonto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.listaPontos - " + ex.getMessage() + "\r\n"
                    + " SELECT RHPonto.*, Funcion.nome "
                    + " LEFT Join RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                     AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                     AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " FROM RHPonto "
                    + " LEFT Join Funcion ON Funcion.Matr = RHPonto.Matr "
                    + "                    AND Rh_Horas.data = RHPonto.DtCompet "
                    + " LEFT Join Rh_Horas ON Rh_Horas.Matr = RHPonto.Matr "
                    + "                    AND Rh_Horas.data = RHPonto.DtCompet "
                    + " LEFT join pstserv ON pstserv.secao = rh_horas.secao "
                    + " WHERE RHPonto.DtCompet = " + dtCompet);
        }
    }

    /**
     * Lista os check ins de todos os prestadors
     *
     * @param dtCompet
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHPonto> listaTodosCheckInsPrestador(String dtCompet, String matricula, Persistencia persistencia) throws Exception {
        try {
            List<RHPonto> retorno = new ArrayList<>();
            String sql = " SELECT * "
                    + " FROM ( SELECT ROW_NUMBER() OVER ( PARTITION BY RHPontoDet.Secao ORDER BY RHPonto.batida ASC) RowNum, "
                    + " RHPonto.*, RHPontoDet.Secao, Pessoa.Nome, "
                    + " RHPontoGEO.Latitude, RHPontoGEO.Longitude, RHPontoDet.Local, "
                    + " RHPontoImagem.url"
                    + " FROM RHPonto "
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Batida = RHPonto.Batida "
                    + "                       AND RHPontoDet.Matr = RHPonto.Matr "
                    + "                   AND RHPontoDet.DtCompet = RHPonto.DtCompet "
                    + " LEFT JOIN RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                 AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                   AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = rhponto.matr "
                    + "                         AND RHPontoImagem.dtcompet = rhponto.dtcompet "
                    + "                         AND RHPontoImagem.batida = rhponto.batida "
                    + " LEFT JOIN Pessoa ON Pessoa.Matr = RHPonto.Matr "
                    + " WHERE RHPonto.Batida between 8000 and  9000 "
                    + "   AND RHPonto.DtCompet = ? ";
            if (!matricula.equals("")) {
                sql += " AND RHPonto.Matr = ? ";
            }
            sql += " ) AS RowConstrainedResult; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            consulta.select();
            RHPonto rhPonto;
            while (consulta.Proximo()) {
                rhPonto = new RHPonto();
                rhPonto.setLocal(consulta.getString("Local"));
                rhPonto.setSecao(consulta.getString("Secao"));
                rhPonto.setMatr(consulta.getBigDecimal("Matr"));
                rhPonto.setDtCompet(consulta.getString("DtCompet"));
                rhPonto.setBatida(consulta.getBigDecimal("Batida"));
                /**
                 * Salvando a ordem da batida em Tipo
                 */
                rhPonto.setTipo(consulta.getBigDecimal("RowNum"));
                rhPonto.setHora(consulta.getString("Hora"));
                rhPonto.setMotivo(consulta.getString("Motivo"));
                rhPonto.setDtBatida(consulta.getString("DtBatida"));
                rhPonto.setNSerie(consulta.getString("nSerie"));
                /**
                 * Salvando o nome do funcionário em Pperador
                 */
                rhPonto.setOperador(consulta.getString("Nome"));
                rhPonto.setDt_Alter(consulta.getString("Dt_Alter"));
                rhPonto.setHr_Alter(consulta.getString("Hr_Alter"));
                rhPonto.setLatitude(consulta.getString("Latitude"));
                rhPonto.setLongitude(consulta.getString("Longitude"));
                rhPonto.setURL(consulta.getString("url"));

                rhPonto.setFoto("https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa()
                        + "/ponto/" + rhPonto.getDtCompet() + "/" + trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg");

                retorno.add(rhPonto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.listaTodosCheckInsPrestador - " + ex.getMessage() + "\r\n"
                    + " SELECT * "
                    + " FROM ( SELECT ROW_NUMBER() OVER ( PARTITION BY RHPontoDet.Secao ORDER BY RHPonto.batida ASC) RowNum, "
                    + " RHPonto.*, RHPontoDet.Secao, Pessoa.Nome, RHPontoGEO.Latitude, RHPontoGEO.Longitude, RHPontoDet.Local "
                    + " FROM RHPonto "
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Batida = RHPonto.Batida "
                    + "                       AND RHPontoDet.Matr = RHPonto.Matr "
                    + "                   AND RHPontoDet.DtCompet = RHPonto.DtCompet "
                    + " LEFT JOIN RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                 AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                   AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " LEFT JOIN Pessoa ON Pessoa.Matr = RHPonto.Matr "
                    + " WHERE RHPonto.Batida between 8000 and  9000 "
                    + "   AND RHPonto.DtCompet = ? "
                    + (!matricula.equals("") ? " AND RHPonto.Matr = " + matricula : "")
                    + " ) AS RowConstrainedResult;");
        }
    }

    /**
     * Lista os check ins de todos os supervisores
     *
     * @param dtCompet
     * @param matricula
     * @param secao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHPonto> listaTodosCheckIns(String dtCompet, String matricula, String secao, Persistencia persistencia) throws Exception {
        try {
            List<RHPonto> retorno = new ArrayList<>();
            String sql = " SELECT * "
                    + " FROM ( SELECT ROW_NUMBER() OVER ( PARTITION BY RHPontoDet.Secao ORDER BY RHPonto.batida ASC) RowNum, "
                    + " RHPonto.*, RHPontoDet.Secao, Funcion.Nome, "
                    + " RHPontoGEO.Latitude, RHPontoGEO.Longitude, PstServ.Local, "
                    + " RHPontoImagem.url"
                    + " FROM RHPonto "
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Batida = RHPonto.Batida "
                    + "                       AND RHPontoDet.Matr = RHPonto.Matr "
                    + "                   AND RHPontoDet.DtCompet = RHPonto.DtCompet "
                    + " LEFT JOIN RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                 AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                   AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = rhponto.matr "
                    + "                         AND RHPontoImagem.dtcompet = rhponto.dtcompet "
                    + "                         AND RHPontoImagem.batida = rhponto.batida "
                    + " LEFT JOIN Funcion ON Funcion.Matr = RHPonto.Matr "
                    + " LEFT JOIN PstServ ON PstServ.Secao = RHPontoDet.Secao "
                    + "                   AND PstServ.Codfil = Funcion.Codfil "
                    + " WHERE RHPonto.Batida > 9000 "
                    + "   AND RHPonto.DtCompet = ? ";
            if (!matricula.equals("")) {
                sql += " AND RHPonto.Matr = ? ";
            }
            if (!secao.equals("")) {
                sql += " AND RHPontoDet.Secao = ? ";
            }
            sql += " ) AS RowConstrainedResult; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            if (!matricula.equals("")) {
                consulta.setString(matricula);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }
            consulta.select();
            RHPonto rhPonto;
            while (consulta.Proximo()) {
                rhPonto = new RHPonto();
                rhPonto.setLocal(consulta.getString("Local"));
                rhPonto.setSecao(consulta.getString("Secao"));
                rhPonto.setMatr(consulta.getBigDecimal("Matr"));
                rhPonto.setDtCompet(consulta.getString("DtCompet"));
                rhPonto.setBatida(consulta.getBigDecimal("Batida"));
                /**
                 * Salvando a ordem da batida em Tipo
                 */
                rhPonto.setTipo(consulta.getBigDecimal("RowNum"));
                rhPonto.setHora(consulta.getString("Hora"));
                rhPonto.setMotivo(consulta.getString("Motivo"));
                rhPonto.setDtBatida(consulta.getString("DtBatida"));
                rhPonto.setNSerie(consulta.getString("nSerie"));
                /**
                 * Salvando o nome do funcionário em Pperador
                 */
                rhPonto.setOperador(consulta.getString("Nome"));
                rhPonto.setDt_Alter(consulta.getString("Dt_Alter"));
                rhPonto.setHr_Alter(consulta.getString("Hr_Alter"));
                rhPonto.setLatitude(consulta.getString("Latitude"));
                rhPonto.setLongitude(consulta.getString("Longitude"));
                rhPonto.setURL(consulta.getString("url"));

                rhPonto.setFoto("https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa()
                        + "/ponto/" + rhPonto.getDtCompet() + "/" + trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg");

                retorno.add(rhPonto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.listaTodosCheckIns - " + ex.getMessage() + "\r\n"
                    + " SELECT * "
                    + " FROM ( SELECT ROW_NUMBER() OVER ( PARTITION BY RHPontoDet.Secao ORDER BY RHPonto.batida ASC) RowNum, "
                    + " RHPonto.*, RHPontoDet.Secao, Funcion.Nome, RHPontoGEO.Latitude, RHPontoGEO.Longitude, PstServ.Local "
                    + " FROM RHPonto "
                    + " LEFT JOIN RHPontoDet ON RHPontoDet.Batida = RHPonto.Batida "
                    + "                       AND RHPontoDet.Matr = RHPonto.Matr "
                    + "                   AND RHPontoDet.DtCompet = RHPonto.DtCompet "
                    + " LEFT JOIN RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                 AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                   AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " LEFT JOIN Funcion ON Funcion.Matr = RHPonto.Matr "
                    + " LEFT JOIN PstServ ON PstServ.Secao = RHPontoDet.Secao "
                    + "                   AND PstServ.Codfil = Funcion.Codfil "
                    + " WHERE RHPonto.Batida > 9000 "
                    + "   AND RHPonto.DtCompet = " + dtCompet
                    + (!matricula.equals("") ? " AND RHPonto.Matr = " + matricula : "")
                    + (!secao.equals("") ? " AND RHPontoDet.Secao = " + secao : "")
                    + " ) AS RowConstrainedResult;");
        }
    }

    /**
     * Lista os pontos batidos em um posto em uma data específica
     *
     * @param secao
     * @param dtCompet
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHPonto> listaPontosPosto(String secao, String dtCompet, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<RHPonto> retorno = new ArrayList<>();
            String sql = " SELECT funcion.nome, rhponto.*, RHPontoGEO.latitude, "
                    + " RHPontoGEO.longitude, RHPontoImagem.url "
                    + " FROM rhponto "
                    + " LEFT join rh_horas ON rh_horas.matr = rhponto.matr "
                    + "             AND rh_horas.data = rhponto.dtcompet "
                    + " LEFT Join RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                     AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                     AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " LEFT join RHPontoImagem ON RHPontoImagem.matr = rhponto.matr "
                    + "                         AND RHPontoImagem.dtcompet = rhponto.dtcompet "
                    + "                         AND RHPontoImagem.batida = rhponto.batida "
                    + " LEFT join funcion ON funcion.matr = rhponto.matr "
                    + " where rh_horas.data = ? AND rh_horas.secao = ? AND rh_horas.codfil = ? "
                    + " order by dtbatida desc, hora desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setString(secao);
            consulta.setString(codFil);
            consulta.select();
            RHPonto rhPonto;
            while (consulta.Proximo()) {
                rhPonto = new RHPonto();
                rhPonto.setFuncionario(consulta.getString("nome"));
                rhPonto.setMatr(consulta.getBigDecimal("Matr"));
                rhPonto.setDtCompet(consulta.getString("dtcompet"));
                rhPonto.setBatida(consulta.getBigDecimal("batida"));
                rhPonto.setTipo(consulta.getBigDecimal("tipo"));
                rhPonto.setHora(consulta.getString("hora"));
                rhPonto.setMotivo(consulta.getString("motivo"));
                rhPonto.setDtBatida(consulta.getString("dtbatida"));
                rhPonto.setNSerie(consulta.getString("nSerie"));
                rhPonto.setOperador(consulta.getString("operador"));
                rhPonto.setDt_Alter(consulta.getString("dt_alter"));
                rhPonto.setHr_Alter(consulta.getString("hr_alter"));
                rhPonto.setLatitude(consulta.getString("latitude"));
                rhPonto.setLongitude(consulta.getString("longitude"));
                rhPonto.setURL(consulta.getString("url"));

                rhPonto.setFoto("https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa()
                        + "/ponto/" + rhPonto.getDtCompet() + "/" + trataMatriculaFoto(rhPonto.getMatr().toBigInteger().toString())
                        + "_" + rhPonto.getTipo() + ".jpg");

                retorno.add(rhPonto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHPontoDao.listaPontosPosto - " + ex.getMessage() + "\r\n"
                    + " SELECT rhponto.*, RHPontoGEO.latitude, RHPontoGEO.longitude "
                    + " FROM rhponto "
                    + " LEFT join rh_horas ON rh_horas.matr = rhponto.matr "
                    + "             AND rh_horas.data = rhponto.dtcompet "
                    + " LEFT Join RHPontoGEO ON RHPontoGEO.Matr = RHPonto.Matr "
                    + "                     AND RHPontoGEO.DtCompet = RHPonto.DtCompet "
                    + "                     AND RHPontoGEO.Batida = RHPonto.Batida "
                    + " where rh_horas.data = " + dtCompet + " AND rh_horas.secao = " + secao
                    + " order by dtbatida desc ");
        }
    }

    private String trataMatriculaFoto(String matricula) {
        if (matricula.length() == 1) {
            matricula = "0000000" + matricula;
        } else if (matricula.length() == 2) {
            matricula = "000000" + matricula;
        } else if (matricula.length() == 3) {
            matricula = "00000" + matricula;
        } else if (matricula.length() == 4) {
            matricula = "0000" + matricula;
        } else if (matricula.length() == 5) {
            matricula = "000" + matricula;
        } else if (matricula.length() == 6) {
            matricula = "00" + matricula;
        } else if (matricula.length() == 7) {
            matricula = "0" + matricula;
        }
        return matricula;
    }
}

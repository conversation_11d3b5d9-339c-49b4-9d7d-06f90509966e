/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CCusto;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CCustoDao {

    public List<CCusto> listarCCusto(Persistencia persistencia) throws Exception {
        try {
            List<CCusto> retorno = new ArrayList<>();
            String sql = " SELECT * FROM CCusto ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            CCusto ccusto;
            while (consulta.Proximo()) {
                ccusto = new CCusto();
                ccusto.setCCusto(consulta.getString("CCusto"));
                ccusto.setPraca(consulta.getString("Praca"));
                ccusto.setNRed(consulta.getString("NRed"));
                ccusto.setDescricao(consulta.getString("Descricao"));
                ccusto.setCNPJ(consulta.getString("CNPJ"));
                ccusto.setCPF(consulta.getString("CPF"));
                ccusto.setContaCTB(consulta.getString("ContaCTB"));
                ccusto.setTipo(consulta.getString("Tipo"));
                ccusto.setSituacao(consulta.getString("Situacao"));
                ccusto.setOperador(consulta.getString("Operador"));
                ccusto.setDt_Alter(consulta.getString("Dt_Alter"));
                ccusto.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(ccusto);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CCustoDao.listarCCusto - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM CCusto ");
        }
    }
}

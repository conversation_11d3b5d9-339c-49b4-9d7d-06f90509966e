/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.QueueFech;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import SasBeans.QueueFech;
import SasBeansCompostas.QueueFechDTO;
import SasBeansCompostas.QueueFechPessoaDTO;
import SasBeansCompostas.QueueFechSolicitacaoSenhaDTO;
import SasDaos.Lgbr_tsolicitacaoDao;
import SasDaos.QueueFechDao;
import SasDaos.User_LkDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.SatCripto;
import java.math.BigDecimal;
import java.security.AccessControlException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueueFechController {

    private final Persistencia persistencia;
    private final QueueFechDao queueFechDao;
    private final Lgbr_tsolicitacaoDao lgbrDao;
    private final User_LkDao userLkDAO;
    private final String CHAVE;
    private static final Map<String, Object> FECHADURAS_TIPOS;
    private ArquivoLog logger;
    private String caminho;

    static {
        FECHADURAS_TIPOS = new HashMap<>();
        FECHADURAS_TIPOS.put("1", 1); // CryptoGard
        FECHADURAS_TIPOS.put("2", 3); // SmartGard
        FECHADURAS_TIPOS.put("4", 21); // Lagard Combogard-BR
        FECHADURAS_TIPOS.put("5", 41); // Lagard Dynamic
    }

    public QueueFechController(Persistencia persistencia) {
        this.persistencia = persistencia;
        CHAVE = "Active Solutions SAS Systems";

        queueFechDao = new QueueFechDao(persistencia);
        lgbrDao = new Lgbr_tsolicitacaoDao(persistencia);
        userLkDAO = new User_LkDao(persistencia);
    }

    public QueueFechController(Persistencia persistencia, ArquivoLog logger, String caminho) {
        this.persistencia = persistencia;
        this.logger = logger;
        this.caminho = caminho;
        CHAVE = "Active Solutions SAS Systems";

        queueFechDao = new QueueFechDao(persistencia);
        lgbrDao = new Lgbr_tsolicitacaoDao(persistencia);
        userLkDAO = new User_LkDao(persistencia);
    }

    public QueueFechDTO getQueueFechComPanico(boolean temPermissao, String codFil) throws Exception {
        if (temPermissao) {
            return queueFechDao.getQueueFechComPanico(codFil);
        } else {
            throw new AccessControlException("PermissaoNegada");
        }
    }

    public boolean gravarQueueFech(boolean temPermissao, QueueFechDTO dto, String operador) throws Exception {
        if (temPermissao) {
            QueueFech queueFech = new QueueFech(dto);
            queueFech.setTipoOperacao("2");
            queueFech.setComando_Crt("RECEBIDO");
            queueFech.setOperador(RecortaAteEspaço(operador, 0, 10));
            queueFech.setData(DataAtual.getDataAtual("SQL"));
            queueFech.setHora(DataAtual.getDataAtual("HORA"));

            return queueFechDao.gravarQueueFech(queueFech);
        } else {
            throw new AccessControlException("PermissaoNegada");
        }
    }

    public List<QueueFechSolicitacaoSenhaDTO> getAllQueueFechSolicitacaoSenha(
            boolean temPermissao,
            String codFil,
            String data,
            boolean marcado
    ) throws Exception {
        if (temPermissao) {
            try {
                return queueFechDao.selecionaSolicitacoesSenha(codFil, data, marcado);
            } catch (Exception e) {
                throw new Exception("ErroLerBD: " + e.getMessage());
            }
        } else {
            throw new Exception("PermissaoNegada");
        }
    }

    public boolean rejeitaSolicitacaoSenha(boolean temPermissao, String sequencia) throws Exception {
        if (temPermissao) {
            try {
                return queueFechDao.rejeitarSolicitacaoSenha(sequencia);
            } catch (Exception e) {
                throw new Exception("ErroSalvarBD");
            }
        } else {
            throw new Exception("PermissaoNegada");
        }
    }

    public boolean updateSolicitacaoSenha(QueueFechSolicitacaoSenhaDTO solicitacao, String senhaManual) throws Exception {
        try {
            return queueFechDao.aprovarSolicitacaoSenha(
                    senhaManual == null ? solicitacao.getSenhaUsr1() : senhaManual,
                    solicitacao.getCodFechadura(),
                    solicitacao.getSequencia()
            );
        } catch (Exception e) {
            throw e;
        }
    }

    public void updateFechaduraAtiva(
            QueueFechSolicitacaoSenhaDTO solicitacao,
            String codFil,
            String operador,
            String matricula,
            int tipoOperacaoLG
    ) throws Exception {
        adaptaSenhaPeloTipo(solicitacao);
        String operadorReduzido = RecortaAteEspaço(operador, 0, 10);
        String matriculaInt = matricula.replaceFirst(".0", "");
        String codFechaduraEfetivo = getCodFechaduraEfetivo(solicitacao, codFil);

        if (isAberturaSmartDynamicFechada(solicitacao, codFechaduraEfetivo, tipoOperacaoLG)) {
            String senha = obterSenha(solicitacao);
            lgbrDao.atualizaSolicitacao(
                    tipoOperacaoLG, operador, obterIdResponsavel(solicitacao),
                    senha, codFechaduraEfetivo, persistencia);

            loopGerarSenha(solicitacao, codFechaduraEfetivo, senha, operadorReduzido, matriculaInt);
        }
    }

    public boolean isOperacaoNaoPermitida(QueueFechSolicitacaoSenhaDTO solicitacao) {
        return solicitacao.getSrvOK().equals('E');
    }

    public boolean isOutrasFechaduras(QueueFechSolicitacaoSenhaDTO solicitacao) {
        return solicitacao.getTipoFech().equals("99");
    }

    public void adaptaTipoFech(QueueFechSolicitacaoSenhaDTO solicitacao) {
        DecimalFormat df = new DecimalFormat("##");
        String tipoFech = solicitacao.getTipoFech();
        try {
            solicitacao.setTipoFech(df.format(Integer.parseInt(tipoFech)));
        } catch (NumberFormatException e) {
            solicitacao.setTipoFech("0");
        }
    }

    // Abertura Smart Dynamic - Verifica se fechadura foi fechada
    private boolean isAberturaSmartDynamicFechada(
            QueueFechSolicitacaoSenhaDTO solicitacao,
            String codFechaduraEfetivo,
            int tipoOperacaoLG
    ) throws Exception {
        if (tipoOperacaoLG == 41) {
            return smartDynamic(solicitacao, codFechaduraEfetivo);
        }
        return true;
    }

    private String getCodFechaduraEfetivo(QueueFechSolicitacaoSenhaDTO solicitacao, String codFil) {
        if (solicitacao.getTipoFech().equals("5")) {
            return solicitacao.getMACFechadura();
        }

        try {
            int codFechaduraInt = ((int) Float.parseFloat(solicitacao.getCodFechadura())) % 1000000;
            int codFilInt = ((int) Float.parseFloat(codFil)) % 10000;
            return String.format("%06d%04d", codFechaduraInt, codFilInt);
        } catch (NumberFormatException e) {
            throw new NumberFormatException("ExcecaoCodFechaduraCodFilInteiro");
        }
    }

    public int getTipoOperacaoLG(QueueFechSolicitacaoSenhaDTO solicitacao) {
        Object tipo = FECHADURAS_TIPOS.get(solicitacao.getTipoFech());
        return tipo == null ? -1 : (int) tipo;
    }

    private String obterIdResponsavel(QueueFechSolicitacaoSenhaDTO solicitacao) {
        String empresa = persistencia.getEmpresa();
        if ((empresa.equals("CONFEDERAL") || empresa.equals("TRANSFEDERAL"))
                && !solicitacao.getChaveSrv().equals("VEICULO")) {
            if (solicitacao.getCheque().equals("S") || !solicitacao.getTpVeic().equals("F")) {
                return solicitacao.getMatrChe().replaceFirst(".0", "");
            } else {
                return solicitacao.getMatrVig1().replaceFirst(".0", "");
            }
        } else {
            return solicitacao.getMatrChe().replaceFirst(".0", "");
        }
    }

    private String obterSenha(QueueFechSolicitacaoSenhaDTO solicitacao) {
        String senha;
        String empresa = persistencia.getEmpresa();
        if ((empresa.equals("CONFEDERAL") || empresa.equals("TRANSFEDERAL"))
                && !solicitacao.getChaveSrv().equals("VEICULO")) {
            if (solicitacao.getCheque().equals("S") || !solicitacao.getTpVeic().equals("F")) {
                senha = SatCripto.Criptografar(solicitacao.getPW(), CHAVE);
            } else {
                senha = SatCripto.Criptografar(solicitacao.getPWVig1(), CHAVE);
            }
        } else {
            senha = SatCripto.Criptografar(solicitacao.getPW(), CHAVE);
        }

        try {
            return Integer.toString((int) Float.parseFloat(senha));
        } catch (NumberFormatException e) {
            return senha.equals("") ? "0" : senha;
        }
    }

    private void adaptaSenhaPeloTipo(QueueFechSolicitacaoSenhaDTO solicitacao) {
        if (solicitacao.getTipoFech().equals("5")) {
            solicitacao.setCodFechadura(solicitacao.getMACFechadura());
        }
    }

    private boolean smartDynamic(QueueFechSolicitacaoSenhaDTO solicitacao, String fechadura) throws Exception {
        String senhaRecebida = lgbrDao.pegaDesSenhaRecebeFechamento(new BigDecimal(solicitacao.getSequencia()));
        if (senhaRecebida == null) {
            //Incluído para avisar o Mobile que existe fechamento pendente
            queueFechDao.aprovarSolicitacaoSenha("FECH_PD", fechadura, solicitacao.getSequencia());
            return false;
        }
        return true;
    }

    private void loopGerarSenha(
            QueueFechSolicitacaoSenhaDTO solicitacao,
            String fechadura,
            String senhaAtual,
            String operador,
            String matricula
    ) throws Exception {
        Thread.sleep(1000);

        for (int i = 0; i <= 20; i++) {
            int pendenIndex = 0;
            int naoIndex = 0;
            String result = lgbrDao.pegaDesSenhaNotNull(fechadura).toUpperCase();
            if (result.contains("PENDEN")) {
                pendenIndex = 1;
            }

            if (result.contains("NAO")) {
                pendenIndex = 1;
            }

            if (pendenIndex > 0 && naoIndex == 0) {
                int NAO = 12; // Envia comando de não aceito
                lgbrDao.atualizaSolicitacao(NAO, operador, matricula, senhaAtual, fechadura, persistencia);
            } else if (pendenIndex > 0 && naoIndex > 0) {
                userLkDAO.eliminaBloqueios();
            } else if (result != null) {
                String vSenhaStr = geradorDeSenha(result);
                queueFechDao.aprovarSolicitacaoSenha(vSenhaStr, fechadura, solicitacao.getSequencia());
                logarFechadura(solicitacao, fechadura, vSenhaStr);

                return;
            }

            Thread.sleep(500);
        }

        // se tem permissao
//        if (Autoriza(Usuario, PW, 10109, 9)) { // Fechaduras - Abertura
        queueFechDao.aprovarSolicitacaoSenhaComCancelado(solicitacao.getSequencia());
//        }

    }

    private String geradorDeSenha(String original) {
        String senha = "";
        int counter = 0;

        if (original.charAt(0) == 'P') {
            senha = original;
        } else {
            for (int j = original.length(); j >= 1; j--) {
                counter++;
                senha = original.charAt(j - 1) + senha;
                if (counter == 3) {
                    counter = 0;
                    if (j > 1) {
                        senha = "." + senha;
                    }
                }
            }
        }
        return senha;
    }

    private void logarFechadura(QueueFechSolicitacaoSenhaDTO solicitacao, String fechadura, String senhaAtual) {
        String log = String.format("FechadurasLog: %s, %s, %s, %s, '1', 'Abertura Mobile', '', '', '', '', %s",
                solicitacao.getCodFil(), obterIdResponsavel(solicitacao), fechadura, solicitacao.getCodCli1(), solicitacao.getSequencia());
        logger.Grava(log, caminho);
    }

    public List<QueueFechPessoaDTO> listarCentralAlertaPorDatas(String codFil, LocalDate dataInicio, LocalDate dataFim) throws Exception {
        try {
            return queueFechDao.listarCentralAlertaPorDatas(codFil, dataInicio, dataFim);
        } catch (Exception e) {
            throw e;
        }
    }

    public void updatePathSrv(QueueFechPessoaDTO queue) throws Exception {
        try {
            String sequencia = queue.getQueueFech().getSequencia();
            String pathSrv = queue.getQueueFech().getPathSrv();

            if (pathSrv.length() > 60) {
                throw new Exception("Limite Excedido de 60 caracteres"); // FIXME
            }
            queueFechDao.updatePathSrv(sequencia, pathSrv);
        } catch (Exception e) {
            throw e;
        }
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 *
 * <AUTHOR>
 */
public class FPPeriodos {

    private BigDecimal CodFil;
    private BigDecimal CodMovFP;
    private LocalDate DtInicio;
    private LocalDate DtInicioF;
    private LocalDate DtFinal;
    private LocalDate DtInicioP;
    private LocalDate DtFinalP;
    private LocalDate DtFecha;
    private LocalDate DtFechaPto;
    private short Status;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public FPPeriodos() {

    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the CodMovFP
     */
    public BigDecimal getCodMovFP() {
        return CodMovFP;
    }

    /**
     * @param CodMovFP the CodMovFP to set
     */
    public void setCodMovFP(String CodMovFP) {
        try {
            this.CodMovFP = new BigDecimal(CodMovFP);
        } catch (Exception e) {
            this.CodMovFP = new BigDecimal("0");
        }
    }

    /**
     * @return the DtInicio
     */
    public LocalDate getDtInicio() {
        return DtInicio;
    }

    /**
     * @param DtInicio the DtInicio to set
     */
    public void setDtInicio(LocalDate DtInicio) {
        this.DtInicio = DtInicio;
    }

    public String getDtInicioF() {
        return DtInicioF.format(DateTimeFormatter.ofPattern("MM/yyyy"));
    }

    public void setDtInicioF(LocalDate DtInicioF) {
        this.DtInicioF = DtInicioF;
    }

    /**
     * @return the DtFinal
     */
    public LocalDate getDtFinal() {
        return DtFinal;
    }

    /**
     * @param DtFinal the DtFinal to set
     */
    public void setDtFinal(LocalDate DtFinal) {
        this.DtFinal = DtFinal;
    }

    /**
     * @return the DtInicioP
     */
    public LocalDate getDtInicioP() {
        return DtInicioP;
    }

    /**
     * @param DtInicioP the DtInicioP to set
     */
    public void setDtInicioP(LocalDate DtInicioP) {
        this.DtInicioP = DtInicioP;
    }

    /**
     * @return the DtFinalP
     */
    public LocalDate getDtFinalP() {
        return DtFinalP;
    }

    /**
     * @param DtFinalP the DtFinalP to set
     */
    public void setDtFinalP(LocalDate DtFinalP) {
        this.DtFinalP = DtFinalP;
    }

    /**
     * @return the DtFecha
     */
    public LocalDate getDtFecha() {
        return DtFecha;
    }

    /**
     * @param DtFecha the DtFecha to set
     */
    public void setDtFecha(LocalDate DtFecha) {
        this.DtFecha = DtFecha;
    }

    /**
     * @return the DtFechaPto
     */
    public LocalDate getDtFechaPto() {
        return DtFechaPto;
    }

    /**
     * @param DtFechaPto the DtFechaPto to set
     */
    public void setDtFechaPto(LocalDate DtFechaPto) {
        this.DtFechaPto = DtFechaPto;
    }

    /**
     * @return the Status
     */
    public short getStatus() {
        return Status;
    }

    /**
     * @param Status the Status to set
     */
    public void setStatus(short Status) {
        this.Status = Status;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

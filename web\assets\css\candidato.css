/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 26-Aug-2019, 09:21:10
    Author     : SASWRichard
*/

* { 
    font-family: Tahoma, Verdana, Segoe, sans-serif;
    outline: 0 !important;
}

.cabecalho{
    color: black;
    font-size: 20px;
}

.h-wrapper, .h-wrapper-com-footer {
    position: relative;
    max-width: 714px;
    padding: 15px 15px 15px 15px;
    margin: 0 auto;
    height: auto;
    background: url('../img/st_back.png') no-repeat top center;
    background-size: 62%;
}

.tabelaArquivos .ui-widget-content{
    background: white;
}
.tabelaArquivos td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-selectable{
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-scrollable-header-box{
    background: white;
}
.tabelaArquivos .ui-datatable-data .ui-widget-content{
    border: none !important;
}
.tabelaArquivos .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabelaArquivos .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}

.rodape-wrapper {
    background: #328fca;
    background: -moz-linear-gradient(top,  #328fca 0%, #16244a 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#328fca), color-stop(100%,#16244a));
    background: -webkit-linear-gradient(top,  #328fca 0%,#16244a 100%);
    background: -o-linear-gradient(top,  #328fca 0%,#16244a 100%);
    background: -ms-linear-gradient(top,  #328fca 0%,#16244a 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#328fca', endColorstr='#16244a',GradientType=0 );
    text-align: center;
    border-radius: 5px;
    padding-bottom: 14px;
    margin-bottom: 5px;
}
.rodape-wrapper ul {
    margin: 0 auto;
    padding: 0;
    list-style: none;
    display: table;
}
.rodape-wrapper ul li {
    display: table-cell;
    height: auto;
    width: auto;
    padding: 14px 5px 0 5px;
}
.rodape-wrapper ul li img {
    max-width: 130px;
}

@media all and (max-width: 768px) {
    .h-wrapper, .h-wrapper-com-footer {
        position: relative;
        max-width: 714px;
        padding: 15px 15px 15px 15px;
        margin: 0 auto;
        height: auto;
        background: url('../img/st_back.png') no-repeat top center;
        background-color: #022a48;
        background-size: 62%;
    }
    .h-wrapper {
        background-size: 90%;
    }
    .h-wrapper-com-footer {
        padding: 60px 15px 15px 15px;
    }
    .rodape-wrapper ul li {
        display: block;
        float: left;
        height: auto;
        width: 100%;
        padding: 14px 15px 0 15px;
    }
}
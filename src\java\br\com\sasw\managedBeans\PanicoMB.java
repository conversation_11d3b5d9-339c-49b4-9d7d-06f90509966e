/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans;

import Controller.QueueFech.QueueFechController;
import Dados.Persistencia;
import SasBeansCompostas.QueueFechDTO;
import br.com.sasw.utils.Messages;
import java.io.Serializable;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "panicoMB")
@ViewScoped
public class PanicoMB implements Serializable {

    private final Persistencia persistencia;
    private final QueueFechController queueController;
    private QueueFechDTO panicoDetectado;
    private final String operador, codFil;
    private final boolean temPermissao;

    public PanicoMB() throws Exception {

        FacesContext fc = FacesContext.getCurrentInstance();
        LoginMB login = fc.getApplication().evaluateExpressionGet(fc, "#{login}", LoginMB.class);
        String banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        temPermissao = login.isPermissaoFechadurasAbertura();

        persistencia = login.getPp();
        if (persistencia == null) {
            FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
        }

        queueController = new QueueFechController(persistencia);
    }

    private void displayError(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    public void atualizar() {
        try {
            panicoDetectado = queueController.getQueueFechComPanico(temPermissao, codFil);
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void update() {
        try {
            if (panicoDetectado == null || panicoDetectado.getSequencia() == null) {
                displayError("null");
                return;
            }

            queueController.gravarQueueFech(temPermissao, panicoDetectado, operador);
            panicoDetectado = null;
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public QueueFechDTO getPanicoDetectado() {
        return panicoDetectado;
    }

    public void setPanicoDetectado(QueueFechDTO panicoDetectado) {
        this.panicoDetectado = panicoDetectado;
    }

    public boolean isTemPermissao() {
        return temPermissao;
    }
}

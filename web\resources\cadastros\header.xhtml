<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:composite = "http://java.sun.com/jsf/composite">
    <composite:interface>
        <composite:attribute name="titulo" required="true" />
        <composite:attribute name="data" required="true" />
        <composite:attribute name="descricao" required="true" />
        <composite:attribute name="endereco" required="true" />
        <composite:attribute name="bairro" required="true" />
        <composite:attribute name="cidade" required="true" />
        <composite:attribute name="UF" required="true" />
        <composite:attribute name="imagem" required="false" />
    </composite:interface>

    <composite:implementation>
        <header>
            <h:form id="#{cc.id}">
                <div class="ui-grid ui-grid-responsive">
                    <div class="ui-grid-row cabecalho">
                        <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12">
                            <img src="#{cc.attrs.imagem}" height="40" style="margin-top:-6px !important" />
                            <label class="TituloPagina">#{cc.attrs.titulo}</label>
                            <label class="TituloDataHora">
                                <h:outputText value="#{localemsgs.Data}: "/>
                                <span>
                                    <h:outputText id="dataDia" value="#{cc.attrs.data}" converter="conversorDia"/>
                                </span>
                            </label>
                        </div>

                        <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12">
                            <div style="float:left;">
                                <label class="FilialNome">
                                    #{cc.attrs.descricao}
                                    <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">
                                        #{localemsgs.TrocarFilial}
                                    </label>
                                </label>

                                <label class="FilialEndereco">
                                    #{cc.attrs.endereco}
                                </label>

                                <label class="FilialBairroCidade">
                                    #{cc.attrs.bairro}, #{cc.attrs.cidade}/#{cc.attrs.UF}
                                </label>
                            </div>
                        </div>

                        <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                            <p:commandLink title="#{localemsgs.Voltar}"
                                        onclick="window.history.back();" action="#">
                             <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                         </p:commandLink>
                        </div>
                    </div>
                </div>
            </h:form>
        </header>
    </composite:implementation>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.operacoes;

import Arquivo.ArquivoLog;
import Controller.DashBoard.DashBoard;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "dashboard")
@ViewScoped
public class DashBoardMB implements Serializable {

    private Persistencia persistencia, central;
    private ArquivoLog logerro;
    private String mesTela, anoTela, log,
            dadosGrafico1QdeLabels, dadosGrafico1QdeRotas, dadosGrafico1QdeGuias, dadosGrafico1QdeParadas, dadosGrafico2TotalValores, dadosGrafico1QdeKm,
            topoTotalGuias, topoTotalRotas, topoTotalParadas, topoTotalKm,
            estatisticaRotaLabel, estatisticaRotaValores, estatisticaRotaParadas, estatisticaCoresParadas, estatisticaCoresValores,
            estatisticaTipoServico, estatisticaRamosTipo, estatisticaTipoCliente, estatisticaCores1, estatisticaCores2, estatisticaCores3,
            estatisticaTipoServicoLabel, estatisticaRamosTipoLabel, estatisticaTipoClienteLabel,
            mediaParadas, mediaValor, mediaGuias, mediaRotas, mediaKm, mediaTotalDias,
            estatisticaPorHora;
    private Map filters;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private final String codfil, banco, codcli, operador, caminho;
    private final BigDecimal codpessoa;
    private List<SasBeansCompostas.DashBoard> dadosDashboard;
    private DashBoard dashBoard;

    public DashBoardMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        codcli = (String) fc.getExternalContext().getSessionMap().get("cliente");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        if (null == anoTela
                || anoTela.equals("")) {
            anoTela = DataAtual.getDataAtual("SQL").substring(0, 4);
        }

        if (null == mesTela
                || mesTela.equals("")) {
            mesTela = DataAtual.getDataAtual("TELA").split("/")[1];
        }
        rotassatweb = new RotasSatWeb();
        logerro = new ArquivoLog();
        dashBoard = new DashBoard();
    }

    public void Persistencia(Persistencia pp, Persistencia local) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = local;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.filters = new HashMap();
            this.filters.put(" clientes.codFil = ? ", this.codfil);
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarGraficos() throws Exception {
        CarregarEstatisticaPorDia();
        CarregarEstatisticaPorRota();
        CarregarEstatisticaNova();
    }

    public void CarregarEstatisticaPorDia() throws Exception {
        dashBoard = new DashBoard();
        dadosDashboard = dashBoard.obterEstatisticaPorDia(this.persistencia, this.anoTela, this.mesTela, this.codfil);

        this.dadosGrafico1QdeLabels = "";
        this.dadosGrafico1QdeRotas = "";
        this.dadosGrafico1QdeGuias = "";
        this.dadosGrafico1QdeKm = "";
        this.dadosGrafico1QdeParadas = "";
        this.dadosGrafico2TotalValores = "";

        this.mediaGuias = "0";
        this.mediaKm = "0";
        this.mediaParadas = "0";
        this.mediaRotas = "0";
        this.mediaTotalDias = "0";
        this.mediaValor = "";

        this.topoTotalGuias = "0";
        this.topoTotalKm = "0";
        this.topoTotalParadas = "0";
        this.topoTotalRotas = "0";

        String Labels = "", QdeRotas = "", QdeGuias = "", QdeParadas = "", TotalValores = "", TotalKm = "";
        int SomaRotas = 0, SomaGuias = 0, SomaParadas = 0, SomaRotasTotal = 0;
        double SomaKm = 0.0;
        BigDecimal SomaValores = BigDecimal.ZERO;

        if (dadosDashboard.size() > 0) {
            for (SasBeansCompostas.DashBoard itemDashBoard : dadosDashboard) {
                Labels += !Labels.equals("") ? ",'" + itemDashBoard.getDia() + "'" : "'" + itemDashBoard.getDia() + "'";

                if (Integer.parseInt(itemDashBoard.getQdeRotas()) > SomaRotas) {
                    SomaRotas = Integer.parseInt(itemDashBoard.getQdeRotas());
                }
                SomaGuias += Integer.parseInt(itemDashBoard.getQdeGuias());
                SomaParadas += Integer.parseInt(itemDashBoard.getQdeParadas());
                SomaKm += Double.parseDouble(itemDashBoard.getTotalKmTransportado());
                SomaRotasTotal += Integer.parseInt(itemDashBoard.getQdeRotas());
                SomaValores = itemDashBoard.getTotalValorTransportado().add(SomaValores);

                QdeRotas += !QdeRotas.equals("") ? "," + itemDashBoard.getQdeRotas() : itemDashBoard.getQdeRotas();
                QdeGuias += !QdeGuias.equals("") ? "," + itemDashBoard.getQdeGuias() : itemDashBoard.getQdeGuias();
                QdeParadas += !QdeParadas.equals("") ? "," + itemDashBoard.getQdeParadas() : itemDashBoard.getQdeParadas();
                TotalValores += !TotalValores.equals("") ? "," + itemDashBoard.getTotalValorTransportado() : itemDashBoard.getTotalValorTransportado();
                TotalKm += !TotalKm.equals("") ? "," + itemDashBoard.getTotalKmTransportado() : itemDashBoard.getTotalKmTransportado();
            }

            this.mediaGuias = Integer.toString((SomaGuias / dadosDashboard.size()));
            DecimalFormat resultado = new DecimalFormat("0.00");
            this.mediaKm = resultado.format((SomaKm / dadosDashboard.size()));
            this.mediaParadas = Integer.toString((SomaParadas / dadosDashboard.size()));
            this.mediaRotas = Integer.toString((SomaRotasTotal / dadosDashboard.size()));
            this.mediaTotalDias = Integer.toString(dadosDashboard.size());

            resultado = new DecimalFormat("#,##0.00");

            BigDecimal quantidade = new BigDecimal(dadosDashboard.size());
            BigDecimal ApurarMediaValor = SomaValores.divide(quantidade, 2, RoundingMode.HALF_EVEN);

            this.mediaValor = resultado.format(ApurarMediaValor);

            this.dadosGrafico1QdeLabels = "[" + Labels + "]";
            this.dadosGrafico1QdeRotas = "[" + QdeRotas + "]";
            this.dadosGrafico1QdeGuias = "[" + QdeGuias + "]";
            this.dadosGrafico1QdeKm = "[" + TotalKm + "]";
            this.dadosGrafico1QdeParadas = "[" + QdeParadas + "]";
            this.dadosGrafico2TotalValores = "[" + TotalValores + "]";

            this.topoTotalGuias = Integer.toString(SomaGuias);
            resultado = new DecimalFormat("0.00");
            this.topoTotalKm = resultado.format(SomaKm);
            this.topoTotalParadas = Integer.toString(SomaParadas);
            this.topoTotalRotas = Integer.toString(SomaRotas);
        }
    }

    public void CarregarEstatisticaPorRota() throws Exception {
        dashBoard = new DashBoard();
        dadosDashboard = dashBoard.obterQdesPorRota(this.persistencia, this.anoTela, this.mesTela, this.codfil);

        this.estatisticaRotaLabel = "";
        this.estatisticaRotaParadas = "0";
        this.estatisticaRotaValores = "0";
        this.estatisticaCoresParadas = "";

        String QdeParadas = "", TotalValores = "", Labels = "", Cores = "";
        SasBeansCompostas.DashBoard dashItem = new SasBeansCompostas.DashBoard();

        if (dadosDashboard.size() > 0) {
            for (int i = 0; i < dadosDashboard.size(); i++) {
                dashItem = this.dadosDashboard.get(i);

                Labels += !Labels.equals("") ? ",'" + getMessageS("Rota") + " " + dashItem.getRota() + "'" : "'" + getMessageS("Rota") + " " + dashItem.getRota() + "'";
                QdeParadas += !QdeParadas.equals("") ? "," + dashItem.getQdeParadas() : dashItem.getQdeParadas();
                Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";

                TotalValores += !TotalValores.equals("") ? "," + dashItem.getTotalValorTransportado() : dashItem.getTotalValorTransportado();
            }

            this.estatisticaRotaLabel = "[" + Labels + "]";
            this.estatisticaRotaParadas = "[" + QdeParadas + "]";
            this.estatisticaCoresParadas = "[" + Cores + "]";
            this.estatisticaRotaValores = "[" + TotalValores + "]";
        }
    }

    public void CarregarEstatisticaNova() throws Exception {
        dashBoard = new DashBoard();
        this.estatisticaTipoServicoLabel = "";
        this.estatisticaTipoServico = "0";
        this.estatisticaRamosTipoLabel = "";
        this.estatisticaRamosTipo = "0";
        this.estatisticaTipoClienteLabel = "";
        this.estatisticaTipoCliente = "0";
        this.estatisticaCores1 = "";
        this.estatisticaCores2 = "";
        this.estatisticaCores3 = "";

        // Tipo Servico
        dadosDashboard = dashBoard.obterQdeNovoTipoServico(this.persistencia, this.anoTela, this.mesTela, this.codfil);

        String Labels = "", Cores = "", TotalValores = "";
        SasBeansCompostas.DashBoard dashItem = new SasBeansCompostas.DashBoard();

        if (dadosDashboard.size() > 0) {
            for (int i = 0; i < dadosDashboard.size(); i++) {
                dashItem = this.dadosDashboard.get(i);

                Labels += !Labels.equals("") ? ",'" + getMessageS(dashItem.getRota()) + "'" : "'" + getMessageS(dashItem.getRota()) + "'";
                TotalValores += !TotalValores.equals("") ? "," + dashItem.getTotalValorTransportado() : dashItem.getTotalValorTransportado();
                Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";
            }

            this.estatisticaTipoServicoLabel = "[" + Labels + "]";
            this.estatisticaCores1 = "[" + Cores + "]";
            this.estatisticaTipoServico = "[" + TotalValores + "]";
        }

        // Ramo Atividade
        dadosDashboard = dashBoard.obterQdeNovoRamoAtiv(this.persistencia, this.anoTela, this.mesTela, this.codfil);

        Labels = "";
        Cores = "";
        TotalValores = "";
        dashItem = new SasBeansCompostas.DashBoard();

        if (dadosDashboard.size() > 0) {
            for (int i = 0; i < dadosDashboard.size(); i++) {
                dashItem = this.dadosDashboard.get(i);

                Labels += !Labels.equals("") ? ",'" + dashItem.getRota() + "'" : "'" + dashItem.getRota() + "'";
                TotalValores += !TotalValores.equals("") ? "," + dashItem.getTotalValorTransportado() : dashItem.getTotalValorTransportado();
                Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";
            }

            this.estatisticaRamosTipoLabel = "[" + Labels + "]";
            this.estatisticaCores2 = "[" + Cores + "]";
            this.estatisticaRamosTipo = "[" + TotalValores + "]";
        }

        // Tipo Cliente
        dadosDashboard = dashBoard.obterQdeNovoClienteTipo(this.persistencia, this.anoTela, this.mesTela, this.codfil);

        Labels = "";
        Cores = "";
        TotalValores = "";
        dashItem = new SasBeansCompostas.DashBoard();

        if (dadosDashboard.size() > 0) {
            for (int i = 0; i < dadosDashboard.size(); i++) {
                dashItem = this.dadosDashboard.get(i);

                Labels += !Labels.equals("") ? ",'" + dashItem.getRota() + "'" : "'" + dashItem.getRota() + "'";
                TotalValores += !TotalValores.equals("") ? "," + dashItem.getTotalValorTransportado() : dashItem.getTotalValorTransportado();
                Cores += !Cores.equals("") ? ",\nCoresGrafico[" + Integer.toString(i) + "]" : "CoresGrafico[" + Integer.toString(i) + "]";
            }

            this.estatisticaTipoClienteLabel = "[" + Labels + "]";
            this.estatisticaCores3 = "[" + Cores + "]";
            this.estatisticaTipoCliente = "[" + TotalValores + "]";
        }
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public String getCodfil() {
        return codfil;
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }

    public String getMesTela() {
        return mesTela;
    }

    public void setMesTela(String mesTela) {
        this.mesTela = mesTela;
    }

    public String getAnoTela() {
        return anoTela;
    }

    public void setAnoTela(String anoTela) {
        this.anoTela = anoTela;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public String getDadosGrafico1QdeLabels() {
        return dadosGrafico1QdeLabels;
    }

    public void setDadosGrafico1QdeLabels(String dadosGrafico1QdeLabels) {
        this.dadosGrafico1QdeLabels = dadosGrafico1QdeLabels;
    }

    public String getDadosGrafico1QdeRotas() {
        return dadosGrafico1QdeRotas;
    }

    public void setDadosGrafico1QdeRotas(String dadosGrafico1QdeRotas) {
        this.dadosGrafico1QdeRotas = dadosGrafico1QdeRotas;
    }

    public String getDadosGrafico1QdeGuias() {
        return dadosGrafico1QdeGuias;
    }

    public void setDadosGrafico1QdeGuias(String dadosGrafico1QdeGuias) {
        this.dadosGrafico1QdeGuias = dadosGrafico1QdeGuias;
    }

    public String getDadosGrafico1QdeParadas() {
        return dadosGrafico1QdeParadas;
    }

    public void setDadosGrafico1QdeParadas(String dadosGrafico1QdeParadas) {
        this.dadosGrafico1QdeParadas = dadosGrafico1QdeParadas;
    }

    public String getDadosGrafico2TotalValores() {
        return dadosGrafico2TotalValores;
    }

    public void setDadosGrafico2TotalValores(String dadosGrafico2TotalValores) {
        this.dadosGrafico2TotalValores = dadosGrafico2TotalValores;
    }

    public String getTopoTotalGuias() {
        return topoTotalGuias;
    }

    public void setTopoTotalGuias(String topoTotalGuias) {
        this.topoTotalGuias = topoTotalGuias;
    }

    public String getTopoTotalRotas() {
        return topoTotalRotas;
    }

    public void setTopoTotalRotas(String topoTotalRotas) {
        this.topoTotalRotas = topoTotalRotas;
    }

    public String getTopoTotalParadas() {
        return topoTotalParadas;
    }

    public void setTopoTotalParadas(String topoTotalParadas) {
        this.topoTotalParadas = topoTotalParadas;
    }

    public String getTopoTotalKm() {
        return topoTotalKm;
    }

    public void setTopoTotalKm(String topoTotalKm) {
        this.topoTotalKm = topoTotalKm;
    }

    public String getEstatisticaRotaLabel() {
        return estatisticaRotaLabel;
    }

    public void setEstatisticaRotaLabel(String estatisticaRotaLabel) {
        this.estatisticaRotaLabel = estatisticaRotaLabel;
    }

    public String getEstatisticaRotaValores() {
        return estatisticaRotaValores;
    }

    public void setEstatisticaRotaValores(String estatisticaRotaValores) {
        this.estatisticaRotaValores = estatisticaRotaValores;
    }

    public String getEstatisticaRotaParadas() {
        return estatisticaRotaParadas;
    }

    public void setEstatisticaRotaParadas(String estatisticaRotaParadas) {
        this.estatisticaRotaParadas = estatisticaRotaParadas;
    }

    public String getEstatisticaCoresParadas() {
        return estatisticaCoresParadas;
    }

    public void setEstatisticaCoresParadas(String estatisticaCoresParadas) {
        this.estatisticaCoresParadas = estatisticaCoresParadas;
    }

    public String getEstatisticaCoresValores() {
        return estatisticaCoresValores;
    }

    public void setEstatisticaCoresValores(String estatisticaCoresValores) {
        this.estatisticaCoresValores = estatisticaCoresValores;
    }

    public String getEstatisticaPorHora() {
        return estatisticaPorHora;
    }

    public void setEstatisticaPorHora(String estatisticaPorHora) {
        this.estatisticaPorHora = estatisticaPorHora;
    }

    public String getDadosGrafico1QdeKm() {
        return dadosGrafico1QdeKm;
    }

    public void setDadosGrafico1QdeKm(String dadosGrafico1QdeKm) {
        this.dadosGrafico1QdeKm = dadosGrafico1QdeKm;
    }

    public String getMediaParadas() {
        return mediaParadas;
    }

    public void setMediaParadas(String mediaParadas) {
        this.mediaParadas = mediaParadas;
    }

    public String getMediaGuias() {
        return mediaGuias;
    }

    public void setMediaGuias(String mediaGuias) {
        this.mediaGuias = mediaGuias;
    }

    public String getMediaRotas() {
        return mediaRotas;
    }

    public void setMediaRotas(String mediaRotas) {
        this.mediaRotas = mediaRotas;
    }

    public String getMediaValor() {
        return mediaValor;
    }

    public void setMediaValor(String mediaValor) {
        this.mediaValor = mediaValor;
    }

    public String getMediaKm() {
        return mediaKm;
    }

    public void setMediaKm(String mediaKm) {
        this.mediaKm = mediaKm;
    }

    public String getMediaTotalDias() {
        return mediaTotalDias;
    }

    public void setMediaTotalDias(String mediaTotalDias) {
        this.mediaTotalDias = mediaTotalDias;
    }

    public String getEstatisticaTipoServico() {
        return estatisticaTipoServico;
    }

    public void setEstatisticaTipoServico(String estatisticaTipoServico) {
        this.estatisticaTipoServico = estatisticaTipoServico;
    }

    public String getEstatisticaRamosTipo() {
        return estatisticaRamosTipo;
    }

    public void setEstatisticaRamosTipo(String estatisticaRamosTipo) {
        this.estatisticaRamosTipo = estatisticaRamosTipo;
    }

    public String getEstatisticaTipoCliente() {
        return estatisticaTipoCliente;
    }

    public void setEstatisticaTipoCliente(String estatisticaTipoCliente) {
        this.estatisticaTipoCliente = estatisticaTipoCliente;
    }

    public String getEstatisticaCores1() {
        return estatisticaCores1;
    }

    public void setEstatisticaCores1(String estatisticaCores1) {
        this.estatisticaCores1 = estatisticaCores1;
    }

    public String getEstatisticaCores2() {
        return estatisticaCores2;
    }

    public void setEstatisticaCores2(String estatisticaCores2) {
        this.estatisticaCores2 = estatisticaCores2;
    }

    public String getEstatisticaCores3() {
        return estatisticaCores3;
    }

    public void setEstatisticaCores3(String estatisticaCores3) {
        this.estatisticaCores3 = estatisticaCores3;
    }

    public String getEstatisticaTipoServicoLabel() {
        return estatisticaTipoServicoLabel;
    }

    public void setEstatisticaTipoServicoLabel(String estatisticaTipoServicoLabel) {
        this.estatisticaTipoServicoLabel = estatisticaTipoServicoLabel;
    }

    public String getEstatisticaRamosTipoLabel() {
        return estatisticaRamosTipoLabel;
    }

    public void setEstatisticaRamosTipoLabel(String estatisticaRamosTipoLabel) {
        this.estatisticaRamosTipoLabel = estatisticaRamosTipoLabel;
    }

    public String getEstatisticaTipoClienteLabel() {
        return estatisticaTipoClienteLabel;
    }

    public void setEstatisticaTipoClienteLabel(String estatisticaTipoClienteLabel) {
        this.estatisticaTipoClienteLabel = estatisticaTipoClienteLabel;
    }

}

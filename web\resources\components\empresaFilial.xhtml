<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:composite = "http://java.sun.com/jsf/composite"
      >
    <composite:interface>
        <composite:attribute name="descricao" required="true" />
        <composite:attribute name="endereco" required="true" />
        <composite:attribute name="bairro" required="true" />
        <composite:attribute name="cidade" required="true" />
        <composite:attribute name="UF" required="true" />
    </composite:interface>

    <composite:implementation>
        <div id="#{cc.id}"
             class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;"
             >
            
                <label class="FilialNome">
                    #{cc.attrs.descricao}
                    <label id="btTrocarFilial"
                           onclick="top.location.href = '../param.xhtml'"
                           >
                        #{localemsgs.TrocarFilial}
                    </label>
                </label>

                <label class="FilialEndereco">
                    #{cc.attrs.endereco}
                </label>

                <label class="FilialBairroCidade">
                    #{cc.attrs.bairro}, #{cc.attrs.cidade}/#{cc.attrs.UF}
                </label>
            
        </div>
    </composite:implementation>
</html>

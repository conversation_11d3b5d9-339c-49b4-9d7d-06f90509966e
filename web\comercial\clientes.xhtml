<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/guias.css" rel="stylesheet"  media="print"/>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <script src="../assets/scripts/jquery.qrcode.js" type="text/javascript"></script>
            <script src="../assets/scripts/qrcode.js" type="text/javascript"></script>
            <link type="text/css" href="../assets/css/animate.css" rel="stylesheet"/>

            <style>
                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                .ui-outputlabel{
                    margin-top: 3px !important;
                }

                .ui-panel.ui-widget .ui-panel-titlebar.ui-corner-all{
                    background-color: #FFF !important;
                    border-bottom: thin solid #CCC !important
                }

                .ItemCliente, .ItemClienteSemLat{
                    position: relative;
                    height: 230px;
                }

                .ItemCliente .ui-panel-title{
                    margin-top: 0px !important;
                    font-size: 16pt !important;
                    font-weight: bold !important;
                    color: #3c8dbc !important;
                    margin-left: #{localeController.number.toString() eq '1'? '108': localeController.number.toString() eq '2'? '75': '84'}px !important;
                }

                .ItemClienteSemLat .ui-panel-title{
                    margin-top: 0px !important;
                    font-size: 16pt !important;
                    font-weight: bold !important;
                    color: goldenrod !important;
                    margin-left: #{localeController.number.toString() eq '1'? '108': localeController.number.toString() eq '2'? '75': '84'}px !important;
                }

                .lblNred{
                    position: absolute;
                    margin-top: -40px;
                    margin-left: 10px;
                    padding: 1px 10px 2px 10px;
                    background-color: orangered;
                    color: #FFF;
                    font-weight: 600 !important;
                    border-radius: 30px;
                    font-size: 9pt !important;
                }

                [id*="formPesquisaRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formOrdenacaoRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                .FotoPendente{
                    width:100% !important;
                    min-height:350px !important;
                    height:100% !important;
                    max-height:100% !important;
                    border:3px solid #BBB;
                    background-color:#DDD;
                    border-radius:4px;
                    color:#BBB;
                    cursor:pointer;
                    box-shadow:2px 2px 3px #CCC;
                    background-size: cover;
                    background-position: center;
                    position:relative !important;
                    margin-top: 20px;
                }

                .FotoPendente:not(:first-child){
                    margin-top: 40px !important;
                }

                .Rotate{
                    transition: all 0.7s ease;
                    transform: rotate(90deg);
                }

                .FotoPendente:hover{
                    background-color:#CCC;
                    border:3px solid #AAA;
                    transition:0.3s ease;
                    color:#999 !important;
                }

                .FotoPendente:hover label,
                .FotoPendente:hover i{
                    color:#999 !important;
                }

                .FotoPendente label{
                    font-size: 20pt;
                }

                .FotoPendente i{
                    font-size: 32pt;
                }


                .LightBox{
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.7);
                    position: absolute;
                    z-index: 9999990;
                }

                .FrameBox{
                    height: 100%;
                    background-color: #FFF;
                    width: 95%;
                    max-width: 710px;
                    position: absolute;
                    z-index: 9999991;
                    right: 0px;
                    bottom: 0px;
                    padding: 0px;
                    animation: fadeInRight 0.2s linear;
                }

                .FrameBox .Titulo i{
                    font-size: 18pt !important;
                    margin-right: 10px;
                    font-weight: 500;
                    color: #666;
                }

                .FrameBox .Titulo{
                    width: 100%;
                    font-size: 14pt !important;
                    color: #888;
                    border-bottom: thin solid #BBB;
                    padding: 15px 15px 12px 15px;
                    background-color: #EEE;
                    text-shadow: 1px 1px #FFF;
                }

                #tblListaClientesQrCode{
                    width: 100%;
                }

                #tblListaClientesQrCode label{
                    color: #000;
                    font-weight: 500 !important;
                    font-size: 10pt !important;
                    cursor: pointer;
                    margin-left: 10px;
                }

                #tblListaClientesQrCode tbody tr td{
                    border-bottom: thin solid #DDD;
                    padding-top: 2px !important;
                    padding-bottom: 0px !important;
                    vertical-align: middle;
                    line-height: 25px !important;
                    min-height: 25px !important;
                    background-color: #FFF !important;
                }

                #tblListaClientesQrCode tbody tr td:first-child{
                    width: 18px;
                    text-align: center;
                }

                #tblListaClientesQrCode tbody tr:not([style*="display:none"]):not([style*="display: none"]):nth-child(even) td{
                    background-color: whitesmoke !important;
                }

                .btImprimir{
                    position: absolute;
                    right: 4px;
                    top: 4px;
                }

                .DescricaoQrCode{
                    font-size: 11pt !important;
                    color: #000;
                    margin-top: 12px;
                    font-weight: 600 !important;
                    width: 170px !important;
                    text-align: center !important;
                    white-space: pre;
                    margin-left: -30px !important;
                }

                .FecharJanela{
                    font-size: 10pt !important;
                    color: red;
                    right: 11px;
                    top: 5px;
                    position: absolute;
                    cursor: pointer;
                    width: 22px;
                    height: 22px;
                    background-color: red;
                    color: #FFF;
                    border-radius: 50%;
                    text-align: center;
                    padding-top: 1px;
                }

                .FecharJanela:hover{
                    background-color: transparent;
                    color: red;
                    transition: 0.2s all;
                }

                #NaoHaClientesQrCode{
                    position: absolute;
                    font-size: 14pt !important;
                    font-weight: 500 !important;
                    text-align: center;
                    color: #BBB !important;
                    width: 100%;
                    height: 40px;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    margin: auto;
                }

                .objHidden{
                    display: none !important;
                }

                @media only screen and (max-width: 640px) and (min-width: 10px) {
                    .ItemCliente, .ItemClienteSemLat{
                        height: 540px;
                    }

                    .ui-tabs-header[data-index="7"]{
                        display: none !important;
                    }
                }

                [id*="dlgAdicionarEmail_content"]{
                    min-height: 350px !important;
                    height: 350px !important;
                    max-height: 350px !important;
                }

                .ui-inputtext {
                    width: 100% !important;
                }

                [id*="tabela_paginator_bottom"]{
                    display: block !Important
                }

                .ThumbFoto{
                    height: 110px;
                    width: 110px;
                    margin-top: 10px;
                    margin-right: 10px;
                    margin-left: 10px;
                    float: left;
                    box-shadow: 2px 2px 3px #CCC;
                    border-radius: 50%;
                    background-size: cover;
                    background-position: center center;
                    cursor: pointer;
                    position: relative;
                }

                .FotoPendente input[type="radio"] + label{
                    margin-top: 10px !important;
                    font-size: 9pt !important;
                    margin-left: 10px;
                    color: #000 !important;
                    font-weight: bold !important;
                }

                .TextRadio{
                    margin-top: 10px !important;
                    font-size: 9pt !important;
                    margin-left: 10px;
                    color: #000 !important;
                    font-weight: bold !important;
                }

                .ThumbFoto[ref="novaFoto"] i:not([class*="fa-cog"]):not([class*="fa-check-square-o"]){
                    font-size: 20pt !important;
                }

                .ThumbFoto[ref="novaFoto"]{
                    color: #303030 !important;
                    padding-top: 30px !important;
                    text-align: center !important;
                    background-color: #AAA !important;
                    cursor: pointer;
                }

                .ThumbFoto:not([ref="novaFoto"]) i:not([class*="fa-cog"]):not([class*="fa-check-square-o"]){
                    background-color: red;
                    border-radius: 50%;
                    padding-top: 8px;
                    color: #FFF !important;
                    font-size: 16pt;
                    width: 30px;
                    height: 30px;
                    text-align: center;
                    position: absolute;
                    right: -10px;
                    top: -6px;
                    z-index: 99999;
                    padding: 4px 0px 0px 1px;
                }

                .ThumbFoto a{
                    position: absolute;
                    bottom: -20px;
                }
            </style>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{clientes.Persistencias(login.pp, login.satellite)}" />
                <f:viewAction action="#{clientes.atribuirChavesGoogle(login.googleApiMob, login.googleApiOper)}" />
                <f:viewAction action="#{clientes.carregarClientesQrCode()}" />
            </f:metadata>

            <div class="LightBox" style="display: none"></div>
            <div class="FrameBox" style="display: none">
                <label class="FecharJanela" title="#{localemsgs.Fechar}"><i class="fa fa-times"></i></label>
                <label class="Titulo"><i class="fa fa-qrcode" aria-hidden="true"></i>#{localemsgs.GeradorQrCode}</label>
                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px 10px 6px 10px; height: calc(100% - 67px)">
                    <div class="col-md-12 col-sm-12 col-xs-12 FundoGrid" style="padding: 2px; border: thin solid #CCC;height: calc(100% - 0px); box-shadow: 2px 2px 4px #CCC;">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="background-color: steelblue; color: #FFF; font-size: 12pt; padding: 10px 10px 10px 12px">
                            #{localemsgs.Clientes}
                            <div style="position: absolute; top: 3px; right: 3px; background-color: #FFF; padding: 5px 10px 4px 10px !important; border-radius: 2px;"><input type="checkbox" id="chkQrCodeTodasFiliais" /><label for="chkQrCodeTodasFiliais" style="margin-left: 6px; color: #666 !important; font-size: 10pt !important; cursor: pointer !important;">#{localemsgs.MostrarTodasFiliais}</label></div>
                        </div>    
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px;">
                            <input type="text" id="txtPesquisaClienteQr" class="form-control" style="padding: 8px !important; height: 38px !important;" placeholder="#{localemsgs.Pesquisar}..." />
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoGridClientesQr" style="padding: 4px 8px 8px 8px; height: calc(100% - 100px); overflow-y: auto; overflow-x: auto;">
                            <table id="tblListaClientesQrCode">
                                <h:outputText value="#{clientes.listaClientesQrCode}" escape="false"/>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12 FundoQr" style="display: none; padding: 2px; border: thin solid #CCC; height: calc(60% - 8px); margin-top: 8px; overflow-y: auto; overflow-x: auto; padding-top: 20px !important;box-shadow: 2px 2px 4px #CCC; padding-left: 40px !important; padding-right: 0px !important">
                        <a href="javascript: void(0);" class="btn btn-success btImprimir"><i class="fa fa-print" aria-hidden="true" style="margin-right: 8px;"></i>#{localemsgs.Imprimir}</a>
                    </div>
                </div>
            </div>

            <p:growl id="msgs"/>
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_clientes.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Clientes}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{clientes.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{clientes.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{clientes.filiais.endereco}</label>
                                        <label class="FilialBairroCidade">#{clientes.filiais.bairro}, #{clientes.filiais.cidade}/#{clientes.filiais.UF}</label>
                                    </div>
                                </div>

                                



                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div id="divBotaoCalendario">
                                    <p:commandLink action="#{clientes.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{clientes.dataTela}"
                                                locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{clientes.selecionarData}" update="main cabecalho msgs" />
                                    </p:calendar>

                                    <p:commandLink action="#{clientes.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>


                                </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="p" oncomplete="PF('dlgPesquisaRapida').show();" update="formPesquisaRapida"/>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; overflow-y:auto !important; padding-right:12px !important; border-top-color:#d2d6de !important">
                        <div class="ui-grid-row">
                            <div class="ui-grid-col-12">
                                <p:panel style="display: inline;">
                                    <p:dataGrid  id="tabela" value="#{clientes.allClientes}" paginator="true" rows="100" lazy="true"
                                                 rowsPerPageTemplate="5,10,15,20,25,50,100"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Clientes}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport}
                                                 {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="lista" columns="1"
                                                 emptyMessage="#{localemsgs.SemRegistros}">
                                        <div class="Cliente" ref="#{lista.tpCli eq '4' ? 'contratovencido' : lista.situacao}">

                                            <p:panel header="#{lista.NRed}" class="#{lista.latitude eq null or lista.latitude eq ''? 'ItemCliente ItemClienteSemLat': 'ItemCliente'}" style="#{lista.latitude eq null or lista.latitude eq ''? 'border-top-color: goldenrod !important': 'border-top-color: #3c8dbc !important'};">
                                                <label class="lblNred">
                                                    #{localemsgs.NRed}
                                                </label>

                                                <div class="col-md-2" style="width: 120px; padding-top: 25px; text-align: center; padding-left: 20px !important; display: #{lista.foto ne null and lista.foto ne ''? '': 'none'}">
                                                    <div title="#{localemsgs.CliqueVisualizarFoto}" style="float: left; background-color: #EEE; border: 2px solid #DDD; width: 100px; height: 100px; /*transform: rotate(90deg);*/ border-radius: 50%; background-image: url(#{lista.foto}); background-size: cover; background-position: center center; cursor: pointer;" onclick="AbrirFoto('#{lista.foto}');"></div>    
                                                </div>
                                                <div class="col-md-10" style="width: calc(100% - #{lista.foto ne null and lista.foto ne ''? '120px':'0px'}); padding-left: 0px !important;">

                                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                                 layout="grid" styleClass="ui-panelgrid-blank" style="margin-top: 8px !important">                                                    
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CodFil}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.codFil}">
                                                                    <f:convertNumber pattern="0000"/>
                                                                </h:outputText>
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Codigo}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.codigo}" class="ItemClienteCodeDescricao" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Nome}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.nome}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Situacao}: " />
                                                            </div>
                                                            <div class="gridValor" ref="status">
                                                                <h:outputText value="#{lista.situacao=='A'?localemsgs.Ativo:localemsgs.Inativo}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Endereco}: "/>
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.ende}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Bairro}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.bairro}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Cidade}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.cidade}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.UF}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.estado}" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.CEP}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.CEP}" converter="conversorCEP" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Fone1}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.fone1}"  />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Fone2}: " />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.fone2}" converter="conversorFone" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <div class="gridTitulo">
                                                                <h:outputText value="#{localemsgs.Email}: " style="white-space: nowrap !important" />
                                                            </div>
                                                            <div class="gridValor">
                                                                <h:outputText value="#{lista.email}" />
                                                            </div>
                                                        </p:column>

                                                    </p:panelGrid>
                                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                                        <p:column style="padding:0px !important; margin:0px !important;">
                                                            <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                                <p:commandLink title="#{localemsgs.Editar}" update="msgs formCadastrar:cadastrar"
                                                                               actionListener="#{clientes.buttonAction(lista)}">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                                                </p:commandLink>
                                                            </div>
                                                        </p:column>
                                                        <p:column style="padding:0px !important; margin:0px !important;">
                                                            <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                                <p:commandLink title="#{localemsgs.Guias}" action="#{clientes.BuscarGuias(lista)}" update="formGuias msgs" rendered="#{login.nivel eq '9'}">
                                                                    <p:graphicImage url="../assets/img/icone_satmob_guias_40x40.png" height="40"/>
                                                                </p:commandLink>
                                                            </div>
                                                        </p:column>
                                                        <p:column style="padding:0px !important; margin:0px !important;">
                                                            <div class="BotoesGrid" style="width:100px; text-align: right; padding:0px !important;">
                                                                <p:commandLink title="#{localemsgs.Emails}" action="#{clientes.buscarEmails(lista)}" update="formEmails msgs" rendered="#{login.nivel eq '9'}">
                                                                    <p:graphicImage url="../assets/img/icone_redondo_email.png" height="40"/>
                                                                </p:commandLink>
                                                            </div>
                                                        </p:column>
                                                    </p:panelGrid>

                                                </div>
                                            </p:panel>

                                        </div>
                                    </p:dataGrid>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 5px; bottom: 42px; background: transparent" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.CapturaCliente}"
                                           update="formCapturar:cadastrar" actionListener="#{clientes.preCaptura}">
                                <p:graphicImage url="../assets/img/icone_captura_cliente.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}"
                                           update="formCadastrar:cadastrar" actionListener="#{clientes.preCadastro}">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="display: none; padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           oncomplete="PF('dlgPesquisar').show()" update="formPesquisar">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           oncomplete="PF('dlgPesquisaRapida').show()" update="formPesquisaRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Ordenacao}" process="@this"
                                           oncomplete="PF('dlgOrdenacaoRapida').show()" update="formOrdenacaoRapida">
                                <p:graphicImage url="../assets/img/icone_redondo_ordenar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;display:#{login.transpCacamba?'block':'none'}">
                            <p:commandLink title="#{localemsgs.Containers}" update="msgs" rendered="#{login.transpCacamba}"
                                           actionListener="#{clientes.listarContainers}">
                                <p:graphicImage url="../assets/img/icone_cacamba.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}"
                                           update="msgs main:tabela corporativo" actionListener="#{clientes.LimparFiltros}">

                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <img title="#{localemsgs.GeradorQrCode}" id="btGerarQrCode" src="../assets/img/icone_qr_code.png" height="40" style="cursor: pointer" />
                        </div>

                    </p:panel>

                </h:form>

                <!-- Capturar Cliente -->
                <h:form id="formCapturar">
                    <style>
                        [id*="formCapturar"] > [class*="col-md"]{
                            padding:0px 4px 8px 4px !important;
                        }

                        [id*="formCapturar"] .lblInfo{
                            position:absolute;
                            background-color: #F0F0F0;
                            color:#666;
                            font-weight: bold;
                            text-shadow:1px 1px #FFF;
                            left:6px;
                            width:60px;
                            text-align:center;
                            font-size:10pt !important;
                            padding:6px 3px 5px 3px !important;
                            top:2px;
                            border-radius:3px;
                        }

                        [id*="formCapturar"] .lblInfo i{
                            margin-right: 4px;
                        }
                    </style>
                    <p:dialog widgetVar="dlgCapturarCliente" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCapturarCliente" focus="nome"
                              style="max-height:90% !important;min-width:350px !important; max-width:95% !important;
                              border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: auto !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cadastros.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CapturaCliente}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color:#EEE; padding-right:0px !important">
                            <div class="col-md-12" style="padding: 6px 4px 0px 6px !important; height:71px !important; background-color:#FFF; max-width: 400px !important; margin-bottom:12px !important; margin-left:4px !important; border-radius:4px; border:thin solid #DDD; box-shadow:2px 2px 3px #CCC;">
                                <div class="col-md-12" style="padding-bottom:0px !important; padding-left:4px !important">
                                    <p:outputLabel value="#{localemsgs.LocalizacaoAtual}" />
                                </div>
                                <div class="col-md-12" style="padding:0px !important;">

                                    <div class="col-md-6 col-sm-6 col-xs-6" style="padding:0px 4px 8px 4px !important; max-width:200px !important">
                                        <p:inputText id="txtLatitude" onkeydown="return false" onkeypress="return false" style="width:100% !important;padding-left:70px !important; background-color:#FFF !important;" maxlength="13" value="#{clientes.novoClienteCaptura.latitude}" />
                                        <label class="lblInfo"><i class="fa fa-map-marker"></i>LAT</label>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6" style="padding:0px 4px 8px 4px !important; max-width:200px !important">
                                        <p:inputText id="txtLongitude" onkeydown="return false" onkeypress="return false" style="width:100% !important;padding-left:70px !important; background-color:#FFF !important;" maxlength="13" value="#{clientes.novoClienteCaptura.longitude}" />
                                        <label class="lblInfo"><i class="fa fa-map-marker"></i>LON</label>
                                    </div>

                                </div>
                            </div>
                            <div class="col-md-12 row" style="padding:0px !important; margin:0px !important">

                                <div class="col-md-8 col-sm-8 col-xs-12" style="padding:0px 4px 8px 4px !important;">
                                    <p:outputLabel for="nome" value="#{localemsgs.NomeCliente}" />
                                    <p:inputText id="nome" value="#{clientes.novoClienteCaptura.nome}"
                                                 required="true" label="#{localemsgs.NomeCliente}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NomeCliente}"
                                                 maxlength="60">
                                        <p:watermark for="nome" value="#{localemsgs.NomeCliente}"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-4 col-sm-4 col-xs-12" style="padding:0px 4px 8px 4px !important;">
                                    <p:outputLabel for="nred" value="#{localemsgs.NRed}" />
                                    <p:inputText id="nred" value="#{clientes.novoClienteCaptura.NRed}"
                                                 required="true" label="#{localemsgs.NRed}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NRed}"
                                                 maxlength="20">
                                        <p:watermark for="nred" value="#{localemsgs.NRed}"/>
                                    </p:inputText>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <p:outputLabel for="endere" value="#{localemsgs.Endereco}" />
                                <p:inputText id="endere" value="#{clientes.novoClienteCaptura.ende}"
                                             required="true" label="#{localemsgs.Endereco}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Endereco}"
                                             maxlength="60">
                                    <p:watermark for="endere" value="#{localemsgs.Endereco}"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-4">
                                <p:outputLabel for="bairro" value="#{localemsgs.Bairro}" />
                                <p:inputText id="bairro" value="#{clientes.novoClienteCaptura.bairro}"
                                             required="true" label="#{localemsgs.Bairro}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Bairro}"
                                             maxlength="60">
                                    <p:watermark for="bairro" value="#{localemsgs.Bairro}"/>
                                </p:inputText>
                            </div>

                            <div class="col-md-2 col-sm-2 col-xs-3">
                                <p:outputLabel for="estado" value="#{localemsgs.Estado}"/>
                                <p:inputText id="estado" value="#{clientes.novoClienteCaptura.estado}" style="width: 100%"
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.estado}"
                                             label="#{localemsgs.Estado}" maxlength="2">
                                    <p:watermark for="estado" value="#{localemsgs.Estado}"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-9">
                                <p:outputLabel for="cidade" value="#{localemsgs.Cidade}" />
                                <p:inputText id="cidade" value="#{clientes.novoClienteCaptura.cidade}"
                                             required="true" label="#{localemsgs.Cidade}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}"
                                             maxlength="25">
                                    <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <p:outputLabel for="cep" value="#{localemsgs.CEP}" />
                                <p:inputText id="cep" value="#{clientes.novoClienteCaptura.CEP}"
                                             maxlength="9" style="width: 100%;">
                                    <p:watermark for="cep" value="#{localemsgs.CEP}"/>
                                </p:inputText>
                            </div>

                            <h:inputHidden id="txtDadosFotosCarregadas" value="#{clientes.fotoCarregada}"></h:inputHidden>

                            <div class="col-md-12 col-sm-12 col-xs-12" ref="PaiFotoPendente" style="padding:0px 10px 0px 0px; height: calc(100% - 330px); min-height:200px">
                                <div class="FotoPendente" foto="1">
                                    <div class="TextRadio" style="position: absolute; left: 0px; left: -13px; top: -44px; white-space: nowrap; display: flex"><input type="radio" checked="true" name="optImgPadrao" /><label>#{localemsgs.ImgPadrao}</label></div>
                                    <i class="fa fa-times" style="background-color: red; border-radius: 50%; padding-top: 8px; color: #FFF !important; font-size: 16pt; width: 30px; height: 30px; text-align: center; position: absolute; right: 10px; top: 0px; right: -14px; top: -15px; z-index: 99999; padding: 4px 0px 0px 1px;" title="#{localemsgs.Excluir}"></i>

                                    <table style="width:100%; position:absolute !important; height: 100%;">
                                        <tr>
                                            <td style="text-align:center; vertical-align:middle !important; min-height:100% !important;height:100% !important;max-height:100% !important">
                                                <label style="cursor:pointer;"><i class="fa fa-camera"></i></label>
                                                <label style="cursor:pointer;">#{localemsgs.Foto} 1</label>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right">
                                <p:commandLink id="btSalvarCaptura" action="#{clientes.cadastrarCaptura}"
                                               update=" :main:tabela msgs cadastrar"
                                               class="btn btn-primary" style="min-width:110px !important; margin-top:12px !important; padding-bottom:2px !important;" >
                                    <h:outputLabel><i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}</h:outputLabel>
                                </p:commandLink>
                            </div>

                            <h:inputHidden id="txtWidthArq" value="#{clientes.largutaImagem}"></h:inputHidden>
                            <h:inputHidden id="txtHeightArq" value="#{clientes.alturaImagem}"></h:inputHidden>

                            <p:remoteCommand name="rcExcluirImagem" 
                                             process="@this" 
                                             update="msgs" 
                                             actionListener="#{clientes.excluirImagem()}" />

                            <p:fileUpload id="uploadFotosRelatorio"
                                          fileUploadListener="#{clientes.HandleFileUpload}" onstart="InicioUpload();"
                                          allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|PNG|JPE?G|GIF|BMP)$/"
                                          auto="true" multiple="false"
                                          process="@this"
                                          invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                          dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                          update="msgs" skinSimple="true" previewWidth="10"
                                          style="width:10px; height:10px; display:none;"></p:fileUpload>
                            <script type="text/javascript">
                                // <![CDATA[
                                var _URL = window.URL || window.webkitURL;
                                var img;

                                function Rotacionar(inURL) {
                                    /*img = new Image();
                                     img.src = inURL;
                                     
                                     img.onload = function () {
                                     if (img.height > img.width)
                                     $('.FotoPendente[style*="' + inURL + '"]').addClass('Rotate');
                                     else
                                     $('.FotoPendente[style*="' + inURL + '"]').removeClass('Rotate');
                                     }*/
                                }

                                $(document).ready(function () {
                                    AtribuirValorObj();
                                    AjustarTamanhoTable();
                                })
                                        .on('click', '.FotoPendente:last-child table', function () {
                                            $('[id*="uploadFotosRelatorio_input"]').click();
                                        })
                                        .on('click', '.FotoPendente .fa-times', function () {
                                            rcExcluirImagem([{name: 'foto', value: $(this).parents('.FotoPendente').attr('foto')}]);
                                        })
                                        .on('change', '.FotoPendente input[type="radio"]', function () {
                                            if ($(this).parents('.FotoPendente').find('table').attr('style').indexOf('display:none') > -1 ||
                                                    $(this).parents('.FotoPendente').find('table').attr('style').indexOf('display: none') > -1) {
                                                let ImagensSrc = '';

                                                $.each($('.FotoPendente'), function (index, item) {
                                                    if (ImagensSrc != '') {
                                                        ImagensSrc += ';';
                                                    }

                                                    ImagensSrc += ReplaceAll(ReplaceAll(ReplaceAll($(this).css('background-image'), 'url(', ''), '"', ''), ')', '');
                                                    ImagensSrc += '='
                                                    ImagensSrc += $(this).find('input[type="radio"]').prop('checked') ? 'S' : 'N';

                                                    $(this).attr('foto', index);
                                                });

                                                $('[id$="txtDadosFotosCarregadas"]').val(ImagensSrc);
                                            }
                                        })
                                        .on('click', '.FotoPendente > .TextRadio > label', function () {
                                            $(this).parent('div').find('input[type="radio"]').prop('checked', true).change();
                                        });

                                function TratarFotosCarregadas() {
                                    let ImagensSrc = '';

                                    $.each($('.FotoPendente'), function (index, item) {

                                        if ($(this).find('table').attr('style').indexOf('display:none') == -1 &&
                                                $(this).find('table').attr('style').indexOf('display: none') == -1) {
                                            $(this).remove();
                                        }

                                        if (ImagensSrc != '') {
                                            ImagensSrc += ';';
                                        }

                                        ImagensSrc += ReplaceAll(ReplaceAll(ReplaceAll($(this).css('background-image'), 'url(', ''), '"', ''), ')', '');
                                        ImagensSrc += '='
                                        ImagensSrc += $(this).find('input[type="radio"]').prop('checked') ? 'S' : 'N';

                                        $(this).attr('foto', index);
                                    });

                                    let numeroFotoCod = $('.FotoPendente').length.toString();
                                    let numeroFoto = ($('.FotoPendente').length + 1).toString();
                                    let varChecked = numeroFotoCod != '0' ? '' : 'checked="true"';

                                    $('div[ref="PaiFotoPendente"]').append('<div class="FotoPendente" foto="' + numeroFotoCod + '">' +
                                            '    <div class="TextRadio" style="position: absolute; left: 0px; left: -13px; top: -44px; white-space: nowrap; display: flex"><input type="radio" name="optImgPadrao" ' + varChecked + ' /><label>#{localemsgs.ImgPadrao}</label></div>' +
                                            '    <i class="fa fa-times" style="background-color: red; border-radius: 50%; padding-top: 8px; color: #FFF !important; font-size: 16pt; width: 30px; height: 30px; text-align: center; position: absolute; right: 10px; top: 0px; right: -14px; top: -15px; z-index: 99999; padding: 4px 0px 0px 1px;" title="#{localemsgs.Excluir}"></i>' +
                                            '    <table style="width:100%; position:absolute !important;height: 100%;">' +
                                            '          <tr>' +
                                            '             <td style="text-align:center; vertical-align:middle !important; min-height:100% !important;height:100% !important;max-height:100% !important">' +
                                            '                 <label style="cursor:pointer;"><i class="fa fa-camera"></i></label>' +
                                            '                 <label style="cursor:pointer;">#{localemsgs.Foto} ' + numeroFoto + '</label>' +
                                            '             </td>' +
                                            '         </tr>' +
                                            '     </table>' +
                                            ' </div>');

                                    $('[id$="txtDadosFotosCarregadas"]').val(ImagensSrc);

                                    if ($('.FotoPendente').find('input[type="radio"]:checked').length == 0) {
                                        $('.FotoPendente:first-child').find('input[type="radio"]').prop('checked', true);
                                        TratarFotosCarregadas();
                                    }
                                }

                                function AjustarTamanhoTable() {
                                    $('.FotoPendente').each(function () {
                                        $(this).find('table').height($(this).height() + 'px');
                                        $(this).find('table').css('min-height', ($(this).height() + 'px'));
                                        $(this).find('table').css('height', ($(this).height() + 'px'));
                                        $(this).find('table').css('max-height', ($(this).height() + 'px'));
                                    });
                                }

                                function InicioUpload() {
                                    $('.FotoPendente:last-child ').css('background-image', '').find('table').css('display', '');
                                    $('.FotoPendente:last-child ').find('table i').attr('class', 'fa fa-refresh fa-spin fa-fw');
                                }
                                // ]]>
                            </script>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Cadastrar novo -->
                <h:form id="formCadastrar">
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              style="max-height:98.5% !important;min-width:98.5% !important;max-width:98.5% !important;
                              border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Clientes}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color:#EEE; padding-right:0px !important">
                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: " />
                                <p:selectOneMenu id="codfil" value="#{clientes.filial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains" disabled="#{clientes.flag eq 2}">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>


                                <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: " style="float:right;"  />
                                <p:selectOneMenu id="situacao" value="#{clientes.novoCliente.situacao}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="false">
                                    <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"></f:selectItem>
                                    <f:selectItem itemLabel="#{localemsgs.Inativo}" itemValue="I"></f:selectItem>
                                </p:selectOneMenu>
                                <p:outputLabel for="dtSituacao" value="#{localemsgs.DataSituacao}: " style="float:right;"  />
                                <p:inputMask id="dtSituacao" value="#{clientes.novoCliente.dtSituacao}" mask="99/99/9999" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataSituacao}" style="width: 100%;" converter="conversorData" />

                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-1,ui-grid-col-5"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="tpCli" value="#{localemsgs.TpCli}: "  />
                                <p:selectOneMenu id="tpCli" value="#{clientes.novoCliente.tpCli}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains" disabled="#{clientes.flag eq 2}">
                                    <f:selectItem itemLabel="#{localemsgs.Normal}" itemValue="0"/>
                                    <f:selectItem itemLabel="#{localemsgs.Cofre}" itemValue="4"/>
                                    <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="7"/>
                                    <f:selectItem itemLabel="#{localemsgs.TransportadoraValores}" itemValue="8"/>
                                    <f:selectItem itemLabel="#{localemsgs.ATM}" itemValue="9"/>
                                </p:selectOneMenu>

                                <p:outputLabel for="nred" value="#{localemsgs.NRed}: " style="float:right;" />
                                <p:inputText id="nred" value="#{clientes.novoCliente.NRed}"
                                             required="true" label="#{localemsgs.NRed}" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NRed}"
                                             maxlength="20">
                                    <p:watermark for="nred" value="#{localemsgs.NRed}"/>
                                </p:inputText>
                            </p:panelGrid>
                            <div class="col-md-12" style="margin-top: 10px; padding: 0px !important">
                                <p:remoteCommand name="rc" 
                                                 process="@this,tabs:cep,tabs:CEPCob,tabs:ende,tabs:endeCob,tabs:cidade,tabs:cidadeCob,tabs:endeCob,tabs:UFCob" 
                                                 update="tabs:cep,tabs:CEPCob,tabs:ende,tabs:endeCob,tabs:cidade,tabs:cidadeCob,tabs:endeCob,tabs:UFCob" 
                                                 actionListener="#{clientes.utilizarEndereco()}" />
                                <p:tabView id="tabs" activeIndex="0" dynamic="true" cache="true" orientation="top" style="padding:0px !important; background-color:#EEE;" onTabChange="rc();">                                    
                                    <p:tab id="tabDados" title="#{localemsgs.Identificacao}">
                                        <p:confirmDialog global="true">
                                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                        </p:confirmDialog>
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                                                <p:inputText id="nome" value="#{clientes.novoCliente.nome}"
                                                             label="#{localemsgs.Nome}" style="width: 100%"
                                                             maxlength="60">
                                                    <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                                    <p:ajax event="blur" listener="#{clientes.BuscarCliente}"
                                                            update="formCadastrar:tabs:cep_pesquisa msgs"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="regiao" value="#{localemsgs.Regiao}:"/>

                                                <p:column>
                                                    <p:selectOneMenu id="regiao" value="#{clientes.regiao.regiao}" converter="omnifaces.SelectItemsConverter"
                                                                     styleClass="filial" style="width: calc(100% - 30px); float: left"
                                                                     filter="true" filterMatchMode="contains">
                                                        <f:selectItems value="#{clientes.regioes}" var="regiao" itemValue="#{regiao.regiao}"
                                                                       itemLabel="#{regiao.regiao} - #{regiao.descricao}" noSelectionValue=""/>
                                                    </p:selectOneMenu>
                                                    <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{clientes.preCadastroRegiao}"
                                                                   update="formCadastrar:addRegiao msgs" process="@this">
                                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                                    </p:commandLink>
                                                </p:column>

                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-6"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="cep" value="#{localemsgs.CEP}: "/>
                                                <p:column>
                                                    <p:inputText id="cep" value="#{clientes.novoCliente.CEP}"
                                                                 maxlength="8" style="width: calc(100% - 30px); float: left">
                                                        <p:watermark for="cep" value="#{localemsgs.CEP}"/>
                                                    </p:inputText>

                                                    <p:commandLink title="#{localemsgs.Pesquisar}"
                                                                   partialSubmit="true" process="@this formCadastrar:tabs:cep" id="cep_pesquisa"
                                                                   update="formCadastrar:tabs:cep formCadastrar:tabs:ende formCadastrar:tabs:bairro formCadastrar:tabs:cidade formCadastrar:tabs:estado msgs"
                                                                   actionListener="#{clientes.Endereco}">
                                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                                        <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                                                  draggable="false" closable="true" width="300">
                                                            <div class="form-inline">
                                                                <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                                                <p:spacer height="20px"/>
                                                            </div>
                                                            <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                                             onclick="PF('dlgOk').hide();" />
                                                        </p:dialog>
                                                    </p:commandLink>
                                                </p:column>

                                                <p:outputLabel for="bairro" value="#{localemsgs.Bairro}: "/>
                                                <p:inputText id="bairro" value="#{clientes.novoCliente.bairro}"
                                                             label="#{localemsgs.Bairro}" style="width: 100%"
                                                             maxlength="25">
                                                    <f:validateLength maximum="25"/>
                                                    <p:watermark for="bairro" value="#{localemsgs.Bairro}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="ende" value="#{localemsgs.Endereco}: "/>
                                                <p:inputText id="ende" value="#{clientes.novoCliente.ende}"
                                                             style="width: 100%"
                                                             maxlength="60">
                                                    <p:watermark for="ende" value="#{localemsgs.Endereco}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="cidade" value="#{localemsgs.Cidade}: "/>
                                                <p:autoComplete id="cidade" value="#{clientes.novoCliente.cidade}" styleClass="cidade"
                                                                completeMethod="#{clientes.BuscarCidade}" scrollHeight="200"
                                                                maxlength="25" forceSelection="true" style="width: 100%">
                                                    <p:ajax event="itemSelect" listener="#{clientes.SelecionarCidade}" 
                                                            update="formCadastrar:tabs:cidade formCadastrar:tabs:estado"/>
                                                    <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                                </p:autoComplete>

                                                <p:outputLabel for="estado" value="#{localemsgs.UF}: "/>
                                                <p:inputText id="estado" value="#{clientes.novoCliente.estado}" style="width: 100%"
                                                             label="#{localemsgs.UF}" disabled="true" maxlength="2">
                                                    <p:watermark for="estado" value="#{localemsgs.UF}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="fone1" value="#{localemsgs.Fone1}: "/>
                                                <p:inputMask id="fone1" value="#{clientes.novoCliente.fone1}" style="width: 100%"
                                                             label="#{localemsgs.Fone1}" maxlength="#{localeController.currentLocale.language eq 'PT'?'11':'11'}" mask="#{mascaras.mascaraFone}">
                                                    <p:watermark for="fone1" value="#{localemsgs.Fone1}"/>
                                                </p:inputMask>

                                                <p:outputLabel for="fone2" value="#{localemsgs.Fone2}: "/>
                                                <p:inputMask id="fone2" value="#{clientes.novoCliente.fone2}" style="width: 100%"
                                                             label="#{localemsgs.Fone2}" maxlength="#{localeController.currentLocale.language eq 'PT'?'11':'11'}" mask="#{mascaras.mascaraFone}">
                                                    <p:watermark for="fone2" value="#{localemsgs.Fone2}"/>
                                                </p:inputMask>

                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="atividade" value="#{localemsgs.Atividade}: "/>
                                                <p:column>
                                                    <p:selectOneMenu id="atividade" value="#{clientes.novoCliente.ramoAtiv}" converter="omnifaces.SelectItemsConverter"
                                                                     styleClass="filial" style="width: calc(100% - 30px); float: left"
                                                                     filter="true" filterMatchMode="contains">
                                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="0" noSelectionOption="true"/>
                                                        <f:selectItems value="#{clientes.ramosAtiv}" var="ramosAtiv" itemValue="#{ramosAtiv.codigo}"
                                                                       itemLabel="#{ramosAtiv.codigo} - #{ramosAtiv.descricao}"/>
                                                    </p:selectOneMenu>

                                                    <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{clientes.preCadastroRamosAtiv}"
                                                                   update="formCadastrar:addRamosAtiv msgs" process="@this">
                                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                                    </p:commandLink>
                                                </p:column>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="email" value="#{localemsgs.Email}: "/>
                                                <p:inputText id="email" value="#{clientes.novoCliente.email}" style="width: 100%"
                                                             label="#{localemsgs.Email}" maxlength="80">
                                                    <p:watermark for="email" value="#{localemsgs.Email}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">
                                                <p:outputLabel for="cnpj" value="#{localemsgs.CNPJCPF}: "/>
                                                <p:inputText id="cnpj" value="#{clientes.cpfcnpj}"
                                                             label="#{localemsgs.CNPJCPF}" style="width: 100%">
                                                    <p:watermark for="cnpj" value="#{localemsgs.CNPJCPF}"/>
                                                    <p:ajax event="change" listener="#{clientes.mascaraCNPJCPF}"
                                                            update="formCadastrar:tabs:cnpj msgs"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:#FFF">

                                                <p:outputLabel for="ierg" value="#{localemsgs.IERG}: "/>
                                                <p:inputText id="ierg" value="#{clientes.ierg}"
                                                             label="#{localemsgs.IERG}" style="width: 100%">
                                                    <p:watermark for="ierg" value="#{localemsgs.IERG}"/>
                                                </p:inputText>

                                                <p:outputLabel for="im" value="#{localemsgs.Insc_Munic}: "/>
                                                <p:inputText id="im" value="#{clientes.novoCliente.insc_Munic}"
                                                             label="#{localemsgs.Insc_Munic}" style="width: 100%">
                                                    <p:watermark for="im" value="#{localemsgs.Insc_Munic}"/>
                                                </p:inputText>
                                            </p:panelGrid>
                                        </div>
                                    </p:tab>
                                    <p:tab id="tabFaturamento" title="#{localemsgs.Faturamento}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="endeCob" value="#{localemsgs.Endereco}: "/>
                                                <p:inputText id="endeCob" value="#{clientes.novoCliente.endCob}"
                                                             style="width: 100%" maxlength="60">
                                                    <p:watermark for="endeCob" value="#{localemsgs.Endereco}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="cidadeCob" value="#{localemsgs.Cidade}: "/>
                                                <p:autoComplete id="cidadeCob" value="#{clientes.novoCliente.cidCob}" styleClass="cidade"
                                                                label="#{localemsgs.CidadeCob}" completeMethod="#{clientes.BuscarCidade}"
                                                                scrollHeight="200"
                                                                maxlength="25" forceSelection="true" style="width: 100%">
                                                    <p:ajax event="itemSelect" listener="#{clientes.SelecionarCidadeFat}"
                                                            update="formCadastrar:tabs:cidadeCob formCadastrar:tabs:UFCob"/>
                                                    <p:watermark for="cidadeCob" value="#{localemsgs.Cidade}"/>
                                                </p:autoComplete>

                                                <p:outputLabel for="UFCob" value="#{localemsgs.UF}: "/>
                                                <p:inputText id="UFCob" value="#{clientes.novoCliente.UFCob}" style="width: 100%"
                                                             label="#{localemsgs.UF}" disabled="true" maxlength="2">
                                                    <p:watermark for="UFCob" value="#{localemsgs.UF}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="CEPCob" value="#{localemsgs.CEP}: "/>
                                                <p:inputText id="CEPCob" value="#{clientes.novoCliente.CEPCob}"
                                                             maxlength="8" style="width: 100%">
                                                    <p:watermark for="CEPCob" value="#{localemsgs.CEP}"/>
                                                </p:inputText>

                                                <p:outputLabel for="retencoes" value="#{localemsgs.Retencoes}: "/>
                                                <p:selectOneMenu id="retencoes" value="#{clientes.novoCliente.retencoesFat}"
                                                                 styleClass="filial" style="width: 100%">
                                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="" noSelectionOption="true"/>
                                                    <f:selectItem itemLabel="#{localemsgs.RetemImpostos}" itemValue="S"/>
                                                    <f:selectItem itemLabel="#{localemsgs.NaoFazRetencoes}" itemValue="N"/>
                                                </p:selectOneMenu>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="emailCob" value="#{localemsgs.Email}: "/>
                                                <p:inputText id="emailCob" value="#{clientes.novoCliente.emailCob}"
                                                             style="width: 100%" maxlength="60">
                                                    <p:watermark for="emailCob" value="#{localemsgs.Email}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="Obs" value="#{localemsgs.Obs}: "/>
                                                <p:inputTextarea id="Obs" value="#{clientes.novoCliente.obs}"
                                                                 rows="4" cols="50">
                                                    <p:watermark for="Obs" value="#{localemsgs.Obs}"/>
                                                </p:inputTextarea>
                                            </p:panelGrid>
                                        </div>
                                    </p:tab>
                                    <p:tab id="tabATM" title="#{localemsgs.DadosATM}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="marca" value="#{localemsgs.Marca}: "/>
                                                <p:inputText id="marca" value="#{clientes.novoCliente.marcaATM}"
                                                             style="width: 100%">
                                                    <p:watermark for="marca" value="#{localemsgs.Marca}"/>
                                                </p:inputText>

                                                <p:outputLabel for="limiteColeta" value="#{localemsgs.AgendarColeta}: "/>
                                                <p:inputText id="limiteColeta" value="#{clientes.novoCliente.limiteColeta}"
                                                             style="width: 100%">
                                                    <p:watermark for="limiteColeta" value="#{localemsgs.AgendarColeta}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1,
                                                         ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="retorno" value="#{localemsgs.Retorno}: "/>
                                                <p:inputText id="retorno" value="#{clientes.novoCliente.retorno}"
                                                             style="width: 100%" maxlength="1">
                                                    <p:watermark for="retorno" value="#{localemsgs.Retorno}"/>
                                                </p:inputText>

                                                <p:outputLabel for="envelope" value="#{localemsgs.Envelope}: "/>
                                                <p:inputText id="envelope" value="#{clientes.novoCliente.envelope}"
                                                             style="width: 100%"  maxlength="1">
                                                    <p:watermark for="envelope" value="#{localemsgs.Envelope}"/>
                                                </p:inputText>


                                                <p:outputLabel for="limiteSeguro" value="#{localemsgs.LimiteSeguro}: "/>
                                                <p:inputText id="limiteSeguro" value="#{clientes.novoCliente.limiteSeguro}"
                                                             style="width: 100%">
                                                    <p:watermark for="limiteSeguro" value="#{localemsgs.LimiteSeguro}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-2,
                                                         ui-grid-col-2,ui-grid-col-3" layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="cheque" value="#{localemsgs.Cheque}: "/>
                                                <p:inputText id="cheque" value="#{clientes.novoCliente.cheque}"
                                                             style="width: 100%" maxlength="1">
                                                    <p:watermark for="cheque" value="#{localemsgs.Cheque}"/>
                                                </p:inputText>

                                                <p:outputLabel for="codCofre" value="#{localemsgs.CodCofre}: "/>
                                                <p:inputText id="codCofre" value="#{clientes.novoCliente.codCofre}"
                                                             style="width: 100%">
                                                    <p:watermark for="codCofre" value="#{localemsgs.CodCofre}"/>
                                                </p:inputText>


                                                <p:outputLabel for="patrimonio" value="#{localemsgs.Patrimonio}: "/>
                                                <p:inputText id="patrimonio" value="#{clientes.novoCliente.patrimonio}"
                                                             style="width: 100%">
                                                    <p:watermark for="patrimonio" value="#{localemsgs.Patrimonio}"/>
                                                </p:inputText>
                                            </p:panelGrid>
                                        </div>
                                    </p:tab>
                                    <p:tab id="tabInterface" title="#{localemsgs.Interface}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="interfext" value="#{localemsgs.InterfExt}: " />
                                                <p:inputText id="interfext" value="#{clientes.novoCliente.interfExt}"
                                                             label="#{localemsgs.InterfExt}" maxlength="80" style="width: 100%">
                                                    <p:watermark for="interfext" value="#{localemsgs.InterfExt}"/>
                                                </p:inputText>
                                            </p:panelGrid>

                                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-3,ui-grid-col-2"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="latitude" value="#{localemsgs.Latitude}: " />
                                                <p:inputText id="latitude" value="#{clientes.novoCliente.latitude}"
                                                             label="#{localemsgs.Latitude}" style="width: 100%">
                                                    <p:watermark for="latitude" value="#{localemsgs.Latitude}"/>
                                                </p:inputText>

                                                <p:outputLabel for="longitude" value="#{localemsgs.Longitude}: " />
                                                <p:inputText id="longitude" value="#{clientes.novoCliente.longitude}"
                                                             label="#{localemsgs.Longitude}" style="width: 100%">
                                                    <p:watermark for="longitude" value="#{localemsgs.Longitude}"/>
                                                </p:inputText>

                                                <p:commandLink title="#{localemsgs.Mapa}"
                                                               update="msgs latitude longitude formCadastrar:gmap" action="#{clientes.posicaoCliente}" style="float:left">
                                                    <p:graphicImage url="../assets/img/icone_redondo_mapa.png" height="30"/>
                                                </p:commandLink>
                                            </p:panelGrid>

                                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-3,ui-grid-col-2"
                                                         layout="grid" styleClass="ui-panelgrid-blank">
                                                <p:outputLabel for="codExt" value="#{localemsgs.CodExt}: " />
                                                <p:inputText id="codExt" value="#{clientes.novoCliente.codExt}"
                                                             label="#{localemsgs.CodExt}" maxlength="80" style="width: 100%">
                                                    <p:watermark for="codExt" value="#{localemsgs.CodExt}"/>
                                                </p:inputText>

                                                <p:outputLabel for="codPtoCli" value="#{localemsgs.CodPtoCli}: " />
                                                <p:inputText id="codPtoCli" value="#{clientes.novoCliente.codPtoCli}"
                                                             label="#{localemsgs.CodPtoCli}" maxlength="80" style="width: 100%">
                                                    <p:watermark for="codPtoCli" value="#{localemsgs.CodPtoCli}"/>
                                                </p:inputText>

                                            </p:panelGrid> 

                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px;">
                                                <h:outputText id="dadosFotos" value="#{clientes.clientesImgHtml}" escape="false"/>
                                            </div>

                                            <p:fileUpload id="uploadFotosRelatorio2"
                                                          fileUploadListener="#{clientes.HandleFileUpload2}"
                                                          allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|PNG|JPE?G|GIF|BMP)$/"
                                                          auto="true" multiple="false"
                                                          process="@this"
                                                          invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                                          dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                          update="msgs dadosFotos :main:tabela :msgs" skinSimple="true" previewWidth="10"
                                                          style="width:10px; height:10px; display:none;"></p:fileUpload>

                                            <p:remoteCommand name="rcExcluirImagemBD" 
                                                             process="@this" 
                                                             update="msgs dadosFotos :main:tabela :msgs" 
                                                             actionListener="#{clientes.excluirImagemBD()}" />

                                            <p:remoteCommand name="rcTornarPadrao" 
                                                             process="@this" 
                                                             update="msgs dadosFotos :main:tabela :msgs" 
                                                             actionListener="#{clientes.tornarImagemPadrao()}" />
                                        </div>
                                    </p:tab>
                                    <p:tab id="tabUsuarios" title="#{localemsgs.Usuarios}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="2">
                                                <p:panel style="vertical-align: top; height: 200px">
                                                    <p:dataTable id="usuarios" value="#{clientes.usuarios}" var="usuario" selection="#{clientes.novoUsuario}"
                                                                 selectionMode="single" rowKey="#{usuario.codigo}"  styleClass="tabela" scrollable="true"
                                                                 scrollHeight="200" >
                                                        <p:column headerText="#{localemsgs.Nome}" style="width: 1250px">
                                                            <h:outputText value="#{usuario.nome}"/>
                                                        </p:column>
                                                        <p:column headerText="#{localemsgs.Email}" style="width: 1250px">
                                                            <h:outputText value="#{usuario.email}"/>
                                                        </p:column>
                                                    </p:dataTable>
                                                </p:panel>

                                                <p:panel style="vertical-align: top; height: 200px; text-align: center !important;">
                                                    <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{clientes.preCadastroUsuario}"
                                                                   update="formCadastrarUsuario:addUsuario msgs" process="@this" style="float: left">
                                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                                    </p:commandLink>

                                                    <p:commandLink title="#{localemsgs.Deletar}" update="msgs usuarios"
                                                                   action="#{clientes.apagarUsuario}" process="@this usuarios" style="float: left">
                                                        <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                                    </p:commandLink>
                                                </p:panel>
                                            </p:panelGrid>
                                        </div>
                                    </p:tab>
                                    <p:tab id="tabChavesAcesso" title="#{localemsgs.ChaveAcesso}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="2">
                                                <p:panel style="vertical-align: top; height: 200px">
                                                    <p:dataTable id="gridChaveAcesso" 
                                                                 var="chave" 
                                                                 value="#{clientes.chavesAcesso}" 
                                                                 selection="#{clientes.gtveAcesso}"
                                                                 selectionMode="single"
                                                                 rowKey="#{chave.codPessoa}" 
                                                                 styleClass="tabela" 
                                                                 scrollable="true" 
                                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                                 scrollHeight="200" >
                                                        <p:column headerText="#{localemsgs.Nome}" style="width: 300px" class="text-center" >
                                                            <h:outputText value="#{chave.nomePessoa}" class="text-center" />
                                                        </p:column>
                                                        <p:column headerText="#{localemsgs.ChaveAcesso}" style="width: 150px" class="text-center" >
                                                            <h:outputText value="#{chave.chave}" class="text-center" />
                                                        </p:column>
                                                    </p:dataTable>
                                                </p:panel>

                                                <p:panel style="vertical-align: top; height: 200px; text-align: center !important;">
                                                    <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{clientes.criarChave}"
                                                                   update="formCadastrarChaveAcesso:addChave gridChaveAcesso msgs" process="@this" style="float: left">
                                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                                    </p:commandLink>

                                                    <p:commandLink title="#{localemsgs.Editar}" actionListener="#{clientes.editarChave}" rendered="false"
                                                                   update="formCadastrarChaveAcesso:addChave msgs" process="@this" style="float: left">
                                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                                    </p:commandLink>

                                                    <p:commandLink title="#{localemsgs.Deletar}" update="msgs gridChaveAcesso"
                                                                   action="#{clientes.excluirChave}" process="@this gridChaveAcesso" style="float: left">
                                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmaExclusao}?" icon="ui-icon-alert" />
                                                        <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                                                    </p:commandLink>
                                                </p:panel>
                                            </p:panelGrid>
                                        </div>
                                    </p:tab>
                                    <p:tab id="tabEmails" title="#{localemsgs.Emails}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <p:panelGrid columns="2">
                                                <p:panel style="vertical-align: top; height: 200px; text-align: center !important;">
                                                    <p:dataTable style=" .cadastrar{
                                                                     height: 50vh;
                                                                 }" id="emails" value="#{clientes.buscaEmails}" 
                                                                 var="listaEmail" selection="#{clientes.emailNovo}"
                                                                 selectionMode="single" rowKey="#{listaEmail.email}"  styleClass="tabela" scrollable="true"
                                                                 scrollHeight="200" sortBy="#{listaEmail.nome}">
                                                        <p:column headerText="#{localemsgs.Nome}" style="width: 1250px">
                                                            <h:outputText value="#{listaEmail.nome}"/>
                                                        </p:column>
                                                        <p:column headerText="#{localemsgs.Email}" style="width: 1250px">
                                                            <h:outputText value="#{listaEmail.email}"/>
                                                        </p:column>
                                                    </p:dataTable>
                                                </p:panel>
                                                <p:panel style="vertical-align: top; height: 200px; text-align:center !important">
                                                    <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{clientes.NovoEmail}"
                                                                   update="formCadastrarEmail:addEmail msgs" process="@this">
                                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40" style="float: left"/>
                                                    </p:commandLink>

                                                    <p:commandLink title="#{localemsgs.Deletar}" update="msgs"
                                                                   action="#{clientes.DeletarEmail}" process="@this emails">
                                                        <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40" style="float: left"/>
                                                    </p:commandLink>
                                                </p:panel>
                                            </p:panelGrid>
                                        </div>
                                    </p:tab>

                                    <p:tab id="tabFechaduras" title="#{localemsgs.Fechaduras}">
                                        <div class="col-md-12" style="height:270px !important; overflow:auto; padding:10px 0px 10px 10px !important; background-color:#FFF;">
                                            <div class="col-md-11 col-sm-11 col-xs-11" style="vertical-align: top; height: 200px; text-align: center !important; width: calc(100% - 62px)">
                                                <p:dataTable id="tabelaFechaduras" 
                                                             value="#{clientes.listaFechaduras}" 
                                                             var="fechadurasItens" 
                                                             rowKey="#{fechadurasItens.codigo.toPlainString()}"
                                                             styleClass="tabela" 
                                                             class="DataGrid" 
                                                             resizableColumns="true"
                                                             reflow="true" 
                                                             emptyMessage="#{localemsgs.SemRegistros}"
                                                             selection="#{clientes.fechaduraSelecionada}" 
                                                             selectionMode="single" 
                                                             style="padding:0px !important; margin-top:0px !important; margin-left:0px !important; max-height:188px !important">
                                                    <p:ajax event="rowDblselect" listener="#{clientes.dblFechadura}" update="formFechaduras msgs"/>
                                                    <p:column headerText="#{localemsgs.CodFil}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.codFil}" title="#{fechadurasItens.codFil}" class="text-center" converter="conversor0"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.tipo}" title="#{fechadurasItens.tipo}" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.TipoInstalacao}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.tipoInst}" title="#{fechadurasItens.tipoInst}" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Identif}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.identif}" title="#{fechadurasItens.identif}" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Status}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.status}" title="#{fechadurasItens.status}" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.operador}" title="#{fechadurasItens.operador}" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.dt_Alter}" title="#{fechadurasItens.dt_Alter}" converter="conversorData" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                                        <h:outputText value="#{fechadurasItens.hr_alter}" title="#{fechadurasItens.hr_alter}" converter="conversorHora" class="text-center"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Obs}" class="text-left">
                                                        <h:outputText value="#{fechadurasItens.obs}" title="#{fechadurasItens.obs}" class="text-left"/>
                                                    </p:column>
                                                </p:dataTable>
                                            </div>
                                            <div class="col-md-1 col-sm-1 col-xs-1" style="vertical-align: top; height: 200px; text-align:center !important; width: 62px">
                                                <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{clientes.NovaFechadura}"
                                                               update="formFechaduras tabelaFechaduras msgs" process="@this tabelaFechaduras msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40" style="float: left"/>
                                                </p:commandLink>

                                                <p:commandLink title="#{localemsgs.Editar}" update="formFechaduras tabelaFechaduras msgs"
                                                               actionListener="#{clientes.EditarFechadura}" process="@this tabelaFechaduras msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40" style="float: left"/>
                                                </p:commandLink>

                                                <p:commandLink title="#{localemsgs.Deletar}" update="formFechaduras tabelaFechaduras msgs"
                                                               action="#{clientes.excluirFechadura}" process="@this tabelaFechaduras msgs">
                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40" style="float: left"/>
                                                </p:commandLink>
                                            </div>

                                        </div>
                                    </p:tab>


                                </p:tabView>
                            </div>
                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="text-align:center !important;">

                                <p:commandLink rendered="#{clientes.flag eq 1}" id="cadastro" action="#{clientes.Cadastrar}"
                                               update=" :main:tabela msgs cadastrar"
                                               class="btn btn-primary" style="min-width:110px !important; margin-top:14px !important; float: right; margin-right: 10px" >
                                    <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                                </p:commandLink>

                                <p:commandLink rendered="#{clientes.flag eq 2}" id="edit" action="#{clientes.Editar}"
                                               update=":main:tabela :msgs"
                                               class="btn btn-primary" style="min-width:110px !important; margin-top:14px !important; float: right; margin-right: 10px" >
                                    <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                                </p:commandLink>

                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgAdicionarRegiao" id="dlgAdicionarRegiao" responsive="true" header="#{localemsgs.Regioes}"
                              draggable="false" modal="true" closable="true" dynamic="true"
                              resizable="false" width="400" showEffect="drop" closeOnEscape="false" hideEffect="fade" styleClass="dialogo"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <p:panelGrid columns="2" id="addRegiao" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="novaRegiao" value="#{localemsgs.CodigoRegiao}:"/>
                            <p:inputText id="novaRegiao" value="#{clientes.novaRegiao.regiao}"
                                         style="width: 100%">
                                <p:watermark for="novaRegiao" value="#{localemsgs.CodigoRegiao}"/>
                            </p:inputText>

                            <p:outputLabel for="novaRegiaoDescricao" value="#{localemsgs.Descricao}:"/>
                            <p:inputText id="novaRegiaoDescricao" value="#{clientes.novaRegiao.descricao}"
                                         style="width: 100%">
                                <p:watermark for="novaRegiaoDescricao" value="#{localemsgs.Descricao}"/>
                            </p:inputText>

                            <p:outputLabel for="novaRegiaoArea" value="#{localemsgs.Abrangencia}:"/>
                            <p:inputText id="novaRegiaoArea" value="#{clientes.novaRegiao.area}"
                                         style="width: 100%">
                                <p:watermark for="novaRegiaoArea" value="#{localemsgs.Abrangencia}"/>
                            </p:inputText>

                            <p:outputLabel for="novaRegiaoLocal" value="#{localemsgs.TipoLocalizacao}:"/>
                            <p:selectOneMenu id="novaRegiaoLocal" value="#{clientes.novaRegiao.local}"
                                             styleClass="filial" style="width: 100%">
                                <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="" noSelectionOption="true"/>
                                <f:selectItem itemLabel="#{localemsgs.Urbano}" itemValue="1"/>
                                <f:selectItem itemLabel="#{localemsgs.Interurbano}" itemValue="2"/>
                            </p:selectOneMenu>

                            <p:commandLink  action="#{clientes.cadastrarRegiao}"
                                            process="@this novaRegiao novaRegiaoDescricao novaRegiaoArea novaRegiaoLocal"
                                            update="formCadastrar:tabs:regiao msgs"
                                            style="float: left; top:20px; left: 350px;">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30"/>
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog>

                    <p:dialog widgetVar="dlgAdicionarRamosAtiv" id="dlgAdicionarRamosAtiv" responsive="true" header="#{localemsgs.Atividades}"
                              draggable="false" modal="true" closable="true" dynamic="true"
                              resizable="false" width="400" showEffect="drop" closeOnEscape="false" hideEffect="fade" styleClass="dialogo"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <p:panelGrid columns="2" id="addRamosAtiv" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="novaRamosAtivCodigo" value="#{localemsgs.Codigo}:"/>
                            <p:inputText id="novaRamosAtivCodigo" value="#{clientes.ramoAtiv.codigo}"
                                         style="width: 100%">
                                <p:watermark for="novaRamosAtivCodigo" value="#{localemsgs.Codigo}"/>
                            </p:inputText>

                            <p:outputLabel for="novaRamosAtivDescricao" value="#{localemsgs.Descricao}:"/>
                            <p:inputText id="novaRamosAtivDescricao" value="#{clientes.ramoAtiv.descricao}"
                                         style="width: 100%">
                                <p:watermark for="novaRamosAtivDescricao" value="#{localemsgs.Descricao}"/>
                            </p:inputText>

                            <p:commandLink  action="#{clientes.cadastrarRamosAtiv}"
                                            process="@this novaRamosAtivCodigo novaRamosAtivDescricao"
                                            update="formCadastrar:tabs:atividade msgs"
                                            style="float: left; top:20px; left: 350px;">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30"/>
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog>

                    <p:dialog id="dlgMapaCliente" widgetVar="dlgMapaCliente" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Clientes}" style="color:black" />
                        </f:facet>
                        <p:panel id="pnlMapa" style="width:calc(100vw - 100px);height:calc(100vh - 100px);background-color: transparent" styleClass="cadastrar">
                            <p:gmap id="gmap" center="#{clientes.centroMapa}" zoom="16" type="TERRAIN"
                                    style="height:85vh" model="#{clientes.mapaCliente}" />
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form id="formFechaduras" style="overflow: hidden !important;">
                    <p:dialog widgetVar="dlgFechaduras" id="dlgAdicionarEmail" responsive="true" header="#{localemsgs.Fechaduras}"
                              draggable="false" modal="true" closable="true" dynamic="true"
                              resizable="false" width="800" showEffect="drop" closeOnEscape="false" height="150" hideEffect="fade" styleClass="dialogo"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;overflow: hidden !important; min-height: 400px !important; height: 400px !important; max-height: 400px !important;">

                        <div class="col-md-12" style="height:475px !important; overflow: hidden; padding:0px 0px 10px 0px !important; background-color:#EEE;">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:outputLabel for="tipoFechadura" value="#{localemsgs.TipoPosto}"  />
                                <p:selectOneMenu id="tipoFechadura" value="#{clientes.fechadura.tipo}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoPosto}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains">
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraLagardCrypto}" itemValue="01"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraLagardSmart}" itemValue="02"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraLagardCombo}" itemValue="03"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraLagardCrypto04}" itemValue="04"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraLagardDynamic}" itemValue="05"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraMasHamington}" itemValue="11"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraTamborMecanico}" itemValue="21"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraTamper}" itemValue="22"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraHslPerto}" itemValue="30"/>
                                    <f:selectItem itemLabel="#{localemsgs.FechaduraOutras}" itemValue="99"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:outputLabel for="tipoInstalacao" value="#{localemsgs.TipoInstalacao}"  />
                                <p:selectOneMenu id="tipoInstalacao" value="#{clientes.fechadura.tipoInst}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoInstalacao}"
                                                 styleClass="filial" style="width: 100%"
                                                 filter="true" filterMatchMode="contains">
                                    <f:selectItem itemLabel="#{localemsgs.TipoInstalacaoAtm}" itemValue="01"/>
                                    <f:selectItem itemLabel="#{localemsgs.TipoInstalacaoAtm2}" itemValue="11"/>
                                    <f:selectItem itemLabel="#{localemsgs.TipoInstalacaoAtm21}" itemValue="21"/>
                                    <f:selectItem itemLabel="#{localemsgs.TipoInstalacaoVeiculo}" itemValue="02"/>
                                    <f:selectItem itemLabel="#{localemsgs.TipoInstalacaoVeiculoFechadura}" itemValue="12"/>
                                    <f:selectItem itemLabel="#{localemsgs.TipoInstalacaoVeiculoFechadura22}" itemValue="22"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:outputLabel for="fechIdentificacao" value="#{localemsgs.Identificacao}"  />
                                <p:inputText id="fechIdentificacao" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Identificacao}" value="#{clientes.fechadura.identif}" style="width: 100%" autocomplete="false"></p:inputText>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <p:outputLabel for="fechSenhaManager" value="#{localemsgs.SenhaManager}"  />
                                <p:inputText id="fechSenhaManager" value="#{clientes.fechadura.senhaManager}" style="width: 100%" autocomplete="off" maxlength="10"></p:inputText>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <p:outputLabel for="usuario1" value="#{localemsgs.Usuario} 1"  />
                                <p:inputText id="usuario1" value="#{clientes.fechadura.senhaUsuario1}" style="width: 100%" autocomplete="off" maxlength="10"></p:inputText>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <p:outputLabel for="usuario2" value="#{localemsgs.Usuario} 2"  />
                                <p:inputText id="usuario2" value="#{clientes.fechadura.senhaUsuario2}" style="width: 100%" autocomplete="off" maxlength="10"></p:inputText>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12">
                                <p:outputLabel for="serial" value="#{localemsgs.Serial}"  />
                                <p:inputNumber id="serial" style="width: 100%" decimalPlaces="0" thousandSeparator="" value="#{clientes.fechadura.PK_Fechadura}"></p:inputNumber>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:outputLabel for="fechObservacoes" value="#{localemsgs.Obs}"  />
                                <p:inputTextarea id="fechObservacoes" rows="4" style="width: 100%" value="#{clientes.fechadura.obs}"></p:inputTextarea>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:commandLink id="cadastroFechadura" actionListener="#{clientes.CadastroFechadura}"
                                               update="formCadastrar:tabs:tabelaFechaduras msgs"
                                               class="btn btn-primary" style="min-width:110px !important; margin-top:10px !important; float: right; margin-right: 0px !important" >
                                    <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </div>
                    </p:dialog>
                </h:form>


                <h:form id="formCadastrarEmail" style="overflow: hidden !important;">

                    <p:dialog widgetVar="dlgAdicionarEmail" id="dlgAdicionarEmail" focus="novoEmail" responsive="true" header="#{localemsgs.Emails}"
                              draggable="false" modal="true" closable="true" dynamic="true"
                              resizable="false" width="400" showEffect="drop" closeOnEscape="false" height="150" hideEffect="fade" styleClass="dialogo"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;overflow: hidden !important; min-height: 210px !important; height: 210px !important; max-height: 210px !important;">

                        <p:panelGrid columns="2" id="addEmail" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="overflow: hidden !important;">
                            <p:outputLabel for="novoNome" value="#{localemsgs.Nome}:"/>
                            <p:inputText id="novoNome" value="#{clientes.emailNovo.nome}" 
                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                         style="width: 100%">
                                <p:watermark for="novoNome" value="#{localemsgs.Nome} "/>
                            </p:inputText>

                            <p:outputLabel for="novoEmail" value="#{localemsgs.Email}:"/>
                            <p:inputText id="novoEmail" value="#{clientes.emailNovo.email}"
                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                         style="width: 100%">
                                <p:watermark for="novoEmail" value="#{localemsgs.Email}"/>
                            </p:inputText>
                            <p:hotkey bind="shift+f" oncomplete="PF('dlgAdicionarEmail').hide()"/>


                            <p:commandLink id="cadastroEmail" action="#{clientes.CadastroEmail}"
                                           update="formCadastrar:tabs:emails novoNome novoEmail msgs"
                                           class="btn btn-primary" style="min-width:110px !important; margin-top:10px !important; float: right; margin-right: 0px !important" >
                                <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog>
                </h:form>

                <h:form id="formCadastrarUsuario" style="overflow: hidden !important;">
                    <p:dialog widgetVar="dlgAdicionarUsuario" id="dlgAdicionarUsuario" responsive="true" header="#{localemsgs.Usuarios}"
                              draggable="false" modal="true" closable="true" dynamic="true"
                              resizable="false" width="400" showEffect="drop" closeOnEscape="false" height="150" hideEffect="fade" styleClass="dialogo"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;overflow: hidden !important; min-height: 210px !important; height: 210px !important; max-height: 210px !important;">


                        <p:panelGrid columns="2" id="addUsuario" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="overflow: hidden !important;">
                            <p:outputLabel for="nomeUsuario" value="#{localemsgs.Nome}:"/>
                            <p:autoComplete id="nomeUsuario" value="#{clientes.novoUsuario}" completeMethod="#{clientes.buscarUsuarios}"
                                            label="#{localemsgs.Nome}" forceSelection="true"  styleClass="cidade"
                                            style="width: 100%" minQueryLength="3" scrollHeight="200"
                                            var="usu" itemValue="#{usu}" itemLabel="#{usu.nome}">
                                <p:ajax event="itemSelect" update="emailUsuario"/>
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{clientes.novosUsuarios}" />
                            </p:autoComplete>

                            <p:outputLabel for="emailUsuario" value="#{localemsgs.Email}:"/>
                            <p:inputText id="emailUsuario" value="#{clientes.novoUsuario.email}" readonly="true"
                                         style="width: 100%">
                                <p:watermark for="emailUsuario" value="#{localemsgs.Email}"/>
                            </p:inputText>

                            <p:commandLink action="#{clientes.cadastrarUsuario}"
                                           update="formCadastrar:tabs:usuarios msgs"
                                           class="btn btn-primary" style="min-width:110px !important; margin-top:10px !important; float: right !important; margin-right: 0px !important; right: 0px !important">
                                <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog>
                </h:form>

                <h:form id="formCadastrarChaveAcesso" style="overflow: hidden !important;">
                    <p:dialog widgetVar="dlgAdicionarChave" id="dlgAdicionarChave" responsive="true" header="#{localemsgs.ChaveAcesso}"
                              draggable="false" modal="true" closable="true" dynamic="true"
                              resizable="false" width="400" showEffect="drop" closeOnEscape="false" height="150" hideEffect="fade" styleClass="dialogo"
                              style="border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;
                              padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important;
                              font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;overflow: hidden !important; min-height: 210px !important; height: 210px !important; max-height: 210px !important;">


                        <p:panelGrid id="addChave" columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                     layout="grid" styleClass="ui-panelgrid-blank" style="overflow: hidden !important;">
                            <p:outputLabel for="nomeChave" value="#{localemsgs.Nome}:"/>
                            <p:autoComplete id="nomeChave" value="#{clientes.novoPessoaChave}" completeMethod="#{clientes.buscarUsuariosPessoa}"
                                            label="#{localemsgs.Nome}" forceSelection="true"  styleClass="cidade"
                                            style="width: 100%" minQueryLength="3" scrollHeight="200"
                                            var="usu" itemValue="#{usu}" itemLabel="#{usu.nome}">
                                <p:ajax event="itemSelect" update="addChave"/>
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{clientes.novosUsuarios}" />
                            </p:autoComplete>

                            <p:outputLabel value="#{localemsgs.Senha}:"/>
                            <p:inputText value="#{clientes.novoPessoaChave.pwweb}" style="width: 100%; -webkit-text-security: circle;" class="form-control" disabled="#{clientes.novoPessoaChave.pwweb ne null and clientes.novoPessoaChave.pwweb ne ''? 'disabled':''}" ></p:inputText>

                            <p:commandLink action="#{clientes.cadastrarChave}"
                                           update="formCadastrar:tabs:gridChaveAcesso msgs"
                                           class="btn btn-primary" style="min-width:110px !important; margin-top:10px !important; float: right !important; margin-right: 0px !important; right: 0px !important">
                                <i class="fa fa-save" style="margin-right:8px !important;"></i>#{localemsgs.Salve}
                            </p:commandLink>
                        </p:panelGrid>
                    </p:dialog>
                </h:form>

                <h:form id="lacres">
                    <p:hotkey bind="esc" oncomplete="PF('dlgDetalhes').hide()"/>
                    <p:dialog widgetVar="dlgDetalhes" positionType="absolute" id="dlgDetalhes"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;width:800px; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.DetalhesGuia}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="detalhesGuia" style="width: 440px; background: transparent">
                            <div class="form-inline">
                                <p:outputLabel for="guia" value="#{localemsgs.Guia}:" style="color: black; float: left;position:absolute"/>
                                <p:inputText readonly="true" id="guia" converter="conversor0" style="float: left;left:70px;position:absolute; width: 100px;"
                                             value="#{clientes.guiaSelecionada.guia}"/>

                                <p:outputLabel for="serie" value="#{localemsgs.Serie}:" style="color: black; float: left; left:180px; position:absolute"/>
                                <p:inputText readonly="true" id="serie" value="#{clientes.guiaSelecionada.serie}"
                                             style="float: left; left:220px; position:absolute; width:40px;" />

                                <p:outputLabel for="hora" value="#{localemsgs.Horario}:" style="color: black; float: left; left:270px; position:absolute"/>
                                <p:inputText readonly="true" id="hora" converter="conversorHora" style="float: left;left:325px;position:absolute; width: 60px;"
                                             value="#{clientes.guiaSelecionada.horaChegada}" title="#{clientes.guiaSelecionada.horaChegada}"/>
                                <p:inputText readonly="true" id="hora2" converter="conversorHora" style="float: left;left:390px;position:absolute; width: 60px;"
                                             value="#{clientes.guiaSelecionada.horaSaida}" title="#{clientes.guiaSelecionada.horaSaida}"/>
                            </div>

                            <p:spacer height="35px"/>

                            <div class="form-inline">
                                <p:outputLabel for="origem" value="#{localemsgs.Origem}:"
                                               style="color: black; float: left;position:absolute"/>
                                <p:inputText readonly="true" id="origem" style="float: left;left:70px;position:absolute; width: 190px;"
                                             value="#{clientes.guiaSelecionada.origem}"/>

                                <p:outputLabel for="valor" value="#{localemsgs.Valor}:" style="color: black; float: left; left:270px; position:absolute"/>
                                <p:inputText readonly="true" id="valor" converter="conversormoeda"
                                             style="float: left;left:325px;position:absolute; width: 125px; text-align: right"
                                             value="#{clientes.guiaSelecionada.valor}" title="#{clientes.guiaSelecionada.valor}"/>
                            </div>

                            <p:spacer height="35px"/>

                            <div class="form-inline">
                                <p:outputLabel for="destino" value="#{localemsgs.Destino}:"
                                               style="color: black; float: left;position:absolute"/>
                                <p:inputText readonly="true" value="#{clientes.guiaSelecionada.destino}" id="destino"
                                             style="float: left;left:70px;position:absolute; width: 380px;"/>
                            </div>

                            <p:spacer height="35px"/>

                            <div class="form-inline">
                                <p:dataTable id="tabela" value="#{clientes.lacres}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="lacre" resizableColumns="true" rendered="#{clientes.lacres.size() gt 0}"
                                             scrollable="true" scrollHeight="200"
                                             style="font-size: 12px; float: left" styleClass="tabela" >
                                    <p:column headerText="#{localemsgs.Lacre}" style="width: 77px">
                                        <h:outputText value="#{lacre.lacre}" title="#{lacre.lacre}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Tipo}" style="width: 115px">
                                        <h:outputText value="#{lacre.tipo}" title="#{lacre.tipo}" converter="tradutor"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formGuias" class="form-inline">
                    <p:hotkey bind="esc" oncomplete="PF('dlgGuias').hide()"/>
                    <p:dialog widgetVar="dlgGuias" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Guias}" style="color:#022a48" />
                        </f:facet>
                        <div>
                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                            <h:outputText value="#{localemsgs.Cliente} #{clientes.clienteSelecionado.NRed}" style="font-weight: bold"/>
                        </div>

                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                            <div>
                                <div style="float: left; width: 40%">
                                    <p:outputLabel for="data1" value="#{localemsgs.DataInicial}: "/>
                                    <p:spacer width="2px"/>
                                    <p:inputMask id="data1" value="#{clientes.data1}" mask="99/99/9999" style="width: 90px;"/>
                                </div>
                                <div style="float: left; width: 40%">
                                    <p:outputLabel for="data2" value="#{localemsgs.DataFinal}: "/>
                                    <p:spacer width="2px"/>
                                    <p:inputMask id="data2" value="#{clientes.data2}" mask="99/99/9999" style="width: 90px;"/>

                                    <p:spacer width="5"/>

                                    <p:commandLink title="#{localemsgs.Pesquisar}" action="#{clientes.ListarGuias}" update="tabela msgs">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>
                                </div>
                                <div style="float: left; width: 20%; text-align: right">
                                    <p:commandLink title="#{localemsgs.Editar}" update="lacres msgs"
                                                   actionListener="#{clientes.detalhesGuias}">
                                        <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                    </p:commandLink>
                                    <p:spacer width="5"/>
                                    <p:commandLink title="#{localemsgs.Imprimir}" action="#{clientes.Imprimir}"
                                                   update="impressao msgs">
                                        <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="30"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <p:dataTable id="tabela" value="#{clientes.guias}" var="guiacliente"
                                         resizableColumns="true" selectionMode="single" styleClass="tabela"
                                         selection="#{clientes.guiaSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollable="true" scrollWidth="100%" scrollHeight="200"
                                         style="font-size: 12px; background: white; float: left" rowKey="#{guiacliente.guia} #{guiacliente.serie}">
                                <p:column headerText="#{localemsgs.Data}" style="width: 77px">
                                    <h:outputText value="#{guiacliente.data}" title="#{guiacliente.data}" converter="conversorData"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.DiaSemana}" style="width: 115px">
                                    <h:outputText value="#{guiacliente.dia}" title="#{guiacliente.dia}" converter="tradutor"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hr_Chegada}" style="width: 50px">
                                    <h:outputText value="#{guiacliente.horaChegada}" title="#{guiacliente.horaChegada}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hr_Saida}" style="width: 50px">
                                    <h:outputText value="#{guiacliente.horaSaida}" title="#{guiacliente.horaSaida}"/>
                                </p:column>
                                <p:column style="width: 38px" styleClass="celula-right">
                                    <f:facet name="header">
                                        <h:outputText value="#{localemsgs.TempoEspera}" title="#{localemsgs.TempoEspera}"/>
                                    </f:facet>
                                    <h:outputText value="#{guiacliente.tempo}" title="#{guiacliente.tempo}"
                                                  converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Guia}" style="width: 100px" styleClass="celula-right">
                                    <h:outputText value="#{guiacliente.guia}" title="#{guiacliente.guia}" converter="conversor0"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Serie}" style="width: 38px">
                                    <h:outputText value="#{guiacliente.serie}" title="#{guiacliente.serie}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Operacao}" style="width: 130px">
                                    <h:outputText value="#{guiacliente.operacao}" title="#{guiacliente.operacao}" converter="tradutor"/>
                                </p:column>
                                <p:column style="width: 180px;" headerText="#{localemsgs.NRed}">
                                    <h:outputText value="#{guiacliente.nred}" title="#{guiacliente.nred}"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Valor}" style="width: 115px" styleClass="celula-right">
                                    <h:outputText value="#{guiacliente.valor}"
                                                  title="#{guiacliente.valor}" converter="conversormoeda"/>
                                </p:column>
                                <p:column style="width: 80px;" headerText="#{localemsgs.Volume}" styleClass="celula-right">
                                    <h:outputText value="#{guiacliente.volumes}"
                                                  title="#{guiacliente.volumes}"/>
                                </p:column>
                                <p:column style="width: 180px;" headerText="#{localemsgs.Assinado}">
                                    <h:outputText value="#{guiacliente.assinado}" title="#{guiacliente.assinado}"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formEmails" class="form-inline">
                    <p:hotkey bind="esc" oncomplete="PF('dlgEmails').hide()"/>
                    <p:dialog widgetVar="dlgEmails" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Emails}" style="color:#022a48" />
                        </f:facet>
                        <div>
                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                            <h:outputText value="#{localemsgs.Cliente} #{clientes.clienteSelecionado.NRed}" style="font-weight: bold"/>
                        </div>

                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                            <div>
                                <div style="float: left; width: 40%">
                                    <p:outputLabel for="data1" value="#{localemsgs.DataInicial}: "/>
                                    <p:spacer width="2px"/>
                                    <p:inputMask id="data1" value="#{clientes.data1}" mask="99/99/9999" style="width: 90px;"/>
                                </div>
                                <div style="float: left; width: 40%">
                                    <p:outputLabel for="data2" value="#{localemsgs.DataFinal}: "/>
                                    <p:spacer width="2px"/>
                                    <p:inputMask id="data2" value="#{clientes.data2}" mask="99/99/9999" style="width: 90px;"/>

                                    <p:spacer width="5"/>

                                    <p:commandLink title="#{localemsgs.Pesquisar}" action="#{clientes.listarEmails}" update="tabela msgs">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>
                                </div>
                                <div style="float: left; width: 20%; text-align: right">
                                    <p:commandLink title="#{localemsgs.Imprimir}" action="#{clientes.Imprimir}"
                                                   update="impressao msgs" rendered="false">
                                        <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="30"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <p:dataTable id="tabela" value="#{clientes.emails}" var="email"
                                         resizableColumns="true" selectionMode="single" styleClass="tabela DataGrid"
                                         selection="#{clientes.email}" emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollable="true" scrollWidth="100%" scrollHeight="200"
                                         style="font-size: 12px; background: white; float: left" rowKey="#{email.sequencia}">
                                <p:ajax update="formEmail" event="rowDblselect" listener="#{clientes.abrirEmail}"/>
                                <p:column headerText="#{localemsgs.Data}" style="width: 80px">
                                    <h:outputText value="#{email.dt_Inclusao}" title="#{email.dt_Inclusao}" converter="conversorData" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora}" style="width: 45px">
                                    <h:outputText value="#{email.hr_Inclusao}" title="#{email.hr_Inclusao}" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Assunto}">
                                    <h:outputText value="#{email.assunto}" title="#{email.assunto}" />
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formEmail">
                    <p:hotkey bind="esc" oncomplete="PF('dlgEmail').hide()"/>
                    <p:dialog widgetVar="dlgEmail" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;min-width:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Email}" style="color:#022a48" />

                            <p:commandLink title="#{localemsgs.ReenviarEmail}" action="#{clientes.enviarEmail}"
                                           update="impressao msgs" style="text-align: right; float: right">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ReenviarEmail}?" icon="ui-icon-alert" />
                                <p:graphicImage url="../assets/img/icone_redondo_email.png" height="30"/>
                            </p:commandLink>

                            <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                            </p:confirmDialog>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="de" value="#{localemsgs.DeRemetente}: "  />
                                <p:inputText id="de" value="#{clientes.email.remet_nome}"
                                             style="width: 100%" readonly="true"/>
                                <p:inputText id="deEmail" value="#{clientes.email.remet_email}"
                                             style="width: 100%" readonly="true"/>

                                <p:outputLabel for="para" value="#{localemsgs.Para}: "  />
                                <p:inputText id="para" value="#{clientes.email.dest_nome}"
                                             style="width: 100%"/>
                                <p:inputText id="paraEmai" value="#{clientes.email.dest_email}"
                                             style="width: 100%"/>

                                <p:outputLabel for="assunto" value="#{localemsgs.Assunto}: "  />
                                <p:inputText id="assunto" value="#{clientes.email.assunto}" style="width: 100%" readonly="true"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <h:outputText id="mensagemLbl" value="#{localemsgs.Mensagem}: "/>
                                <p:panel style="height:calc(100vh - 300px) !important; overflow:auto !important;">
                                    <h:outputText id="mensagem" value="#{clientes.email.mensagem}" escape="false"/>
                                </p:panel>
                            </p:panelGrid>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="impressao">
                    <p:hotkey bind="esc" oncomplete="PF('dlgImprimir').hide()"/>
                    <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" styleClass="dialogo"
                              style="max-height:95% !important;width:800px; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <div class="ui-grid-col-10">
                                <img src="../assets/img/icone_satmob_guias_40x40.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Imprimir}"/>
                            </div>
                            <div class="ui-grid-col-2" style="text-align: right">
                                <p:commandLink title="#{localemsgs.Imprimir}">
                                    <p:graphicImage url="../assets/img/icone_redondo_impressao.png" height="40"/>
                                    <p:printer target="guiaimpressa"/>
                                </p:commandLink>
                            </div>
                        </f:facet>

                        <p:panel id="guiaimpressa">
                            <ui:include src="../guia/guiaC.xhtml"/>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisa Rápida de clientes-->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarCliente}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{clientes.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="CODIGO" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.NRed}" itemValue="NRED" />
                                        <f:selectItem itemLabel="#{localemsgs.Agencia}" itemValue="AGENCIA" />
                                        <f:selectItem itemLabel="#{localemsgs.Chave}" itemValue="CHAVE" />
                                        <f:selectItem itemLabel="#{localemsgs.Endereco}" itemValue="ENDERECO" />
                                        <f:selectItem itemLabel="#{localemsgs.Bairro}" itemValue="BAIRRO" />
                                        <f:selectItem itemLabel="#{localemsgs.Regiao}" itemValue="REGIAO" />
                                        <f:selectItem itemLabel="#{localemsgs.SemLatLon}" itemValue="SEMLATLON" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'CODIGO'}" value="#{localemsgs.Codigo}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'NOME'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'NRED'}" value="#{localemsgs.NRed}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'AGENCIA'}" value="#{localemsgs.Agencia}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'CHAVE'}" value="#{localemsgs.Chave}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'ENDERECO'}" value="#{localemsgs.Endereco}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'BAIRRO'}" value="#{localemsgs.Bairro}: "/>
                                        <p:outputLabel for="opcao" rendered="#{clientes.chavePesquisa eq 'REGIAO'}" value="#{localemsgs.Regiao}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{clientes.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right: 0px !important">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{clientes.pesquisarUnico()}"
                                               update=" :main:tabela :msgs"
                                               oncomplete="PF('dlgPesquisaRapida').hide()"
                                               title="#{localemsgs.Pesquisar}" styleClass="btn btn-primary">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    // <![CDATA[
                    var GridClientesBase = '#{clientes.listaClientesQrCode}';

                    function RemoverOcultos() {
                        setTimeout(function () {
                            $('.objHidden').remove();
                        }, 1000);
                    }

                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    })
                            .on('change', '#chkQrCodeTodasFiliais', function () {
                                let HTML = '<table id="tblListaClientesQrCode">';
                                HTML += GridClientesBase;
                                HTML += '</table>';

                                $('.FundoGridClientesQr').html(HTML);

                                if (!$(this).prop('checked'))
                                    $('.FundoGridClientesQr').find('.objHidden').remove();
                                else
                                    $('.FundoGridClientesQr tbody tr').removeClass('objHidden');

                                if ($('#txtPesquisaClienteQr').val().trim() != '')
                                    $('#txtPesquisaClienteQr').keyup();
                            })
                            .on('click', '#btGerarQrCode', function () {
                                $('.LightBox, .FrameBox').css('display', '');
                            })
                            .on('click', '.LightBox, .FecharJanela', function () {
                                $('.LightBox, .FrameBox').css('display', 'none');
                            })
                            .on('click', '.btImprimir', function () {
                                $('.FundoQr a').remove();
                                $('.FundoQr').find('[tipo="qrCode"]').css('margin-left', '15px').css('margin-right', '15px');
                                Imprimir($('.FundoQr'));
                                $('.FundoQr').prepend('<a href="javascript: void(0);" class="btn btn-success btImprimir"><i class="fa fa-print" aria-hidden="true" style="margin-right: 8px;"></i>#{localemsgs.Imprimir}</a>');
                                $('.FundoQr').find('[tipo="qrCode"]').css('margin-left', '0px').css('margin-right', '0px');
                            })
                            .on('keyup', '#txtPesquisaClienteQr', function () {
                                if ($(this).val().length >= 2) {
                                    var value = $(this).val().toLowerCase();
                                    $("#tblListaClientesQrCode tr").filter(function () {
                                        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                                    });
                                } else
                                    $('#tblListaClientesQrCode tbody tr').css('display', '');
                            })
                            .on('change', '#tblListaClientesQrCode tbody tr td input[type="checkbox"]', function () {
                                // Gerar/Apagar QR Code do Cliente
                                let DescricaoQrCode = $(this).parent('td').parent('tr').find('td:nth-child(2)').text();
                                let Codigo = $(this).parent('td').parent('tr').attr('Codigo');
                                let CodigoPt = $(this).parent('td').parent('tr').attr('CodPtoCli');
                                $('div[tipo="qrCode"][Codigo="' + Codigo + '"]').remove();

                                if ($(this).prop('checked')) {
                                    $('.FundoQr').append('<div style="margin-top: 40px; width: 198px !important; height: 198px !important; float: left; text-align: center !important; margin-right: 15px;" tipo="qrCode" Codigo="' + Codigo + '"><div id="qrCode_' + Codigo + '"></div><center><label class="DescricaoQrCode" style="font-family: Arial !important; font-size: 8pt !important;color: #000;margin-top: 12px !important;font-weight: 600 !important;text-align: center !important; white-space: pre !important;">' + DescricaoQrCode + '</label></center></div>');

                                    let IdObj = '#qrCode_' + Codigo;

                                    jQuery(IdObj).qrcode({
                                        render: "table",
                                        text: CodigoPt,
                                        width: 170,
                                        height: 170
                                    });
                                }

                                // Mostrar/Ocultar Box de QR Code's
                                if ($('#tblListaClientesQrCode tbody tr td input[type="checkbox"]:checked').length > 0) {
                                    $('.FundoQr').css('display', '');
                                    $('.FundoGrid').css('height', 'calc(40% - 0px)');
                                } else {
                                    $('.FundoQr').css('display', 'none');
                                    $('.FundoGrid').css('height', 'calc(100% - 0px)');
                                }
                            })
                            ;
                    // ]]>
                </script>

                <!--Ordenação Rápida de clientes-->
                <h:form id="formOrdenacaoRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgOrdenacaoRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgOrdenacaoRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Ordenacao}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelOrdenacaoRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoesOrdenacao" value="#{localemsgs.OrdenarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoesOrdenacao"
                                        value="#{clientes.chaveOrdem}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="CODIGO" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.NRed}" itemValue="NRED" />
                                        <f:selectItem itemLabel="#{localemsgs.Agencia}" itemValue="AGENCIA" />
                                        <f:selectItem itemLabel="#{localemsgs.Chave}" itemValue="CHAVE" />
                                        <f:selectItem itemLabel="#{localemsgs.Endereco}" itemValue="ENDERECO" />
                                        <f:selectItem itemLabel="#{localemsgs.Bairro}" itemValue="BAIRRO" />
                                        <f:selectItem itemLabel="#{localemsgs.Regiao}" itemValue="REGIAO" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right: 0px !important">
                                <p:commandLink id="botaoOrdenacaoRapida"
                                               action="#{clientes.ordenarGride()}"
                                               process="@this,radioOpcoesOrdenacao"
                                               update=" :main:tabela :msgs"
                                               oncomplete="PF('dlgOrdenacaoRapida').hide()"
                                               title="#{localemsgs.Pesquisar}" styleClass="btn btn-primary">
                                    <i class="fa fa-sort" style="margin-right:8px !important"></i>#{localemsgs.Ordenar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelOrdenacaoRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoOrdenacaoRapida').click();
                        }
                    });
                </script>

                <!--Pesquisar clientes-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">

                        <f:facet name="header">
                            <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarCliente}" style="color:#022a48" />

                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{clientes.novoCliente.codFil}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codigo" value="#{localemsgs.Codigo}"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="codigo" value="#{clientes.novoCliente.codigo}" label="#{localemsgs.Codigo}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="codigo" value="#{localemsgs.Codigo}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome" value="#{localemsgs.Nome}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{clientes.novoCliente.nome}" label="#{localemsgs.Nome}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nred" value="#{localemsgs.NRed}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nred" value="#{clientes.novoCliente.NRed}" label="#{localemsgs.NRed}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nred" value="#{localemsgs.NRed}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{clientes.PesquisaPaginada}" oncomplete="PF('dlgPesquisar').hide()"
                                               update=" :main:tabela :msgs corporativo"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:hotkey bind="1" oncomplete="PF('dlgPesquisar').hide()"/>
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                          style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" />
                    </f:facet>
                    <h:form class="form-inline">
                        <p:hotkey bind="esc" oncomplete="PF('dlgExportar').hide()"/>
                        <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                        <p:separator />
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="filial" value="#{clientes.eFilial}">
                                    <p:ajax update="labelFilial"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{clientes.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="codigo" value="#{clientes.eCodigo}">
                                    <p:ajax update="labelCodigo"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCodigo" value="#{localemsgs.Codigo}" style="#{clientes.eCodigo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nome" value="#{clientes.eNome}">
                                    <p:ajax update="labelNome"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{clientes.eNome eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="nred" value="#{clientes.eNRed}">
                                    <p:ajax update="labelNRed"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelNRed" value="#{localemsgs.NRed}" style="#{clientes.eNRed eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="email" value="#{clientes.eEmail}">
                                    <p:ajax update="labelEmail"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelEmail" value="#{localemsgs.Email}" style="#{clientes.eEmail eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cnpj" value="#{clientes.eCNPJ}">
                                    <p:ajax update="labelCNPJ"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCNPJ" value="#{localemsgs.CGC}" style="#{clientes.eCNPJ ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="inscmun" value="#{clientes.eInscMun}">
                                    <p:ajax update="labelInscMun"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelInscMun" value="#{localemsgs.Insc_Munic}" style="#{clientes.eInscMun eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="inscest" value="#{clientes.eInscEst}">
                                    <p:ajax update="labelInscEst"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelInscEst" value="#{localemsgs.InscEstadual}" style="#{clientes.eInscEst eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cpf" value="#{clientes.eCPF}">
                                    <p:ajax update="labelCPF"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCPF" value="#{localemsgs.CPF}" style="#{clientes.eCPF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="rg" value="#{clientes.eRG}">
                                    <p:ajax update="labelRG"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelRG" value="#{localemsgs.RG}" style="#{clientes.eRG ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="fon1" value="#{clientes.eFone1}">
                                    <p:ajax update="labelFon1"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFon1" value="#{localemsgs.Fone1}" style="#{clientes.eFone1 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="fon2" value="#{clientes.eFone2}">
                                    <p:ajax update="labelFon2"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelFon2" value="#{localemsgs.Fone2}" style="#{clientes.eFone2 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="Ende" value="#{clientes.eEnde}">
                                    <p:ajax update="eEnde"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eEnde" value="#{localemsgs.Ende}" style="#{clientes.eEnde eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="bairro" value="#{clientes.eBairro}">
                                    <p:ajax event="change" update="labelBairro"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelBairro"  value="#{localemsgs.Bairro}" style="#{clientes.eBairro eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cidade" value="#{clientes.eCidade}">
                                    <p:ajax update="labelCidade"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCidade" value="#{localemsgs.Cidade}" style="#{clientes.eCidade eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="uf" value="#{clientes.eUF}">
                                    <p:ajax update="labelUF"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelUF" value="#{localemsgs.UF}" style="#{clientes.eUF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="cep" value="#{clientes.eCEP}">
                                    <p:ajax update="labelCEP"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelCEP" value="#{localemsgs.CEP}" style="#{clientes.eCEP eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="lat" value="#{clientes.eLat}">
                                    <p:ajax update="labelLat"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelLat" value="#{localemsgs.Latitude}" style="#{clientes.eLat ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="lon" value="#{clientes.eLon}">
                                    <p:ajax update="labelLon"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelLon" value="#{localemsgs.Longitude}" style="#{clientes.eLon eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="InterfExt" value="#{clientes.eInterfExt}">
                                    <p:ajax update="eInterfExt"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="eInterfExt" value="#{localemsgs.InterfExt}" style="#{clientes.eInterfExt eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <div class="ui-grid-row" style="padding-bottom: 3px;">
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="hralter" value="#{clientes.eHrAlter}">
                                    <p:ajax update="labelHrAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{clientes.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="dtalter" value="#{clientes.eDtAlter}">
                                    <p:ajax update="labelDtAlter"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{clientes.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                            <div style="width: 5%; float: left;">
                                <p:selectBooleanCheckbox id="operador" value="#{clientes.eOperador}">
                                    <p:ajax update="labelOperador"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="width: 20%; float: left;">
                                <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{clientes.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                            </div>
                        </div>
                        <p:separator />
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:panel style="text-align: center">
                                <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf" actionListener="#{clientes.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_pdf.png"/>
                                    <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Clientes}"
                                                    preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center">
                                <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xlsx" actionListener="#{clientes.AtualizaTabela}">
                                    <p:graphicImage url="../assets/img/icone_xls.png"/>
                                    <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Clientes}"/>
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>
                    </h:form>
                </p:dialog>

                <h:form id="formContainers">
                    <p:dialog widgetVar="dlgContainers" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_cacamba.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Containers}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelContainers" style="background-color: transparent" styleClass="cadastrar2">

                            <div class="form-inline">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="dataContainer1" value="#{localemsgs.DtInicio}:" indicateRequired="false"/>
                                    <p:inputMask id="dataContainer1" value="#{clientes.dataContainer1}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtInicio}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="#{mascaras.padraoData}"
                                                 converter="conversorData"/>

                                    <p:outputLabel for="dataContainer2" value="#{localemsgs.DtFim}:" indicateRequired="false"/>
                                    <p:inputMask id="dataContainer2" value="#{clientes.dataContainer2}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtFim}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="#{mascaras.padraoData}"
                                                 converter="conversorData"/>
                                </p:panelGrid>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{clientes.listarMovimentacaoContainers}"
                                               title="#{localemsgs.Selecionar}" update="msgs">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>

                    <p:dialog widgetVar="dlgMovimentacaoContainer" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="  background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_cacamba.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.MovimentacaoContainer}" style="color:black" />
                        </f:facet>
                        <p:panel id="panelHistoricoContainers" style="background-color: transparent" styleClass="cadastrar">
                            <div class="form-inline">
                                <p:dataTable id="tabelaMovimentacaoContainer" value="#{clientes.historicoMovimentacao}"
                                             style="font-size: 12px" var="historicoMovimentacao"
                                             styleClass="tabela" scrollWidth="100%"
                                             resizableColumns="true" scrollable="true" scrollHeight="400" >
                                    <f:facet name="header">
                                        <h:outputText value="#{localemsgs.Cliente}: #{clientes.clienteSelecionado.NRed}. "/>
                                        <h:outputText value="#{clientes.dataContainer1}" converter="conversorData"/>
                                        <h:outputText value=" - "/>
                                        <h:outputText value="#{clientes.dataContainer2}" converter="conversorData"/>
                                    </f:facet>
                                    <p:column headerText="#{localemsgs.ER}" style="width: 25px">
                                        <h:outputText value="#{historicoMovimentacao.tpServico}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.CodFil}" style="width: 70px">
                                        <h:outputText value="#{historicoMovimentacao.codFil}" converter="conversorCodFil"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Container}" style="width: 50px; font-weight: bold" styleClass="celula-right">
                                        <h:outputText value="#{historicoMovimentacao.container}" converter="conversorCodFil"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                        <h:outputText value="#{historicoMovimentacao.data}" converter="conversorData"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Rota}" style="width: 50px">
                                        <h:outputText value="#{historicoMovimentacao.rota}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.HrServico}" style="width: 65px">
                                        <h:outputText value="#{historicoMovimentacao.hrServico}" converter="conversorHora"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Nome}" style="width: 200px">
                                        <h:outputText value="#{historicoMovimentacao.nome}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.NRed}" style="width: 150px">
                                        <h:outputText value="#{historicoMovimentacao.nred}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Ende}" style="width: 280px">
                                        <h:outputText value="#{historicoMovimentacao.ende}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Bairro}" style="width: 150px">
                                        <h:outputText value="#{historicoMovimentacao.bairro}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cidade}" style="width: 125px">
                                        <h:outputText value="#{historicoMovimentacao.cidade}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Estado}" style="width: 55px">
                                        <h:outputText value="#{historicoMovimentacao.estado}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Motorista}" style="width: 200px">
                                        <h:outputText value="#{historicoMovimentacao.motorista}" style="white-space: pre-line;"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Placa}" style="width: 90px">
                                        <h:outputText value="#{historicoMovimentacao.placa}"/>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{clientes.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela corporativo" listener="#{clientes.MostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.SomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{clientes.somenteAtivos}">
                                    <p:ajax update="msgs main:tabela corporativo" listener="#{clientes.mostrarSomenteAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important; margin-top:5px !important;">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                // <![CDATA[
                var options = {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                };

                var LatitudeLogon;
                var LongitudeLogon;

                function Resize() {
                    let refCalc = 0;

                    if (ObterParamURL('selecao') &&
                            ObterParamURL('selecao') === 'S')
                        refCalc = 31;
                    else if ($(document).width() <= 700)
                        refCalc = 200;
                    else
                        refCalc = 120;

                    if ($(document).width() <= 700)
                        $('.FundoPagina').css('max-height', ($('body').height() - refCalc) + 'px');
                    else
                        $('.FundoPagina').css('max-height', ($('body').height() - refCalc) + 'px');
                }

                $(document).ready(function () {
                    AtribuirValorObj();
                    Resize();

                    if (ObterParamURL('selecao') &&
                            ObterParamURL('selecao') === 'S') {
                        $.MsgBoxLaranjaOk('#{localemsgs.Aviso}', '#{localemsgs.DuploClickCliente}');
                        $('header, footer').css('display', 'none');
                    }
                })
                        .on('click', '.ThumbFoto[ref="novaFoto"]', function () {
                            $('[id*="uploadFotosRelatorio2_input"]').click();
                        })
                        .on('mousedown', '.ThumbFoto:not([ref="novaFoto"]) i', function () {
                            $(this).parents('.ThumbFoto').off("click");

                            rcExcluirImagemBD([{name: 'sequencia', value: $(this).parents('.ThumbFoto').attr('sequencia')}]);
                        })
                        .on('mousedown', '.ThumbFoto .btn-warning', function () {
                            $(this).parents('.ThumbFoto').off("click");

                            rcTornarPadrao([{name: 'sequencia', value: $(this).parents('.ThumbFoto').attr('sequencia')}]);
                        })
                        .on('dblclick', '.ItemCliente', function () {
                            if (ObterParamURL('selecao') &&
                                    ObterParamURL('selecao') === 'S') {
                                let CodigoClienteSelecionado = $(this).find('.ItemClienteCodeDescricao').text();
                                let TipoCarregamento = ObterParamURL('tipo');

                                if (TipoCarregamento === 'D')
                                    window.parent.CarregarClienteDestino(CodigoClienteSelecionado);
                                else
                                    window.parent.CarregarClienteOrigem(CodigoClienteSelecionado);
                            }
                        })
                        ;

                function SucessoCapturaPosicao(position) {
                    LatitudeLogon = position.coords.latitude;
                    LongitudeLogon = position.coords.longitude;

                    if ($('[id$="formCapturar"] [id$="estado"]').attr('id')) {
                        $('[id*="txtLatitude"]').val(LatitudeLogon.toString().substr(0, 12));
                        $('[id*="txtLongitude"]').val(LongitudeLogon.toString().substr(0, 12));
                        ConsultarEndereco();
                    }
                }

                function AtribuirValorObj() {
                    if (navigator.geolocation)
                        navigator.geolocation.getCurrentPosition(SucessoCapturaPosicao, ErroCapturaPosicao, options);
                }

                function ErroCapturaPosicao(error) {

                }

                function ConsultarEndereco() {
                    $.ajax({
                        url: 'https://maps.googleapis.com/maps/api/geocode/json?key=#{login.googleApiOper}&latlng=' + LatitudeLogon + ',' + LongitudeLogon,
                        method: 'get'
                    })
                            .done(function (response) {
                                let Cidade = '';
                                $('[id$="formCapturar"] [id$="bairro"], [id$="formCapturar"] [id$="cidade"]').val('');
                                $('[id$="formCapturar"] [id$="endere"]').val(response.results[0].formatted_address.substr(0, 60));
                                $('[id$="formCapturar"] [id$="cep"]').val(response.results[0].address_components[response.results[0].address_components.length - 1].long_name);

                                $.each(response.results[0].address_components, function (i, item) {
                                    if (item.types.indexOf('administrative_area_level_1') > -1) {
                                        $('[id$="formCapturar"] [id$="estado"]').val(item.short_name.replace('CDMX', 'DF').substr(0, 2));
                                        Cidade = item.long_name;
                                    } else if (item.types.indexOf('administrative_area_level_2') > -1)
                                        $('[id$="formCapturar"] [id$="cidade"]').val(item.long_name);
                                    else if (item.types.indexOf('political') > -1 && $('[id$="formCapturar"] [id$="bairro"]').val().trim() === '')
                                        $('[id$="formCapturar"] [id$="bairro"]').val(item.long_name);

                                });

                                if ($('[id$="formCapturar"] [id$="cidade"]').val().trim() === '')
                                    $('[id$="formCapturar"] [id$="cidade"]').val(Cidade);
                            });
                }

                $(window).resize(function () {
                    Resize();
                });
                // ]]>
            </script>
        </h:body>
    </f:view>
</html>

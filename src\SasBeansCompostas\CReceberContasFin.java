package SasBeansCompostas;

import SasBeans.CReceber;
import SasBeans.ContasFin;

/**
 *
 * <AUTHOR>
 */
public class CReceberContasFin {

    private String Descricao;
    private String Valor;
    private CReceber creceber;
    private ContasFin contasfin;

    public CReceber getCreceber() {
        return creceber;
    }

    public void setCreceber(CReceber creceber) {
        this.creceber = creceber;
    }

    public ContasFin getContasfin() {
        return contasfin;
    }

    public void setContasfin(ContasFin contasfin) {
        this.contasfin = contasfin;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String descricao) {
        this.Descricao = descricao;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String valor) {
        this.Valor = valor;
    }

}

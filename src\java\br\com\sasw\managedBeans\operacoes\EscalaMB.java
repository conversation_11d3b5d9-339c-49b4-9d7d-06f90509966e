package br.com.sasw.managedBeans.operacoes;

import Controller.Escala.EscalaSatMobWeb;
import Controller.Pessoas.PessoasSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import SasBeans.Escala;
import SasBeans.Funcion;
import SasBeans.Municipios;
import SasBeans.Paramet;
import SasBeans.Pessoa;
import SasBeans.Rotas;
import SasBeans.Rt_Escala;
import SasBeans.SasPWFill;
import SasBeans.Veiculos;
import SasBeansCompostas.EscalaPessoaDTO;
import SasBeansCompostas.Login;
import br.com.sasw.lazydatamodels.EscalaPessoaLazyList;
import br.com.sasw.managedBeans.BaseBeanMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.isTodayOrFuture;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "escala")
@ViewScoped
public class EscalaMB extends BaseBeanMB {

    private SasPWFill filial;
    private Rotas rotaSelecionada;
    private List<Rotas> listaRotas;
    private Pessoa pessoa, motorista, chEquipe, vigilante1, vigilante2, vigilante3;
    private List<Pessoa> listaPessoa;
    private Escala escala;
    private Veiculos veiculo, novoVeiculo;
    private Municipios municipios;
    private List<Veiculos> listaVeiculos;
    private Rt_Escala rt_Escala;
    private Login loginObj;
    private Paramet parametros;
    private final EscalaSatMobWeb escalaSatMobWeb;
    private final PessoasSatMobWeb pessoasatmobweb;
    private final RotasSatWeb rotassatweb;
    private EscalaPessoaDTO escalaSelecionada, escalaEdicao;
    private LazyDataModel<EscalaPessoaDTO> escalas = null;
    private final BigDecimal codPessoa;
    private String filialDesc, hora1, hora2, hora3, hora4, texto, msgVeiculo;
    private Date ultimoDia;
    private final Calendar hora1C, hora2C, hora3C, hora4C;
    private boolean escalaExistente, senhaM, mostrarFiliais,
            infoChEquipe, permissaoRota, apenasAtivos;
    private int flagEscala;
    private final Map filters;
    private String chavePesquisa = "nome", valorPesquisa;
    private List<Funcion> folgas;

    public EscalaMB() throws Exception {
        super();
        FacesContext fc = FacesContext.getCurrentInstance();
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        ultimoDia = ultimoDiadoMes();
        escalaSatMobWeb = new EscalaSatMobWeb();
        pessoasatmobweb = new PessoasSatMobWeb();
        rotassatweb = new RotasSatWeb();
        mostrarFiliais = false;
        apenasAtivos = true;
        escalaExistente = false;
        hora1C = Calendar.getInstance();
        hora2C = Calendar.getInstance();
        hora3C = Calendar.getInstance();
        hora4C = Calendar.getInstance();

        filters = new HashMap();
        filters.put("escala.codfil = ? ", codFil);
        filters.put("rotas.flag_excl <> ? ", "*");
        filters.put("escala.data = ? ", dataTela);
        filters.put("rota", "");
        filters.put("nome", "");
        filters.put("nred", "");
    }

    @PostConstruct
    public void init() {
        try {
            carregarEscalas();
        } catch (Exception e) {
            displayFatal(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void carregarEscalas() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        escalas = new EscalaPessoaLazyList(persistencia);
        dt.setFirst(0);
    }

    public void limparFiltro() {
        mostrarFiliais = false;
        apenasAtivos = true;
        filters.replace("escala.codfil = ? ", codFil);
        filters.replace("rotas.flag_excl <> ? ", "*");
        filters.replace("rota", "");
        filters.replace("nome", "");
        filters.replace("nred", "");
        valorPesquisa = "";
        carregarEscalas();
    }

    public void mostrarFiliais() {
        filters.replace("escala.codfil = ? ", mostrarFiliais ? "" : codFil);
        carregarEscalas();
    }

    public void mostrarExcluidos() {
        filters.replace("rotas.flag_excl <> ? ", apenasAtivos ? "*" : "");
        carregarEscalas();
    }

    public void selecionarData(SelectEvent data) {
        dataTela = (String) data.getObject();
        filters.replace("escala.data = ? ", dataTela);
        carregarEscalas();
    }

    public void escalaAnterior() throws ParseException {
        dataTela = diaAnterior();
        filters.replace("escala.data = ? ", dataTela);
        carregarEscalas();
    }

    public void escalaPosterior() throws ParseException {
        dataTela = diaPosterior();
        filters.replace("escala.data = ? ", dataTela);
        carregarEscalas();
    }

    /**
     * Alterna para o dia anterior a data atual
     *
     * @return data do dia anterior a atual
     * @throws ParseException
     */
    private String diaAnterior() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        String formato = "yyyyMMdd";

        java.util.Date date = new SimpleDateFormat(formato).parse(dataTela);
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        java.util.Date dtbefore = calendar.getTime();
        SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
        dataTela = tesedata.format(dtbefore);

        return dataTela;
    }

    /**
     * Alterna para o dia posterior a data atual
     *
     * @return data do dia posterior a atual
     * @throws ParseException
     */
    private String diaPosterior() throws ParseException {

        Calendar calendar = Calendar.getInstance();
        String formato = "yyyyMMdd";

        java.util.Date date = new SimpleDateFormat(formato).parse(dataTela);
        calendar.setTime(date);
        calendar.add(Calendar.DATE, +1);
        java.util.Date dtbefore = calendar.getTime();
        SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
        dataTela = tesedata.format(dtbefore);
        return dataTela;
    }

    /**
     * Procura o último dia do mês atual
     *
     * @return A data do último dia do mês
     */
    private Date ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());
        Date retorno = null;

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);
        try {
            retorno = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            displayError(e.getMessage());
        }

        return retorno;
    }

    public void novaEscala() {
        try {
            escala = new Escala();
            SimpleDateFormat formatter1 = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss.S");
            escala.setData(formatter2.format(formatter1.parse(dataTela)));
            flagEscala = 1;
            filial = escalaSatMobWeb.buscaFilial(codFil, codPessoa, persistencia);
            escala.setCodFil(filial.getCodfilAc());
            parametros = rotassatweb.buscarParamet(escala.getCodFil().toString(), persistencia);

            rotaSelecionada = new Rotas();
            listaRotas = rotassatweb.listarRotasData(filial.getCodfilAc(), dataTela, persistencia);
            listaRotas.add(0, new Rotas());

            veiculo = new Veiculos();
            listaVeiculos = rotassatweb.listarVeiculos(escala.getCodFil().toString(), persistencia);
            listaVeiculos.add(0, new Veiculos());

            senhaM = false;
            permissaoRota = false;

            // pessoas
            pessoa = new Pessoa(); // apagar?
            motorista = new Pessoa();
            chEquipe = new Pessoa();
            vigilante1 = new Pessoa();
            vigilante2 = new Pessoa();
            vigilante3 = new Pessoa();
            infoChEquipe = false;

            PrimeFaces.current().resetInputs("cadastroEscala");
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }

    }

    public void limparCampos() {
        try {
            escala = new Escala();
            veiculo = new Veiculos();
            rotaSelecionada = new Rotas();
            pessoa = new Pessoa();
            senhaM = false;

            setHora1("");
            setHora2("");
            setHora3("");
            setHora4("");
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    //////////////////////////////////////////////////////////////////////////
    public void verificaHoraIntervaloEscala() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);
        LocalDate dia = LocalDate.parse(escala.getData(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"));

        hora1C.setTime(sdf.parse(escala.getHora1()));
        hora1C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora1C.get(Calendar.HOUR_OF_DAY), hora1C.get(Calendar.MINUTE));
        hora2C.setTime(sdf.parse(escala.getHora2()));
        hora2C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora2C.get(Calendar.HOUR_OF_DAY), hora2C.get(Calendar.MINUTE));
        hora3C.setTime(sdf.parse(escala.getHora3()));
        hora3C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora3C.get(Calendar.HOUR_OF_DAY), hora3C.get(Calendar.MINUTE));
        hora4C.setTime(sdf.parse(escala.getHora4()));
        hora4C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora4C.get(Calendar.HOUR_OF_DAY), hora4C.get(Calendar.MINUTE));

        long diferenca, minutos;
        float tot;

        if (!escala.getHora2().equals(escala.getHora3())) {
            if (hora1C.before(hora4C)) {
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                hora4C.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        hora4C.get(Calendar.HOUR_OF_DAY), hora4C.get(Calendar.MINUTE));
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            escala.setHsTot(Float.toString(tot / 60));
            if (hora2C.before(hora1C)) {
                LocalDate dia3 = dia.plusDays(1);
                hora2C.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        hora2C.get(Calendar.HOUR_OF_DAY), hora2C.get(Calendar.MINUTE));
            }
            if (hora2C.after(hora4C)) {
                throw new Exception("IntervaloInvalido");
            }
            if (hora3C.before(hora1C)) {
                LocalDate dia3 = dia.plusDays(1);
                hora3C.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        hora3C.get(Calendar.HOUR_OF_DAY), hora3C.get(Calendar.MINUTE));
            }
            if (hora3C.after(hora4C)) {
                throw new Exception("IntervaloInvalido");
            }
            if (hora2C.before(hora3C)) {
                diferenca = hora3C.getTime().getTime() - hora2C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else if (hora2C.after(hora3C)) {
                throw new Exception("IntervaloInvalido");
            }
            tot = tot - minutos;
            if (tot > 0) {
                escala.setHsTot(Float.toString(tot / 60));
            }
            if (tot < 0) {
                throw new Exception("IntervaloErrado");
            }
            hora1C.add(Calendar.MINUTE, -15);
        } else {
            if (hora1C.before(hora4C)) {
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                hora4C.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        hora4C.get(Calendar.HOUR_OF_DAY), hora4C.get(Calendar.MINUTE));
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            escala.setHsTot(Float.toString(tot / 60));
            hora1C.add(Calendar.MINUTE, -15);
        }
    }

    public void cadastrarEscala(boolean prosseguir) {
        try {
            List<String> matrs = new ArrayList<>();
            if (!escala.getMatrMot().equals(BigDecimal.ZERO)) {
                matrs.add(escala.getMatrMot().toBigInteger().toString());
            }
            folgas = rotassatweb.funcionariosFolga(matrs, escala.getData(), persistencia);
            if (!folgas.isEmpty() && !prosseguir) {
                PrimeFaces.current().ajax().update("panelFuncionarioFolga");
                PrimeFaces.current().executeScript("PF('dlgFuncionarioFolga').show();");
            } else {
                // validar data
                if (!isTodayOrFuture(escala.getData(), "yyyy-MM-dd")) {
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    throw new Exception("DataAnteriorEdicaoBloqueada");
                }

                if (null == rotaSelecionada.getSequencia() || rotaSelecionada.getSequencia().equals(BigDecimal.ZERO)) {
                    throw new Exception("SelecioneRotaValida");
                }

                if (null != motorista && !validarEscalaFuncionario(motorista.getCodigo())) {
                    throw new Exception("MotoristaJaEscalado");
                }
                if (null != chEquipe && !validarEscalaFuncionario(chEquipe.getCodigo())) {
                    throw new Exception("ChEquipeJaEscalado");
                }
                if (null != vigilante1 && !validarEscalaFuncionario(vigilante1.getCodigo())) {
                    throw new Exception("Vigilante1JaEscalado");
                }
                if (null != vigilante2 && !validarEscalaFuncionario(vigilante2.getCodigo())) {
                    throw new Exception("Vigilante2JaEscalado");
                }
                if (null != vigilante3 && !validarEscalaFuncionario(vigilante3.getCodigo())) {
                    throw new Exception("Vigilante3JaEscalado");
                }

                if (null != motorista && null != motorista.getCodigo() && !motorista.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == escala.getHrMot() || escala.getHrMot().equals("")) {
                        throw new Exception("HrMotInvalida");
                    }
                    if (motorista.getFuncao().equals("M") || motorista.getFuncao().equals("T")) {
                        if (rotassatweb.existeEscala(escala.getCodFil().toString(),
                                escala.getData(),
                                motorista.getMatr().toString(),
                                escala.getSeqRota().toBigInteger().toString(), persistencia)) {
                            motorista = new Pessoa();
                            throw new Exception("MotoristaJaEscalado");
                        }
                        escala.setMatrMot(motorista.getMatr().toString());
                    } else {
                        motorista = new Pessoa();
                        throw new Exception("PessoaNaoMotorista");
                    }
                }

                if (null != chEquipe && null != chEquipe.getCodigo() && !chEquipe.getCodigo().equals(BigDecimal.ZERO)) {
                    if (chEquipe.getFuncao().equals("C") || motorista.getFuncao().equals("T")) {
                        if (rotassatweb.existeEscala(escala.getCodFil().toString(),
                                escala.getData(),
                                chEquipe.getMatr().toString(),
                                escala.getSeqRota().toBigInteger().toString(), persistencia)) {
                            chEquipe = new Pessoa();
                            throw new Exception("ChEquipeJaEscalado");
                        }
                        permissaoRota = rotassatweb.buscarPermissaoRotas(chEquipe.getCodigo(), persistencia);
                        escala.setMatrChe(chEquipe.getMatr().toString());
                    } else {
                        chEquipe = new Pessoa();
                        throw new Exception("PessoaNaoChEquipe");
                    }
                }

                if (null != veiculo && veiculo.getNumero() != 0) {
                    Escala escalaVeiculo = rotassatweb.existeEscalaVeiculo(escala.getData(),
                            String.valueOf(veiculo.getNumero()), persistencia);

                    if (escalaVeiculo != null
                            && LocalTime.parse(escalaVeiculo.getHora4(), DateTimeFormatter.ofPattern("HH:mm"))
                                    .isAfter(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm")))) {
                        if (escalaVeiculo.getSeqRota().compareTo(escala.getSeqRota()) != 0) {
                            throw new Exception("VeiculoJaEscalado");
                        }
                    }
                }

                verificaHoraIntervaloEscala();
                escala.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                rotassatweb.inserirEscala(escala, persistencia);
                PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                displayInfo("CadastroSucesso");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void editarEscala(boolean prosseguir) {
        try {
            List<String> matrs = new ArrayList<>();
            if (!escala.getMatrMot().equals(BigDecimal.ZERO)) {
                matrs.add(escala.getMatrMot().toBigInteger().toString());
            }
            folgas = rotassatweb.funcionariosFolga(matrs, escala.getData(), persistencia);
            if (!folgas.isEmpty() && !prosseguir) {
                PrimeFaces.current().ajax().update("panelFuncionarioFolga");
                PrimeFaces.current().executeScript("PF('dlgFuncionarioFolga').show();");
            } else {
                // validar data
                if (!isTodayOrFuture(escala.getData(), "yyyy-MM-dd")) {
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    throw new Exception("DataAnteriorEdicaoBloqueada");
                }

                if (null == rotaSelecionada.getSequencia() || rotaSelecionada.getSequencia().equals(BigDecimal.ZERO)) {
                    throw new Exception("SelecioneRotaValida");
                }

                if (motorista != null && !validarEscalaFuncionario(motorista.getCodigo())) {
                    throw new Exception("MotoristaJaEscalado");
                }
                if (chEquipe != null && !validarEscalaFuncionario(chEquipe.getCodigo())) {
                    throw new Exception("ChEquipeJaEscalado");
                }
                if (vigilante1 != null && !validarEscalaFuncionario(vigilante1.getCodigo())) {
                    throw new Exception("Vigilante1JaEscalado");
                }
                if (vigilante2 != null && !validarEscalaFuncionario(vigilante2.getCodigo())) {
                    throw new Exception("Vigilante2JaEscalado");
                }
                if (vigilante3 != null && !validarEscalaFuncionario(vigilante3.getCodigo())) {
                    throw new Exception("Vigilante3JaEscalado");
                }

                if (null != motorista && null != motorista.getCodigo() && !motorista.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == escala.getHrMot() || escala.getHrMot().equals("")) {
                        throw new Exception("HrMotInvalida");
                    }
                    if (motorista.getFuncao().equals("M") || motorista.getFuncao().equals("T")) {
                        if (rotassatweb.existeEscala(escala.getCodFil().toString(),
                                escala.getData(),
                                motorista.getMatr().toString(),
                                escala.getSeqRota().toBigInteger().toString(), persistencia)) {
                            motorista = new Pessoa();
                            throw new Exception("MotoristaJaEscalado");
                        }
                        escala.setMatrMot(motorista.getMatr().toString());
                    } else {
                        motorista = new Pessoa();
                        throw new Exception("PessoaNaoMotorista");
                    }
                }

                if (null != chEquipe && null != chEquipe.getCodigo() && !chEquipe.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == escala.getHrChe() || escala.getHrChe().equals("")) {
                        throw new Exception("HrCheInvalida");
                    }
                    if (chEquipe.getFuncao().equals("C") || motorista.getFuncao().equals("T")) {
                        if (rotassatweb.existeEscala(escala.getCodFil().toString(),
                                escala.getData(),
                                chEquipe.getMatr().toString(),
                                escala.getSeqRota().toBigInteger().toString(), persistencia)) {
                            chEquipe = new Pessoa();
                            throw new Exception("ChEquipeJaEscalado");
                        }
                        permissaoRota = rotassatweb.buscarPermissaoRotas(chEquipe.getCodigo(), persistencia);
                        escala.setMatrChe(chEquipe.getMatr().toString());
                    } else {
                        chEquipe = new Pessoa();
                        throw new Exception("PessoaNaoChEquipe");
                    }
                }

                if (null != vigilante1 && null != vigilante1.getCodigo() && !vigilante1.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == escala.getHrVig1() || escala.getHrVig1().equals("")) {
                        throw new Exception("HrVig1Invalida");
                    }
                }

                if (null != vigilante2 && null != vigilante2.getCodigo() && !vigilante2.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == escala.getHrVig2() || escala.getHrVig2().equals("")) {
                        throw new Exception("HrVig2Invalida");
                    }
                }

                if (null != vigilante3 && null != vigilante3.getCodigo() && !vigilante3.getCodigo().equals(BigDecimal.ZERO)) {
                    if (null == escala.getHrVig3() || escala.getHrVig3().equals("")) {
                        throw new Exception("HrVig3Invalida");
                    }
                }

                if (null != this.veiculo && veiculo.getNumero() != 0) {
                    Escala escalaVeiculo = rotassatweb.existeEscalaVeiculo(escala.getData(),
                            String.valueOf(veiculo.getNumero()), persistencia);

                    if (escalaVeiculo != null
                            && LocalTime.parse(escalaVeiculo.getHora4(), DateTimeFormatter.ofPattern("HH:mm"))
                                    .isAfter(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm")))) {
                        if (escalaVeiculo.getSeqRota().compareTo(escala.getSeqRota()) != 0) {
                            throw new Exception("VeiculoJaEscalado");
                        }
                    }
                }

                verificaHoraIntervaloEscala();
                escala.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
                rotassatweb.atualizarEscala(escala, persistencia);
                PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                displayInfo("EdicaoSucesso");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void dblSelect(SelectEvent event) {
        try {
            escalaSelecionada = (EscalaPessoaDTO) event.getObject();
            buttonAction(null);
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void buttonAction(ActionEvent actionEvent) {
        try {
            if (null == escalaSelecionada) {
                displayInfo("SelecioneEscala");
            } else {
                novaListaPessoas();
                escalaEdicao = new EscalaPessoaDTO(escalaSelecionada);
                escala = escalaSelecionada.getEscala();
                flagEscala = 2;
                filial = escalaSatMobWeb.buscaFilial(escala.getCodFil().toString(), codPessoa, persistencia);
                parametros = rotassatweb.buscarParamet(escala.getCodFil().toString(), persistencia);
                listaVeiculos = rotassatweb.listarVeiculos(escala.getCodFil().toString(), persistencia);
                listaVeiculos.add(0, new Veiculos());
                listaRotas = rotassatweb.listarRotasData(filial.getCodfilAc(), dataTela, persistencia);
                listaRotas.add(0, new Rotas());
                motorista = rotassatweb.buscarMatricula(escala.getMatrMot().toBigInteger().toString(), persistencia);
                chEquipe = rotassatweb.buscarMatricula(escala.getMatrChe().toBigInteger().toString(), persistencia);
                permissaoRota = chEquipe != null
                        && rotassatweb.buscarPermissaoRotas(chEquipe.getCodigo(), persistencia);
                vigilante1 = rotassatweb.buscarMatricula(escala.getMatrVig1().toBigInteger().toString(), persistencia);
                vigilante2 = rotassatweb.buscarMatricula(escala.getMatrVig2().toBigInteger().toString(), persistencia);
                vigilante3 = rotassatweb.buscarMatricula(escala.getMatrVig3().toBigInteger().toString(), persistencia);
                vigilante3 = vigilante3 == null ? new Pessoa() : vigilante3;
                vigilante3.setMatr(vigilante3.getMatr() == null ? new BigDecimal("0") : vigilante3.getMatr());
                vigilante3.setNome(vigilante3.getNome() == null ? "" : vigilante3.getNome());
                veiculo = rotassatweb.buscarVeiculo(escala.getVeiculo().intValue(), persistencia);
                rotaSelecionada = escalaSatMobWeb.buscaRota(escala.getCodFil().toPlainString(), escala.getRota(),
                        escala.getData(), persistencia);
                infoChEquipe = false;
                hora1 = escala.getHora1();
                hora2 = escala.getHora2();
                hora3 = escala.getHora3();
                hora4 = escala.getHora4();

                try {
                    verificaHoraIntervalo();
                } catch (Exception e) {
                    displayWarn("HoraInvalida");
                }

                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public boolean validarEscalaFuncionario(BigDecimal codigo) {
        int cont = 0;
        if (null != motorista
                && null != motorista.getCodigo()
                && !motorista.getCodigo().equals(BigDecimal.ZERO)
                && motorista.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != chEquipe
                && null != chEquipe.getCodigo()
                && !chEquipe.getCodigo().equals(BigDecimal.ZERO)
                && chEquipe.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != vigilante1
                && null != vigilante1.getCodigo()
                && !vigilante1.getCodigo().equals(BigDecimal.ZERO)
                && vigilante1.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != vigilante2
                && null != vigilante2.getCodigo()
                && !vigilante2.getCodigo().equals(BigDecimal.ZERO)
                && vigilante2.getCodigo().equals(codigo)) {
            cont++;
        }
        if (null != vigilante3
                && null != vigilante3.getCodigo()
                && !vigilante3.getCodigo().equals(BigDecimal.ZERO)
                && vigilante3.getCodigo().equals(codigo)) {
            cont++;
        }
        return cont <= 1;
    }

    private void novaListaPessoas() {
        listaPessoa = new ArrayList<>();
        if (null != motorista) {
            listaPessoa.add(motorista);
        }
        if (null != chEquipe) {
            listaPessoa.add(chEquipe);
        }
        if (null != vigilante1) {
            listaPessoa.add(vigilante1);
        }
        if (null != vigilante2) {
            listaPessoa.add(vigilante2);
        }
        if (null != vigilante3) {
            listaPessoa.add(vigilante3);
        }
    }

    public void selecionarMotorista(SelectEvent event) {
        try {
            novaListaPessoas();
            motorista = (Pessoa) event.getObject();
            if (motorista.getFuncao().equals("M") || motorista.getFuncao().equals("T")) {
                if (rotassatweb.existeEscala(escala.getCodFil().toString(),
                        escala.getData(),
                        motorista.getMatr().toString(),
                        escala.getSeqRota().toBigInteger().toString(), persistencia)) {
                    motorista = new Pessoa();
                    throw new Exception("PessoaJaEscalada");
                }
                if (!validarEscalaFuncionario(motorista.getCodigo())) {
                    throw new Exception("PessoaJaEscalada");
                }
                if (null == veiculo
                        || null == veiculo.getMatr_Mot()
                        || veiculo.getMatr_Mot().compareTo(BigDecimal.ZERO) == 0
                        || veiculo.getMatr_Mot().compareTo(motorista.getMatr()) == 0) {
                    msgVeiculo = Messages.getMessageS("MotoristaDiferenteVeiculo");
                } else {
                    msgVeiculo = "";
                }

                try {
                    escala.setMatrMot(motorista.getMatr().toString());
                    if (escala.getHora1() != null) {
                        escala.setHrMot(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                                .minusMinutes(parametros.getEscTolerMot()).toString());
                    }
                } catch (DateTimeParseException e) {
                    displayInfo("HorarioVazio");
                }
            } else {
                motorista = new Pessoa();
                throw new Exception("PessoaNaoMotorista");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarChEquipe(SelectEvent event) {
        try {
            novaListaPessoas();
            chEquipe = (Pessoa) event.getObject();
            if (chEquipe.getFuncao().equals("C") || chEquipe.getFuncao().equals("T")) {
                if (rotassatweb.existeEscala(escala.getCodFil().toString(),
                        escala.getData(),
                        chEquipe.getMatr().toString(),
                        escala.getSeqRota().toBigInteger().toString(), persistencia)) {
                    chEquipe = new Pessoa();
                    throw new Exception("PessoaJaEscalada");
                }
                if (!validarEscalaFuncionario(chEquipe.getCodigo())) {
                    throw new Exception("PessoaJaEscalada");
                }
                permissaoRota = rotassatweb.buscarPermissaoRotas(chEquipe.getCodigo(), persistencia);
                escala.setMatrChe(chEquipe.getMatr().toString());

                try {
                    if (escala.getHora1() != null) {
                        escala.setHrChe(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                                .minusMinutes(parametros.getEscTolerChe()).toString());
                    }
                } catch (DateTimeParseException e) {
                    displayInfo("HorarioVazio");
                }
            } else {
                chEquipe = new Pessoa();
                throw new Exception("PessoaNaoChEquipe");
            }
            listaPessoa = new ArrayList<>();
            if (null != motorista) {
                listaPessoa.add(motorista);
            }
            if (null != chEquipe) {
                listaPessoa.add(chEquipe);
            }
            if (null != vigilante1) {
                listaPessoa.add(vigilante1);
            }
            if (null != vigilante2) {
                listaPessoa.add(vigilante2);
            }
            if (null != vigilante3) {
                listaPessoa.add(vigilante3);
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarVigilante1(SelectEvent event) {
        try {
            novaListaPessoas();
            vigilante1 = (Pessoa) event.getObject();
            if (!validarEscalaFuncionario(vigilante1.getCodigo())) {
                throw new Exception("PessoaJaEscalada");
            }
            escala.setMatrVig1(vigilante1.getMatr().toString());

            try {
                if (escala.getHora1() != null) {
                    escala.setHrVig1(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                            .minusMinutes(parametros.getEscTolerVig()).toString());
                }
            } catch (DateTimeParseException e) {
                displayInfo("HorarioVazio");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarVigilante2(SelectEvent event) {
        try {
            novaListaPessoas();
            vigilante2 = (Pessoa) event.getObject();
            if (!validarEscalaFuncionario(vigilante2.getCodigo())) {
                throw new Exception("PessoaJaEscalada");
            }
            escala.setMatrVig2(vigilante2.getMatr().toString());

            try {
                if (escala.getHora1() != null) {
                    escala.setHrVig2(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                            .minusMinutes(parametros.getEscTolerVig()).toString());
                }
            } catch (DateTimeParseException e) {
                displayInfo("HorarioVazio");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarVigilante3(SelectEvent event) {
        try {
            novaListaPessoas();
            vigilante3 = (Pessoa) event.getObject();
            if (!validarEscalaFuncionario(vigilante3.getCodigo())) {
                throw new Exception("PessoaJaEscalada");
            }
            escala.setMatrVig3(vigilante3.getMatr().toString());

            try {
                if (escala.getHora1() != null) {
                    escala.setHrVig3(LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"))
                            .minusMinutes(parametros.getEscTolerVig()).toString());
                }
            } catch (DateTimeParseException e) {
                displayInfo("HorarioVazio");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarVeiculo() {
        try {
            if (null == motorista
                    || null == motorista.getMatr()
                    || motorista.getMatr().compareTo(BigDecimal.ZERO) == 0
                    || veiculo.getMatr_Mot().compareTo(motorista.getMatr()) == 0) {
                msgVeiculo = Messages.getMessageS("MotoristaDiferenteVeiculo");
            } else {
                msgVeiculo = "";
            }
            Escala escalaVeiculo = rotassatweb.existeEscalaVeiculo(escala.getData(),
                    String.valueOf(veiculo.getNumero()), persistencia);

            if (escalaVeiculo != null) {
                LocalTime localHora1, localHora4;
                try {
                    localHora4 = LocalTime.parse(escalaVeiculo.getHora4(), DateTimeFormatter.ofPattern("HH:mm"));
                } catch (DateTimeParseException e) {
                    throw new Exception("HoraInvalida");
                }

                try {
                    localHora1 = LocalTime.parse(escala.getHora1(), DateTimeFormatter.ofPattern("HH:mm"));
                } catch (DateTimeParseException e) {
                    throw new Exception("HoraInvalida");
                }

                if (localHora4.isAfter(localHora1)
                        && escalaVeiculo.getSeqRota().compareTo(escala.getSeqRota()) != 0) {
                    throw new Exception("VeiculoJaEscalado");
                }
            }

            escala.setVeiculo(String.valueOf(veiculo.getNumero()));
        } catch (Exception e) {
            /*displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());*/
        }
    }

    /**
     * Verifica qual o veículo selecionado para a escala
     *
     * @param veiculo Número do veículo selecionado
     * @return Retorna o veículo com informações sobre o mesmo
     */
    public Veiculos selecionarVeiculo(BigDecimal veiculo) {
        try {
            this.veiculo = escalaSatMobWeb.BuscaVeiculoNumero(veiculo.intValue(), persistencia);
            if (this.veiculo.getPlaca() == null) {
                this.veiculo = new Veiculos();
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return this.veiculo;
    }

    public void selecionarFilial(SelectEvent event) {
        try {
            escala.setCodFil(filial.getCodfilAc());
            parametros = rotassatweb.buscarParamet(escala.getCodFil().toString(), persistencia);
            listaRotas = rotassatweb.listarRotasData(escala.getCodFil().toString(), dataTela, persistencia);
            listaRotas.add(0, new Rotas());
            rotaSelecionada = new Rotas();
            veiculo = new Veiculos();
            motorista = new Pessoa();
            chEquipe = new Pessoa();
            permissaoRota = false;
            vigilante1 = new Pessoa();
            vigilante2 = new Pessoa();
            vigilante3 = new Pessoa();

            infoChEquipe = false;

            PrimeFaces.current().resetInputs("cadastroEscala");
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void selecionarRota(SelectEvent event) {
        try {
            if (null == rotaSelecionada.getSequencia() || rotaSelecionada.getSequencia().equals(BigDecimal.ZERO)) {
                escala = new Escala();
                SimpleDateFormat formatter1 = new SimpleDateFormat("yyyyMMdd");
                SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss.S");
                escala.setData(formatter2.format(formatter1.parse(dataTela)));
                motorista = new Pessoa();
                chEquipe = new Pessoa();
                permissaoRota = false;
                vigilante1 = new Pessoa();
                vigilante2 = new Pessoa();
                vigilante3 = new Pessoa();
                veiculo = new Veiculos();

                infoChEquipe = false;
                throw new Exception("SelecioneRotaValida");
            } else {
                escala = rotassatweb.selecionaEscala(rotaSelecionada.getSequencia(), persistencia);
                if (null == escala.getSeqRota() || escala.getSeqRota().compareTo(BigDecimal.ZERO) == 0) {
                    flagEscala = 1;
                    escala.setSeqRota(rotaSelecionada.getSequencia().toString());
                    escala.setCodFil(rotaSelecionada.getCodFil().toString());
                    escala.setRota(rotaSelecionada.getRota());
                    escala.setData(rotaSelecionada.getData());
                    escala.setHora1(rotaSelecionada.getHrLargada());
                    escala.setHora2(rotaSelecionada.getHrIntIni());
                    escala.setHora3(rotaSelecionada.getHrIntFim());
                    escala.setHora4(rotaSelecionada.getHrChegada());
                } else {
                    flagEscala = 2;
                }

                veiculo = rotassatweb.buscarVeiculo(escala.getVeiculo().intValue(), persistencia);
                motorista = rotassatweb.buscarMatricula(escala.getMatrMot().toBigInteger().toString(), persistencia);
                chEquipe = rotassatweb.buscarMatricula(escala.getMatrChe().toBigInteger().toString(), persistencia);
                permissaoRota = chEquipe != null
                        && rotassatweb.buscarPermissaoRotas(chEquipe.getCodigo(), persistencia);
                vigilante1 = rotassatweb.buscarMatricula(escala.getMatrVig1().toBigInteger().toString(), persistencia);
                vigilante2 = rotassatweb.buscarMatricula(escala.getMatrVig2().toBigInteger().toString(), persistencia);
                vigilante3 = rotassatweb.buscarMatricula(escala.getMatrVig3().toBigInteger().toString(), persistencia);

                infoChEquipe = false;
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public List<Pessoa> buscarPessoas(String query, String funcao) {
        listaPessoa = new ArrayList<>();
        try {
            listaPessoa = rotassatweb.buscarPessoaEscalaFuncao(query, funcao, filial.getCodfilAc(), persistencia);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return listaPessoa;
    }

    public List<Pessoa> buscarMotorista(String query) {
        return buscarPessoas(query, "M");
    }

    public List<Pessoa> buscarChEquip(String query) {
        return buscarPessoas(query, "C");
    }

    public List<Pessoa> buscarVigilante(String query) {
        return buscarPessoas(query, "V");
    }

    /**
     * Seleciona o supervisor da rota
     *
     * @param event Evento no qual retorna o código de Pessoa
     */
    public void selecionarPessoa(SelectEvent event) {
        try {
            Pessoa p = ((Pessoa) event.getObject());
            pessoa = pessoasatmobweb.ListaPessoa(p, persistencia).get(0);
            loginObj = escalaSatMobWeb.BuscaPermissao(pessoa.getCodigo(), persistencia);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    /**
     *
     * @param query
     * @return
     */
    public List<Veiculos> listarVeiculos(String query) {
        List<Veiculos> retorno = new ArrayList<>();
        listaVeiculos = new ArrayList<>();
        try {
            listaVeiculos = escalaSatMobWeb.ListaVeiculos(filial.getCodfilAc(), persistencia);

            for (Veiculos v : listaVeiculos) {
                if (v.getPlaca().toUpperCase().contains(query.toUpperCase())) {
                    retorno.add(v);
                }
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return retorno;
    }

    public void selecionaVeiculo(SelectEvent event) {
        try {
            veiculo = escalaSatMobWeb.BuscaVeiculoPlaca(veiculo.getPlaca(), persistencia);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void veiculoNovo() {
        novoVeiculo = new Veiculos();
        municipios = new Municipios();
    }

    public void numVeiculo() {
        try {
            int num = escalaSatMobWeb.BuscaNumeroVeic(persistencia);
            novoVeiculo.setNumero(num);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void cadastraVeiculo() {
        try {
            novoVeiculo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            novoVeiculo.setDt_Alter(DataAtual.getDataAtual("SQL"));
            novoVeiculo.setHr_Alter(DataAtual.getDataAtual("HORA"));

            novoVeiculo.setCodFil(filial.getCodfilAc());
            municipios = escalaSatMobWeb.BuscaMunicipio(municipios.getCodigo(), persistencia);
            novoVeiculo.setUF_Placa(municipios.getUF());
            novoVeiculo.setMun_Placa(municipios.getNome());

            if (novoVeiculo.getPlaca().contains("-")) {
                novoVeiculo.setPlaca(novoVeiculo.getPlaca().replace("-", "").toUpperCase());
            }

            escalaSatMobWeb.InsereVeiculo(novoVeiculo, persistencia);

            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }

        EscalaMB.this.listarVeiculos();
    }

    public void listarVeiculos() {
        try {
            listaVeiculos = escalaSatMobWeb.ListaVeiculos(filial.getCodfilAc(), persistencia);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public List<Municipios> listarMunicipios(String query) {
        List<Municipios> retorno = new ArrayList<>();
        List<Municipios> listMunicipios;
        try {
            listMunicipios = escalaSatMobWeb.ListaMunicipios(persistencia);
            for (Municipios Municipio : listMunicipios) {
                if (Municipio.getNome().toUpperCase().contains(query.toUpperCase())) {
                    retorno.add(Municipio);
                }
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
        return retorno;
    }

    public void selecionarMunicipio(SelectEvent event) {
        try {
            municipios = escalaSatMobWeb.BuscaMunicipio(municipios.getCodigo(), persistencia);
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void cadastrarVeiculo() {
        novoVeiculo.setUF_Placa(municipios.getUF());
        novoVeiculo.setMun_Placa(municipios.getNome());
        novoVeiculo.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
        novoVeiculo.setHr_Alter(DataAtual.getDataAtual("HORA"));
    }

    public void calculaHorasTrabalhadas() {
        try {
            verificaHoraIntervalo();
        } catch (ParseException e) {
            displayInfo("HoraInvalida");
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void verificaHoraIntervalo() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        sdf.setLenient(true);
        LocalDate dia = LocalDate.parse(escala.getData(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"));

        hora1C.setTime(sdf.parse(escala.getHora1()));
        hora1C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora1C.get(Calendar.HOUR_OF_DAY), hora1C.get(Calendar.MINUTE));
        hora2C.setTime(sdf.parse(escala.getHora2()));
        hora2C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora2C.get(Calendar.HOUR_OF_DAY), hora2C.get(Calendar.MINUTE));
        hora3C.setTime(sdf.parse(escala.getHora3()));
        hora3C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora3C.get(Calendar.HOUR_OF_DAY), hora3C.get(Calendar.MINUTE));
        hora4C.setTime(sdf.parse(escala.getHora4()));
        hora4C.set(dia.getYear(), dia.getMonthValue(), dia.getDayOfMonth(),
                hora4C.get(Calendar.HOUR_OF_DAY), hora4C.get(Calendar.MINUTE));

        long diferenca, minutos;
        float tot;

        if (!escala.getHora2().equals(escala.getHora3())) {
            if (hora1C.before(hora4C)) {
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                hora4C.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        hora4C.get(Calendar.HOUR_OF_DAY), hora4C.get(Calendar.MINUTE));
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            escala.setHsTot(Float.toString(tot / 60));
            if (hora2C.before(hora1C)) {
                LocalDate dia3 = dia.plusDays(1);
                hora2C.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        hora2C.get(Calendar.HOUR_OF_DAY), hora2C.get(Calendar.MINUTE));
            }
            if (hora2C.after(hora4C)) {
                throw new Exception("IntervaloInvalido");
            }
            if (hora3C.before(hora1)) {
                LocalDate dia3 = dia.plusDays(1);
                hora3C.set(dia3.getYear(), dia3.getMonthValue(), dia3.getDayOfMonth(),
                        hora3C.get(Calendar.HOUR_OF_DAY), hora3C.get(Calendar.MINUTE));
            }
            if (hora3C.after(hora4C)) {
                throw new Exception("IntervaloInvalido");
            }
            if (hora2C.before(hora3C)) {
                diferenca = hora3C.getTime().getTime() - hora2C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else if (hora2C.after(hora3C)) {
                throw new Exception("IntervaloInvalido");
            }
            tot = tot - minutos;
            if (tot > 0) {
                escala.setHsTot(Float.toString(tot / 60));
            }
            if (tot < 0) {
                throw new Exception("IntervaloErrado");
            }
            hora1C.add(Calendar.MINUTE, -15);
        } else {
            if (hora1C.before(hora4C)) {
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            } else {
                LocalDate dia4 = dia.plusDays(1);
                hora4C.set(dia4.getYear(), dia4.getMonthValue(), dia4.getDayOfMonth(),
                        hora4C.get(Calendar.HOUR_OF_DAY), hora4C.get(Calendar.MINUTE));
                diferenca = hora4C.getTime().getTime() - hora1C.getTime().getTime();
                minutos = TimeUnit.MINUTES.convert(diferenca, TimeUnit.MILLISECONDS);
            }
            tot = minutos;
            escala.setHsTot(Float.toString(tot / 60));
            hora1C.add(Calendar.MINUTE, -15);
        }
    }

    /**
     * Calcula a hora de chegada do supervisor escalado para 15 minutos antes do
     * início da escala; 15 minutos antes como padrão podendo seer modificada
     * pelo usuário
     *
     * @param hrChe
     */
    private void calculaHrChe(String hrChe) {
        String[] split = hrChe.split(":");
        if (split[0].length() == 1) {
            split[0] = "0" + split[0];
            hrChe = split[0] + ":" + split[1];
        }
        if (split[1].length() == 1) {
            split[1] = "0" + split[1];
            hrChe = split[0] + ":" + split[1];
        }
        escala.setHrChe(hrChe);
    }

    public void excluir() {
        try {
            if (escalaSelecionada == null) {
                throw new Exception(Messages.getMessageS("SelecioneEscala"));
            } else {
                escala = escalaSelecionada.getEscala();
                escalaSatMobWeb.ExcluirEscala(escala, persistencia);
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        } catch (Exception e) {
            displayError(e.getMessage());
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void ativarModalPesquisa() {
        PrimeFaces.current().resetInputs("formPesquisa:pesquisar");
        PrimeFaces.current().executeScript("PF('dlgPesquisa').show();");
    }

    public void pesquisar() {
        filters.replace("rota", "");
        filters.replace("nome", "");
        filters.replace("nred", "");
        filters.replace(chavePesquisa, valorPesquisa);
        carregarEscalas();
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getHora2() {
        return hora2;
    }

    public void setHora2(String hora2) {
        this.hora2 = hora2;
    }

    public String getHora3() {
        return hora3;
    }

    public void setHora3(String hora3) {
        this.hora3 = hora3;
    }

    public String getHora4() {
        return hora4;
    }

    public void setHora4(String hora4) {
        this.hora4 = hora4;
    }

    public Rotas getRotaSelecionada() {
        return rotaSelecionada;
    }

    public void setRotaSelecionada(Rotas rotaSelecionada) {
        this.rotaSelecionada = rotaSelecionada;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Veiculos getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(Veiculos veiculo) {
        this.veiculo = veiculo;
    }

    public Escala getEscala() {
        return escala;
    }

    public void setEscala(Escala escala) {
        this.escala = escala;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public void setUltimoDia(Date ultimoDia) {
        this.ultimoDia = ultimoDia;
    }

    public Login getLoginObj() {
        return loginObj;
    }

    public void setLoginObj(Login loginObj) {
        this.loginObj = loginObj;
    }

    public boolean isSenhaM() {
        return senhaM;
    }

    public void setSenhaM(boolean senhaM) {
        this.senhaM = senhaM;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public Veiculos getNovoVeiculo() {
        return novoVeiculo;
    }

    public void setNovoVeiculo(Veiculos novoVeiculo) {
        this.novoVeiculo = novoVeiculo;
    }

    public Municipios getMunicipios() {
        return municipios;
    }

    public void setMunicipios(Municipios municipios) {
        this.municipios = municipios;
    }

    public List<Veiculos> getListaVeiculos() {
        return listaVeiculos;
    }

    public void setListaVeiculos(List<Veiculos> listaVeiculos) {
        this.listaVeiculos = listaVeiculos;
    }

    public List<Pessoa> getListaPessoa() {
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Pessoa getMotorista() {
        return motorista;
    }

    public void setMotorista(Pessoa motorista) {
        this.motorista = motorista;
    }

    public Pessoa getChEquipe() {
        return chEquipe;
    }

    public void setChEquipe(Pessoa chEquipe) {
        this.chEquipe = chEquipe;
    }

    public Pessoa getVigilante1() {
        return vigilante1;
    }

    public void setVigilante1(Pessoa vigilante1) {
        this.vigilante1 = vigilante1;
    }

    public Pessoa getVigilante2() {
        return vigilante2;
    }

    public void setVigilante2(Pessoa vigilante2) {
        this.vigilante2 = vigilante2;
    }

    public Pessoa getVigilante3() {
        return vigilante3;
    }

    public void setVigilante3(Pessoa vigilante3) {
        this.vigilante3 = vigilante3;
    }

    public boolean isInfoChEquipe() {
        return infoChEquipe;
    }

    public void setInfoChEquipe(boolean infoChEquipe) {
        this.infoChEquipe = infoChEquipe;
    }

    public String getMsgVeiculo() {
        return msgVeiculo;
    }

    public void setMsgVeiculo(String msgVeiculo) {
        this.msgVeiculo = msgVeiculo;
    }

    public boolean isPermissaoRota() {
        return permissaoRota;
    }

    public int getFlagEscala() {
        return flagEscala;
    }

    public List<Rotas> getListaRotas() {
        return listaRotas;
    }

    public LazyDataModel<EscalaPessoaDTO> getEscalas() {
        return escalas;
    }

    public EscalaPessoaDTO getEscalaSelecionada() {
        return escalaSelecionada;
    }

    public void setEscalaSelecionada(EscalaPessoaDTO escalaSelecionada) {
        this.escalaSelecionada = escalaSelecionada;
    }

    public EscalaPessoaDTO getEscalaEdicao() {
        return escalaEdicao;
    }

    public void setEscalaEdicao(EscalaPessoaDTO escalaEdicao) {
        this.escalaEdicao = escalaEdicao;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public boolean isApenasAtivos() {
        return apenasAtivos;
    }

    public void setApenasAtivos(boolean apenasAtivos) {
        this.apenasAtivos = apenasAtivos;
    }

    public List<Funcion> getFolgas() {
        return folgas;
    }

    public void setFolgas(List<Funcion> folgas) {
        this.folgas = folgas;
    }
}

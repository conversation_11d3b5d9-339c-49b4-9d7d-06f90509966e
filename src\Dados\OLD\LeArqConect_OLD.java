/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Dados.OLD;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.InputStream;
import java.io.Reader;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 *
 * <AUTHOR>
 */
public class LeArqConect_OLD {

    private String Empresa;
    private String ip, login, senha;

    public void Mapeia(String param, String caminho, String Opcao) throws Exception {
        if (Opcao.equals("DESK")) {
            this.MapeiaDesk(param, caminho);
        } else if (Opcao.equals("WEB")) {
            this.MapeiaWeb(param, caminho);
        }
    }

    public void MapeiaDesk(String param, String caminho) throws Exception {
        String linha;

        try {
            InputStream in = getClass().getResourceAsStream(caminho);
            Reader reader = new java.io.InputStreamReader(in);
// instancia do arquivo que vou ler  
            //FileReader reader = new FileReader(caminho);  
            BufferedReader leitor = new BufferedReader(reader);
// loop que percorrerá todas as  linhas do arquivo.txt que eu quero ler  
            linha = leitor.readLine();
            while (linha != null) {
//No metodo StringTokenizer passo os parametros que quero ler, em seguida o que eu quero descartar no meu caso( } ).   
                StringTokenizer st = new StringTokenizer(linha, "}");
                Empresa = st.nextToken();
                if (Empresa.equals(param)) {
                    ip = st.nextToken();
                    login = st.nextToken();
                    senha = st.nextToken();
                    linha = null;
                } else {
                    linha = leitor.readLine();
                }
            }
            leitor.close();
            reader.close();
        } catch (Exception e) {
            throw new Exception("Erro - " + e.getMessage());
        }
    }

    public void MapeiaWeb(String param, String caminho) throws Exception {
        String linha;
// instancia do arquivo que vou lermuito legal isso 
        FileReader reader = new FileReader(caminho);
        BufferedReader leitor = new BufferedReader(reader);
// loop que percorrerá todas as  linhas do arquivo.txt que eu quero ler  
        linha = leitor.readLine();
        while (linha != null) {
//No metodo StringTokenizer passo os parametros que quero ler, em seguida o que eu quero descartar no meu caso( ; ).   
            StringTokenizer st = new StringTokenizer(linha, "}");
            Empresa = st.nextToken();
            if (Empresa.equals(param)) {
                ip = st.nextToken();
                login = st.nextToken();
                senha = st.nextToken();
                linha = null;
            } else {
                linha = leitor.readLine();
            }
        }
        leitor.close();
        reader.close();
    }

    /**
     * Busca na mapconect todos os clientes e devolve uma lista para estabelecer
     * a conexao
     *
     * @param caminho - caminho do arquivo mapconect
     * @return - Lista do tipo DadosBancos
     * @throws Exception - Gera exception pela de acesso ao arquivo
     */
    public List<DadosBancos_OLD> MapeiaPoolWeb(String caminho) throws Exception {
        String linha;
        List<DadosBancos_OLD> pool = new ArrayList();
        try {
            BufferedReader leitor;
            // instancia do arquivo que vou ler  
            try {
                FileReader reader = new FileReader(caminho);
                leitor = new BufferedReader(reader);
            } catch (Exception e) {
                InputStream in = getClass().getResourceAsStream(caminho);
                Reader reader = new java.io.InputStreamReader(in);
                leitor = new BufferedReader(reader);
            }
// loop que percorrerá todas as  linhas do arquivo.txt que eu quero ler  
            linha = leitor.readLine();
            while (linha != null) {
                DadosBancos_OLD bd = new DadosBancos_OLD();
//No metodo StringTokenizer passo os parametros que quero ler, em seguida o que eu quero descartar no meu caso( ; ).   
                StringTokenizer st = new StringTokenizer(linha, "}");
                Empresa = st.nextToken();
                ip = st.nextToken();
                login = st.nextToken();
                senha = st.nextToken();
                bd.setEmpresa(Empresa);
                bd.setIP(ip);
                bd.setLogin(login);
                bd.setSenha(senha);
                pool.add(bd);
                linha = leitor.readLine();
            }
            leitor.close();
            return pool;
        } catch (Exception e) {
            throw new Exception("Erro ao ler mapconect - " + e.getMessage());
        }
    }

    public String getIp() {
        return ip;
    }

    public String getLogin() {
        return login;
    }

    public String getSenha() {
        return senha;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">

    <ui:insert name="metadata"/>

    <h:head>
        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
        <meta name="apple-mobile-web-app-capable" content="yes"/>
        <meta name="mobile-web-app-capable" content="yes"/>
        <ui:insert name="head"/>
        <ui:insert name="head-end"/>
        <h:outputScript library="primefaces" name="jquery/jquery.js" target="head"/>
        <script src="../assets/js/bootstrap.min.js" type="text/javascript"></script>
        <script src="../assets/js/admin-lte.min.js" type="text/javascript"></script>
        <script src="../assets/js/admintemplate.js" type="text/javascript"></script>
        <script src="../assets/js/slimscroll.min.js" type="text/javascript"></script>
        <script src="../assets/js/slideout.min.js" type="text/javascript"></script>
        <script src="../assets/js/adminslide.js" type="text/javascript"></script> 

    </h:head>

    <h:body styleClass="skin-black-light sidebar-mini">
        <ui:insert name="body-begin"/>
        <p:ajaxStatus onstart="showBar();"
                      onsuccess="hideBar();"/>

        <p:dialog id="adminStatusDialog" modal="true" widgetVar="statusDialog" draggable="false" closable="false"
                  resizable="false" responsive="true" showHeader="false" appendTo="@(body)">
            <p:graphicImage url="../assets/images/ajaxloadingbar.gif"/>
        </p:dialog>

        <div class="wrapper">

            <header class="main-header">
                <ui:insert name="template-header"/>
            </header>

            <ui:insert name="template-menu"/>
            <!-- content -->
            <h:panelGroup layout="block" id="content" styleClass="content-wrapper">
                <ui:insert name="content-wrapper"/>
                <section class="content">
                    <ui:insert name="content-begin"/>
                    <ui:fragment>
                        <div class="row">
                            <div class="col-sm-12">
                                <p:messages id="messages" closable="true" globalOnly="true" 
                                            showDetail="true" severity="error,fatal" escape="false">
                                    <p:autoUpdate/>
                                </p:messages>
                                <!-- we need two messages because info-messages are automatically hidden via javascript  -->
                                <p:messages id="info-messages" closable="true" showDetail="true" 
                                            severity="info,warn" escape="false">
                                    <p:autoUpdate/>
                                </p:messages>
                                <p:tooltip/> <!-- exception messages with type tooltip -->
                            </div>
                        </div>
                    </ui:fragment>

                    <ui:insert name="body"/>
                </section>
                <ui:insert name="content-end"/>
            </h:panelGroup>

            <footer class="main-footer">
                <ui:insert name="footer"/>
            </footer>
            <div id="scrollTop" class="ripplelink scroll-top" onclick="window.scrollTo(0, 0)">
                <i class="fa fa-chevron-up"></i>
            </div>

            <ui:insert name="wrapper-end"/>
            <ui:insert name="wrapper-end"/>
        </div>

        <ui:insert name="after-wrapper"/>

        <script type="text/javascript">
            if (isMobile()) {
                window.onscroll = activateScrollToTop;
            }
        </script>


        <ui:fragment>
            <script type="text/javascript">
                $(window).scroll(function () {
                    if (isMobile()) {
                        var minScrollTimeNav = 700;
                        var now = new Date().getTime();
                        if (scrollTimerNav) {
                            if ((now - lastScrollFireTimeNav) > (5 * minScrollTimeNav)) {
                                activateAutoShowNavbarOnScrollUp();   // fire immediately on first scroll
                                lastScrollFireTimeNav = now;
                            }
                        } else {
                            scrollTimerNav = setTimeout(function () {
                                scrollTimerNav = null;
                                lastScrollFireTimeNav = new Date().getTime();
                                activateAutoShowNavbarOnScrollUp();
                            }, minScrollTimeNav);
                        }
                    }
                });
            </script>
        </ui:fragment>

        <ui:fragment>

            <script type="text/javascript">
                //<![CDATA[
                /* hide info messages */
                $(document).on("pfAjaxComplete", function () {
                    var $messages = $("div[id$='info-messages']");

                    if ($messages.length) {
                        var wordCount = $messages.text().split(/\W/).length;
                        var readingTimeMillis = 10 + (wordCount * 200);

                        setTimeout(function () {
                            $messages.slideUp();
                        }, readingTimeMillis);
                    }

                });

                $(document).ready(function () {
                    var $messages = $("div[id$='info-messages']");

                    if ($messages.length) {
                        var wordCount = $messages.text().split(/\W/).length;
                        var readingTimeMillis = 10 + (wordCount * 200);

                        setTimeout(function () {
                            $messages.slideUp();
                        }, readingTimeMillis);
                    }

                });
                //]]>
            </script>
        </ui:fragment>

        <ui:fragment>
            <div id="enableMobileHeader"></div>
        </ui:fragment>

        <ui:fragment>
            <script type="text/javascript">
                $(document).ready(function () {
                    activateRippleIcons();
                });

                $(document).on("pfAjaxComplete", function () {
                    activateRippleIcons();
                });
            </script>
        </ui:fragment>

        <ui:fragment>
            <style type="text/css">
                body .ui-outputlabel-rfi {
                    display: inline;
                }
            </style>
        </ui:fragment>

        <ui:fragment>
            <style type="text/css">
                #adminStatusDialog .ui-dialog-titlebar {
                    padding: 0;
                    border: 0;
                }
            </style>
        </ui:fragment>

        <ui:insert name="body-end"/>
    </h:body>

</html>

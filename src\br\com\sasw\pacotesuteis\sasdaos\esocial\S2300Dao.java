/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2300;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2300Dao {

    public List<S2300> get(String codFil, String compet, String ambiente, String cadini, String tipo, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select "
                    + " (Select top 01 substring(xml_retorno,  charindex('<nrRecibo>',xml_retorno)+10, "
                    + "    charindex('</nrRecibo>',xml_retorno) - charindex('<nrRecibo>',xml_retorno)-10) ideEvento_nrRecibo "
                    + "    From XmleSocial z "
                    + "    where z.Identificador = Funcion.CPF "
                    + "                     and z.evento = 'S-2300'"
                    + "                     and z.CodFil = ? "
                    //+ "                     and z.Compet = ? "
                    + "                     and z.Ambiente = ? "
                    + "                     and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') "
                    + " Order by Sequencia Desc) ideEvento_nrRecibo, "
                    + "funcion.PgCtSin ,RHHorario.tipocomp, RHHorario.chcomp, Funcion.CPF trabalhador_cpfTrab, "
                    + " Funcion.PIS trabalhador_nisTrab, Funcion.Nome trabalhador_nmTrab, "
                    + " Funcion.Sexo trabalhador_sexo, Funcion.Raca trabalhador_racaCor,  Funcion.EstCivil trabalhador_estCiv, "
                    + " Funcion.Instrucao trabalhador_grauInstr, Funcion.TipoAdm trabalhador_indPriEmpr, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto, "
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie CTPS_serieCtps, "
                    + " Funcion.CTPS_UF CTPS_ufCtps, Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, "
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, "
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, "
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, "
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, Convert(BigInt,Municipios.CodIBGE) brasil_codMunic, "
                    + " Funcion.UF brasil_uf, Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, Funcion.Email contato_emailPrinc, "
                    + " Funcion.Matr vinculo_matricula, substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoTSVInicio_dtInicio, "
                    + " Fornec.CNPJ infoCeletista_cnpjSindCategProf, Funcion.Salario remuneracao_vrSalFx,  Funcion.FormaPgto remuneracao_undSalFixo, "
                    + " Clientes.CGC localTrabGeral_nrInsc, Cargos.Descricao cargoFuncao_codCargo, Cargos.CBO,  RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, "
                    + " RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, RHEscala.HrSabNot, RHEscala.HrDomDiu, "
                    + " RHEscala.HrDomNot, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, RHHorario.D1, "
                    + " RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7, Funcion.Situacao, "
                    + " substring(replace(convert(varchar,Funcion.Dt_Demis,111),'/','-'),0,11) desligamento_dtDesligamento, "
                    + " Case when Funcion.TipoADM = 70 then 2 else 1 end infoCeletista_tpAdmissao,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc,"
                    + " FuncionAdic.NatEstagio infoEstagiario_natEstagio, FuncionAdic.NivelEstagio infoEstagiario_nivEstagio, "
                    + " substring(replace(convert(varchar,FuncionAdic.DtFimEstagio,111),'/','-'),0,11) infoEstagiario_dtPrevTerm, "
                    + " instEnsino.CNPJ instEnsino_cnpjInstEnsino, instEnsino.Empresa instEnsino_nmRazao, instEnsino.Endereco instEnsino_dscLograd, "
                    + " instEnsino.Bairro instEnsino_bairro, instEnsino.CEP instEnsino_cep, instEnsino.CodCidade instEnsino_codMunic, "
                    + " instEnsino.UF instEnsino_uf, "
                    + " AgenteIntegrador.CNPJ ageIntegracao_cnpjAgntInteg, AgenteIntegrador.Empresa ageIntegracao_nmRazao, AgenteIntegrador.Endereco ageIntegracao_dscLograd, "
                    + " AgenteIntegrador.Bairro ageIntegracao_bairro, AgenteIntegrador.CEP ageIntegracao_cep, AgenteIntegrador.CodCidade ageIntegracao_codMunic, "
                    + " AgenteIntegrador.UF ageIntegracao_uf, "
                    + " SupEstagio.CPF supervisorEstagio_cpfSupervisor, SupEstagio.Nome supervisorEstagio_nmSuperv, "
                    + " Case when (CharIndex('/',Funcion.Naturalid) > 1) then"
                    + "                       (Select Convert(BigInt,Max(CodIBGE)) From Municipios where Nome = Substring(Funcion.Naturalid,1,(CharIndex('/',Funcion.Naturalid)-1)) "
                    + "                                                         and UF = Substring(Funcion.Naturalid,(CharIndex('/',Funcion.Naturalid)+1),2) and Len(Funcion.Naturalid) > 0) "
                    + "                    else Convert(BigInt,0) end nascimento_codMunic, "
                    + "                    Case when (CharIndex('/',Funcion.Naturalid) > 1) then"
                    + "                    (Select Substring(Funcion.Naturalid,(CharIndex('/',Funcion.Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) "
                    + " else '0' end nascimento_uf, "
                    + " Case when Funcion.vinculo = 'D' then '721' "
                    + "      when Funcion.Vinculo = 'S' and Funcion.TipoADM = 80 then '722' "                    
                    + "      when Funcion.Vinculo = 'S' then '723' "            
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + " end infoTSVInicio_codCateg, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Funcion.CPF "
                    + "             and z.evento = 'S-2300' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.CPF "
                    + "             and z.evento = 'S-2300' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Funcion.CPF "
                    + "             and z.evento = 'S-2300' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'"
                    + "                  or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso "
                    + " from FPMensal "
                    + " Left Join Funcion  on Funcion.Matr = FPMensal.Matr "
                    + " Left Join Cargos   on Cargos.Codigo = Funcion.CodCargo "
                    + " Left Join Sindicatos  on Sindicatos.Codigo = FPMensal.Sindicato "
                    + " Left Join PstServ  on PstServ.Secao  = FPMensal.Secao "
                    + "                   and PstServ.CodFil = FPMensal.CodFil "
                    + " Left Join Clientes  on Clientes.Codigo = PstServ.CodCli "
                    + "                    and Clientes.CodFil = PstServ.CodFil "
                    + " Left Join RHEscala  on RHEscala.Codigo = FPMEnsal.Escala "
                    + "                    and RHEscala.CodFil = FPMEnsal.CodFil "
                    + " Left Join RHHorario  on  RHHorario.Codigo    = FPMensal.Horario "
                    + "                      and RHHorario.CodFil    = FPMensal.CodFil "
                    + " Left Join Filiais on Filiais.codFil = FPMensal.CodFil"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade "
                    + "                     and Municipios.UF   = Funcion.UF"
                    + " Left Join Fornec on Fornec.Codigo = Sindicatos.CodForn "
                    + " Left Join FuncionAdic  on FuncionAdic.Matr = Funcion.Matr "
                    + " Left Join Fornec instEnsino  on instEnsino.Codigo = FuncionAdic.InstEnsino "
                    + " Left Join Fornec AgenteIntegrador  on AgenteIntegrador.Codigo = FuncionAdic.AgenteIntegrador "
                    + " Left Join Pessoa SupEstagio  on SupEstagio.Codigo = FuncionAdic.SupEstagio "
                    + " where TipoFP in ('MEN','AUT','EST') "
                    + " and Funcion.codfil = ? "
                    + " and FPMensal.situacao <> 'D' "
                    + " and Funcion.Vinculo in('D','E','S','A') "
                    + " and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    //                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate())) "
                    + "                             from FPPeriodos where DtInicio = ?) "
                    + " ORDER BY sucesso asc, Funcion.Matr asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet + "-01");
            consulta.select();
            List<S2300> retorno = new ArrayList<>();
            S2300 S2300;
            while (consulta.Proximo()) {
                S2300 = new S2300();
                S2300.setSucesso(consulta.getInt("sucesso"));
                S2300.setIdeEvento_procEmi("1");
                S2300.setIdeEvento_verProc("Satellite eSocial");

                //S2300.setEvtTSVInicio_Id(consulta.getString("evtTSVInicio_Id"));
                S2300.setIdeEvento_indRetif("1");
                S2300.setIdeEvento_nrRecibo(consulta.getString("ideEvento_nrRecibo"));
                S2300.setIdeEvento_tpAmb(ambiente);
                S2300.setIdeEvento_procEmi("1");
                S2300.setIdeEvento_verProc("Satellite eSocial");

                S2300.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                S2300.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                S2300.setTrabalhador_cpfTrab(consulta.getString("trabalhador_cpfTrab"));
                S2300.setTrabalhador_nisTrab(consulta.getString("trabalhador_nisTrab"));
                S2300.setTrabalhador_nmTrab(consulta.getString("trabalhador_nmTrab"));
                S2300.setTrabalhador_sexo(consulta.getString("trabalhador_sexo"));
                S2300.setTrabalhador_racaCor(consulta.getString("trabalhador_racaCor"));
                S2300.setTrabalhador_estCiv(consulta.getString("trabalhador_estCiv"));
                S2300.setTrabalhador_grauInstr(consulta.getString("trabalhador_grauInstr"));
//                S2300.setTrabalhador_nmSoc(consulta.getString("trabalhador_nmSoc"));
                S2300.setNascimento_dtNascto(consulta.getString("nascimento_dtNascto"));
                S2300.setNascimento_codMunic(consulta.getString("nascimento_codMunic"));
                S2300.setNascimento_uf(consulta.getString("nascimento_uf"));
                S2300.setNascimento_paisNascto("105");
                S2300.setNascimento_paisNac("105");
                S2300.setNascimento_nmMae(consulta.getString("nascimento_nmMae"));
                S2300.setNascimento_nmPai(consulta.getString("nascimento_nmPai"));
                S2300.setCTPS_nrCtps(consulta.getString("CTPS_nrCtps"));
                S2300.setCTPS_serieCtps(consulta.getString("CTPS_serieCtps"));
                S2300.setCTPS_ufCtps(consulta.getString("CTPS_ufCtps"));
//                S2300.setRIC_nrRic(consulta.getString("RIC_nrRic"));
//                S2300.setRIC_orgaoEmissor(consulta.getString("RIC_orgaoEmissor"));
//                S2300.setRIC_dtExped(consulta.getString("RIC_dtExped"));
                S2300.setRG_nrRg(consulta.getString("RG_nrRg"));
                S2300.setRG_orgaoEmissor(consulta.getString("RG_orgaoEmissor"));
                S2300.setRG_dtExped(consulta.getString("RG_dtExped"));
//                S2300.setRNE_nrRne(consulta.getString("RNE_nrRne"));
//                S2300.setRNE_orgaoEmissor(consulta.getString("RNE_orgaoEmissor"));
//                S2300.setRNE_dtExped(consulta.getString("RNE_dtExped"));
//                S2300.setOC_nrOc(consulta.getString("OC_nrOc"));
//                S2300.setOC_orgaoEmissor(consulta.getString("OC_orgaoEmissor"));
//                S2300.setOC_dtExped(consulta.getString("OC_dtExped"));
//                S2300.setOC_dtValid(consulta.getString("OC_dtValid"));
                S2300.setCNH_nrRegCnh(consulta.getString("CNH_nrRegCnh"));
//                S2300.setCNH_dtExped(consulta.getString("CNH_dtExped"));
                S2300.setCNH_ufCnh(consulta.getString("CNH_ufCnh"));
                S2300.setCNH_dtValid(consulta.getString("CNH_dtValid"));
//                S2300.setCNH_dtPriHab(consulta.getString("CNH_dtPriHab"));
                S2300.setCNH_categoriaCnh(consulta.getString("CNH_categoriaCnh"));
//                S2300.setBrasil_tpLograd(consulta.getString("brasil_tpLograd"));
                S2300.setBrasil_dscLograd(consulta.getString("brasil_dscLograd"));
                S2300.setBrasil_nrLograd(consulta.getString("brasil_nrLograd"));
                S2300.setBrasil_complemento(consulta.getString("brasil_complemento"));
                S2300.setBrasil_bairro(consulta.getString("brasil_bairro"));
                S2300.setBrasil_cep(consulta.getString("brasil_cep"));
                S2300.setBrasil_codMunic(consulta.getString("brasil_codMunic"));
                S2300.setBrasil_uf(consulta.getString("brasil_uf"));
//                S2300.setExterior_paisResid(consulta.getString("exterior_paisResid"));
//                S2300.setExterior_dscLograd(consulta.getString("exterior_dscLograd"));
//                S2300.setExterior_nrLograd(consulta.getString("exterior_nrLograd"));
//                S2300.setExterior_complemento(consulta.getString("exterior_complemento"));
//                S2300.setExterior_bairro(consulta.getString("exterior_bairro"));
//                S2300.setExterior_nmCid(consulta.getString("exterior_nmCid"));
//                S2300.setExterior_codPostal(consulta.getString("exterior_codPostal"));
//                S2300.setTrabEstrangeiro_dtChegada(consulta.getString("trabEstrangeiro_dtChegada"));
//                S2300.setTrabEstrangeiro_classTrabEstrang(consulta.getString("trabEstrangeiro_classTrabEstrang"));
//                S2300.setTrabEstrangeiro_casadoBr(consulta.getString("trabEstrangeiro_casadoBr"));
//                S2300.setTrabEstrangeiro_filhosBr(consulta.getString("trabEstrangeiro_filhosBr"));
//                S2300.setInfoDeficiencia_defFisica(consulta.getString("infoDeficiencia_defFisica"));
//                S2300.setInfoDeficiencia_defVisual(consulta.getString("infoDeficiencia_defVisual"));
//                S2300.setInfoDeficiencia_defAuditiva(consulta.getString("infoDeficiencia_defAuditiva"));
//                S2300.setInfoDeficiencia_defMental(consulta.getString("infoDeficiencia_defMental"));
//                S2300.setInfoDeficiencia_defIntelectual(consulta.getString("infoDeficiencia_defIntelectual"));
//                S2300.setInfoDeficiencia_reabReadap(consulta.getString("infoDeficiencia_reabReadap"));
//                S2300.setInfoDeficiencia_observacao(consulta.getString("infoDeficiencia_observacao"));
//                S2300.setDependente_tpDep(consulta.getString("dependente_tpDep"));
//                S2300.setDependente_nmDep(consulta.getString("dependente_nmDep"));
//                S2300.setDependente_dtNascto(consulta.getString("dependente_dtNascto"));
//                S2300.setDependente_cpfDep(consulta.getString("dependente_cpfDep"));
//                S2300.setDependente_depIRRF(consulta.getString("dependente_depIRRF"));
//                S2300.setDependente_depSF(consulta.getString("dependente_depSF"));
//                S2300.setDependente_incTrab(consulta.getString("dependente_incTrab"));
                S2300.setContato_fonePrinc(consulta.getString("contato_fonePrinc"));
                S2300.setContato_foneAlternat(consulta.getString("contato_foneAlternat"));
                S2300.setContato_emailPrinc(consulta.getString("contato_emailPrinc"));
//                S2300.setContato_emailAlternat(consulta.getString("contato_emailAlternat"));
                S2300.setInfoTSVInicio_cadIni(cadini);
                S2300.setInfoTSVInicio_codCateg(consulta.getString("infoTSVInicio_codCateg"));
                S2300.setInfoTSVInicio_dtInicio(consulta.getString("infoTSVInicio_dtInicio"));
                S2300.setInfoTSVInicio_natAtividade("1");
                S2300.setCargoFuncao_codCargo(consulta.getString("cargoFuncao_codCargo"));
//                S2300.setCargoFuncao_codFuncao(consulta.getString("cargoFuncao_codFuncao"));
                S2300.setRemuneracao_vrSalFx(consulta.getString("remuneracao_vrSalFx"));
                S2300.setRemuneracao_undSalFixo(consulta.getString("remuneracao_undSalFixo"));
                //S2300.setRemuneracao_dscSalVar(consulta.getString("remuneracao_dscSalVar"));
                S2300.setFgts_opcFGTS("1");
                S2300.setFgts_dtOpcFGTS(consulta.getString("infoTSVInicio_dtInicio"));

//                S2300.setInfoDirigenteSindical_categOrig(consulta.getString("infoDirigenteSindical_categOrig"));
//                S2300.setInfoDirigenteSindical_cnpjOrigem(consulta.getString("infoDirigenteSindical_cnpjOrigem"));
//                S2300.setInfoDirigenteSindical_dtAdmOrig(consulta.getString("infoDirigenteSindical_dtAdmOrig"));
//                S2300.setInfoDirigenteSindical_matricOrig(consulta.getString("infoDirigenteSindical_matricOrig"));
//                S2300.setInfoTrabCedido_categOrig(consulta.getString("infoTrabCedido_categOrig"));
//                S2300.setInfoTrabCedido_cnpjCednt(consulta.getString("infoTrabCedido_cnpjCednt"));
//                S2300.setInfoTrabCedido_matricCed(consulta.getString("infoTrabCedido_matricCed"));
//                S2300.setInfoTrabCedido_dtAdmCed(consulta.getString("infoTrabCedido_dtAdmCed"));
//                S2300.setInfoTrabCedido_tpRegTrab(consulta.getString("infoTrabCedido_tpRegTrab"));
//                S2300.setInfoTrabCedido_tpRegPrev(consulta.getString("infoTrabCedido_tpRegPrev"));
//                S2300.setInfoTrabCedido_infOnus(consulta.getString("infoTrabCedido_infOnus"));
                S2300.setInfoEstagiario_natEstagio(consulta.getString("infoEstagiario_natEstagio"));
                S2300.setInfoEstagiario_nivEstagio(consulta.getString("infoEstagiario_nivEstagio"));
//                S2300.setInfoEstagiario_areaAtuacao(consulta.getString("infoEstagiario_areaAtuacao"));
//                S2300.setInfoEstagiario_nrApol(consulta.getString("infoEstagiario_nrApol"));
                S2300.setInfoEstagiario_vlrBolsa(consulta.getString("remuneracao_vrSalFx"));
                S2300.setInfoEstagiario_dtPrevTerm(consulta.getString("infoEstagiario_dtPrevTerm"));
                S2300.setInstEnsino_cnpjInstEnsino(consulta.getString("instEnsino_cnpjInstEnsino"));
                S2300.setInstEnsino_nmRazao(consulta.getString("instEnsino_nmRazao"));
                S2300.setInstEnsino_dscLograd(consulta.getString("instEnsino_dscLograd"));
//                S2300.setInstEnsino_nrLograd(consulta.getString("instEnsino_nrLograd"));
                S2300.setInstEnsino_bairro(consulta.getString("instEnsino_bairro"));
                S2300.setInstEnsino_cep(consulta.getString("instEnsino_cep"));
                S2300.setInstEnsino_codMunic(consulta.getString("instEnsino_codMunic"));
                S2300.setInstEnsino_uf(consulta.getString("instEnsino_uf"));
                S2300.setAgeIntegracao_cnpjAgntInteg(consulta.getString("ageIntegracao_cnpjAgntInteg"));
                S2300.setAgeIntegracao_nmRazao(consulta.getString("ageIntegracao_nmRazao"));
                S2300.setAgeIntegracao_dscLograd(consulta.getString("ageIntegracao_dscLograd"));
//                S2300.setAgeIntegracao_nrLograd(consulta.getString("ageIntegracao_nrLograd"));
                S2300.setAgeIntegracao_bairro(consulta.getString("ageIntegracao_bairro"));
                S2300.setAgeIntegracao_cep(consulta.getString("ageIntegracao_cep"));
                S2300.setAgeIntegracao_codMunic(consulta.getString("ageIntegracao_codMunic"));
                S2300.setAgeIntegracao_uf(consulta.getString("ageIntegracao_uf"));
                S2300.setSupervisorEstagio_cpfSupervisor(consulta.getString("supervisorEstagio_cpfSupervisor"));
                S2300.setSupervisorEstagio_nmSuperv(consulta.getString("supervisorEstagio_nmSuperv"));
//                S2300.setAfastamento_dtIniAfast(consulta.getString("afastamento_dtIniAfast"));
//                S2300.setAfastamento_codMotAfast(consulta.getString("afastamento_codMotAfast"));
//                S2300.setTermino_dtTerm(consulta.getString("termino_dtTerm"));

                retorno.add(S2300);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2300Dao.getTransferencias - " + e.getMessage());
        }
    }
}

/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 23-Aug-2018, 16:18:30
    Author     : SASW
*/

.jqplot-title{
    color: #FFF;
}
.leftmenu{
    left: 0;
    float: left;
    position: fixed;
    z-index: 999;
    background-color: #1b6d85;
    background-size: contain;
    background-position: center center;
    height: 100%;
    padding: 5px;
}
.left_content{
    padding: 5px;
    margin-left: 170px;
}
.centro{
    margin-top: 60px;
}
#top{
    background: url("../img/bk.jpg") no-repeat;
    top: 0;
    position: fixed;
    left: 0;
    width: 100%;
    z-index: 1000;
    height: auto;
    background-size: cover;
}
#logo{
    size: 50px;
    margin-left: auto;
    margin-right: auto;
    display: block;

}
.ui-menu.ui-menuitem{
    color : red;
    background: #85b2cb;
    -webkit-box-shadow: 0px 0px 8px #85b2cb;
    box-shadow: 0px 0px 8px #85b2cb;
}
.calendario .ui-state-active, .calendario .ui-state-highlight{
    background-color: #022a48 !important;
    color: white !important;
}
.ui-icon-clock{
    color: white !important;
}
/*telasmenores*/

@media screen and (min-width:1320px){
    .leftmenu{
        height: 570px;
        left: 0;
        float: left;
        position: fixed;
        background-color: #1b6d85;
        background-size: contain;
        min-height: 100%;
        height: 590px;
        padding: 5px;
    }
}
@media screen and (min-width:1420px){
    .leftmenu{
        left: 0;
        float: left;
        position: fixed;
        background-color: #1b6d85;
        background-size: contain;
        min-height: 100%;
        height: 723.5px;
        padding: 5px;
    }
}
.ui-selectcheckboxmenu-multiple{
    width: 350px;
}  
.ui-selectcheckboxmenu .ui-selectcheckboxmenu-token.ui-state-active,
.ui-autocomplete .ui-autocomplete-token.ui-state-active,
.ui-chips .ui-chips-token.ui-state-active{
    background: #ccccff;
    color: #000;
}
.ui-selectcheckboxmenu-header .ui-selectcheckboxmenu-filter-container {
    float: left;
    position: relative;
    margin-left: 5px;
}

.ui-selectcheckboxmenu-header .ui-selectcheckboxmenu-filter-container .ui-icon{
    position: absolute;
    right: 5px;
    top: 2px;
}

.ui-selectcheckboxmenu-header .ui-inputfield {
    padding: 1px 180px 1px 2px;
}

 .ui-widget-header .ui-chkbox{
    margin-top: 5px;
}
.ui-selectcheckboxmenu .ui-selectcheckboxmenu-trigger {
    border-right: none;
    border-top: none;
    border-bottom: none;
    cursor: pointer;
    width: 16px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 3px 0 0;
}

.selectcheckboxmenu-trigger {
    border-color: #ffffff;
}

.dialogo{
    top: 100px !important;
}
﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtConvInterm/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtConvInterm/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtConvInterm">
                    <xs:annotation>
                        <xs:documentation>Evento Convocacao para Trabalho Intermitente</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideVinculo" type="TIdeVinculoNisObrig">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Trabalhador e do Vinculo</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoConvInterm">
                                <xs:annotation>
                                    <xs:documentation>Informacoes da convocacao para trabalho intermitente</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="codConv">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo que identifica a convocacao para trabalho intermitente</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="2"/>
                                                    <xs:maxLength value="30"/>
                                                    <xs:whiteSpace value="preserve"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dtInicio">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data de Inicio</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dtFim">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data Fim</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dtPrevPgto">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data prevista para o pagamento da remuneracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="jornada">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes da(s) jornada(s) diaria(s) da prestacao de trabalho intermitente</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="codHorContrat" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Horario Contratual</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dscJornada" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Descricao das jornadas de trabalho intermitente</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="999"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="localTrab">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do local da prestacao de trabalho intermitente</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="indLocal">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Indicativo do local da prestacao de trabalho intermitente</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="localTrabInterm" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do local de trabalho intermitente</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpLograd">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de Logradouro</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="4"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dscLograd">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Descricao do logradouro</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="100"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrLograd">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Número do logradouro</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="10"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="complem" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Complemento</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="30"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="bairro" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Nome do bairro/distrito</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="90"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="cep">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo de Enderecamento Postal - CEP</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="\d{8}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codMunic">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo do Municipio</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{7}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="uf">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Sigla da UF</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:enumeration value="AC"/>
                                                                            <xs:enumeration value="AL"/>
                                                                            <xs:enumeration value="AP"/>
                                                                            <xs:enumeration value="AM"/>
                                                                            <xs:enumeration value="BA"/>
                                                                            <xs:enumeration value="CE"/>
                                                                            <xs:enumeration value="DF"/>
                                                                            <xs:enumeration value="ES"/>
                                                                            <xs:enumeration value="GO"/>
                                                                            <xs:enumeration value="MA"/>
                                                                            <xs:enumeration value="MT"/>
                                                                            <xs:enumeration value="MS"/>
                                                                            <xs:enumeration value="MG"/>
                                                                            <xs:enumeration value="PA"/>
                                                                            <xs:enumeration value="PB"/>
                                                                            <xs:enumeration value="PR"/>
                                                                            <xs:enumeration value="PE"/>
                                                                            <xs:enumeration value="PI"/>
                                                                            <xs:enumeration value="RJ"/>
                                                                            <xs:enumeration value="RN"/>
                                                                            <xs:enumeration value="RS"/>
                                                                            <xs:enumeration value="RO"/>
                                                                            <xs:enumeration value="RR"/>
                                                                            <xs:enumeration value="SC"/>
                                                                            <xs:enumeration value="SP"/>
                                                                            <xs:enumeration value="SE"/>
                                                                            <xs:enumeration value="TO"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeVinculoNisObrig">
        <xs:annotation>
            <xs:documentation>Informacoes do Vinculo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cpfTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>CPF do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="11"/>
                        <xs:pattern value="\d{11}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nisTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>NIS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="11"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="matricula">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Matricula</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

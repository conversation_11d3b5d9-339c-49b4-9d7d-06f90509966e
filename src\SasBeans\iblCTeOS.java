/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class iblCTeOS {
    public String chaveCTEOS;
    public String CFOP;
    public String CFOPDescr;
    public String natOp;
    public String serie;
    public String nCT;
    public String dhEmi;
    public String cMunIni;
    public String xMunIni;
    public String UFIni;
    public String cMunFin;
    public String xMunFin;
    public String UFFin;
    public String xObs;
    public String vTPrest;
    public String vRec;
    public String vBC;
    public String pICMS;
    public String vICMS;
    public String vPIS;
    public String vCOFINS;
    public String vIR;
    public String vCSLL;
    public String vINSS;
    public String vOutrasRet;
    public String vISSQN;
    public String desconto;
    public String aliquota;
    public String valor;
    public String valorLiq;
    public String valorUnitario;
    public String valorTotal;
    public String historico;
    public String descrLinhas;
    public String nfeRetorno;
    public iblCTeOS_Cliente emit;
    public iblCTeOS_Cliente toma;
    public iblCTeOS_infGTV InfGTV;

    public String getChaveCTEOS() {
        return chaveCTEOS;
    }

    public void setChaveCTEOS(String chaveCTEOS) {
        this.chaveCTEOS = chaveCTEOS;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public String getNatOp() {
        return natOp;
    }

    public void setNatOp(String natOp) {
        this.natOp = natOp;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getnCT() {
        return nCT;
    }

    public void setnCT(String nCT) {
        this.nCT = nCT;
    }

    public String getDhEmi() {
        return dhEmi;
    }

    public void setDhEmi(String dhEmi) {
        this.dhEmi = dhEmi;
    }

    public String getcMunIni() {
        return cMunIni;
    }

    public void setcMunIni(String cMunIni) {
        this.cMunIni = cMunIni;
    }

    public String getxMunIni() {
        return xMunIni;
    }

    public void setxMunIni(String xMunIni) {
        this.xMunIni = xMunIni;
    }

    public String getUFIni() {
        return UFIni;
    }

    public void setUFIni(String UFIni) {
        this.UFIni = UFIni;
    }

    public String getcMunFin() {
        return cMunFin;
    }

    public void setcMunFin(String cMunFin) {
        this.cMunFin = cMunFin;
    }

    public String getxMunFin() {
        return xMunFin;
    }

    public void setxMunFin(String xMunFin) {
        this.xMunFin = xMunFin;
    }

    public String getUFFin() {
        return UFFin;
    }

    public void setUFFin(String UFFin) {
        this.UFFin = UFFin;
    }

    public String getxObs() {
        return xObs;
    }

    public void setxObs(String xObs) {
        this.xObs = xObs;
    }

    public String getvTPrest() {
        return vTPrest;
    }

    public void setvTPrest(String vTPrest) {
        this.vTPrest = vTPrest;
    }

    public String getvRec() {
        return vRec;
    }

    public void setvRec(String vRec) {
        this.vRec = vRec;
    }

    public String getvBC() {
        return vBC;
    }

    public void setvBC(String vBC) {
        this.vBC = vBC;
    }

    public String getpICMS() {
        return pICMS;
    }

    public void setpICMS(String pICMS) {
        this.pICMS = pICMS;
    }

    public String getvICMS() {
        return vICMS;
    }

    public void setvICMS(String vICMS) {
        this.vICMS = vICMS;
    }

    public String getvPIS() {
        return vPIS;
    }

    public void setvPIS(String vPIS) {
        this.vPIS = vPIS;
    }

    public String getvCOFINS() {
        return vCOFINS;
    }

    public void setvCOFINS(String vCOFINS) {
        this.vCOFINS = vCOFINS;
    }

    public String getvIR() {
        return vIR;
    }

    public void setvIR(String vIR) {
        this.vIR = vIR;
    }

    public String getvCSLL() {
        return vCSLL;
    }

    public void setvCSLL(String vCSLL) {
        this.vCSLL = vCSLL;
    }

    public iblCTeOS_Cliente getEmit() {
        return emit;
    }

    public void setEmit(iblCTeOS_Cliente emit) {
        this.emit = emit;
    }

    public iblCTeOS_Cliente getToma() {
        return toma;
    }

    public void setToma(iblCTeOS_Cliente toma) {
        this.toma = toma;
    }

    public iblCTeOS_infGTV getInfGTV() {
        return InfGTV;
    }

    public void setInfGTV(iblCTeOS_infGTV InfGTV) {
        this.InfGTV = InfGTV;
    }

    public String getCFOPDescr() {
        return CFOPDescr;
    }

    public void setCFOPDescr(String CFOPDescr) {
        this.CFOPDescr = CFOPDescr;
    }

    public String getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(String valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getvINSS() {
        return vINSS;
    }

    public void setvINSS(String vINSS) {
        this.vINSS = vINSS;
    }

    public String getvOutrasRet() {
        return vOutrasRet;
    }

    public void setvOutrasRet(String vOutrasRet) {
        this.vOutrasRet = vOutrasRet;
    }

    public String getvISSQN() {
        return vISSQN;
    }

    public void setvISSQN(String vISSQN) {
        this.vISSQN = vISSQN;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getValorLiq() {
        return valorLiq;
    }

    public void setValorLiq(String valorLiq) {
        this.valorLiq = valorLiq;
    }

    public String getAliquota() {
        return aliquota;
    }

    public void setAliquota(String aliquota) {
        this.aliquota = aliquota;
    }

    public String getDesconto() {
        return desconto;
    }

    public void setDesconto(String desconto) {
        this.desconto = desconto;
    }

    public String getHistorico() {
        return historico;
    }

    public void setHistorico(String historico) {
        this.historico = historico;
    }

    public String getNfeRetorno() {
        return nfeRetorno;
    }

    public void setNfeRetorno(String nfeRetorno) {
        this.nfeRetorno = nfeRetorno;
    }

    public String getDescrLinhas() {
        return descrLinhas;
    }

    public void setDescrLinhas(String descrLinhas) {
        this.descrLinhas = descrLinhas;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S2190 {

    private int sucesso;
    private String evtAdmPrelim_Id;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String infoRegPrelim_cpfTrab;
    private String infoRegPrelim_dtNascto;
    private String infoRegPrelim_dtAdm;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtAdmPrelim_Id() {
        return evtAdmPrelim_Id;
    }

    public void setEvtAdmPrelim_Id(String evtAdmPrelim_Id) {
        this.evtAdmPrelim_Id = evtAdmPrelim_Id;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getInfoRegPrelim_cpfTrab() {
        return infoRegPrelim_cpfTrab;
    }

    public void setInfoRegPrelim_cpfTrab(String infoRegPrelim_cpfTrab) {
        this.infoRegPrelim_cpfTrab = infoRegPrelim_cpfTrab;
    }

    public String getInfoRegPrelim_dtNascto() {
        return infoRegPrelim_dtNascto;
    }

    public void setInfoRegPrelim_dtNascto(String infoRegPrelim_dtNascto) {
        this.infoRegPrelim_dtNascto = infoRegPrelim_dtNascto;
    }

    public String getInfoRegPrelim_dtAdm() {
        return infoRegPrelim_dtAdm;
    }

    public void setInfoRegPrelim_dtAdm(String infoRegPrelim_dtAdm) {
        this.infoRegPrelim_dtAdm = infoRegPrelim_dtAdm;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.PedidoRefeicaoItens;

/**
 *
 * <AUTHOR>
 */
public class PedidoRefeicaoItensDao {

    public PedidoRefeicaoItens listarItensPedido(String seqRota, String parada, String codFil, Persistencia persistencia) throws Exception {

        try {
            String sql = " SELECT \n"
                    + "    CONVERT(BigInt, SUM(QtdeCafe)) QtdeCafe,\n"
                    + "    CONVERT(BigInt, SUM(QtdeAlmoco)) QtdeAlmoco,\n"
                    + "    CONVERT(BigInt, SUM(QtdeJantar)) QtdeJantar,\n"
                    + "    CONVERT(BigInt, SUM(QtdeCeia)) QtdeCeia \n"
                    + " FROM \n"
                    + "     PedidoRefeicaoItens\n"
                    + " LEFT JOIN \n"
                    + "     PedidoRefeicao ON PedidoRefeicao.Sequencia = PedidoRefeicaoItens.Sequencia\n"
                    + "LEFT JOIN \n"
                    + "     Pedido ON PedidoRefeicao.CodFil = Pedido.CodFil  AND PedidoRefeicao.Data   = Pedido.Data AND PedidoRefeicao.CodCli = Pedido.CodCli2 \n"
                    + " WHERE \n"
                    + "     Pedido.SeqRota = ? AND  Pedido.Parada = ? AND  Pedido.CodFil = ?\n";

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setString(seqRota);
            consulta.setString(parada);
            consulta.setString(codFil);
            consulta.select();

            PedidoRefeicaoItens pedidoRefeicaoItem = null;

            if (consulta.Proximo()) {
                pedidoRefeicaoItem = new PedidoRefeicaoItens();

                pedidoRefeicaoItem.setQtdeAlmoco(consulta.getString("QtdeAlmoco"));
                pedidoRefeicaoItem.setQtdeCafe(consulta.getString("QtdeCafe"));
                pedidoRefeicaoItem.setQtdeCeia(consulta.getString("QtdeCeia"));
                pedidoRefeicaoItem.setQtdeJantar(consulta.getString("QtdeJantar"));
            }

            consulta.close();

            return pedidoRefeicaoItem;

        } catch (Exception e) {
            throw new Exception("PedidoRefeicaoItensDao.listarItensPedido - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "    SUM(QtdeCafe) QtdeCafe,\n"
                    + "    SUM(QtdeAlmoco) QtdeAlmoco,\n"
                    + "    SUM(QtdeJantar) QtdeJantar,\n"
                    + "    SUM(QtdeCeia) QtdeCeia \n"
                    + " FROM \n"
                    + "     PedidoRefeicaoItens\n"
                    + " LEFT JOIN \n"
                    + "     PedidoRefeicao ON PedidoRefeicao.Sequencia = PedidoRefeicaoItens.Sequencia\n"
                    + "LEFT JOIN \n"
                    + "     Pedido ON PedidoRefeicao.CodFil = Pedido.CodFil  AND PedidoRefeicao.Data   = Pedido.Data AND PedidoRefeicao.CodCli = Pedido.CodCli2 \n"
                    + " WHERE \n"
                    + "     Pedido.SeqRota = " + seqRota + " AND  Pedido.Parada = " + parada + " AND  Pedido.CodFil = " + codFil + "\n");
        }
    }
}

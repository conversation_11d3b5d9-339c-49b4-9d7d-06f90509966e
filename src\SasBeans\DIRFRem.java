package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class DIRFRem {

    private BigDecimal CodFil;
    private Integer AnoCompet;
    private LocalDate DtEnvio;
    private String Retificadora;
    private String Operador;
    private LocalDate Dt_alter;
    private String Hr_alter;

    public DIRFRem() {
        this.CodFil = new BigDecimal("0");
        this.AnoCompet = 0;
        this.DtEnvio = LocalDate.now();
        this.Retificadora = "";
        this.Operador = "";
        this.Dt_alter = LocalDate.now();
        this.Hr_alter = "";
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public Integer getAnoCompet() {
        return AnoCompet;
    }

    public void setAnoCompet(Integer AnoCompet) {
        this.AnoCompet = AnoCompet;
    }

    public LocalDate getDtEnvio() {
        return DtEnvio;
    }

    public void setDtEnvio(LocalDate DtEnvio) {
        this.DtEnvio = DtEnvio;
    }

    public String getRetificadora() {
        return Retificadora;
    }

    public void setRetificadora(String Retificadora) {
        this.Retificadora = Retificadora;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }
}

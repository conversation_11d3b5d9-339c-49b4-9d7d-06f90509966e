<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets" style="overflow:hidden !important; max-height:100% !important;"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/cofres.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/chart/chart.js" type="text/javascript"></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiMob}" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
            <style>
                [id*="formBoletimTrabalho"] .ui-state-focus{
                    box-shadow: none !important;
                    outline: none !important;
                }


                [id*="TopoPonto"], .Teste{
                    border-bottom: thin solid #DDD !important;
                }

                .EntradaSaida{
                    font-size:8pt;
                    font-weight:bold !important;
                    color:#FFF !important;
                    background-color:#666;
                    display:block !important;
                    min-width:90px;
                    padding:2px 6px 2px 6px;
                    text-align: center;
                    border-radius:3px;
                    box-shadow:2px 2px 3px #CCC;
                    max-width:calc(100% - 35px) !important;
                    white-space:nowrap;
                    overflow:hidden !important;
                    text-overflow:ellipsis !important;
                    text-transform: uppercase !important;
                }

                .EntradaSaida[ref1="1"],
                .EntradaSaida[ref1="1"],
                .EntradaSaida[ref1="11"]{
                    background-color: forestgreen;
                    clear:both !important;
                    display: block !important;
                    border:thin solid #1D761D;
                }

                .EntradaSaida[ref1="1"][ref2="2"],
                .EntradaSaida[ref1="1"][ref2="4"],
                .EntradaSaida[ref1="12"]{
                    background-color: red;
                    clear:both !important;
                    display:block !important;
                    border:thin solid #D20000;
                }

                div[ref="nada"]{
                    display:none !important;
                }

                @media only screen and (max-width: 764px) and (min-width: 10px) {
                    div[ref^="botaoRelatorio"]{
                        height: calc(50% - 10px) !important;
                    }

                    div[ref="botaoRelatorioContatos"]{
                        margin-top:10px !important;
                    }

                    .btSalvar{
                        max-width:100% !important;
                    }

                    #PaiMap, #map{
                        height: 250px !important;
                    }

                    #PaidivItensHistorico{
                        height: calc(100vh - 600px) !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    .ui-paginator-current{
                        font-size: 8pt !important;
                        margin-right:0px !important;
                        margin-left:12px !important;
                        padding-left:0px !important;
                        padding-right:0px !important;
                    }
                }

                div[id*="pnlHTML"] table{
                    padding:0px !important;
                    border-spacing:0px !important;
                }

                div[id*="pnlHTML"] table tbody tr td img{
                    margin-right:12px;
                }

                div[id*="pnlHTML"] table tbody tr td span,
                div[id*="pnlHTML"] table tbody tr td span strong{
                    font-size:8pt !important;
                    padding:0px !important;
                }

                div[id*="pnlHTML"] table tbody tr{
                    /*min-height:5px !important;
                    height:5px !important;
                    max-height:5px !important;
                    line-height:5px !important;*/
                }

                div[id*="pnlHTML"] table tbody tr td{
                    padding:0px !important;
                    min-height:5px !important;
                    height:5px !important;
                    max-height:5px !important;
                    line-height:5px !important;
                    vertical-align: middle !important;
                }

                div[id*="pnlHTML"] table tbody tr{
                    padding:0px !important;
                    line-height:5px !important;
                    height:5px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td,
                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr{
                    padding-top: 0px !important;
                    padding-bottom: 0px !important;
                    height:auto !important;
                }

                div[id*="pnlHTML"] table tbody tr td table tbody tr td span{
                    /*width:100px !important;
                    white-space:nowrap;
                    overflow:hidden !important;
                    text-overflow:ellipsis !important;*/
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:first-child td{
                    background-color:#3C8DBC !important;
                    color:#FFF !important;
                    padding-bottom:0px !important;
                    height:auto !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td{
                    padding-top: 6px !important;
                    padding-left: 6px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table{
                    border:thin solid #BBB !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:nth-child(odd):not(:first-child) td{
                    background-color:whitesmoke !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td:first-child{
                    text-align:right !important;
                    padding-right: 6px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td span{
                    margin-top: 8px !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table,
                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr,
                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td{
                    vertical-align:middle !important;
                }

                div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td{
                    height:10px !important;
                    line-height:10px !important;
                    max-height:10px !important;
                }

                div[id*="pnlHTML"][class*="INSP"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td,
                div[id*="pnlHTML"][class*="RO"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td,
                div[id*="pnlHTML"][class*="SUP"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td{
                    display:none !important;
                }

                div[id*="pnlHTML"]{
                    height:100% !important;
                    overflow:hidden !important;
                    overflow-y:auto !important;
                }

                div[id*="pnlHTMLpai"]{
                    height: 436px !important;
                }

                #divConteudo{
                    height:436px !important;
                }

                .ui-datatable:not([id*="tabelaClienteEW"]) thead th {
                    background-color: #3C8DBC !important;
                    color: #FFF;
                }

                div[id*="panelInspecoesDetalhes"]{

                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    div[id*="pnlHTMLpai"][class*="INSP"]{
                        height:350px !important;
                    }

                    div[id*="pnlHTMLpai"][class*="INSP"] div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:not(:first-child) td span,
                    div[id*="pnlHTMLpai"][class*="SUP"] div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:not(:first-child) td span{
                        height:auto !important;
                        line-height:initial !important;
                        display:inline-block;
                        margin:0px !important;

                        /*max-height:15px !important;*/
                    }

                    #divConteudo[class*="INSP"]{
                        height:2900px !important;
                    }

                    div[id*="pnlHTML"]{
                        overflow-y:hidden !important;
                    }

                    [id*="gmap"]{
                        height: 295px !important;
                    }

                    #pnlHTMLpai{
                        min-height: 520px !important;
                        height: 520px !important;
                        max-height: 520px !important;
                    }

                    [id*="btExpandirMapa"]{
                        top: 4px !important;
                        right: 13px !important;
                        width: 32px !important;
                        height: 32px !important;
                    }

                    [id*="btExpandirMapa"] img{
                        margin-top: 4px !important;
                        margin-left: 0px !important;
                    }

                    [id*="cadastrar"]{
                        height: calc(100vh - 180px) !important;
                    }

                    [id*="panelInspecoesDetalhes"]{
                        overflow: hidden !important;
                        height: auto !important;
                    }
                }

                .quadroResumo{
                    padding:0px 8px 0px 0px !important;
                    margin-bottom:8px !important;
                    height:53px;
                }

                .quadroResumo div{
                    border-radius:0px;
                    height:100% !important;
                    padding:0px !important;
                    position:relative;
                    background-color: #FFF;
                    box-shadow:1px 0px 4px #BBB;
                }

                .quadroResumo div[ref="entrada"]{
                    color: forestgreen !important;
                }

                .quadroResumo div[ref="entrada"] label[ref="icon"]{
                    background-color:forestgreen;
                    color:#FFF;
                }

                .quadroResumo div[ref="saida"]{
                    color:red !important;
                }

                .quadroResumo div[ref="saida"] label[ref="icon"]{
                    background-color:red;
                    color:#FFF;
                }

                .quadroResumo div[ref="inspecao"]{
                    color:#b4af00 !important;
                }

                .quadroResumo div[ref="inspecao"] label[ref="icon"]{
                    background-color:#b4af00;
                    color:#FFF;
                }

                .quadroResumo div[ref="relatoriosX"]{
                    color:#f29100 !important;
                }

                .quadroResumo div[ref="relatoriosX"] label[ref="icon"]{
                    background-color:#F90;
                    color:#FFF;
                }

                .quadroResumo div[ref="rondas"]{
                    color:#8b6dc1 !important;
                }

                .quadroResumo div[ref="rondas"] label[ref="icon"]{
                    background-color:#8b6dc1;
                    color:#FFF;
                }

                .quadroResumo div[ref="outros"]{
                    color:#505050 !important;
                }

                .quadroResumo div[ref="outros"] label[ref="icon"]{
                    background-color:#505050;
                    color:#FFF;
                }

                .quadroResumo div[ref="dashboard"]{
                    color:#3479A3 !important;
                }

                .quadroResumo div[ref="dashboard"] label[ref="icon"]{
                    background-color:#3479A3;
                    color:#FFF;
                }



                .quadroResumo label[ref="titulo"]{
                    font-size:8pt !important;
                    font-weight:500 !important;
                    width:calc(100% - 60px);
                    text-align:center;
                    text-transform:uppercase !important;
                    height:15px !important;
                    float:left;
                    margin-top:2px !important;
                    max-width:100%;
                    overflow:hidden;
                    white-space:nowrap;
                    text-overflow:ellipsis;
                    padding-left:4px !important;
                    padding-right:4px !important;
                }

                .quadroResumo label[ref="icon"]{
                    float:left;
                    height:53px;
                    width:60px;
                    margin:0px !important;
                }

                .quadroResumo label[ref="valor"]{
                    font-size:20pt !important;
                    font-weight:500 !important;
                    width:calc(100% - 60px);
                    text-align:center;
                    left:0px;
                    height:55px;
                    margin-top: -5px !important;
                    float:right !important;
                    max-width:100%;
                    overflow:hidden;
                    white-space:nowrap;
                    text-overflow:ellipsis;

                }
                .quadroResumo div[ref="relatoriosX"] label[ref="valor"],
                .quadroResumo div[ref="relatoriosX"] label[ref="titulo"]{
                    width:calc(100% - 85px) !important;
                    max-width:calc(100% - 85px) !important;
                    left:0px !important;
                }

                .quadroResumo div[ref="relatoriosX"] label[ref="valor"]{
                    margin-right:25px !important;
                }

                .quadroResumo label[ref="icon"]{
                    font-size:20pt;
                    text-align:center;
                    padding-top:6px !important;
                }

                .ui-selectonemenu,
                .ui-selectonemenu label{
                    cursor:pointer !important;
                }

                .lblRotulo{
                    font-size:10pt !important;
                    color:#505050 !important;
                    font-weight: bold !important;
                    text-shadow:1px 1px #FFF;
                    cursor: default !important;
                    font-family:'Open Sans', sans-serif !important;
                    margin:0px !important;
                }

                .FotoPendente{
                    width:100% !important;
                    min-height:350px !important;
                    height:100% !important;
                    max-height:100% !important;
                    border:3px solid #BBB;
                    background-color:#DDD;
                    border-radius:4px;
                    color:#BBB;
                    cursor:pointer;
                    box-shadow:2px 2px 3px #CCC;
                    background-size: cover;
                    background-position: center center;
                    position:relative !important;
                }

                .FotoPendente:hover{
                    background-color:#CCC;
                    border:3px solid #AAA;
                    transition:0.3s ease;
                    color:#999 !important;
                }

                .FotoPendente:hover label,
                .FotoPendente:hover i{
                    color:#999 !important;
                }

                .FotoPendente label{
                    font-size: 20pt;
                }

                .FotoPendente i{
                    font-size: 32pt;
                }

                #divMapaFull{
                    position:absolute;
                    width:100%;
                    height:100%;
                    top:0px;
                    left:0px;
                    background-color:#FFF;
                    z-index:9999 !important;
                    padding:0px !important;
                }

                [id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-titlebar,
                [id*="formUltimaComunicacao"] .ui-dialog.ui-widget-content .ui-dialog-titlebar,
                [id*="formEfetivos"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #EEE !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content,
                [id*="formUltimaComunicacao"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content,
                [id*="formEfetivos"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #FFF !important;
                }

                [id*="relatorio"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #EEE !important;
                    border-bottom-color: #CCC !important;
                    height: 70px !important;
                }

                [id*="relatorio"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #FFF !important;
                }

                [id*="dlgRelatorio_content"]{
                    background-color: transparent !important;
                }

                [id*="batidaPonto"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #EEE !important;
                    border-bottom-color: #CCC !important;
                    height: 70px !important;
                }

                [id*="batidaPonto"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #FFF !important;
                }

                [id*="dlgBatidaPonto_content"]{
                    background-color: transparent !important;
                }


                .DadosCabecalhoPonto img{
                    max-height: 80px;
                    margin-right: 8px;
                }

                .DadosCabecalhoPonto{
                    font-size: 8pt !important;
                }

                .DadosCabecalhoPonto tr:first-child td,
                .DadosCabecalhoPonto tr:nth-child(2) td,
                .DadosCabecalhoPonto tr:nth-child(5) td{
                    text-transform: uppercase;
                    font-weight: bold !important;
                }

                .DadosCabecalhoPonto tr:nth-child(4) td{
                    color: darkred !important;
                }

                .divTopoBatida{
                    border-bottom: thin solid #DDD;
                    padding: 0px 0px 8px 0px !important;
                }

                .batida-saida, .batida-entrada{
                    color: #FFF !important;
                    border-radius: 30px;
                    text-align: center;
                    font-weight: bold !important;
                }

                .DadosCabecalhoPonto tr td{
                    padding: 2px !important;
                }

                .batida-saida{
                    background-color: red !important;
                }

                .batida-entrada{
                    background-color: forestgreen !important;
                }

                #divHistoricoPonto{
                    padding-right: 8px !important;
                    padding-left: 20px !important;
                    overflow: auto;
                    max-height: calc(100% - 2px);
                }

                .DetalhesPonto tr td{
                    width: calc(100% - 20px) !important;
                    max-width: calc(100% - 20px) !important;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 9pt;
                }

                .DetalhesPonto tr:first-child td{
                    font-size: 14pt;
                    color: steelblue
                }

                .tab-pane{
                    height: calc(100vh - 220px);
                    padding: 15px !important; 
                    border-left: thin solid #DDD;
                    border-right: thin solid #DDD;
                    border-bottom: thin solid #DDD;
                    overflow: hidden !important;
                }


                @media only screen and (max-width: 768px) and (min-width: 10px) {
                    #divHistoricoPonto{
                        padding-right: 0px !important;
                        padding-left: 0px !important;
                        margin-top: 10px;
                        overflow: hidden !important;
                        max-height: inherit !important;
                    }    

                    .tab-pane{
                        border-left: none !important;
                        border-right: none !important;
                        border-bottom: none !important;
                    }

                    .tab-pane{
                        overflow: auto !important
                    }

                    .DetalhesPonto tr td{
                        max-width: 110px !important;
                    }
                }

                [id*="pnlUltima"] table:not(.GridDash){
                    width: 100%;
                    height: 100%;
                    margin: 0px !important;
                    border-collapse: separate;
                    padding: 0px !important;
                }

                [id*="pnlUltima"] table:not(.GridDash) tr td i{
                    font-size: 32pt;
                    color: #FFF;
                }

                [id*="pnlUltima"] table:not(.GridDash) tr td label:not(.Qtde){
                    font-size: 12pt;
                    color: #FFF;
                    text-transform: uppercase;
                    width: 100%;
                    text-align: center;
                }

                [id*="pnlUltima"] table:not(.GridDash) tr td {
                    vertical-align: middle;
                    text-align: center;
                }

                .GridDash{
                    width: 100%;
                }

                .GridDash thead tr th{
                    text-align: center;
                    padding: 5px 2px 5px 2px !important;
                    font-size: 9pt !important;
                }

                .GridDash tbody tr td{
                    color: #666;
                    text-align: center;
                    padding: 3px 2px 3px 2px !important;
                    border-bottom: thin solid #CCC;
                    font-size: 8pt !important;
                }

                .GridDash tbody tr:nth-child(even) td{
                    background-color: whitesmoke;
                }

                .Qtde{
                    font-size: 24pt !important;
                    padding: 0px 4px 0px 4px !important;
                    border-bottom: 1px solid #FFF !important;
                    border-top: 1px solid #FFF !important;
                    width: 80px !important;
                    color: #FFF;
                    text-align: center;
                    margin-bottom: 8px !important;
                }

                .GridDash[ref="Ferias"] thead tr th{
                    background-color: honeydew;
                    border-bottom: thin solid #1D761D;
                    border-top: thin solid #1D761D;
                    color: #1D761D;
                }

                .GridDash[ref="Faltas"] thead tr th{
                    background-color: mistyrose;
                    border-bottom: thin solid ;
                    border-top: thin solid #D20000;
                    color: #D20000;
                }

                .GridDash[ref="Atrasos"] thead tr th{
                    background-color: blanchedalmond;
                    border-bottom: thin solid #f29100;
                    border-top: thin solid #f29100;
                    color: #f29100;   
                }

                @media only screen and (max-width: 768px) and (min-width: 10px) {
                    .Qtde{
                        font-size: 14pt !important;
                        width: auto !important;
                        /*padding: 0px 4px 0px 4px !important;
                        border-bottom: 1px solid #FFF !important;
                        border-top: 1px solid #FFF !important;
                        width: 80px !important;
                        color: #FFF;
                        text-align: center;
                        margin-bottom: 15px !important;*/
                    }

                    [id*="pnlUltima"] table:not(.GridDash) tr td i{
                        font-size: 22pt;
                    }

                    [id*="pnlUltima"] table:not(.GridDash) tr td label:not(.Qtde){
                        font-size: 10pt;
                    }

                    #divPaiDash{
                        padding-right: 0px !important;
                    }

                    #PaiMap{
                        height: calc(100vh - 320px) !important;
                    }

                    .FundoPagina{
                        min-height: calc(100% - 125px) !important;
                    }
                }

                .ui-accordion-content {
                    padding: 10px 10px 0px 10px !important;
                    overflow: hidden !important
                }

                .calendario input{
                    width: 200px !important;
                }

                [id*="Efetiv"] .calendario input{
                    width: 100% !important;
                }

                #tblEfetivos tbody tr td,
                #tblEfetivos thead tr td,
                #tblEfetivosPtos tbody tr td,
                #tblEfetivosPtos thead tr td{
                    text-align: center;
                }

                #tblEfetivos thead tr td,
                #tblEfetivosPtos thead tr td{
                    positio: sticky !important;
                    z-index: 999 !important;
                }

                .ui-selectcheckboxmenu-multiple-container{
                    max-height: 100px !important;
                    overflow: auto;
                } 
            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;max-height:100% !important;">
            <f:metadata>
                <f:viewAction action="#{mobEW.Persistencia(login.pp, login.satellite)}" />
                <f:viewAction action="#{mobEW.carregarResumoQdes(true)}" />
                <f:viewAction action="#{mobEW.atualizarListaPostos()}" />
                <f:viewAction action="#{mobEW.atualizarLazyList()}" />
            </f:metadata>

            <ui:include src="/botao_panico.xhtml"/>

            <p:growl id="msgs" widgetVar="g"/>
            <div id="divMapaFull" style="display:none"></div>
            <div id="body" style="overflow:hidden !important;max-height:100% !important;">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Relatorios}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{mobEW.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{mobEW.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{mobEW.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{mobEW.filiais.bairro}, #{mobEW.filiais.cidade}/#{mobEW.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" >

                                    <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                  value="#{mobEW.datasSelecionadas}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{mobEW.selecionarDatas}" update="msgs main:pnlAcordionTopo" />
                                    </p:datePicker>

                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main" style="overflow:hidden !important; height: calc(100vh - 88px) !important">
                    <p:hotkey bind="p" actionListener="#{mobEW.prepararFiltro}" update="formPesquisar"/>
                    <p:hotkey bind="e" update="relatorio msgs" actionListener="#{mobEW.buttonAction}"/>

                    <p:accordionPanel id="pnlAcordionTopo" styleClass="painelCadastro" activeIndex="0" style="margin-bottom: 10px; padding:0px !important;">
                        <p:tab title="#{localemsgs.Quantidade}">
                            <p:panel id="pnlResumo" style="padding:0px !important; width:calc(100% + 8px) !important; padding:0px !important">
                                <div class="col-md-12" style="padding:0px 0px 6px 0px !important; background-color:transparent !important;">
                                    <div class="col-md-2 col-sm-2 col-xs-6 quadroResumo">
                                        <div class="col-md-12" ref="entrada">
                                            <label ref="icon"><i class="fa fa-thumbs-o-up" aria-hidden="true"></i></label>
                                            <label ref="titulo">#{localemsgs.EmServico}</label>
                                            <label ref="valor">#{mobEW.resumoQdeEntradas}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2 col-xs-6 quadroResumo">
                                        <div class="col-md-12" ref="saida">
                                            <label ref="icon"><i class="fa fa-thumbs-o-down" aria-hidden="true"></i></label>
                                            <label ref="titulo">#{localemsgs.ForaServico}</label>
                                            <label ref="valor">#{mobEW.resumoQdeSaidas}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2 col-xs-6 quadroResumo">
                                        <div class="col-md-12" ref="inspecao">
                                            <label ref="icon"><i class="fa fa-binoculars" aria-hidden="true"></i></label>
                                            <label ref="titulo">#{localemsgs.Inspecoes}</label>
                                            <label ref="valor">#{mobEW.resumoQdeInspecoes}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 col-sm-2 col-xs-6 quadroResumo">
                                        <div class="col-md-12" ref="relatoriosX">
                                            <label ref="icon"><i class="fa fa-file-text-o" aria-hidden="true"></i></label>
                                            <label ref="titulo">#{localemsgs.Relatorios}</label>
                                            <label ref="valor">#{mobEW.resumoQdeRelatorios}</label>

                                            <p:commandLink title="#{localemsgs.Adicionar}"
                                                           update="msgs relatorio" onclick="AtribuirValorObj()"  actionListener="#{mobEW.novoLancamento()}">
                                                <label style="position:absolute; cursor:pointer; background-color: #ff9900; color:#fff; padding-top: 2px; padding-left: 0px; text-align: center; font-size: 11pt; width: 25px; height:25px; border-radius:50%; right:6px; top:14px; border:thin solid #b96f00">
                                                    <i class="fa fa-plus"></i>
                                                </label>
                                            </p:commandLink>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-4 col-xs-12" style="padding: 0px !important">
                                        <div class="col-md-4 col-sm-4 col-xs-6 quadroResumo">
                                            <div class="col-md-12" ref="rondas">
                                                <label ref="icon" style="padding-top:7px !important;"><i class="fa fa-random" aria-hidden="true"></i></label>
                                                <label ref="titulo">#{localemsgs.Rondas}</label>
                                                <label ref="valor">#{mobEW.resumoQdeRondas}</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-6 quadroResumo">
                                            <div class="col-md-12" ref="outros">
                                                <label ref="icon"><i class="fa fa-certificate" aria-hidden="true"></i></label>
                                                <label ref="titulo">#{localemsgs.OutrosDesc}</label>
                                                <label ref="valor">#{mobEW.resumoQdeOutros}</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-12 quadroResumo">
                                            <div class="col-md-12" ref="dashboard">
                                                <p:commandLink actionListener="#{mobEW.abrirUltimaComunicacao(false)}" style="cursor: pointer !important" update="msgs">
                                                    <label ref="icon" style="cursor: pointer !important"><i class="fa fa-line-chart" aria-hidden="true"></i></label>
                                                    <label ref="titulo" style="font-size: 9pt !important; color: #3479A3 !important; cursor: pointer !important; height: 30px !important; margin-top: 17px !important">#{localemsgs.Dashboard}</label>
                                                </p:commandLink>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </p:panel>
                        </p:tab>
                    </p:accordionPanel>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow-y:auto !important;padding-left:8px !important">
                        <div class="ui-grid-row">

                            <div class="ui-grid-col-12" style="padding-right: 6px !important;">
                                <p:panel id="painelPesquisa" style="padding-right:8px !important;padding-left:6px !important">
                                    <p:accordionPanel styleClass="painelCadastro" activeIndex="1" style="margin-bottom: 10px; padding:0px !important;">
                                        <p:tab title="#{localemsgs.Filtrar}">
                                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 8px 0px 0px;">
                                                <p:outputLabel for="codfil" value="#{localemsgs.Filiais}: "/>
                                                <p:selectOneMenu id="codfil" value="#{mobEW.logsSatMobEWFiltro.codfil}" converter="omnifaces.SelectItemsConverter"
                                                                 filter="true" filterMatchMode="contains"
                                                                 style="width: 100%;">
                                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"  />
                                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                                    <p:ajax event="itemSelect" listener="#{mobEW.atualizarListaPostos}"
                                                            update="filtroPosto"/>
                                                    <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                                </p:selectOneMenu>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 8px 0px 0px;">
                                                <p:outputLabel for="filtroPosto" value="#{localemsgs.Postos}: " />
                                                <p:selectCheckboxMenu id="filtroPosto" value="#{mobEW.secaoSelecionadosFiltro}" converter="omnifaces.SelectItemsConverter"
                                                                      styleClass="filial"
                                                                      filter="true" filterMatchMode="contains" multiple="true"
                                                                      required="false"
                                                                      style="width: 100%">
                                                    <f:selectItem itemLabel="#{localemsgs.Todos}" noSelectionOption="true"  />
                                                    <f:selectItems value="#{mobEW.postos}" var="pstserv" itemValue="#{pstserv}"
                                                                   itemLabel="#{pstserv.secao}, #{pstserv.local}: #{pstserv.descContrato}" noSelectionValue=""/>
                                                    <p:ajax event="change" listener="#{mobEW.carregarFuncionariosTela}" update="filtroFuncionario"/>
                                                </p:selectCheckboxMenu>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 8px 0px 0px;">
                                                <p:outputLabel for="filtroFuncionario" value="#{localemsgs.Funcionarios}: "/>
                                                <p:inputText id="filtroFuncionario" value="#{mobEW.filtroNomeFuncionario}" required="false" style="width: 100%"></p:inputText>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 0px 0px 0px;">
                                                <div class="col-md-7 col-sm-7 col-xs-12" style="padding:0px 8px 0px 0px;">
                                                    <p:outputLabel for="filtroTipo" value="#{localemsgs.Tipo}: "/>
                                                    <p:selectOneMenu id="filtroTipo" value="#{mobEW.logsSatMobEWFiltro.tipo}"
                                                                     styleClass="filial" panelStyleClass="hideDisabled"
                                                                     filter="true" filterMatchMode="contains"
                                                                     style="width: 100%">
                                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                                        <f:selectItem itemLabel="#{localemsgs.ClockInClockOut}" itemValue="1"/>
                                                        <f:selectItem itemLabel="#{localemsgs.CheckInCheckOut}" itemValue="5" itemDisabled="#{!mobEW.supervisor}"/>
                                                        <f:selectItem itemLabel="#{localemsgs.Inspecao}" itemValue="7"/>
                                                        <f:selectItem itemLabel="#{localemsgs.Relatorios}" itemValue="2"/>
                                                        <f:selectItem itemLabel="#{localemsgs.RelatorioPrestador}" itemValue="10" itemDisabled="#{!mobEW.supervisor}"/>
                                                        <f:selectItem itemLabel="#{localemsgs.Rondas}" itemValue="4"/>
                                                        <f:selectItem itemLabel="#{localemsgs.RotaPrestador}" itemValue="9" itemDisabled="#{!mobEW.supervisor}"/>
                                                    </p:selectOneMenu>
                                                </div>
                                                <div class="col-md-5 col-sm-5 col-xs-12" style="padding:0px 8px 0px 0px;">
                                                    <p:outputLabel for="filtroOrdenacao" value="#{localemsgs.Ordenacao}: "/>
                                                    <p:selectOneMenu id="filtroOrdenacao" value="#{mobEW.ordenacao}"
                                                                     styleClass="filial" panelStyleClass="hideDisabled"
                                                                     filter="true" filterMatchMode="contains"
                                                                     style="width: 100%">
                                                        <f:selectItem itemLabel="#{localemsgs.Crescente}" itemValue="C"/>
                                                        <f:selectItem itemLabel="#{localemsgs.Decrescente}" itemValue="D"/>
                                                    </p:selectOneMenu>
                                                </div>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:8px 8px 0px 0px;">
                                                <p:commandButton action="#{mobEW.pesquisar()}" update="msgs main:pnlAcordionTopo" styleClass="botao btn btn-primary" value="#{localemsgs.Pesquisar}">
                                                </p:commandButton>
                                                <p:commandButton action="#{mobEW.limparTodosFiltros()}" update="msgs main:tabela main:painelPesquisa" styleClass="botao btn btn-warning" value="#{localemsgs.LimparFiltros}" style="margin-left: 4px;">
                                                </p:commandButton>
                                            </div>
                                        </p:tab>
                                    </p:accordionPanel>
                                </p:panel>

                                <p:panel id="pnlListaPontos" style="display: inline; padding-bottom:6px !important; padding-right:8px !important; padding-left:0px !important">
                                    <i id="btImprimirPontos" class="fa fa-print" title="#{localemsgs.Imprimir}" style="width:70px; height: 70px; text-align: center; font-size: 22pt; color: #FFF; position: absolute; right: 10px; bottom: 0px; padding-top: 17px !important; cursor: pointer; background-color: red; border-radius: 50%; z-index: 99; border: 2px solid darkred"></i>

                                    <p:commandLink update="msgs relatorio batidaPonto" 
                                                   actionListener="#{mobEW.buttonActionEfetivos}">
                                        <i class="fa fa-file-text" id="btEfetivosLog" title="#{localemsgs.Efetividade}" style="width:70px; height: 70px; text-align: center; font-size: 22pt; color: #FFF; position: absolute; right: 96px; bottom: 0px; padding-top: 17px !important; cursor: pointer; background-color: darkorange; border-radius: 50%; z-index: 99; border: 2px solid #bb5f00"></i>
                                    </p:commandLink>

                                    <p:dataGrid  id="tabela" value="#{mobEW.lazyGride}" paginator="true" rows="100" lazy="true"
                                                 rowsPerPageTemplate="5,10,15,20,25,50,100" layout="grid"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} #{mobEW.total}"
                                                 paginatorTemplate="{PreviousPageLink} {CurrentPageReport}
                                                 {RowsPerPageDropdown} {NextPageLink}"
                                                 var="lista" columns="2" emptyMessage="#{localemsgs.SemRegistros}">
                                        <p:panel id="TopoGride" style="background-color: #F0F0F0; border: 1px solid #DDD !important;
                                                 border-radius: 6px; padding: 0px !important; box-shadow:1px 1px 5px #CCC; position:relative;">
                                            <f:facet name="header">
                                                <div class="col-md-12" style="padding:0px !important;display: flex;align-items: center; position:relative; height:63px !important;">
                                                    <div ref="#{mobEW.getFotoGride(lista.tipo, lista.chave, lista.fotos)}"
                                                         style="background-image:url(#{mobEW.getFotoGride(lista.tipo, lista.chave, lista.fotos)});
                                                         height:60px !important; width:60px !important; border-radius:50%; background-size:cover;
                                                         background-position:center center; display:inline-block; font-weight:bold !important;
                                                         text-shadow:1px 1px #FFF !important; margin-right:12px; border:2px solid #DDD;"></div>
                                                    <h:outputText class="lblNomePonto" value="#{lista.funcionario == ''?lista.titulo: lista.funcionario}"
                                                                  title="#{lista.funcionario}" style="display:inline-block !important; top:0px !important;
                                                                  margin-top:-17px !important; color:#3479A3 !important;
                                                                  font-family:'Open Sans', sans-serif !important; white-space:nowrap; overflow:hidden;
                                                                  text-overflow:ellipsis;"></h:outputText>

                                                    <label class="EntradaSaida" style="position:absolute !important;bottom:5px !important;left:72px !important; float: left; color: #FFF; font-weight: bold; min-width: 80px; text-align: center; border-radius: 3px; background-color: #{(lista.tipo eq 1 or lista.tipo eq 11) and lista.chave.split(';')[2] ne 2 and lista.chave.split(';')[2] ne 4?'forestgreen':'red' };"
                                                           ref1="#{lista.tipo}" ref2="#{lista.chave.split(';')[2]}">
                                                        #{mobEW.getTipoItem(lista.tipo, lista.chave, lista.titulo)}
                                                    </label>

                                                    <img src="../assets/img/camera-check2.jpg" width="28" style="display: #{lista.tipo eq '7' and lista.qtdeFotos ne 0 and lista.titulo.split(' ')[0] eq 'BOLETIM'? '' :'none'}; position:absolute !important;bottom:2px !important; left:175px !important; float: left;" />

                                                </div>
                                                <img src="#{mobEW.getImagemTipo(lista.tipo, lista.chave)}" height="40" width="40"
                                                     style="float:right !important; position:absolute; top:12px; right:10px;"/>

                                            </f:facet>

                                            <p:panelGrid columns="3" columnClasses="ui-g-11, ui-g-1"
                                                         layout="grid" styleClass="ui-panelgrid-blank" style="position:relative !important;padding-left:0px !important">
                                                <p:column>
                                                    <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                                                 layout="grid" styleClass="ui-panelgrid-blank" style="padding:8px 10px 8px 15px !important; font-size:10pt !important;font-family:'Open Sans', sans-serif !important; min-height:110px;">
                                                        <p:column>
                                                            <i class="fa fa-calendar" style="width:20px !important; text-align:center !important;"></i>&nbsp;<h:outputText value="#{localemsgs.Data}: " title="#{localemsgs.Data}" styleClass="negrito" style="font-weight: bold" />
                                                            <h:outputText value="#{lista.data}" title="#{lista.data}" converter="conversorData"/>
                                                        </p:column>
                                                        <p:column>
                                                            <i class="fa fa-clock-o" style="width:20px !important; text-align:center !important;"></i>&nbsp;<h:outputText value="#{localemsgs.Hora}: " title="#{localemsgs.Hora}" styleClass="negrito" style="font-weight: bold"/>
                                                            <h:outputText value="#{lista.hora}" title="#{lista.hora}" styleClass="negrito" style="font-weight: bold"/>
                                                        </p:column>
                                                        <p:column>
                                                            <div style="width:100% !important; padding:0px !important;" ref="posicao" posicao="#{lista.latitude}|#{lista.longitude}">
                                                                <i class="fa fa-map-marker" style="width:20px !important; text-align:center !important; padding-right:2px !important;"></i>&nbsp;
                                                                <h:outputText value="#{localemsgs.Local}: " title="#{localemsgs.Local}" styleClass="negrito" style="font-weight: bold"/>
                                                                <h:outputText value="#{lista.posto}" class="#{lista.posto == ''? 'SemPosto': 'ComPosto'}"  title="#{lista.posto}"/>
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <i class="fa fa-id-card-o" style="width:20px !important; text-align:center !important;"></i>&nbsp;<h:outputText value="#{localemsgs.Cargo}: " title="#{localemsgs.Cargo}" styleClass="negrito" style="font-weight: bold"/>
                                                            <h:outputText value="#{lista.cargo}" title="#{lista.cargo}" />
                                                        </p:column>

                                                        <p:column>
                                                            <i class="fa fa-map" style="width:20px !important; text-align:center !important; padding-right:2px !important"></i>&nbsp;<h:outputText value="#{localemsgs.Latitude}/#{localemsgs.Longitude}: " title="#{localemsgs.Latitude}/#{localemsgs.Longitude}:" styleClass="negrito" style="font-weight: bold" />
                                                            <h:outputText value="#{lista.latitude}/#{lista.longitude}" title="#{lista.latitude}/#{lista.longitude}" converter="conversorData"/>
                                                        </p:column>

                                                        <p:column rendered="#{lista.distancia_km ne '0'}">
                                                            <i class="fa fa-map-pin" style="width:20px !important; text-align:center !important; padding-right:2px !important"></i>&nbsp;<h:outputText value="#{localemsgs.Distancia}: " title="#{localemsgs.Distancia}" styleClass="negrito" style="font-weight: bold" />
                                                            <h:outputText value="#{lista.distancia_km}" title="#{lista.distancia_km}" converter="conversorData"/>
                                                        </p:column>

                                                        <p:column>
                                                            <div class="col-md-5 col-sm-5 col-xs-5" style="padding: 0px 4px 0px 0px">
                                                                <i class="fa fa-certificate" style="width:20px !important; text-align:center !important;"></i>&nbsp;<h:outputText value="#{localemsgs.Matricula}: " title="#{localemsgs.Matricula}" styleClass="negrito" style="font-weight: bold"/>
                                                                <h:outputText value="#{lista.matricula}" title="#{lista.matricula}" converter="conversor0" />
                                                            </div>
                                                        </p:column>
                                                        <p:column>
                                                            <i class="fa fa-certificate" style="width:20px !important; text-align:center !important;"></i>&nbsp;<h:outputText value="#{localemsgs.Jornada}: " title="#{localemsgs.Jornada}" styleClass="negrito" style="font-weight: bold"/>
                                                            <h:outputText value="#{lista.turno}" title="#{lista.turno}"  />
                                                        </p:column>
                                                        <p:column rendered="#{mobEW.getTipoItem(lista.tipo, lista.chave, lista.titulo) == 'ENTRADA' || mobEW.getTipoItem(lista.tipo, lista.chave, lista.titulo) == 'SAÍDA' || lista.distancia_km != ''?true:false}">
                                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px !important; padding-bottom: 3px !important; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: #{lista.tipo eq '7'? '': 'none'}">
                                                                <i class="fa fa-user" style="width:20px !important; text-align:center !important; padding-right:2px !important"></i>&nbsp;<h:outputText value="#{localemsgs.Responsavel}: " title="#{localemsgs.Responsavel}" styleClass="negrito" style="font-weight: bold" />
                                                                <h:outputText value="#{lista.detalhes}" title="#{lista.detalhes}" converter="conversorData"/>
                                                            </div>
                                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px !important; padding-bottom: 3px !important; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: #{lista.tipo ne '7'? '': 'none'}">
                                                                &nbsp;
                                                            </div>
                                                        </p:column>
                                                    </p:panelGrid>

                                                </p:column>

                                                <p:column>
                                                    <p:panel styleClass="celula-right" style="background-color: transparent; position:absolute; bottom:4px; right:10px;">
                                                        <div style="padding-bottom: 10px">
                                                            <p:commandLink title="#{localemsgs.MovimentacaoDiaria}" update="msgs relatorio batidaPonto"
                                                                           actionListener="#{mobEW.buttonAction(lista)}">
                                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                                                            </p:commandLink>
                                                        </div>
                                                    </p:panel>
                                                </p:column>
                                            </p:panelGrid>
                                            <script type="text/javascript">
                                                // <![CDATA[

                                                if ($(document).width() <= 700)
                                                    $('.FundoPagina').css('max-height', ($('html').height() - 384) + 'px').css('height', ($('html').height() - 199) + 'px');
                                                else
                                                    $('.FundoPagina').css('max-height', ($('html').height() - 250) + 'px').css('height', ($('html').height() - 250) + 'px');

                                                function CalcularTamanhoNome() {
                                                    var Tamanho = $('div[id*="TopoGride"]').width();
                                                    Tamanho = (eval(Tamanho) - 140);
                                                    $('.lblNomePonto').css('max-width', Tamanho + 'px');
                                                }

                                                CalcularTamanhoNome();

                                                $(window).resize(function () {
                                                    if ($(document).width() <= 700)
                                                        $('.FundoPagina').css('max-height', ($('html').height() - 384) + 'px').css('height', ($('html').height() - 199) + 'px');
                                                    else
                                                        $('.FundoPagina').css('max-height', ($('html').height() - 250) + 'px').css('height', ($('html').height() - 250) + 'px');

                                                    CalcularTamanhoNome();
                                                });
                                                // ]]>
                                            </script>
                                        </p:panel>
                                    </p:dataGrid>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 0px; bottom: 100px; background: transparent">
                        <h:inputHidden id="txtLatitude" value="#{mobEW.cadastroRelatorio.latitude}" ></h:inputHidden>
                        <h:inputHidden id="txtLongitude" value="#{mobEW.cadastroRelatorio.longitude}"></h:inputHidden>
                    </p:panel>

                    <script>
                        // <![CDATA[    
                        $(document).on('click', '#btImprimirPontos', function () {
                            Imprimir(CriarCabecalho('#{login.getLogo(login.empresa.bancoDados)}',
                                    '#{mobEW.filiais.descricao}',
                                    '#{mobEW.filiais.endereco} - #{mobEW.filiais.bairro}, #{mobEW.filiais.cidade}/#{mobEW.filiais.UF} - #{mobEW.filiais.CEP} ',
                                    'CNPJ: #{mobEW.filiais.CNPJ}',
                                    '#{localemsgs.RELATÓRIO} - #{localemsgs.PontoEletronico} - #{localemsgs.Periodo}: ' + $('[id*="range_input"]').val()),
                                    $('[id*="pnlListaPontos"]'));
                        });

                        // ]]>
                    </script>
                </h:form>

                <h:form id="formEfetivos" class="form-inline">
                    <p:dialog widgetVar="dlgEfetivos"  positionType="absolute" responsive="true" draggable="false" focus="efetivosCodFil"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; width: calc(100vh - 150px) !important; min-height: calc(100vh - 150px) !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_historico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Efetividade}" style="color:#022a48"/>
                        </f:facet>      


                        <p:panel id="pnlEfetividade" style="background-color: #FFF; max-width: 100% !important; display: block !important; margin-top:-10px !important; z-index:999 !important; height:calc(100vh - 230px) !important;padding-bottom: 0px !important; padding: 14px 0px 0px 0px !Important" class="cadastrar">
                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding-left: 0px !Important">
                                <p:outputLabel for="efetivosPeriodo" value="#{localemsgs.Periodo}"/>
                                <p:datePicker id="efetivosPeriodo" selectionMode="range" readonlyInput="true" 
                                              value="#{mobEW.datasSelecionadasEfetivos}"
                                              monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                              converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{mobEW.selecionarDatasEfetivos}" update="msgs main:tabela" />
                                </p:datePicker>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 8px 0px 0px;">
                                <p:outputLabel for="efetivosCodFil" value="#{localemsgs.Filiais}"/>
                                <p:selectOneMenu id="efetivosCodFil" value="#{mobEW.codFilEfetivos}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"  />
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                    <p:ajax event="itemSelect" listener="#{mobEW.atualizarListaPostosEfetivos}"
                                            update="efetivosPosto"/>
                                    <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-12" style="padding:0px 8px 0px 0px;">
                                <p:outputLabel for="efetivosPosto" value="#{localemsgs.Postos}" />
                                <p:selectOneMenu id="efetivosPosto" value="#{mobEW.secaoEfetivos}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" />
                                    <f:selectItems value="#{mobEW.postosEfetivos}" var="pstservEfetivos" itemValue="#{pstservEfetivos.secao}"
                                                   itemLabel="#{pstservEfetivos.secao}, #{pstservEfetivos.local}: #{pstservEfetivos.descContrato}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-1 col-sm-1 col-xs-12" style="padding:20px 0px 0px 0px;">
                                <p:commandLink update="msgs pnlGrdEfetivos pnlGrdPtosEfetivos"
                                               actionListener="#{mobEW.carregarEfetivos()}" style="width: 100% !important;">
                                    <label class="btn btn-primary" style="width: 100% !important; height: 34px !important; padding-top: 6px !important"><i class="fa fa-search"></i>&nbsp;&nbsp;#{localemsgs.Pesquisar}</label>
                                </p:commandLink>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100vh - 320px); width: 100%; overflow: hidden; margin-top: 10px; padding-left: 0px; padding-right: 0px;">
                                <ul class="nav nav-tabs">
                                    <li class="active"><a data-toggle="tab" href="#efetivosDetalhes"><i class="fa fa-users" aria-hidden="true"></i>&nbsp;#{localemsgs.Efetivos}</a></li>
                                    <li><a data-toggle="tab" href="#efetivosPonto"><i class="fa fa-clock-o" aria-hidden="true"></i>&nbsp;#{localemsgs.PontoEletronico}</a></li>
                                </ul>
                                <div class="tab-content">
                                    <div class="tab-pane fadeIn active" id="efetivosDetalhes" style="height: calc(100vh - 365px) !important; overflow: auto !important;">
                                        <i id="btImprimirEfetivos" class="fa fa-print" title="#{localemsgs.Imprimir}" style="width:50px; height: 50px; text-align: center; font-size: 16pt; color: #FFF; position: absolute; right: 10px; bottom: 0px; padding-top: 11px !important; cursor: pointer; background-color: red; border-radius: 50%; z-index: 99; border: 2px solid darkred"></i>

                                        <p:panel id="pnlGrdEfetivos">
                                            <table id="tblEfetivos" class="DataGrid" style="width: 100%">
                                                <thead>
                                                    <tr>
                                                        <td>#{localemsgs.Matr}</td>
                                                        <td>#{localemsgs.Nome}</td>
                                                        <td>#{localemsgs.Cargo}</td>
                                                        <td>#{localemsgs.Horario}</td>
                                                        <td>#{localemsgs.Escala}</td>
                                                        <td>#{localemsgs.Dt_Admis}</td>    
                                                        <td>#{localemsgs.Dt_Demis}</td>    
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <h:outputText escape="false" value="#{mobEW.htmlEfetivos}"></h:outputText>
                                                </tbody>
                                            </table>
                                        </p:panel>
                                    </div>
                                    <div class="tab-pane fadeIn" id="efetivosPonto" style="height: calc(100vh - 365px) !important; overflow: auto !important;">
                                        <i id="btImprimirEfetivosPontos" class="fa fa-print" title="#{localemsgs.Imprimir}" style="width:50px; height: 50px; text-align: center; font-size: 16pt; color: #FFF; position: absolute; right: 10px; bottom: 0px; padding-top: 11px !important; cursor: pointer; background-color: red; border-radius: 50%; z-index: 99; border: 2px solid darkred"></i>

                                        <p:panel id="pnlGrdPtosEfetivos">
                                            <table id="tblEfetivosPtos" class="DataGrid" style="width: 100%">
                                                <thead>
                                                    <tr>
                                                        <td>#{localemsgs.Matr}</td>
                                                        <td>#{localemsgs.Nome}</td>
                                                        <td>#{localemsgs.Posto}</td>
                                                        <td>#{localemsgs.Competencia}</td>
                                                        <td>#{localemsgs.DiaSem}</td>
                                                        <td>#{localemsgs.Hora1}</td>    
                                                        <td>#{localemsgs.Hora2}</td>    
                                                        <td>#{localemsgs.Hora3}</td>    
                                                        <td>#{localemsgs.Hora4}</td>    
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <h:outputText escape="false" value="#{mobEW.htmlEfetivosPontos}"></h:outputText>
                                                </tbody>
                                            </table>
                                        </p:panel>
                                    </div>
                                </div>

                            </div>
                        </p:panel>

                    </p:dialog>
                </h:form>

                <h:form id="formUltimaComunicacao" class="form-inline">
                    <p:dialog widgetVar="dlgUltimaComunicacao"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; width: calc(100vh - 150px) !important; min-height: calc(100vh - 150px) !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_Dashboard_cliente.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Dashboard}" style="color:#022a48"/>
                        </f:facet>      

                        <p:panel id="pnlUltima" style="background-color: #FFF; max-width: 100% !important; display: block !important; margin-top:-10px !important; z-index:999 !important; min-height:calc(100% - 28px) !important; height:calc(100% - 28px) !important; max-height:calc(100% - 28px) !important; padding-bottom: 0px !important" class="cadastrar">
                            <div id="divPaiDash" class="col-md-7 col-sm-7 col-xs-12" style="height: calc(100vh - 205px); padding-left: 0px !important; padding-bottom: 0px !important">
                                <div class="col-md-6 col-sm-6 col-xs-12" style="height: 116px; background-color: #EEE; border: thin solid #CCC; margin-top: 10px; padding: 2px 4px 2px 4px !important; border-radius: 4px; box-shadow: 2px 2px 4px #DDD; width: calc(100% - 275px)">
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 24px 6px 6px 6px !important">
                                        <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Funcionarios}</label>
                                        <p:selectOneMenu id="funcionarios" value="#{mobEW.funcionarioSelecionado}" converter="omnifaces.SelectItemsConverter"
                                                         filter="true" filterMatchMode="contains"
                                                         style="width: 100%; outline: none !important" >
                                            <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue=""></f:selectItem>
                                            <f:selectItems value="#{mobEW.funcionariosDash}" var="funcionarios" itemValue="#{funcionarios}"
                                                           itemLabel="#{funcionarios.nome}"/>
                                            <p:ajax event="itemSelect" listener="#{mobEW.carregarTodosTiposDash}" update="msgs pnlUltima"/>
                                        </p:selectOneMenu>
                                    </div>
                                </div>

                                <div class="col-md-6 col-sm-6 col-xs-12" style="height: 100px; width: 275px">
                                    <center>
                                        <table style="width: 275px">
                                            <tr>
                                                <td style="width: 130px !important">
                                                    <canvas id="Grafico" style="height: 120px !important; width: 120px !important" height="120" width="120"></canvas>
                                                </td>
                                                <td style="width: 110px; vertical-align: middle; padding-left: 12px">
                                                    <table style="height: 70px;">
                                                        <tr>
                                                            <td style="vertical-align: middle; width: 15px !important;"><label style="width: 15px; height: 15px; background-color: #1D761D"></label></td>
                                                            <td style="white-space: nowrap; color: #1D761D; display: block; width: 110px; text-align: left; padding-bottom: 8px; padding-left: 6px"> #{localemsgs.Ferias}</td>            
                                                        </tr>
                                                        <tr>
                                                            <td style="vertical-align: middle; width: 15px !important;"><label style="width: 15px; height: 15px; background-color: #f29100"></label></td>
                                                            <td style="white-space: nowrap; color: #f29100; display: block; width: 110px; text-align: left; padding-bottom: 8px; padding-left: 6px"> #{localemsgs.Atrasados}</td>
                                                        </tr>
                                                        <tr>
                                                            <td style="vertical-align: middle; width: 15px !important;"><label style="width: 15px; height: 15px; background-color: #D20000"></label></td>
                                                            <td style="white-space: nowrap; color: #D20000; display: block; width: 110px; text-align: left; padding-bottom: 8px; padding-left: 6px"> #{localemsgs.Faltas}</td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </center>
                                </div>

                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(33.33% - 35px); padding: 10px 6px 8px 0px !important; margin-top: 0px;">
                                    <div class="col-md-3 col-sm-3 col-xs-3" style="height: 100%; background-color: #1D761D; padding:2px !important;">
                                        <table>
                                            <tr>
                                                <td>
                                                    <label class="Qtde">#{mobEW.qtdeFerias}</label>
                                                    <label>#{localemsgs.Ferias}</label>
                                                    <i class="fa fa-plane" aria-hidden="true"></i>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9" style="height: 100%; border-top: thin solid #1D761D; border-right: thin solid #1D761D; border-bottom: thin solid #1D761D; border-radius: 0px 4px 4px 0px; padding: 8px !important; overflow: auto;">
                                        <h:outputText value="#{mobEW.htmlFerias}" escape="false" />
                                    </div>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(33.33% - 35px); padding: 10px 6px 8px 0px !important;">
                                    <div class="col-md-3 col-sm-3 col-xs-3" style="height: 100%; background-color: #f29100; padding:0px !important;">
                                        <table>
                                            <tr>
                                                <td>
                                                    <label class="Qtde">#{mobEW.qtdeAtrasados}</label>
                                                    <label style="margin-top: 1px !important;">#{localemsgs.Atrasos}</label>
                                                    <i class="fa fa-clock-o" aria-hidden="true"></i>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9" style="height: 100%; border-top: thin solid #f29100; border-right: thin solid #f29100; border-bottom: thin solid #f29100; border-radius: 0px 4px 4px 0px; padding: 8px !important; overflow: auto;">
                                        <h:outputText value="#{mobEW.htmlAtrasados}" escape="false" />
                                    </div>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(33.34% - 50px); padding: 10px 6px 8px 0px !important;">
                                    <div class="col-md-3 col-sm-3 col-xs-3" style="height: 100%; background-color: #D20000; padding:0px !important;">
                                        <table>
                                            <tr>
                                                <td>
                                                    <label class="Qtde">#{mobEW.qtdeFaltas}</label>
                                                    <label style="padding: 0px !important; margin: 0px !Important">#{localemsgs.Faltas}</label>
                                                    <i class="fa fa-user-times" aria-hidden="true"></i>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-9 col-sm-9 col-xs-9" style="height: 100%; border-top: thin solid #D20000; border-right: thin solid #D20000; border-bottom: thin solid #D20000; border-radius: 0px 4px 4px 0px; padding: 8px !important; overflow: auto;">
                                        <h:outputText value="#{mobEW.htmlFaltas}" escape="false" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-12" style="height: 80px; background-color: #EEE; border: thin solid #CCC; margin-top: 10px; padding: 2px 4px 2px 4px !important; border-radius: 4px; box-shadow: 2px 2px 4px #DDD;">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px 3px 6px 6px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Operador}</label>
                                    <p:selectOneMenu id="operador" value="#{mobEW.pessoaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%; outline: none !important" >
                                        <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue=""></f:selectItem>
                                        <f:selectItems value="#{mobEW.pessoas}" var="operador" itemValue="#{operador}"
                                                       itemLabel="#{operador.nome}"/>
                                        <p:ajax event="itemSelect" listener="#{mobEW.selecionarPessoaUltimaComunicacao}" update="msgs pnlUltima"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div id="PaiMap" class="col-md-5 col-sm-5 col-xs-12" style="height: calc(100vh - 318px); border: thin solid #DDD; margin-top: 15px !important;  box-shadow: 2px 2px 4px #DDD; padding: 3px !important">
                                <label style="background-color: steelblue !important; color: #FFF; font-size: 12pt; width: 100%; padding: 2px 2px 3px 2px !important; margin:0px 0px 2px 0px !important; text-align: center"><i class="fa fa-map-marker"></i>&nbsp;&nbsp;#{localemsgs.UltimaComunicacao}</label>
                                <div id="mapGoogle" style="min-width:100% !important;width:100% !important;max-width:100% !important;"></div>
                            </div>
                            <script type="text/javascript">
                                // <![CDATA[
                                var map;

                                function inicioMapaUltimaComunicacao() {

                                    CarregarGrafico();


                                    let Altura = $('#mapGoogle').parent('div').height() - 30;

                                    $('#mapGoogle').css('min-height', Altura + 'px');

                                    map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                        zoom: 11,
                                        center: #{mobEW.centro},
                                        gestureHandling: 'cooperative'
                                    });

                                #{mobEW.markers}
                                }

                                function CarregarGrafico() {
                                    let options = {
                                        animateScale: false,
                                        responsive: true,
                                        maintainAspectRatio: true
                                    };

                                    let DadosGrafico1 = new Array();

                                    DadosGrafico1.push({
                                        value: ConverterFloat('#{mobEW.feriasQtde}'),
                                        label: '#{localemsgs.Ferias}',
                                        color: '#1D761D'
                                    });

                                    DadosGrafico1.push({
                                        value: ConverterFloat('#{mobEW.atrasadosQde}'),
                                        label: '#{localemsgs.Atrasados}',
                                        color: '#f29100'
                                    });

                                    DadosGrafico1.push({
                                        value: ConverterFloat('#{mobEW.faltasQtde}'),
                                        label: '#{localemsgs.Faltas}',
                                        color: '#D20000'
                                    });

                                    var ctx = document.getElementById('Grafico').getContext("2d");
                                    var myPieChart = new Chart(ctx).Pie(DadosGrafico1, options);
                                }

                                $(document).ready(function () {
                                    inicioMapaUltimaComunicacao();
                                });
                                // ]]>
                            </script>   
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formBoletimTrabalho" class="form-inline">
                    <p:dialog widgetVar="dlgBoletimTrabalho"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; width: calc(100vh - 150px) !important; min-height: calc(100vh - 150px) !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cadastros.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ResumoTrabalho}" style="color:#022a48"/>
                        </f:facet>      

                        <p:panel id="pnlBoletim" style="background-color: #FFF; max-width: 100% !important; display: block !important; margin-top:-10px !important; z-index:999 !important; min-height:calc(100% - 20px) !important; height:calc(100% - 20px) !important; max-height:calc(100% - 20px) !important" class="cadastrar">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: 80px; background-color: #EEE; border: thin solid #CCC; margin-top: 10px; padding: 2px 4px 2px 4px !important; border-radius: 4px; box-shadow: 2px 2px 4px #DDD;">
                                <div class="col-md-9 col-sm-9 col-xs-9" style="padding: 6px 3px 6px 6px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Operador}</label>
                                    <p:selectOneMenu id="operador" value="#{mobEW.pessoaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%; outline: none !important" >
                                        <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue=""></f:selectItem>
                                        <f:selectItems value="#{mobEW.pessoas}" var="operador" itemValue="#{operador}"
                                                       itemLabel="#{operador.nome}"/>
                                        <p:ajax event="itemSelect" listener="#{mobEW.selecionarPessoa}" update="msgs pnlBoletim"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-3" style="padding: 6px 6px 6px 3px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF; width: 100% !Important; text-align: center !important; color: orangered; white-space: nowrap; overflow: hidden; text-overflow: ellips; height: 15px !important">#{localemsgs.LocaisVisitados}</label>
                                    <label id="lblQtdeVisitas" style="background-color: lightyellow; border: thin solid orangered; color: orangered; width: 100%; height: 34px !important; border-radius: 3px; text-align: left; font-weight: bold; font-size: 16pt !important; padding-left: 8px !Important; padding-top: 1px !important;text-align: center !important;"><i class="fa fa-cog fa-spin fa-fw"></i></label>
                                </div>
                            </div>

                            <div id="PaiMap" class="col-md-5 col-sm-5 col-xs-12" style="height: calc(100vh - 308px); border: thin solid #DDD; margin-top: 15px !important;  box-shadow: 2px 2px 4px #DDD; padding: 3px !important">
                                <div id="mapGoogle" style="min-width:100% !important;width:100% !important;max-width:100% !important;"><i class="fa fa-cog fa-spin fa-fw" style="position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size: 32pt; width: 45px; height:45px;"></i></div>
                            </div>

                            <div id="PaidivItensHistorico" class="col-md-7 col-sm-7 col-xs-12" style="padding:15px 0px 0px 10px !important;height: calc(100vh - 293px);">
                                <div id="divItensHistorico" class="col-md-12 col-sm-12 col-xs-12" style="height: 100% !important; overflow: auto !important;border: thin solid #DDD; padding: 3px !important;  box-shadow: 2px 2px 4px #DDD;"><i class="fa fa-cog fa-spin fa-fw" style="position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size: 32pt; width: 45px; height:45px;"></i></div>
                            </div> 
                            <script type="text/javascript">
                                // <![CDATA[
                                var map;

                                #{mobEW.listaTrabalhos}

                                function inicioMapaServico() {
                                    let Altura = $('#mapGoogle').parent('div').height() - 0;

                                    $('#mapGoogle').css('min-height', Altura + 'px');

                                    map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                        zoom: 11,
                                        center: #{mobEW.centro},
                                        gestureHandling: 'cooperative'
                                    });

                                #{mobEW.markers}
                                }

                                $(document).ready(function () {
                                    inicioMapaServico();
                                });
                                // ]]>
                            </script>   
                        </p:panel>
                    </p:dialog>
                </h:form>

                <p:dialog positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;"
                          header="#{localemsgs.Opcoes}" widgetVar="dlgOk">
                    <h:form style="#{login.postos.size() gt 1 ? 'width: 400px' : 'width: 250px' }">
                        <div class="form-inline">
                            <h:outputText value="#{localemsgs.EscolherOpcao}:" style="text-align: center"/>
                        </div>
                        <p:spacer height="30px"/>
                        <div class="form-inline">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                         styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center">
                                <h:panelGrid columns="1" style="width: 100%; text-align: center"
                                             rendered="#{login.postos.size() gt 1}">
                                    <p:commandLink oncomplete="PF('dlgSelecionarClienteEW').show()">
                                        <p:graphicImage url="../assets/img/icone_clientes.png" height="40"/>
                                    </p:commandLink>
                                    <p:commandLink oncomplete="PF('dlgSelecionarClienteEW').show()">
                                        <h:outputText  value="#{localemsgs.TrocarCliente}"/>
                                    </p:commandLink>
                                </h:panelGrid>

                                <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                    <p:commandLink oncomplete="PF('dlgTrocarSenha').show()">
                                        <p:graphicImage url="../assets/img/icone_configuracoes.png" height="40"/>
                                    </p:commandLink>
                                    <p:commandLink oncomplete="PF('dlgTrocarSenha').show()" >
                                        <h:outputText  value="#{localemsgs.TrocarSenha}"/>
                                    </p:commandLink>
                                </h:panelGrid>

                                <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                    <p:commandLink action="#{login.logOutRH}">
                                        <p:graphicImage url="../assets/img/icone_sair.png" height="40"/>
                                    </p:commandLink>
                                    <p:commandLink action="#{login.logOutRH}">
                                        <h:outputText value="#{localemsgs.Sair}"/>
                                    </p:commandLink>
                                </h:panelGrid>
                            </p:panelGrid>
                        </div>
                    </h:form>
                </p:dialog>

                <h:form id="clienteEW">

                    <p:dialog widgetVar="dlgSelecionarClienteEW" positionType="absolute" id="dlgClienteEW"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">
                        <f:facet name="header">
                            <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="tabelaClientesEW" style="width: 440px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabelaClienteEW" value="#{login.postos}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.postoSelecionado}" rowKey="#{cli.secao}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabelaClienteEW"
                                             style="font-size: 12px; float: left" styleClass="tabela" filteredValue="#{login.postosFiltrados}">
                                    <f:facet name="footer">
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                            </div>
                                            <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                                <p:inputText id="globalFilter" onkeyup="PF('tabelaClienteEW').filter()" style="width:150px;"/>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectEW}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 150px" filterBy="#{cli.secao}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.secao}" title="#{cli.secao}" />
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" filterBy="#{cli.local}" style="width: 285px"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.local}" title="#{cli.local}" converter="tradutor"/>
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteEW}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="trocarsenha">
                    <p:dialog widgetVar="dlgTrocarSenha" positionType="absolute" id="dlgTrocarSenha"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('../assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">
                        <f:facet name="header">
                            <img src="../assets/img/icone_configuracoes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="panelSenha" style="width: 440px; background: transparent">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}" />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:password id="atual" value="#{mobEW.senhaAtual}" label="#{localemsgs.SenhaAtual}" required="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.SenhaAtual}"/>
                                    </div>
                                </div>
                            </div>

                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="nova1" value="#{localemsgs.NovaSenha}" />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:password id="nova1" value="#{mobEW.novaSenha}" match="nova2"
                                                    label="#{localemsgs.NovaSenha}" required="true" feedback="true"
                                                    promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                    goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NovaSenha}">
                                            <f:validateRegex pattern="^[0-9]{5,20}$" for="nova1"/>
                                        </p:password>
                                    </div>
                                </div>
                            </div>

                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="nova2" value="#{localemsgs.confirmarNovaSenha}" />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:password id="nova2" value="#{mobEW.novaSenha}" label="#{localemsgs.SenhaAtual}" required="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Confirmacao}"/>
                                    </div>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{mobEW.trocarSenha}"
                                               title="#{localemsgs.Concluido}" update="msgs">
                                    <p:graphicImage url="../assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form id="batidaPonto" style="overflow:hidden !important;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgRelatorio').hide()"/>
                    <p:dialog widgetVar="dlgBatidaPonto" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgRelatorio"
                              style="height:98% !important; min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color: #FFF !important;">
                        <f:facet name="header">
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-1,ui-grid-col-9,ui-grid-col-1,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="max-height:20px !important;margin:0px !important; width:100% !important; height:100% !important;">
                                <img src="#{mobEW.getImagemTipo(mobEW.logsSatMobEWSelecionado.tipo, mobEW.logsSatMobEWSelecionado.chave)}" height="40" width="40"/>

                                <h:outputText value="#{mobEW.titulo}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px;" />
                            </p:panelGrid>
                        </f:facet>
                        <p:panel styleClass="cadastrar" id="cadastrar" style="overflow: hidden !important; max-width: 100% !important;padding:0px !important; height: calc(100vh - 175px); background-color: #FFF !important">
                            <ul class="nav nav-tabs">
                                <li class="active"><a data-toggle="tab" href="#VisualizacaoDiario"><i class="fa fa-certificate" aria-hidden="true"></i>&nbsp;#{localemsgs.Dia}</a></li>
                                <li><a data-toggle="tab" href="#VisualizacaoMensal"><i class="fa fa-calendar" aria-hidden="true"></i>&nbsp;#{localemsgs.Mensal}</a></li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane fadeIn active" id="VisualizacaoDiario">
                                    <div class="col-md-6 col-sm-6 col-xs-12" style="border: thin solid #DDD; padding: 12px !important">
                                        <h:outputText escape="false" value="#{mobEW.batidaPontoHtmlTopo}" />

                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 8px 0px 0px 0px !important">
                                            <label style="background-color: steelblue; color: #FFF; width: 100%; text-align: center; padding: 5px; font-weight: bold; margin: 0px; font-size: 10pt"><i class="fa fa-map-marker"></i>&nbsp;&nbsp;#{localemsgs.Localizacao}</label>
                                            <p:gmap id="gmap" center="#{mobEW.centroMapa}" zoom="#{mobEW.zoomMapa}" type="TERRAIN"
                                                    style="margin-top:0px; height: calc(100vh - 420px); width:100% !important; border:thin solid #BBB;" model="#{mobEW.mapa}"/>
                                        </div>
                                    </div>
                                    <div id="divHistoricoPonto" class="col-md-6 col-sm-6 col-xs-12" style="padding: 0px 0px 12px 0px">
                                        <label style="background-color: steelblue; color: #FFF; font-weight: bold; padding: 4px 10px 4px 20px; width: 100%; border-radius:20px"><i class="fa fa-hand-pointer-o" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.PontoEletronicoSelecionado}</label>

                                        <div class="col-md-12 col-sm-12 col-xs-12" style="height: 136px; padding-top: 10px; border: thin solid steelblue; border-radius: 6px; box-shadow: 2px 2px 4px #EEE, -2px -2px 4px #EEE; margin-top: 6px; background-color:  azure">
                                            <div title="#{localemsgs.CliqueVisualizarFoto}" style="float: left; background-color: #EEE; border: 2px solid #DDD; margin-top: 8px !important; width: 100px; height: 100px; border-radius: 50%; background-image: url(#{mobEW.batidaPontoFoto}); background-size: cover; background-position: center center; cursor: pointer;" onclick="AbrirFoto('#{mobEW.batidaPontoFoto}');"></div>
                                            <div style="height: 120px; float: left; width: calc(100% - 118px); margin-left: 18px">
                                                <table class="DetalhesPonto" style="width: 100%">
                                                    <tr>
                                                        <td>#{mobEW.logsSatMobEWSelecionado.funcionario}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><label class="#{mobEW.batidaPontoTipo eq '0'? 'batida-saida': 'batida-entrada'}" style="width: 130px !important;">#{mobEW.batidaPontoTipo eq '0'? localemsgs.Saida: localemsgs.Entrada}</label></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i class="fa fa-clock-o" aria-hidden="true" style="width: 30px; text-align: center"></i><b>#{localemsgs.Hora}: </b>#{mobEW.logsSatMobEWSelecionado.hora}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><i class="fa fa-id-card-o" aria-hidden="true" style="width: 30px; text-align: center"></i><b>#{localemsgs.Cargo}: </b>#{mobEW.logsSatMobEWSelecionado.cargo}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><i class="fa fa-map-marker" aria-hidden="true" style="width: 30px; text-align: center"></i><b>#{localemsgs.Local}: </b>#{mobEW.logsSatMobEWSelecionado.posto}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><i class="fa fa-map-pin" aria-hidden="true" style="width: 30px; text-align: center"></i><b>#{localemsgs.Distancia}: </b>#{mobEW.logsSatMobEWSelecionado.distancia_km}</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>

                                        <label style="background-color: #303030; color: #FFF; font-weight: bold; padding: 4px 10px 4px 20px; width: 100%; border-radius:20px; margin-top: 20px"><i class="fa fa-history" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.PontoEletronicoAnteriores}</label>

                                        <h:outputText escape="false" value="#{mobEW.batidaPontoHtmlHistorico}" />
                                    </div>
                                </div>
                                <div class="tab-pane fadeIn" id="VisualizacaoMensal" style="padding: 3px 0px 10px 10px !important;">
                                    <label style="background-color: #303030; color: #FFF; font-weight: bold; padding: 4px 10px 4px 20px; width: calc(100% - 10px); border-radius:20px; margin-top: 10px; margin-bottom: 0px !important"><i class="fa fa-history" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.PontoEletronicoMensal}</label>

                                    <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100vh - 280px); overflow: auto !important; padding: 0px 10px 2px 0px !important;">
                                        <h:outputText escape="false" value="#{mobEW.batidaPontoHtmlHistoricoMensal}" />
                                    </div>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>


                <h:form id="relatorio" style="overflow:hidden !important;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgRelatorio').hide()"/>
                    <p:dialog widgetVar="dlgRelatorio" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgRelatorio"
                              style="height:98% !important; min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color: #FFF !important;">
                        <f:facet name="header">
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-1,ui-grid-col-9,ui-grid-col-1,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="max-height:20px !important;margin:0px !important; width:100% !important; height:100% !important;">
                                <img src="#{mobEW.getImagemTipo(mobEW.logsSatMobEWSelecionado.tipo, mobEW.logsSatMobEWSelecionado.chave)}" height="40" width="40"/>

                                <h:outputText value="#{mobEW.titulo}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px;" />

                                <p:commandLink title="#{localemsgs.Editar}" update="relatorio msgs" id="btnEditar"
                                               ajax="false" rendered="false"
                                               actionListener="#{mobEW.gerarRelatorioDownload}">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                    <p:fileDownload value="#{mobEW.arquivoRelatorio}" />
                                </p:commandLink>
                                <div style="position:absolute; right:16px;top:26px; z-index: 10 !important; white-space:nowrap !important; width:400px !important;text-align:right !important; display:#{mobEW.logsSatMobEWSelecionado.tipo eq '2' and mobEW.supervisor?'block':'none'}">
                                    <label for="filtroWeb"
                                           style="color:maroon; white-space:nowrap !important; font-weight:bold; text-align:right !important; padding-right:32px; font-size:8pt !important;">
                                        #{localemsgs.RelatorioPublico}:&nbsp;
                                    </label>

                                    <p:selectBooleanCheckbox id="filtroWeb" value="#{mobEW.filtroWeb}" rendered="#{mobEW.logsSatMobEWSelecionado.tipo eq '2' and mobEW.supervisor}" style="position:absolute; top:28px; right:26px !important;">
                                        <p:ajax update="msgs" listener="#{mobEW.atualizarFiltroWeb}" />
                                    </p:selectBooleanCheckbox>

                                </div>

                                <p:commandLink title="#{localemsgs.Editar}" update="relatorio msgs" id="btnEditar2"
                                               actionListener="#{mobEW.buttonAction}"
                                               rendered="false">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>
                            </p:panelGrid>
                        </f:facet>
                        <p:panel styleClass="cadastrar" id="cadastrar" style="overflow:hidden !important; max-width: 100% !important;padding:0px !important; height: calc(100vh - 175px); background-color: #FFF !important">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: calc(100vh - 178px); overflow:hidden !important; overflow-y: auto!important;  padding:0px !important; background-color: #FFF !important;">
                                <div id="pnlHTMLpai" class="col-md-7 col-sm-7 col-xs-12 #{mobEW.getTipoItem(mobEW.logsSatMobEWSelecionado.tipo, mobEW.logsSatMobEWSelecionado.chave, mobEW.logsSatMobEWSelecionado.titulo)}" style="overflow:hidden !important; padding:0px 8px 6px 4px !important; min-height:100%;background-color: #FFF !important">
                                    <label id="btExpandirMapa" style="position:absolute; width:50px; height: 50px; border-radius:50%; background-color:#022a48; top:22px; z-index:9999 !important; right:20px; cursor:pointer; text-align:center"><img src="../assets/img/icone_abrirmapa.png" style="max-width:70%; max-height:70%; margin-top:8px; margin-left:1px;" /></label>

                                    <p:panel id="pnlHTML" style="min-height:100% !important; height:100% !important; max-height:100% !important; background: #FFF !important; padding:0px 10px 0px 10px !important; border:thin solid #DDD !important; box-shadow: 2px 2px 3px #CCC; min-height:100% !important;">
                                        <div id="DadosHTML"  class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; margin: 0px !important">
                                            <h:outputText value="#{mobEW.html}" escape="false" />
                                        </div>
                                        <p:gmap id="gmap" center="#{mobEW.centroMapa}" zoom="#{mobEW.zoomMapa}" type="TERRAIN"
                                                style="margin-top:6px; height: calc(100vh - #{!mobEW.pstInspecaoSelecionadaDetalhes.isEmpty()?'460px' : '400px'}); width:100% !important; border:thin solid #BBB;" model="#{mobEW.mapa}" rendered="#{mobEW.mapa ne null}"/>
                                    </p:panel>
                                </div>

                                <div id="divConteudo" class="col-md-5 col-sm-5 col-xs-12 #{mobEW.getTipoItem(mobEW.logsSatMobEWSelecionado.tipo, mobEW.logsSatMobEWSelecionado.chave, mobEW.logsSatMobEWSelecionado.titulo)}"
                                     style=" padding:0px 8px 6px 4px !important; position:relative;">
                                    <p:panel id="panelMedia" style="height: calc(100vh - 183px) !important; background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important;" styleClass="col-md-12"
                                             rendered="#{mobEW.fotosRelatorio.size() gt 0}">

                                        <table style="max-height:100% !important;" summary="sasw" cellspacing="0" cellpadding="0" border="0">
                                            <tbody>
                                                <tr>
                                                    <td style="padding:.75pt .75pt .75pt .75pt">
                                                        <p:commandButton icon=" ui-icon-triangle-1-w" style="width: 30px; float: left;"
                                                                         action="#{mobEW.voltarFotoRelatorio}" rendered="#{mobEW.fotosRelatorio.size() gt 1}"
                                                                         update="relatorio:panelFoto relatorio:panelVideo relatorio:panelMedia msgs">
                                                        </p:commandButton>
                                                    </td>
                                                    <td style="padding:.75pt .75pt .75pt .75pt; width: 100%">
                                                        <p:panel id="panelFoto" rendered="#{mobEW.video eq false}">
                                                            <div id="divFoto" style="width: 100%; height: 100%; margin: 0 auto; text-align: center;white-space: nowrap; float: left;">
                                                                <p:lightBox style=" text-align: center; width:100%; height: calc(100vh - 190px);
                                                                            display: inline-block; vertical-align: middle;" id="foto">
                                                                    <span style="display: inline-block; height: 100%; vertical-align: middle;"></span>
                                                                    <h:outputLink value="#{mobEW.fotoRelatorio}">
                                                                        <h:graphicImage value="#{mobEW.fotoRelatorio}" id="fotoRelatorio" style="height: auto !important; max-height: 100%; max-width:95% !important"/>
                                                                    </h:outputLink>
                                                                </p:lightBox>
                                                                <script type="text/javascript">
                                                                    rotate = 0;
                                                                    function ajusteImagem() {
                                                                        /*var imagem = document.getElementById("relatorio:fotoRelatorio");
                                                                         var divFoto = document.getElementById("divFoto");
                                                                         console.log(divFoto.clientWidth);
                                                                         console.log(divFoto.clientHeight);
                                                                         if (imagem.clientWidth > imagem.clientHeight) {
                                                                         imagem.width = divFoto.clientWidth;
                                                                         imagem.height = (divFoto.clientWidth * imagem.clientHeight) / imagem.clientWidth;
                                                                         } else {
                                                                         imagem.height = divFoto.clientHeight;
                                                                         imagem.width = (divFoto.clientHeight * imagem.clientWidth) / imagem.clientHeight;
                                                                         }*/
                                                                    }

                                                                    function rotateLeft(e) {
                                                                        e.preventDefault();
                                                                        if (rotate === -360) {
                                                                            rotate = 0;
                                                                        }

                                                                        rotate = rotate + -90;
                                                                        var img = document.getElementById("relatorio:fotoRelatorio");
                                                                        img.style.transform = "rotate(" + rotate + "deg)";
                                                                        ajusteImagem();
                                                                    }
                                                                    ;
                                                                    function rotateRight(e) {
                                                                        e.preventDefault();
                                                                        if (rotate === 360) {
                                                                            rotate = 0;
                                                                        }

                                                                        rotate = rotate + 90;
                                                                        var img = document.getElementById("relatorio:fotoRelatorio");
                                                                        img.style.transform = "rotate(" + rotate + "deg)";
                                                                        ajusteImagem();
                                                                    }
                                                                    ;
                                                                </script>
                                                            </div>
                                                        </p:panel>
                                                        <p:panel id="panelVideo" rendered="#{mobEW.video}" style="background: transparent">
                                                            <p:media value="#{mobEW.fotoRelatorio}" style="width: 100%; background: transparent" height="425">
                                                                <f:param name="autoPlay" value="false" />
                                                            </p:media>
                                                        </p:panel>
                                                    </td>

                                                    <td style="padding:.75pt .75pt .75pt .75pt">
                                                        <p:commandButton icon=" ui-icon-triangle-1-e" style="width: 30px;"
                                                                         action="#{mobEW.avancarFotoRelatorio}" rendered="#{mobEW.fotosRelatorio.size() gt 1}"
                                                                         update="relatorio:panelFoto relatorio:panelVideo relatorio:panelMedia msgs">
                                                        </p:commandButton>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </p:panel>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:commandButton id="rotateLeft" icon="ui-icon-arrowreturnthick-1-w" style="position:absolute; right:8px !important; height:calc(100vh - 187px); top:2px; border:none !important;width: 30px;border-radius:0px 4px 4px 0px !important;outline:none !important; box-shadow:none !important; width:20px !important; background-color:#BBB !important;"
                                                         onclick="rotateLeft(event)" rendered="#{not empty mobEW.fotoRelatorio}"/>
                                        <p:commandButton id="rotateRight" icon="ui-icon-arrowreturnthick-1-e" style="position:absolute; left:6px !important; height:calc(100vh - 187px); top:2px; border:none !important; width: 30px; border-radius: 4px 0px 0px 4px !important;outline:none !important; box-shadow:none !important; width:20px !important; background-color:#BBB !important;"
                                                         onclick="rotateRight(event)" rendered="#{not empty mobEW.fotoRelatorio}"/>
                                    </p:panelGrid>

                                    <p:chart type="bar" model="#{mobEW.graficoRonda}" style="height: 100px" rendered="#{!mobEW.rondas.isEmpty()}"/>

                                    <p:panel id="panelInspecoesDetalhes" style="height: calc(100vh - 183px); background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important; padding: 3px !important; overflow: auto" styleClass="col-md-12"
                                             rendered="#{!mobEW.pstInspecaoSelecionadaDetalhes.isEmpty()}">
                                        <p:remoteCommand name="rcResposta" partialSubmit="true" 
                                                         process="@this" 
                                                         update="msgs main:pnlListaPontos panelInspecoesDetalhes" 
                                                         actionListener="#{mobEW.editarRespostaInsp}" />  

                                        <ui:repeat value="#{mobEW.pstInspecaoSelecionadaDetalhes}" var="pstInspecao">
                                            <p:panel style="margin-bottom:0px; padding: 0px !important;">                                                

                                                <label style="font-size: 14pt; font-weight: 500; width: 100%; color: steelblue; padding: 8px 0px 5px 10px; background-color: #EEE; text-align: left; margin: 0px 0px 5px 0px !important; text-shadow: 1px 1px #FFF; border: thin solid #DDD"><i class="fa fa-check-square-o" aria-hidden="true" style="font-size: 12pt !important;"></i>&nbsp;&nbsp;#{pstInspecao.pstInspecao.pergunta}  <label class="btn btn-warning" ref="btEditarInsp" ref-codigo="#{pstInspecao.pstInspecao.codigo}" ref-sequencia="#{pstInspecao.pstInspecao.sequencia}" ref-resp="#{pstInspecao.pstInspecao.resposta}" style="display:#{pstInspecao.pstInspecao.resposta ne null and pstInspecao.pstInspecao.resposta ne ''? '': 'none'} margin-left: 10px; margin-top: -4px; padding: 3px 6px 2px 6px; font-size: 8pt;">#{localemsgs.EditarResposta}</label></label>
                                                <label style="display: #{pstInspecao.fotos.size() eq 0 or (pstInspecao.pstInspecao.resposta ne null and pstInspecao.pstInspecao.resposta ne '') ? '': 'none'}; font-size: 10pt; color: #666; width: 100% !important; text-align: left; padding: 0px 0px 5px 0px; margin: 3px 0px 0px 0px !important;"><span style="float: left; font-weight: 500;width: 100%; text-align: left; padding-left: 8px; color: #303030 !important; text-transform: uppercase !important"><h:outputText class="obj_#{pstInspecao.pstInspecao.codigo}_#{pstInspecao.pstInspecao.sequencia}" escape="false" value="#{pstInspecao.pstInspecao.resposta}" /></span></label>

                                                <p:panel id="panelMedia" style="background-color: transparent; padding-right:0px !important; padding-bottom: 8px !important" rendered="#{pstInspecao.fotos.size() gt 0}">
                                                    <ui:repeat value="#{pstInspecao.fotos}" var="pstInspecaoFotoArray">
                                                        <div class="col-md-3 col-sm-3 col-xs-4" style="padding: 3px !important; margin-bottom: 4px !important">
                                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px !important; border: thin solid #CCC;">
                                                                <img src="#{pstInspecaoFotoArray}" style="max-width: 100%;" onclick="AbrirFoto('#{pstInspecaoFotoArray}')" />
                                                            </div>
                                                        </div>
                                                    </ui:repeat>
                                                </p:panel>

                                                <p:panel id="panelMediaVideo" style="background-color: transparent; padding-right:0px !important; text-align:center !important;"
                                                         rendered="#{pstInspecao.videos.size() gt 0}">
                                                    <table summary="sasw" cellspacing="0" cellpadding="0" border="0" style="background-color: transparent; border-radius: 3px !important;margin-bottom:10px !important; border:thin solid #CCC !important; width:100%">
                                                        <tbody>
                                                            <tr>
                                                                <td style="padding:.75pt .75pt .75pt .75pt">
                                                                    <p:commandButton icon=" ui-icon-triangle-1-w" style="width: 30px; float: left;"
                                                                                     action="#{pstInspecao.voltarVideo}" rendered="#{pstInspecao.videos.size() gt 1}"
                                                                                     update="@parent msgs">
                                                                    </p:commandButton>
                                                                </td>
                                                                <td style="padding:.75pt .75pt .75pt .75pt; width: 100%">
                                                                    <p:panel id="panelVideo" style="background: transparent">
                                                                        <p:media value="#{pstInspecao.video}" style="max-width: 100% !important; vertical-align: middle;">
                                                                            <f:param name="autoPlay" value="false" />
                                                                        </p:media>
                                                                    </p:panel>
                                                                </td>
                                                                <td style="padding:.75pt .75pt .75pt .75pt">
                                                                    <p:commandButton icon=" ui-icon-triangle-1-e" style="width: 30px;"
                                                                                     action="#{pstInspecao.avancarVideo}" rendered="#{pstInspecao.videos.size() gt 1}"
                                                                                     update="@parent msgs">
                                                                    </p:commandButton>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </p:panel>

                                                <p:panel id="panelAudio" style="background-color: transparent; padding-right:0px !important; padding-bottom: 8px !important" rendered="#{pstInspecao.audios.size() gt 0}">
                                                    <ui:repeat value="#{pstInspecao.audios}" var="pstInspecaoAudioArray">
                                                        <audio controls="controls">
                                                            <source src="#{pstInspecaoAudioArray}" type="audio/mp3" />
                                                            Seu browser não suporta áudio
                                                        </audio>
                                                    </ui:repeat>
                                                </p:panel> 
                                            </p:panel>
                                        </ui:repeat>
                                    </p:panel>

                                    <p:panel style="height: calc(100vh - 184px) !important; background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important; padding:10px !important;" styleClass="col-md-12"
                                             rendered="#{!mobEW.rondas.isEmpty()}">
                                        <p:dataTable id="tabela" value="#{mobEW.rondas}" rendered="#{!mobEW.rondas.isEmpty()}"
                                                     var="ronda" rowKey="#{ronda.sequencia}" sortBy="#{ronda.hr_alter}"
                                                     resizableColumns="true" styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                                     rowIndexVar="ordem" style="font-size: 12px">
                                            <p:column headerText="#{localemsgs.NomeTag}">
                                                <h:outputText value="#{ronda.descricao}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.OrdemLeitura}" style="width: 140px; text-align: center">
                                                <h:outputText value="#{ordem+1}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TagTour}" style="width: 140px; text-align: center">
                                                <h:outputText value="#{ronda.codDepen}" converter="conversor0"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hora}" style="width: 70px; text-align: center">
                                                <h:outputText value="#{ronda.hr_alter}" converter="conversorHora"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>

                                    <p:panel style="height: calc(100vh - 184px) !important; background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important; padding:10px !important;" styleClass="col-md-12"
                                             rendered="#{!mobEW.contatos.isEmpty()}">

                                        <p:dataTable id="tabelaParadas" value="#{mobEW.contatos}" rendered="#{!mobEW.contatos.isEmpty()}"
                                                     var="conts" sortBy="#{conts.hrCheg}"
                                                     resizableColumns="true" styleClass="tabela" emptyMessage="#{localemsgs.SemRegistros}"
                                                     style="font-size: 12px;">
                                            <p:column headerText="#{localemsgs.Local}" style="width: 200px;">
                                                <h:outputText value="#{conts.descricao}" />
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HrCheg}" style="width: 70px; text-align: center">
                                                <h:outputText value="#{conts.hrCheg}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.HrSaida}" style="width: 70px; text-align: center">
                                                <h:outputText value="#{conts.hrSaida}" converter="conversorHora"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </div>
                            </div>
                        </p:panel>
                        <script type="text/javascript">
                            // <![CDATA[
                            setTimeout(function () {
                                AlinhaObjetos();
                            }, 0);
                            function AlinhaObjetos() {
                                if (!$('div[id*="divConteudo"] div div').html() &&
                                        !$('div[id*="divConteudo"] div table').html() &&
                                        !$('div[id*="divConteudo"]').find('[id*="panelInspecoesDetalhes"]').html() {

                                    $('div[id*="divConteudo"]').css('display', 'none');
                                    $('div[id*="pnlHTMLpai"]').css('min-width', '100%');
                                    if ($(document).width() > 700) {
                                        $('[id*="DadosHTML"]').css('min-width', '49.5%').css('width', '49.5%').css('max-width', '49.5%').css('float', 'left');
                                        $('div[id*="gmap"]').css('min-width', '49.5%').css('width', '49.5%').css('max-width', '49.5%').css('float', 'right').css('min-height', '400px');
                                    } else {
                                        $('[id*="DadosHTML"]').css('min-width', '100%').css('width', '100%').css('max-width', '100%').css('float', 'initial');
                                        $('div[id*="gmap"]').css('min-width', '100%').css('width', '100%').css('max-width', '100%').css('float', 'initial').css('min-height', '0px');
                                    }
                                }
                            }

                            $(window).resize(function () {
                                AlinhaObjetos();
                            });
                            // ]]>
                        </script>
                    </p:dialog>
                </h:form>

                <h:form id="novoLancamento" style="overflow:hidden !important; padding-bottom:0px !important">
                    <p:hotkey bind="esc" oncomplete="PF('dlgNovoLancamento').hide()"/>
                    <p:dialog widgetVar="dlgNovoLancamento" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgNovoLancamento"
                              style="max-height:95% !important; height:95% !important;min-height:95%; min-width:95%; max-width:95%; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <p:panelGrid columns="4" columnClasses="ui-grid-col-1,ui-grid-col-9,ui-grid-col-1,ui-grid-col-1"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="max-height:20px !important;margin:0px !important; width:100% !important; height:100% !important;">
                                <img src="../assets/img/icone_satmob_cadastros.png" height="40" width="40" style="margin-left:10px" />
                                <h:outputText value="#{localemsgs.Relatorio}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px;" />
                            </p:panelGrid>
                        </f:facet>
                        <p:panel id="escolhaTipo" style="overflow:hidden !important; max-width: 100% !important;padding:0px 0px 0px 10px !important; background-color:#EEE; display:none;">
                            <p:commandLink update="escolhaTipo cadastrarNovoRel msgs"
                                           actionListener="#{mobEW.novoRelatorioTipos('PST')}" style="color:#022a48 !important;height:100% !important">
                                <div ref="botaoRelatorioPostos" class="col-md-6 col-sm-6 col-xs-12" style="height:100%; padding:0px 10px 0px 0px !important; cursor:pointer;">
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding:10px !important; height:100% !important; background-color:#FFF; border-radius:5px; border:1px solid #CCC;cursor:pointer !important;">
                                        <table style="height:100%; width:100%; animation:bounceIn 1.5s ease-in;cursor:pointer;">
                                            <tr>
                                                <td style="vertical-align:middle;text-align:center !important;cursor:pointer !important">
                                                    <label style="display:block;text-align:center !important; font-size:40pt;cursor:pointer !important"><i class="fa fa-briefcase"></i></label>
                                                    <label style="display:block;text-align:center !important; font-size:14pt;cursor:pointer !important">#{localemsgs.PostoServicos}</label>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </p:commandLink>
                            <p:commandLink update="escolhaTipo cadastrarNovoRel msgs"
                                           actionListener="#{mobEW.novoRelatorioTipos('C')}" style="color:#022a48 !important;height:100% !important">
                                <div ref="botaoRelatorioContatos" class="col-md-6 col-sm-6 col-xs-12" style="height:100%; padding:0px 10px 0px 0px !important;cursor:pointer;">
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding:10px !important; height:100% !important; background-color:#FFF; border-radius:5px; border:1px solid #CCC;cursor:pointer !important; ">
                                        <table style="height:100%; width:100%; animation:bounceIn 1.5s ease-in;cursor:pointer;">
                                            <tr>
                                                <td style="vertical-align:middle;text-align:center !important;cursor:pointer !important">
                                                    <label style="display:block;text-align:center !important; font-size:40pt;cursor:pointer !important"><i class="fa fa-users"></i></label>
                                                    <label style="display:block;text-align:center !important; font-size:14pt;cursor:pointer !important">#{localemsgs.Contatos}</label>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </p:commandLink>
                            <script type="text/javascript">
                                // <![CDATA[
                                $(document).ready(function () {
                                    $('div[id*="escolhaTipo"]').css('height', ($('body').height() - 130) + 'px').css('max-height', ($('body').height() - 130) + 'px');
                                });
                                $(window).resize(function () {
                                    $('div[id*="escolhaTipo"]').css('height', ($('body').height() - 130) + 'px').css('max-height', ($('body').height() - 130) + 'px');
                                });
                                // ]]>
                            </script>
                        </p:panel>

                        <p:panel styleClass="cadastrar" id="cadastrarNovoRel" style="overflow:auto !important; max-width: 100% !important;padding:0px 0px 5px 0px !important; background-color: #EEE; margin:0px !important; height:calc(100vh - 150px) !important">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height:100% !important; padding:0px 0px 0px 10px !important; overflow:hidden !important">
                                <div id="divPostos" class="col-md-12 col-sm-12 col-xs-12" style="padding:10px 10px 0px 0px">
                                    <label class="lblRotulo" for="postosCadastro">#{localemsgs.PostoServicos}</label>

                                    <p:selectOneMenu id="slmPostos" value="#{mobEW.cadastroRelatorio.secao}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%;">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"  />
                                        <f:selectItems value="#{mobEW.postosCadastro}" var="postosCadastro" itemValue="#{postosCadastro.secao}"
                                                       itemLabel="#{postosCadastro.posto}" noSelectionValue="" />

                                        <p:ajax listener="#{mobEW.carregarContatos()}" update="msgs slmContatos" />
                                    </p:selectOneMenu>
                                </div>
                                <div id="divContatos" class="col-md-12 col-sm-12 col-xs-12" style="padding:10px 10px 0px 0px">
                                    <label class="lblRotulo" for="slmContatos">#{localemsgs.Contatos}</label>

                                    <p:selectOneMenu id="slmContatos" value="#{mobEW.cadastroRelatorio.codigo}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%;">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"  />
                                        <f:selectItems value="#{mobEW.contatosCadastro}" var="contatosCadastro" itemValue="#{contatosCadastro.codigo}"
                                                       itemLabel="#{contatosCadastro.nome}" noSelectionValue=""/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:6px 10px 0px 0px">
                                    <label class="lblRotulo" for="txtHistorico">#{localemsgs.Historico}</label>

                                    <p:inputText id="txtHistorico" class="form-control" style="width: 100%" value="#{mobEW.cadastroRelatorio.historico}"></p:inputText>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:6px 10px 0px 0px">
                                    <label class="lblRotulo" for="txtDetalhes">#{localemsgs.Detalhes}</label>

                                    <p:inputTextarea id="txtDetalhes" class="form-control" rows="2" style="width: 100%"  value="#{mobEW.cadastroRelatorio.detalhes}"></p:inputTextarea>
                                </div>

                                <p:fileUpload id="uploadFotosRelatorio"
                                              fileUploadListener="#{mobEW.HandleFileUpload}" onstart="InicioUpload();"
                                              allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|PNG|JPE?G|GIF|BMP)$/"
                                              auto="true" multiple="false"
                                              invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                              dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                              update="msgs" skinSimple="true" previewWidth="10"
                                              style="width:10px; height:10px; display:none;"></p:fileUpload>

                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding:10px 10px 0px 0px; height: calc(100% - 330px); min-height:200px">
                                    <div class="FotoPendente" foto="1">
                                        <table style="width:100%; position:absolute !important;">
                                            <tr>
                                                <td style="text-align:center; vertical-align:middle !important; min-height:100% !important;height:100% !important;max-height:100% !important">
                                                    <label style="cursor:pointer;"><i class="fa fa-camera"></i></label>
                                                    <label style="cursor:pointer;">#{localemsgs.Foto} 1</label>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding:10px 10px 0px 0px; height: calc(100% - 330px); min-height:200px">
                                    <div class="FotoPendente" foto="2">
                                        <table style="width:100%; position:absolute !important;">
                                            <tr>
                                                <td style="text-align:center; vertical-align:middle;min-height:100% !important; ">
                                                    <label style="cursor:pointer;"><i class="fa fa-camera"></i></label>
                                                    <label style="cursor:pointer;">#{localemsgs.Foto} 2</label>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding:10px 10px 0px 0px; height: calc(100% - 330px); min-height:200px">
                                    <div class="FotoPendente" foto="3">
                                        <table style="min-height:100% !important; width:100%; position:absolute !important;">
                                            <tr>
                                                <td style="text-align:center; vertical-align:middle;min-height:100% !important; ">
                                                    <label style="cursor:pointer;"><i class="fa fa-camera"></i></label>
                                                    <label style="cursor:pointer;">#{localemsgs.Foto} 3</label>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding:10px 10px 0px 0px; height: calc(100% - 330px); min-height:200px">
                                    <div class="FotoPendente" foto="4">
                                        <table style="width:100%; position:absolute !important;">
                                            <tr>
                                                <td style="text-align:center; vertical-align:middle;min-height:100% !important; ">
                                                    <label style="cursor:pointer;"><i class="fa fa-camera"></i></label>
                                                    <label style="cursor:pointer;">#{localemsgs.Foto} 4</label>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:12px 10px 0px 0px; text-align:center">
                                    <!--<div class="col-md-6 col-sm-6 col-xs-6" style="padding:0px 0px 0px 0px !important; text-align:left;">
                                        <p:commandLink update="escolhaTipo cadastrarNovoRel msgs"
                                                       class="btn btn-warning" style="height:35px;font-size:12pt !important; padding-top:5px; width:100%; max-width:130px; text-align:center; margin-right:10px;">
                                            <label style="cursor:pointer"><i class="fa fa-arrow-circle-o-left"></i>&nbsp;#{localemsgs.Voltar}</label>
                                        </p:commandLink>
                                    </div>-->
                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px 0px 0px 4px !important; text-align:right;">
                                        <p:commandLink update="cadastrarNovoRel msgs main"
                                                       class="btn btn-primary btSalvar" style="height:35px;font-size:12pt !important; padding-top:5px; width:100%; max-width:130px; text-align:center" onclick="AtribuirValorObj()" action="#{mobEW.salvarRelatorio()}">
                                            <label style="cursor:pointer"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                                        </p:commandLink>
                                    </div>
                                </div>

                            </div>
                            <script type="text/javascript">
                                // <![CDATA[
                                $(document).ready(function () {
                                    AjustarTamanhoTable();
                                })
                                        ;
                                function AjustarTamanhoTable() {
                                    $('.FotoPendente').each(function () {
                                        $(this).find('table').height($(this).height() + 'px');
                                        $(this).find('table').css('min-height', ($(this).height() + 'px'));
                                        $(this).find('table').css('height', ($(this).height() + 'px'));
                                        $(this).find('table').css('max-height', ($(this).height() + 'px'));
                                    });
                                }

                                function InicioUpload() {
                                    $this.css('background-image', '').find('table').css('display', '');
                                    $this.find('i').attr('class', 'fa fa-refresh fa-spin fa-fw');
                                }

                                $(window).resize(function () {

                                });
                                // ]]>
                            </script>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisar -->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_relatorios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Filtrar}" style="color:#022a48;" />
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.Filial}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{mobEW.logsSatMobEWSelecionado.codfil}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" listener="#{mobEW.atualizarListaPostos}"
                                                update="formPesquisar:posto"/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="posto" value="#{localemsgs.Posto}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="posto" value="#{mobEW.logsSatMobEWSelecionado.secao}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{mobEW.postos}" var="postos" itemValue="#{postos.secao}"
                                                       itemLabel="#{postos.local}" noSelectionValue=""/>
                                        <p:watermark for="posto" value="#{localemsgs.Posto}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="tipo" value="#{localemsgs.Relatorio}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="tipo" value="#{mobEW.logsSatMobEWSelecionado.tipo}"
                                                     styleClass="filial" panelStyleClass="hideDisabled"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItem itemLabel="#{localemsgs.ClockInClockOut}" itemValue="1"/>
                                        <f:selectItem itemLabel="#{localemsgs.Relatorio}" itemValue="2"/>
                                        <f:selectItem itemLabel="#{localemsgs.RelatorioSupervisor}" itemValue="3" itemDisabled="#{!mobEW.supervisor}"/>
                                        <f:selectItem itemLabel="#{localemsgs.Rondas}" itemValue="4"/>
                                        <f:selectItem itemLabel="#{localemsgs.CheckInCheckOut}" itemValue="5" itemDisabled="#{!mobEW.supervisor}"/>
                                        <f:selectItem itemLabel="#{localemsgs.RelatorioPrestador}" itemValue="10" itemDisabled="#{!mobEW.supervisor}"/>
                                        <f:selectItem itemLabel="#{localemsgs.RotaPrestador}" itemValue="9" itemDisabled="#{!mobEW.supervisor}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="cadastro" action="#{mobEW.filtrar}" update=":main:tabela :msgs cabecalho"
                                               oncomplete="PF('dlgPesquisar').hide()"
                                               title="#{localemsgs.Filtrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>


            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{mobEW.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela main:pnlAcordionTopo corporativo cabecalho" listener="#{mobEW.mostrarTodasFiliais}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                    <td>
                                        <h:form>
                                            <p:commandLink title="#{localemsgs.Manuais}" rendered="#{login.empresa.bancoDados.contains('PISCINA')}"
                                                           oncomplete="PF('dlgManuais').show()">
                                                <p:graphicImage url="../assets/img/icone_redondo_ajuda.png" height="25" />
                                            </p:commandLink>
                                        </h:form>
                                    </td>
                                    <td>
                                        <h:form>
                                            <p:commandLink title="#{localemsgs.Mensagens}" rendered="#{login.empresa.bancoDados.contains('PISCINA')}"
                                                           actionListener="#{mobEW.abrirMensagens(false)}">
                                                <p:graphicImage url="../assets/img/icone_redondo_email.png" height="25" />
                                            </p:commandLink>
                                        </h:form>
                                    </td>                                    
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <h:form>
                <p:dialog  widgetVar="dlgManuais" positionType="absolute" responsive="true"
                           draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                           showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                           style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_relatorios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Manuais}" style="color:#022a48;" />
                    </f:facet>
                    <p:panel id="pesquisar" style="background: transparent">

                        <div class="ui-grid-row">
                            <p:commandLink target="_blank" ajax="false" style="color:black;" action="#{mobEW.manual1}">
                                <p:graphicImage url="../assets/img/icone_download_manual.png" height="30" width="30"/>
                                <span>Manual de Operação de Piscineiro</span>
                            </p:commandLink>
                        </div>

                        <div class="ui-grid-row">
                            <p:commandLink target="_blank" ajax="false" style="color:black;" action="#{mobEW.manual2}">
                                <p:graphicImage url="../assets/img/icone_download_manual.png" height="30" width="30"/>
                                <span>Manual de Operação de Supervisor</span>
                            </p:commandLink>
                        </div>

                        <div class="ui-grid-row">
                            <p:commandLink target="_blank" ajax="false" style="color:black;" action="#{mobEW.manual3}">
                                <p:graphicImage url="../assets/img/icone_download_manual.png" height="30" width="30"/>
                                <span>Política Disciplinar Piscina Fácil</span>
                            </p:commandLink>
                        </div>

                        <div class="form-inline">
                            <p:commandLink id="cadastro" oncomplete="PF('dlgManuais').hide()"
                                           title="#{localemsgs.Fechar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>

                    </p:panel>
                </p:dialog>             
            </h:form>


            <h:form id="formMensagens" class="form-inline">               
                <p:dialog  widgetVar="dlgMensagens" positionType="absolute" responsive="true"
                           draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                           showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                           style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_redondo_email.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Mensagens}" style="color:#022a48;" />
                    </f:facet>
                    <p:panel id="mensagens" style="background: transparent">


                        <div class="col-md-9 col-sm-9 col-xs-9" style="padding: 6px 3px 6px 6px !important">
                            <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Operador}</label>
                            <p:selectOneMenu id="operador" value="#{mobEW.pessoaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                             filter="true" filterMatchMode="contains"
                                             style="width: 100%; outline: none !important" >
                                <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" itemValue=""></f:selectItem>
                                <f:selectItems value="#{mobEW.pessoas}" var="operador" itemValue="#{operador}"
                                               itemLabel="#{operador.nome}"/>                                
                            </p:selectOneMenu>
                        </div>

                        <div class="col-md-9 col-sm-9 col-xs-9" style="padding: 6px 3px 6px 6px !important">
                            <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Mensagem}</label>
                            <p:inputText id="mensagem" value="#{mobEW.mensagem}" style="width: 100%"
                                         label="#{localemsgs.Mensagem}" maxlength="80">
                                <p:watermark for="mensagem" value="#{localmsgs.mensagem}"/>
                            </p:inputText>
                        </div>


                        <div class="col-md-9 col-sm-9 col-xs-9" style="padding: 6px 3px 6px 6px !important">
                            <p:commandLink id="btnMensagem" actionListener="#{mobEW.envioMensagem()}"
                                           title="#{localemsgs.Enviar}" update="msgs">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>

                    </p:panel>
                </p:dialog>                
            </h:form>   


            <script>
                // <![CDATA[    

                function posSalvar(inCodigo, inSeq, inResp) {
                    let refClass = 'obj_' + inCodigo + '_' + inSeq;

                    setInterval(function () {
                        $('[class="' + refClass + '"]').text(inResp);
                    }, 600);
                }

                $(document)
                        .on('click', '[ref="btEditarInsp"]', function () {
                            let HTML = '';
                            let inCodigo = $(this).attr('ref-codigo');
                            let inResposta = $(this).attr('ref-resp');
                            let inSequencia = $(this).attr('ref-sequencia');

                            HTML += '<div>';
                            HTML += '   <input type="text" id="txtResposta" value="' + inResposta + '" class="form-control" />';
                            HTML += '</div>';

                            $dlAzulOkgMessage = $.alert({
                                icon: 'fa fa-question fa-lg',
                                type: 'blue',
                                title: '<font color="#3498DB">#{localemsgs.Resposta}</font>',
                                content: HTML,
                                buttons: {
                                    ok: {
                                        text: 'Ok',
                                        btnClass: 'btn-blue',
                                        action: function () {
                                            rcResposta([{name: 'inCodigo', value: parseInt(inCodigo)}, {name: 'inSequencia', value: parseInt(inSequencia)}, {name: 'inResposta', value: $('#txtResposta').val()}]);
                                        }
                                    },
                                    cancel: {
                                        text: '#{localemsgs.Cancelar}'
                                    }
                                },
                                onContentReady: function () {
                                    $('#txtResposta').focus().select();
                                }
                            });
                        })
                        .on('click', '#btImprimirEfetivos', function () {
                            if (!$('#tblEfetivos tbody tr:first-child td').attr('colspan')) {
                                $objExport = $('[id*="pnlGrdEfetivos"]:first-child').clone();
                                $objExport.find('td').css('font-size', '8pt').css('text-align', 'center').css('border-bottom', 'thin solid #CCC');
                                $objExport.find('thead tr td').css('background-color', '#DDD').css('color', '#000').css('font-weight', 'bold').css('border-top', 'thin solid #CCC').css('border-left', 'thin solid #CCC');
                                $objExport.find('table').css('border-spacing', '0px').find('tbody tr td').css('border-left', 'none').css('border-right', 'none');

                                Imprimir(CriarCabecalho('#{login.getLogo(login.empresa.bancoDados)}',
                                        '#{mobEW.filiais.descricao}',
                                        '#{mobEW.filiais.endereco} - #{mobEW.filiais.bairro}, #{mobEW.filiais.cidade}/#{mobEW.filiais.UF} - #{mobEW.filiais.CEP} ',
                                        'CNPJ: #{mobEW.filiais.CNPJ}',
                                        '#{localemsgs.RELATÓRIO} - #{localemsgs.Efetividade} - #{localemsgs.Periodo}: ' + $('[id*="efetivosPeriodo_input"]').val()),
                                        $objExport);

                            } else {
                                $.MsgBoxLaranjaOk('#{localemsgs.Aviso}', '#{localemsgs.NaoHaDados}');
                            }
                        })
                        .on('click', '#btImprimirEfetivosPontos', function () {
                            if (!$('#tblEfetivosPtos tbody tr:first-child td').attr('colspan')) {
                                $objExport = $('[id*="pnlGrdPtosEfetivos"]:first-child').clone();
                                $objExport.find('td').css('font-size', '8pt').css('text-align', 'center').css('border-bottom', 'thin solid #CCC');
                                $objExport.find('thead tr td').css('background-color', '#DDD').css('color', '#000').css('font-weight', 'bold').css('border-top', 'thin solid #CCC').css('border-left', 'thin solid #CCC');
                                $objExport.find('table').css('border-spacing', '0px').find('tbody tr td').css('border-left', 'none').css('border-right', 'none');

                                Imprimir(CriarCabecalho('#{login.getLogo(login.empresa.bancoDados)}',
                                        '#{mobEW.filiais.descricao}',
                                        '#{mobEW.filiais.endereco} - #{mobEW.filiais.bairro}, #{mobEW.filiais.cidade}/#{mobEW.filiais.UF} - #{mobEW.filiais.CEP} ',
                                        'CNPJ: #{mobEW.filiais.CNPJ}',
                                        '#{localemsgs.RELATÓRIO} - #{localemsgs.PontoEletronico} - #{localemsgs.Periodo}: ' + $('[id*="efetivosPeriodo_input"]').val()),
                                        $objExport);

                            } else {
                                $.MsgBoxLaranjaOk('#{localemsgs.Aviso}', '#{localemsgs.NaoHaDados}');
                            }
                        });
                // ]]>
            </script>
            <script>
                // <![CDATA[
                var ArrayBoletimTrabalho = new Array();

                function CarregarListaTrabalho(inQtdeVisitas) {
                    setTimeout(function () {
                        let UltimoGrupo = '', HTML = '', Pergunta = '';

                        for (I = 0; I < ArrayBoletimTrabalho.length; I++) {
                            Pergunta = ArrayBoletimTrabalho[I].Pergunta.toString().indexOf('-') > -1 ? ArrayBoletimTrabalho[I].Pergunta.toString().split('-')[1] : ArrayBoletimTrabalho[I].Pergunta.toString();

                            if (UltimoGrupo !== ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim())
                                HTML += '<label style="font-size: 14pt; font-weight: 500; width: 100%; color: steelblue; padding: 8px 0px 5px 10px; background-color: #EEE; text-align: left; margin: 0px 0px 5px 0px !important; text-shadow: 1px 1px #FFF; border: thin solid #DDD"><i class="fa fa-check-square-o" aria-hidden="true" style="font-size: 12pt !important;"></i>&nbsp;&nbsp;' + ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim() + '</label>';

                            HTML += '<label style="font-size: 10pt; color: #666; width: 100%; text-align: left; padding: 0px 0px 5px 0px; border-bottom: 1px dashed #DDD; margin: 0px !important;"><span style="float: left; width: 55%; text-align: right">' + Pergunta + ':</span><span style="float: right; font-weight: 600;width: 45%; text-align: left; padding-left: 8px; color: #303030 !important">' + ArrayBoletimTrabalho[I].Resposta + '</span></label>';

                            UltimoGrupo = ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim();
                        }

                        $('#divItensHistorico').html(HTML);
                        $('#lblQtdeVisitas').html(inQtdeVisitas);
                    }, 1000);
                }

                var options = {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                };
                var LatitudeLogon;
                var LongitudeLogon;
                $(document).ready(function () {
                    if (navigator.geolocation)
                        navigator.geolocation.getCurrentPosition(SucessoCapturaPosicao, ErroCapturaPosicao, options);
                })
                        .on('click', '.FotoPendente', function () {
                            if (navigator.geolocation)
                                navigator.geolocation.getCurrentPosition(SucessoCapturaPosicao, ErroCapturaPosicao, options);
                            $this = $(this);
                            $('[id*="uploadFotosRelatorio_input"]').click();
                        })
                        .on('click', '#btExpandirMapa', function () {
                            $('#divMapaFull').html('<label id="lblTeste"></label>');
                            $('div[id*="gmap"]').insertAfter($('#lblTeste'));
                            $('#divMapaFull').append('<label id="btRecolherMapa" style="position:absolute; top:10px; right:10px; width:60px; height:60px; border-radius:50%; z-index:99999999 !important; cursor:pointer; background-color:#022a48;"><img src="../assets/img/icone_fecharmapa.png" style="max-width:55%; max-height:55%; margin-top:13px; margin-left:14px;" /></label>');
                            $('#divMapaFull').fadeIn();
                            setTimeout(function () {
                                $('div[id*="gmap"]').css({
                                    'position': 'absolute',
                                    'top': '0px',
                                    'height': '100%',
                                    'margin-top': '0px'
                                });
                            }, 500);
                        })
                        .on('click', '#btRecolherMapa', function () {
                            $('div[id*="gmap"]').insertAfter($('#DadosHTML'));
                            $('#divMapaFull').html('');
                            $('div[id*="gmap"]').css({
                                'position': 'relative',
                                'top': '0px',
                                'height': 'calc(100vh - 400px)',
                                'margin-top': '6px'
                            });
                            $('#divMapaFull').fadeOut();
                        })
                        ;
                function SucessoCapturaPosicao(position) {
                    LatitudeLogon = position.coords.latitude;
                    LongitudeLogon = position.coords.longitude;
                }

                function AtribuirValorObj() {
                    $('[id*="txtLatitude"]').val(LatitudeLogon);
                    $('[id*="txtLongitude"]').val(LongitudeLogon);
                }

                function ErroCapturaPosicao(error) {

                }

                async function ConsultarEndereco() {
                    $('.SemPosto').parents('div[ref="posicao"]').each(async function () {
                        await ConsultarEnderecoGoogle($(this));
                    });
                }

                function ConsultarEnderecoGoogle($obj) {
                    return new Promise(resolve => {
                        if ($obj.attr('posicao') !== '|') {
                            $.ajax({
                                url: 'https://maps.googleapis.com/maps/api/geocode/json?key=#{login.googleApiMob}&latlng=' + $obj.attr('posicao').split('|')[0] + ',' + $obj.attr('posicao').split('|')[1],
                                method: 'get'
                            })
                                    .done((response) => {
                                        $obj.attr('class', 'ComPosto').text(response.results[0].formatted_address);
                                        resolve();
                                    })
                                    .fail(() => {
                                        resolve();
                                    });
                        } else
                            resolve();
                    });
                }

                /*function ConsultarEndereco() {
                 $('.SemPosto').parents('div[ref="posicao"]').each(function () {
                 console.log('https://maps.googleapis.com/maps/api/geocode/json?key=#{login.googleApiMob}&latlng=' + $(this).attr('posicao').split('|')[0] + ',' + $(this).attr('posicao').split('|')[1]);
                 $.ajax({
                 url: 'https://maps.googleapis.com/maps/api/geocode/json?key=#{login.googleApiMob}&latlng=' + $(this).attr('posicao').split('|')[0] + ',' + $(this).attr('posicao').split('|')[1],
                 method: 'get'
                 })
                 .done(function (response) {
                 console.log(response.results[0].formatted_address);
                 $('.SemPosto:eq(0)').attr('class', 'ComPosto').text(response.results[0].formatted_address);
                 });
                 });
                 }*/
                // ]]>
            </script>
        </h:body>
    </f:view>
</html>


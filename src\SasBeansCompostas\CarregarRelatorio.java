/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Cargos;
import SasBeans.F5_Dep;
import SasBeans.FPRescisoes;
import SasBeans.Filiais;
import SasBeans.Fornec;
import SasBeans.Funcion;
import SasBeans.Municipios;
import SasBeans.PropCmlMod;
import SasBeans.PropCmlModItens;
import SasBeans.RHEscala;
import SasBeans.RHHorario;
import SasBeans.Sindicatos;

/**
 *
 * <AUTHOR>
 */
public class CarregarRelatorio {

    private PropCmlModItens propCmlModItens;
    private PropCmlMod propCmlMod;
    private Funcion funcion;
    private Cargos cargos;
    private RHHorario rhhorario;
    private RHEscala rhEscala;
    private Sindicatos sindicatos;
    private Fornec fornec;
    private FPRescisoes fpRescisoes;
    private Filiais filiais;
    private F5_Dep f5_Dep;
    private Municipios municipios;

    public PropCmlModItens getPropCmlModItens() {
        return propCmlModItens;
    }

    public void setPropCmlModItens(PropCmlModItens propCmlModItens) {
        this.propCmlModItens = propCmlModItens;
    }

    public PropCmlMod getPropCmlMod() {
        return propCmlMod;
    }

    public void setPropCmlMod(PropCmlMod propCmlMod) {
        this.propCmlMod = propCmlMod;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public Cargos getCargos() {
        return cargos;
    }

    public void setCargos(Cargos cargos) {
        this.cargos = cargos;
    }

    public RHHorario getRhhorario() {
        return rhhorario;
    }

    public void setRhhorario(RHHorario rhhorario) {
        this.rhhorario = rhhorario;
    }

    public RHEscala getRhEscala() {
        return rhEscala;
    }

    public void setRhEscala(RHEscala rhEscala) {
        this.rhEscala = rhEscala;
    }

    public Sindicatos getSindicatos() {
        return sindicatos;
    }

    public void setSindicatos(Sindicatos sindicatos) {
        this.sindicatos = sindicatos;
    }

    public Fornec getFornec() {
        return fornec;
    }

    public void setFornec(Fornec fornec) {
        this.fornec = fornec;
    }

    public FPRescisoes getFpRescisoes() {
        return fpRescisoes;
    }

    public void setFpRescisoes(FPRescisoes fpRescisoes) {
        this.fpRescisoes = fpRescisoes;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public F5_Dep getF5_Dep() {
        return f5_Dep;
    }

    public void setF5_Dep(F5_Dep f5_Dep) {
        this.f5_Dep = f5_Dep;
    }

    public Municipios getMunicipios() {
        return municipios;
    }

    public void setMunicipios(Municipios municipios) {
        this.municipios = municipios;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class iblCTeOS_infGTV {
    public String nGTV;
    public String serie;
    public String dEmi;
    public String qCarga;
    public String placa;
    public String UF;
    public List<iblCTeOS_infGTVnotaFiscal> notaFiscal;

    public String getnGTV() {
        return nGTV;
    }

    public void setnGTV(String nGTV) {
        this.nGTV = nGTV;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getdEmi() {
        return dEmi;
    }

    public void setdEmi(String dEmi) {
        this.dEmi = dEmi;
    }

    public String getqCarga() {
        return qCarga;
    }

    public void setqCarga(String qCarga) {
        this.qCarga = qCarga;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public List<iblCTeOS_infGTVnotaFiscal> getNotaFiscal() {
        return notaFiscal;
    }

    public void setNotaFiscal(List<iblCTeOS_infGTVnotaFiscal> notaFiscal) {
        this.notaFiscal = notaFiscal;
    }
    
    
}

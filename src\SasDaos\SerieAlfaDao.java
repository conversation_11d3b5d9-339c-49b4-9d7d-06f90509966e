/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class SerieAlfaDao {

    /**
     * Converte uma série numérica em série alfanumérica
     *
     * @param serienum - série numérica
     * @param persistencia
     * @return - série alfanumérica
     * @throws java.lang.Exception
     */
    public String SerieNum2Alfa(BigDecimal serienum, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select serie from seriealfa where serienum = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(serienum);
            consult.select();
            while (consult.Proximo()) {
                return consult.getString("serie");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao converter série numérica em alfanumérica - " + e.getMessage());
        }
        return null;
    }

    /**
     * Converte uma série alfanumérica em numérica
     *
     * @param serie - série alfanumérica
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal SerieAlfa2Num(String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select serienum from seriealfa where serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(serie);
            consult.select();
            while (consult.Proximo()) {
                return consult.getBigDecimal("serienum");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao converter série alfanumérica em numérica - " + e.getMessage());
        }
        return BigDecimal.ZERO;
    }
}

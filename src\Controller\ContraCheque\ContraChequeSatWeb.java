/*
 */
package Controller.ContraCheque;

import Dados.Persistencia;
import SasBeans.FPMensal;
import SasBeans.FPPeriodos;
import SasBeansCompostas.ContraCheque;
import SasDaos.ContraChequeCorpvsDao;
import SasDaos.ContraChequeDao;
import SasDaos.FPMensalDao;
import SasDaos.FPPeriodosDao;
import SasDaos.LogPortalDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ContraChequeSatWeb {

    protected boolean corpvs;
    protected Persistencia corpvsPersistencia;
    protected ContraChequeCorpvsDao corpvsdao;
    protected String mat;

    public ContraChequeSatWeb() {
    }

    public ContraChequeSatWeb(Persistencia persistencia, String codFil, String matricula) {
        corpvsPersistencia = persistencia;
        corpvsdao = new ContraChequeCorpvsDao(codFil);
        mat = matricula;
        corpvs = true;
    }

    /**
     * Retorna os seis períodos mais recentes com contracheques disponíveis
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<FPPeriodos> getPeriodos(Persistencia persistencia) throws Exception {
        try {
            if (corpvs) {
                return corpvsdao.getFPs(mat, corpvsPersistencia);
            } else {
                FPPeriodosDao fpperiodosdao = new FPPeriodosDao();
                return fpperiodosdao.getFPs(persistencia);
            }
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha buscar periodos> - " + e.getMessage());
        }
    }

    /**
     * Retorna os seis períodos mais recentes com contracheques disponíveis
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<FPPeriodos> getPeriodo(String codMovFP, Persistencia persistencia) throws Exception {
        try {
            if (corpvs) {
                return corpvsdao.getFP(mat, codMovFP, corpvsPersistencia);
            } else {
                FPPeriodosDao fpperiodosdao = new FPPeriodosDao();
                return fpperiodosdao.getFP(codMovFP, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha buscar periodos> - " + e.getMessage());
        }
    }

    /**
     * Retorna os contracheques disponíveis para determinado período
     *
     * @param codMovFP - periodo yyMM
     * @param dataInicio - periodo formatado MM/yyyy
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<FPMensal> getContrachqeuesPeriodo(String codMovFP, String dataInicio, String matricula, Persistencia persistencia) throws Exception {
        try {
            if (corpvs) {
                return corpvsdao.listaFPMensal(codMovFP, dataInicio, matricula, corpvsPersistencia, persistencia);
            } else {
                FPMensalDao fpmensaldao = new FPMensalDao();
                return fpmensaldao.listaFPMensal(codMovFP, dataInicio, matricula, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha buscar contracheques do periodo> - " + e.getMessage());
        }
    }

    /**
     * Busca os dados para montar o cabeçalho da folha de pagamento
     *
     * @param codMovFP
     * @param matricula
     * @param tipoFP
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<ContraCheque> getCabecalhoCC(String codMovFP, String matricula, String tipoFP, Persistencia persistencia) throws Exception {
        try {
            codMovFP = codMovFP.replace(".0", "");
            
            if (corpvs) {
                return corpvsdao.getCCcabecalho(codMovFP, matricula, tipoFP, persistencia);
            } else {
                ContraChequeDao contrachequedao = new ContraChequeDao();
                return contrachequedao.getCCcabecalho(codMovFP, matricula, tipoFP, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha buscar cabecalho> - " + e.getMessage());
        }
    }

    /**
     * Busca os dados para montar o corpo da folha de pagamento
     *
     * @param codMovFP
     * @param matricula
     * @param tipoFP
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<ContraCheque> getComposicaoCC(String codMovFP, String matricula, String tipoFP, Persistencia persistencia) throws Exception {
        try {
            codMovFP = codMovFP.replace(".0", "");
            
            if (corpvs) {
                return corpvsdao.getCCComposicao(codMovFP, matricula, tipoFP, corpvsPersistencia);
            } else {
                ContraChequeDao contrachequedao = new ContraChequeDao();
                return contrachequedao.getCCComposicao(codMovFP, matricula, tipoFP, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha buscar corpo> - " + e.getMessage());
        }
    }

    /**
     * Busca os dados para montar cálculo base da folha de pagamento
     *
     * @param codMovFP
     * @param matricula
     * @param tipoFP
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<ContraCheque> getBaseCC(String codMovFP, String matricula, String tipoFP, Persistencia persistencia) throws Exception {
        try {
            if (corpvs) {
                return corpvsdao.getBaseCalcs(codMovFP, matricula, tipoFP, corpvsPersistencia);
            } else {
                ContraChequeDao contrachequedao = new ContraChequeDao();
                return contrachequedao.getBaseCalcs(codMovFP, matricula, tipoFP, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha buscar cálculos base> - " + e.getMessage());
        }
    }

    /**
     * Insere log da geração de contracheque no banco
     *
     * @param matricula
     * @param codFil
     * @param composicao
     * @param tipoFP
     * @param persistencia
     * @throws Exception
     */
    public void geraLogContraCheque(String matricula, String codFil,
            String composicao, String tipoFP, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            logportaldao.insereLog(matricula, codFil, "Contracheque gerado " + composicao + "- tipo " + tipoFP, persistencia);
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha salvar log> - " + e.getMessage());
        }
    }
    
    
     /**
     * Insere log da geração de contracheque no banco
     *
     * @param matricula
     * @param codFil
     * @param composicao
     * @param tipoFP
     * @param persistencia
     * @throws Exception
     */
    public void geraLogContraCheque(String matricula, String codFil,
            String composicao, String tipoFP, String caminhoAssinatura, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            logportaldao.insereLog(matricula, codFil, "Contracheque gerado " + composicao + "- tipo " + tipoFP, caminhoAssinatura, persistencia);
        } catch (Exception e) {
            throw new Exception("contracheques.falhageral<falha salvar log> - " + e.getMessage());
        }
    }
}

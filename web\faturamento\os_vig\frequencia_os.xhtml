<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <h:form id="formFrequencia" class="form-inline">
        <p:dialog widgetVar="dlgFrequencia" positionType="absolute" responsive="true" draggable="false" modal="true"
                  closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="false"
                  style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; 
                  box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;
                  border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; 
                  padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
            <f:facet name="header">
                <img src="../assets/img/icone_tesouraria_entradas.png" height="40" width="40"/>
                <p:spacer width="5px"/>
                <h:outputText value="#{localemsgs.CadastrarFrequencia}" style="color:#022a48" />
            </f:facet>

            <p:panel id="edicao" style="background-color: transparent;" styleClass="cadastrar">
                <div class="content-fluid">
                    <div class="row row-margin">
                        <div class="col-md-2">
                            <p:outputLabel for="codfil" value="#{localemsgs.CodFil}: "/>
                        </div>
                        <div class="col-md-2">
                            <h:outputText id="codfil" value="#{os_vig.frequenciaEdicao.codFil}" converter="conversorCodFil"/>
                        </div>

                        <div class="col-md-2">
                            <p:outputLabel for="os" value="#{localemsgs.OS}:"/>
                        </div>
                        <div class="col-md-2">
                            <h:outputText id="os" value="#{os_vig.frequenciaEdicao.OS}" converter="conversor0"/>
                        </div>
                    </div>

                    <div class="row row-margin">
                        <div class="col-md-2">
                            <p:outputLabel for="diaSem" value="#{localemsgs.DiaSem}: " indicateRequired="false"/>
                        </div>
                        <div class="col-md-3">
                            <p:selectOneMenu id="diaSem" value="#{os_vig.frequenciaEdicao.diaSem}" disabled="#{os_vig.editandoFrequencia}"
                                             converter="omnifaces.SelectItemsConverter" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                             styleClass="filial" style="width: 100%" filter="true" filterMatchMode="contains">
                                <f:selectItem itemLabel="1 - #{localemsgs.Domingo}" itemValue="1"/>
                                <f:selectItem itemLabel="2 - #{localemsgs.Segunda}" itemValue="2"/>
                                <f:selectItem itemLabel="3 - #{localemsgs.Terça}" itemValue="3"/>
                                <f:selectItem itemLabel="4 - #{localemsgs.Quarta}" itemValue="4"/>
                                <f:selectItem itemLabel="5 - #{localemsgs.Quinta}" itemValue="5"/>
                                <f:selectItem itemLabel="6 - #{localemsgs.Sexta}" itemValue="6"/>
                                <f:selectItem itemLabel="7 - #{localemsgs.Sabado}" itemValue="7"/>
                                <f:selectItem itemLabel="8 - #{localemsgs.Domingo}" itemValue="8"/>
                                <f:selectItem itemLabel="9 - #{localemsgs.DiasUteis}" itemValue="9"/>
                                <f:selectItem itemLabel="0 - #{localemsgs.TodosDias}" itemValue="0"/>
                            </p:selectOneMenu>
                        </div>

                        <div class="col-md-1">
                            <p:outputLabel for="tipo"
                                           value="#{localemsgs.Tipo}: "
                                           indicateRequired="false"/>
                        </div>
                        <div class="col-md-3">
                            <p:selectOneMenu
                                id="tipo"
                                value="#{os_vig.frequenciaEdicao.tipo}"
                                disabled="#{os_vig.editandoFrequencia}"
                                converter="omnifaces.SelectItemsConverter"
                                required="true"
                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                styleClass="filial"
                                style="width: 100%"
                                filter="true"
                                filterMatchMode="contains">
                                <f:selectItem itemLabel="A - #{localemsgs.AssistTecnica}"
                                              itemValue="A"/>
                                <f:selectItem itemLabel="S - #{localemsgs.Suprimento}"
                                              itemValue="S"/>
                                <f:selectItem itemLabel="R - #{localemsgs.Recolhimento}"
                                              itemValue="R"/>
                            </p:selectOneMenu>
                        </div>

                        <div class="col-md-1">
                            <p:outputLabel for="rotaC"
                                           value="#{localemsgs.RotaC}: "/>
                        </div>
                        <div class="col-md-1">
                            <p:inputText id="rotaC"
                                         style="max-width: 50px;"
                                         value="#{os_vig.frequenciaEdicao.rotaC}"/>
                        </div>
                    </div>

                    <div class="row row-margin row-no-gutters">
                        <div class="col-md-2">
                            <p:outputLabel for="hora1"
                                           value="#{localemsgs.Intervalo}: "
                                           indicateRequired="false"/>
                        </div>
                        <div class="col-md-3">
                            <p:inputMask id="hora1"
                                         value="#{os_vig.frequenciaEdicao.hora1}"
                                         style="width: 50%"
                                         type="text"
                                         converter="conversorHora"
                                         validator="ValidadorHora"
                                         mask="#{mascaras.mascaraHora}">
                            </p:inputMask>
                            <p:spacer width="5" />
                            <p:inputMask id="hora2"
                                         value="#{os_vig.frequenciaEdicao.hora2}"
                                         style="width: 50%"
                                         type="text"
                                         converter="conversorHora"
                                         validator="ValidadorHora"
                                         mask="#{mascaras.mascaraHora}">
                            </p:inputMask>
                        </div>
                    </div>
                    <div class="row row-margin">
                        <div class="col-md-6">
                            <fieldset>
                                <legend style="font-size: 16px;">
                                    #{localemsgs.DiasMes}
                                    <p:selectBooleanCheckbox
                                        itemLabel="#{localemsgs.MarcarDesmarcar}"
                                        value="#{os_vig.todosDiasMes}"
                                        style="padding-right: 15px;"
                                        >
                                        <p:ajax
                                            event="change"
                                            listener="#{os_vig.onChangeDiasMes()}"
                                            update="@form:edicao"
                                            />
                                    </p:selectBooleanCheckbox>
                                </legend>
                                <p:selectManyCheckbox
                                    id="diasMes"
                                    value="#{os_vig.frequenciaDiasSelecionados}"
                                    layout="grid"
                                    columns="5">
                                    <f:selectItems
                                        value="#{os_vig.frequenciaDias}"
                                        var="dia"
                                        itemLabel="#{dia}"
                                        itemValue="#{dia}"/>
                                </p:selectManyCheckbox>
                            </fieldset>
                        </div>

                        <div class="col-md-6">
                            <fieldset>
                                <legend style="font-size: 16px;">
                                    #{localemsgs.DiasUteis}
                                    <p:selectBooleanCheckbox
                                        itemLabel="#{localemsgs.MarcarDesmarcar}"
                                        value="#{os_vig.todosDiasUteis}"
                                        style="padding-right: 15px;"
                                        >
                                        <p:ajax
                                            event="change"
                                            listener="#{os_vig.onChangeDiasDU()}"
                                            update="@form:edicao"
                                            />
                                    </p:selectBooleanCheckbox>
                                </legend>
                                <p:selectManyCheckbox
                                    id="diasUteis"
                                    value="#{os_vig.frequenciaDUSelecionados}"
                                    layout="grid"
                                    columns="5">
                                    <f:selectItems
                                        value="#{os_vig.frequenciaDU}"
                                        var="dia"
                                        itemLabel="#{dia}"
                                        itemValue="#{dia}"/>
                                </p:selectManyCheckbox>
                            </fieldset>
                        </div>
                    </div>

                    <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                 layout="grid" styleClass="ui-panelgrid-blank"
                                 style="margin-top:6px; padding-left:0px !important">
                        <p:commandLink
                            title="#{localemsgs.Salve}"
                            id="salvar"
                            action="#{os_vig.gravarEntrada()}"
                            update="msgs formCadastrar:tabs:tabelaFrequencia:tabelaFrequenciaServicos"
                            style="width:100%">
                            <label class="btn btn-lg btn-success" style="width:100% !important;margin-left: 0px;">
                                <i class="fa fa-save"/>
                                <h:outputText value="&#160;"/>
                                #{localemsgs.Salve}
                            </label>
                        </p:commandLink>
                    </p:panelGrid>
                </div>
            </p:panel>
        </p:dialog>
    </h:form>

</ui:composition>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rt_Hist {

    private BigDecimal Sequencia;
    private BigDecimal Parada;
    private BigDecimal Ordem;
    private BigDecimal CodFil;
    private String Historico;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public BigDecimal getParada() {
        return Parada;
    }

    public void setParada(BigDecimal Parada) {
        this.Parada = Parada;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public String getHistorico() {
        return Historico;
    }

    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CxForte {

    private String CodFil;
    private String CodCli;
    private BigDecimal Custodia;
    private BigDecimal RemPendQtde;
    private BigDecimal RemPendValor;
    private BigDecimal RemPrepQtde;
    private BigDecimal RemPrepValor;
    private BigDecimal Saldo;
    private BigDecimal SaldoReal;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    private String email;
    private String NRed;
    private String filial;
    private String ultMovDataEntrada;
    private String ultMovHoraEntrada;
    private String ultMovValorEntrada;
    private String ultMovOperadorEntrada;
    private String ultMovDataSaida;
    private String ultMovHoraSaida;
    private String ultMovValorSaida;
    private String ultMovOperadorSaida;

    private String DtFecha;

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public BigDecimal getCustodia() {
        return Custodia;
    }

    public void setCustodia(String Custodia) {
        try {
            this.Custodia = new BigDecimal(Custodia);
        } catch (Exception e) {
            this.Custodia = new BigDecimal("0");
        }
    }

    public BigDecimal getRemPendQtde() {
        return RemPendQtde;
    }

    public void setRemPendQtde(String RemPendQtde) {
        try {
            this.RemPendQtde = new BigDecimal(RemPendQtde);
        } catch (Exception e) {
            this.RemPendQtde = new BigDecimal("0");
        }
    }

    public BigDecimal getRemPendValor() {
        return RemPendValor;
    }

    public void setRemPendValor(String RemPendValor) {
        try {
            this.RemPendValor = new BigDecimal(RemPendValor);
        } catch (Exception e) {
            this.RemPendValor = new BigDecimal("0");
        }
    }

    public BigDecimal getRemPrepQtde() {
        return RemPrepQtde;
    }

    public void setRemPrepQtde(String RemPrepQtde) {
        try {
            this.RemPrepQtde = new BigDecimal(RemPrepQtde);
        } catch (Exception e) {
            this.RemPrepQtde = new BigDecimal("0");
        }
    }

    public BigDecimal getRemPrepValor() {
        return RemPrepValor;
    }

    public void setRemPrepValor(String RemPrepValor) {
        try {
            this.RemPrepValor = new BigDecimal(RemPrepValor);
        } catch (Exception e) {
            this.RemPrepValor = new BigDecimal("0");
        }
    }

    public BigDecimal getSaldo() {
        return Saldo;
    }

    public void setSaldo(String Saldo) {
        try {
            this.Saldo = new BigDecimal(Saldo);
        } catch (Exception e) {
            this.Saldo = new BigDecimal("0");
        }
    }

    public BigDecimal getSaldoReal() {
        return SaldoReal;
    }

    public void setSaldoReal(String SaldoReal) {
        try {
            this.SaldoReal = new BigDecimal(SaldoReal);
        } catch (Exception e) {
            this.SaldoReal = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getDtFecha() {
        return DtFecha;
    }

    public void setDtFecha(String DtFecha) {
        this.DtFecha = DtFecha;
    }

    public String getFilial() {
        return filial;
    }

    public void setFilial(String filial) {
        this.filial = filial;
    }

    public String getUltMovDataEntrada() {
        return ultMovDataEntrada;
    }

    public void setUltMovDataEntrada(String ultMovDataEntrada) {
        this.ultMovDataEntrada = ultMovDataEntrada;
    }

    public String getUltMovHoraEntrada() {
        return ultMovHoraEntrada;
    }

    public void setUltMovHoraEntrada(String ultMovHoraEntrada) {
        this.ultMovHoraEntrada = ultMovHoraEntrada;
    }

    public String getUltMovValorEntrada() {
        return ultMovValorEntrada;
    }

    public void setUltMovValorEntrada(String ultMovValorEntrada) {
        this.ultMovValorEntrada = ultMovValorEntrada;
    }

    public String getUltMovOperadorEntrada() {
        return ultMovOperadorEntrada;
    }

    public void setUltMovOperadorEntrada(String ultMovOperadorEntrada) {
        this.ultMovOperadorEntrada = ultMovOperadorEntrada;
    }

    public String getUltMovDataSaida() {
        return ultMovDataSaida;
    }

    public void setUltMovDataSaida(String ultMovDataSaida) {
        this.ultMovDataSaida = ultMovDataSaida;
    }

    public String getUltMovHoraSaida() {
        return ultMovHoraSaida;
    }

    public void setUltMovHoraSaida(String ultMovHoraSaida) {
        this.ultMovHoraSaida = ultMovHoraSaida;
    }

    public String getUltMovValorSaida() {
        return ultMovValorSaida;
    }

    public void setUltMovValorSaida(String ultMovValorSaida) {
        this.ultMovValorSaida = ultMovValorSaida;
    }

    public String getUltMovOperadorSaida() {
        return ultMovOperadorSaida;
    }

    public void setUltMovOperadorSaida(String ultMovOperadorSaida) {
        this.ultMovOperadorSaida = ultMovOperadorSaida;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CxForte other = (CxForte) obj;
        if (!Objects.equals(this.CodCli, other.CodCli)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        return true;
    }
}

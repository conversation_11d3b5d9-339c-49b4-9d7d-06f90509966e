/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import static br.com.sasw.utils.Mascaras.Moeda;

/**
 *
 * <AUTHOR>
 */
public class Mapas {

    public static final String MARCADOR = "var marker@indice = new google.maps.Marker({ position: {lat: @lat, lng: @lng}, map: map, title: '@title', icon: '@icon'}); try{ListaMarcadores.push(marker@indice);}catch(e){} \n";
    public static final String MARCADOR2 = "var marker@indice = new google.maps.Marker({ position: {lat: @lat, lng: @lng}, map: map2, title: '@title', icon: '@icon'}); try{ListaMarcadores.push(marker@indice);}catch(e){} \n";
    public static final String MARCADOR_PROGRAMACAO = "var marker@indice = new google.maps.Marker({ position: {lat: @lat, lng: @lng}, map: map, title: '@title', icon: '@icon'}); ListaMarcadores.push(marker@indice); \n";

    public static final String PIN_BASE = "https://mobile.sasw.com.br:9091/satmobile/pins/pin_SPM_mapa.png";

    public static final String PIN_VEICULO_MOVIMENTO = "https://mobile.sasw.com.br:9091/satmobile/pins/icone_VEICULO_movimento.png";
    public static final String PIN_VEICULO_PARADO = "https://mobile.sasw.com.br:9091/satmobile/pins/pin_VEICULO_40_70.png";
    public static final String PIN_VEICULO = "https://mobile.sasw.com.br:9091/satmobile/pins/pin_VEICULO_Azul.png";
    public static final String PIN_VERDE_MANHA = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconeverde_M.png";
    public static final String PIN_VERDE_TARDE = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconeverde_T.png";
    public static final String PIN_VERDE_NOITE = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconeverde_NOITE.png";
    public static final String PIN_DOURADO_MANHA = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconedourado_M.png";
    public static final String PIN_DOURADO_TARDE = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconedourado_T.png";
    public static final String PIN_DOURADO_NOITE = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_iconedourado_NOITE.png";
    public static final String PIN_ATRASO = "https://mobile.sasw.com.br:9091/satmobile/pins/novo_icone_ATRASO.png";
    public static final String PIN_CANCELAMENTO_CLIENTE = "https://mobile.sasw.com.br:9091/satmobile/pins/icone_cancelado_cliente.png";
    public static final String PIN_CANCELAMENTO_TRANSPORTADORA = "https://mobile.sasw.com.br:9091/satmobile/pins/icone_cancelado_transp.png";

    public static final String PIN_TROCA_MANHA = "https://mobile.sasw.com.br:9091/satmobile/img/icone_troca_manha.png";
    public static final String PIN_TROCA_TARDE = "https://mobile.sasw.com.br:9091/satmobile/img/icone_troca_tarde.png";
    public static final String PIN_TROCA_NOITE = "https://mobile.sasw.com.br:9091/satmobile/img/icone_troca_noite.png";

    public static final String PIN_PEDIDOS_MANHA = "https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_manha.png";
    public static final String PIN_PEDIDOS_TARDE = "https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_tarde.png";
    public static final String PIN_PEDIDOS_NOITE = "https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_noite.png";

    public static final String POSICOES_MAPA = "waypoints: [@posicoes],";
    public static final String POSICAO_MAPA = "{ location: \"@lat,@lon\", stopover: true }, ";
    public static final String ORIGEM_MAPA = " origin: \"@lat,@lon\", ";
    public static final String DESTINO_MAPA = "destination: \"@lat,@lon\", ";

    public static final String JANELA_INFO = "var infowindow@indice = new google.maps.InfoWindow({\n"
            + "     content: contentString@indice\n"
            + "});\n"
            + "<EMAIL>('click', function() {\n"
            + "   $('.gm-style-iw, .gm-style-iw-t').remove();  <EMAIL>(mapGoogle, marker@indice);\n"
            + "});\n";
    public static final String LEGENDA_ROTAS_INFO = "          var div@indice = document.createElement('div');\n"
            + "         <EMAIL> = ' <a href=\"#\">text.Rota @Rota - text.Veiculo @Veiculo </a>'\n"
            + "         <EMAIL>('id', 'legenda@indice');\n"
            + "         <EMAIL>('click', function() {\n"
            + "             <EMAIL>(mapGoogle, marker@indice);\n"
            + "         }, false);\n"
            + "         legend.appendChild(div@indice);\n";
    public static final String LEGENDA_TRAJETO_INFO = "          var div@indice = document.createElement('div');\n"
            + "         <EMAIL> = ' <a href=\"../operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=@seqRota&codfil=@codfil\">text.Rota @Rota - text.Veiculo @Veiculo </a>'\n"
            + "         legend.appendChild(div@indice);\n";
    public static final String LEGENDA_TRAJETO_INFO_COMPLETO = "          var div@indice = document.createElement('div');\n"
            + "         <EMAIL> = ' <a href=\"#\"><b style=\"font-size:10pt !important\">text.Rota @Rota</b> - text.Veiculo @Veiculo<div class=\"LegendaRota\"><table><tr><td>title.TotalRota</td><td>value.TotalRota</td></tr><tr><td>title.TotalRecolhimentos</td><td>value.TotalRecolhimentos</td></tr><tr><td>title.TotalEntregas</td><td>value.TotalEntregas</td></tr></table></div> </a>'\n"
            + "         <EMAIL>('id', 'legenda@indice');\n"
            + "         <EMAIL>('click', function() {\n"
            + "             <EMAIL>(mapGoogle, marker@indice);\n"
            + "         }, false);\n"
            + "         legend.appendChild(div@indice);\n";
    public static final String LEGENDA_TRAJETO_INFO_COMPLETO_LINK = "          var div@indice = document.createElement('div');\n"
            + "         <EMAIL> = ' <a href=\"../operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=@seqRota&codfil=@codfil\"><b style=\"font-size:10pt !important\">text.Rota @Rota</b> - text.Veiculo @Veiculo<div class=\"LegendaRota\"><table><tr><td>title.TotalRota</td><td>value.TotalRota</td></tr><tr><td>title.TotalRecolhimentos</td><td>value.TotalRecolhimentos</td></tr><tr><td>title.TotalEntregas</td><td>value.TotalEntregas</td></tr></table></div> </a>'\n"
            + "         legend.appendChild(div@indice);\n";
    public static final String LEGENDA_TRAJETO_INFO_CHECKBOX = " var div@indice = document.createElement('div');\n"
            + "         <EMAIL> = '<table><tr><td><a href=\"../operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=@seqRota&codfil=@codfil\">text.Rota @Rota - text.Veiculo @Veiculo </a></td><td class=\"CheckBoxRota\"><input type=\"checkbox\" ref=\"@IdRota\" /></td></tr></table>'\n"
            + "         legend.appendChild(div@indice);\n";
    public static final String LEGENDA_TRAJETO_INFO_COMPLETO_CHECKBOX = "          var div@indice = document.createElement('div');\n"
            + "         <EMAIL> = '<a href=\"#\"><table><tr><td><b style=\"font-size:10pt !important\">text.Rota @Rota</b> - text.Veiculo @Veiculo</td><td class=\"CheckBoxRota\"><input type=\"checkbox\" ref=\"@IdRota\" /></td></tr></table><div class=\"LegendaRota\"><table><tr><td>title.TotalRota</td><td>value.TotalRota</td></tr><tr><td>title.TotalRecolhimentos</td><td>value.TotalRecolhimentos</td></tr><tr><td>title.TotalEntregas</td><td>value.TotalEntregas</td></tr></table></div></a>'\n"
            + "         legend.appendChild(div@indice);\n";
    public static final String INFO_CENTRAL = "var contentString@indice = '<div id=\"content@indice\">'+\n"
            + "     '<h3 id=\"firstHeading\" class=\"firstHeading\">@Nome</h3>'+\n"
            + "     '<div id=\"bodyContent\">'+\n"
            + "         '<p>@Endereco</p> '+\n"
            //           + "         '<br><img src=\"https://maps.googleapis.com/maps/api/streetview?size=350x120&location=@lat,@lon\">'\n"
            + "     '</div>'+\n"
            + "'</div>';\n";
    public static final String INFO_POSICAO = "var contentString@indice = '<div id=\"content@indice\">'+\n"
            + "     '<h3 id=\"firstHeading\" class=\"firstHeading\">@NRed</h3>'+\n"
            + "     '<div id=\"bodyContent\">'+\n"
            + "         '<br><p>text.Nome: @NomeOri</p> '+\n"
            + "         '<p>@EnderecoOri</p> '+\n"
            + "         '<p>@HorarioOri</p><br> '+\n"
            //            + "         '<img src=\"https://maps.googleapis.com/maps/api/streetview?size=350x120&location=@lat,@lon\">'+"
            + "         '<a class=\"btn btn-primary\" href=\"../guia/guia_satmob.xhtml?faces-redirect=true&seqRota=@seqRota&parada=@parada&data=@data\" target=\"_blank\" style=\"color:#FFF !important; display:@mostrarGuia\"><i class=\"fa fa-file-text\"></i> text.Guias</a>'+\n"
            + "         '<a class=\"btn btn-primary\" href=\"javascript:;\" ref=\"btPedido\" numero=\"@numeroPedido\" codfil=\"@codfil\" style=\"color:#FFF !important; display:@mostrarRoteirizar\"><i class=\"fa fa-cog\"></i> text.Roteirizar</a>'+\n"
            + "     '</div>'+\n"
            + "'</div>';\n";
    public static final String INFO_POSICAO_COM_GUIAS = "var contentString@indice = '<div id=\"content@indice\">'+\n"
            + "     '<h3 id=\"firstHeading\" class=\"firstHeading\">@NRed</h3>'+\n"
            + "     '<div id=\"bodyContent\">'+\n"
            + "         '<br><p>text.Nome: @NomeOri</p> '+\n"
            + "         '<p>@EnderecoOri</p> '+\n"
            + "         '<p>@HorarioOri</p> '+\n"
            + "         '<p style=\"padding: 8px !important; width: 100% !important; border: thin solid #DDD !important; margin-top:4px !important; margin-bottom:0px !important; text-align: center !important; border-radius: 4px; background-color: whitesmoke; color: #505050 !important; font-weight: 500 !important; @DadosBaixa\">@Distancia</p><br> '+\n"
            //            + "         '<img src=\"https://maps.googleapis.com/maps/api/streetview?size=350x120&location=@lat,@lon\">'+"
            + "         '@botoesGuias'+\n"
            + "         '@botoesGtv'+\n"
            + "     '</div>'+\n"
            + "'</div>';\n";
    public static final String INFO_ROTA = ""
            + "var contentString@indice = '<div id=\"contentString@indice\">'+\n"
            + "'    <div id=\"marker@indice\">'+\n"
            + "'        <h2 id=\"firstHeading\" class=\"firstHeading\">text.Rota: @rota</h2>'+\n"
            + "'        <div ref=\"infoRota\" id=\"bodyContent\">'+\n"
            + "'            <p align=\"right\"><b>text.TotalemRota:</b> @TotalemRota</p> ' +\n"
            + "'            <p><b>text.Motorista:</b> @motorista</p>'+\n"
            + "'            <p><b>text.ChEquipe:</b> @nome</p>'+\n"
            + "'            <p><b>text.Veiculo:</b> @veiculo <b>text.Placa</b>: @placa - @modelo</p>'+\n"
            + "'            <p><b>text.Horario:</b> @hora1 - @hora4 <b>text.Intervalo</b>:  @hora2- @hora3</p>'+\n"
            + "'            <table style=\"background-color: transparent !important; width: 100%; margin-bottom: 10px; margin-top: 10px;\">'+\n"
            + "'                <tr><td><a href=\"../operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=@seqRota&codfil=@codfil\">'+\n"
            + "'                    <i class=\"fa fa-map\"></i> text.Trajetos</a></td>' +\n"
            + "'                    <td><a href=\"javascript:void(0);\" id=\"btnPrd@indice\" class=\"btn btn-primary\" onclick=\"AbreEfetividade($objEfetividade@indice);\">text.Efetividade</a></td>' +\n"
            + "'                <td style=\"text-align: right;\"><b>text.HPosicao</b>: @HorarioPosicao</td></tr></table>'+\n"
            + "'            <table class=\"tableVeiculo\">'+ \n"
            + "'                <tr>'+\n"
            + "'                    <td></td>'+\n"
            + "'                    <td colspan=\"3\" align=\"center\">text.Feito</td>'+\n"
            + "'                    <td colspan=\"3\" align=\"center\">text.Pendente</td>'+\n"
            + "'                </tr>'+\n"
            + "'                <tr>'+\n"
            + "'                <td></td>'+\n"
            + "'                    <td align=\"center\">text.Trajetos</td>'+\n"
            + "'                    <td align=\"center\">text.Lacres</td>'+\n"
            + "'                    <td align=\"center\">text.Valor</td>'+\n"
            + "'                    <td align=\"center\">text.Trajetos</td>'+\n"
            + "'                    <td align=\"center\">text.Lacres</td>'+\n"
            + "'                    <td align=\"center\">text.Valor</td>'+\n"
            + "'                </tr>'+\n"
            + "'                <tr>'+\n"
            + "'                    <td align=\"left\">text.Recolhimentos</td>'+\n"
            + "'                    <td align=\"right\">@RecOk</td>'+\n"
            + "'                    <td align=\"right\">@RecLacres</td>'+\n"
            + "'                    <td align=\"right\">@RecValor</td>'+\n"
            + "'                    <td align=\"right\">@RecPd</td>'+\n"
            + "'                    <td align=\"right\"></td>'+\n"
            + "'                    <td align=\"right\"></td>'+\n"
            + "'                </tr>'+\n"
            + "'                <tr>'+\n"
            + "'                    <td align=\"left\">text.Entregas</td>'+\n"
            + "'                    <td align=\"right\">@EntOk</td>'+\n"
            + "'                    <td align=\"right\">@EntLacres</td>'+\n"
            + "'                    <td align=\"right\">@EntValor</td>'+\n"
            + "'                    <td align=\"right\">@EntPendente</td>'+\n"
            + "'                    <td align=\"right\">@EntPdLacres</td>'+\n"
            + "'                    <td align=\"right\">@EntPdValor</td>'+\n"
            + "'                </tr>'+\n"
            + "'                <tr>'+\n"
            + "'                    <td align=\"left\">text.Transferencias</td>'+\n"
            + "'                    <td align=\"right\"></td>'+\n"
            + "'                    <td align=\"right\"></td>'+\n"
            + "'                    <td align=\"right\">" + Moeda("0") + "</td>'+\n"
            + "'                    <td align=\"right\"></td>'+\n"
            + "'                    <td align=\"right\"></td>'+\n"
            + "'                    <td align=\"right\">" + Moeda("0") + "</td>'+\n"
            + "'                </tr>'+\n"
            + "'                <tr>'+\n"
            + "'                    <td colspan=\"3\" align=\"right\">text.ValorDepositadoCXF</td>'+\n"
            + "'                    <td colspan=\"4\" align=\"right\">@VlrDepCXF</td>'+\n"
            + "'                </tr>'+\n"
            + "'                <tr>'+\n"
            + "'                    <td colspan=\"3\" align=\"right\">text.VlrEntDir</td>'+\n"
            + "'                    <td colspan=\"4\" align=\"right\">@VlrEntDir</td>'+\n"
            + "'                </tr>'+\n"
            + "'            </table> '+ \n"
            + "'            </br>'+ \n"
            + "'        </div>'+\n"
            + "'    </div>'+\n"
            + "'</div>';\n";
    public static final String INFO_VEICULO = ""
            + "var contentString@indice = '<div id=\"contentString@indice\">'+\n"
            + "'    <div id=\"marker@indice\" </div>'+\n"
            + "'    <h2 id=\"firstHeading\" class=\"firstHeading\">text.Rota: @rota</h2>'+\n"
            + "'    <div id=\"bodyContent\">'+\n"
            + "'        <p align=\"right\"><b>text.TotalemRota:</b> @TotalemRota</p> ' +\n"
            + "'        <p><b>text.Motorista:</b> @motorista</p>'+\n"
            + "'        <p><b>text.ChEquipe:</b> @nome</p>'+\n"
            + "'        <p><b>text.Veiculo:</b> @veiculo <b>text.Placa</b>: @placa - @modelo</p>'+\n"
            + "'        <p><b>text.Horario:</b> @hora1 - @hora4 <b>text.Intervalo</b>:  @hora2- @hora3</p>'+\n"
            + "'        </br>'+\n"
            + "'        <table class=\"tableVeiculo\">'+ \n"
            + "'            <tr>'+\n"
            + "'                <td></td>'+\n"
            + "'                <td colspan=\"3\" align=\"center\">text.Feito</td>'+\n"
            + "'                <td colspan=\"3\" align=\"center\">text.Pendente</td>'+\n"
            + "'            </tr>'+\n"
            + "'            <tr>'+\n"
            + "'                <td></td>'+\n"
            + "'                <td align=\"center\">text.Trajetos</td>'+\n"
            + "'                <td align=\"center\">text.Lacres</td>'+\n"
            + "'                <td align=\"center\">text.Valor</td>'+\n"
            + "'                <td align=\"center\">text.Trajetos</td>'+\n"
            + "'                <td align=\"center\">text.Lacres</td>'+\n"
            + "'                <td align=\"center\">text.Valor</td>'+\n"
            + "'            </tr>'+\n"
            + "'            <tr>'+\n"
            + "'                <td align=\"left\">text.Recolhimentos</td>'+\n"
            + "'                <td align=\"right\">@RecOk</td>'+\n"
            + "'                <td align=\"right\">@RecLacres</td>'+\n"
            + "'                <td align=\"right\">@RecValor</td>'+\n"
            + "'                <td align=\"right\">@RecPd</td>'+\n"
            + "'                <td align=\"right\"></td>'+\n"
            + "'                <td align=\"right\"></td>'+\n"
            + "'            </tr>'+\n"
            + "'            <tr>'+\n"
            + "'                <td align=\"left\">text.Entregas</td>'+\n"
            + "'                <td align=\"right\">@EntOk</td>'+\n"
            + "'                <td align=\"right\">@EntLacres</td>'+\n"
            + "'                <td align=\"right\">@EntValor</td>'+\n"
            + "'                <td align=\"right\">@EntPendente</td>'+\n"
            + "'                <td align=\"right\">@EntPdLacres</td>'+\n"
            + "'                <td align=\"right\">@EntPdValor</td>'+\n"
            + "'            </tr>'+\n"
            + "'            <tr>'+\n"
            + "'                <td align=\"left\">text.Transferencias</td>'+\n"
            + "'                <td align=\"right\"></td>'+\n"
            + "'                <td align=\"right\"></td>'+\n"
            + "'                <td align=\"right\">" + Moeda("0") + "</td>'+\n"
            + "'                <td align=\"right\"></td>'+\n"
            + "'                <td align=\"right\"></td>'+\n"
            + "'                <td align=\"right\">" + Moeda("0") + "</td>'+\n"
            + "'            </tr>'+\n"
            + "'        </table> '+ \n"
            + "'        </br> '+ \n"
            + "'        <table style=\"background-color: transparent !important; width: 100%;\"><tr><td></td>' +\n"
            + "'        <td style=\"text-align: right;\"><b>text.HPosicao</b>: @HorarioPosicao</td></tr></table>'+\n"
            + "'    </div>'+\n"
            + "'</div>';\n";

    public static final String INFO_CANCELAMENTO = "var contentString@indice = '<div id=\"content@indice\">'+\n"
            /*+ "     '<div id=\"marker@indice\"></div>'+\n"*/
            + "     '<h3 id=\"firstHeading\" class=\"firstHeading\">@NRed</h3>'+\n"
            + "     '<div id=\"bodyContent\">'+\n"
            + "         '<p>text.Nome: @NomeOri</p> '+\n"
            + "         '<p>@EnderecoOri</p> '+\n"
            + "         '<p>@HorarioOri</p> '+\n"
            + "         '<p style=\"color: red !important\">@MotivoCanc</p> '+\n"
            + "     '</div>'+\n"
            + "'</div>';\n";

    public static final String SCRIPT_MAPA = "function initMap() {\n"
            + "    var directionsService = new google.maps.DirectionsService;\n"
            + "    var directionsDisplay = new google.maps.DirectionsRenderer({suppressMarkers: true});\n"
            + "    var map = new google.maps.Map(document.getElementById('mapGoogle'), {\n"
            + "        zoom: 10,\n"
            + "        center: @centro \n"
            + "    });\n"
            + "    @markers \n"
            + "    @contentStrings \n"
            + "    @infoWindows \n"
            + "    directionsDisplay.setMap(map);\n"
            + "    var onChangeHandler = function () {\n"
            + "        calculateAndDisplayRoute(directionsService, directionsDisplay);\n"
            + "    };\n"
            + "    onChangeHandler();\n"
            + "}\n"
            + "function calculateAndDisplayRoute(directionsService, directionsDisplay) {\n"
            + "    directionsService.route({\n"
            + "        @directions \n"
            + "        travelMode: 'DRIVING'\n"
            + "    }, function (response, status) {\n"
            + "        if (status === 'OK') {\n"
            + "            directionsDisplay.setDirections(response);\n"
            + "        } else {\n"
            + "            ;\n"
            + "        }\n"
            + "        console.log(response);\n"
            + "    });\n"
            + "}\n";
    
     public static final String INFO_PRODUTIVIDADE_ROTA2 =  " $objEfetividade@indice = '<div align=\"left\"> "
            + "        <h2 id=\"firstHeading\" class=\"firstHeading\">text.Rota: @rota</h2> "
            + "              <p align=\"right\"><b>text.TotalemRota:</b> @TotalemRota</p>   "
            + "              <p><b>text.Motorista:</b> @motorista</p> "
            + "              <p><b>text.ChEquipe:</b> @nome</p> "
            + "              <p><b>text.Veiculo:</b> @veiculo <b>text.Placa</b>: @placa - @modelo</p> "
            + "              <p><b>text.Horario:</b> @hora1 - @hora4 <b>text.Intervalo</b>:  @hora2- @hora3</p> "
            + "              <table style=\"background-color: transparent !important; width: 100%; margin-bottom: 10px; margin-top: 10px;\"> "             
            + "                  <tr><td><a href=\"../operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=@seqRota&codfil=@codfil\"> "
            + "                      <i class=\"fa fa-map\"></i> text.Trajetos</a></td>  "
            + "                  <td style=\"text-align: right;\"><b>text.HPosicao</b>: @HorarioPosicao</td></tr></table> "
            + "              <table class=\"tableVeiculo\" align=\"center\">  "
            + "                  <tr> "
            + "                      <td colspan=\"7\" align=\"center\">text.Efetividade</td> "
            + "                  </tr> "
            + "                  <tr> "
            + "                  <td colspan=\"3\"></td> "
            + "                      <td colspan=\"2\" align=\"center\">text.Quantidade</td> "
            + "                      <td colspan=\"2\" align=\"center\">text.Percentual</td> "
            + "                  </tr> "
            + "                  <tr> "
            + "                      <td colspan=\"3\" align=\"left\">text.Efetivos</td> "
            + "                      <td colspan=\"2\" align=\"right\">@SrvEfetivos</td> "
            + "                      <td colspan=\"2\" align=\"right\">@PercEfetivos</td> "
            + "                  </tr> "
            + "                  <tr> "
            + "                      <td colspan=\"3\" align=\"left\">text.Adiantados</td> "
            + "                      <td colspan=\"2\" align=\"right\">@SrvAdiantados</td> "
            + "                      <td colspan=\"2\" align=\"right\">@PercAdiantados</td> "
            + "                  </tr> "
            + "                  <tr> "
            + "                      <td colspan=\"3\" align=\"left\">text.Atrasados</td> "
            + "                      <td colspan=\"2\" align=\"right\">@SrvAtrasados</td> "
            + "                      <td colspan=\"2\" align=\"right\">@PercAtrasados</td> "
            + "                  </tr> "
            + "                  <tr> "
            + "                      <td colspan=\"3\" align=\"left\">text.Pendentes</td> "
            + "                      <td colspan=\"2\" align=\"right\">@SrvPendentes</td> "
            + "                      <td colspan=\"2\" align=\"right\">@PercPendentes</td> "
            + "                  </tr> "
            + "                  <tr> "
            + "                      <td colspan=\"3\" align=\"left\">text.Total</td> "
            + "                      <td colspan=\"2\" align=\"right\">@SrvTotal</td> "
            + "                      <td colspan=\"2\" align=\"right\">@PercTotal</td> "
            + "                  </tr> "
            + "              </table>   "
            + "              </div>';  ";
    
    
    public static final String INFO_PRODUTIVIDADE_ROTA = " '<div id=\"modalPrd@indice\" class=\"modal\"> ' +"
            + " '     <div class=\"modal-content\"> ' +"
            + " '          <span class=\"close\">&times;</span> ' +"
            + " '         <h2 id=\"firstHeading\" class=\"firstHeading\">text.ResumoEfetividadeRota: @rota</h2> ' +"
            + " '         <div ref=\"infoRota\" id=\"bodyContent\" align=\"center\"> ' +"
            + " '             <p align=\"right\"><b>text.TotalemRota:</b> @TotalemRota</p>   ' +"
            + " '             <p><b>text.Motorista:</b> @motorista</p> ' +"
            + " '             <p><b>text.ChEquipe:</b> @nome</p> ' +"
            + " '             <p><b>text.Veiculo:</b> @veiculo <b>text.Placa</b>: @placa - @modelo</p> ' +"
            + " '             <p><b>text.Horario:</b> @hora1 - @hora4 <b>text.Intervalo</b>:  @hora2- @hora3</p> ' +"
            + " '             <table style=\"background-color: transparent !important; width: 100%; margin-bottom: 10px; margin-top: 10px;\"> ' +"
            + " '                 <tr><td><a href=\"../operacoes/mapa_direccion.xhtml?faces-redirect=true&seqRota=@seqRota&codfil=@codfil\"> ' +"
            + " '                     <i class=\"fa fa-map\"></i> text.Trajetos</a></td>  ' +"
            + " '                 <td style=\"text-align: right;\"><b>text.HPosicao</b>: @HorarioPosicao</td></tr></table> ' +"
            + " '             <table class=\"tableVeiculo\">  ' +"
            + " '                 <tr> ' +"
            + " '                     <td colspan=\"7\" align=\"center\">text.Efetividade</td> ' +"
            + " '                 </tr> ' +"
            + " '                 <tr> ' +"
            + " '                 <td colspan=\"3\"></td> ' +"
            + " '                     <td colspan=\"2\" align=\"center\">text.Quantidade</td> ' +"
            + " '                     <td colspan=\"2\" align=\"center\">text.Percentual</td> ' +"
            + " '                 </tr> ' +"
            + " '                 <tr> ' +"
            + " '                     <td colspan=\"3\" align=\"left\">text.Efetivos</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@SrvEfetivos</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@PercEfetivos</td> ' +"
            + " '                 </tr> ' +"
            + " '                 <tr> ' +"
            + " '                     <td colspan=\"3\" align=\"left\">text.Adiantados</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@SrvAdiantados</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@PercAdiantados</td> ' +"
            + " '                 </tr> ' +"
            + " '                 <tr> ' +"
            + " '                     <td colspan=\"3\" align=\"left\">text.Atrasados</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@SrvAtrasados</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@PercAtrasados</td> ' +"
            + " '                 </tr> ' +"
            + " '                 <tr> ' +"
            + " '                     <td colspan=\"3\" align=\"left\">text.Pendentes</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@SrvPendentes</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@PercPendentes</td> ' +"
            + " '                 </tr> ' +"
            + " '                 <tr> ' +"
            + " '                     <td colspan=\"3\" align=\"left\">text.Total</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@SrvTotal</td> ' +"
            + " '                     <td colspan=\"2\" align=\"right\">@PercTotal</td> ' +"
            + " '                 </tr> ' +"
            + " '             </table>   ' +"
            + " '             </div>  ' +"
            + " '     </div> ' +"
            + " ' </div> ' "
//            + "<script> "
            + " var modal@indice = document.getElementById(\"modalPrd@indice\");"
            + ""
            + "// Get the button that opens the modal"
            + "var btn@indice = document.getElementById(\"btnPrd@indice\");"
            + ""
            + "// Get the <span> element that closes the modal"
            + "var span@indice = document.getElementsByClassName(\"close\")[@jsfind];"
            + ""
            + "// When the user clicks on the button, open the modal"
            + "<EMAIL> = function() {"
            + "  <EMAIL> = \"block\";"
            + "}"
            + ""
            + "// When the user clicks on <span> (x), close the modal"
            + "<EMAIL> = function() {"
            + "  <EMAIL> = \"none\";"
            + "}"
            + ""
            + "// When the user clicks anywhere outside of the modal, close it"
            + "window.onclick = function(event) {"
            + "  if (event.target == modal@indice) {"
            + "    <EMAIL> = \"none\";"
            + "  }"
            + "}";
//            + "</script>";
}

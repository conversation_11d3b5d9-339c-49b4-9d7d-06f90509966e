/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rt_Escala {

    private BigDecimal sequencia;
    private BigDecimal matr;
    private String data;
    private String funcao;
    private String hora;
    private String operador;
    private String dt_alter;
    private String hr_alter;

    public BigDecimal getSequencia() {
        return sequencia;
    }

    public void setSequencia(BigDecimal sequencia) {
        this.sequencia = sequencia;
    }

    public BigDecimal getMatr() {
        return matr;
    }

    public void setMatr(BigDecimal matr) {
        this.matr = matr;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getFuncao() {
        return funcao;
    }

    public void setFuncao(String funcao) {
        this.funcao = funcao;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(String dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

}

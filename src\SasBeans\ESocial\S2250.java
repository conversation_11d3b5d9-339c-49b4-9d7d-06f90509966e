/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S2250 {

    private int sucesso;
    private String evtAvPrevio_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideVinculo_cpfTrab;
    private String ideVinculo_nisTrab;
    private String ideVinculo_matricula;
    private String detAvPrevio_dtAvPrevio;
    private String detAvPrevio_dtPrevDeslig;
    private String detAvPrevio_tpAvPrevio;
    private String detAvPrevio_observacao;
    private String cancAvPrevio_dtCancAvPrv;
    private String cancAvPrevio_observacao;
    private String cancAvPrevio_mtvCancAvPrevio;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtAvPrevio_Id() {
        return evtAvPrevio_Id;
    }

    public void setEvtAvPrevio_Id(String evtAvPrevio_Id) {
        this.evtAvPrevio_Id = evtAvPrevio_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_nisTrab() {
        return ideVinculo_nisTrab;
    }

    public void setIdeVinculo_nisTrab(String ideVinculo_nisTrab) {
        this.ideVinculo_nisTrab = ideVinculo_nisTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getDetAvPrevio_dtAvPrevio() {
        return detAvPrevio_dtAvPrevio;
    }

    public void setDetAvPrevio_dtAvPrevio(String detAvPrevio_dtAvPrevio) {
        this.detAvPrevio_dtAvPrevio = detAvPrevio_dtAvPrevio;
    }

    public String getDetAvPrevio_dtPrevDeslig() {
        return detAvPrevio_dtPrevDeslig;
    }

    public void setDetAvPrevio_dtPrevDeslig(String detAvPrevio_dtPrevDeslig) {
        this.detAvPrevio_dtPrevDeslig = detAvPrevio_dtPrevDeslig;
    }

    public String getDetAvPrevio_tpAvPrevio() {
        return detAvPrevio_tpAvPrevio;
    }

    public void setDetAvPrevio_tpAvPrevio(String detAvPrevio_tpAvPrevio) {
        this.detAvPrevio_tpAvPrevio = detAvPrevio_tpAvPrevio;
    }

    public String getDetAvPrevio_observacao() {
        return detAvPrevio_observacao;
    }

    public void setDetAvPrevio_observacao(String detAvPrevio_observacao) {
        this.detAvPrevio_observacao = detAvPrevio_observacao;
    }

    public String getCancAvPrevio_dtCancAvPrv() {
        return cancAvPrevio_dtCancAvPrv;
    }

    public void setCancAvPrevio_dtCancAvPrv(String cancAvPrevio_dtCancAvPrv) {
        this.cancAvPrevio_dtCancAvPrv = cancAvPrevio_dtCancAvPrv;
    }

    public String getCancAvPrevio_observacao() {
        return cancAvPrevio_observacao;
    }

    public void setCancAvPrevio_observacao(String cancAvPrevio_observacao) {
        this.cancAvPrevio_observacao = cancAvPrevio_observacao;
    }

    public String getCancAvPrevio_mtvCancAvPrevio() {
        return cancAvPrevio_mtvCancAvPrevio;
    }

    public void setCancAvPrevio_mtvCancAvPrevio(String cancAvPrevio_mtvCancAvPrevio) {
        this.cancAvPrevio_mtvCancAvPrevio = cancAvPrevio_mtvCancAvPrevio;
    }

}

package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class EmailsEnviar {

    // 19/01/2018
    private String codCli;
    private String codFil;
    private String codContato;
    private String parametro;

    private String Flag_Enviado;
    private String Dt_Inclusao;
    private String Hr_Inclusao;
    private String Dt_Envio;
    private String Hr_Envio;

    private BigDecimal Sequencia;
    private String smtp;
    private String dest_email;
    private String dest_nome;
    private String remet_email;
    private String remet_nome;
    private String assunto;
    private String mensagem;
    private String aut_login;
    private String aut_senha;
    private int porta;
    private int contador;

    public String getCodCli() {
        return codCli;
    }

    public void setCodCli(String codCli) {
        this.codCli = codCli;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getCodContato() {
        return codContato;
    }

    public void setCodContato(String codContato) {
        this.codContato = codContato;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    /**
     * Obtem a quantidade de vezes que foi executada
     *
     * @return quantidade
     */
    public int getContador() {
        return contador;
    }

    /**
     * Inseri a quantidade de vezes que foi executado
     *
     * @param contador
     */
    public void setContador(int contador) {
        this.contador = contador;
    }

    //sets da classe
    /**
     * Define a sequencia dos emails
     *
     * @param value
     */
    public void setSequencia(String value) {
        Sequencia = new BigDecimal(value);
    }

    /**
     * Define STMP de envio
     *
     * @param value
     */
    public void setSmtp(String value) {
        smtp = value;
    }

    /**
     * Define email do destinatario
     *
     * @param value
     */
    public void setDest_email(String value) {
        dest_email = value;
    }

    /**
     * Define Nome do destinatario
     *
     * @param value
     */
    public void setDest_nome(String value) {
        dest_nome = value;
    }

    /**
     * Define email do remetente
     *
     * @param value
     */
    public void setRemet_email(String value) {
        remet_email = value;
    }

    /**
     * Define nome do remetente
     *
     * @param value
     */
    public void setRemet_nome(String value) {
        remet_nome = value;
    }

    /**
     * Define o assunto do email
     *
     * @param value
     */
    public void setAssunto(String value) {
        assunto = value;
    }

    /**
     * Define Mensagem do email
     *
     * @param value
     */
    public void setMensagem(String value) {
        if (value.contains("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">")) {
            value = value.replace("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n", "");
        }
        mensagem = value;
    }

    /**
     * Define login de autenticacao
     *
     * @param value
     */
    public void setAut_login(String value) {
        aut_login = value;
    }

    /**
     * Define senha de autentificacao
     *
     * @param value
     */
    public void setAut_senha(String value) {
        aut_senha = value;
    }

    /**
     * Define porta do SMTP
     *
     * @param value
     */
    public void setPorta(int value) {
        porta = value;
    }

    //Gets da classe
    /**
     * Retorna a Sequencia do email
     */
    public BigDecimal getSequencia() {
        return Sequencia;
    }

    /**
     * Retorna smtp de envio
     *
     * @return
     */
    public String getSmtp() {
        return smtp;
    }

    /**
     * Retorna email do destinatario
     *
     * @return
     */
    public String getDest_email() {
        return dest_email;
    }

    /**
     * Retorna nome do destinatario
     *
     * @return
     */
    public String getDest_nome() {
        return dest_nome;
    }

    /**
     * Retorna email do remetente
     *
     * @return
     */
    public String getRemet_email() {
        return remet_email;
    }

    /**
     * Retorna nome do remetente
     *
     * @return
     */
    public String getRemet_nome() {
        return remet_nome;
    }

    /**
     * Retorna assunto do email
     *
     * @return
     */
    public String getAssunto() {
        return assunto;
    }

    /**
     * Retorna mensagem do email
     *
     * @return
     */
    public String getMensagem() {
        return mensagem;
    }

    /**
     * Retorna login de autenticacao
     *
     * @return
     */
    public String getAut_login() {
        return aut_login;
    }

    /**
     * Retorna Senha de autenticacao
     *
     * @return
     */
    public String getAut_senha() {
        return aut_senha;
    }

    /**
     * Retorna porta de envio
     *
     * @return
     */
    public int getPorta() {
        return porta;
    }

    public String getFlag_Enviado() {
        return Flag_Enviado;
    }

    public void setFlag_Enviado(String Flag_Enviado) {
        this.Flag_Enviado = Flag_Enviado;
    }

    public String getDt_Inclusao() {
        return Dt_Inclusao;
    }

    public void setDt_Inclusao(String Dt_Inclusao) {
        this.Dt_Inclusao = Dt_Inclusao;
    }

    public String getHr_Inclusao() {
        return Hr_Inclusao;
    }

    public void setHr_Inclusao(String Hr_Inclusao) {
        this.Hr_Inclusao = Hr_Inclusao;
    }

    public String getDt_Envio() {
        return Dt_Envio;
    }

    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    public String getHr_Envio() {
        return Hr_Envio;
    }

    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 53 * hash + Objects.hashCode(this.Sequencia);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final EmailsEnviar other = (EmailsEnviar) obj;
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        return true;
    }
}

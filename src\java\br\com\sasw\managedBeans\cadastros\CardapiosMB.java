/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cadastros;

import Arquivo.ArquivoLog;
import Controller.Cardapio.CardapioController;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Cardapio;
import SasBeans.CardapioDia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeansCompostas.CardapioDiaDietaCardapio;
import SasDaos.CardapioDiaDao;
import br.com.sasw.managedBeans.comercial.ClientesMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "cardapio")
@ViewScoped
public class CardapiosMB implements Serializable {

    private final BigDecimal codPessoa;
    private final String banco, operador, caminho, codFil;
    private final ArquivoLog logerro = new ArquivoLog();
    private final RotasSatWeb rotassatweb = new RotasSatWeb();
    private final CardapioController controller = new CardapioController();
    private Persistencia persistencia;
    private Filiais filiais;
    private String log, dataTela;
    private Clientes clientes;
    private List<CardapioDiaDietaCardapio> allCardapioDia;
    private CardapioDiaDietaCardapio cardapioDiaDietaCardapioSelecionado, cardapioDiaDietaCardapioNovo;
    private CardapioDia cardapioDiaASalvar;
    private List<SasBeans.Cardapio> cardapiosCadastrados, allCardapiosCadastradosPaginado;
    private SasBeans.Cardapio cardapioVazio, cardapioCadastroSelecionado;
    private String chavePesquisa = "CARDAPIO", valorPesquisa;
    private Map filters;
    private Date dataInicio, dataFim;
    private ClientesMB clientesMB;
    private boolean editandoCardapioDia;
    private final String CODIGO = "CardapioDia.Sequencia = ?",
            CODCARDAPIO = "Cardapio.Codigo = ?",
            DIETA = "Dieta.Descricao LIKE ?",
            CARDAPIO = "Cardapio.Descricao LIKE ?",
            ESPECIFICACAO = "Cardapio.Especificacao LIKE ?",
            OBSERVACAO = "CardapioDia.Especificacao LIKE ?",
            DATA1 = "CardapioDia.Data >= ?",
            DATA2 = "CardapioDia.Data <= ?";

    public CardapiosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        cardapioVazio = new Cardapio();
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        log = new String();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        dataTela = DataAtual.getDataAtual("SQL");
        clientes = new Clientes();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
    }

    public void Persistencias(Persistencia pstLocal) {
        try {
            persistencia = pstLocal;
            if (null == persistencia) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }
            filters = new HashMap<>();
            filters.put(CODIGO, "");
            filters.put(CODCARDAPIO, "");
            filters.put(DIETA, "");
            filters.put(CARDAPIO, "");
            filters.put(ESPECIFICACAO, "");
            filters.put(OBSERVACAO, "");
            filters.put(DATA1, dataInicio);
            filters.put(DATA2, dataFim);
            clientesMB = new ClientesMB(pstLocal);

            filiais = rotassatweb.buscaInfoFilial(codFil, persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private void logaErro(final Exception e, final String methodName) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    public void setDiaDeHoje() {
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataInicio = c.getTime();
        dataFim = c.getTime();
    }

    public void onDateSelect() {
        if (dataFim.before(dataInicio)) {
            String warning = Messages.getMessageS("DatasIncoerentes");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, warning, null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }

        carregarListaCardapioDia();
    }

    public void vaDataAnterior() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataInicio);
        calendar.add(Calendar.DATE, -1);
        dataInicio = calendar.getTime();

        carregarListaCardapioDia();
    }

    public void vaDataPosterior() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataFim);
        calendar.add(Calendar.DATE, +1);
        dataFim = calendar.getTime();

        carregarListaCardapioDia();
    }

    public void carregarListaCardapioDia() {
        try {
            Format formatter = new SimpleDateFormat("yyyy-MM-dd");
            filters.replace(DATA1, formatter.format(dataInicio));
            filters.replace(DATA2, formatter.format(dataFim));
            allCardapioDia = controller.listarCardapioDiaDietaCardapioPaginada(0, 50, filters, persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    public void preCadastro() throws Exception {
        editandoCardapioDia = false;
        cardapiosCadastrados = controller.listaCardapiosCadastrados(persistencia);
        cardapioDiaDietaCardapioNovo = new CardapioDiaDietaCardapio();
        cardapioDiaDietaCardapioNovo.setData(DataAtual.getDataAtual("SQL"));

        PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    public void preEdicao() throws Exception {
        if (cardapioDiaDietaCardapioSelecionado == null) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCardapio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                editandoCardapioDia = true;
                cardapiosCadastrados = controller.listaCardapiosCadastrados(persistencia);
                cardapioDiaDietaCardapioNovo = new CardapioDiaDietaCardapio(cardapioDiaDietaCardapioSelecionado);

                this.clientesMB.setCodigo(cardapioDiaDietaCardapioNovo.getClientes().getCodigo());
                this.clientesMB.BuscarCliente();
                this.clientes = this.clientesMB.getLista().get(0);

                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }

    public void SelecionarCliente(SelectEvent event) {
        Clientes cli = ((Clientes) event.getObject());
        this.clientesMB.setCodigo(cli.getCodigo());
        this.clientesMB.BuscarCliente();
        this.clientes = this.clientesMB.getLista().get(0);
    }

    private boolean validarCardapioDia() {
        if (cardapioDiaASalvar.getCodDieta() == null
                || cardapioDiaASalvar.getCodCardapio() == null
                || cardapioDiaASalvar.getData() == null
                || cardapioDiaASalvar.getEspecificacao() == null
                || cardapioDiaASalvar.getOperador() == null
                || cardapioDiaASalvar.getDt_alter() == null
                || cardapioDiaASalvar.getHr_Alter() == null) {
            return false;
        }
        if (editandoCardapioDia && cardapioDiaASalvar.getSequencia() == null) {
            return false;
        }
        return true;
    }

    public void salvarCardapioDia(String dialogId) throws Exception {
        try {
            cardapioDiaDietaCardapioNovo.setClientes(this.clientes);
            cardapioDiaASalvar = new CardapioDia();
            cardapioDiaASalvar.setSequencia(cardapioDiaDietaCardapioNovo.getSequencia());
            cardapioDiaASalvar.setCodDieta(cardapioDiaDietaCardapioNovo.getDieta().getSequencia());
            cardapioDiaASalvar.setCodCardapio(cardapioDiaDietaCardapioNovo.getCardapio().getCodigo());
            cardapioDiaASalvar.setEspecificacao(cardapioDiaDietaCardapioNovo.getEspecificacao().toUpperCase());
            cardapioDiaASalvar.setData(cardapioDiaDietaCardapioNovo.getData());
            cardapioDiaASalvar.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            cardapioDiaASalvar.setCodCli(cardapioDiaDietaCardapioNovo.getClientes().getCodigo());
            cardapioDiaASalvar.setPeriodo(cardapioDiaDietaCardapioNovo.getPeriodo());
            cardapioDiaASalvar.setHr_Alter(DataAtual.getDataAtual("HORA"));
            cardapioDiaASalvar.setDt_alter(DataAtual.getDataAtual("SQL"));

            if (null == cardapioDiaASalvar.getCodDieta()
                    || cardapioDiaASalvar.getCodDieta().equals("")
                    || null == cardapioDiaASalvar.getCodCli()
                    || cardapioDiaASalvar.getCodCli().equals("")) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("Obrigatorio"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                if (!validarCardapioDia()) {
                    throw new Exception("dadosNaoConfereTenteNovamente");
                }

                if (editandoCardapioDia) {
                    controller.editarCardapioDia(cardapioDiaASalvar, persistencia);
                } else {
                    controller.cadastrarCardapioDia(cardapioDiaASalvar, persistencia);
                }
                salvarCardapioDiaSucesso(dialogId);
            }
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
        }
    }

    private void salvarCardapioDiaSucesso(String dialogId) {
        final String mensagem = editandoCardapioDia ? "EdicaoSucesso" : "CadastroSucesso";
        carregarListaCardapioDia();
        PrimeFaces.current().executeScript("PF('" + dialogId + "').hide();");
        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagem), null);
        FacesContext.getCurrentInstance().addMessage(null, message);
    }

    public void prepararPesquisa(String formId, String dialogId) {
        String script = "PF('" + dialogId + "').show();";
        PrimeFaces.current().resetInputs(formId);
        PrimeFaces.current().executeScript(script);
    }

    public void pesquisarCardapioDia() {
        resetFilters();
        replaceFilter(valorPesquisa);
        carregarListaCardapioDia();
    }

    private void replaceFilter(String valor) {
        switch (chavePesquisa) {
            case "CODIGO":
                filters.replace(CODIGO, valor);
                return;
            case "CODCARDAPIO":
                filters.replace(CODCARDAPIO, valor);
                return;
            case "DIETA":
                filters.replace(DIETA, "%" + valor + "%");
                return;
            case "CARDAPIO":
                filters.replace(CARDAPIO, "%" + valor + "%");
                return;
            case "ESPECIFICACAO":
                filters.replace(ESPECIFICACAO, "%" + valor + "%");
                return;
            case "OBSERVACAO":
                filters.replace(OBSERVACAO, "%" + valor + "%");
                return;
        }
    }

    private void resetFilters() {
        filters.replace(CODIGO, "");
        filters.replace(CODCARDAPIO, "");
        filters.replace(DIETA, "");
        filters.replace(CARDAPIO, "");
        filters.replace(ESPECIFICACAO, "");
        filters.replace(OBSERVACAO, "");
    }

    public void limparFiltros() {
        resetFilters();
        carregarListaCardapioDia();
    }

    public void allCardapiosCadastro() throws Exception {
        int primeiro = 0;
        int ultimo = 49;
        this.cardapioCadastroSelecionado = new Cardapio();

        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            primeiro = dt.getFirst();
        } catch (Exception e) {
            primeiro = 0;
        }

        allCardapiosCadastradosPaginado = controller.listaCardapiosCadastradosPaginado(primeiro, ultimo, persistencia, "");
    }

    public void preCadastroCardapio() throws Exception {
        cardapioVazio = new Cardapio();
        this.cardapioCadastroSelecionado = new Cardapio();

        PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    public void prePesquisaCadastro() throws Exception {
        this.chavePesquisa = "D";
        this.valorPesquisa = "";

        PrimeFaces.current().resetInputs("formPesquisaRapida:panelPesquisaRapida");
        PrimeFaces.current().executeScript("PF('dlgPesquisaRapida').show();");
    }

    public void buttonActionCadastro(ActionEvent actionEvent) {
        if (null == cardapioCadastroSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCardapio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                cardapioVazio = new Cardapio();
                cardapioVazio.setCodigo(cardapioCadastroSelecionado.getCodigo());
                cardapioVazio.setDescricao(cardapioCadastroSelecionado.getDescricao());
                cardapioVazio.setEspecificacao(cardapioCadastroSelecionado.getEspecificacao());

                PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");

            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }
    
    public void excluirCardapio() {
        try {
            if (null != this.cardapioCadastroSelecionado) {
                CardapioDiaDao objCardapio = new CardapioDiaDao();
                objCardapio.excluirCardapio(this.cardapioCadastroSelecionado.getCodigo(), this.persistencia);
                allCardapiosCadastro();
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExcluidoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCardapio"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }
    
    public void buttonActionExcluir(ActionEvent actionEvent) {
        if (null == cardapioCadastroSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCardapio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                PrimeFaces.current().executeScript("$.MsgBoxVerdeSimNao('" + Messages.getMessageS("Atencao") + "','"
                    + Messages.getMessageS("ExcluirCardapio") + " " + this.cardapioCadastroSelecionado.getCodigo() + " ?','"
                    + Messages.getMessageS("Sim") + "','"
                    + Messages.getMessageS("Nao") + "',"
                    + "function(){ execExclusao();});");
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }
    
    public void excluirCardapioDia() {
        try {
            if (null != this.cardapioDiaDietaCardapioSelecionado) {
                CardapioDiaDao objCardapio = new CardapioDiaDao();
                objCardapio.excluirCardapioDia(this.cardapioDiaDietaCardapioSelecionado.getSequencia(), this.persistencia);
                carregarListaCardapioDia();
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExcluidoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCardapio"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void buttonActionExcluirCardapioDia(ActionEvent actionEvent) {
        if (null == cardapioDiaDietaCardapioSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCardapio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                PrimeFaces.current().executeScript("$.MsgBoxVerdeSimNao('" + Messages.getMessageS("Atencao") + "','"
                    + Messages.getMessageS("ExcluirCardapio") + " " + this.cardapioDiaDietaCardapioSelecionado.getSequencia() + " ?','"
                    + Messages.getMessageS("Sim") + "','"
                    + Messages.getMessageS("Nao") + "',"
                    + "function(){ execExclusao();});");
            } catch (Exception e) {
                logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            }
        }
    }
    
    public void dblSelectCadastro(SelectEvent event) {
        this.cardapioCadastroSelecionado = (Cardapio) event.getObject();
        buttonActionCadastro(null);
    }

    public void pesquisaRapidaCadastro() throws Exception {
        String Where = "";

        switch (this.chavePesquisa) {
            case "C":
                Where = " WHERE Codigo = '" + this.valorPesquisa + "'";
                break;
            case "D":
                Where = " WHERE Descricao LIKE'%" + this.valorPesquisa + "%'";
                break;
            case "E":
                Where = " WHERE Especificacao LIKE'%" + this.valorPesquisa + "%'";
                break;
        }

        allCardapiosCadastradosPaginado = controller.listaCardapiosCadastradosPaginado(0, 50, persistencia, Where);
        PrimeFaces.current().executeScript("PF('dlgPesquisaRapida').hide();");
    }

    public void limparFiltrosCadastro() throws Exception {
        allCardapiosCadastro();
    }

    public void salvarCadastroCardapio() throws Exception {
        String Mensagem = "CadastroSucesso";
        this.cardapioVazio.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.cardapioVazio.setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.cardapioVazio.setDt_alter(DataAtual.getDataAtual("SQL"));
        this.cardapioVazio.setDescricao(this.cardapioVazio.getDescricao().toUpperCase());
        this.cardapioVazio.setEspecificacao(this.cardapioVazio.getEspecificacao().toUpperCase());

        if (null != cardapioCadastroSelecionado
                && !cardapioCadastroSelecionado.getCodigo().equals("")
                && !cardapioCadastroSelecionado.getCodigo().equals("0")) {
            this.cardapioVazio.setCodigo(cardapioCadastroSelecionado.getCodigo());
            Mensagem = "EdicaoSucesso";
        } else {
            this.cardapioVazio.setCodigo("0");
        }

        controller.salvarCadastroCardapio(this.cardapioVazio, this.persistencia);
        if (!this.cardapioVazio.getCodigo().equals("0")) {
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
        }
        this.cardapioVazio = new Cardapio();
        this.cardapioCadastroSelecionado = new Cardapio();
        allCardapiosCadastro();

        PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
        PrimeFaces.current().executeScript("$('input[id*=\"descricao\"]').focus();");
        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(Mensagem), null);
        FacesContext.getCurrentInstance().addMessage(null, message);
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public List<CardapioDiaDietaCardapio> getAllCardapioDia() {
        return allCardapioDia;
    }

    public CardapioDiaDietaCardapio getCardapioDiaDietaCardapioSelecionado() {
        return cardapioDiaDietaCardapioSelecionado;
    }

    public void setCardapioDiaDietaCardapioSelecionado(CardapioDiaDietaCardapio cardapioDiaDietaCardapioSelecionado) {
        this.cardapioDiaDietaCardapioSelecionado = cardapioDiaDietaCardapioSelecionado;
    }

    public List<Cardapio> getCardapiosCadastrados() {
        return cardapiosCadastrados;
    }

    public void setCardapiosCadastrados(List<Cardapio> cardapiosCadastrados) {
        this.cardapiosCadastrados = cardapiosCadastrados;
    }

    public Cardapio getCardapioVazio() {
        return cardapioVazio;
    }

    public void setCardapioVazio(Cardapio cardapioVazio) {
        this.cardapioVazio = cardapioVazio;
    }

    public List<Cardapio> getAllCardapiosCadastradosPaginado() {
        return allCardapiosCadastradosPaginado;
    }

    public void setAllCardapiosCadastradosPaginado(List<Cardapio> allCardapiosCadastradosPaginado) {
        this.allCardapiosCadastradosPaginado = allCardapiosCadastradosPaginado;
    }

    public Cardapio getCardapioCadastroSelecionado() {
        return cardapioCadastroSelecionado;
    }

    public void setCardapioCadastroSelecionado(Cardapio cardapioCadastroSelecionado) {
        this.cardapioCadastroSelecionado = cardapioCadastroSelecionado;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public boolean isEditandoCardapioDia() {
        return editandoCardapioDia;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public CardapioDiaDietaCardapio getCardapioDiaDietaCardapioNovo() {
        return cardapioDiaDietaCardapioNovo;
    }

    public void setCardapioDiaDietaCardapioNovo(CardapioDiaDietaCardapio cardapioDiaDietaCardapioNovo) {
        this.cardapioDiaDietaCardapioNovo = cardapioDiaDietaCardapioNovo;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.compostas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaLogin;
import SasBeans.PessoaPortalSrv;
import SasBeansCompostas.UsuarioSatMobWebServicos;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class UsuarioSatMobWebServicosDao {

    public List<UsuarioSatMobWebServicos> buscarUsuarios(String email, Persistencia persistencia) throws Exception {
        try {
            List<UsuarioSatMobWebServicos> retorno = new ArrayList<>();
            UsuarioSatMobWebServicos usuarioSatMobWebServicos;
            PessoaLogin pessoaLogin;
            PessoaPortalSrv pessoaPortalSrv;
            int indicePessoa, indiceLogin, indicePortal;

            String sql = "Select Pessoa.Codigo CodPessoa, Pessoa.nome, Pessoa.email, Pessoa.pwWEB, PessoaLogin.BancoDados, PessoaLogin.CodPessoaBD, \n"
                    + " ISNULL(PortalSrv.Codigo,'000') Servico, SubString(ISNULL(PortalSrv.Descricao,'Satellite WEB'),1,100) DescricaoSrv, \n"
                    + " pessoaportalsrv.Ordem, PessoaLogin.Nivel \n"
                    + " from Pessoa \n"
                    + " left join PessoaPortalSrv on PessoaPortalSrv.Codigo = pessoa.Codigo \n"
                    + " left join PortalSrv on POrtalSrv.Codigo = PessoaPortalSrv.Servico \n"
                    + " left join PessoaLogin on PessoaLogin.Codigo = Pessoa.Codigo \n"
                    + " where Pessoa.email = ? ORDER BY CASE WHEN PessoaLogin.BancoDados <> 'SATELLITE' THEN REPLACE(PessoaLogin.BancoDados,'SAT','') ELSE PessoaLogin.BancoDados END, CASE WHEN PortalSrv.Codigo = 0 THEN 'AA' ELSE PortalSrv.Descricao END";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(email);
            consulta.select();
            while (consulta.Proximo()) {
                usuarioSatMobWebServicos = new UsuarioSatMobWebServicos();
                usuarioSatMobWebServicos.getPessoa().setCodigo(consulta.getString("CodPessoa"));
                usuarioSatMobWebServicos.getPessoa().setEmail(consulta.getString("Email"));
                usuarioSatMobWebServicos.getPessoa().setPWWeb(consulta.getString("pwWEB"));
                usuarioSatMobWebServicos.getPessoa().setNome(consulta.getString("nome"));

                pessoaLogin = new PessoaLogin();
                pessoaLogin.setCodigo(consulta.getString("CodPessoa"));
                pessoaLogin.setBancoDados(consulta.getString("BancoDados"));
                pessoaLogin.setCodPessoaBD(consulta.getString("CodPessoaBD"));
                pessoaLogin.setNivel(consulta.getString("Nivel"));

                pessoaPortalSrv = new PessoaPortalSrv();
                pessoaPortalSrv.setCodigo(consulta.getString("CodPessoa"));
                pessoaPortalSrv.setServico(consulta.getString("Servico"));
                pessoaPortalSrv.setDescricao(consulta.getString("DescricaoSrv"));
                pessoaPortalSrv.setOrdem(consulta.getString("Ordem"));

                indicePessoa = retorno.indexOf(usuarioSatMobWebServicos);
                if (indicePessoa >= 0) {
                    indiceLogin = retorno.get(indicePessoa).getPessoaLogin().indexOf(pessoaLogin);
                    if (indiceLogin == -1) {
                        retorno.get(indicePessoa).getPessoaLogin().add(pessoaLogin);
                    }

                    indicePortal = retorno.get(indicePessoa).getPessoaPortalSrv().indexOf(pessoaPortalSrv);
                    if (indicePortal == -1) {
                        retorno.get(indicePessoa).getPessoaPortalSrv().add(pessoaPortalSrv);
                    }
                } else {
                    usuarioSatMobWebServicos.getPessoaLogin().add(pessoaLogin);
                    usuarioSatMobWebServicos.getPessoaPortalSrv().add(pessoaPortalSrv);

                    retorno.add(usuarioSatMobWebServicos);
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("UsuarioSatMobWebServicosDao.buscarUsuarios - " + e.getMessage() + "\r\n"
                    + "Select Pessoa.Codigo CodPessoa, Pessoa.nome, Pessoa.email, Pessoa.pwWEB, PessoaLogin.BancoDados, PessoaLogin.CodPessoaBD,\n"
                    + "PortalSrv.Codigo Servico, SubString(PortalSrv.Descricao,1,100) DescricaoSrv, pessoaportalsrv.Ordem \n"
                    + "from Pessoa\n"
                    + "left join PessoaPortalSrv on PessoaPortalSrv.Codigo = pessoa.Codigo \n"
                    + "left join PortalSrv on POrtalSrv.Codigo = PessoaPortalSrv.Servico\n"
                    + "left join PessoaLogin on PessoaLogin.Codigo = Pessoa.Codigo\n"
                    + "where Pessoa.email = " + email);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Rt_Modelo;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rt_ModeloDao {

    /**
     * Lista os modelos de rota cadastrados destino.
     *
     * @param codFil
     * @param excl
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rt_Modelo> listarModelosRota(String codFil, Boolean excl, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {
            List<Rt_Modelo> retorno = new ArrayList<>();

            sql.append("SELECT");
            sql.append(" Modelo,");
            sql.append(" COUNT(*) QtdeRotas,");
            sql.append(" Sequencia,");
            sql.append(" Rota,");
            sql.append(" Data,");
            sql.append(" CodFil,");
            sql.append(" TpVeic,");
            sql.append(" Viagem,");
            sql.append(" ATM,");
            sql.append(" BACEN,");
            sql.append(" Aeroporto,");
            sql.append(" HRLargada,");
            sql.append(" HrChegada,");
            sql.append(" HrIntIni,");
            sql.append(" HsTotal,");
            sql.append(" Observacao,");
            sql.append(" OperFech,");
            sql.append(" DtFim,");
            sql.append(" Operador,");
            sql.append(" Dt_Alter,");
            sql.append(" Hr_Alter,");
            sql.append(" MIN(Flag_excl) Flag_Excl,");
            sql.append(" MAX(Dt_Excl) Dt_Excl,");
            sql.append(" MAX(Hr_Excl) Hr_Excl");
            sql.append(" FROM GRotas");
            sql.append(" WHERE CodFil = ?");
            if (!excl) {
                sql.append(" AND   Flag_excl <> ?");
            }
            sql.append(" GROUP BY Modelo,");
            sql.append("          Sequencia,");
            sql.append("          Rota,");
            sql.append("          Data,");
            sql.append("          CodFil,");
            sql.append("          TpVeic,");
            sql.append("          Viagem,");
            sql.append("          ATM,");
            sql.append("          BACEN,");
            sql.append("          Aeroporto,");
            sql.append("          HRLargada,");
            sql.append("          HrChegada,");
            sql.append("          HrIntIni,");
            sql.append("          HsTotal,");
            sql.append("          Observacao,");
            sql.append("          OperFech,");
            sql.append("          DtFim,");
            sql.append("          Operador,");
            sql.append("          Dt_Alter,");
            sql.append("          Hr_Alter");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setString(codFil);
            if (!excl) {
                consulta.setString("*");
            }
            consulta.select();

            Rt_Modelo rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Modelo();
                rt_Perc.setATM(consulta.getString("ATM"));
                rt_Perc.setAeroporto(consulta.getString("Aeroporto"));
                rt_Perc.setBACEN(consulta.getString("BACEN"));
                rt_Perc.setCodFil(consulta.getString("CodFil"));
                rt_Perc.setData(consulta.getString("Data"));
                rt_Perc.setDtFim(consulta.getString("DtFim"));
                rt_Perc.setDt_Alter(consulta.getString("Dt_Alter"));
                rt_Perc.setDt_Excl(consulta.getString("Dt_Excl"));
                rt_Perc.setFlag_Excl(consulta.getString("Flag_Excl"));
                rt_Perc.setHRLargada(consulta.getString("HRLargada"));
                rt_Perc.setHrIntIni(consulta.getString("HrIntIni"));
                rt_Perc.setHr_Alter(consulta.getString("Hr_Alter"));
                rt_Perc.setHr_Excl(consulta.getString("Hr_Excl"));
                rt_Perc.setHsTotal(consulta.getString("HsTotal"));
                rt_Perc.setModelo(consulta.getString("Modelo"));
                rt_Perc.setObservacao(consulta.getString("Observacao"));
                rt_Perc.setOperFech(consulta.getString("OperFech"));
                rt_Perc.setOperador(consulta.getString("Operador"));
                rt_Perc.setQtdeRotas(consulta.getString("QtdeRotas"));
                rt_Perc.setRota(consulta.getString("Rota"));
                rt_Perc.setSequencia(consulta.getString("Sequencia"));
                rt_Perc.setTpVeic(consulta.getString("TpVeic"));
                rt_Perc.setViagem(consulta.getString("Viagem"));

                retorno.add(rt_Perc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_ModeloDao.listarModelosRota - " + e.getMessage() + " \r\n" + sql.toString());
        }
    }

    /**
     * Lista os modelos de rota cadastrados destino.
     *
     * @param codFil
     * @param excl
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rt_Modelo> listarModelosRotaResumido(String codFil, Boolean excl, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {
            List<Rt_Modelo> retorno = new ArrayList<>();

            sql.append("SELECT");
            sql.append(" Modelo,");
            sql.append(" COUNT(*) QtdeRotas,");
            sql.append(" MIN(Flag_excl) Flag_Excl,");
            sql.append(" MAX(Dt_Excl) Dt_Excl,");
            sql.append(" MAX(Hr_Excl) Hr_Excl");
            sql.append(" FROM GRotas");
            sql.append(" WHERE CodFil = ?");
            if (!excl) {
                sql.append(" AND   Flag_excl <> ?");
            }
            sql.append(" GROUP BY Modelo");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setString(codFil);
            if (!excl) {
                consulta.setString("*");
            }
            consulta.select();

            Rt_Modelo rt_Perc;
            while (consulta.Proximo()) {
                rt_Perc = new Rt_Modelo();
                rt_Perc.setModelo(consulta.getString("Modelo"));
                rt_Perc.setQtdeRotas(consulta.getString("QtdeRotas"));
                rt_Perc.setDt_Excl(consulta.getString("Dt_Excl"));
                rt_Perc.setHr_Excl(consulta.getString("Hr_Excl"));
                rt_Perc.setFlag_Excl(consulta.getString("Flag_Excl"));

                retorno.add(rt_Perc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_ModeloDao.listarModelosRota - " + e.getMessage() + " \r\n" + sql.toString());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2399;
import SasBeans.ESocial.S2399.DetOper;
import SasBeans.ESocial.S2399.DetPlano;
import SasBeans.ESocial.S2399.DetVerbas;
import SasBeans.ESocial.S2399.InfoPerApur;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2399Dao {

    public List<S2399> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S2399> retorno = new ArrayList<>();
            int indice, subIndice;
            String sql = " Select (select max(sucesso) from  ( "
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "                                From XmleSocial z "
                    + "                             where z.Identificador = Funcion.CPF "
                    + "                             and z.evento = 'S-2399' "
                    + "                             and z.CodFil = ? "
                    + "                             and z.Compet = ? "
                    + "                             and z.Ambiente = ? "
                    + "                             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + "                     union "
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "                             From XmleSocial z  "
                    + "                             where z.Identificador = Funcion.CPF "
                    + "                             and z.evento = 'S-2399' "
                    + "                             and z.CodFil = ? "
                    + "                             and z.Compet = ? "
                    + "                             and z.Ambiente = ? "
                    + "                             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + "                     union "
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "                             From XmleSocial z  "
                    + "                             where z.Identificador = Funcion.CPF "
                    + "                             and z.evento = 'S-2399' "
                    + "                             and z.CodFil = ? "
                    + "                             and z.Compet = ? "
                    + "                             and z.Ambiente = ? "
                    + "                             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso , "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, "
                    + " substring(Filiais.CNPJ,1,8) ideEmpregador_nrInsc, "
                    + " FPRescisoes.Matr, "
                    + " FPRescisoes.CodMovFP ideEvento_compet, "
                    + " Funcion.CPF ideTrabSemVinculo_cpfTrab, "
                    + " Funcion.PIS ideTrabSemVinculo_nisTrab, "
                    + " Funcion.Matr ideTrabSemVinculo_matricula, "
                    + " Case when Funcion.vinculo = 'D' then '721' "
                    + "      when Funcion.Vinculo = 'S' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + " end ideTrabSemVinculo_codCateg, "
                    + " FPRescisoes.DtDemissao infoTSVTermino_dtTerm, "
                    + " '99' infoTSVTermino_mtvDesligTSV, "
                    + " 'RES'+Convert(varchar,FPRescisoes.CodMovFP) dmDev_ideDmDev, "
                    + " 2 infoSimples_indSimples, "
                    + " Case when FPRescisoes.Tipo = 2 then 'S' else 'N' end infoDeslig_indPagtoAPI, "
                    + " FPRescisoes.DtAvisoFim infoDeslig_dtProjFimAPI, "
                    + " (Select  "
                    + "  Case when COUNT(*) > 0 then '2' else '0' end  "
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%PENSAO ALIMENTICIA%') infoDeslig_pensAlim, "
                    + " (Select  "
                    + "  Isnull(Sum(ValorCalc),0) "
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%PENSAO ALIMENTICIA%') infoDeslig_vrAlim, "
                    + " Case when FPMensal.Proventos > 0 then Round(((Select  "
                    + "  Isnull(Sum(ValorCalc),0) "
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%PENSAO ALIMENTICIA%')/FPMensal.Proventos)*100,2) else 0 end infoDeslig_percAliment, "
                    + " (Select  "
                    + "  Case when COUNT(*) > 0 and FPRescisoes.Tipo = 2 then '2' " //Cumprimento Parcial Aviso. 
                    + "       when COUNT(*) = 0 and FPRescisoes.Tipo = 2 then '0' " //Cumprimento Total Aviso. 
                    + "  else '4' end " //Aviso indenizado ou não exigivel. 
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%AVISO PREVIO INDENIZADO%') infoDeslig_indCumprParc, "
                    + " Case when Funcion.TrabIntermitente = 'S' then ( "
                    + " Select Count(Distinct CtrOperv.Data) Qtde from CtrOperv  "
                    + " where CtrOperv.FuncSubs = FPRescisoes.Matr "
                    + "  and CtrOperv.Data between FPPeriodos.DtInicioP and  FPPeriodos.DtFinalP "
                    + "  and CtrOperv.  Flag_Excl <> '*' "
                    + " ) else 0 end infoDeslig_qtdDiasInterm, "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEstabLot_tpInsc, "
                    + " Filiais.CNPJ ideEstabLot_nrInsc, "
                    + " (Filiais.CodFil*10000)+Filiais.CodFil ideEstabLot_codLotacao, "
                    + " Substring(Funcion.OBS,1,35) infoDeslig_nrCertObito, "
                    + " (Select Isnull(Max(z.CNPJ),0) "
                    + "  from Funcion x"
                    + "  Left join Filiais z  on z.CodFil = x.CodFil "
                    + "  Where x.CPF = Funcion.CPF "
                    + "    and x.Situacao = 'A') sucessaoVinc_cnpjSucessora,  "
                    + " FPRescisoes.OBS sucessaoVinc_cnpjSucessora_BD_Segregados, Upper(Filiais.RazaoSocial) RazaoSocial "
                    + " from FPRescisoes "
                    + " Left join FPMensal  on FPMensal.CodMovFP = FPRescisoes.CodMovFP "
                    + "                    and FPMensal.TipoFP = 'RES' "
                    + "                    and FPMensal.Matr = FPRescisoes.Matr "
                    + " Left  join Filiais  on Filiais.CodFil = FPRescisoes.CodFil "
                    + " Inner Join Funcion  on FPRescisoes.Matr = Funcion.Matr "
                    + " Left  join FPPeriodos  on FPPeriodos.CodMovFP = FPRescisoes.CodMovFP "
                    + " Where FPRescisoes.CodFil = ? "
                    + "   and FPRescisoes.CodMovFP = ? "
                    + "   and Funcion.Vinculo in ('D','E','S','A') "
                    + "   and FPRescisoes.Matr not in (Select Matr From FPRescisoes z where z.Matr = FPMensal.Matr and z.CodMovFP < FPMensal.CodMovFP) "; //Inclusão tratamento rescisões complementares.            
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet.substring(2, 4) + compet.substring(5));
            consulta.select();
            S2399 s2399;
            while (consulta.Proximo()) {
                s2399 = new S2399();

                s2399 = new S2399();
                s2399.setIdeEvento_indRetif("1");
                s2399.setIdeEvento_procEmi("1");
                s2399.setIdeEvento_verProc("Satellite eSocial");
                s2399.setSucesso(consulta.getInt("sucesso"));

                s2399.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2399.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2399.setIdeTrabSemVinculo_cpfTrab(consulta.getString("ideTrabSemVinculo_cpfTrab"));
                s2399.setIdeTrabSemVinculo_nisTrab(consulta.getString("ideTrabSemVinculo_nisTrab"));
                s2399.setIdeTrabSemVinculo_matricula(consulta.getString("ideTrabSemVinculo_matricula"));
                s2399.setIdeTrabSemVinculo_codCateg(consulta.getString("ideTrabSemVinculo_codCateg"));
                s2399.setInfoTSVTermino_dtTerm(consulta.getString("infoTSVTermino_dtTerm"));
                s2399.setInfoTSVTermino_mtvDesligTSV(consulta.getString("infoTSVTermino_mtvDesligTSV"));

                s2399.setDmDev_ideDmDev(consulta.getString("dmDev_ideDmDev"));
                s2399.setIdeEstabLot_tpInsc(consulta.getString("ideEstabLot_tpInsc"));
                s2399.setIdeEstabLot_nrInsc(consulta.getString("ideEstabLot_nrInsc"));
                s2399.setIdeEstabLot_codLotacao(consulta.getString("ideEstabLot_codLotacao"));
                s2399.setInfoSimples_indSimples(consulta.getString("infoSimples_indSimples"));
                s2399.setIdeEvento_compet(consulta.getString("ideEvento_compet"));

                s2399.setInfoPerApur_ideEstabLot(new InfoPerApur());
                s2399.getInfoPerApur_ideEstabLot().setIdeEstabLot_detVerbas(new ArrayList<>());
                s2399.getInfoPerApur_ideEstabLot().setInfoSaudeColet_detOper(new ArrayList<>());

                retorno.add(s2399);
            }

            sql = "Select FpLancamentos.Matr ideTrabSemVinculo_matricula, "
                    + " Fornec.CNPJ detOper_cnpjOper, Fornec.OBS detOper_regANS, Sum(ValorCalc) detOper_vrPgTit "
                    + " from FpLancamentos "
                    + " inner join Verbas  on FpLancamentos.Verba = Verbas.Verba "
                    + " inner join Fornec  on Verbas.CodForn = Fornec.Codigo "
                    + " left join Funcion  on Funcion.Matr = FPLancamentos.Matr "
                    + " Where CodMovFP = ? "
                    + "       and TipoFP = 'RES' "
                    + "       and Verbas.PlanoSaude = 'S' "
                    + "       and FPLancamentos.ValorCalc > 0 "
                    + "      and Funcion.Vinculo in ('D','E','S','A') "
                    + " Group by Fornec.CNPJ, Fornec.OBS, FpLancamentos.Matr ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet.substring(2, 4) + compet.substring(5));
            consulta.select();
            S2399.DetOper dtOper;
            while (consulta.Proximo()) {
                s2399 = new S2399();
                dtOper = new DetOper();
                dtOper.setDetOper_cnpjOper(consulta.getString("detOper_cnpjOper"));
                dtOper.setDetOper_regANS(consulta.getString("detOper_regANS"));
                dtOper.setDetOper_vrPgTit(consulta.getString("detOper_vrPgTit"));
                dtOper.setDetOper_detPlano(new ArrayList<>());
                s2399.setIdeTrabSemVinculo_matricula(consulta.getString("ideTrabSemVinculo_matricula"));

                indice = retorno.indexOf(s2399);
                if (indice >= 0) {
                    retorno.get(indice).getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper().add(dtOper);
                }
            }

            sql = "Select F5_Dep.Matr ideTrabSemVinculo_matricula,\n"
                    + "Case when F5_Dep.Tipo = 'C' then '01' "
                    +//Conjugue\n +
                    "     when F5_Dep.Tipo = 'F' then '03' "
                    +//Filho\n +
                    "	when F5_Dep.Tipo = 'M' then '09' "
                    +//Mae\n +
                    "	when F5_Dep.Tipo = 'P' then '09' "
                    +//Pai\n +
                    "	when F5_Dep.Tipo = 'E' then '03' "
                    +//Enteado\n +
                    "	when F5_Dep.Tipo = 'O' then '99' "
                    +//Agregados/Outros\n +
                    "	end detPlano_tpDep,\n"
                    + "F5_Dep.CPF detPlano_cpfDep,\n"
                    + "F5_Dep.Nome detPlano_nmDep,\n"
                    + "F5_Dep.Dt_Nasc detPlano_dtNascto,\n"
                    + "F5_Dep.ValorPS+F5_Dep.ValorPO detPlano_vlrPgDep,\n"
                    + "Fornec.OBS detOper_regANS\n"
                    + "from F5_Dep\n"
                    + "inner join Verbas  on F5_Dep.VerbaPS = Verbas.Verba\n"
                    + "inner join Fornec  on Verbas.CodForn = Fornec.Codigo\n"
                    + "Where F5_Dep.Tipo <> 'T'\n"
                    + "  and (F5_Dep.DepPS = 'S')\n"
                    + "union\n"
                    + "Select F5_Dep.Matr IdeVinculo_matricula,\n"
                    + "Case when F5_Dep.Tipo = 'C' then '01' "
                    +//Conjugue\n +
                    "     when F5_Dep.Tipo = 'F' then '03' "
                    +//Filho\n +
                    "	when F5_Dep.Tipo = 'M' then '09' "
                    +//Mae\n +
                    "	when F5_Dep.Tipo = 'P' then '09' "
                    +//Pai\n +
                    "	when F5_Dep.Tipo = 'E' then '03' "
                    +//Enteado\n +
                    "	when F5_Dep.Tipo = 'O' then '99' "
                    +//Agregados/Outros\n +
                    "	end detPlano_tpDep,\n"
                    + "F5_Dep.CPF detPlano_cpfDep,\n"
                    + "F5_Dep.Nome detPlano_nmDep,\n"
                    + "F5_Dep.Dt_Nasc detPlano_dtNascto,\n"
                    + "F5_Dep.ValorPS+F5_Dep.ValorPO detPlano_vlrPgDep,\n"
                    + "Fornec.OBS detOper_regANS\n"
                    + "from F5_Dep\n"
                    + "inner join Verbas  on F5_Dep.VerbaPO = Verbas.Verba\n"
                    + "inner join Fornec  on Verbas.CodForn = Fornec.Codigo\n"
                    + "left join Funcion  on Funcion.Matr = F5_Dep.Matr "
                    + "Where F5_Dep.Tipo <> 'T'\n"
                    + "      and Funcion.Vinculo in ('D','E','S','A') "
                    + "  and (F5_Dep.DepPO = 'S')";
            consulta = new Consulta(sql, persistencia);
            consulta.select();
            S2399.DetPlano detPlano;
            while (consulta.Proximo()) {
                s2399 = new S2399();
                detPlano = new DetPlano();
                dtOper = new DetOper();
                detPlano.setDetPlano_tpDep(consulta.getString("detPlano_tpDep"));
                detPlano.setDetPlano_cpfDep(consulta.getString("detPlano_cpfDep"));
                detPlano.setDetPlano_nmDep(consulta.getString("detPlano_nmDep"));
                detPlano.setDetPlano_dtNascto(consulta.getString("detPlano_dtNascto"));
                detPlano.setDetPlano_vlrPgDep(consulta.getString("detPlano_vlrPgDep"));

                s2399.setIdeTrabSemVinculo_matricula(consulta.getString("ideTrabSemVinculo_matricula"));
                indice = retorno.indexOf(s2399);
                if (indice >= 0) {
                    dtOper.setDetOper_regANS(consulta.getString("detOper_regANS"));
                    subIndice = retorno.get(indice).getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper().indexOf(dtOper);
                    if (subIndice > 0) {
                        retorno.get(indice).getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper().get(subIndice).getDetOper_detPlano().add(detPlano);
                    }
                }
            }

            sql = "Select FpLancamentos.Matr ideTrabSemVinculo_matricula, FPLancamentos.Verba detVerbas_codRubr, FPLancamentos.Verba detVerbas_ideTabRubr, "
                    + " Case when FPLancamentos.Valor = 0 then 1 else FPLancamentos.Valor end detVerbas_qtdRubr, "
                    + " Round(FPLancamentos.ValorCalc/Case when FPLancamentos.Valor = 0 then 1 else FPLancamentos.Valor end,2) detVerbas_vrUnit, "
                    + " FPLancamentos.ValorCalc detVerbas_vrRubr "
                    + " from FpLancamentos "
                    + " Left join Verbas  on Verbas.Verba = FPLancamentos.Verba "
                    + " Left join Funcion  on Funcion.Matr = FPLancamentos.Matr "
                    + " Where CodMovFP = ? "
                    + "   and TipoFP = 'RES' "
                    + "   and FpLancamentos.ValorCalc > 0 "
                    + "      and Funcion.Vinculo in ('D','E','S','A') "
                    + "   and FPLancamentos.Flag_Excl <> '*' "
                    + " and Verbas.Formula not in('0221','0222','0223','0225','0226','0003','0013','0023','0033')";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet.substring(2, 4) + compet.substring(5));
            consulta.select();
            S2399.DetVerbas detVerbas;
            while (consulta.Proximo()) {
                s2399 = new S2399();
                detVerbas = new DetVerbas();
                detVerbas.setDetVerbas_codRubr(consulta.getString("detVerbas_codRubr"));
                detVerbas.setDetVerbas_ideTabRubr(consulta.getString("detVerbas_ideTabRubr"));
                detVerbas.setDetVerbas_qtdRubr(consulta.getString("detVerbas_qtdRubr"));
                detVerbas.setDetVerbas_vrUnit(consulta.getString("detVerbas_vrUnit"));
                detVerbas.setDetVerbas_vrRubr(consulta.getString("detVerbas_vrRubr"));

                s2399.setIdeTrabSemVinculo_matricula(consulta.getString("ideTrabSemVinculo_matricula"));
                indice = retorno.indexOf(s2399);
                if (indice >= 0) {
                    retorno.get(indice).getInfoPerApur_ideEstabLot().getIdeEstabLot_detVerbas().add(detVerbas);
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2399Dao.get - " + e.getMessage() + "\r\n"
                    + "");
        }
    }
}

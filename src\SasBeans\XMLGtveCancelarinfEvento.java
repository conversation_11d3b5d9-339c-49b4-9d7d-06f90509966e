/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class XMLGtveCancelarinfEvento {

    public String Id;
    public String cOrgao;
    public String tpAmb;
    public String CNPJ;
    public String chCTe;
    public String dhEvento;
    public String tpEvento;
    public String nSeqEvento;
    public XMLGtveCancelarinfEventoDet detEvento;

    public XMLGtveCancelarinfEvento() {
        detEvento = new XMLGtveCancelarinfEventoDet();
    }

    public void setId(String Id) {
        this.Id = Id;
    }
    
    public void setcOrgao(String cOrgao) {
        this.cOrgao = cOrgao;
    }

    public void setTpAmb(String tpAmb) {
        this.tpAmb = tpAmb;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public void setChCTe(String chCTe) {
        this.chCTe = chCTe;
    }
    
    public void setDhEvento(String dhEvento) {
        this.dhEvento = dhEvento;
    }

    public void setTpEvento(String tpEvento) {
        this.tpEvento = tpEvento;
    }

    public void setnSeqEvento(String nSeqEvento) {
        this.nSeqEvento = nSeqEvento;
    }

    public void setDetEvento(XMLGtveCancelarinfEventoDet detEvento) {
        this.detEvento = detEvento;
    }
    
    
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasdaos.Comunicacoes;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ComunicacoesDao {
    
    public void marcarMensagemEnviada(String numero, Persistencia persistencia) throws Exception{
        try{
            String sql = " UPDATE \n"
                    + "     Comunicacoes \n"
                    + " SET \n"
                    + "     Situacao = 1 \n"
                    + " WHERE \n"
                    + "     Numero = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(numero);
            consulta.update();
            consulta.Close();
        } catch (Exception e){
            throw new Exception("ComunicacoesDao.marcarMensagemEnviada - "+e.getMessage()+"\r\n"
                    + " UPDATE \n"
                    + "     Comunicacoes \n"
                    + " SET \n"
                    + "     Situacao = 1 \n"
                    + " WHERE \n"
                    + "     Numero = "+numero);
        }
    }

    public List<Comunicacoes> listarComunicacoes(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT \n"
                    + "     *, CONVERT(Varchar, DtCadastro, 112) DtCadastroF, Pessoa.Nome Remetente \n"
                    + " FROM \n"
                    + "     Comunicacoes \n"
                    + " LEFT JOIN \n"
                    + "     Pessoa ON Pessoa.Codigo = Comunicacoes.CodRemet \n"
                    + " WHERE \n"
                    + "     CodDest = ? AND Comunicacoes.Situacao = 0 AND Flag_Excl <> '*' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();

            List<Comunicacoes> retorno = new ArrayList<>();
            Comunicacoes comunicacoes;
            while (consulta.Proximo()) {
                comunicacoes = new Comunicacoes();
                comunicacoes.setNumero(consulta.getString("Numero"));
                comunicacoes.setCodFil(consulta.getString("CodFil"));
                comunicacoes.setDtCadastro(consulta.getString("DtCadastroF"));
                comunicacoes.setHrCadastro(consulta.getString("HrCadastro"));
                comunicacoes.setDtLimite(consulta.getString("DtLimite"));
                comunicacoes.setSLA_Dias(consulta.getString("SLA_Dias"));
                comunicacoes.setCodRemet(consulta.getString("CodRemet"));
                comunicacoes.setTipo(consulta.getString("Tipo"));
                comunicacoes.setAssunto(consulta.getString("Assunto"));
                comunicacoes.setDetalhes(consulta.getString("Detalhes"));
                comunicacoes.setSecaoDest(consulta.getString("SecaoDest"));
                comunicacoes.setCodDest(consulta.getString("CodDest"));
                comunicacoes.setParecer(consulta.getString("Parecer"));
                comunicacoes.setParecerTeor(consulta.getString("ParecerTeor"));
                comunicacoes.setSolucao(consulta.getString("Solucao"));
                comunicacoes.setSolucaoDet(consulta.getString("SolucaoDet"));
                comunicacoes.setSituacao(consulta.getString("Situacao"));
                comunicacoes.setDtFechamento(consulta.getString("DtFechamento"));
                comunicacoes.setHrFechamento(consulta.getString("HrFechamento"));
                comunicacoes.setCodContato(consulta.getString("CodContato"));
                comunicacoes.setOper_Excl(consulta.getString("Oper_Excl"));
                comunicacoes.setDt_Excl(consulta.getString("Dt_Excl"));
                comunicacoes.setHr_Excl(consulta.getString("Hr_Excl"));
                comunicacoes.setFlag_Excl(consulta.getString("Flag_Excl"));
                
                comunicacoes.setRemetente(consulta.getString("Remetente"));
                retorno.add(comunicacoes);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ComunicacoesDao.listarComunicacoes - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     *, CONVERT(Varchar, DtCadastro, 112) DtCadastroF, Pessoa.Nome Remetente \n"
                    + " FROM \n"
                    + "     Comunicacoes \n"
                    + " LEFT JOIN \n"
                    + "     Pessoa ON Pessoa.Codigo = Comunicacoes.CodRemet \n"
                    + " WHERE \n"
                    + "     CodDest = " + codPessoa + " AND Situacao = 0 AND Flag_Excl <> '*' ");
        }
    }
}

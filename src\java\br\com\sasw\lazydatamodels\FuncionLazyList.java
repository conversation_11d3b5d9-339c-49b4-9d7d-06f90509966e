/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Funcion.FuncionSatMobWeb;
import Dados.Persistencia;
import SasBeansCompostas.FuncionPstServ;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class FuncionLazyList extends LazyDataModel<FuncionPstServ> {

    private static final long serialVersionUID = 1L;
    private List<FuncionPstServ> funcions;
    private final FuncionSatMobWeb funcionsatmobweb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;
    private Map filters;

    public FuncionLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.funcionsatmobweb = new FuncionSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    public FuncionLazyList(Persistencia pst, BigDecimal codPessoa, Map filters) {
        this.funcionsatmobweb = new FuncionSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
        this.filters = filters;
    }

    @Override
    public List<FuncionPstServ> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {

            if (null == filters) {
                filters = this.filters;
            }

            this.funcions = this.funcionsatmobweb.ListagemPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.funcionsatmobweb.Contagem(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.funcions;
    }

    @Override
    public Object getRowKey(FuncionPstServ funcion) {
        return funcion.getFuncion().getMatr();
    }

    @Override
    public FuncionPstServ getRowData(String codigo) {
        for (FuncionPstServ funcion : this.funcions) {
            if (codigo.equals(funcion.getFuncion().getMatr().toPlainString())) {
                return funcion;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

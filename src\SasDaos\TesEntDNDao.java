package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesEntDN;
import SasBeans.TesEntrada;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesEntDNDao {

    /**
     * Lista as composicoes a partir de uma lista de TesEntrada
     *
     * @param tesEntradas
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesEntDN> listaComposicoes(List<TesEntrada> tesEntradas, String serie, Persistencia persistencia) throws Exception {
        try {
            List<TesEntDN> retorno = new ArrayList<>();
            String sql = " SELECT *, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC "
                    + " FROM TesEntDN "
                    + " WHERE guia in (";
            for (TesEntrada tesEntrada : tesEntradas) {
                sql += " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + " ) AND serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            for (TesEntrada tesEntrada : tesEntradas) {
                consulta.setBigDecimal(tesEntrada.getGuia());
            }
            consulta.setString(serie);
            consulta.select();
            TesEntDN tesEntDN;
            while (consulta.Proximo()) {
                tesEntDN = new TesEntDN();
                tesEntDN.setGuia(consulta.getString("Guia"));
                tesEntDN.setSerie(consulta.getString("Serie"));
                tesEntDN.setCodigo(consulta.getString("Codigo"));
                tesEntDN.setDocto(consulta.getString("Docto"));
                tesEntDN.setQtde(consulta.getString("Qtde"));
                tesEntDN.setValor(consulta.getString("Valor"));
                tesEntDN.setOperador(consulta.getString("Operador"));
                tesEntDN.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesEntDN.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesEntDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list composições para guia - " + e.getMessage());
        }
    }

    /**
     * Lista as composicoes de guias de um cliente
     *
     * @param codCli
     * @param codFil
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesEntDN> listaComposicoes(String codCli, String codFil, String serie, Persistencia persistencia) throws Exception {
        try {
            List<TesEntDN> retorno = new ArrayList<>();
            String sql = " SELECT *, CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC "
                    + " FROM TesEntDN "
                    + " WHERE guia in (SELECT Guia \n"
                    + "                 FROM TesEntrada \n"
                    + "                 WHERE CodCli1 = ? AND CodFil = ? AND Serie = TesEntDN.Serie)"
                    + " AND serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli);
            consulta.setString(codFil);
            consulta.setString(serie);
            consulta.select();
            TesEntDN tesEntDN;
            while (consulta.Proximo()) {
                tesEntDN = new TesEntDN();
                tesEntDN.setGuia(consulta.getString("Guia"));
                tesEntDN.setSerie(consulta.getString("Serie"));
                tesEntDN.setCodigo(consulta.getString("Codigo"));
                tesEntDN.setDocto(consulta.getString("Docto"));
                tesEntDN.setQtde(consulta.getString("Qtde"));
                tesEntDN.setValor(consulta.getString("Valor"));
                tesEntDN.setOperador(consulta.getString("Operador"));
                tesEntDN.setDt_Alter(consulta.getString("Dt_AlterC"));
                tesEntDN.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(tesEntDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list composições para guia - " + e.getMessage());
        }
    }

    public List<TesEntDN> listaTodasComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesEntDN> retorno = new ArrayList<>();
        try {
            String sql = " SELECT codigo, sum(qtde) qtde "
                    + " FROM TesEntDN "
                    + " WHERE guia = ? AND serie = ? "
                    + " group by codigo ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            TesEntDN tesEntDN;
            while (consulta.Proximo()) {
                tesEntDN = new TesEntDN();
                tesEntDN.setCodigo(consulta.getString("codigo"));
                tesEntDN.setQtde(consulta.getString("qtde"));
                retorno.add(tesEntDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list composições para guia - " + e.getMessage());
        }
    }

    /**
     * Recupera lista de composicoess
     *
     * @param guia numero da guia
     * @param serie serie da guia
     * @param docto numero do documento
     * @param persistencia conexao com o banco de dados
     * @return lista de composicoes
     * @throws Exception
     */
    public List<TesEntDN> ListaTesEntDN(BigDecimal guia, String serie, String docto, Persistencia persistencia) throws Exception {
        List<TesEntDN> tesEntDNs = new ArrayList<>();
        try {
            String sql = "SELECT * FROM tesentdn WHERE guia = ? AND serie = ? AND docto = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();

            TesEntDN tesEntDN = null;
            while (consulta.Proximo()) {
                tesEntDN = new TesEntDN();
                tesEntDN.setGuia(consulta.getString("guia"));
                tesEntDN.setSerie(consulta.getString("serie"));
                tesEntDN.setCodigo(consulta.getString("codigo"));
                tesEntDN.setDocto(consulta.getString("docto"));
                tesEntDN.setQtde(consulta.getString("qtde"));
                tesEntDN.setIDK7(consulta.getString("idk7"));
                tesEntDN.setTipoCed(consulta.getString("tipoced"));
                tesEntDN.setOperador(consulta.getString("operador"));
                tesEntDN.setValor(consulta.getString("valor"));
                tesEntDN.setDt_Alter(consulta.getString("dt_alter"));
                tesEntDNs.add(tesEntDN);
            }
        } catch (Exception e) {
            throw new Exception("Ocorreu um erro: " + e.getMessage());
        }
        return tesEntDNs;
    }

    /**
     * Troca a serie da guia em TesEntrada
     *
     * @param persistencia - conexão ao banco de dados
     * @param tesentdn - Guia em tesentrada Obrigatório - guia, série
     * @param novaserie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, TesEntDN tesentdn, String novaserie) throws Exception {
        try {
            String sql;
            sql = "update TesEntDN set Serie = ?"
                    + " where Guia = ?"
                    + " and Serie =  ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(novaserie);
            consulta.setString(tesentdn.getGuia().toPlainString());
            consulta.setString(tesentdn.getSerie());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao trocar serie da guia em TesEntDN - " + e.getMessage());
        }
    }

    /**
     * Lista composicoes
     *
     * @param guia
     * @param serie
     * @param docto - numero da sangria
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesEntDN> ListaComposicao(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        List<TesEntDN> composicaoDN = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesEntDN"
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.setString(docto);
            consult.select();
            TesEntDN tesentdn;
            while (consult.Proximo()) {
                tesentdn = new TesEntDN();
                tesentdn.setCodigo(consult.getString("codigo"));
                tesentdn.setQtde(consult.getString("qtde"));
                composicaoDN.add(tesentdn);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list tesentmd - " + e.getMessage());
        }
        return composicaoDN;
    }

    /**
     * Exclui a composição da sangria da tabela TesEntDN
     *
     * @param guia Valor da guia
     * @param serie Valor da série
     * @param docto Número da sangria
     * @param codigo Código da nota
     * @param persistencia Conexão com o banco
     * @throws Exception
     */
    public void ExcluirComposicao(String guia, String serie, String docto, String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "delete from tesentdn "
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and codigo = ?"
                    + " and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codigo);
            consulta.setString(docto);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao excluir de tesentdn - " + e.getMessage());
        }
    }

    /**
     * Insere a composição na tabela
     *
     * @param guia Valor da guia
     * @param serie Valor da série
     * @param docto Número da sangria
     * @param codigo Código da nota
     * @param qtde Quantidade de notas
     * @param valor Valores das notas
     * @param operador Operador
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void InserirComposicao(String guia, String serie, String docto, String codigo, BigDecimal qtde,
            BigDecimal valor, String operador, Persistencia persistencia) throws Exception {
        try {
            String sql;
            if (codigo.equals("201")) {
                codigo = "200";
            }
            //if (!qtde.equals(0) && !valor.equals(0) && (valor != null) && !valor.equals(null)) {
            if((qtde != null) && (valor != null) && (qtde.compareTo(BigDecimal.ZERO) != 0) && (valor.compareTo(BigDecimal.ZERO) != 0)){
                sql = "insert into tesentdn (guia, serie, codigo, docto, qtde, valor, operador, dt_alter, hr_alter)"
                        + " values (?,?,?,?,?,?,?,?,?)";
                Consulta consulta = new Consulta(sql, persistencia);
                consulta.setString(guia);
                consulta.setString(serie);
                consulta.setString(codigo);
                consulta.setString(docto);
                consulta.setBigDecimal(qtde);
                consulta.setBigDecimal(valor);
                consulta.setString(operador);
                consulta.setString(DataAtual.getDataAtual("SQL"));
                consulta.setString(DataAtual.getDataAtual("HORA"));
                consulta.insert();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("Falha ao inserir composicao em tesentdn - " + e.getMessage());
        }
    }

    /**
     * Lista composicoes
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesEntDN> ListaComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesEntDN> composicaoDN = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesEntDN"
                    + " Where Guia = ?"
                    + "  and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            TesEntDN tesentdn;
            while (consult.Proximo()) {
                tesentdn = new TesEntDN();
                tesentdn.setCodigo(consult.getString("codigo"));
                tesentdn.setQtde(consult.getString("qtde"));
                composicaoDN.add(tesentdn);
            }
            consult.Close();
            return composicaoDN;
        } catch (Exception e) {
            throw new Exception("Failed to list tesentmd - " + e.getMessage() + "\r\n"
                    + "elect codigo, qtde from TesEntDN"
                    + " Where Guia = " + guia
                    + "  and Serie = " + serie);
        }
    }
}

/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Filiais.FiliaisSatMobWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class FiliaisLazyList extends LazyDataModel<Filiais> {

    private static final long serialVersionUID = 1L;
    private List<Filiais> filiais;
    private final FiliaisSatMobWeb filiaissatmobweb;
    private Persistencia persistencia;

    public FiliaisLazyList(Persistencia pst) {
        this.filiaissatmobweb = new FiliaisSatMobWeb();
        this.persistencia = pst;
    }

    @Override
    public List<Filiais> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.filiais = this.filiaissatmobweb.ListagemPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.filiaissatmobweb.Contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.filiais;
    }

    @Override
    public Object getRowKey(Filiais filial) {
        return filial.getCodFil();
    }

    @Override
    public Filiais getRowData(String codFil) {
        for (Filiais filial : this.filiais) {
            if (codFil.equals(filial.getCodFil().toPlainString())) {
                return filial;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

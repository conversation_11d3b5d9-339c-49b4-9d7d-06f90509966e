<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/recursoshumanos/inspecoes.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiMob}" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                [id*="formBoletimTrabalho"] .ui-state-focus{
                    box-shadow: none !important;
                    outline: none !important;
                }

                th[id*="formListarInspecoes"] {
                    padding-top: 4px !important;
                }

                .FundoPagina{
                    height: calc(100% - 8px) !important
                }

                [id*="exibicaoPstInspecao"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #EEE !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="exibicaoPstInspecao"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #FFF !important;
                }

                [id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
                    background-color: #EEE !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
                    background-color: #FFF !important;
                }

                @media only screen and (max-width: 764px) and (min-width: 10px) {                    
                    #PaiMap, #map{
                        height: 250px !important;
                    }

                    #PaidivItensHistorico{
                        height: calc(100vh - 600px) !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {   
                    [id*="gmap"]{
                        height: 295px !important;
                    }
                }
                           
                .DataGrid[id*="tabelaInspecoesRelatorio"] [role="columnheader"] > span {
                    top: -1px !important;
                    position: relative !important;
                }
                
                [id*="tabelaInspecoesRelatorio"].ui-datatable{
                    max-height: 50vh !important;
                    overflow-y: auto !important;
                    overflow-x: hidden !important;
                }
            </style>
        </h:head>
        <h:body id="h" style="max-height: 100% !important; height: 100% !important;">
            <f:metadata>
                <f:viewAction action="#{inspecoes.Persistencia(login.pp)}" />
            </f:metadata>
            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.PstInspecao}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{inspecoes.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{inspecoes.nomeFilial}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{inspecoes.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{inspecoes.filiais.bairro}, #{rotasescala.filiais.cidade}/#{rotasescala.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{inspecoes.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{inspecoes.dataTela}"
                                                locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{inspecoes.selecionarData}" update="main cabecalho msgs" />
                                    </p:calendar>

                                    <p:commandLink action="#{inspecoes.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>
                <h:form id="main">
                    <p:hotkey bind="p" actionListener="#{inspecoes.prepararFiltro}" update="formPesquisar"/> 
                    <p:hotkey bind="e" actionListener="#{inspecoes.abrirPstInspecao}" update="main exibicaoPstInspecao msgs"/> 
                    <p:hotkey bind="a" actionListener="#{inspecoes.listarInspecoes}" update="formListarInspecoes"/> 

                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable id="tabela" value="#{inspecoes.allPstInspecoes}" paginator="true" rows="50" lazy="true"
                                     rowsPerPageTemplate="5,10,15,20,25,50"
                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.PstInspecao}"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                     var="lista" rowKey="#{lista.chavePrimaria}"
                                     selectionMode="single"
                                     styleClass="tabela DataGrid"
                                     selection="#{inspecoes.pstInspecaoSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                     scrollable="true" scrollWidth="100%">
                            <p:ajax listener="#{inspecoes.rowDblselect}" event="rowDblselect" update="main exibicaoPstInspecao msgs"/>
                            <p:column headerText="#{localemsgs.Inspecao}" style="min-width: 150px !important">
                                <h:outputText value="#{lista.pergunta}" style="min-width: 150px !important"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Local}" style="min-width: 150px !important">
                                <h:outputText value="#{lista.local}" style="min-width: 150px !important"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Inspecionado}" style="min-width: 150px !important">
                                <h:outputText value="#{lista.resposta}" style="min-width: 150px !important"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Inspetor}" style="min-width: 150px !important">
                                <h:outputText value="#{lista.inspetor}" style="min-width: 150px !important"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" styleClass="text-center" style="min-width: 100px !important; width: 100px !important; max-width: 100px !important">
                                <h:outputText value="#{lista.data}" class="text-center" converter="conversorData" style="min-width: 100px !important; width: 100px !important; max-width: 100px !important"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hora}" styleClass="text-center" style="min-width: 100px !important; width: 100px !important; max-width: 100px !important">
                                <h:outputText value="#{lista.hr_Alter}" class="text-center" converter="conversorHora" style="min-width: 100px !important; width: 100px !important; max-width: 100px !important"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>

                    <p:panel id="botoes"
                             style="position: fixed; z-index: 1; right: 1px; bottom: 0px !important; background: transparent; height: 280px !important;">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Cadastrar}" actionListener="#{inspecoes.listarInspecoes}"
                                           update="formListarInspecoes">
                                <p:graphicImage url="../assets/img/icone_redondo_configurar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}"
                                           actionListener="#{inspecoes.abrirPstInspecao}"
                                           update="main exibicaoPstInspecao msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Filtrar}"
                                           actionListener="#{inspecoes.prepararFiltro}"
                                           update="formPesquisar">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Exportar}"
                                           actionListener="#{inspecoes.listarInspecaoRelatorioDinamico()}"
                                           update="formExportar">
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <h:form id="formBoletimTrabalho" class="form-inline">
                    <p:dialog widgetVar="dlgBoletimTrabalho"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important; width: calc(100vh - 150px) !important; min-height: calc(100vh - 150px) !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cadastros.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ResumoTrabalho}" style="color:#022a48"/>
                        </f:facet>      

                        <p:panel id="pnlBoletim" style="background-color: #FFF; max-width: 100% !important; display: block !important; margin-top:-10px !important; z-index:999 !important; min-height:calc(100% - 20px) !important; height:calc(100% - 20px) !important; max-height:calc(100% - 20px) !important" class="cadastrar">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="height: 80px; background-color: #EEE; border: thin solid #CCC; margin-top: 10px; padding: 2px 4px 2px 4px !important; border-radius: 4px; box-shadow: 2px 2px 4px #DDD;">
                                <div class="col-md-9 col-sm-9 col-xs-9" style="padding: 6px 3px 6px 6px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF">#{localemsgs.Operador}</label>
                                    <p:selectOneMenu id="operador" value="#{inspecoes.pessoaSelecionada}" converter="omnifaces.SelectItemsConverter"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%; outline: none !important" >
                                        <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue=""></f:selectItem>
                                        <f:selectItems value="#{inspecoes.pessoas}" var="operador" itemValue="#{operador}"
                                                       itemLabel="#{operador.nome}"/>
                                        <p:ajax event="itemSelect" listener="#{inspecoes.selecionarPessoa}" update="msgs pnlBoletim"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-3" style="padding: 6px 6px 6px 3px !important">
                                    <label style="font-weight: bold; text-shadow: 1px 1px #FFF; width: 100% !Important; text-align: center !important; color: orangered; white-space: nowrap; overflow: hidden; text-overflow: ellips; height: 15px !important">#{localemsgs.LocaisVisitados}</label>
                                    <label id="lblQtdeVisitas" style="background-color: lightyellow; border: thin solid orangered; color: orangered; width: 100%; height: 34px !important; border-radius: 3px; text-align: left; font-weight: bold; font-size: 16pt !important; padding-left: 8px !Important; padding-top: 1px !important;text-align: center !important;"><i class="fa fa-cog fa-spin fa-fw"></i></label>
                                </div>
                            </div>

                            <div id="PaiMap" class="col-md-5 col-sm-5 col-xs-12" style="height: calc(100vh - 308px); border: thin solid #DDD; margin-top: 15px !important;  box-shadow: 2px 2px 4px #DDD; padding: 3px !important">
                                <div id="mapGoogle" style="min-width:100% !important;width:100% !important;max-width:100% !important;"><i class="fa fa-cog fa-spin fa-fw" style="position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size: 32pt; width: 45px; height:45px;"></i></div>
                            </div>

                            <div id="PaidivItensHistorico" class="col-md-7 col-sm-7 col-xs-12" style="padding:15px 0px 0px 10px !important;height: calc(100vh - 293px);">
                                <div id="divItensHistorico" class="col-md-12 col-sm-12 col-xs-12" style="height: 100% !important; overflow: auto !important;border: thin solid #DDD; padding: 3px !important;  box-shadow: 2px 2px 4px #DDD;"><i class="fa fa-cog fa-spin fa-fw" style="position: absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size: 32pt; width: 45px; height:45px;"></i></div>
                            </div> 
                            <script type="text/javascript">
                                // <![CDATA[
                                var map;
                                var ArrayBoletimTrabalho = new Array();

                                #{inspecoes.listaTrabalhos}

                                function inicioMapaServico() {
                                    let Altura = $('#mapGoogle').parent('div').height() - 0;

                                    $('#mapGoogle').css('min-height', Altura + 'px');

                                    map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                        zoom: 11,
                                        center: #{inspecoes.centro},
                                        gestureHandling: 'cooperative'
                                    });

                                #{inspecoes.markers}
                                }

                                function CarregarListaTrabalho(inQtdeVisitas) {
                                    let UltimoGrupo = '', HTML = '', Pergunta = '';

                                    for (I = 0; I < ArrayBoletimTrabalho.length; I++) {
                                        Pergunta = ArrayBoletimTrabalho[I].Pergunta.toString().indexOf('-') > -1 ? ArrayBoletimTrabalho[I].Pergunta.toString().split('-')[1] : ArrayBoletimTrabalho[I].Pergunta.toString();

                                        if (UltimoGrupo !== ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim())
                                            HTML += '<label style="font-size: 14pt; font-weight: 500; width: 100%; color: steelblue; padding: 8px 0px 5px 10px; background-color: #EEE; text-align: left; margin: 0px 0px 5px 0px !important; text-shadow: 1px 1px #FFF; border: thin solid #DDD"><i class="fa fa-check-square-o" aria-hidden="true" style="font-size: 12pt !important;"></i>&nbsp;&nbsp;' + ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim() + '</label>';

                                        HTML += '<label style="font-size: 10pt; color: #666; width: 100%; text-align: left; padding: 0px 0px 5px 0px; border-bottom: 1px dashed #DDD; margin: 0px !important;"><span style="float: left; width: 55%; text-align: right">' + Pergunta + ':</span><span style="float: right; font-weight: 600;width: 45%; text-align: left; padding-left: 8px; color: #303030 !important">' + ArrayBoletimTrabalho[I].Resposta + '</span></label>';

                                        UltimoGrupo = ArrayBoletimTrabalho[I].Pergunta.split('-')[0].trim();
                                    }

                                    $('#divItensHistorico').html(HTML);
                                    $('#lblQtdeVisitas').html(inQtdeVisitas);
                                }

                                $(document).ready(function () {
                                    inicioMapaServico();
                                });
                                // ]]>
                            </script>   
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="exibicaoPstInspecao">
                    <p:dialog widgetVar="dlgPstInspecao" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgPstInspecao"
                              style="height:98% !important; min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color: #FFF !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PstInspecao}" style="color:#022a48;" /> 
                        </f:facet>

                        <p:panel id="panelInspecoes" style="overflow: hidden !important; overflow-y: auto !important; max-width: 100% !important;padding:0px !important; height: calc(100vh - 175px); background-color: #FFF !important" styleClass="cadastrar">                                

                            <div id="pnlHTMLpai" class="col-md-7 col-sm-7 col-xs-12" style="overflow:hidden !important; padding:0px 8px 6px 4px !important; min-height: calc(100vh - 176px) !important;background-color: #FFF !important">
                                <p:panel id="pnlHTML" style="min-height:100% !important; height:100% !important; max-height:100% !important; background: #FFF !important; padding:0px 10px 0px 10px !important; border:thin solid #DDD !important; box-shadow: 2px 2px 3px #CCC; min-height:100% !important;">
                                    <div id="DadosHTML"  class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; margin: 0px !important">
                                        <h:outputText value="#{inspecoes.relatorio}" escape="false" />
                                    </div>
                                    <p:gmap id="gmap" center="#{inspecoes.centroMapa}" zoom="#{inspecoes.zoomMapa}" type="TERRAIN"
                                            style="margin-top:6px; height: calc(100vh - 460px); width:100% !important; border:thin solid #BBB;" model="#{inspecoes.mapa}" rendered="#{inspecoes.mapa ne null}"/>
                                </p:panel>
                            </div>


                            <div id="divConteudo" class="col-md-5 col-sm-5 col-xs-12" style=" padding:0px 8px 6px 4px !important; position:relative;">
                                <p:panel id="panelInspecoesDetalhes" style="height: calc(100vh - 183px); background-color: transparent; border: thin solid #DDD !important; position:relative; border-radius:5px !important; background-color:#FFF !important;box-shadow:2px 2px 3px #CCC !important; overflow: auto" styleClass="col-md-12">


                                    <ui:repeat value="#{inspecoes.pstInspecaoSelecionadaDetalhes}" var="pstInspecao">
                                        <p:panel style="margin-bottom:0px; padding: 0px !important;">                                                
                                            <label style="font-size: 14pt; font-weight: 500; width: 100%; color: steelblue; padding: 8px 0px 5px 10px; background-color: #EEE; text-align: left; margin: 0px 0px 5px 0px !important; text-shadow: 1px 1px #FFF; border: thin solid #DDD"><i class="fa fa-check-square-o" aria-hidden="true" style="font-size: 12pt !important;"></i>&nbsp;&nbsp;#{pstInspecao.pstInspecao.pergunta}</label>
                                            <label style="display: #{pstInspecao.fotos.size() eq 0 or (pstInspecao.pstInspecao.resposta ne null and pstInspecao.pstInspecao.resposta ne '') ? '': 'none'}; font-size: 10pt; color: #666; width: 100% !important; text-align: left; padding: 0px 0px 5px 0px; margin: 3px 0px 0px 0px !important;"><span style="float: left; font-weight: 500;width: 100%; text-align: left; padding-left: 8px; color: #303030 !important; text-transform: uppercase !important"><h:outputText escape="false" value="#{pstInspecao.pstInspecao.resposta}" /></span></label>


                                            <p:panel id="panelMedia" style="background-color: transparent; padding-right:0px !important; padding-bottom: 8px !important" rendered="#{pstInspecao.fotos.size() gt 0}">
                                                <ui:repeat value="#{pstInspecao.fotos}" var="pstInspecaoFotoArray">
                                                    <div class="col-md-3 col-sm-3 col-xs-4" style="padding: 3px !important; margin-bottom: 4px !important">
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px !important; border: thin solid #CCC;">
                                                            <img src="#{pstInspecaoFotoArray}" style="max-width: 100%;" onclick="AbrirFoto('#{pstInspecaoFotoArray}')" />
                                                        </div>
                                                    </div>
                                                </ui:repeat>
                                            </p:panel>

                                            <p:panel id="panelMediaVideo" style="background-color: transparent; border: 1px solid #cccccc !important;
                                                     border-radius: 3px; margin: 0 auto; text-align: center; margin-bottom: 5px;"
                                                     rendered="#{pstInspecao.videos.size() gt 0}">  
                                                <table style="width:100.0%" summary="sasw" cellspacing="0" cellpadding="0" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td style="padding:.75pt .75pt .75pt .75pt">
                                                                <p:commandButton icon=" ui-icon-triangle-1-w" style="width: 30px; float: left;"
                                                                                 action="#{pstInspecao.voltarVideo}" rendered="#{pstInspecao.videos.size() gt 1}"
                                                                                 update="@parent msgs">
                                                                </p:commandButton>
                                                            </td>
                                                            <td style="padding:.75pt .75pt .75pt .75pt; width: 100%">
                                                                <p:panel id="panelVideo" style="background: transparent">
                                                                    <p:media value="#{pstInspecao.video}" style="max-width: 100% !important; vertical-align: middle;">
                                                                        <f:param name="autoPlay" value="false" />
                                                                    </p:media>
                                                                </p:panel>
                                                            </td>
                                                            <td style="padding:.75pt .75pt .75pt .75pt">
                                                                <p:commandButton icon=" ui-icon-triangle-1-e" style="width: 30px;"
                                                                                 action="#{pstInspecao.avancarVideo}" rendered="#{pstInspecao.videos.size() gt 1}"
                                                                                 update="@parent msgs">
                                                                </p:commandButton>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </p:panel>

                                            <p:panel id="panelAudio" style="background-color: transparent; padding-right:0px !important; padding-bottom: 8px !important" rendered="#{pstInspecao.audios.size() gt 0}">
                                                <ui:repeat value="#{pstInspecao.audios}" var="pstInspecaoAudioArray">
                                                    <audio controls="controls">
                                                        <source src="#{pstInspecaoAudioArray}" type="audio/mp3" />
                                                        Seu browser não suporta áudio
                                                    </audio>
                                                </ui:repeat>
                                            </p:panel> 
                                        </p:panel>
                                    </ui:repeat>


                                </p:panel>
                            </div>    
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formListarInspecoes">
                    <p:dialog widgetVar="dlgListarInspecoes" id="dlgListarInspecoes" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop"
                              hideEffect="drop" closeOnEscape="false" class="dialogoGrande" style="padding-bottom: 0px !important">
                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Inspecoes}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="panelListaInspecoes" style="background-color: transparent; padding-top: 0px !important; padding-bottom: 0px !important" styleClass="cadastrar">
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:0px !important; padding-left: 0px !important; padding-right: 0px !important;">
                                <div class="col-md-11 col-sm-11 col-xs-11" style="width:calc(100% - 62px) !important; padding:0px !important; margin:0px !important; height:195px !important; overflow:hidden;">
                                    <p:dataTable id="tabelaInspecoes" value="#{inspecoes.inspecoes}"
                                                 var="inspecao" rowKey="#{inspecao.codigo}"
                                                 styleClass="tabela" scrollHeight="300"
                                                 resizableColumns="true" scrollable="true" selectionMode="single" scrollWidth="100%"
                                                 emptyMessage="#{localemsgs.SemRegistros}"
                                                 selection="#{inspecoes.inspecaoSelecionada}">
                                        <p:ajax event="rowDblselect" listener="#{inspecoes.dblSelectInspecao}" update="msgs formInspecaoItens:dlgInspecaoItens"/>
                                        <p:column headerText="#{localemsgs.Descricao}">
                                            <h:outputText value="#{inspecao.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" style="width: 75px" class="text-center">
                                            <h:outputText value="#{inspecao.dt_Alter}" converter="conversorData" class="text-center" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora}" style="width: 75px" class="text-center">
                                            <h:outputText value="#{inspecao.hr_Alter}" converter="conversorHora" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" style="width: 100px" class="text-center">
                                            <h:outputText value="#{inspecao.operador}" class="text-center"/>
                                        </p:column>
                                    </p:dataTable>
                                </div>
                                <div class="col-md-1 col-sm-1 col-xs-1" style="width: 58px; margin-top: 8px; padding:6px 0px 0px 0px !important; margin:0px !important; height:195px; margin-left:4px !important; border:thin solid #DDD;background-color:#FFF;">
                                    <p:panel style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background: transparent">
                                        <p:commandLink title="#{localemsgs.Adicionar}"
                                                       actionListener="#{inspecoes.prepararNovaInspecao}"
                                                       update="msgs">
                                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" style="margin-top: 9px !important;" />
                                        </p:commandLink>
                                        <p:commandLink title="#{localemsgs.Editar}" actionListener="#{inspecoes.abrirInspecao}"
                                                       update="panelListaInspecoes msgs formAdicionarInspecao" >
                                            <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40" style="margin-top: 10px !important;" />
                                        </p:commandLink>
                                        <p:commandLink title="#{localemsgs.Remover}" action="#{inspecoes.apagarInspecao}"
                                                       update="panelListaInspecoes msgs" >
                                            <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" style="margin-top: 10px !important;" />
                                        </p:commandLink>
                                    </p:panel>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formAdicionarInspecao">
                    <p:dialog widgetVar="dlgAdicionarInspecao" id="dlgAdicionarInspecao"  positionType="absolute" responsive="true" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop" closeOnEscape="false" 
                              class="dialogoPequeno" style="padding:0px !important">
                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.AdicionarInspecao}"/> 
                        </f:facet>
                        <p:outputPanel id="panelAdicionarInspecoes" style="padding: 0px !important">

                            <div class="verticalSpace col-md-12 col-sm-12 col-xs-12">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "/>
                            </div>

                            <div class="verticalSpace col-md-12 col-sm-12 col-xs-12">
                                <p:inputText value="#{inspecoes.inspecaoSelecionada.descricao}"
                                             id="descricao" style="width: 100%" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}">
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>   
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right">
                                <p:commandLink update="formListarInspecoes:tabelaInspecoes msgs"
                                               actionListener="#{inspecoes.cadastrarInspecao}"
                                               title="#{localemsgs.Salve}" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:outputPanel>
                    </p:dialog>
                </h:form>

                <h:form id="formInspecaoItens">
                    <p:dialog widgetVar="dlgInspecaoItens" id="dlgInspecaoItens" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop"
                              hideEffect="drop" closeOnEscape="false" class="dialogoGrande" style="padding-bottom: 0px !important; min-height: 90% !important">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgInspecaoItens').closeIcon.unbind('click');
                                //register your own
                                PF('dlgInspecaoItens').closeIcon.click(function (e) {
                                    $("#formInspecaoItens\\:botaoFecharRota").click()
                                    //should be always called
                                    e.preventDefault()
                                })
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFecharRota" style="display: none"
                                         oncomplete="PF('dlgInspecaoItens').hide()" id="botaoFecharRota">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{inspecoes.inspecaoSelecionada.descricao}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="panelInspecoesItens" style="background-color: transparent; padding-top: 0px !important; padding-bottom: 0px !important"
                                 styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panel id="panelNovoItemInspecao" style="background-color: transparent; width: 100%">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:0px !important">

                                    <p:outputLabel for="novaResposta" value="#{localemsgs.Tipo}" />
                                    <p:selectOneMenu value="#{inspecoes.novoItemInspecao.tipoResp}"
                                                     id="novaResposta" style="width: 100%" immediate="true">
                                        <p:ajax event="itemSelect" update="panelNovoItemInspecao" oncomplete="PF('dlgInspecaoItens').initPosition()" />
                                        <f:selectItem itemValue="0" itemLabel="#{localemsgs.SimNao}"/>
                                        <f:selectItem itemValue="1" itemLabel="#{localemsgs.Texto}"/>
                                        <f:selectItem itemValue="7" itemLabel="#{localemsgs.Numero}"/>
                                        <f:selectItem itemValue="8" itemLabel="#{localemsgs.Opcoes}"/>
                                        <f:selectItem itemValue="2" itemLabel="#{localemsgs.Funcion}"/>
                                        <f:selectItem itemValue="9" itemLabel="#{localemsgs.Pessoa}"/>
                                        <f:selectItem itemValue="3" itemLabel="#{localemsgs.Veiculo}"/>
                                        <f:selectItem itemValue="4" itemLabel="#{localemsgs.Assinatura}"/>
                                        <f:selectItem itemValue="5" itemLabel="#{localemsgs.Data}"/>
                                        <f:selectItem itemValue="6" itemLabel="#{localemsgs.Hora}"/>
                                    </p:selectOneMenu>

                                    <p:outputLabel for="novaPergunta" value="#{localemsgs.Pergunta}" style="margin-top: 8px !important"/>
                                    <p:inputText value="#{inspecoes.novoItemInspecao.pergunta}"
                                                 id="novaPergunta" style="width: 100%">
                                        <p:watermark for="novaPergunta" value="#{localemsgs.Pergunta}"/>
                                    </p:inputText>

                                    <p:selectBooleanCheckbox id="preenchimentoObrigatorio" value="#{inspecoes.novoItemInspecao.obrigatorio}"
                                                             style="width: 100%; margin-top: 10px" itemLabel="#{localemsgs.PreenchimentoObrigatorio}"/>

                                    <p:panel id="pnlNovaOpcoes" rendered="#{inspecoes.novoItemInspecao.tipoResp eq '8'}" style="background-color: transparent !important;">
                                        <h:inputHidden id="txtOpcoesRespota" value="#{inspecoes.novasOpcoesResposta}"></h:inputHidden>

                                        <p:outputLabel value="#{localemsgs.RespostasPermitidas}" style="margin-top: 8px !important" /><a id="btAddNovaPossivelResposta" href="javascript:void(0);" class="btn btn-success" style="font-size: 9pt !important; padding: 1px 8px 1px 8px !important; margin-left: 10px; margin-top: -5px !important"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;#{localemsgs.Adicionar}</a>

                                        <div class="col-md-12 col-sm-12 col-xs-12" id="divItensRespostasPossiveis" style="padding: 0px !important;  max-height: 150px !Important; overflow-y: auto !important">
                                            <div ref="itemResposta" style="position: relative; width: 100%; padding-top: 5px !important">
                                                <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 4px 0px 0px !important">
                                                    <input type="text" class="form-control" ref="txtNovoOpcaoItem" placeholder="#{localemsgs.Item}" />
                                                </div>
                                                <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 0px 0px 0px 4px !important">
                                                    <input type="text" class="form-control" ref="txtNovoOpcaoDescricao" placeholder="#{localemsgs.Descricao}" />
                                                </div>
                                            </div>
                                        </div>
                                    </p:panel>

                                    <p:selectBooleanCheckbox id="novaFoto" value="#{inspecoes.novoItemInspecao.foto}"
                                                             rendered="#{inspecoes.novoItemInspecao.tipoResp ne '4'
                                                                         and inspecoes.novoItemInspecao.tipoResp ne '5'
                                                                         and inspecoes.novoItemInspecao.tipoResp ne '6'}"
                                                             style="width: 100%; margin-top: 10px" itemLabel="#{localemsgs.CapturaFoto}"/>

                                    <p:selectBooleanCheckbox id="novoVideo" value="#{inspecoes.novoItemInspecao.video}"
                                                             rendered="#{inspecoes.novoItemInspecao.tipoResp ne '4'
                                                                         and inspecoes.novoItemInspecao.tipoResp ne '5'
                                                                         and inspecoes.novoItemInspecao.tipoResp ne '6'}"
                                                             style="width: 100%; margin-top: 10px" itemLabel="#{localemsgs.CapturaVideo}"/>
                                </div>

                                <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; margin-bottom: 10px;">
                                    <p:commandLink update="panelInspecoesItens msgs" partialSubmit="true" process="panelNovoItemInspecao"
                                                   actionListener="#{inspecoes.cadastrarItemInspecao}" id="btSalvarNovoCadastro"
                                                   title="#{localemsgs.Salve}" styleClass="btn btn-primary" style="color:#FFF !important">
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>
                                </div>
                            </p:panel>

                            <p:separator style="width: 100%" />

                            <p:scrollPanel mode="native" style="height: calc(90vh - 390px) !important; background: transparent; width: 100%;" id="panelItensInspecao">
                                <c:forEach var="itensInspecao" items="#{inspecoes.itensInspecao}" varStatus="itemInspecao">

                                    <p:panel id="itensInspecao_#{itensInspecao.sequencia}" style="margin-bottom:20px; padding: 10px; border: 2px solid #145B9B !important">

                                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding-top:10px !important">

                                            <p:outputLabel for="resposta_#{itensInspecao.sequencia}" value="#{localemsgs.Tipo}:"/>
                                            <p:selectOneMenu value="#{itensInspecao.tipoResp}" id="resposta_#{itensInspecao.sequencia}" 
                                                             style="width: 100%">
                                                <f:selectItem itemValue="0" itemLabel="#{localemsgs.SimNao}"/>
                                                <f:selectItem itemValue="1" itemLabel="#{localemsgs.Texto}"/>
                                                <f:selectItem itemValue="7" itemLabel="#{localemsgs.Numero}"/>
                                                <f:selectItem itemValue="8" itemLabel="#{localemsgs.Opcoes}"/>
                                                <f:selectItem itemValue="2" itemLabel="#{localemsgs.Funcion}"/>
                                                <f:selectItem itemValue="9" itemLabel="#{localemsgs.Pessoa}"/>
                                                <f:selectItem itemValue="3" itemLabel="#{localemsgs.Veiculo}"/>
                                                <f:selectItem itemValue="4" itemLabel="#{localemsgs.Assinatura}"/>
                                                <f:selectItem itemValue="5" itemLabel="#{localemsgs.Data}"/>
                                                <f:selectItem itemValue="6" itemLabel="#{localemsgs.Hora}"/>
                                                <p:ajax event="itemSelect" update="itensInspecao_#{itensInspecao.sequencia}"></p:ajax>
                                            </p:selectOneMenu>

                                            <p:outputLabel for="pergunta_#{itensInspecao.sequencia}" value="#{localemsgs.Pergunta}:"/>
                                            <p:inputText value="#{itensInspecao.pergunta}"
                                                         id="pergunta_#{itensInspecao.sequencia}" style="width: 100%">
                                                <p:watermark for="pergunta_#{itensInspecao.sequencia}" value="#{localemsgs.Pergunta}"/>
                                            </p:inputText>

                                            <p:selectBooleanCheckbox id="preenchimentoObrigatorio_#{itensInspecao.sequencia}" 
                                                                     value="#{itensInspecao.obrigatorioChecked}"
                                                                     style="width: 100%; margin-top: 10px" itemLabel="#{localemsgs.PreenchimentoObrigatorio}"/>

                                            <p:panel id="pnlNovaOpcoesEdicao_#{itensInspecao.sequencia}" style="background-color: transparent !important;" rendered="#{itensInspecao.tipoResp eq '8'}">
                                                <h:inputHidden id="txtOpcoesRespotaEdicao_#{itensInspecao.sequencia}" value="#{itensInspecao.opcoesResposta}"></h:inputHidden>

                                                <p:outputLabel value="#{localemsgs.RespostasPermitidas}" style="margin-top: 8px !important" /><a id="btAddNovaPossivelRespostaEdicao_#{itensInspecao.sequencia}" href="javascript:void(0);" class="btn btn-success" style="font-size: 9pt !important; padding: 1px 8px 1px 8px !important; margin-left: 10px; margin-top: -5px !important"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;#{localemsgs.Adicionar}</a>

                                                <div class="col-md-12 col-sm-12 col-xs-12" id="divItensRespostasPossiveisEdicao_#{itensInspecao.sequencia}" sequencia="#{itensInspecao.sequencia}" style="padding: 0px !important;  max-height: 150px !Important; overflow-y: auto !important">
                                                    <h:outputText escape="false" value="#{itensInspecao.opcoesRespostaHTML}" />
                                                </div>
                                            </p:panel>

                                            <p:selectBooleanCheckbox id="foto_#{itensInspecao.sequencia}" value="#{itensInspecao.fotoChecked}"
                                                                     rendered="#{itensInspecao.tipoResp ne '4'
                                                                                 and itensInspecao.tipoResp ne '5'
                                                                                 and itensInspecao.tipoResp ne '6'}"
                                                                     style="width: 100%; margin-top: 10px" itemLabel="#{localemsgs.CapturaFoto}"/>

                                            <p:selectBooleanCheckbox id="video_#{itensInspecao.sequencia}" value="#{itensInspecao.videoChecked}"
                                                                     rendered="#{itensInspecao.tipoResp ne '4'
                                                                                 and itensInspecao.tipoResp ne '5'
                                                                                 and itensInspecao.tipoResp ne '6'}"
                                                                     style="width: 100%; margin-top: 10px" itemLabel="#{localemsgs.CapturaVideo}"/>
                                        </div>

                                        <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; margin-bottom: 10px;">

                                            <p:commandLink title="#{localemsgs.Remover}" process="@this" action="#{inspecoes.removerItemInspecao(itensInspecao)}"
                                                           update="panelItensInspecao msgs" style="float: right">
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="30" height="30" />
                                            </p:commandLink>

                                            <p:commandLink title="#{localemsgs.Confirmar}" process="itensInspecao_#{itensInspecao.sequencia}"
                                                           action="#{inspecoes.editarItemInspecao(itensInspecao, itemInspecao.getCurrent())}"
                                                           update="panelItensInspecao msgs" style="float: right">
                                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30" />
                                            </p:commandLink>
                                        </div>

                                        <p:panelGrid id="pnlNovaOpcoesPaiEdicao_#{itensInspecao.sequencia}" columns="1" columnClasses="ui-grid-col-12" 
                                                     layout="grid" styleClass="ui-panelgrid-blank"
                                                     style="background: white">

                                        </p:panelGrid>

                                    </p:panel>
                                </c:forEach>
                            </p:scrollPanel>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisar -->
                <h:form id="formPesquisar">
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                        <f:facet name="header">
                            <img src="../assets/img/icones_satmob_insp_postos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Filtrar}" style="color:#022a48;" /> 
                        </f:facet>
                        <p:panel id="pesquisar" style="background: transparent">

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.Filial}: "/>
                                </div>    
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{inspecoes.pstInspecaoSelecionada.codfil}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" listener="#{inspecoes.atualizarListaPostos}"
                                                update="formPesquisar:posto"/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="posto" value="#{localemsgs.Posto}: "/>
                                </div>    
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="posto" value="#{inspecoes.pstInspecaoSelecionada.secao}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{inspecoes.postos}" var="postos" itemValue="#{postos.secao}"
                                                       itemLabel="#{postos.local}" noSelectionValue=""/>
                                        <p:watermark for="posto" value="#{localemsgs.Posto}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="inspecao" value="#{localemsgs.Inspecao}: "/>
                                </div>    
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="inspecao" value="#{inspecoes.pstInspecaoSelecionada.codInspecao}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{inspecoes.inspecoes}" var="inspecs" itemValue="#{inspecs.codigo}"
                                                       itemLabel="#{inspecs.descricao}" noSelectionValue=""/>
                                        <p:watermark for="inspecao" value="#{localemsgs.Inspecao}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="cadastro" action="#{inspecoes.filtrar}" update=":main:tabela :msgs cabecalho"
                                               oncomplete="PF('dlgPesquisar').hide()"
                                               title="#{localemsgs.Filtrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Exportar-->
                <h:form id="formExportar" class="form-inline">
                    <p:dialog widgetVar="dlgExportar"
                              positionType="absolute"
                              responsive="true"
                              draggable="false"
                              modal="true"
                              closable="true"
                              resizable="false"
                              dynamic="true"
                              showEffect="drop"
                              hideEffect="drop"
                              closeOnEscape="false"
                              style="min-height:95% !important; width:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel>
                            <p:dataTable id="tabelaInspecoesRelatorio"
                                         value="#{inspecoes.dadosGride}"
                                         paginator="true"
                                         rows="10"
                                         reflow="true"
                                         rowsPerPageTemplate="10,15,20,25,50,100"
                                         currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.PstInspecao}"
                                         paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                         var="insItem"
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         styleClass="tabela DataGrid"
                                         scrollable="true"
                                         scrollWidth="100%">
                                <p:columns value="#{inspecoes.columnsGride}" var="colKey" class="text-center">
                                    <f:facet name="header">
                                        <h:outputText value="#{colKey.header}"/>
                                    </f:facet>
                                    <h:outputText value="#{insItem.linha[colKey.position].header}" converter="conversorData" />
                                </p:columns>
                            </p:dataTable>
                        </p:panel>

                        <p:separator />
                        <p:panelGrid rendered="#{not empty inspecoes.dadosGride}"
                                     columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:panel style="text-align: center; background-color:#EEE;">
                                <p:outputLabel for="pdf" value="#{localemsgs.DownloadPDF}:" style="font-weight: bold"/>
                                <h:commandLink id="pdf">
                                    <p:graphicImage url="../assets/img/icone_pdf.png" style="height:80px;"/>

                                    <p:dataExporter target="formExportar:tabelaInspecoesRelatorio"
                                                    type="pdf"
                                                    preProcessor="#{inspecoes.preProcessPDF}"
                                                    encoding="iso-8859-1"
                                                    options="#{inspecoes.pdfOptions}"
                                                    fileName="#{localemsgs.Inspecoes}-#{inspecoes.dataTela}"/>
                                </h:commandLink>
                            </p:panel>

                            <p:panel style="text-align: center; background-color:#EEE;">
                                <p:outputLabel for="xls" value="#{localemsgs.Download} #{localemsgs.xls}:" style="font-weight: bold"/>
                                <h:commandLink id="xls">
                                    <p:graphicImage url="../assets/img/icone_xls.png" style="height:80px;"/>

                                    <p:dataExporter target="formExportar:tabelaInspecoesRelatorio"
                                                    type="xls"
                                                    fileName="#{localemsgs.Inspecoes}-#{inspecoes.dataTela}"/>
                                </h:commandLink>
                            </p:panel>
                        </p:panelGrid>

                        <p:panel rendered="#{empty inspecoes.dadosGride}">
                            <h:outputText value="#{localemsgs.SemRegistros}"/>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <script type="text/javascript">
                // <![CDATA[
                var ItemNovaOpcao = '<div ref="itemResposta" style="position: relative; width: 100%; margin-top: 5px !important">' +
                        '    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 5px 4px 0px 0px !important; float: left">' +
                        '        <input type="text" class="form-control" ref="txtNovoOpcaoItem" placeholder="#{localemsgs.Item}" />' +
                        '    </div>' +
                        '    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 5px 4px 0px 4px !important; float: left; width: calc(50% - 28px) !important">' +
                        '        <input type="text" class="form-control" ref="txtNovoOpcaoDescricao" placeholder="#{localemsgs.Descricao}" />' +
                        '    </div>' +
                        '    <i class="fa fa-minus-square" ref="btExcluirPossivelResposta" style="font-size: 12pt; color: red; cursor:pointer; float: right; padding-top: 14px !important; padding-right: 8px !important" title="#{localemsgs.Excluir}"></i>' +
                        '</div>';

                var ItemNovaOpcaoEdicao = '<div ref="itemResposta" style="position: relative; width: 100%; margin-top: 5px !important">' +
                        '    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 5px 4px 0px 0px !important; float: left">' +
                        '        <input type="text" class="form-control" ref="txtNovoOpcaoItem" sequencia="" placeholder="#{localemsgs.Item}" />' +
                        '    </div>' +
                        '    <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 5px 4px 0px 4px !important; float: left; width: calc(50% - 28px) !important">' +
                        '        <input type="text" class="form-control" ref="txtNovoOpcaoDescricao" sequencia="" placeholder="#{localemsgs.Descricao}" />' +
                        '    </div>' +
                        '    <i class="fa fa-minus-square" ref="btExcluirPossivelRespostaEdicao_0" style="font-size: 12pt; color: red; cursor:pointer; float: right; padding-top: 14px !important; padding-right: 8px !important" title="#{localemsgs.Excluir}"></i>' +
                        '</div>';

                function SubstituirTraducao() {
                    try {
                        setTimeout(function () {
                            $('[ref="txtNovoOpcaoDescricao"]').each(function () {
                                $(this).attr('placeholder', '#{localemsgs.Descricao}');
                            });

                            $('[ref="txtNovoOpcaoItem"]').each(function () {
                                $(this).attr('placeholder', '#{localemsgs.Item}');
                            });

                            $('[ref^="btExcluirPossivelRespostaEdicao_"]').each(function () {
                                $(this).attr('title', ReplaceAll($(this).attr('title'), 'localemsgs.Excluir', '#{localemsgs.Excluir}'));
                            });
                        }, 200)
                    } catch (e) {
                    }
                }

                function ChangeInspecao() {
                    let HTML = '';

                    $('[id*="panelNovoItemInspecao"]').find('div[ref="itemResposta"]').each(function () {
                        if (HTML !== '')
                            HTML += 'sasw_separator2';

                        HTML += $(this).find('input[ref="txtNovoOpcaoItem"]').val();
                        HTML += 'sasw_separator1';
                        HTML += $(this).find('input[ref="txtNovoOpcaoDescricao"]').val();
                    });

                    $('[id*="txtOpcoesRespota"]').val(HTML);
                }

                function ChangeInspecaoEdicao(inID) {
                    let HTML = '';

                    $('#divItensRespostasPossiveisEdicao_' + inID).find('div[ref="itemResposta"]').each(function () {
                        if (HTML !== '')
                            HTML += 'sasw_separator2';

                        HTML += $(this).find('input[ref="txtNovoOpcaoItem"]').val();
                        HTML += 'sasw_separator1';
                        HTML += $(this).find('input[ref="txtNovoOpcaoDescricao"]').val();
                    });

                    $('[id*="txtOpcoesRespotaEdicao_' + inID + '"]').val(HTML);
                }

                $(document)
                        .on('change', '[id*="panelNovoItemInspecao"] input[ref="txtNovoOpcaoItem"], [id*="panelNovoItemInspecao"] input[ref="txtNovoOpcaoDescricao"]', function () {
                            ChangeInspecao();
                        })
                        .on('change', '[id^="divItensRespostasPossiveisEdicao_"] input[ref="txtNovoOpcaoItem"], [id^="divItensRespostasPossiveisEdicao_"] input[ref="txtNovoOpcaoDescricao"]', function () {
                            ChangeInspecaoEdicao($(this).attr('sequencia'));
                        })
                        .on('click', '#btAddNovaPossivelResposta', function () {
                            $('#divItensRespostasPossiveis').append(ItemNovaOpcao);
                            $('[id*="panelNovoItemInspecao"] [ref="itemResposta"]:last-child').find('[ref="txtNovoOpcaoItem"]').focus();
                        })
                        .on('click', '[id^="btAddNovaPossivelRespostaEdicao_"]', function () {
                            $('#divItensRespostasPossiveisEdicao_' + $(this).attr('id').split('_')[1]).append(ReplaceAll(ItemNovaOpcaoEdicao.replace('btExcluirPossivelRespostaEdicao_0', 'btExcluirPossivelRespostaEdicao_' + $(this).attr('id').split('_')[1]), 'sequencia=""', 'sequencia="' + $(this).attr('id').split('_')[1] + '"'));
                            $('#divItensRespostasPossiveisEdicao_' + $(this).attr('id').split('_')[1]).find('[ref="itemResposta"]:last-child').find('[ref="txtNovoOpcaoItem"]').focus();
                        })
                        .on('click', '[ref^="btExcluirPossivelRespostaEdicao_"]', function () {
                            let sequencia = $(this).attr('ref').split('_')[1].toString();
                            $(this).parent('div').remove();
                            ChangeInspecaoEdicao(sequencia);
                            setTimeout(function () {
                                $('#divItensRespostasPossiveisEdicao_' + $(this).attr('id').split('_')[1]).find('[ref="itemResposta"]:last-child').find('[ref="txtNovoOpcaoItem"]').focus();
                            }, 250);
                        })
                        ;
                // ]]>
            </script>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck">
                                    <h:outputText styleClass="corporativo-label" value="#{localemsgs.Corporativo}: " />
                                </label>
                                <p:selectBooleanCheckbox value="#{inspecoes.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{inspecoes.mostrarTodasFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.LimparFiltros}: " /></label>
                                <p:selectBooleanCheckbox value="#{inspecoes.limparFiltros}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{inspecoes.limparTodosFiltros}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" action="#{localeController.getLocales}">
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25"/>
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>


/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.pacotesuteis.utilidades;

import Dados.Persistencia;
import SasDaos.ParametDao;
import br.com.sasw.pacotesuteis.sasbeans.TesMoedasVlr;
import br.com.sasw.pacotesuteis.sasdaos.TesMoedasVlrDao;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 * <AUTHOR>
 */
public class Moeda {

    public static BigDecimal converterMoedaPadrao(String valor, String moeda, String codFil, Persistencia persistencia) throws Exception{
        if (moeda.equals("R$")) moeda = "BRL";
        
        if (moeda.equals(ParametDao.getMoedaPdrMobileS(codFil, persistencia))) return new BigDecimal(valor);
        TesMoedasVlr tesMoedasVlr = TesMoedasVlrDao.obterCotacaoDiaria(codFil, moeda, persistencia);
        return new BigDecimal(valor).multiply(new BigDecimal(1000))
                .divide(new BigDecimal(tesMoedasVlr.getValor()).multiply(new BigDecimal(1000)), 2, RoundingMode.HALF_UP);
    }
}

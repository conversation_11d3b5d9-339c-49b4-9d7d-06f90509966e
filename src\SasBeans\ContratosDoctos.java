/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ContratosDoctos {

    private String Contrato;
    private String CodFil;
    private String DtArquivo;
    private Integer Ordem;
    private String Descricao;
    private String Dt_Inicio;
    private String Dt_Termino;
    private String Comissao;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDtArquivo() {
        return DtArquivo;
    }

    public void setDtArquivo(String DtArquivo) {
        this.DtArquivo = DtArquivo;
    }

    public Integer getOrdem() {
        return Ordem;
    }

    public void setOrdem(Integer Ordem) {
        this.Ordem = Ordem;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDt_Inicio() {
        return Dt_Inicio;
    }

    public void setDt_Inicio(String Dt_Inicio) {
        this.Dt_Inicio = Dt_Inicio;
    }

    public String getDt_Termino() {
        return Dt_Termino;
    }

    public void setDt_Termino(String Dt_Termino) {
        this.Dt_Termino = Dt_Termino;
    }

    public String getComissao() {
        return Comissao;
    }

    public void setComissao(String Comissao) {
        this.Comissao = Comissao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 67 * hash + Objects.hashCode(this.Contrato);
        hash = 67 * hash + Objects.hashCode(this.CodFil);
        hash = 67 * hash + Objects.hashCode(this.DtArquivo);
        hash = 67 * hash + Objects.hashCode(this.Ordem);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ContratosDoctos other = (ContratosDoctos) obj;
        if (!Objects.equals(this.Contrato, other.Contrato)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        if (!Objects.equals(this.DtArquivo, other.DtArquivo)) {
            return false;
        }
        if (!Objects.equals(this.Ordem, other.Ordem)) {
            return false;
        }
        return true;
    }
}

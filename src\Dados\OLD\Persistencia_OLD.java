/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Dados.OLD;

import Dados.Persistencia;
import java.sql.PreparedStatement;

/**
 *
 * <AUTHOR>
 */
public class Persistencia_OLD extends Persistencia {

    private final Conecta_OLD conecta;
    private String Empresa;
    private boolean stds = false;
    private boolean mysql = false;

    /**
     * Para declaração vazia
     */
    public Persistencia_OLD() {
        this.conecta = new Conecta_OLD();
    }

    /**
     * Construtor da classe
     *
     * @param Parametro - nome da empresa todos os caracters em maiusculo
     * @param Caminho - caminho do arquivo mapconect.txt
     * @param Opcao - WEB - para uso em aplicações web e DESK para aplicações
     * desktop
     * @throws Exception - excessoes da criacao da classe
     */
    public Persistencia_OLD(String Parametro, String Caminho, String Opcao) throws Exception {
        this.conecta = new Conecta_OLD();
        conecta.Abreconexao(Parametro, Caminho, Opcao);
    }

    /**
     * Construtor da classe
     *
     * @param Parametro - nome da empresa todos os caracters em maiusculo
     * @param Caminho - caminho do arquivo mapconect.txt
     * @param Opcao - WEB - para uso em aplicações web e DESK para aplicações
     * desktop
     * @param DriverJSTD - False - driver microsoft (padrão) - True - uso de
     * jtds
     * @throws Exception - excessoes da criacao da classe
     */
    public Persistencia_OLD(String Parametro, String Caminho, String Opcao, boolean DriverJSTD) throws Exception {
        this.conecta = new Conecta_OLD();
        this.stds = DriverJSTD;
        conecta.setJtds(DriverJSTD);
        conecta.Abreconexao(Parametro, Caminho, Opcao);
    }

    /**
     * Construtor da classe
     *
     * @param Parametro - nome da empresa todos os caracters em maiusculo
     * @param Caminho - caminho do arquivo mapconect.txt
     * @param Opcao - WEB - para uso em aplicações web e DESK para aplicações
     * desktop
     * @param DriverJSTD - False - driver microsoft (padrão) - True - uso de
     * jtds
     * @param DriverMySQL
     * @throws Exception - excessoes da criacao da classe
     */
    public Persistencia_OLD(String Parametro, String Caminho, String Opcao, boolean DriverJSTD, boolean DriverMySQL) throws Exception {
        this.conecta = new Conecta_OLD();
        this.stds = DriverJSTD;
        this.mysql = DriverMySQL;
        conecta.setJtds(DriverJSTD);
        conecta.setMysql(DriverMySQL);
        conecta.Abreconexao(Parametro, Caminho, Opcao);
    }

    /**
     * Estabelecer conexao diretamente sem os dados estarem na mapconect Para
     * usar driver jtds - setar opção antes de usar ConexaoDireta
     *
     * @param IP - String contendo ip, porta e nome do banco no modelo
     * ***************:2006;databaseName=NomeBanco
     * @param Login - login de acesso ao banco
     * @param Senha - senha de acesso ao banco
     * @throws Exception - pode gerar exception em caso de erro de conexão
     */
    public void ConexaoDireta(String IP, String Login, String Senha) throws Exception {
        conecta.setJtds(this.stds);
        conecta.setMysql(this.mysql);
        conecta.ConexaoDireta(IP, Login, Senha);
    }

    /**
     * Use para fechar a conexao com o banco
     *
     * @throws Exception - gera mensagem de erro caso ocorra
     */
    public void FechaConexao() throws Exception {
        conecta.Fechaconexao();
    }

    /**
     * Devolve um PreparedStatement da conexao atual
     *
     * @param sql - string sql que sera usada
     * @return - Prepared Statement da conexao atual
     * @throws Exception - gera a mensagem de erro caso ocorra
     */
    public PreparedStatement getState(String sql) throws Exception {
        this.Reconectar();
        return conecta.getState(sql);
    }

    /**
     * Usado internamente para garantir que a conexao com o banco continua
     * aberta
     */
    private void Reconectar() throws Exception {
        conecta.Abreconexao("", "", "");
    }

    /**
     * Devolve a empresa da persistencia atual
     *
     * @return - Nome do Parametro da empresa
     */
    public String getEmpresa() {
        return Empresa;
    }

    /**
     * Determina a empresa da persistencia atual
     *
     * @param Empresa - Parametro da Empresa
     */
    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    /**
     * Ativa uso de jdbc STDL Padrão uso de jdbc microsoft
     *
     * @param value (false) - padrão usado para microsoft (true) - usado para
     * jtds
     */
    public void setJtds(boolean value) {
        this.stds = value;
    }

    /**
     * Ativa uso de jdbc STDL Padrão uso de jdbc microsoft
     *
     * @param value (false) - padrão usado para microsoft (true) - usado para
     * jtds
     */
    public void setMysql(boolean value) {
        this.mysql = value;
    }

    /**
     * Inicia uma transacao
     *
     * @throws Exception
     */
    public void setTransacao() throws Exception {
        conecta.setIniTransacao();
    }

    /**
     * Efetua o commit da transacao e finaliza a transacao
     *
     * @throws Exception
     */
    public void commitTransacao() throws Exception {
        conecta.ExecutaTransacao();
        conecta.setFimTransacao();
    }

    public boolean isMysql() {
        return mysql;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class CtrItensAntReajustes {

    private String CodFil;
    private String Contrato;
    private String Data;
    private String Descricao;
    private String DtBase;
    private String Dt_alter;
    private String FranquiaAst;
    private String FranquiaEsp;
    private String FranquiaEve;
    private String FranquiaRot;
    private String HEDiurna2;
    private String HEDiurna;
    private String HENoturna2;
    private String HENoturna;
    private String Hr_alter;
    private String Indice;
    private String Operador;
    private String Ordem;
    private String Reforco;
    private String Salario;
    private String SeqReaj;
    private String TipoPosto;
    private String ValorAst;
    private String ValorEsp;
    private String ValorEve;
    private String ValorPosto;
    private String ValorRot;
    private String inibirReajuste;

    public CtrItensAntReajustes() {
        this.Contrato = "";
        this.CodFil = "0";
        this.TipoPosto = "";
        this.SeqReaj = "";
        this.Data = "";
        this.Ordem = "";
        this.ValorPosto = "";
        this.HEDiurna = "0";
        this.HENoturna = "0";
        this.HEDiurna2 = "0";
        this.HENoturna2 = "0";
        this.Reforco = "0";
        this.ValorRot = "";
        this.FranquiaRot = "";
        this.ValorEve = "";
        this.FranquiaEve = "";
        this.ValorEsp = "";
        this.FranquiaEsp = "";
        this.ValorAst = "";
        this.FranquiaAst = "";
        this.Salario = "";
        this.inibirReajuste = "";
        this.Operador = "";
        this.Dt_alter = null;
        this.Hr_alter = null;
        this.Descricao = "";
        this.DtBase = "";
        this.Indice = "";
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getTipoPosto() {
        return TipoPosto;
    }

    public void setTipoPosto(String TipoPosto) {
        this.TipoPosto = TipoPosto;
    }

    public String getSeqReaj() {
        return SeqReaj;
    }

    public void setSeqReaj(String SeqReaj) {
        this.SeqReaj = SeqReaj;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getOrdem() {
        return Ordem;
    }

    public void setOrdem(String Ordem) {
        this.Ordem = Ordem;
    }

    public String getValorPosto() {
        return ValorPosto;
    }

    public void setValorPosto(String ValorPosto) {
        this.ValorPosto = ValorPosto;
    }

    public String getHEDiurna() {
        return HEDiurna;
    }

    public void setHEDiurna(String HEDiurna) {
        this.HEDiurna = HEDiurna;
    }

    public String getHENoturna() {
        return HENoturna;
    }

    public void setHENoturna(String HENoturna) {
        this.HENoturna = HENoturna;
    }

    public String getHEDiurna2() {
        return HEDiurna2;
    }

    public void setHEDiurna2(String HEDiurna2) {
        this.HEDiurna2 = HEDiurna2;
    }

    public String getHENoturna2() {
        return HENoturna2;
    }

    public void setHENoturna2(String HENoturna2) {
        this.HENoturna2 = HENoturna2;
    }

    public String getReforco() {
        return Reforco;
    }

    public void setReforco(String Reforco) {
        this.Reforco = Reforco;
    }

    public String getValorRot() {
        return ValorRot;
    }

    public void setValorRot(String ValorRot) {
        this.ValorRot = ValorRot;
    }

    public String getFranquiaRot() {
        return FranquiaRot;
    }

    public void setFranquiaRot(String FranquiaRot) {
        this.FranquiaRot = FranquiaRot;
    }

    public String getValorEve() {
        return ValorEve;
    }

    public void setValorEve(String ValorEve) {
        this.ValorEve = ValorEve;
    }

    public String getFranquiaEve() {
        return FranquiaEve;
    }

    public void setFranquiaEve(String FranquiaEve) {
        this.FranquiaEve = FranquiaEve;
    }

    public String getValorEsp() {
        return ValorEsp;
    }

    public void setValorEsp(String ValorEsp) {
        this.ValorEsp = ValorEsp;
    }

    public String getFranquiaEsp() {
        return FranquiaEsp;
    }

    public void setFranquiaEsp(String FranquiaEsp) {
        this.FranquiaEsp = FranquiaEsp;
    }

    public String getValorAst() {
        return ValorAst;
    }

    public void setValorAst(String ValorAst) {
        this.ValorAst = ValorAst;
    }

    public String getFranquiaAst() {
        return FranquiaAst;
    }

    public void setFranquiaAst(String FranquiaAst) {
        this.FranquiaAst = FranquiaAst;
    }

    public String getSalario() {
        return Salario;
    }

    public void setSalario(String Salario) {
        this.Salario = Salario;
    }

    public String getInibirReajuste() {
        return inibirReajuste;
    }

    public void setInibirReajuste(String inibirReajuste) {
        this.inibirReajuste = inibirReajuste;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDtBase() {
        return DtBase;
    }

    public void setDtBase(String DtBase) {
        this.DtBase = DtBase;
    }

    public String getIndice() {
        return Indice;
    }

    public void setIndice(String Indice) {
        this.Indice = Indice;
    }

}

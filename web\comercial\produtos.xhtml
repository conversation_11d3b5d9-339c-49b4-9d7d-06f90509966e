<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/clientes.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        height: calc(100% - 6.5em);
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid thead tr td:nth-child(1){
                        min-width: 49px;
                    }
                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid thead tr td:nth-child(2){
                        min-width: 275px;
                    }
                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid thead tr td:nth-child(3){
                        min-width: 150px;
                    }
                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid thead tr td:nth-child(4){
                        min-width: 80px;
                    }
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid thead tr td:nth-child(5){
                        min-width: 200px;
                    }
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid thead tr td:nth-child(6){
                        min-width: 140px;
                    }
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid thead tr td:nth-child(7){
                        min-width: 80px;
                    }
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid thead tr td:nth-child(8){
                        min-width: 80px;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formCadastrar .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }
            </style>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{produtos.Persistencias(login.pp)}"/>
            </f:metadata>
            <p:growl id="msgs"/>
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;"
                                     >
                                    <img src="../assets/img/icone_satmob_produtosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Produtos}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span>
                                            <h:outputText
                                                id="dataDia"
                                                value="#{produtos.dataTela}"
                                                converter="conversorDia"
                                                />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="text-align: center !important;"
                                     >
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{produtos.filiais.descricao}
                                            <label id="btTrocarFilial"
                                                   onclick="top.location.href = '../param.xhtml'"
                                                   >
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{produtos.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{produtos.filiais.bairro}, #{produtos.filiais.cidade}/#{produtos.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar"
                                     class="col-md-2 col-sm-2 col-xs-3"
                                     style="padding:0px 10px 0px 0px !important; text-align: right !important"
                                     >
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();"
                                                   action="#"
                                                   >
                                        <p:graphicImage
                                            url="../assets/img/icone_voltar_branco.png"
                                            height="40"
                                            />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Tabela produtos-->
                <h:form id="main">
                    <p:hotkey bind="a" action="#{produtos.PreCadastrar}" oncomplete="PF('dlgCadastrar').show();" update="formCadastrar msgs"/>
                    <p:hotkey bind="p" action="#{produtos.PreCadastrar}" update="formPesquisar msgs" oncomplete="PF('dlgPesquisar').show()"/>
                    <p:hotkey bind="e" action="#{produtos.PreEditar}" update="formCadastrar msgs"/>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{produtos.allProdutos}"
                                        paginator="true"
                                        rows="15"
                                        lazy="true"
                                        reflow="true"
                                        rowsPerPageTemplate="5,10,15, 20, 25"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Produtos}"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        var="lista"
                                        styleClass="tabela"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        selection="#{produtos.novo}"
                                        rowKey="#{lista.codigo}"
                                        selectionMode="single"
                                        scrollable="true"
                                        class="tabela DataGrid"
                                        scrollWidth="100%"
                                        style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax event="rowDblselect" listener="#{produtos.PreEditar}" update="formCadastrar msgs"/>
                                        <p:column headerText="#{localemsgs.Codigo}">
                                            <h:outputText value="#{lista.codigo}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Descricao}">
                                            <h:outputText value="#{lista.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Aplicacao}" class="text-center">
                                            <h:outputText value="#{lista.aplicacao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Preco}">
                                            <h:outputText value="#{lista.precoVenda}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Obs}">
                                            <h:outputText value="#{lista.obs}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}">
                                            <h:outputText value="#{lista.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}">
                                            <h:outputText value="#{lista.hr_Alter}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}">
                                            <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                    </p:dataTable>

                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" action="#{produtos.PreCadastrar}"
                                           oncomplete="PF('dlgCadastrar').show();"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" action="#{produtos.PreEditar}"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" action="#{produtos.PreCadastrar}"
                                           update="formPesquisar msgs" oncomplete="PF('dlgPesquisar').show()">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{produtos.LimparFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!--Cadastrar novo-->
                <h:form class="form-inline" id="formCadastrar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_produtosG.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarProdutos}" style="color:#022a48" />
                        </f:facet>
                        <p:panel id="cadastrar"
                                 style="background-color: transparent;  max-width: 100% !important;"
                                 styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "/>
                                <p:inputText value="#{produtos.novo.descricao}" id="descricao" required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}"
                                             style="width: 100%">
                                    <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>

                                <p:outputLabel for="aplicacao" value="#{localemsgs.Aplicacao}: "/>
                                <p:inputText value="#{produtos.novo.aplicacao}" id="aplicacao"
                                             style="width: 100%">
                                    <p:watermark for="aplicacao" value="#{localemsgs.Aplicacao}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="preco" value="#{localemsgs.Preco}: "/>
                                <p:inputNumber value="#{produtos.novo.precoVenda}" id="preco" required="true"
                                               requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Preco}"
                                               style="width: 100%">
                                    <p:watermark for="preco" value="#{localemsgs.Preco}"/>
                                </p:inputNumber>

                                <p:outputLabel for="obs" value="#{localemsgs.Obs}: "/>
                                <p:inputText value="#{produtos.novo.obs}" id="obs"
                                             style="width: 100%">
                                    <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <div class="form-inline">
                                <p:commandLink id="cadastro" action="#{produtos.Cadastrar}" update="msgs :main:tabela"
                                               title="#{localemsgs.Cadastrar}" rendered="#{produtos.flag eq 1}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink id="edicao" update="msgs :main:tabela" action="#{produtos.Editar}"
                                               title="#{localemsgs.Editar}" rendered="#{produtos.flag eq 2}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:blockUI block="formCadastrar:cadastrar" trigger="formCadastrar:cadastro"/>
                                <p:blockUI block="formCadastrar:cadastrar" trigger="formCadastrar:edicao"/>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisar produtos-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                               draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                               showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400"
                               style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_produtosG.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarProduto}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="descricao" value="#{produtos.novo.descricao}" label="#{localemsgs.Descricao}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="aplicacao" value="#{localemsgs.Aplicacao}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="aplicacao" value="#{produtos.novo.aplicacao}" label="#{localemsgs.Aplicacao}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="aplicacao" value="#{localemsgs.Aplicacao}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{produtos.PesquisaPaginada}" oncomplete="PF('dlgPesquisar').hide()"
                                               update=" :main:tabela :msgs :cabecalho"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

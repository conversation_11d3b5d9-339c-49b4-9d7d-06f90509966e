/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.DashBoard;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DashBoardDao {

    String sql = "";

    public List<DashBoard> obterEstatisticaPorDia(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            List<DashBoard> retorno = new ArrayList<>();

            sql = "SELECT\n"
                    + " CONVERT(INT, RIGHT(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),2)) DiaResumo,\n"
                    + " SUM(rtgerencial.Paradas) TotalParadas,\n"
                    + " SUM(rtgerencial.Guias) TotalGuias,\n"
                    + " COUNT(DISTINCT rtgerencial.Rota) TotalRotas,\n"
                    + " CAST(CAST(SUM(rtgerencial.Montante) AS DECIMAL(18,2)) AS VARCHAR(30)) AS TotalMontante,\n"
                    + " CAST(CAST(SUM(rtgerencial.KmTotal) AS DECIMAL(18,2)) AS VARCHAR(30)) AS TotalKm\n"
                    + " FROM rtgerencial\n"
                    + " WHERE LEFT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),1,7),4) = ?\n"
                    + " AND   RIGHT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),1,7),2) = ?\n"
                    + " AND   rtgerencial.CodFil = ?\n"
                    + " GROUP BY RIGHT(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),2)\n"
                    + " ORDER BY RIGHT(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),2)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Ano);
            consulta.setString(Mes);
            consulta.setString(CodFil);
            consulta.select();
            DashBoard dash;

            while (consulta.Proximo()) {
                dash = new DashBoard();
                dash.setDia(consulta.getString("DiaResumo"));
                dash.setQdeGuias(consulta.getString("TotalParadas"));
                dash.setQdeParadas(consulta.getString("TotalGuias"));
                dash.setQdeRotas(consulta.getString("TotalRotas"));
                dash.setTotalValorTransportado(consulta.getBigDecimal("TotalMontante"));
                dash.setTotalKmTransportado(consulta.getString("TotalKm"));

                retorno.add(dash);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DashBoardDao.obterEstatisticaPorDia - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<DashBoard> obterQdesPorRota(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            List<DashBoard> retorno = new ArrayList<>();

            sql = "SELECT\n"
                    + " rtgerencial.Rota,\n"
                    + " SUM(rtgerencial.Paradas) TotalParadas,\n"
                    + " CAST(CAST(SUM(rtgerencial.Montante) AS DECIMAL(18,2)) AS VARCHAR(30)) AS TotalMontante\n"
                    + " FROM rtgerencial\n"
                    + " WHERE LEFT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),1,7),4) = ?\n"
                    + " AND   RIGHT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), rtgerencial.Data, 102), '.', '-'),1,7),2) = ?\n"
                    + " AND   rtgerencial.CodFil = ?\n"
                    + " GROUP BY rtgerencial.Rota\n"
                    + " HAVING SUM(rtgerencial.Montante) > 0\n"
                    + " ORDER BY rtgerencial.Rota";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Ano);
            consulta.setString(Mes);
            consulta.setString(CodFil);
            consulta.select();
            DashBoard dash;

            while (consulta.Proximo()) {
                dash = new DashBoard();
                dash.setRota(consulta.getString("Rota"));
                dash.setTotalValorTransportado(consulta.getBigDecimal("TotalMontante"));
                dash.setQdeParadas(consulta.getString("TotalParadas"));

                retorno.add(dash);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DashBoardDao.obterQdesPorRota - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<DashBoard> obterQdeNovoTipoServico(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            List<DashBoard> retorno = new ArrayList<>();

            sql = "Select Nfiscal.CodFil, CONVERT(DECIMAL(10,2), Round(Sum(FatTvPlan.vEmb),2)) Embarque, CONVERT(DECIMAL(10,2), Round(Sum(FatTvPlan.Advalorem),2)) AdValorem, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vTempoEspera),2)) TempoEspera, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vMilheiros),2)) Milheiro, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vEnvelopes),2)) Envelopes,  CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vMalotes),2)) Malotes, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vCustodia),2)) Custodia, CONVERT(DECIMAL(10,2), round(sum(FatTvPlan.vKm),2)) KM, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.Total),2)) Total from NFiscal \n"
                    + "Left Join Clientes on Clientes.Codigo = NFiscal.Clifat\n"
                    + "                  and Clientes.CodFil = NFiscal.CodFil\n"
                    + "Left Join FatTvFechaNF  on FatTvFechaNF.Numero = NFiscal.NumCalculo\n"
                    + "                       and FatTvFechaNF.NF = NFiscal.Numero\n"
                    + "                       and FatTvFechaNF.Praca = NFiscal.Praca \n"
                    + "Left Join FatTvPlan on FatTvPlan.Numero = FatTvFechaNF.Numero\n"
                    + "                    and FatTvPlan.SeqNF = FatTvFechaNF.SeqNF\n"
                    + " WHERE LEFT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), NFiscal.Data, 102), '.', '-'),1,7),4) = ?\n"
                    + " AND   RIGHT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), NFiscal.Data, 102), '.', '-'),1,7),2) = ?\n"
                    + " AND   NFiscal.Situacao = 'A'\n"
                    + " AND   NFiscal.CodFil = ?\n"
                    + "Group by Nfiscal.CodFil";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Ano);
            consulta.setString(Mes);
            consulta.setString(CodFil);
            consulta.select();
            DashBoard dash;

            while (consulta.Proximo()) {
                dash = new DashBoard();
                dash.setRota("Embarques");
                dash.setTotalValorTransportado(consulta.getBigDecimal("Embarque"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("AdValorem");
                dash.setTotalValorTransportado(consulta.getBigDecimal("AdValorem"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("TempoEspera");
                dash.setTotalValorTransportado(consulta.getBigDecimal("TempoEspera"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("Milheiro");
                dash.setTotalValorTransportado(consulta.getBigDecimal("Milheiro"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("Envelopes");
                dash.setTotalValorTransportado(consulta.getBigDecimal("Envelopes"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("Malotes");
                dash.setTotalValorTransportado(consulta.getBigDecimal("Malotes"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("Custodia");
                dash.setTotalValorTransportado(consulta.getBigDecimal("Custodia"));
                dash.setQdeParadas("0");
                retorno.add(dash);

                dash = new DashBoard();
                dash.setRota("KM");
                dash.setTotalValorTransportado(consulta.getBigDecimal("KM"));
                dash.setQdeParadas("0");
                retorno.add(dash);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DashBoardDao.obterQdeNovoTipoServico - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<DashBoard> obterQdeNovoRamoAtiv(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            List<DashBoard> retorno = new ArrayList<>();

            sql = "Select Nfiscal.CodFil, RamosAtiv.Descricao, CONVERT(DECIMAL(10,2), Round(Sum(FatTvPlan.vEmb),2)) Embarque, CONVERT(DECIMAL(10,2), Round(Sum(FatTvPlan.Advalorem),2)) AdValorem, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vTempoEspera),2)) TempoEspera, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vMilheiros),2)) Milheiro, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vEnvelopes),2)) Envelopes,  CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vMalotes),2)) Malotes, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vCustodia),2)) Custodia, CONVERT(DECIMAL(10,2), round(sum(FatTvPlan.vKm),2)) KM, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.Total),2)) Total from NFiscal \n"
                    + "Left Join Clientes on Clientes.Codigo = NFiscal.Clifat\n"
                    + "                  and Clientes.CodFil = NFiscal.CodFil\n"
                    + "Left Join RamosAtiv on RamosAtiv.Codigo = Clientes.RamoAtiv\n"
                    + "Left Join FatTvFechaNF  on FatTvFechaNF.Numero = NFiscal.NumCalculo\n"
                    + "                       and FatTvFechaNF.NF = NFiscal.Numero\n"
                    + "                       and FatTvFechaNF.Praca = NFiscal.Praca \n"
                    + "Left Join FatTvPlan on FatTvPlan.Numero = FatTvFechaNF.Numero\n"
                    + "                    and FatTvPlan.SeqNF = FatTvFechaNF.SeqNF\n"
                    + " WHERE LEFT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), NFiscal.Data, 102), '.', '-'),1,7),4) = ?\n"
                    + " AND   RIGHT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), NFiscal.Data, 102), '.', '-'),1,7),2) = ?\n"
                    + " AND   NFiscal.Situacao = 'A'\n"
                    + " AND   NFiscal.CodFil = ?\n"
                    + "Group by Nfiscal.CodFil, RamosAtiv.Descricao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Ano);
            consulta.setString(Mes);
            consulta.setString(CodFil);
            consulta.select();
            DashBoard dash;

            while (consulta.Proximo()) {
                dash = new DashBoard();
                dash.setRota(consulta.getString("Descricao"));
                dash.setTotalValorTransportado(consulta.getBigDecimal("Total"));
                dash.setQdeParadas("0");
                retorno.add(dash);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DashBoardDao.obterQdeNovoRamoAtiv - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<DashBoard> obterQdeNovoClienteTipo(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            List<DashBoard> retorno = new ArrayList<>();

            sql = "Select Nfiscal.CodFil, \n"
                    + "Case when Clientes.TpCli = 0 then 'Banco'\n"
                    + "     when Clientes.TpCli = 3 then 'Pab'\n"
                    + "     when Clientes.TpCli = 4 then 'TA/Cofre'\n"
                    + "     when Clientes.TpCli = 5 then 'TAC'\n"
                    + "     when Clientes.TpCli = 6 then 'Clientes'\n"
                    + "     when Clientes.TpCli = 7 then 'Tesouraria'\n"
                    + "     when Clientes.TpCli = 8 then 'Transportadoras'\n"
                    + "     when Clientes.TpCli = 9 then 'ATM' end TipoCliente,\n"
                    + "CONVERT(DECIMAL(10,2), Round(Sum(FatTvPlan.vEmb),2)) Embarque, CONVERT(DECIMAL(10,2), Round(Sum(FatTvPlan.Advalorem),2)) AdValorem, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vTempoEspera),2)) TempoEspera, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vMilheiros),2)) Milheiro, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vEnvelopes),2)) Envelopes,  CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vMalotes),2)) Malotes, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.vCustodia),2)) Custodia, CONVERT(DECIMAL(10,2), round(sum(FatTvPlan.vKm),2)) KM, CONVERT(DECIMAL(10,2), round(Sum(FatTvPlan.Total),2)) Total from NFiscal \n"
                    + "Left Join FatTvFechaNF  on FatTvFechaNF.Numero = NFiscal.NumCalculo\n"
                    + "                       and FatTvFechaNF.NF = NFiscal.Numero\n"
                    + "                       and FatTvFechaNF.Praca = NFiscal.Praca \n"
                    + "Left Join FatTvPlan on FatTvPlan.Numero = FatTvFechaNF.Numero\n"
                    + "                    and FatTvPlan.SeqNF = FatTvFechaNF.SeqNF\n"
                    + "Left Join OS_Vig on  OS_Vig.OS = FatTvPlan.OS\n"
                    + "                 and OS_Vig.Codfil = FatTvPlan.CodFil\n"
                    + "Left Join Clientes on Clientes.Codigo = OS_Vig.Cliente\n"
                    + "                  and Clientes.CodFil = OS_Vig.CodFil\n"
                    + " WHERE LEFT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), NFiscal.Data, 102), '.', '-'),1,7),4) = ?\n"
                    + " AND   RIGHT(SUBSTRING(REPLACE(CONVERT(VARCHAR(10), NFiscal.Data, 102), '.', '-'),1,7),2) = ?\n"
                    + " AND   NFiscal.Situacao = 'A'\n"
                    + " AND   NFiscal.CodFil = ?\n"
                    + "  and Clientes.TpCli is not null\n"
                    + "Group by Nfiscal.CodFil, Clientes.TpCli\n"
                    + "order by TipoCliente";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Ano);
            consulta.setString(Mes);
            consulta.setString(CodFil);
            consulta.select();
            DashBoard dash;

            while (consulta.Proximo()) {
                dash = new DashBoard();
                dash.setRota(consulta.getString("TipoCliente"));
                dash.setTotalValorTransportado(consulta.getBigDecimal("Total"));
                dash.setQdeParadas("0");
                retorno.add(dash);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DashBoardDao.obterQdeNovoClienteTipo - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
}

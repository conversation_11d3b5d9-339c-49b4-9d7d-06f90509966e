<html>
<head>
    <title>SASw - Gerador de QR Code</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <script src="assets/js/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/scripts/jquery.qrcode.js"></script>
    <script src="assets/scripts/qrcode.js"></script>
    <link rel="icon" sizes="192x192" href="assets/logos/LogoSASEX.jpg" />
    <script src="http://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    
    <style>
        @-webkit-keyframes autofill {
            to {
                color: #000 !important;
                background: #FFF !important;
                background-color: #FFF !important;
            }
        }

        input:-webkit-autofill {
            -webkit-animation-name: autofill;
            -webkit-animation-fill-mode: both;
            color: #000 !important;
            background: #FFF !important;
            background-color: #FFF !important;
        }

        label {
            width: 100%;
            margin: 10px 0px 0px 0px !important;
        }

        #btGerar {
            width: 100%;
            margin-top: 31px;
            padding-top: 0px !important;
            padding-bottom: 0px !important;
            height: 32px !important;
        }

        .PalavraPx {
            position: absolute;
            background-color: #DDD;
            color: #777 !important;
            text-shadow: 1px 1px #FFF !important;
            width: 38px;
            right: 17px;
            text-align: center;
            border-radius: 0px 3px 3px 0px;
            padding: 5px !important;
            z-index: 999;
            top: -8px;
        }

        input {
            color: #000 !important;
            background: #FFF !important;
            background-color: #FFF !important;
        }

        #GerarQrCode #texto {
            white-space: nowrap;
            font-size: 10pt;
            color: #666;
            text-shadow: 1px 1px #FFF;
            font-weight: bold;
            margin-bottom: 18px;
            background-color: #DDD;
            border: thin solid #CCC;
            border-radius: 50px;
            padding: 5px 8px 5px 8px !important;
        }

        .LogoQr{
            position: absolute;
            width:80px;
            padding: 5px;
            border-radius: 50px;
            background-color: #FFF;
            top:0;
            right:0;
            bottom:0;
            left:0;
            margin: auto;
        }

    </style>
</head>
<body style="background: #FFF">
    <div class="col-md-12 col-sm-12 col-xs-12" style="        padding: 2px !important;
        background: #FFF;
        text-align: center;
        border-bottom: 2px solid #BBB;">
        <img src="assets/logos/LogoSASEX.jpg" style="height: 90px;" />
    </div>
    <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px 12px 15px 12px !important;
        background-color: #EEE; border-bottom: 2px solid #BBB;">
        <form id="frmQrCode">
            <div class="cod-md-8 col-sm-8 col-xs-12">
                <label>Nome do QR Code</label>
                <input type="text" id="txtNomeQr" class="form-control" required="required" autocomplete="off" />
            </div>
            <div class="cod-md-4 col-sm-4 col-xs-12">
                <label>Tamanho do QR Code <i style="font-weight: 500 !important;">(Pixels)</i></label>
                <div class="cod-md-6 col-sm-6 col-xs-6" style="padding-left:0px !important; position: relative;">
                    <input type="text" id="txtTamanhoLargura" class="form-control" placeholder="Largura" required="required" autocomplete="off"  />
                    <label class="PalavraPx">px</label>
                </div>
                <div class="cod-md-6 col-sm-6 col-xs-6" style="padding-right:0px !important; position: relative">
                    <input type="text" id="txtTamanhoAltura" class="form-control" placeholder="Altura" required="required" autocomplete="off"  />
                    <label class="PalavraPx" style="right: 2px;">px</label>
                </div>
            </div>
            <div class="cod-md-12 col-sm-12 col-xs-12">
                <label>Texto / Link / Valor - QR Code <i style="font-weight: 500 !important;">(O que dever&aacute; ser executado ao Scanear)</i></label>
                <input type="text" id="txtValorQr" class="form-control" required="required" autocomplete="off"  />
            </div>
            <div class="cod-md-10 col-sm-10 col-xs-12">
                <label>Logo <i style="font-weight: 500 !important;">(Caminho Completo)</i></label>
                <input type="text" id="txtLogo" class="form-control" />
            </div>
            <div class="cod-md-2 col-sm-2 col-xs-12">
                <button id="btGerar" class="btn btn-success" onsubmit="return false">Gerar QR Code</button>
            </div>
        </form>
    </div>
    <div id="GerarQrCode" class="col-md-12 col-sm-12 col-xs-12" style="display: none; text-align: center !important; margin-top: 50px;">
        <div id="divImgQr" style="background-color: #FFF; padding: 5px !important; width: auto">
            <center><div id="texto"></div></center>
            <center><div id="qr" style="position: relative"></div></center>
        </div>
        
        <a id="btGerarBase64" href="javascript:void(0);" class="btn btn-primary" style="margin-top: 30px !important;">Gerar Image/Base64</a>
        
        <textarea id="Texto64" rows="5" style="display: none; width: 100%; margin-top: 30px"></textarea>
    </div>
    <script>
        $(document)
            .on('submit', '#frmQrCode', function () {
                GerarQr();
                return false
            })
                .on('click', '#btGerarBase64', function(){
                html2canvas(document.querySelector("#divImgQr")).then(canvas => {
                    canvas.id = 'QrCodeGerado';
                    $('#Texto64').val(canvas.toDataURL('image/jpeg')).css('display','');
                    
                    //document.body.appendChild(canvas);
                });            
            })
            ;

        function GerarQr() {
            try {
                $('.LogoQr').remove();
                $('#Texto64').val('').css('display','none');
                $('#GerarQrCode #texto, #GerarQrCode #qr').html('');
                $('#GerarQrCode').css('display', '');
                $('#GerarQrCode #texto').css('max-width', eval($('#txtTamanhoLargura').val()) + 40 + 'px');

                $('#GerarQrCode #qr').qrcode({
                    width: eval($('#txtTamanhoLargura').val()),
                    height: eval($('#txtTamanhoAltura').val()),
                    text: $('#txtValorQr').val()
                });

                if ($('#txtLogo').val().trim() != '')
                    $('#GerarQrCode #qr').append('<img src="' + $('#txtLogo').val() + '" class="LogoQr" />');
                $('#GerarQrCode #texto').html($('#txtNomeQr').val());
            }
            catch (e) {
                $('#GerarQrCode').css('display', 'none');
                alert('Erro ao gerar QR Code.\n\nMensagem de Erro: ' + e.toString())
            }
        }
    </script>
</body>
</html>
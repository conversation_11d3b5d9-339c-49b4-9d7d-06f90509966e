/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasFat;

/**
 *
 * <AUTHOR>
 */
public class Rt_GuiasFatDao {

    /**
     * Busca as informações de pagamento para uma guia
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rt_GuiasFat obterRt_GuiasFatGuia(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasFat retorno = null;
            String sql = " SELECT Rt_GuiasFat.*, FormasPgto.Descricao \n"
                    + " FROM Rt_GuiasFat \n"
                    + " LEFT JOIN FormasPgto ON FormasPgto.Codigo = Rt_GuiasFat.FormaPgto \n"
                    + " WHERE Guia = ? AND Serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Rt_GuiasFat();
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setParada(consulta.getString("Parada"));
                retorno.setGuia(consulta.getString("Guia"));
                retorno.setSerie(consulta.getString("Serie"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setOS(consulta.getString("OS"));
                retorno.setEmbarques(consulta.getString("Embarques"));
                retorno.setValorEmb(consulta.getString("ValorEmb"));
                retorno.setValorAst(consulta.getString("ValorAst"));
                retorno.setValorAdv(consulta.getString("ValorAdv"));
                retorno.setValorTot(consulta.getString("ValorTot"));
                retorno.setFormaPgto(consulta.getString("FormaPgto"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));

                retorno.setFormaPgtoDescricao(consulta.getString("Descricao"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasFatDao.obterRt_GuiasFatGuia - " + e.getMessage() + "\r\n"
                    + " SELECT Rt_GuiasFat.*, FormasPgto.Descricao \n"
                    + " FROM Rt_GuiasFat \n"
                    + " LEFT JOIN FormasPgto ON FormasPgto.Codigo = Rt_GuiasFat.FormaPgto \n"
                    + " WHERE Guia = " + guia + " AND Serie = " + serie);
        }
    }

    /**
     * Busca as informações de pagamento para uma guia
     *
     * @param sequencia
     * @param parada
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rt_GuiasFat obterRt_GuiasFat(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasFat retorno = null;
            String sql = " SELECT Rt_GuiasFat.*, FormasPgto.Descricao \n"
                    + " FROM Rt_GuiasFat \n"
                    + " LEFT JOIN FormasPgto ON FormasPgto.Codigo = Rt_GuiasFat.FormaPgto \n"
                    + " WHERE Sequencia = ? AND Parada = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Rt_GuiasFat();
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setParada(consulta.getString("Parada"));
                retorno.setGuia(consulta.getString("Guia"));
                retorno.setSerie(consulta.getString("Serie"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setOS(consulta.getString("OS"));
                retorno.setEmbarques(consulta.getString("Embarques"));
                retorno.setValorEmb(consulta.getString("ValorEmb"));
                retorno.setValorAst(consulta.getString("ValorAst"));
                retorno.setValorAdv(consulta.getString("ValorAdv"));
                retorno.setValorTot(consulta.getString("ValorTot"));
                retorno.setFormaPgto(consulta.getString("FormaPgto"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));

                retorno.setFormaPgtoDescricao(consulta.getString("Descricao"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasFatDao.obterRt_GuiasFat - " + e.getMessage() + "\r\n"
                    + " SELECT Rt_GuiasFat.*, FormasPgto.Descricao \n"
                    + " FROM Rt_GuiasFat \n"
                    + " LEFT JOIN FormasPgto ON FormasPgto.Codigo = Rt_GuiasFat.FormaPgto \n"
                    + " WHERE Sequencia = " + sequencia + " AND Parada = " + parada);
        }
    }

    public void inserirRt_GuiasFat(Rt_GuiasFat rt_GuiasFat, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Rt_GuiasFat(Sequencia, Parada, Guia, Serie, CodFil, OS, Embarques, ValorEmb, \n"
                    + " ValorAst, ValorAdv, ValorTot, FormaPgto, Obs, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rt_GuiasFat.getSequencia());
            consulta.setString(rt_GuiasFat.getParada());
            consulta.setString(rt_GuiasFat.getGuia());
            consulta.setString(rt_GuiasFat.getSerie());
            consulta.setString(rt_GuiasFat.getCodFil());
            consulta.setString(rt_GuiasFat.getOS());
            consulta.setString(rt_GuiasFat.getEmbarques());
            consulta.setString(rt_GuiasFat.getValorEmb());
            consulta.setString(rt_GuiasFat.getValorAst());
            consulta.setString(rt_GuiasFat.getValorAdv());
            consulta.setString(rt_GuiasFat.getValorTot());
            consulta.setString(rt_GuiasFat.getFormaPgto());
            consulta.setString(rt_GuiasFat.getObs());
            consulta.setString(rt_GuiasFat.getOperador());
            consulta.setString(rt_GuiasFat.getDt_Alter());
            consulta.setString(rt_GuiasFat.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasFatDao.inserirRt_GuiasFat - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Rt_GuiasFat(Sequencia, Parada, Guia, Serie, CodFil, OS, Embarques, ValorEmb, \n"
                    + " ValorAst, ValorAdv, ValorTot, FormaPgto, Obs, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (" + rt_GuiasFat.getSequencia() + ", " + rt_GuiasFat.getParada() + ", " + rt_GuiasFat.getGuia() + ", "
                    + rt_GuiasFat.getSerie() + ", " + rt_GuiasFat.getCodFil() + ", " + rt_GuiasFat.getOS() + ", " + rt_GuiasFat.getEmbarques() + ", "
                    + rt_GuiasFat.getValorEmb() + ", " + rt_GuiasFat.getValorAst() + ", " + rt_GuiasFat.getValorAdv() + ", "
                    + rt_GuiasFat.getValorTot() + ", " + rt_GuiasFat.getFormaPgto() + ", " + rt_GuiasFat.getObs() + ", " + rt_GuiasFat.getOperador() + ", "
                    + rt_GuiasFat.getDt_Alter() + ", " + rt_GuiasFat.getHr_Alter() + ")");
        }
    }

}

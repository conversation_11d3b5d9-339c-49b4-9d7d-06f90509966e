/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1210;
import SasBeans.ESocial.S1210.DetPgtoBenPr;
import SasBeans.ESocial.S1210.DetPgtoFer;
import SasBeans.ESocial.S1210.DetPgtoFl;
import SasBeans.ESocial.S1210.DetRubrFer;
import SasBeans.ESocial.S1210.InfoPgto;
import SasBeans.ESocial.S1210.PenAlim;
import SasBeans.ESocial.S1210.RetPgtoTot;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1210Dao {

    public List<S1210> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S1210> retorno = new ArrayList<>();
            String sql = " Select 1 ideEvento_indRetif,\n "
                    //+ " Case when FPMensal.TipoFP = '132' then 2 else 1 end ideEvento_indApuracao,\n "
                    + " 1 ideEvento_indApuracao, \n "
                    + " Case when (Select count(*) from FPRescisoes (nolock) where FPRescisoes.CodMovFP = FPMEnsal.CodMovFP and FPRescisoes.Matr = FPMensal.Matr and FPRescisoes.Motivo not in ('80')) > 0 then (Select Top 1 Replace(Substring(Convert(Varchar,DtPagto,111),1,7),'/','-') from FPRescisoes (nolock) where FPRescisoes.CodMovFP = FPMEnsal.CodMovFP and FPRescisoes.Matr = FPMensal.Matr and FPRescisoes.Motivo not in ('80')) \n "
                    + "      when FPMensal.TipoFP = '132' then ('20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)) "
                    //+ "      else Substring(Replace(Convert(varchar,DateAdd(Day, 31, Convert(Date,'20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPMensal.CodMovFP),3,2)+'-01')),111),'/','-'),1,7) end ideEvento_perApur, \n " Substring(Replace(Convert(varchar,(Convert(Date,'20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPMensal.CodMovFP),3,2)+'-01')),111),'/','-'),1,7) end
                    + "      else Substring(Replace(Convert(varchar,(Convert(Date,'20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPMensal.CodMovFP),3,2)+'-01')),111),'/','-'),1,7) end ideEvento_perApur, \n " 
                    + " Filiais.TipoPessoa ideEmpregador_tpInsc,\n "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,  \n "
                    + " Funcion.CPF ideBenef_cpfBenef,\n "
                    + " Funcion.Nome ideBenef_nome,\n "
                    + " convert(varchar,convert(BigInt,Funcion.Matr)) ideBenef_matr,\n "
                    + " Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end ideBenef_matrCodPonto,\n "
                    + " Case when Funcion.vinculo in ('J','M') then '103' "
                    + "      when Funcion.vinculo = 'D'  and Filiais.Descricao not like '%Agil%' then '721' "
                    + "      when Funcion.Vinculo = 'S'  and Filiais.Descricao not like '%Agil%' then '723' "
                    + "      when Funcion.Vinculo = 'E' then '901' "
                    + "      when Funcion.Vinculo = 'A' then '701' "
                    + "      when Funcion.TrabIntermitente = 'S' then '111' "
                    + "      else '101' end dmDev_codCateg, "
                    + " (Select max(DeducaoDep) from FPIRRF where DtValidade >= FPPeriodos.DtInicio)*FPMensal.DepIR deps_vrDedDep,\n "
                    + " (select max(sucesso) from  ( \n "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n "
                    + "         From XmleSocial z (nolock)  \n "
                    + "         where z.Identificador = Funcion.CPF\n "
                    + "             and z.evento = 'S-1210' \n "
                    + "             and z.Compet = ? \n "
                    + "             and z.Ambiente = ? \n "
                    + "             and z.CodFil = ? \n"
                    + "             and (z.Xml_Retorno like '%aguardando%' \n "
                    + "                     or z.Xml_Retorno = ''\n "
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) \n "
                    + " union \n "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n "
                    + "         From XmleSocial z (nolock) \n "
                    + "         where z.Identificador = Funcion.CPF\n "
                    + "             and z.evento = 'S-1210' \n "
                    + "             and z.Compet = ? \n "
                    + "             and z.Ambiente = ? \n "
                    + "             and z.CodFil = ? \n"
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' \n "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n "
                    + " union \n "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n "
                    + "         From XmleSocial z (nolock) \n "
                    + "         where z.Identificador = Funcion.CPF\n "
                    + "             and z.evento = 'S-1210' \n "
                    + "             and z.Compet = ? \n "
                    + "             and z.Ambiente = ? \n "
                    + "             and z.CodFil = ? \n"
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%')"
                    + " union \n "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n "
                    + "         From XmleSocial z (nolock) \n "
                    + "         where z.Identificador = Funcion.CPF\n "
                    + "             and z.evento = 'S-1210' \n "
                    + "             and z.Compet = ? \n "
                    + "             and z.Ambiente = ? \n "
                    + "             and z.CodFil = ? \n"
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' ))a) Sucesso \n"
                    + " from FPMensal (nolock) \n "
                    + " Left join Filiais (nolock) on Filiais.CodFil = FPMensal.CodFil\n "
                    + " Left join Funcion (nolock) on Funcion.Matr = FPMensal.Matr \n "
                    + " Left join FPPeriodos (nolock) on FPPeriodos.CodMovFP = FPMensal.CodMovFP\n ";
            if (compet.contains("-13")) {
                sql = sql + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n "
                        + "   and FPMensal.TipoFP = '132'\n";
            } else {
                sql = sql + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n "
                        + "   and (FPMensal.Liquido > 0 or FPMensal.matr in (Select FPRescisoes.Matr "
                        + " from FPRescisoes (nolock) "
                        + " Left join FPMensal z  on FPRescisoes.CodMovFP = z.CodMovFP "
                        + "                      and FPRescisoes.Matr = z.Matr "
                        + "                      and z.TipoFP = 'RES' "
                        + " where FPRescisoes.CodMovFP = FPMensal.CodMovFP "
                        + "   and z.Liquido > 0 )) "
                        + "   and FPMensal.TipoFP in ('MEN','AUT') \n";
            }
            sql = sql + "   and FPMensal.CodFil = ? \n "
                   //   + " and FPMensal.Matr = 113 "
                    + " Order by FPMensal.Matr ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);

            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);

            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);

            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            S1210 s1210;
            RetPgtoTot retPgtoTot;
            PenAlim penAlim;
            DetRubrFer detRubrFer;
            InfoPgto infoPgto;
            DetPgtoFl detPgtoFl;
            DetPgtoFer detPgtoFer;
            int indice, indice2, indice3;
            while (consulta.Proximo()) {
                s1210 = new S1210();

                s1210.setSucesso(consulta.getInt("sucesso"));

                s1210.setIdeEvento_procEmi("1");
                s1210.setIdeEvento_verProc("Satellite eSocial");
                s1210.setIdeEvento_indRetif(consulta.getString("ideEvento_indRetif"));
                s1210.setIdeEvento_indApuracao(consulta.getString("ideEvento_indApuracao"));
                s1210.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));
                s1210.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1210.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                s1210.setDeps_vrDedDep(consulta.getString("deps_vrDedDep"));
                if ((persistencia.getEmpresa().equals("SATB_RASIFORT")) || (persistencia.getEmpresa().equals("SATTRA_NSPORTER")) || (persistencia.getEmpresa().equals("SATS_EFIX"))) {
                    s1210.setIdeBenef_matr(consulta.getString("ideBenef_matrCodPonto"));
                } else {
                    s1210.setIdeBenef_matr(consulta.getString("ideBenef_matr"));
                }
                s1210.setIdeBenef_nome(consulta.getString("ideBenef_nome"));
                s1210.setIdeBenef_infoPgto(new ArrayList<>());

                s1210.setDmDev_codCateg(consulta.getString("dmDev_codCateg"));

                retorno.add(s1210);
            }

            sql = " Select Funcion.CPF ideBenef_cpfBenef, Convert(BigInt,Funcion.Matr) Matr, \n"
                    + " case when FPRescisoes.DtPagto is not null and FPMensal.TipoFP <> 'MEN' then Replace(Convert(Varchar,FPRescisoes.DtPagto,111),'/','-')\n"
                    + "       when FPFerias.DtPagto is not null and FPMensal.TipoFP <> 'MEN' then Replace(Convert(Varchar,FPFerias.DtPagto,111),'/','-')\n"
                    + "  	 else '' end infoPgto_dtPgto,\n"
                    + "  Case when FPMensal.TipoFP = 'MEN' then '1' \n"
                    + "       when FPMensal.TipoFP = '132' then '1' \n"
                    + "       when FPMensal.TipoFP = 'AUT' then '1' \n"
                    + "       when FPMensal.TipoFP = 'RES' then '2' \n"
                    + "       when FPMensal.TipoFP = 'FER' then '7' \n"
                    + "       when FPMensal.TipoFP = 'FEJ' then '7' end infoPgto_tpPgto,\n"
                    + "  'S' infoPgto_indResBr\n"
                    + "FROM fpmensal (nolock) \n"
                    + "LEFT JOIN funcion (nolock) on funcion.matr = fpmensal.matr\n"
                    + "Left join FPRescisoes (nolock) on FPRescisoes.Matr     = FPMensal.Matr\n"
                    + "                      and FPRescisoes.CodMovFP = FPMensal.CodMovFP\n"
                    + "Left join FPFerias (nolock) on FpFerias.Matr     = FPMensal.Matr\n"
                    + "                   and FpFerias.CodMovFP = FPMensal.CodMovFP\n";
            if (compet.contains("-13")) {
                sql = sql + "Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n"
                        + "    and FPMensal.TipoFP in ('132') ";
            } else {
                sql = sql + "Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n"
                        + "    and FPMensal.TipoFP in ('MEN', 'FER_Desativado', 'RES','AUT') ";
            }

            sql = sql + "    and FPMensal.codfil = ? "
                    + "    and FPMEnsal.Liquido > 0 ";

            consulta = new Consulta(sql, persistencia);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                s1210 = new S1210();

                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                s1210.setIdeBenef_matr(consulta.getString("Matr"));

                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_dtPgto(consulta.getString("infoPgto_dtPgto"));
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));
                    infoPgto.setInfoPgto_indResBr(consulta.getString("infoPgto_indResBr"));

                    infoPgto.setInfoPgto_detPgtoFl(new DetPgtoFl());
                    infoPgto.setInfoPgto_detPgtoBenPr(new DetPgtoBenPr());
                    infoPgto.setInfoPgto_detPgtoFer(new DetPgtoFer());
                    retorno.get(indice).getIdeBenef_infoPgto().add(infoPgto);
                }
            }

            sql = " Select Funcion.CPF ideBenef_cpfBenef, Convert(BigInt,Funcion.Matr) Matr, \n "
                    + " Case when FPMensal.TipoFP = 'MEN' then '1' \n "
                    + "      when FPMensal.TipoFP = '132' then '1' \n "
                    + "      when FPMensal.TipoFP = 'AUT' then '1' \n "
                    + "      when FPMensal.TipoFP = 'RES' then '2' \n "
                    + "      when FPMensal.TipoFP = 'FER' then '7' end infoPgto_tpPgto,\n "
                    + " Case when FPMensal.TipoFP = '132' then '20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2) "
                    + " else Substring(Replace(Convert(varchar,DateAdd(Day, 1, Convert(Date,'20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPMensal.CodMovFP),3,2)+'-01')),111),'/','-'),1,7) end detPgtoFl_perRef,\n "
                    + //" Case when FPMensal.TipoFP = 'FER' then '' else FPMensal.TipoFP+Convert(Varchar,FPMensal.CodMovFP) end detPgtoFl_ideDmDev,\n " +
                    " FPMensal.TipoFP+Convert(Varchar,FPMensal.CodMovFP) detPgtoFl_ideDmDev,\n "
                    + " Case when FPMensal.TipoFP = 'FER' then '' else 'S' end detPgtoFl_indPgtoTt,\n "
                    + " Case when FPMensal.TipoFP = 'FER' then '' else FPMensal.Liquido end detPgtoFl_vrLiq,\n "
                    + " Case when FPMensal.TipoFP = 'RES' then (Select top 1 \n"
                    + "case when CHARINDEX('<nrRecibo>', Xml_Retorno) > 0 then Substring(Xml_Retorno,CHARINDEX('<nrRecibo>', Xml_Retorno)+10,CHARINDEX('</nrRecibo>', Xml_Retorno)-(CHARINDEX('<nrRecibo>', Xml_Retorno)+10)) end \n"
                    + "from XmlEsocial (nolock) \n"
                    + "where XmlEsocial.Evento = 'S-2299' \n"
                    + "  and XmlEsocial.identificador = FPMensal.Matr \n"
                    + "  and Xml_Retorno like '%<nrRecibo>%'\n"
                    + "Order by XmlEsocial.Sequencia Desc) else '' end detPgtoFl_nRecArq\n "
                    + " from FPMensal (nolock) \n "
                    + " Left join Funcion on funcion.Matr = FPMensal.Matr \n "
                    + "                  and funcion.codfil = FPMensal.codfil\n ";
            if (compet.contains("-13")) {
                sql = sql + "Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n"
                        + "    and FPMensal.TipoFP in ('132') ";
            } else {
                sql = sql + "Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n"
                        + "    and FPMensal.TipoFP in ('MEN', 'FER_Desativado', 'RES', 'AUT') ";
            }

            sql = sql + "   and FPMensal.codfil = ? ";

            consulta = new Consulta(sql, persistencia);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                s1210.setIdeBenef_matr(consulta.getString("Matr"));

                // Busca do funcionário
                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));

                    // Verifica se já existe folha ou recisão
                    indice2 = retorno.get(indice).getIdeBenef_infoPgto().indexOf(infoPgto);

                    if (indice2 >= 0) {
                        detPgtoFl = new DetPgtoFl();
                        detPgtoFl.setDetPgtoFl_perRef(consulta.getString("detPgtoFl_perRef"));
                        detPgtoFl.setDetPgtoFl_ideDmDev(consulta.getString("detPgtoFl_ideDmDev"));
                        detPgtoFl.setDetPgtoFl_indPgtoTt(consulta.getString("detPgtoFl_indPgtoTt"));
                        detPgtoFl.setDetPgtoFl_vrLiq(consulta.getString("detPgtoFl_vrLiq"));
                        detPgtoFl.setDetPgtoFl_nrRecArq(consulta.getString("detPgtoFl_nRecArq"));
                        detPgtoFl.setDetPgtoFl_retPgtoTot(new ArrayList<>());

                        retorno.get(indice).getIdeBenef_infoPgto().get(indice2).setInfoPgto_detPgtoFl(detPgtoFl);
                    }
                }
            }

            sql = " Select Convert(BigInt, Funcion.Matr) Matr, Funcion.CPF ideBenef_cpfBenef,\n "
                    + " Case when FPLancamentos.TipoFP = 'MEN' then '1' \n "
                    + "      when FPLancamentos.TipoFP = '132' then '1'\n "
                    + "      when FPLancamentos.TipoFP = 'AUT' then '1'\n "
                    + "      when FPLancamentos.TipoFP = 'RES' then '2'\n "
                    + "      when FPLancamentos.TipoFP = 'FER' then '7'\n "
                    + "      when FPLancamentos.TipoFP = 'FEJ' then '7' end infoPgto_tpPgto,\n "
                    + " FPLancamentos.Verba retPgtoTot_codRubr,\n "
                    + " FPLancamentos.Verba retPgtoTot_ideTabRubr,\n "
                    + " FPLancamentos.ValorCalc retPgtoTot_vrRubr\n "
                    + " from FPLancamentos (nolock) \n "
                    + " Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP "
                    + "                   and FPMensal.TipoFP = FPLancamentos.TipoFP "
                    + "                   and FPMensal.Matr = FPLancamentos.Matr "
                    + " Left join Verbas (nolock)  on Verbas.Verba = FPLancamentos.Verba\n "
                    + " Left join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr\n "
                    + "                 and Funcion.CodFil = FPLancamentos.CodFil\n ";
            if (compet.contains("-13")) {
                sql = sql + " Where Convert(Varchar,FPLancamentos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n "
                        + " and FPLancamentos.TipoFP = '132' ";
            } else {
                sql = sql + " Where Convert(Varchar,FPLancamentos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n "
                        + " and FPLancamentos.TipoFP <> '132' ";
            }

            sql = sql + "   and FPLancamentos.ValorCalc > 0\n "
                    + "   and Verbas.Formula in('0221','0222','0223','0225','0226','0003','0013','0023','0033') \n"
                    + "   and FPMensal.Liquido > 0 "
                    + "   and FPLancamentos.CodFil = ? ; ";
            consulta = new Consulta(sql, persistencia);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                s1210.setIdeBenef_matr(consulta.getString("Matr"));

                // Busca do funcionário
                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));

                    // Verifica se já existe folha ou recisão
                    indice2 = retorno.get(indice).getIdeBenef_infoPgto().indexOf(infoPgto);

                    if (indice2 >= 0) {
                        retPgtoTot = new RetPgtoTot();
                        retPgtoTot.setRetPgtoTot_codRubr(consulta.getString("retPgtoTot_codRubr"));
                        retPgtoTot.setRetPgtoTot_ideTabRubr(consulta.getString("retPgtoTot_ideTabRubr"));
                        retPgtoTot.setRetPgtoTot_vrRubr(consulta.getString("retPgtoTot_vrRubr"));
                        retPgtoTot.setRetPgtoTot_penAlim(new ArrayList<>());

                        retorno.get(indice).getIdeBenef_infoPgto().get(indice2).getInfoPgto_detPgtoFl().getDetPgtoFl_retPgtoTot().add(retPgtoTot);
                    }
                }

            }

            sql = " Select Funcion.CPF ideBenef_cpfBenef, Convert(BigInt, FPLancamentos.Matr) Matr, \n "
                    + " Case when FPLancamentos.TipoFP = 'MEN' then '1' \n "
                    + "      when FPLancamentos.TipoFP = '132' then '1' \n "
                    + "      when FPLancamentos.TipoFP = 'AUT' then '1'\n "
                    + "      when FPLancamentos.TipoFP = 'RES' then '2' \n "
                    + "      when FPLancamentos.TipoFP = 'FER' then '7' \n "
                    + "      when FPLancamentos.TipoFP = 'FEJ' then '7' end infoPgto_tpPgto,\n "
                    + " FPLancamentos.Verba retPgtoTot_codRubr,\n "
                    + " FPLancamentos.Verba retPgtoTot_ideTabRubr,\n "
                    + " F5_Dep.CPF penAlim_cpfBenef,\n "
                    + " Replace(Convert(varchar,F5_Dep.Dt_Nasc,111),'/','-') penAlim_dtNasctoBenef,\n "
                    + " F5_Dep.Nome penAlim_nmBenefic,\n "
                    + " Sum(FPLancamentos.ValorCalc) penAlim_vlrPensao\n "
                    + " from FPLancamentos (nolock) \n "
                    + " Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP "
                    + "                   and FPMensal.TipoFP = FPLancamentos.TipoFP "
                    + "                   and FPMensal.Matr = FPLancamentos.Matr "
                    + " Left join F5_Dep (nolock) on F5_Dep.Matr = FPLancamentos.Matr\n "
                    + "                  and F5_Dep.VerbaPens = FPLancamentos.Verba\n "
                    + " Left join Verbas (nolock) on Verbas.Verba = FPLancamentos.Verba\n "
                    + " Left Join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr\n "
                    + "                  and Funcion.Codfil = FPLancamentos.CodFil\n ";

            if (compet.contains("-13")) {
                sql = sql + " Where Convert(Varchar,FPLancamentos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n "
                        + " and FPLancamentos.TipoFP = '132' ";
            } else {
                sql = sql + " Where Convert(Varchar,FPLancamentos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n "
                        + " and FPLancamentos.TipoFP <> '132' ";
            }
            sql = sql + "      and FPLancamentos.codfil = ? \n "
                    + "      and Verbas.Formula in ('0221', '0222', '0223', '0225', '0226')\n "
                    + "   and FPMensal.Liquido > 0 "
                    //                    + " and FPMEnsal.Matr = 1057 "
                    + " Group by Funcion.CPF, FPLancamentos.Matr, FPLancamentos.Verba,\n "
                    + "  FPLancamentos.TipoFP, FPLancamentos.CodMovFP, F5_Dep.CPF, F5_Dep.Dt_Nasc, F5_Dep.Nome;\n ";
            consulta = new Consulta(sql, persistencia);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                s1210.setIdeBenef_matr(consulta.getString("Matr"));

                // Busca do funcionário
                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));

                    // Verifica se já existe folha ou recisão
                    indice2 = retorno.get(indice).getIdeBenef_infoPgto().indexOf(infoPgto);

                    if (indice2 >= 0) {
                        retPgtoTot = new RetPgtoTot();
                        retPgtoTot.setRetPgtoTot_codRubr(consulta.getString("retPgtoTot_codRubr"));
                        retPgtoTot.setRetPgtoTot_ideTabRubr(consulta.getString("retPgtoTot_ideTabRubr"));

                        indice3 = retorno.get(indice).getIdeBenef_infoPgto().get(indice2).getInfoPgto_detPgtoFl().getDetPgtoFl_retPgtoTot().indexOf(retPgtoTot);

                        if (indice3 >= 0) {
                            penAlim = new PenAlim();
                            penAlim.setPenAlim_cpfBenef(consulta.getString("penAlim_cpfBenef"));
                            penAlim.setPenAlim_dtNasctoBenef(consulta.getString("penAlim_dtNasctoBenef"));
                            penAlim.setPenAlim_nmBenefic(consulta.getString("penAlim_nmBenefic"));
                            penAlim.setPenAlim_vlrPensao(consulta.getString("penAlim_vlrPensao"));
                            penAlim.setPenAlim_matr(consulta.getString("Matr"));

                            retorno.get(indice).getIdeBenef_infoPgto() // busca o funcionário
                                    .get(indice2).getInfoPgto_detPgtoFl().getDetPgtoFl_retPgtoTot() //busca as verbas
                                    .get(indice3).getRetPgtoTot_penAlim().add(penAlim); // adiciona a pensão
                        }
                    }
                }
            }

            sql = " Select Funcion.CPF ideBenef_cpfBenef,\n "
                    + " Case when FPMensal.TipoFP = 'MEN' then '1' \n "
                    + "      when FPMensal.TipoFP = '132' then '1' \n "
                    + "      when FPMensal.TipoFP = 'RES' then '2' \n "
                    + "      when FPMensal.TipoFP = 'FER' then '7' \n "
                    + "      when FPMensal.TipoFP = 'FEJ' then '7' end infoPgto_tpPgto,\n "
                    + " Case when Funcion.FormaPgto = 'H' then '111' \n "
                    + "      when Funcion.vinculo in ('E','J','M') then '901' \n "
                    + "      when Funcion.Vinculo in ('A') then '701' \n "
                    + "      else '101' end detPgtoFer_codCateg,\n "
                    + " convert(varchar,convert(BigInt,Funcion.Matr)) detPgtoFer_matricula,\n "
                    + " Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end detPgtoFer_matriculaCodPonto,\n "
                    + " Replace(Convert(Varchar,FPFerias.DtInicioFer,111),'/','-') detPgtoFer_dtIniGoz,\n "
                    + " FPFerias.DiasFerias detPgtoFer_qtDias,\n "
                    + " FPMensal.Liquido detPgtoFer_vrLiq\n "
                    + " from FPMensal (nolock) \n "
                    + " Left join Funcion (nolock) on FPMensal.Matr = Funcion.Matr\n "
                    + " Left join FPFerias (nolock) on FpFerias.Matr     = FPMensal.Matr \n "
                    + "                    and FpFerias.CodMovFP = FPMensal.CodMovFP\n "
                    + " Where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','')\n "
                    + "   and FPMensal.CodFil = ?\n "
                    + "   and FPMensal.Liquido > 0\n "
                    + "   and FPMensal.TipoFP = 'FER_Desativado' ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {

                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));

                // Busca do funcionário
                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));

                    // Verifica se já existe folha ou recisão
                    indice2 = retorno.get(indice).getIdeBenef_infoPgto().indexOf(infoPgto);

                    if (indice2 >= 0) {
                        detPgtoFer = new DetPgtoFer();
                        detPgtoFer.setDetPgtoFer_codCateg(consulta.getString("detPgtoFer_codCateg"));
                        if ((persistencia.getEmpresa().equals("SATBRAS_IFORT")) || (persistencia.getEmpresa().equals("SATTRAN_SPORTER")) || (persistencia.getEmpresa().equals("SATS_EFIX"))) {
                            detPgtoFer.setDetPgtoFer_matricula(consulta.getString("detPgtoFer_matriculaCodPonto"));
                        } else {
                            detPgtoFer.setDetPgtoFer_matricula(consulta.getString("detPgtoFer_matricula"));
                       }
                        detPgtoFer.setDetPgtoFer_dtIniGoz(consulta.getString("detPgtoFer_dtIniGoz"));
                        detPgtoFer.setDetPgtoFer_qtDias(consulta.getString("detPgtoFer_qtDias"));
                        detPgtoFer.setDetPgtoFer_dtIniGoz(consulta.getString("detPgtoFer_dtIniGoz"));
                        detPgtoFer.setDetPgtoFer_vrLiq(consulta.getString("detPgtoFer_vrLiq"));
                        detPgtoFer.setDetPgtoFer_detRubrFer(new ArrayList<>());

                        retorno.get(indice).getIdeBenef_infoPgto().get(indice2).setInfoPgto_detPgtoFer(detPgtoFer);
                    }
                }
            }

            sql = " Select Funcion.CPF ideBenef_cpfBenef,\n "
                    + " Case when FPLancamentos.TipoFP = 'MEN' then '1' \n "
                    + "      when FPLancamentos.TipoFP = '132' then '1' \n "
                    + "      when FPLancamentos.TipoFP = 'RES' then '2' \n "
                    + "      when FPLancamentos.TipoFP = 'FER' then '7' \n "
                    + "      when FPLancamentos.TipoFP = 'FEJ' then '7' end infoPgto_tpPgto,\n "
                    + " Case when Verbas.Formula = '0002' then '9'+Verbas.Verba else Verbas.Verba end detRubrFer_codRubr,\n "
                    + " Case when Verbas.Formula = '0002' then '9'+Verbas.Verba else Verbas.Verba end  detRubrFer_ideTabRubr,\n "
                    + " FPLancamentos.ValorCalc  detRubrFer_vrRubr\n "
                    + " from FPLancamentos (nolock) \n "
                    + " Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP "
                    + "                   and FPMensal.TipoFP = FPLancamentos.TipoFP "
                    + "                   and FPMensal.Matr = FPLancamentos.Matr "
                    + " Left join Verbas (nolock) on Verbas.Verba = FPLancamentos.Verba\n "
                    + " Left Join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr\n "
                    + "                 and Funcion.CodFil = FPLancamentos.CodFil\n "
                    + " Where '20'+Substring(Convert(Varchar,FPLancamentos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPLancamentos.CodMovFP),3,2) = ?\n "
                    + "   and FPLancamentos.CodFil = ?\n "
                    + //"   and Verbas.Formula in ('0221', '0222', '0223', '0225', '0226') \n" +
                    "   and FPLancamentos.TipoFP = 'FER_Desativado' \n "
                    + "   and FPMensal.Liquido > 0 "
                    + "   and FPLancamentos.ValorCalc > 0; ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));

                // Busca do funcionário
                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));

                    // Verifica se já existe folha ou recisão
                    indice2 = retorno.get(indice).getIdeBenef_infoPgto().indexOf(infoPgto);

                    if (indice2 >= 0) {
                        detRubrFer = new DetRubrFer();
                        detRubrFer.setDetRubrFer_codRubr(consulta.getString("detRubrFer_codRubr"));
                        detRubrFer.setDetRubrFer_ideTabRubr(consulta.getString("detRubrFer_ideTabRubr"));
                        detRubrFer.setDetRubrFer_vrRubr(consulta.getString("detRubrFer_vrRubr"));
                        detRubrFer.setDetRubrFer_penAlim(new ArrayList<>());

                        retorno.get(indice).getIdeBenef_infoPgto().get(indice2).getInfoPgto_detPgtoFer().getDetPgtoFer_detRubrFer().add(detRubrFer);
                    }
                }
            }

            sql = " Select Funcion.CPF ideBenef_cpfBenef, Funcion.Matr, \n "
                    + " Case when FPLancamentos.TipoFP = 'MEN' then '1' \n "
                    + "      when FPLancamentos.TipoFP = '132' then '1' \n "
                    + "      when FPLancamentos.TipoFP = 'RES' then '2' \n "
                    + "      when FPLancamentos.TipoFP = 'FER' then '7' \n "
                    + "      when FPLancamentos.TipoFP = 'FEJ' then '7' end infoPgto_tpPgto,\n "
                    + " FPLancamentos.Verba detRubrFer_codRubr,\n "
                    + " FPLancamentos.Verba  detRubrFer_ideTabRubr,\n "
                    + " F5_Dep.CPF penAlim_cpfBenef,\n "
                    + " F5_Dep.Dt_Nasc penAlim_dtNasctoBenef,\n "
                    + " F5_Dep.Nome penAlim_nmBenefic,\n "
                    + " FPLancamentos.ValorCalc penAlim_vlrPensao\n "
                    + " from FPLancamentos (nolock) \n "
                    + " Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP "
                    + "                   and FPMensal.TipoFP = FPLancamentos.TipoFP "
                    + "                   and FPMensal.Matr = FPLancamentos.Matr "
                    + " Left join F5_Dep (nolock) on F5_Dep.Matr = FPLancamentos.Matr\n "
                    + "                  and F5_Dep.VerbaPens = FPLancamentos.Verba\n "
                    + " Left join Verbas (nolock) on Verbas.Verba = FPLancamentos.Verba\n "
                    + " Left join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr\n "
                    + "                  and Funcion.CodFil = FPLancamentos.CodFil\n "
                    + " Where Convert(Varchar,FPLancamentos.CodMovFP) = REPLACE(RIGHT(?,5),'-','')\n "
                    + "   and FPLancamentos.CodFil = ?\n "
                    + "   and FPLancamentos.TipoFP = 'FER_Desativado'\n "
                    + "   and FPMensal.Liquido > 0 "
                    + "   and Verbas.Formula in ('0221', '0222', '0223', '0225', '0226'); ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {

                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                s1210.setIdeBenef_matr(consulta.getString("Matr"));

                // Busca do funcionário
                indice = retorno.indexOf(s1210);

                if (indice >= 0) {
                    infoPgto = new InfoPgto();
                    infoPgto.setInfoPgto_tpPgto(consulta.getString("infoPgto_tpPgto"));

                    // Verifica se já existe  férias
                    indice2 = retorno.get(indice).getIdeBenef_infoPgto().indexOf(infoPgto);

                    if (indice2 >= 0) {
                        detRubrFer = new DetRubrFer();
                        detRubrFer.setDetRubrFer_codRubr(consulta.getString("detRubrFer_codRubr"));
                        detRubrFer.setDetRubrFer_ideTabRubr(consulta.getString("detRubrFer_ideTabRubr"));

                        indice3 = retorno.get(indice).getIdeBenef_infoPgto().get(indice2)
                                .getInfoPgto_detPgtoFer().getDetPgtoFer_detRubrFer().indexOf(detRubrFer);

                        if (indice3 >= 0) {
                            penAlim = new PenAlim();
                            penAlim.setPenAlim_cpfBenef(consulta.getString("penAlim_cpfBenef"));
                            penAlim.setPenAlim_dtNasctoBenef(consulta.getString("penAlim_dtNasctoBenef"));
                            penAlim.setPenAlim_nmBenefic(consulta.getString("penAlim_nmBenefic"));
                            penAlim.setPenAlim_vlrPensao(consulta.getString("penAlim_vlrPensao"));

                            retorno.get(indice).getIdeBenef_infoPgto() // busca o funcionário
                                    .get(indice2).getInfoPgto_detPgtoFer().getDetPgtoFer_detRubrFer() //busca a verba das férias
                                    .get(indice3).getDetRubrFer_penAlim().add(penAlim); // adiciona a pensão
                        }
                    }
                }
            }

            consulta.Close();
            
            sql = " Select Funcion.CPF ideBenef_cpfBenef, Funcion.Matr, \n "
                    + " FPLancamentos.Valor planSaude_vlrSaudeTit, \n "
                    + " Fornec.CNPJ planSaude_cnpjOper\n "
                    + " from FPLancamentos (nolock) \n "
                    + " Left join FPMensal (nolock) on FPMensal.CodMovFP = FPLancamentos.CodMovFP \n"
                    + "                   and FPMensal.TipoFP = FPLancamentos.TipoFP \n"
                    + "                   and FPMensal.Matr = FPLancamentos.Matr \n"
                    + " Left join Verbas (nolock) on Verbas.Verba = FPLancamentos.Verba\n "
                    + " Left join Fornec (nolock) on Verbas.CodForn = Fornec.Codigo\n "
                    + " Left join Funcion (nolock) on Funcion.Matr = FPLancamentos.Matr\n "
                    + "                  and Funcion.CodFil = FPLancamentos.CodFil\n "
                    + " Where Convert(Varchar,FPLancamentos.CodMovFP) = REPLACE(RIGHT(?,5),'-','')\n "
                    + "   and FPLancamentos.CodFil = ?\n "
                    //+ "   and FPLancamentos.TipoFP = 'FER_Desativado'\n "
                    + "   and Verbas.PlanoSaude = 'S' \n"
                    + "   and Verbas.NatEsocial = 9219 \n";
            consulta = new Consulta(sql, persistencia); 
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                s1210 = new S1210();
                s1210.setIdeBenef_cpfBenef(consulta.getString("ideBenef_cpfBenef"));
                // Remover '.0' da matrícula
                s1210.setIdeBenef_matr(consulta.getString("Matr").replace(".0", ""));
                // Busca do funcionário
                indice = retorno.indexOf(s1210);
                if (indice >= 0) {
                    S1210.PlanSaude planSaude = new S1210.PlanSaude();
                    planSaude.setPlanSaude_vlrSaudeTit(consulta.getString(
                            "planSaude_vlrSaudeTit"));
                    planSaude.setPlanSaude_cnpjOper(consulta.getString(
                            "planSaude_cnpjOper"));
                    
                    // Atualiza plano de saúde
                    retorno.get(indice).setPlanSaude(planSaude);
                }
            }
            consulta.Close();
            
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1210Dao.get - " + e.getMessage());
        }
    }
}

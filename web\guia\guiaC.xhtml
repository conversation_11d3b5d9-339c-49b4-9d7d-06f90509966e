<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:f="http://xmlns.jcp.org/jsf/core">
    <h:head>
        <link type="text/css" href="../assets/css/guias.css" rel="stylesheet" />
    </h:head>
    <ui:composition>
        <div >
            <table id="cabecalho"  class="cabecalhoNota">
                <tbody>
                    <tr style="text-align: center">
                        <td rowspan="4">
                            <img src="#{login.getLogo(login.pp.getEmpresa())}" height="47px" width="59px"/>
                        </td>
                        <td colspan="3">
                            <h:outputText value="#{clientes.filialGuia.razaoSocial}" class="negrito"/>
                        </td>
                    </tr>
                    <tr style="text-align: center">
                        <td colspan="3">
                            <h:outputText value="#{clientes.filialGuia.endereco}"/>,<h:outputText value="#{clientes.filialGuia.bairro}"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="text-align: center">
                            <h:outputText value="#{clientes.filialGuia.cidade}"/>/<h:outputText value="#{clientes.filialGuia.UF}"/>
                            &nbsp;-&nbsp;
                            <h:outputText value="#{clientes.filialGuia.CEP}" converter="conversorCEP"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.CGC}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{clientes.filialGuia.CNPJ}" converter="conversorCNPJ"/>
                        </td>
                        <td style="text-align: right">
                            <h:outputText value="#{localemsgs.Telefone}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{clientes.filialGuia.fone}" converter="conversorFone"/>
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="filialOS" class="cabecalhoNota">
                <tbody>
                    <tr style="text-align: center">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.GETV}" class="negrito"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localemsgs.Filial}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.codfil}"/>
                        </td>
                        <td rowspan="2" style="text-align: right">
                            <p:graphicImage value="#{barCodeMB.image}">
                                <f:param name="barcode" value="#{clientes.codBarras}" />
                            </p:graphicImage>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localemsgs.OS}: " class="campoCabecalho"/>
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.OS}"/>
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="cliente" class="datagrid">
                <tbody>
                    <tr class="caixa">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.ClienteP}" class="negrito"/> 
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <h:outputText value="#{clientes.faturamento.nome}" class="cliente"/>
                        </td>
                    </tr>            
                    <tr>                
                        <td  colspan="2">
                            <h:outputText value="#{localemsgs.Endereco}: " style="font-style: italic"/>
                            &nbsp;
                            <h:outputText value="#{clientes.faturamento.ende}" />
                        </td>            
                    </tr>  
                    <tr>                
                        <td  colspan="2">
                            <h:outputText value="#{clientes.faturamento.bairro}, #{clientes.faturamento.cidade}/#{clientes.faturamento.estado}"/>
                        </td>            
                    </tr> 
                    <tr>
                        <td width="50%">
                            <h:outputText value="#{localemsgs.CGC}: " class="campo"/>
                            &nbsp;
                            <h:outputText value="#{clientes.faturamento.CGC}" converter="conversorCNPJ"/>
                        </td>  
                        <td width="50%">
                            <h:outputText value="#{localemsgs.InscEstadual}: " class="campo"/>
                            &nbsp;  
                            <h:outputText value="#{clientes.faturamento.IE}" />
                        </td>
                    </tr>   
                    </tbody>   
            </table>
            <table id="origem" class="datagrid">
                <tbody>
                    <tr class="caixa">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.DadosOrigem}" class="negrito"/> 
                        </td>
                        <td>
                            <h:outputText value="#{clientes.origem.agencia}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="20%">
                            <h:outputText value="#{localemsgs.Remetente}: " class="campo"/> 
                        </td>
                        <td colspan="2">
                            <h:outputText value="#{clientes.origem.NRed}" class="cliente"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h:outputText value="#{localemsgs.Endereco}: " class="campo"/> 
                        </td>
                        <td colspan="2">
                            <h:outputText value="#{clientes.origem.ende}"/>
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="enderecoOrigem" class="datagrid">
                <tbody>
                    <tr>
                        <td width="33%">
                            <h:outputText value="#{clientes.origem.bairro}"/>
                        </td>
                        <td style="text-align: center" width="33%">
                            <h:outputText value="#{clientes.origem.cidade}"/>
                        </td>
                        <td style="text-align: right; padding-right: 4px !important" width="33%">
                            <h:outputText value="#{localemsgs.UF}: " class="campo"/> 
                            <h:outputText value="#{clientes.origem.estado}"/>
                        </td> 
                    </tr>
                </tbody>   
            </table>
            <table id="dadosOrigem" class="datagrid">
                <tbody>
                    <tr class="caixa">
                        <td>
                            <h:outputText value="#{localemsgs.Data}: " class="campo"/>
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.data}" converter="conversorData"/>
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Veiculo}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.veiculo}" converter="conversor0"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Rota}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.rota}" class="negrito" converter="conversor0"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Chegada}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.horaChegada}"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Saida}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.horaSaida}"/> 
                        </td>
                    </tr>
                </tbody>   
            </table>
            <table id="destino" class="datagrid">
                <tbody>
                    <tr class="caixaAcima">  
                        <td colspan="3">
                            <h:outputText value="#{localemsgs.DadosDestino}" class="negrito"/> 
                        </td>       
                    </tr>   
                    <tr>    
                        <td width="20%">
                            <h:outputText value="#{localemsgs.Destinatario}: " class="campo"/> 
                        </td>
                        <td colspan="2">
                            <h:outputText value="#{clientes.destino.NRed}" class="cliente"/> 
                        </td>   
                    </tr>   
                    <tr>    
                        <td>
                            <h:outputText value="#{localemsgs.Endereco}: " class="campo"/> 
                        </td>
                        <td colspan="2">
                            <h:outputText value="#{clientes.destino.ende}"/>
                        </td>    
                    </tr>
                </tbody>   
            </table>
            <table id="enderecoDestino" class="datagrid">
                <tbody>
                    <tr>      
                        <td width="33%">
                            <h:outputText value="#{clientes.destino.bairro}"/>
                        </td> 
                        <td style="text-align: center;" width="33%">
                            <h:outputText value="#{clientes.destino.cidade}"/>
                        </td>
                        <td style="text-align: right; padding-right: 4px !important" width="33%">
                            <h:outputText value="#{localemsgs.UF}:" class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.destino.estado}"/>
                        </td> 
                    </tr>
                </tbody>   
            </table>
            <table id="dadosDestino" class="datagrid">
                <tbody>
                    <tr class="caixa"> 
                        <td>
                            <h:outputText value="#{localemsgs.Data}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.data}" converter="conversorData"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Veiculo}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.veiculo}" converter="conversor0"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Chegada}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.hora1}" converter="conversorHora"/> 
                        </td>
                        <td>
                            <h:outputText value="#{localemsgs.Saida}: " class="campo"/> 
                            &nbsp;
                            ***
                        </td>        
                    </tr>
                </tbody>   
            </table>
            <table id="detalhes" class="datagrid">
                <tbody>
                    <tr class="caixaAcima">   
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.DiscriminacaoValorIdentificacao}" class="negrito"/> 
                        </td>       
                    </tr>        
                    <tr>         
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.ValorDeclarado}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.valor}" class="negrito" converter="conversormoeda"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="preencheLinha" colspan="2">
                            <h:outputText value="#{clientes.extenso}"/>
                        </td>  
                    </tr>
                    <tr class="caixa">
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.IdentificacaoMalote}" class="negrito"/> 
                        </td>  
                    </tr>      
                    <tr>       
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.Lacres}:" class="campo"/> 
                        </td>     
                    </tr>    
                    <c:forEach var="lacre" items="#{clientes.lacres}">
                        <tr>       
                            <td colspan="2">
                                <h:outputText value="#{lacre.lacre}"/>
                            </td>
                        </tr>    
                    </c:forEach>
                        
                    <tr class="caixa">
                        <td style="text-align: justify; padding-right: 4px !important;" colspan="2">
                            <h:outputText value="&emsp; #{localemsgs.TermoAssGuia}" class="campo"/>
                        </td>     
                    </tr>     
                    <tr>     
                        <td style="border-right: 1px solid black ;vertical-align: baseline;" width="50%">
                            <h:outputText value="#{localemsgs.AssRemetente}: " class="campo"/> 
                        </td>
                        <td style="vertical-align: baseline;" width="50%" height="40px" rowspan="2">
                            <h:outputText value="#{localemsgs.AssDestinatario}:" class="campo"/> 
                        </td>       
                    </tr>
                    <tr>     
                        <td style="border-right: 1px solid black;vertical-align: baseline;" width="50%">
                            <h:outputText value="#{clientes.guiaSelecionada.codPessoaAut}"/>
                        </td>      
                    </tr>
                    <tr class="caixa">     
                        <td colspan="2">
                            <h:outputText value="#{localemsgs.Obs}: " class="campo"/> 
                            &nbsp;
                            <h:outputText value="#{clientes.guiaSelecionada.observ}"/> 
                        </td>                
                    </tr>                   
                </tbody>
            </table>
        </div>
    </ui:composition>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.containers;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.Rotas;
import br.com.sasw.lazydatamodels.containers.RotasLazyList;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.Numeros.isNumeric;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "rotasContainer")
@ViewScoped
public class RotasContainerMB implements Serializable {

    private final RotasSatWeb rotasSatWeb;
    private Persistencia persistencia, satellite;
    private LazyDataModel<Rotas> rotas = null;
    private Map filtersRota;
    private Filiais filiais;
    private String codFil, dataTela, caminho, log, operador, banco,
            pesquisaRotaRota, pesquisaRotaVeiculo, pesquisaRotaMotorista, pesquisaRotaChEquip;
    private BigDecimal codpessoa;
    private ArquivoLog logerro;
    private static Date ultimoDia;
    private int total;

    public RotasContainerMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");

        dataTela = getDataAtual("SQL");
        ultimoDiadoMes();

        rotasSatWeb = new RotasSatWeb();

        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
    }

    public void Persistencia(Persistencia pp, Persistencia ss) {
        try {
            this.persistencia = pp;
            this.satellite = ss;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            if (null == this.satellite) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.codFil = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial");

            this.pesquisaRotaRota = "";
            this.pesquisaRotaVeiculo = "";
            this.pesquisaRotaMotorista = "";
            this.pesquisaRotaChEquip = "";

            this.filtersRota = new HashMap();
            this.filtersRota.put(" Rotas.Flag_excl <> ? ", Arrays.asList("*"));
            this.filtersRota.put(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList("*"));
            this.filtersRota.put(" Rotas.CodFil = ? ", Arrays.asList(this.codFil));
            this.filtersRota.put(" Rotas.Data = ? ", Arrays.asList(this.dataTela));

            this.filtersRota.put(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList());

            this.filtersRota.put(" Escala.matrche = ? ", Arrays.asList());
            this.filtersRota.put(" Funcion.nome like ? ", Arrays.asList());

            this.filtersRota.put(" Escala.matrmot = ? ", Arrays.asList());
            this.filtersRota.put(" mot.nome like ? ", Arrays.asList());

            this.filtersRota.put(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            this.filtersRota.put(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());

            this.filiais = this.rotasSatWeb.buscaInfoFilial(this.codFil, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<Rotas> getAllRotas() {
        if (this.rotas == null) {
            this.filtersRota.replace(" Rotas.Flag_excl <> ? ", Arrays.asList("*"));
            this.filtersRota.replace(" (Rt_Perc.Flag_excl <> ? OR Rt_Perc.Flag_excl IS NULL) ", Arrays.asList("*"));
            this.filtersRota.replace(" Rotas.CodFil = ? ", Arrays.asList(this.codFil));
            this.filtersRota.replace(" Rotas.Data = ? ", Arrays.asList(this.dataTela));

            this.filtersRota.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList());

            this.filtersRota.replace(" Escala.matrche = ? ", Arrays.asList());
            this.filtersRota.replace(" Funcion.nome = ? ", Arrays.asList());

            this.filtersRota.replace(" Escala.matrmot = ? ", Arrays.asList());
            this.filtersRota.replace(" mot.nome like ? ", Arrays.asList());

            this.filtersRota.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            this.filtersRota.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            this.rotas = new RotasLazyList(this.persistencia, this.filtersRota);
        } else {
            ((RotasLazyList) this.rotas).setFilters(this.filtersRota);
        }
        try {
            this.total = this.rotasSatWeb.contagemRotasContainers(this.filtersRota, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.rotas;
    }

    public void selecionarData(SelectEvent data) {
        this.dataTela = (String) data.getObject();
        this.filtersRota.replace(" Rotas.Data = ? ", Arrays.asList(this.dataTela));
        getAllRotas();
    }

    public void rotaAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filtersRota.replace(" Rotas.Data = ? ", Arrays.asList(this.dataTela));
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void rotaPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filtersRota.replace(" Rotas.Data = ? ", Arrays.asList(this.dataTela));
            getAllRotas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void pesquisar() {

        if (this.pesquisaRotaRota != null && !this.pesquisaRotaRota.equals("") && isNumeric(this.pesquisaRotaRota)) {
            this.filtersRota.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList(this.pesquisaRotaRota));
        } else {
            this.pesquisaRotaRota = "";
            this.filtersRota.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList());
        }

        if (this.pesquisaRotaChEquip == null || this.pesquisaRotaChEquip.equals("")) {
            this.filtersRota.replace(" Escala.matrche = ? ", Arrays.asList());
            this.filtersRota.replace(" Funcion.nome like ? ", Arrays.asList());
        } else if (isNumeric(this.pesquisaRotaChEquip)) {
            this.filtersRota.replace(" Escala.matrche = ? ", Arrays.asList(this.pesquisaRotaChEquip));
            this.filtersRota.replace(" Funcion.nome like ? ", Arrays.asList("%" + this.pesquisaRotaChEquip + "%"));
        } else {
            this.filtersRota.replace(" Escala.matrche = ? ", Arrays.asList());
            this.filtersRota.replace(" Funcion.nome like ? ", Arrays.asList("%" + this.pesquisaRotaChEquip + "%"));
        }

        if (this.pesquisaRotaMotorista == null || this.pesquisaRotaMotorista.equals("")) {
            this.filtersRota.replace(" Escala.matrmot = ? ", Arrays.asList());
            this.filtersRota.replace(" mot.nome like ? ", Arrays.asList());
        } else if (isNumeric(this.pesquisaRotaMotorista)) {
            this.filtersRota.replace(" Escala.matrmot = ? ", Arrays.asList(this.pesquisaRotaMotorista));
            this.filtersRota.replace(" mot.nome like ? ", Arrays.asList("%" + this.pesquisaRotaMotorista + "%"));
        } else {
            this.filtersRota.replace(" Escala.matrmot = ? ", Arrays.asList());
            this.filtersRota.replace(" mot.nome like ? ", Arrays.asList("%" + this.pesquisaRotaMotorista + "%"));
        }

        if (this.pesquisaRotaVeiculo == null || this.pesquisaRotaVeiculo.equals("")) {
            this.filtersRota.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            this.filtersRota.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
        } else if (isNumeric(this.pesquisaRotaVeiculo)) {
            this.filtersRota.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
            this.filtersRota.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList(this.pesquisaRotaVeiculo, "%" + this.pesquisaRotaVeiculo + "%", "%" + this.pesquisaRotaVeiculo + "%"));
        } else {
            this.filtersRota.put(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList("%" + this.pesquisaRotaVeiculo + "%", "%" + this.pesquisaRotaVeiculo + "%"));
            this.filtersRota.put(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
        }

        getAllRotas();
    }

    public void limparPesquisa() {

        this.pesquisaRotaRota = "";
        this.filtersRota.replace(" CONVERT(BigInt, Rotas.Rota) = ? ", Arrays.asList());

        this.pesquisaRotaChEquip = "";
        this.filtersRota.replace(" Escala.matrche = ? ", Arrays.asList());
        this.filtersRota.replace(" Funcion.nome like ? ", Arrays.asList());

        this.pesquisaRotaMotorista = "";
        this.filtersRota.replace(" Escala.matrmot = ? ", Arrays.asList());
        this.filtersRota.replace(" mot.nome like ? ", Arrays.asList());

        this.pesquisaRotaVeiculo = "";
        this.filtersRota.replace(" (veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());
        this.filtersRota.replace(" (escala.veiculo = ? OR veiculos.placa like ? OR VeiculosMod.Descricao like ?) ", Arrays.asList());

        getAllRotas();
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            RotasContainerMB.ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public String getPesquisaRotaRota() {
        return pesquisaRotaRota;
    }

    public void setPesquisaRotaRota(String pesquisaRotaRota) {
        this.pesquisaRotaRota = pesquisaRotaRota;
    }

    public String getPesquisaRotaVeiculo() {
        return pesquisaRotaVeiculo;
    }

    public void setPesquisaRotaVeiculo(String pesquisaRotaVeiculo) {
        this.pesquisaRotaVeiculo = pesquisaRotaVeiculo;
    }

    public String getPesquisaRotaMotorista() {
        return pesquisaRotaMotorista;
    }

    public void setPesquisaRotaMotorista(String pesquisaRotaMotorista) {
        this.pesquisaRotaMotorista = pesquisaRotaMotorista;
    }

    public String getPesquisaRotaChEquip() {
        return pesquisaRotaChEquip;
    }

    public void setPesquisaRotaChEquip(String pesquisaRotaChEquip) {
        this.pesquisaRotaChEquip = pesquisaRotaChEquip;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}

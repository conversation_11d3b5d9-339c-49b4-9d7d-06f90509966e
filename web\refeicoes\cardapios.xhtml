<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/pessoas.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }
                }

                #divCalendario{
                    margin-top: -13px !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{cardapio.Persistencias(login.pp)}"/>
                <f:viewAction action="#{cardapio.setDiaDeHoje()}"/>
                <f:viewAction action="#{cardapio.carregarListaCardapioDia()}"/>
                <f:viewAction action="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_satmob_cardapiododia.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina" style="margin-top:4px !important;">#{localemsgs.CardapioDia}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{cardapio.dataInicio}" converter="conversorDate" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{cardapio.dataFim}" converter="conversorDate"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{cardapio.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{cardapio.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{cardapio.filiais.bairro}, #{cardapio.filiais.cidade}/#{cardapio.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="ui-grid-col-3" style="align-self: center; text-align: center;">
                                    <p:commandLink action="#{cardapio.vaDataAnterior}"  update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 40px"/>  
                                    </p:commandLink>

                                    <p:commandLink id="calendar" oncomplete="PF('oCalendarios').loadContents();"
                                                   styleClass="botao" update="main cabecalho msgs" >
                                        <p:graphicImage url="../assets/img/icone_escaladodia.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>

                                    <p:commandLink action="#{cardapio.vaDataPosterior}">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 40px"/>
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}" onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey
                        bind="p"
                        oncomplete="PF('dlgPesquisaRapida').show();"
                        actionListener="#{cardapio.prepararPesquisa('formPesquisaRapida', 'dlgPesquisaRapida')}"
                        update="formPesquisaRapida cabecalho"/>

                    <p:hotkey bind="shift+x" oncomplete="PF('dlgExportar').show();" actionListener="#{exportarMB.setTitulo(localemsgs.Pessoas)}"/>
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <!--<p:panel style="overflow:hidden !important; max-width:100% !important; min-height:100% !important; height:100% !important;max-height:100% !important; position:relative;">-->
                                <p:panel style="display: inline">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{cardapio.allCardapioDia}"
                                        paginator="true"
                                        rows="50"
                                        reflow="true"
                                        rowsPerPageTemplate="50"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.CardapioDia}"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        var="lista"
                                        rowKey="#{lista.sequencia}"
                                        selectionMode="single"
                                        styleClass="tabela"
                                        selection="#{cardapio.cardapioDiaDietaCardapioSelecionado}"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        scrollable="true"
                                        class="tabela DataGrid"
                                        scrollHeight="100%"
                                        style="font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax
                                            event="rowDblselect"
                                            listener="#{cardapio.preEdicao()}"
                                            update="msgs formCadastrar:cadastrar"/>
                                        <p:column headerText="#{localemsgs.Codigo}" class="text-center">
                                            <h:outputText value="#{lista.sequencia}" class="text-center" converter="conversor0" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CodigoCardapio}" class="text-center">
                                            <h:outputText value="#{lista.cardapio.codigo}" class="text-center" converter="conversor0" />
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Data}" class="text-center">
                                            <h:outputText value="#{lista.data}" class="text-center" converter="conversorData"/>
                                        </p:column>

                                        <p:column headerText="#{localemsgs.Cliente}" class="text-center">
                                            <h:outputText value="#{lista.clientes.NRed}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Periodo}" class="text-center">
                                            <h:outputText value="#{lista.periodo == 'A'? localemsgs.Almoco:lista.periodo == 'J'?localemsgs.Janta: lista.periodo == 'CA'?localemsgs.CafeManha :localemsgs.Ceia}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dieta}" class="text-center">
                                            <h:outputText value="#{lista.dieta.descricao}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cardapio}">
                                            <h:outputText value="#{lista.cardapio.descricao}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Especificacao}">
                                            <h:outputText value="#{lista.cardapio.especificacao}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Obs}">
                                            <h:outputText value="#{lista.especificacao}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                            <h:outputText value="#{lista.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                            <h:outputText value="#{lista.dt_alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                            <h:outputText value="#{lista.hr_Alter}" converter="conversorHora"/>
                                        </p:column>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 110px !important; background: transparent; height:200px !important;" id="botoes">
                        <p:remoteCommand name="execExclusao" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs main" 
                                         actionListener="#{cardapio.excluirCardapioDia()}" />  

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{cardapio.preCadastro()}"
                                           update="formCadastrar:cadastrar formCadastrar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}"
                                           actionListener="#{cardapio.preEdicao()}"
                                           update="msgs formCadastrar:cadastrar">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{cardapio.buttonActionExcluirCardapioDia}"
                                           update="msgs formCadastrar:cadastrar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           oncomplete="PF('dlgPesquisaRapida').show();"
                                           actionListener="#{cardapio.prepararPesquisa('formPesquisaRapida', 'dlgPesquisaRapida')}"
                                           update="formPesquisaRapida cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{cardapio.limparFiltros()}"
                                           update="msgs main:tabela cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>

                </h:form>

                <!--Cadastrar novo-->
                <h:form class="form-inline" id="formCadastrar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute"  focus="data" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.CardapioDia}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 20px; top:20px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; padding-right:0px !important; padding-left:8px !important; margin-top:10px !important" class="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-3, ui-grid-col-7"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="data" value="#{localemsgs.Data}: "/>

                                <p:datePicker id="data" value="#{cardapio.cardapioDiaDietaCardapioNovo.data}" readonlyInput="true"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                              monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                              converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                </p:datePicker>
                            </p:panelGrid>


                            <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank"  style="background-color:#EEE">
                                <p:outputLabel for="cliente" value="#{localemsgs.Cliente}:"/>
                                <p:autoComplete id="cliente" value="#{cardapio.clientes}" styleClass="cliente"
                                                style="width: 100%"
                                                completeMethod="#{pessoa.ListarClientes}" required="true" scrollHeight="200"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" forceSelection="true"
                                                var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}" converter="conversorCliente">
                                    <p:ajax event="itemSelect" listener="#{cardapio.SelecionarCliente}"
                                            update="nomeCliente"/>
                                    <p:watermark for="cliente" value="#{localemsgs.Cliente}" />
                                </p:autoComplete>

                                <p:inputText id="nomeCliente" value="#{cardapio.clientes.nome}" disabled="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}" required="true"
                                             style="width: 100%">
                                    <p:watermark for="nomeCliente" value="#{localemsgs.Cliente}" />
                                </p:inputText>
                            </p:panelGrid>



                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="periodo" value="#{localemsgs.Periodo}: "/>

                                <p:selectOneMenu id="periodo" converter="omnifaces.SelectItemsConverter" required="true" style="width: 100%;" value="#{cardapio.cardapioDiaDietaCardapioNovo.periodo}">
                                    <f:selectItem itemLabel="#{localemsgs.CafeManha}" itemValue="CA" />
                                    <f:selectItem itemLabel="#{localemsgs.Almoco}" itemValue="A" />
                                    <f:selectItem itemLabel="#{localemsgs.Janta}" itemValue="J" />
                                    <f:selectItem itemLabel="#{localemsgs.Ceia}" itemValue="CE" />
                                </p:selectOneMenu>

                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="dieta" value="#{localemsgs.Dieta}: "/>

                                <p:selectOneMenu
                                    id="dieta"
                                    value="#{cardapio.cardapioDiaDietaCardapioNovo.dieta}"
                                    converter="omnifaces.SelectItemsConverter"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dieta}"
                                    style="width: 100%;"
                                    filter="true"
                                    filterMatchMode="contains">
                                    <f:selectItems
                                        value="#{pessoa.listaDietas}"
                                        var="dietaItem"
                                        itemValue="#{dietaItem}"
                                        itemLabel="#{dietaItem.descricao}"
                                        noSelectionValue="Selecione"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="cardapio" value="#{localemsgs.Cardapio}: "/>

                                <p:selectOneMenu
                                    id="cardapio"
                                    value="#{cardapio.cardapioDiaDietaCardapioNovo.cardapio}"
                                    converter="omnifaces.SelectItemsConverter"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cardapio}"
                                    style="width: 100%"
                                    filter="true"
                                    filterMatchMode="contains" >
                                    <f:selectItem
                                        itemLabel="#{localemsgs.Selecione}" itemValue="#{cardapio.cardapioVazio}"/>

                                    <f:selectItems
                                        value="#{cardapio.cardapiosCadastrados}"
                                        var="cardapioItem"
                                        itemValue="#{cardapioItem}"
                                        itemLabel="#{cardapioItem.descricao}" />
                                    <p:ajax event="itemSelect"
                                            update="formCadastrar:descricao_cardapio" />
                                </p:selectOneMenu>

                                <p:outputLabel for="descricao_cardapio" value="#{localemsgs.Especificacao}: "/>
                                <p:inputText id="descricao_cardapio"
                                             value="#{cardapio.cardapioDiaDietaCardapioNovo.cardapio.especificacao}"
                                             disabled="true"
                                             label="#{localemsgs.Especificacao}"
                                             style="width: 100%"
                                             maxlength="255">
                                    <p:watermark for="descricao_cardapio" value="#{localemsgs.Especificacao}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}: "/>
                                <p:inputText id="obs" value="#{cardapio.cardapioDiaDietaCardapioNovo.especificacao}"
                                             label="#{localemsgs.Obs}" style="width: 100%"
                                             maxlength="60">
                                    <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="margin-top:6px; padding-left:0px !important">
                                <p:commandLink
                                    title="#{localemsgs.Salve}"
                                    id="cadastrarDieta"
                                    action="#{cardapio.salvarCardapioDia('dlgCadastrar')}"
                                    update="main:tabela msgs"
                                    style="width:100%">
                                    <label class="btn btn-lg btn-success" style="width:100% !important;margin-left: 0px;"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                                </p:commandLink>
                            </p:panelGrid>


                        </p:panel>
                    </p:dialog>

                    <!--CalendárioS de período-->
                    <p:overlayPanel
                        id="calendarios"
                        for="cabecalho:calendar"
                        hideEffect="fade"
                        dynamic="true"
                        style="font-size: 14px;"
                        widgetVar="oCalendarios"
                        my="top"
                        at="bottom"
                        class="overlay"
                        >
                        <div class="ui-grid-row ui-grid-responsive">
                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row" style="margin: 5px">
                                    <h:outputText id="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}"/>
                                </div>
                                <div class="ui-grid-row" style="margin: 5px">
                                    <p:calendar
                                        id="calendario1"
                                        styleClass="calendario"
                                        value="#{cardapio.dataInicio}"
                                        mask="true"
                                        title="#{localemsgs.DataInicial}"
                                        label="#{localemsgs.DataInicial}"
                                        pattern="#{mascaras.getPadraoDataS()}"
                                        locale="#{localeController.getCurrentLocale()}"
                                        >
                                        <p:ajax
                                            event="dateSelect"
                                            listener="#{cardapio.onDateSelect()}"
                                            update="main cabecalho msgs" />
                                    </p:calendar>
                                </div>
                            </div>

                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row" style="margin: 5px">
                                    <h:outputText id="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}"/>
                                </div>
                                <div class="ui-grid-row" style="margin: 5px">
                                    <p:calendar
                                        id="calendario2"
                                        styleClass="calendario"
                                        value="#{cardapio.dataFim}"
                                        mask="true"
                                        title="#{localemsgs.DataFinal}"
                                        label="#{localemsgs.DataFinal}"
                                        pattern="#{mascaras.getPadraoDataS()}"
                                        locale="#{localeController.getCurrentLocale()}"
                                        >
                                        <p:ajax
                                            event="dateSelect"
                                            listener="#{cardapio.onDateSelect()}"
                                            update="main cabecalho msgs" />
                                    </p:calendar>
                                </div>
                            </div>
                        </div>
                    </p:overlayPanel>
                </h:form>

                <!-- Pesquisar rápida -->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pessoas.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarCardapioDia}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="panelPesquisaRapida" style="background: transparent">

                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{cardapio.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="CODIGO" />
                                        <f:selectItem itemLabel="#{localemsgs.CodigoCardapio}" itemValue="CODCARDAPIO" />
                                        <f:selectItem itemLabel="#{localemsgs.Dieta}" itemValue="DIETA" />
                                        <f:selectItem itemLabel="#{localemsgs.Cardapio}" itemValue="CARDAPIO" />
                                        <f:selectItem itemLabel="#{localemsgs.Especificacao}" itemValue="ESPECIFICACAO" />
                                        <f:selectItem itemLabel="#{localemsgs.OBS}" itemValue="OBSERVACAO" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'CODIGO'}" value="#{localemsgs.Codigo}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'CODCARDAPIO'}" value="#{localemsgs.CodigoCardapio}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'DIETA'}" value="#{localemsgs.Dieta}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'CARDAPIO'}" value="#{localemsgs.Cardapio}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'ESPECIFICACAO'}" value="#{localemsgs.Especificacao}: "/>
                                        <p:outputLabel for="opcao" rendered="#{cardapio.chavePesquisa eq 'OBSERVACAO'}" value="#{localemsgs.OBS}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{cardapio.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{cardapio.pesquisarCardapioDia()}"
                                               update=" :main:tabela :msgs cabecalho"
                                               oncomplete="PF('dlgPesquisaRapida').hide()"
                                               title="#{localemsgs.Pesquisar}">
                                    <label class="btn btn-lg btn-primary" style="width: 100% !important; margin-top: 10px !important">
                                        <i class="fa fa-search" />
                                        &nbsp;#{localemsgs.Pesquisar}
                                    </label>
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>

            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>
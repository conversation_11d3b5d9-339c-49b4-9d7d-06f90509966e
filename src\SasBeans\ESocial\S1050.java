/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S1050 {

    private int sucesso;
    private String evtTabHorTur_Id;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideHorContratual_codHorContrat;
    private String ideHorContratual_iniValid;
    private String dadosHorContratual_hrEntr;
    private String dadosHorContratual_hrSaida;
    private String dadosHorContratual_durJornada;
    private String dadosHorContratual_perHorFlexivel;
    /**
     * 1 - Fixo / 2 - Variavel
     *
     */
    private String horarioIntervalo_tpInterv;
    private String horarioIntervalo_durInterv;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getHorarioIntervalo_tpInterv() {
        return null == horarioIntervalo_tpInterv ? "" : horarioIntervalo_tpInterv;
    }

    public void setHorarioIntervalo_tpInterv(String horarioIntervalo_tpInterv) {
        this.horarioIntervalo_tpInterv = horarioIntervalo_tpInterv;
    }

    public String getHorarioIntervalo_durInterv() {
        return null == horarioIntervalo_durInterv ? "" : horarioIntervalo_durInterv;
    }

    public void setHorarioIntervalo_durInterv(String horarioIntervalo_durInterv) {
        this.horarioIntervalo_durInterv = horarioIntervalo_durInterv;
    }

    public String getEvtTabHorTur_Id() {
        return null == evtTabHorTur_Id ? "" : evtTabHorTur_Id;
    }

    public void setEvtTabHorTur_Id(String evtTabHorTur_Id) {
        this.evtTabHorTur_Id = evtTabHorTur_Id;
    }

    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeHorContratual_codHorContrat() {
        return null == ideHorContratual_codHorContrat ? "" : ideHorContratual_codHorContrat;
    }

    public void setIdeHorContratual_codHorContrat(String ideHorContratual_codHorContrat) {
        this.ideHorContratual_codHorContrat = ideHorContratual_codHorContrat;
    }

    public String getIdeHorContratual_iniValid() {
        return null == ideHorContratual_iniValid ? "" : ideHorContratual_iniValid;
    }

    public void setIdeHorContratual_iniValid(String ideHorContratual_iniValid) {
        this.ideHorContratual_iniValid = ideHorContratual_iniValid;
    }

    public String getDadosHorContratual_hrEntr() {
        return null == dadosHorContratual_hrEntr ? "" : dadosHorContratual_hrEntr;
    }

    public void setDadosHorContratual_hrEntr(String dadosHorContratual_hrEntr) {
        this.dadosHorContratual_hrEntr = dadosHorContratual_hrEntr;
    }

    public String getDadosHorContratual_hrSaida() {
        return null == dadosHorContratual_hrSaida ? "" : dadosHorContratual_hrSaida;
    }

    public void setDadosHorContratual_hrSaida(String dadosHorContratual_hrSaida) {
        this.dadosHorContratual_hrSaida = dadosHorContratual_hrSaida;
    }

    public String getDadosHorContratual_durJornada() {
        return null == dadosHorContratual_durJornada ? "" : dadosHorContratual_durJornada;
    }

    public void setDadosHorContratual_durJornada(String dadosHorContratual_durJornada) {
        this.dadosHorContratual_durJornada = dadosHorContratual_durJornada;
    }

    public String getDadosHorContratual_perHorFlexivel() {
        return null == dadosHorContratual_perHorFlexivel ? "" : dadosHorContratual_perHorFlexivel;
    }

    public void setDadosHorContratual_perHorFlexivel(String dadosHorContratual_perHorFlexivel) {
        this.dadosHorContratual_perHorFlexivel = dadosHorContratual_perHorFlexivel;
    }
}

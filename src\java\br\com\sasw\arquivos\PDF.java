package br.com.sasw.arquivos;

import SasBeans.DIRFDetPS;
import SasBeans.Filiais;
import SasBeans.FolhaPonto;
import SasBeans.Rh_Horas;
import SasBeansCompostas.ContraCheque;
import SasBeansCompostas.GeraPdfFolhaPonto;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.BatidaPonto;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Font.FontFamily;
import com.itextpdf.text.Font.FontStyle;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import static java.lang.Integer.parseInt;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.ResolverStyle;
import java.util.Date;
import java.util.List;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
public class PDF {

    File file;
    private Document document = new Document();
    private String Arquivo, codFil;
    PdfPTable table;
    PdfWriter writer;

    public PDF(String caminho, String codFil, String arquivo) {
        this.codFil = codFil;
        Arquivo = caminho + "\\" + arquivo;
        try {
            File theFile = new File(caminho);
            theFile.mkdirs();// will create a sub folder for each user (currently does not work, below hopefully is a solution) (DOES WORK NOW)

            writer = PdfWriter.getInstance(document, new FileOutputStream(Arquivo));
        } catch (DocumentException | IOException de) {
            System.err.println(de.getMessage());
        }
        document.setMargins(8, 8, 8, 8);
        document.open();
    }

    public PDF(String caminho, String arquivo) {
        Arquivo = caminho + "\\" + arquivo;
        try {
            File theFile = new File(caminho);
            theFile.mkdirs();// will create a sub folder for each user (currently does not work, below hopefully is a solution) (DOES WORK NOW)

            writer = PdfWriter.getInstance(document, new FileOutputStream(Arquivo));
        } catch (DocumentException | IOException de) {
            System.err.println(de.getMessage());
        }
        document.setMargins(8, 8, 8, 8);
        document.open();
    }

    public void CabecalhoPonto(String caminho, String logo, GeraPdfFolhaPonto gerapdffolhaponto) {
        Font f, f1, f2, f3, f4;
        Paragraph p, p1, p2, p3;
        Image img;
        try {
            //adiciona o texto ao PDF
            f = new Font(FontFamily.COURIER, 12, Font.BOLD);
            f1 = new Font(FontFamily.COURIER, 12);
            f2 = new Font(FontFamily.COURIER, 6);
            f3 = new Font(FontFamily.COURIER, 6);
            f4 = new Font(FontFamily.COURIER, 8);
            p1 = new Paragraph(gerapdffolhaponto.getFiliais().getRazaoSocial(), f1);
            p2 = new Paragraph(gerapdffolhaponto.getFiliais().getEndereco() + " - " + gerapdffolhaponto.getFiliais().getBairro()
                    + " - " + gerapdffolhaponto.getFiliais().getCidade() + "/" + gerapdffolhaponto.getFiliais().getUF()
                    + " - " + FuncoesString.formatarString(gerapdffolhaponto.getFiliais().getCEP(), "##.###-###"), f2);
            p3 = new Paragraph("C.N.P.J.:" + FuncoesString.formatarString(gerapdffolhaponto.getFiliais().getCNPJ(), "##.###.###/####-##"), f3);
            p = new Paragraph("Folha de Ponto Individual", f);
            // adiciona imagem ao pdf  
            //img = Image.getInstance(caminho + getLogo(logo));
            try {
                img = Image.getInstance(getLogo(logo).replace("https://mobile.sasw.com.br:9091/satellite/logos/", "C:/xampp/htdocs/satellite/logos/"));
            } catch (Exception ex) {
                img = Image.getInstance(getLogo(logo));
            }
            img.setAlignment(Element.PARAGRAPH);
            img.scalePercent(30, 30);
            p.setSpacingAfter(8);
            p.setAlignment(Element.ALIGN_CENTER);
            //coloca no documento os objetos gerados
            document.add(img);
            document.add(p1);
            document.add(p2);
            document.add(p3);
            document.add(p);
            //insere tabela com dados do funcionario            
            PdfPTable TabelaDados = new PdfPTable(new float[]{0.15f, 0.25f, 0.1f, 0.25f, 0.1f, 0.15f});
            TabelaDados.setWidthPercentage(100);
            PdfPCell cel1 = new PdfPCell(new Paragraph("Matricula:", f4));
            PdfPCell cel2 = new PdfPCell(new Paragraph(gerapdffolhaponto.getRh_Ctrl().getMatr().toPlainString().replace(".0", ""), f4));
            PdfPCell cel3 = new PdfPCell(new Paragraph("Nome:", f4));
            PdfPCell cel4 = new PdfPCell(new Paragraph(gerapdffolhaponto.getFuncion().getNome_Guer(), f4));
            PdfPCell cel5 = new PdfPCell(new Paragraph("Função:", f4));
            PdfPCell cel6 = new PdfPCell(new Paragraph(gerapdffolhaponto.getCargos().getDescricao(), f4));
            PdfPCell cel7 = new PdfPCell(new Paragraph("Filial:", f4));
            PdfPCell cel8 = new PdfPCell(new Paragraph(gerapdffolhaponto.getFiliais().getCodFil().toPlainString().replace(".0", "") + " "
                    + gerapdffolhaponto.getFiliais().getDescricao(), f4));
            PdfPCell cel9 = new PdfPCell(new Paragraph("Posto:", f4));
            PdfPCell cel10 = new PdfPCell(new Paragraph(gerapdffolhaponto.getPstServ().getLocal(), f4));
            PdfPCell cel11 = new PdfPCell(new Paragraph("", f4));
            PdfPCell cel12 = new PdfPCell(new Paragraph("Hr. de Trabalho:", f4));
            PdfPCell cel13 = new PdfPCell(new Paragraph("Escala Definida em CCT", f4));
            PdfPCell cel14 = new PdfPCell(new Paragraph("CTPS:", f4));
            PdfPCell cel15 = new PdfPCell(new Paragraph(gerapdffolhaponto.getFuncion().getCTPS_Nro(), f4));
            PdfPCell cel16 = new PdfPCell(new Paragraph("Série:", f4));
            PdfPCell cel17 = new PdfPCell(new Paragraph(gerapdffolhaponto.getFuncion().getCTPS_Serie(), f4));
            cel1.setBorder(0);
            cel2.setBorder(0);
            cel3.setBorder(0);
            cel4.setBorder(0);
            cel5.setBorder(0);
            cel6.setBorder(0);
            cel7.setBorder(0);
            cel8.setBorder(0);
            cel9.setBorder(0);
            cel10.setBorder(0);
            cel11.setBorder(0);
            cel11.setBorder(0);
            cel12.setBorder(0);
            cel13.setBorder(0);
            cel14.setBorder(0);
            cel15.setBorder(0);
            cel16.setBorder(0);
            cel17.setBorder(0);
            TabelaDados.addCell(cel1);
            TabelaDados.addCell(cel2);
            TabelaDados.addCell(cel3);
            TabelaDados.addCell(cel4);
            TabelaDados.addCell(cel5);
            TabelaDados.addCell(cel6);
            TabelaDados.addCell(cel7);
            TabelaDados.addCell(cel8);
            TabelaDados.addCell(cel9);
            TabelaDados.addCell(cel10);
            TabelaDados.addCell(cel11);
            TabelaDados.addCell(cel11);
            TabelaDados.addCell(cel12);
            TabelaDados.addCell(cel13);
            TabelaDados.addCell(cel14);
            TabelaDados.addCell(cel15);
            TabelaDados.addCell(cel16);
            TabelaDados.addCell(cel17);
            document.add(TabelaDados);
            // adiciona tabela ao pdf
            table = new PdfPTable(new float[]{0.09f, 0.12f, 0.1f, 0.09f, 0.09f, 0.1f, 0.19f, 0.1f, 0.1f});
            table.setSpacingBefore(10);
            table.setWidthPercentage(100);
            PdfPCell header = new PdfPCell(new Paragraph("Intervalo", f4));
            PdfPCell header1 = new PdfPCell(new Paragraph("Dia", f4));
            PdfPCell header2 = new PdfPCell(new Paragraph("Dia Semana", f4));
            PdfPCell header3 = new PdfPCell(new Paragraph("Entrada", f4));
            PdfPCell header4 = new PdfPCell(new Paragraph("Saida", f4));
            PdfPCell header5 = new PdfPCell(new Paragraph("Local de Atuação", f4));
            PdfPCell header6 = new PdfPCell(new Paragraph("Assinatura", f4));
            PdfPCell header7 = new PdfPCell(new Paragraph("Visto Fiscal", f4));
            header.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header1.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header2.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header3.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header4.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header5.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header6.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header7.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header.setHorizontalAlignment(Element.ALIGN_CENTER);
            header1.setHorizontalAlignment(Element.ALIGN_CENTER);
            header2.setHorizontalAlignment(Element.ALIGN_CENTER);
            header3.setHorizontalAlignment(Element.ALIGN_CENTER);
            header4.setHorizontalAlignment(Element.ALIGN_CENTER);
            header5.setHorizontalAlignment(Element.ALIGN_CENTER);
            header6.setHorizontalAlignment(Element.ALIGN_CENTER);
            header7.setHorizontalAlignment(Element.ALIGN_CENTER);
            header.setColspan(2);
            header1.setRowspan(2);
            header2.setRowspan(2);
            header3.setRowspan(2);
            header4.setRowspan(2);
            header5.setRowspan(2);
            header6.setRowspan(2);
            header7.setRowspan(2);
            table.addCell(header1);
            table.addCell(header2);
            table.addCell(header3);
            table.addCell(header);
            table.addCell(header4);
            table.addCell(header5);
            table.addCell(header6);
            table.addCell(header7);
            header3.setRowspan(1);
            header4.setRowspan(1);
            table.addCell(header4);
            table.addCell(header3);
        } catch (Exception de) {
            System.err.println(de.getMessage());
        }
    }

    public void CabecalhoPontoNew(String caminho, String logo, Filiais filial, FolhaPonto funcionario, int NumeroPagina) {
        Font f, f1, f2, f3, f4;
        Paragraph p, p1, p2, p3;
        Image img;
        try {
            //adiciona o texto ao PDF
            f = new Font(FontFamily.COURIER, 11, Font.BOLD);
            f1 = new Font(FontFamily.COURIER, 12);
            f2 = new Font(FontFamily.COURIER, 6);
            f3 = new Font(FontFamily.COURIER, 6);
            f4 = new Font(FontFamily.COURIER, 8);
            p1 = new Paragraph(filial.getRazaoSocial(), f1);
            p2 = new Paragraph(filial.getEndereco() + " - " + filial.getBairro()
                    + " - " + filial.getCidade() + "/" + filial.getUF()
                    + " - " + FuncoesString.formatarString(filial.getCEP(), "##.###-###"), f2);
            p3 = new Paragraph(Messages.getMessageS("CGC") + FuncoesString.formatarString(filial.getCNPJ(), "##.###.###/####-##"), f3);
            //p = new Paragraph("Folha de Ponto Individual",f);
            //p = new Paragraph(Messages.getMessageS("FolhaPontoIndividual"), f);
            p = new Paragraph(Messages.getMessageS("FolhaPontoIndividual") + "\n" + funcionario.getCliente(), f);
            // adiciona imagem ao pdf  
            //img = Image.getInstance(caminho + getLogo(logo));
            try {
                img = Image.getInstance(getLogo(logo).replace("https://mobile.sasw.com.br:9091/satellite/logos/", "C:/xampp/htdocs/satellite/logos/"));
            } catch (Exception ex) {
                img = Image.getInstance(getLogo(logo));
            }
            img.setAlignment(Element.PARAGRAPH);
            img.scalePercent(30, 30);
            p.setSpacingAfter(8);
            p.setAlignment(Element.ALIGN_CENTER);
            //coloca no documento os objetos gerados
            if (NumeroPagina != 1) {
                document.newPage();
            }
            document.add(img);
            document.add(p1);
            document.add(p2);
            document.add(p3);
            document.add(p);
            //insere tabela com dados do funcionario            
            PdfPTable TabelaDados = new PdfPTable(new float[]{0.15f, 0.25f, 0.1f, 0.25f, 0.1f, 0.15f});
            TabelaDados.setWidthPercentage(100);
            PdfPCell cel1 = new PdfPCell(new Paragraph(Messages.getMessageS("Matricula") + ":", f4));
            PdfPCell cel2 = new PdfPCell(new Paragraph(funcionario.getMatr().replace(".0", ""), f4));
            PdfPCell cel3 = new PdfPCell(new Paragraph(Messages.getMessageS("Nome") + ":", f4));
            PdfPCell cel4 = new PdfPCell(new Paragraph(funcionario.getNome(), f4));
            PdfPCell cel5 = new PdfPCell(new Paragraph(Messages.getMessageS("Funcao") + ":", f4));
            PdfPCell cel6 = new PdfPCell(new Paragraph(funcionario.getCargo(), f4));
            PdfPCell cel7 = new PdfPCell(new Paragraph(Messages.getMessageS("Filial") + ":", f4));
            PdfPCell cel8 = new PdfPCell(new Paragraph(filial.getCodFil().toPlainString().replace(".0", "") + " " + filial.getDescricao(), f4));
            PdfPCell cel9 = new PdfPCell(new Paragraph(Messages.getMessageS("Posto") + ":", f4));
            PdfPCell cel10 = new PdfPCell(new Paragraph(funcionario.getPosto(), f4));

            //PdfPCell cel11 = new PdfPCell(new Paragraph(Messages.getMessageS("Cliente") + ":", f4));
            //PdfPCell cel11A = new PdfPCell(new Paragraph(funcionario.getCliente(), f4));
            PdfPCell cel11 = new PdfPCell(new Paragraph("", f4));

            PdfPCell cel12 = new PdfPCell(new Paragraph(Messages.getMessageS("HrTrabalho") + ":", f4));
            PdfPCell cel13 = new PdfPCell(new Paragraph(funcionario.getHorario(), f4));
            PdfPCell cel14 = new PdfPCell(new Paragraph(Messages.getMessageS("CTPS") + ":", f4));
            PdfPCell cel15 = new PdfPCell(new Paragraph(funcionario.getCTPS_Nro(), f4));
            PdfPCell cel16 = new PdfPCell(new Paragraph(Messages.getMessageS("Serie") + ":", f4));
            PdfPCell cel17 = new PdfPCell(new Paragraph(funcionario.getCTPS_Serie(), f4));
            cel1.setBorder(0);
            cel2.setBorder(0);
            cel3.setBorder(0);
            cel4.setBorder(0);
            cel5.setBorder(0);
            cel6.setBorder(0);
            cel7.setBorder(0);
            cel8.setBorder(0);
            cel9.setBorder(0);
            cel10.setBorder(0);
            cel11.setBorder(0);
            //cel11A.setBorder(0);
            cel12.setBorder(0);
            cel13.setBorder(0);
            cel14.setBorder(0);
            cel15.setBorder(0);
            cel16.setBorder(0);
            cel17.setBorder(0);
            TabelaDados.addCell(cel1);
            TabelaDados.addCell(cel2);
            TabelaDados.addCell(cel3);
            TabelaDados.addCell(cel4);
            TabelaDados.addCell(cel5);
            TabelaDados.addCell(cel6);
            TabelaDados.addCell(cel7);
            TabelaDados.addCell(cel8);
            TabelaDados.addCell(cel9);
            TabelaDados.addCell(cel10);
            TabelaDados.addCell(cel11);
            TabelaDados.addCell(cel11);
            //TabelaDados.addCell(cel11A);
            TabelaDados.addCell(cel12);
            TabelaDados.addCell(cel13);
            TabelaDados.addCell(cel14);
            TabelaDados.addCell(cel15);
            TabelaDados.addCell(cel16);
            TabelaDados.addCell(cel17);
            document.add(TabelaDados);
            // adiciona tabela ao pdf
            table = new PdfPTable(new float[]{0.09f, 0.12f, 0.07f, 0.07f,
                0.07f, 0.07f, 0.07f, 0.07f, 0.27f, 0.1f});
            table.setSpacingBefore(10);
            table.setWidthPercentage(100);
            PdfPCell superHeaderIntervalo = new PdfPCell(new Paragraph(Messages.getMessageS("Intervalo"), f4));
            PdfPCell superHeaderExtra = new PdfPCell(new Paragraph(Messages.getMessageS("Extra"), f4));
            PdfPCell header1 = new PdfPCell(new Paragraph(Messages.getMessageS("Dia"), f4));
            PdfPCell header2 = new PdfPCell(new Paragraph(Messages.getMessageS("DiaSemana"), f4));
            PdfPCell header3 = new PdfPCell(new Paragraph(Messages.getMessageS("ENTRADA"), f4));
            PdfPCell header4 = new PdfPCell(new Paragraph(Messages.getMessageS("SAÍDA"), f4));
            PdfPCell header5 = new PdfPCell(new Paragraph(Messages.getMessageS("LocalAtuacao"), f4));
            PdfPCell header6 = new PdfPCell(new Paragraph(Messages.getMessageS("Obs."), f4));
            superHeaderIntervalo.setBackgroundColor(BaseColor.LIGHT_GRAY);
            superHeaderExtra.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header1.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header2.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header3.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header4.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header5.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header6.setBackgroundColor(BaseColor.LIGHT_GRAY);
            superHeaderIntervalo.setHorizontalAlignment(Element.ALIGN_CENTER);
            superHeaderExtra.setHorizontalAlignment(Element.ALIGN_CENTER);
            header1.setHorizontalAlignment(Element.ALIGN_CENTER);
            header2.setHorizontalAlignment(Element.ALIGN_CENTER);
            header3.setHorizontalAlignment(Element.ALIGN_CENTER);
            header4.setHorizontalAlignment(Element.ALIGN_CENTER);
            header5.setHorizontalAlignment(Element.ALIGN_CENTER);
            header6.setHorizontalAlignment(Element.ALIGN_CENTER);
            superHeaderIntervalo.setColspan(2);
            superHeaderExtra.setColspan(2);
            header1.setRowspan(2);
            header2.setRowspan(2);
            header3.setRowspan(2);
            header4.setRowspan(2);
            header5.setRowspan(2);
            header6.setRowspan(2);
            table.addCell(header1);
            table.addCell(header2);
            table.addCell(header3);
            table.addCell(superHeaderIntervalo);
            table.addCell(header4);
            table.addCell(superHeaderExtra);
            table.addCell(header5);
            table.addCell(header6);
            header3.setRowspan(1);
            header4.setRowspan(1);
            table.addCell(header4);
            table.addCell(header3);
            table.addCell(header4);
            table.addCell(header3);
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
    }

    public void CabecalhoPontoNew(String caminho, String logo, Filiais filial, FolhaPonto funcionario) {
        Font f, f1, f2, f3, f4;
        Paragraph p, p1, p2, p3;
        Image img;
        try {
            //adiciona o texto ao PDF
            f = new Font(FontFamily.COURIER, 11, Font.BOLD);
            f1 = new Font(FontFamily.COURIER, 12);
            f2 = new Font(FontFamily.COURIER, 6);
            f3 = new Font(FontFamily.COURIER, 6);
            f4 = new Font(FontFamily.COURIER, 8);
            p1 = new Paragraph(filial.getRazaoSocial(), f1);
            p2 = new Paragraph(filial.getEndereco() + " - " + filial.getBairro()
                    + " - " + filial.getCidade() + "/" + filial.getUF()
                    + " - " + FuncoesString.formatarString(filial.getCEP(), "##.###-###"), f2);
            p3 = new Paragraph(Messages.getMessageS("CGC") + FuncoesString.formatarString(filial.getCNPJ(), "##.###.###/####-##"), f3);
            //p = new Paragraph("Folha de Ponto Individual",f);
            p = new Paragraph(Messages.getMessageS("FolhaPontoIndividual") + "\n" + funcionario.getCliente(), f);
            // adiciona imagem ao pdf  
            //img = Image.getInstance(caminho + getLogo(logo));
            try {
                img = Image.getInstance(getLogo(logo).replace("https://mobile.sasw.com.br:9091/satellite/logos/", "C:/xampp/htdocs/satellite/logos/"));
            } catch (Exception ex) {
                img = Image.getInstance(getLogo(logo));
            }

            img.setAlignment(Element.PARAGRAPH);
            img.scalePercent(30, 30);
            p.setSpacingAfter(8);
            p.setAlignment(Element.ALIGN_CENTER);
            //coloca no documento os objetos gerados
            document.add(img);
            document.add(p1);
            document.add(p2);
            document.add(p3);
            document.add(p);
            //insere tabela com dados do funcionario            
            PdfPTable TabelaDados = new PdfPTable(new float[]{0.15f, 0.25f, 0.1f, 0.25f, 0.1f, 0.15f});
            TabelaDados.setWidthPercentage(100);
            PdfPCell cel1 = new PdfPCell(new Paragraph(Messages.getMessageS("Matricula") + ":", f4));
            PdfPCell cel2 = new PdfPCell(new Paragraph(funcionario.getMatr().replace(".0", ""), f4));
            PdfPCell cel3 = new PdfPCell(new Paragraph(Messages.getMessageS("Nome") + ":", f4));
            PdfPCell cel4 = new PdfPCell(new Paragraph(funcionario.getNome(), f4));
            PdfPCell cel5 = new PdfPCell(new Paragraph(Messages.getMessageS("Funcao") + ":", f4));
            PdfPCell cel6 = new PdfPCell(new Paragraph(funcionario.getCargo(), f4));
            PdfPCell cel7 = new PdfPCell(new Paragraph(Messages.getMessageS("Filial") + ":", f4));
            PdfPCell cel8 = new PdfPCell(new Paragraph(filial.getCodFil().toPlainString().replace(".0", "") + " " + filial.getDescricao(), f4));

            PdfPCell cel9 = new PdfPCell(new Paragraph(Messages.getMessageS("Posto") + ":", f4));
            PdfPCell cel10 = new PdfPCell(new Paragraph(funcionario.getPosto(), f4));

            //PdfPCell cel11 = new PdfPCell(new Paragraph(Messages.getMessageS("Cliente") + ":", f4));
            //PdfPCell cel11A = new PdfPCell(new Paragraph(funcionario.getCliente(), f4));
            PdfPCell cel11 = new PdfPCell(new Paragraph("", f4));

            PdfPCell cel12 = new PdfPCell(new Paragraph(Messages.getMessageS("HrTrabalho") + ":", f4));
            PdfPCell cel13 = new PdfPCell(new Paragraph(funcionario.getHorario(), f4));
            PdfPCell cel14 = new PdfPCell(new Paragraph(Messages.getMessageS("CTPS") + ":", f4));
            PdfPCell cel15 = new PdfPCell(new Paragraph(funcionario.getCTPS_Nro(), f4));
            PdfPCell cel16 = new PdfPCell(new Paragraph(Messages.getMessageS("Serie") + ":", f4));
            PdfPCell cel17 = new PdfPCell(new Paragraph(funcionario.getCTPS_Serie(), f4));
            cel1.setBorder(0);
            cel2.setBorder(0);
            cel3.setBorder(0);
            cel4.setBorder(0);
            cel5.setBorder(0);
            cel6.setBorder(0);
            cel7.setBorder(0);
            cel8.setBorder(0);
            cel9.setBorder(0);
            cel10.setBorder(0);
            cel11.setBorder(0);
            //cel11A.setBorder(0);
            cel12.setBorder(0);
            cel13.setBorder(0);
            cel14.setBorder(0);
            cel15.setBorder(0);
            cel16.setBorder(0);
            cel17.setBorder(0);
            TabelaDados.addCell(cel1);
            TabelaDados.addCell(cel2);
            TabelaDados.addCell(cel3);
            TabelaDados.addCell(cel4);
            TabelaDados.addCell(cel5);
            TabelaDados.addCell(cel6);
            TabelaDados.addCell(cel7);
            TabelaDados.addCell(cel8);
            TabelaDados.addCell(cel9);
            TabelaDados.addCell(cel10);
            TabelaDados.addCell(cel11);
            TabelaDados.addCell(cel11);
            //TabelaDados.addCell(cel11A);
            TabelaDados.addCell(cel12);
            TabelaDados.addCell(cel13);
            TabelaDados.addCell(cel14);
            TabelaDados.addCell(cel15);
            TabelaDados.addCell(cel16);
            TabelaDados.addCell(cel17);
            document.add(TabelaDados);
            // adiciona tabela ao pdf
            table = new PdfPTable(new float[]{0.09f, 0.12f, 0.07f, 0.07f,
                0.07f, 0.07f, 0.07f, 0.07f, 0.27f, 0.1f});
            table.setSpacingBefore(10);
            table.setWidthPercentage(100);
            PdfPCell superHeaderIntervalo = new PdfPCell(new Paragraph(Messages.getMessageS("Intervalo"), f4));
            PdfPCell superHeaderExtra = new PdfPCell(new Paragraph(Messages.getMessageS("Extra"), f4));
            PdfPCell header1 = new PdfPCell(new Paragraph(Messages.getMessageS("Dia"), f4));
            PdfPCell header2 = new PdfPCell(new Paragraph(Messages.getMessageS("DiaSemana"), f4));
            PdfPCell header3 = new PdfPCell(new Paragraph(Messages.getMessageS("ENTRADA"), f4));
            PdfPCell header4 = new PdfPCell(new Paragraph(Messages.getMessageS("SAÍDA"), f4));
            PdfPCell header5 = new PdfPCell(new Paragraph(Messages.getMessageS("LocalAtuacao"), f4));
            PdfPCell header6 = new PdfPCell(new Paragraph(Messages.getMessageS("Obs."), f4));
            superHeaderIntervalo.setBackgroundColor(BaseColor.LIGHT_GRAY);
            superHeaderExtra.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header1.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header2.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header3.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header4.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header5.setBackgroundColor(BaseColor.LIGHT_GRAY);
            header6.setBackgroundColor(BaseColor.LIGHT_GRAY);
            superHeaderIntervalo.setHorizontalAlignment(Element.ALIGN_CENTER);
            superHeaderExtra.setHorizontalAlignment(Element.ALIGN_CENTER);
            header1.setHorizontalAlignment(Element.ALIGN_CENTER);
            header2.setHorizontalAlignment(Element.ALIGN_CENTER);
            header3.setHorizontalAlignment(Element.ALIGN_CENTER);
            header4.setHorizontalAlignment(Element.ALIGN_CENTER);
            header5.setHorizontalAlignment(Element.ALIGN_CENTER);
            header6.setHorizontalAlignment(Element.ALIGN_CENTER);
            superHeaderIntervalo.setColspan(2);
            superHeaderExtra.setColspan(2);
            header1.setRowspan(2);
            header2.setRowspan(2);
            header3.setRowspan(2);
            header4.setRowspan(2);
            header5.setRowspan(2);
            header6.setRowspan(2);
            table.addCell(header1);
            table.addCell(header2);
            table.addCell(header3);
            table.addCell(superHeaderIntervalo);
            table.addCell(header4);
            table.addCell(superHeaderExtra);
            table.addCell(header5);
            table.addCell(header6);
            header3.setRowspan(1);
            header4.setRowspan(1);
            table.addCell(header4);
            table.addCell(header3);
            table.addCell(header4);
            table.addCell(header3);
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
    }

    public void ItemPonto(Rh_Horas rh_hora) {
        Font f = new Font(FontFamily.COURIER, 8);
        PdfPCell cel1 = new PdfPCell(new Paragraph(rh_hora.getData(), f));
        PdfPCell cel2 = new PdfPCell(new Paragraph(TraduzSemana(rh_hora.getSemana().intValue()), f));
        PdfPCell cel3 = new PdfPCell(new Paragraph(rh_hora.getHora1(), f));
        PdfPCell cel4 = new PdfPCell(new Paragraph(rh_hora.getHora2(), f));
        PdfPCell cel5 = new PdfPCell(new Paragraph(rh_hora.getHora3(), f));
        PdfPCell cel6 = new PdfPCell(new Paragraph(rh_hora.getHora4(), f));
        PdfPCell cel7 = new PdfPCell(new Paragraph(""));
        PdfPCell cel8 = new PdfPCell(new Paragraph(""));
        PdfPCell cel9 = new PdfPCell(new Paragraph(""));
        cel1.setFixedHeight(18.45f);
        cel2.setFixedHeight(18.45f);
        cel3.setFixedHeight(18.45f);
        cel4.setFixedHeight(18.45f);
        cel5.setFixedHeight(18.45f);
        cel6.setFixedHeight(18.45f);
        cel7.setFixedHeight(18.45f);
        cel8.setFixedHeight(18.45f);
        cel9.setFixedHeight(18.45f);
        table.addCell(cel1);
        table.addCell(cel2);
        table.addCell(cel3);
        table.addCell(cel4);
        table.addCell(cel5);
        table.addCell(cel6);
        table.addCell(cel7);
        table.addCell(cel8);
        table.addCell(cel9);
    }

    private String formateData(String raw) {
        DateTimeFormatter df;
        Mascaras m = new Mascaras();
        DateTimeFormatter dataTime = DateTimeFormatter.ofPattern(m.getPadraoData());
        LocalDate dia;
        try {
            df = DateTimeFormatter.ofPattern("yyyyMMdd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            dia = LocalDate.parse(raw, df);
            return dia.format(dataTime);
        } catch (Exception e) {
            return raw;
        }
    }

    public void ItemPonto(BatidaPonto batida) {
        Font f = new Font(FontFamily.COURIER, 8);
        PdfPCell cel1 = new PdfPCell(new Paragraph(formateData(batida.getData()), f));

        String diaSemana = "";
        try {
            diaSemana = TraduzSemana(parseInt(batida.getDiaSemana()));
        } catch (Exception e) {
        }
        PdfPCell cel2 = new PdfPCell(new Paragraph(diaSemana, f));

        PdfPCell cel3 = new PdfPCell(new Paragraph(batida.getBatida01(), f));
        PdfPCell cel4 = new PdfPCell(new Paragraph(batida.getBatida02(), f));
        PdfPCell cel5 = new PdfPCell(new Paragraph(batida.getBatida03(), f));
        PdfPCell cel6 = new PdfPCell(new Paragraph(batida.getBatida04(), f));
        PdfPCell cel7 = new PdfPCell(new Paragraph("-", f));
        PdfPCell cel8 = new PdfPCell(new Paragraph("-", f));
        PdfPCell cel9 = new PdfPCell(new Paragraph(batida.getPostoRegistro(), f));
        PdfPCell cel10 = new PdfPCell(new Paragraph(batida.getOBS(), f));
//        PdfPCell cel9 = new PdfPCell(new Paragraph("unused"));
        cel1.setFixedHeight(18.45f);
        cel2.setFixedHeight(18.45f);
        cel3.setFixedHeight(18.45f);
        cel4.setFixedHeight(18.45f);
        cel5.setFixedHeight(18.45f);
        cel6.setFixedHeight(18.45f);
        cel7.setFixedHeight(18.45f);
        cel8.setFixedHeight(18.45f);
        cel9.setFixedHeight(18.45f);
        cel10.setFixedHeight(18.45f);
        table.addCell(cel1);
        table.addCell(cel2);
        table.addCell(cel3);
        table.addCell(cel4);
        table.addCell(cel5);
        table.addCell(cel6);
        table.addCell(cel7);
        table.addCell(cel8);
        table.addCell(cel9);
        table.addCell(cel10);
    }

    public void FechaPonto(GeraPdfFolhaPonto gerapdffolhaponto) {
        try {
            Font f = new Font(FontFamily.COURIER, 10);
            Paragraph periodo, assinadoPor,  linha, nome;
            periodo = new Paragraph(gerapdffolhaponto.getRh_Ctrl().getDt_Ini() + " a " + gerapdffolhaponto.getRh_Ctrl().getDt_Fim(), f);
            assinadoPor = new Paragraph("      ASSINADO ELETRONICAMENTE     ", f);                                 
            linha = new Paragraph("___________________________________", f);
            nome = new Paragraph(gerapdffolhaponto.getFuncion().getNome_Guer(), f);
            periodo.setAlignment(Element.ALIGN_CENTER);
            linha.setAlignment(Element.ALIGN_CENTER);
            //linha.setSpacingBefore(30);
            assinadoPor.setSpacingBefore(20);
            nome.setAlignment(Element.ALIGN_CENTER);
            assinadoPor.setAlignment(Element.ALIGN_CENTER);
            
            document.add(table);
            document.add(periodo);
            document.add(assinadoPor);
            document.add(linha);
            document.add(nome);
        } catch (DocumentException ex) {
        }
    }

    public void FechaPontoNew(String dataInicio, String dataFim, String nomeGuerra) {
        try {
            Font f = new Font(FontFamily.COURIER, 10);
            Paragraph periodo, assinadoPor, linha, nome;
            periodo = new Paragraph(formateData(dataInicio) + " a " + formateData(dataFim), f);
            assinadoPor = new Paragraph("      ASSINADO ELETRONICAMENTE     ", f);                                             
            linha = new Paragraph("___________________________________", f);
            nome = new Paragraph(nomeGuerra, f);
            periodo.setAlignment(Element.ALIGN_CENTER);
            linha.setAlignment(Element.ALIGN_CENTER);
            //linha.setSpacingBefore(20);
            nome.setAlignment(Element.ALIGN_CENTER);
            assinadoPor.setAlignment(Element.ALIGN_CENTER);
            assinadoPor.setSpacingBefore(20);            
            document.add(table);
            document.add(periodo);
            document.add(assinadoPor);
            document.add(linha);
            document.add(nome);
        } catch (DocumentException ex) {
        }
    }

    /**
     * ***********************************************************************************************
     * inicia a formatacao do contra-cheque
     * ***********************************************************************************************
     */
    /**
     *
     * @param caminho
     * @param logo
     * @param codmovfp
     * @param contracheque
     * @param tipofolha
     * @throws Exception
     */
    public void CabecalhoContraCheque(String caminho, String logo, String codmovfp, ContraCheque contracheque, String tipofolha) throws Exception {
        Font f, f1, f4, f5;
        Paragraph p;
        Image img;
        try {
            f = new Font(FontFamily.HELVETICA, 9, Font.BOLD);
            f1 = new Font(FontFamily.HELVETICA, 8);
            f4 = new Font(FontFamily.HELVETICA, 8);
            f5 = new Font(FontFamily.HELVETICA, 6);
            p = new Paragraph("");
            p.setSpacingAfter(15);
            //p = new Paragraph("Demonstrativo de Pagamento Mensal",f);p.setAlignment(Element.ALIGN_RIGHT);
            //img = Image.getInstance(caminho + getLogo(logo));
            try {
               img = Image.getInstance(getLogo(logo).replace("https://mobile.sasw.com.br:9091/satellite/logos/", "C:/xampp/htdocs/satellite/logos/"));
                //img = Image.getInstance("C:/xampp/htdocs/satellite/logos/LogoCPV.JPG");//.replace("https://mobile.sasw.com.br:9091/satellite/logos/", "C:/xampp/htdocs/satellite/logos/"));
            } catch (Exception ex) {
                img = Image.getInstance(getLogo(logo));
                //img = Image.getInstance("C:/xampp/htdocs/satellite/logos/LogoCPV.JPG");//.replace("https://mobile.sasw.com.br:9091/satellite/logos/", "C:/xampp/htdocs/satellite/logos/"));
            }
            img.setAlignment(Element.PARAGRAPH);
            img.scalePercent(30, 30);
            //p.setSpacingAfter(15);
            PdfPTable TabelaEmp = new PdfPTable(3);
            TabelaEmp.setWidthPercentage(100);
            PdfPCell emp1 = new PdfPCell(new Paragraph(contracheque.getFiliais().getRazaoSocial(), f));
            String doc;
            if (tipofolha.equals("MEN")) {
                doc = " Mensal";
            } else if (tipofolha.equals("ADT")) {
                doc = " de Adiantamento";
            } else if (tipofolha.equals("131")) {
                doc = " de adiantamento de 13º";
            } else if (tipofolha.equals("132")) {
                doc = " de 13º";
            } else if (tipofolha.equals("133")) {
                doc = " de complemento de 13º";
            } else if (tipofolha.equals("AUT")) {
                doc = " de Autônomo";
            } else if (tipofolha.equals("EST")) {
                doc = " de Estagiário";
            } else if (tipofolha.equals("FER")) {
                doc = " de Férias";
            } else if (tipofolha.equals("FEJ")) {
                doc = " de Férias Reajustadas";
            } else if (tipofolha.equals("CPL")) {
                doc = " Complementar";
            } else {
                if (logo.toUpperCase().contains("CORPVS") && tipofolha.equals("1")) {
                    doc = " Mensal";
                } else {
                    doc = " de Rescisão";
                }
            }
            PdfPCell emp2 = new PdfPCell(new Paragraph("Demonstrativo de Pagamento" + doc, f));
            PdfPCell emp3 = new PdfPCell(new Paragraph(contracheque.getFiliais().getEndereco() + " - " + contracheque.getFiliais().getBairro()
                    + " - " + contracheque.getFiliais().getCidade() + " - " + contracheque.getFiliais().getUF() + " - " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString(contracheque.getFiliais().getCEP(), "##.###-###"), f1));
            PdfPCell emp4 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString(contracheque.getFiliais().getCNPJ(), "##.###.###/####-##"), f1));
            PdfPCell emp5 = new PdfPCell(new Paragraph("Competência - " + codmovfp, f1));
            emp1.setColspan(2);
            emp2.setHorizontalAlignment(Element.ALIGN_RIGHT);
            emp3.setColspan(3);
            emp4.setColspan(2);
            emp5.setHorizontalAlignment(Element.ALIGN_RIGHT);
            emp1.setBorderWidthTop(0);
            emp1.setBorderWidthBottom(0);
            emp1.setBorderWidthLeft(0);
            emp1.setBorderWidthRight(0);
            emp2.setBorderWidthTop(0);
            emp2.setBorderWidthBottom(0);
            emp2.setBorderWidthLeft(0);
            emp2.setBorderWidthRight(0);
            emp3.setBorderWidthTop(0);
            emp3.setBorderWidthBottom(0);
            emp3.setBorderWidthLeft(0);
            emp3.setBorderWidthRight(0);
            emp4.setBorderWidthTop(0);
            emp4.setBorderWidthBottom(0);
            emp4.setBorderWidthLeft(0);
            emp4.setBorderWidthRight(0);
            emp5.setBorderWidthTop(0);
            emp5.setBorderWidthBottom(0);
            emp5.setBorderWidthLeft(0);
            emp5.setBorderWidthRight(0);
            TabelaEmp.addCell(emp1);
            TabelaEmp.addCell(emp2);
            TabelaEmp.addCell(emp3);
            TabelaEmp.addCell(emp4);
            TabelaEmp.addCell(emp5);
            document.add(img);
            //document.add(p);
            document.add(TabelaEmp);
            document.add(p);
            PdfPTable TabelaDados = new PdfPTable(7);
            TabelaDados.setWidthPercentage(100);
            //desenhando cabeçalho do contra-cheque            
            PdfPCell cel1 = new PdfPCell(new Paragraph("Matricula:", f5));
            PdfPCell cel2 = new PdfPCell(new Paragraph("Nome:", f5));
            cel2.setColspan(5);
            PdfPCell cel3 = new PdfPCell(new Paragraph("Data de Admissão:", f5));
            PdfPCell cel4 = new PdfPCell(new Paragraph(contracheque.getFuncion().getMatr().toPlainString().replace(".0", ""), f4));
            PdfPCell cel5 = new PdfPCell(new Paragraph(contracheque.getFuncion().getNome(), f4));
            cel5.setColspan(5);
            PdfPCell cel6 = new PdfPCell(new Paragraph(contracheque.getFuncion().getDt_Admis(), f4));
            PdfPCell cel7 = new PdfPCell(new Paragraph("Lotação:", f5));
            cel7.setColspan(2);
            PdfPCell cel8 = new PdfPCell(new Paragraph(contracheque.getCargos().getCBO().equals("") ? "" : "CBO:", f5));
            PdfPCell cel9 = new PdfPCell(new Paragraph("Função:", f5));
            cel9.setColspan(2);
            PdfPCell cel10 = new PdfPCell(new Paragraph(contracheque.getcCusto().getDescricao().equals("") ? "" : "Centro de Custo:", f5));
            cel10.setColspan(2);
            PdfPCell cel11 = new PdfPCell(new Paragraph(contracheque.getPstServ().getLocal(), f4));
            cel11.setColspan(2);
            PdfPCell cel12 = new PdfPCell(new Paragraph(contracheque.getCargos().getCBO(), f4));
            PdfPCell cel13 = new PdfPCell(new Paragraph(contracheque.getCargos().getDescricao(), f4));
            cel13.setColspan(2);
            PdfPCell cel14 = new PdfPCell(new Paragraph(contracheque.getcCusto().getDescricao(), f4));
            cel14.setColspan(2);

            //PdfPCell cel15 = new PdfPCell(new Paragraph(dados[12],f4));         cel15.setColspan(2);
            //PdfPCell cel16 = new PdfPCell(new Paragraph(dados[13],f4));         cel16.setColspan(2);
            //PdfPCell cel17 = new PdfPCell(new Paragraph(dados[14],f4));         cel17.setColspan(2);
            //PdfPCell cel18 = new PdfPCell(new Paragraph(dados[15],f4));
            PdfPCell cel19 = new PdfPCell(new Paragraph("PIS:", f5));
            PdfPCell cel20 = new PdfPCell(new Paragraph("CPF:", f5));
            PdfPCell cel21 = new PdfPCell(new Paragraph("Identidade:", f5));
            PdfPCell cel22 = new PdfPCell(new Paragraph("", f5));
            //PdfPCell cel23 = new PdfPCell(new Paragraph("Ref.:",f4));
            PdfPCell cel24 = new PdfPCell(new Paragraph("Dep. Sal. Família:", f5));
            cel24.setColspan(2);
            PdfPCell cel25 = new PdfPCell(new Paragraph("Dep. IR:", f5));
            PdfPCell cel26 = new PdfPCell(new Paragraph(contracheque.getFuncion().getPIS(), f4));
            PdfPCell cel27 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString(contracheque.getFuncion().getCPF(), "###.###.###-##"), f4));
            PdfPCell cel28 = new PdfPCell(new Paragraph(contracheque.getFuncion().getRG(), f4));
            PdfPCell cel29 = new PdfPCell(new Paragraph("", f4));
            //PdfPCell cel30 = new PdfPCell(new Paragraph(dados[17],f4));
            PdfPCell cel31 = new PdfPCell(new Paragraph(contracheque.getFuncion().getDepSF(), f4));
            cel31.setColspan(2);
            PdfPCell cel32 = new PdfPCell(new Paragraph(contracheque.getFuncion().getDepIR(), f4));
            cel1.setBorderWidthTop(0);
            cel1.setBorderWidthBottom(0);
            cel1.setBorderWidthLeft(0);
            cel1.setBorderWidthRight(0);
            cel2.setBorderWidthTop(0);
            cel2.setBorderWidthBottom(0);
            cel2.setBorderWidthLeft(0);
            cel2.setBorderWidthRight(0);
            cel3.setBorderWidthTop(0);
            cel3.setBorderWidthBottom(0);
            cel3.setBorderWidthLeft(0);
            cel3.setBorderWidthRight(0);
            cel4.setBorderWidthTop(0);
            cel4.setBorderWidthBottom(0);
            cel4.setBorderWidthLeft(0);
            cel4.setBorderWidthRight(0);
            cel5.setBorderWidthTop(0);
            cel5.setBorderWidthBottom(0);
            cel5.setBorderWidthLeft(0);
            cel5.setBorderWidthRight(0);
            cel6.setBorderWidthTop(0);
            cel6.setBorderWidthBottom(0);
            cel6.setBorderWidthLeft(0);
            cel6.setBorderWidthRight(0);
            cel7.setBorderWidthTop(1);
            cel7.setBorderWidthBottom(0);
            cel7.setBorderWidthLeft(0);
            cel7.setBorderWidthRight(0);
            cel8.setBorderWidthTop(1);
            cel8.setBorderWidthBottom(0);
            cel8.setBorderWidthLeft(0);
            cel8.setBorderWidthRight(0);
            cel9.setBorderWidthTop(1);
            cel9.setBorderWidthBottom(0);
            cel9.setBorderWidthLeft(0);
            cel9.setBorderWidthRight(0);
            cel10.setBorderWidthTop(1);
            cel10.setBorderWidthBottom(0);
            cel10.setBorderWidthLeft(0);
            cel10.setBorderWidthRight(0);
            cel11.setBorderWidthTop(0);
            cel11.setBorderWidthBottom(0);
            cel11.setBorderWidthLeft(0);
            cel11.setBorderWidthRight(0);
            cel12.setBorderWidthTop(0);
            cel12.setBorderWidthBottom(0);
            cel12.setBorderWidthLeft(0);
            cel12.setBorderWidthRight(0);
            cel13.setBorderWidthTop(0);
            cel13.setBorderWidthBottom(0);
            cel13.setBorderWidthLeft(0);
            cel13.setBorderWidthRight(0);
            cel14.setBorderWidthTop(0);
            cel14.setBorderWidthBottom(0);
            cel14.setBorderWidthLeft(0);
            cel14.setBorderWidthRight(0);

            /*cel15.setBorderWidthTop(0);cel15.setBorderWidthBottom(0);cel15.setBorderWidthLeft(0);cel15.setBorderWidthRight(0);
            cel16.setBorderWidthTop(0);cel16.setBorderWidthBottom(0);cel16.setBorderWidthLeft(0);cel16.setBorderWidthRight(0);
            cel17.setBorderWidthTop(0);cel17.setBorderWidthBottom(0);cel17.setBorderWidthLeft(0);cel17.setBorderWidthRight(0);
            cel18.setBorderWidthTop(0);cel18.setBorderWidthBottom(0);cel18.setBorderWidthLeft(0);cel18.setBorderWidthRight(0);*/
            cel19.setBorderWidthTop(1);
            cel19.setBorderWidthBottom(0);
            cel19.setBorderWidthLeft(0);
            cel19.setBorderWidthRight(0);
            cel20.setBorderWidthTop(1);
            cel20.setBorderWidthBottom(0);
            cel20.setBorderWidthLeft(0);
            cel20.setBorderWidthRight(0);
            cel21.setBorderWidthTop(1);
            cel21.setBorderWidthBottom(0);
            cel21.setBorderWidthLeft(0);
            cel21.setBorderWidthRight(0);
            cel22.setBorderWidthTop(1);
            cel22.setBorderWidthBottom(0);
            cel22.setBorderWidthLeft(0);
            cel22.setBorderWidthRight(0);
            //cel23.setBorderWidthTop(1);cel23.setBorderWidthBottom(0);cel23.setBorderWidthLeft(0);cel23.setBorderWidthRight(0);
            cel24.setBorderWidthTop(1);
            cel24.setBorderWidthBottom(0);
            cel24.setBorderWidthLeft(0);
            cel24.setBorderWidthRight(0);
            cel25.setBorderWidthTop(1);
            cel25.setBorderWidthBottom(0);
            cel25.setBorderWidthLeft(0);
            cel25.setBorderWidthRight(0);
            cel26.setBorderWidthTop(0);
            cel26.setBorderWidthBottom(1);
            cel26.setBorderWidthLeft(0);
            cel26.setBorderWidthRight(0);
            cel27.setBorderWidthTop(0);
            cel27.setBorderWidthBottom(1);
            cel27.setBorderWidthLeft(0);
            cel27.setBorderWidthRight(0);
            cel28.setBorderWidthTop(0);
            cel28.setBorderWidthBottom(1);
            cel28.setBorderWidthLeft(0);
            cel28.setBorderWidthRight(0);
            cel29.setBorderWidthTop(0);
            cel29.setBorderWidthBottom(1);
            cel29.setBorderWidthLeft(0);
            cel29.setBorderWidthRight(0);
            //cel30.setBorderWidthTop(0);cel30.setBorderWidthBottom(1);cel30.setBorderWidthLeft(0);cel30.setBorderWidthRight(0);
            cel31.setBorderWidthTop(0);
            cel31.setBorderWidthBottom(1);
            cel31.setBorderWidthLeft(0);
            cel31.setBorderWidthRight(0);
            cel32.setBorderWidthTop(0);
            cel32.setBorderWidthBottom(1);
            cel32.setBorderWidthLeft(0);
            cel32.setBorderWidthRight(0);
            TabelaDados.addCell(cel1);
            TabelaDados.addCell(cel2);
            TabelaDados.addCell(cel3);
            TabelaDados.addCell(cel4);
            TabelaDados.addCell(cel5);
            TabelaDados.addCell(cel6);
            TabelaDados.addCell(cel7);
            TabelaDados.addCell(cel8);
            TabelaDados.addCell(cel9);
            TabelaDados.addCell(cel10);
            TabelaDados.addCell(cel11);
            TabelaDados.addCell(cel12);
            TabelaDados.addCell(cel13);
            TabelaDados.addCell(cel14);
            /*TabelaDados.addCell(cel15);
            TabelaDados.addCell(cel16);
            TabelaDados.addCell(cel17);
            TabelaDados.addCell(cel18);*/
            TabelaDados.addCell(cel19);
            TabelaDados.addCell(cel20);
            TabelaDados.addCell(cel21);
            TabelaDados.addCell(cel22);
            //TabelaDados.addCell(cel23);
            TabelaDados.addCell(cel24);
            TabelaDados.addCell(cel25);
            TabelaDados.addCell(cel26);
            TabelaDados.addCell(cel27);
            TabelaDados.addCell(cel28);
            TabelaDados.addCell(cel29);
            //TabelaDados.addCell(cel30);
            TabelaDados.addCell(cel31);
            TabelaDados.addCell(cel32);
            document.add(TabelaDados);
            Paragraph espaco = new Paragraph(" ");
            espaco.setSpacingBefore(5);
            document.add(espaco);
        } catch (Exception e) {
            throw new Exception("Falha CabecalhoContraCheque- " + e.getMessage());
        }
    }

    public void CorpoContraChequeTitulo() throws Exception {
        Font f4;
        try {
            f4 = new Font(FontFamily.HELVETICA, 10);
            PdfPTable TabelaCorpo = new PdfPTable(new float[]{0.1f, 0.1f, 0.35f, 0.15f, 0.15f, 0.15f});
            TabelaCorpo.setWidthPercentage(100);
            //desenhando cabeçalho do contra-cheque   
            PdfPCell cel1 = new PdfPCell(new Paragraph("Composição Salário", f4));
            PdfPCell cel2 = new PdfPCell(new Paragraph("", f4));
            cel1.setBorderWidthTop(0);
            cel1.setBorderWidthBottom(0);
            cel1.setBorderWidthLeft(0);
            cel1.setBorderWidthRight(0);
            cel2.setBorderWidthTop(0);
            cel2.setBorderWidthBottom(0);
            cel2.setBorderWidthLeft(0);
            cel2.setBorderWidthRight(0);
            cel1.setColspan(3);
            cel2.setColspan(3);
            TabelaCorpo.addCell(cel1);
            TabelaCorpo.addCell(cel2);
            PdfPCell cab1 = new PdfPCell(new Paragraph("Mês/Ano", f4));
            PdfPCell cab2 = new PdfPCell(new Paragraph("Verba", f4));
            PdfPCell cab3 = new PdfPCell(new Paragraph("Descrição", f4));
            PdfPCell cab4 = new PdfPCell(new Paragraph("Ref", f4));
            cab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
            PdfPCell cab5 = new PdfPCell(new Paragraph("Proventos", f4));
            cab5.setHorizontalAlignment(Element.ALIGN_RIGHT);
            PdfPCell cab6 = new PdfPCell(new Paragraph("Descontos", f4));
            cab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cab1.setBorderWidthTop(0);
            cab1.setBorderWidthBottom(1);
            cab1.setBorderWidthLeft(0);
            cab1.setBorderWidthRight(0);
            cab2.setBorderWidthTop(0);
            cab2.setBorderWidthBottom(1);
            cab2.setBorderWidthLeft(0);
            cab2.setBorderWidthRight(0);
            cab3.setBorderWidthTop(0);
            cab3.setBorderWidthBottom(1);
            cab3.setBorderWidthLeft(0);
            cab3.setBorderWidthRight(0);
            cab4.setBorderWidthTop(0);
            cab4.setBorderWidthBottom(1);
            cab4.setBorderWidthLeft(0);
            cab4.setBorderWidthRight(0);
            cab5.setBorderWidthTop(0);
            cab5.setBorderWidthBottom(1);
            cab5.setBorderWidthLeft(0);
            cab5.setBorderWidthRight(0);
            cab6.setBorderWidthTop(0);
            cab6.setBorderWidthBottom(1);
            cab6.setBorderWidthLeft(0);
            cab6.setBorderWidthRight(0);
            TabelaCorpo.addCell(cab1);
            TabelaCorpo.addCell(cab2);
            TabelaCorpo.addCell(cab3);
            TabelaCorpo.addCell(cab4);
            TabelaCorpo.addCell(cab5);
            TabelaCorpo.addCell(cab6);
            document.add(TabelaCorpo);
        } catch (DocumentException e) {
            throw new Exception("Falha CorpoContraChequeTitulo- " + e.getMessage());
        }
    }

    /**
     *
     * @param codmovfp
     * @param contracheque
     * @param fecha
     * @throws Exception
     */
    public void CorpoContraChequeLinhas(String codmovfp, ContraCheque contracheque, int fecha) throws Exception {
        Font f4;
        PdfPTable TabelaCorpo = new PdfPTable(new float[]{0.1f, 0.1f, 0.35f, 0.15f, 0.15f, 0.15f});
        TabelaCorpo.setWidthPercentage(100);
        try {
            f4 = new Font(FontFamily.HELVETICA, 8);
            PdfPCell cab1, cab2, cab3, cab4, cab5, cab6;
            if (fecha == 0) {
                cab1 = new PdfPCell(new Paragraph(codmovfp, f4));
                cab2 = new PdfPCell(new Paragraph(contracheque.getfPLancamentos().getVerba(), f4));
                cab3 = new PdfPCell(new Paragraph(contracheque.getVerbas().getDescricao(), f4));
                cab4 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPLancamentos().getValor().toPlainString(), false), f4));
                cab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
                if (contracheque.getfPLancamentos().getTipo().equals("V")) {
                    cab5 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPLancamentos().getValorCalc().toPlainString(), false), f4));
                    cab5.setHorizontalAlignment(Element.ALIGN_RIGHT);
                    cab6 = new PdfPCell(new Paragraph("", f4));
                    cab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
                } else {
                    cab5 = new PdfPCell(new Paragraph("", f4));
                    cab5.setHorizontalAlignment(Element.ALIGN_RIGHT);
                    cab6 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPLancamentos().getValorCalc().toPlainString(), false), f4));
                    cab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
                }
            } else {
                cab1 = new PdfPCell(new Paragraph("", f4));
                cab2 = new PdfPCell(new Paragraph("", f4));
                cab3 = new PdfPCell(new Paragraph("", f4));
                cab4 = new PdfPCell(new Paragraph("", f4));
                cab5 = new PdfPCell(new Paragraph("", f4));
                cab6 = new PdfPCell(new Paragraph("", f4));
            }
            cab1.setBorderWidthTop(fecha);
            cab1.setBorderWidthBottom(0);
            cab1.setBorderWidthLeft(0);
            cab1.setBorderWidthRight(0);
            cab2.setBorderWidthTop(fecha);
            cab2.setBorderWidthBottom(0);
            cab2.setBorderWidthLeft(0);
            cab2.setBorderWidthRight(0);
            cab3.setBorderWidthTop(fecha);
            cab3.setBorderWidthBottom(0);
            cab3.setBorderWidthLeft(0);
            cab3.setBorderWidthRight(0);
            cab4.setBorderWidthTop(fecha);
            cab4.setBorderWidthBottom(0);
            cab4.setBorderWidthLeft(0);
            cab4.setBorderWidthRight(0);
            cab5.setBorderWidthTop(fecha);
            cab5.setBorderWidthBottom(0);
            cab5.setBorderWidthLeft(0);
            cab5.setBorderWidthRight(0);
            cab6.setBorderWidthTop(fecha);
            cab6.setBorderWidthBottom(0);
            cab6.setBorderWidthLeft(0);
            cab6.setBorderWidthRight(0);
            TabelaCorpo.addCell(cab1);
            TabelaCorpo.addCell(cab2);
            TabelaCorpo.addCell(cab3);
            TabelaCorpo.addCell(cab4);
            TabelaCorpo.addCell(cab5);
            TabelaCorpo.addCell(cab6);
            document.add(TabelaCorpo);
        } catch (Exception e) {
            throw new Exception("Falha CorpoContraChequeLinhas- " + e.getMessage());
        }
    }

    public void RodapeContraCheque(ContraCheque contracheque, String codigo) throws Exception {
        Font f4, f5;
        PdfPTable TabelaCorpo = new PdfPTable(6);
        TabelaCorpo.setWidthPercentage(100);
        try {
            f4 = new Font(FontFamily.HELVETICA, 8);
            f5 = new Font(FontFamily.HELVETICA, 7);
            PdfPCell cab1 = new PdfPCell(new Paragraph("Base FGTS", f5));
            PdfPCell cab2 = new PdfPCell(new Paragraph("FGTS Mês", f5));
            PdfPCell cab3 = new PdfPCell(new Paragraph("Base IRPF", f5));
            PdfPCell cab4 = new PdfPCell(new Paragraph("Sal. Cont. INSS", f5));
            PdfPCell cab5 = new PdfPCell(new Paragraph("Total Proventos", f5));
            PdfPCell cab6 = new PdfPCell(new Paragraph("Total Descontos", f5));

            PdfPCell cab7 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getBaseFGTS().toPlainString(), false), f4));
            PdfPCell cab8 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getFGTS().toPlainString(), false), f4));
            PdfPCell cab9 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getBaseIR().toPlainString(), false), f4));
            PdfPCell cab10 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getBaseINSS().toPlainString(), false), f4));
            PdfPCell cab11 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getProventos().toPlainString(), false), f4));
            PdfPCell cab12 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getDescontos().toPlainString(), false), f4));

            PdfPCell cab13 = new PdfPCell(new Paragraph(" ", f4));
            PdfPCell cab14 = new PdfPCell(new Paragraph("Valor Líquido ->", f4));
            PdfPCell cab15 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getLiquido().toPlainString(), false), f4));
            PdfPCell cab16 = new PdfPCell(new Paragraph(" ", f4));

            cab13.setColspan(4);
            cab14.setRowspan(2);
            cab15.setRowspan(2);
            cab16.setColspan(4);

            cab1.setBorderWidthTop(1);
            cab1.setBorderWidthBottom(0);
            cab1.setBorderWidthLeft(1);
            cab1.setBorderWidthRight(0);
            cab2.setBorderWidthTop(1);
            cab2.setBorderWidthBottom(0);
            cab2.setBorderWidthLeft(1);
            cab2.setBorderWidthRight(0);
            cab3.setBorderWidthTop(1);
            cab3.setBorderWidthBottom(0);
            cab3.setBorderWidthLeft(1);
            cab3.setBorderWidthRight(0);
            cab4.setBorderWidthTop(1);
            cab4.setBorderWidthBottom(0);
            cab4.setBorderWidthLeft(1);
            cab4.setBorderWidthRight(0);
            cab5.setBorderWidthTop(1);
            cab5.setBorderWidthBottom(0);
            cab5.setBorderWidthLeft(1);
            cab5.setBorderWidthRight(0);
            cab6.setBorderWidthTop(1);
            cab6.setBorderWidthBottom(0);
            cab6.setBorderWidthLeft(1);
            cab6.setBorderWidthRight(1);

            cab7.setBorderWidthTop(0);
            cab7.setBorderWidthBottom(0);
            cab7.setBorderWidthLeft(1);
            cab7.setBorderWidthRight(0);
            cab8.setBorderWidthTop(0);
            cab8.setBorderWidthBottom(0);
            cab8.setBorderWidthLeft(1);
            cab8.setBorderWidthRight(0);
            cab9.setBorderWidthTop(0);
            cab9.setBorderWidthBottom(0);
            cab9.setBorderWidthLeft(1);
            cab9.setBorderWidthRight(0);
            cab10.setBorderWidthTop(0);
            cab10.setBorderWidthBottom(0);
            cab10.setBorderWidthLeft(1);
            cab10.setBorderWidthRight(0);
            cab11.setBorderWidthTop(0);
            cab11.setBorderWidthBottom(0);
            cab11.setBorderWidthLeft(1);
            cab11.setBorderWidthRight(0);
            cab12.setBorderWidthTop(0);
            cab12.setBorderWidthBottom(0);
            cab12.setBorderWidthLeft(1);
            cab12.setBorderWidthRight(1);

            cab13.setBorderWidthTop(1);
            cab13.setBorderWidthBottom(0);
            cab13.setBorderWidthLeft(0);
            cab13.setBorderWidthRight(0);
            cab14.setBorderWidthTop(1);
            cab14.setBorderWidthBottom(1);
            cab14.setBorderWidthLeft(1);
            cab14.setBorderWidthRight(0);
            cab15.setBorderWidthTop(1);
            cab15.setBorderWidthBottom(1);
            cab15.setBorderWidthLeft(1);
            cab15.setBorderWidthRight(1);
            cab16.setBorderWidthTop(0);
            cab16.setBorderWidthBottom(0);
            cab16.setBorderWidthLeft(0);
            cab16.setBorderWidthRight(0);

            cab1.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab2.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab3.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab4.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab5.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab6.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab7.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab8.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab9.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab10.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab11.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab12.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab13.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab14.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab14.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cab15.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab15.setVerticalAlignment(Element.ALIGN_MIDDLE);

            TabelaCorpo.addCell(cab1);
            TabelaCorpo.addCell(cab2);
            TabelaCorpo.addCell(cab3);
            TabelaCorpo.addCell(cab4);
            TabelaCorpo.addCell(cab5);
            TabelaCorpo.addCell(cab6);

            TabelaCorpo.addCell(cab7);
            TabelaCorpo.addCell(cab8);
            TabelaCorpo.addCell(cab9);
            TabelaCorpo.addCell(cab10);
            TabelaCorpo.addCell(cab11);
            TabelaCorpo.addCell(cab12);

            TabelaCorpo.addCell(cab13);
            TabelaCorpo.addCell(cab14);
            TabelaCorpo.addCell(cab15);
            TabelaCorpo.addCell(cab16);

            TabelaCorpo.setSpacingBefore(10);
            TabelaCorpo.setSpacingAfter(5);
            document.add(TabelaCorpo);
            Font f = new Font(FontFamily.HELVETICA, 10);                        
            Paragraph assinatura, assinadoPor, obs, sasw, obs1, obs2, obs3,
                    obs4, obs5, obs6, obs7;
            
            // Carlos 21/10/2022 inclusao:  Assinado Eletronicamente: 
            assinadoPor = new Paragraph("ASSINADO ELETRONICAMENTE", f);
            assinadoPor.setAlignment(Element.ALIGN_CENTER);
            assinadoPor.setSpacingBefore(-10);
            assinadoPor.setSpacingAfter(10);
                        
            assinatura = new Paragraph("Assinatura:________________________________________________", f);
            assinatura.setSpacingBefore(15);
            assinatura.setSpacingAfter(10);
            
            DateFormat sf = new SimpleDateFormat("dd/MM/yyyy");
            DateFormat dt = new SimpleDateFormat("HH:mm");
            Date data = new Date();
            obs = new Paragraph("Documento gerado em: " + sf.format(data) + " às " + dt.format(data) + ". Para fazer a autenticação deste contracheque, acesse web.satmob.com.br, clique em Validar Contracheque e entre o código abaixo:", f4);
            sasw = new Paragraph("Satellite - www.gruposas.com.br", f5);
             /*if (logo.toUpperCase().contains("CORPVS") ) {
            obs1 = new Paragraph("O canal de Denúncia é  um meio de comunicação  direta do grupo CORPVS. Esta ferramenta busca apurar, ");
            obs2 = new Paragraph("investigar e buscar soluções a atos que contrariem o Código de Ética e Conduta da CORPVS, políticas internas e legislações. ");
            obs3 = new Paragraph("A denúncia pode ser realizada na modalidade anônima ou identificável.");
            obs4 = new Paragraph("As informações coletadas pelo Canal  são processadas com absoluto sigilo e com o tratamento adequado para cada situação em conformidade com a legislação brasileira. ");
            obs5 = new Paragraph(" ");
            obs6 = new Paragraph("Canal de Denúncias - disponível em:https://www.corpvs.com.br/canal-de-denuncias/ ");
            obs7 = new Paragraph("Código de Ética e Conduta - disponivel em:  http://intranet.corpvs.com.br/contracheque/ ");
             }*/
            document.add(assinadoPor);
            document.add(assinatura);
            //document.add(obs1);
            //document.add(obs2);
            //document.add(obs3);
           // document.add(obs4);
           // document.add(obs5);
           // document.add(obs6);
           // document.add(obs7);
           // document.add(obs);
            PdfPTable TabelaAutentica = new PdfPTable(new float[]{0.2f, 0.8f});
            TabelaAutentica.setWidthPercentage(100);
            PdfPCell cel1 = new PdfPCell(new Paragraph("Cód. Autenticação:", f4));
            PdfPCell cel2 = new PdfPCell(new Paragraph(codigo, f4));
            cel1.setBorder(0);
            cel2.setBorder(0);
            cel1.setBackgroundColor(BaseColor.DARK_GRAY);
            cel2.setBackgroundColor(BaseColor.DARK_GRAY);
            TabelaAutentica.addCell(cel1);
            TabelaAutentica.addCell(cel2);
            TabelaAutentica.setSpacingBefore(10);
            document.add(TabelaAutentica);
            sasw.setAlignment(Element.ALIGN_RIGHT);
            sasw.setSpacingBefore(5);
            document.add(sasw);
        } catch (Exception e) {
            throw new Exception("Falha RodapeContraCheque- " + e.getMessage());
        }
    }

    public void RodapeContraCheque(ContraCheque contracheque, String codigo, String caminhoAssinatura) throws Exception {
        Font f4, f5;
        PdfPTable TabelaCorpo = new PdfPTable(6);
        TabelaCorpo.setWidthPercentage(100);
        try {
            f4 = new Font(FontFamily.HELVETICA, 8);
            f5 = new Font(FontFamily.HELVETICA, 7);
            PdfPCell cab1 = new PdfPCell(new Paragraph("Base FGTS", f5));
            PdfPCell cab2 = new PdfPCell(new Paragraph("FGTS Mês", f5));
            PdfPCell cab3 = new PdfPCell(new Paragraph("Base IRPF", f5));
            PdfPCell cab4 = new PdfPCell(new Paragraph("Sal. Cont. INSS", f5));
            PdfPCell cab5 = new PdfPCell(new Paragraph("Total Proventos", f5));
            PdfPCell cab6 = new PdfPCell(new Paragraph("Total Descontos", f5));

            PdfPCell cab7 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getBaseFGTS().toPlainString(), false), f4));
            PdfPCell cab8 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getFGTS().toPlainString(), false), f4));
            PdfPCell cab9 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getBaseIR().toPlainString(), false), f4));
            PdfPCell cab10 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getBaseINSS().toPlainString(), false), f4));
            PdfPCell cab11 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getProventos().toPlainString(), false), f4));
            PdfPCell cab12 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getDescontos().toPlainString(), false), f4));

            PdfPCell cab13 = new PdfPCell(new Paragraph(" ", f4));
            PdfPCell cab14 = new PdfPCell(new Paragraph("Valor Líquido ->", f4));
            PdfPCell cab15 = new PdfPCell(new Paragraph(br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda(contracheque.getfPMensal().getLiquido().toPlainString(), false), f4));
            PdfPCell cab16 = new PdfPCell(new Paragraph(" ", f4));

            cab13.setColspan(4);
            cab14.setRowspan(2);
            cab15.setRowspan(2);
            cab16.setColspan(4);

            cab1.setBorderWidthTop(1);
            cab1.setBorderWidthBottom(0);
            cab1.setBorderWidthLeft(1);
            cab1.setBorderWidthRight(0);
            cab2.setBorderWidthTop(1);
            cab2.setBorderWidthBottom(0);
            cab2.setBorderWidthLeft(1);
            cab2.setBorderWidthRight(0);
            cab3.setBorderWidthTop(1);
            cab3.setBorderWidthBottom(0);
            cab3.setBorderWidthLeft(1);
            cab3.setBorderWidthRight(0);
            cab4.setBorderWidthTop(1);
            cab4.setBorderWidthBottom(0);
            cab4.setBorderWidthLeft(1);
            cab4.setBorderWidthRight(0);
            cab5.setBorderWidthTop(1);
            cab5.setBorderWidthBottom(0);
            cab5.setBorderWidthLeft(1);
            cab5.setBorderWidthRight(0);
            cab6.setBorderWidthTop(1);
            cab6.setBorderWidthBottom(0);
            cab6.setBorderWidthLeft(1);
            cab6.setBorderWidthRight(1);

            cab7.setBorderWidthTop(0);
            cab7.setBorderWidthBottom(0);
            cab7.setBorderWidthLeft(1);
            cab7.setBorderWidthRight(0);
            cab8.setBorderWidthTop(0);
            cab8.setBorderWidthBottom(0);
            cab8.setBorderWidthLeft(1);
            cab8.setBorderWidthRight(0);
            cab9.setBorderWidthTop(0);
            cab9.setBorderWidthBottom(0);
            cab9.setBorderWidthLeft(1);
            cab9.setBorderWidthRight(0);
            cab10.setBorderWidthTop(0);
            cab10.setBorderWidthBottom(0);
            cab10.setBorderWidthLeft(1);
            cab10.setBorderWidthRight(0);
            cab11.setBorderWidthTop(0);
            cab11.setBorderWidthBottom(0);
            cab11.setBorderWidthLeft(1);
            cab11.setBorderWidthRight(0);
            cab12.setBorderWidthTop(0);
            cab12.setBorderWidthBottom(0);
            cab12.setBorderWidthLeft(1);
            cab12.setBorderWidthRight(1);

            cab13.setBorderWidthTop(1);
            cab13.setBorderWidthBottom(0);
            cab13.setBorderWidthLeft(0);
            cab13.setBorderWidthRight(0);
            cab14.setBorderWidthTop(1);
            cab14.setBorderWidthBottom(1);
            cab14.setBorderWidthLeft(1);
            cab14.setBorderWidthRight(0);
            cab15.setBorderWidthTop(1);
            cab15.setBorderWidthBottom(1);
            cab15.setBorderWidthLeft(1);
            cab15.setBorderWidthRight(1);
            cab16.setBorderWidthTop(0);
            cab16.setBorderWidthBottom(0);
            cab16.setBorderWidthLeft(0);
            cab16.setBorderWidthRight(0);

            cab1.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab2.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab3.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab4.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab5.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab6.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab7.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab8.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab9.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab10.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab11.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab12.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab13.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab14.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab14.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cab15.setHorizontalAlignment(Element.ALIGN_CENTER);
            cab15.setVerticalAlignment(Element.ALIGN_MIDDLE);

            TabelaCorpo.addCell(cab1);
            TabelaCorpo.addCell(cab2);
            TabelaCorpo.addCell(cab3);
            TabelaCorpo.addCell(cab4);
            TabelaCorpo.addCell(cab5);
            TabelaCorpo.addCell(cab6);

            TabelaCorpo.addCell(cab7);
            TabelaCorpo.addCell(cab8);
            TabelaCorpo.addCell(cab9);
            TabelaCorpo.addCell(cab10);
            TabelaCorpo.addCell(cab11);
            TabelaCorpo.addCell(cab12);

            TabelaCorpo.addCell(cab13);
            TabelaCorpo.addCell(cab14);
            TabelaCorpo.addCell(cab15);
            TabelaCorpo.addCell(cab16);

            TabelaCorpo.setSpacingBefore(10);
            TabelaCorpo.setSpacingAfter(5);
            document.add(TabelaCorpo);

            Image img;
            Font f = new Font(FontFamily.HELVETICA, 10);
            Paragraph assinadoPor, assinatura, obs, sasw, imagem, obs1, obs2, obs3,
                    obs4, obs5, obs6, obs7;

            if (null != caminhoAssinatura && !caminhoAssinatura.equals("")) {
                try {
                    img = Image.getInstance(caminhoAssinatura);
                } catch (Exception ex) {
                    img = Image.getInstance(caminhoAssinatura);
                }
                img.setAlignment(Element.ALIGN_CENTER);
                img.scalePercent(30, 30);

                imagem = new Paragraph("", f);
                imagem.setSpacingBefore(20);
                imagem.add(img);

                // Carlos 21/10/2022 inclusao:  Assinado Eletronicamente: 
                assinadoPor = new Paragraph("ASSINADO ELETRONICAMENTE", f);
                assinadoPor.setAlignment(Element.ALIGN_CENTER);
                assinadoPor.setSpacingBefore(-10);
                assinadoPor.setSpacingAfter(10);
                
                assinatura = new Paragraph("Assinatura:________________________________________________", f);
                assinatura.setAlignment(Element.ALIGN_CENTER);
                assinatura.setSpacingBefore(-10);
                assinatura.setSpacingAfter(10);

                DateFormat sf = new SimpleDateFormat("dd/MM/yyyy");
                DateFormat dt = new SimpleDateFormat("HH:mm");
                Date data = new Date();
                obs = new Paragraph("Documento gerado em: " + sf.format(data) + " às " + dt.format(data) + ". Para fazer a autenticação deste contracheque, acesse web.satmob.com.br, clique em Validar Contracheque e entre o código abaixo:", f4);
            /*
                obs1 = new Paragraph("O canal de Denúncia é  um meio de comunicação  direta do grupo CORPVS. Esta ferramenta busca apurar, ");
            obs2 = new Paragraph("investigar e buscar soluções a atos que contrariem o Código de Ética e Conduta da CORPVS, políticas internas e legislações. ");
            obs3 = new Paragraph("A denúncia pode ser realizada na modalidade anônima ou identificável.");
            obs4 = new Paragraph("As informações coletadas pelo Canal  são processadas com absoluto sigilo e com o tratamento adequado para cada situação em conformidade com a legislação brasileira. ");
            obs5 = new Paragraph(" ");
            obs6 = new Paragraph("Canal de Denúncias - disponível em:https://www.corpvs.com.br/canal-de-denuncias/ ");
            obs7 = new Paragraph("Código de Ética e Conduta - disponivel em:  http://intranet.corpvs.com.br/contracheque/ "); */
            document.add(assinadoPor);
            document.add(assinatura);
            /*
            document.add(obs1);
            document.add(obs2);
            document.add(obs3);
            document.add(obs4);
            document.add(obs5);
            document.add(obs6);
            document.add(obs7);                
 */               
            sasw = new Paragraph("Satellite - www.gruposas.com.br", f5);

            document.add(imagem);
            document.add(assinadoPor);
            document.add(assinatura);
            document.add(obs);
            } else {
                assinatura = new Paragraph("Assinatura:________________________________________________", f);
                assinatura.setSpacingBefore(15);
                assinatura.setSpacingAfter(10);
                DateFormat sf = new SimpleDateFormat("dd/MM/yyyy");
                DateFormat dt = new SimpleDateFormat("HH:mm");
                Date data = new Date();
                obs = new Paragraph("Documento gerado em: " + sf.format(data) + " às " + dt.format(data) + ". Para fazer a autenticação deste contracheque, acesse web.satmob.com.br, clique em Validar Contracheque e entre o código abaixo:", f4);
                assinadoPor = new Paragraph("");
            /*    
            obs1 = new Paragraph("O canal de Denúncia é  um meio de comunicação  direta do grupo CORPVS. Esta ferramenta busca apurar, ");
            obs2 = new Paragraph("investigar e buscar soluções a atos que contrariem o Código de Ética e Conduta da CORPVS, políticas internas e legislações. ");
            obs3 = new Paragraph("A denúncia pode ser realizada na modalidade anônima ou identificável.");
            obs4 = new Paragraph("As informações coletadas pelo Canal  são processadas com absoluto sigilo e com o tratamento adequado para cada situação em conformidade com a legislação brasileira. ");
            obs5 = new Paragraph(" ");
            obs6 = new Paragraph("Canal de Denúncias - disponível em:https://www.corpvs.com.br/canal-de-denuncias/ ");
            obs7 = new Paragraph("Código de Ética e Conduta - disponivel em:  http://intranet.corpvs.com.br/contracheque/ ");
                */
            document.add(assinadoPor);
            document.add(assinatura);
            /*
            document.add(obs1);
            document.add(obs2);
            document.add(obs3);
            document.add(obs4);
            document.add(obs5);
            document.add(obs6);
            document.add(obs7);                
              */  
                sasw = new Paragraph("Satellite - www.gruposas.com.br", f5);
                document.add(assinatura);
                document.add(obs);
            }

            PdfPTable TabelaAutentica = new PdfPTable(new float[]{0.2f, 0.8f});
            TabelaAutentica.setWidthPercentage(100);
            PdfPCell cel1 = new PdfPCell(new Paragraph("Cód. Autenticação:", f4));
            PdfPCell cel2 = new PdfPCell(new Paragraph(codigo, f4));
            cel1.setBorder(0);
            cel2.setBorder(0);
            cel1.setBackgroundColor(BaseColor.DARK_GRAY);
            cel2.setBackgroundColor(BaseColor.DARK_GRAY);
            TabelaAutentica.addCell(cel1);
            TabelaAutentica.addCell(cel2);
            TabelaAutentica.setSpacingBefore(10);
            document.add(TabelaAutentica);
            sasw.setAlignment(Element.ALIGN_RIGHT);
            sasw.setSpacingBefore(5);
            document.add(sasw);
        } catch (Exception e) {
            throw new Exception("Falha RodapeContraCheque- " + e.getMessage());
        }
    }

    public void InformeRendimentos(String Escudo_rep, String Exercicio, String AnoBase,
            String CNPJ, String RazaoSocial, String CPF, String Nome, String Natureza,
            String TotalRendimentos, String INSS, String PrevPrivada, String PensaoAlimenticia, String IRRF,
            String ParcelaIsenta, String Diarias, String Pensao, String Lucros, String PagosSocios, String Indenizacoes, String Outros,
            String Salario13, String IRRF13, String Outros13,
            String QuantidadeMeses,
            String TotalRendimentosAcumulado, String DeducaoDespesasAcaoJudAcumulado, String DeducaoPrevidenciaOficialAcumulado, String DeducaoPensaoAlimentAcumulado, String IRRFAcumulado, String RendimentosIsentosAcumulado,
            List<DIRFDetPS> Dependentes,
            String Responsavel, String Data) throws Exception {
        try {
            Font f1, f2, f3, f4, f5;
            PdfPTable TabelaCab = new PdfPTable(10);
            TabelaCab.setWidthPercentage(100);
            Image img;
            Paragraph p, espaco;
            p = new Paragraph("");
            p.setSpacingAfter(15);
            espaco = new Paragraph("");
            espaco.setSpacingAfter(5);
            img = Image.getInstance(Escudo_rep);
            img.setAlignment(Element.PARAGRAPH);
            img.scalePercent(30, 30);
            f1 = new Font(FontFamily.HELVETICA, 8);
            f2 = new Font(FontFamily.HELVETICA, 8);
            f2.setStyle(FontStyle.BOLD.ordinal());
            f3 = new Font(FontFamily.HELVETICA, 7);
            f4 = new Font(FontFamily.HELVETICA, 8);
            f5 = new Font(FontFamily.HELVETICA, 6);
            PdfPCell emp1 = new PdfPCell(new Paragraph("Ministério da Fazenda"));
            PdfPCell emp2 = new PdfPCell(new Paragraph("Secretaria da Receita Federal do Brasil", f1));
            PdfPCell emp3 = new PdfPCell(new Paragraph("Imposto sobre a Renda da Pessoa Física", f1));
            PdfPCell emp4 = new PdfPCell(new Paragraph("Exercício - " + Exercicio, f1));
            PdfPCell emp5 = new PdfPCell(new Paragraph("Comprovante de Rendimentos Pagos e de"));
            PdfPCell emp6 = new PdfPCell(new Paragraph("Imposto sobra a Renda Retido na Fonte", f1));
            PdfPCell emp7 = new PdfPCell(new Paragraph("", f1));
            PdfPCell emp8 = new PdfPCell(new Paragraph("Ano-calendário - " + AnoBase, f1));
            PdfPCell empimg = new PdfPCell(img);
            empimg.setRowspan(4);
            empimg.setColspan(1);
            emp1.setColspan(4);
            emp2.setColspan(4);
            emp3.setColspan(4);
            emp4.setColspan(4);
            emp5.setColspan(5);
            emp6.setColspan(5);
            emp7.setColspan(5);
            emp8.setColspan(5);
            emp1.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp2.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp3.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp4.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp5.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp6.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp7.setHorizontalAlignment(Element.ALIGN_CENTER);
            emp8.setHorizontalAlignment(Element.ALIGN_CENTER);
            empimg.setBorderWidthTop(1);
            empimg.setBorderWidthBottom(0);
            empimg.setBorderWidthLeft(1);
            empimg.setBorderWidthRight(0);
            emp1.setBorderWidthTop(1);
            emp1.setBorderWidthBottom(0);
            emp1.setBorderWidthLeft(0);
            emp1.setBorderWidthRight(1);
            emp2.setBorderWidthTop(0);
            emp2.setBorderWidthBottom(0);
            emp2.setBorderWidthLeft(0);
            emp2.setBorderWidthRight(1);
            emp3.setBorderWidthTop(0);
            emp3.setBorderWidthBottom(0);
            emp3.setBorderWidthLeft(0);
            emp3.setBorderWidthRight(1);
            emp4.setBorderWidthTop(0);
            emp4.setBorderWidthBottom(0);
            emp4.setBorderWidthLeft(0);
            emp4.setBorderWidthRight(1);
            emp5.setBorderWidthTop(1);
            emp5.setBorderWidthBottom(0);
            emp5.setBorderWidthLeft(0);
            emp5.setBorderWidthRight(1);
            emp6.setBorderWidthTop(0);
            emp6.setBorderWidthBottom(0);
            emp6.setBorderWidthLeft(0);
            emp6.setBorderWidthRight(1);
            emp7.setBorderWidthTop(0);
            emp7.setBorderWidthBottom(0);
            emp7.setBorderWidthLeft(0);
            emp7.setBorderWidthRight(1);
            emp8.setBorderWidthTop(0);
            emp8.setBorderWidthBottom(0);
            emp8.setBorderWidthLeft(0);
            emp8.setBorderWidthRight(1);
            TabelaCab.addCell(empimg);
            TabelaCab.addCell(emp1);
            TabelaCab.addCell(emp5);
            TabelaCab.addCell(emp2);
            TabelaCab.addCell(emp6);
            TabelaCab.addCell(emp3);
            TabelaCab.addCell(emp7);
            TabelaCab.addCell(emp4);
            TabelaCab.addCell(emp8);

            PdfPTable TabelaObs = new PdfPTable(1);
            TabelaObs.setWidthPercentage(100);
            PdfPCell obs1 = new PdfPCell(new Paragraph("Verifique as condições e o prazo para a apresentação da Declaração do Imposto sobre a Renda da Pessoa Física para este", f1));
            PdfPCell obs2 = new PdfPCell(new Paragraph("ano-calendário no sítio da Secretaria da Receita Federal do Brasil na Internet, no endereço <www.receita.fazenda.gov.br>", f1));
            obs1.setHorizontalAlignment(Element.ALIGN_CENTER);
            obs2.setHorizontalAlignment(Element.ALIGN_CENTER);
            obs1.setBorderWidthTop(1);
            obs1.setBorderWidthBottom(0);
            obs1.setBorderWidthLeft(1);
            obs1.setBorderWidthRight(1);
            obs2.setBorderWidthTop(0);
            obs2.setBorderWidthBottom(1);
            obs2.setBorderWidthLeft(1);
            obs2.setBorderWidthRight(1);
            TabelaObs.addCell(obs1);
            TabelaObs.addCell(obs2);

            PdfPCell tab1;
            PdfPCell tab2;
            PdfPCell tab3;
            PdfPCell tab4;
            PdfPCell tab5;
            PdfPCell tab6;
            PdfPCell tab7;
            PdfPCell tab8;
            PdfPCell tab9;
            PdfPCell tab10;
            PdfPCell tab11;
            PdfPCell tab12;
            PdfPCell tab13;
            PdfPCell tab14;
            PdfPCell tab15;
            PdfPCell tab16;
            //Fonte pagadora
            PdfPTable Tabela1 = new PdfPTable(4);
            Tabela1.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("1. Fonte Pagadora Pessoa Jurídica", f2));
            tab2 = new PdfPCell(new Paragraph("CNPJ", f5));
            tab3 = new PdfPCell(new Paragraph("Nome empresarial", f5));
            tab4 = new PdfPCell(new Paragraph(CNPJ, f1));
            tab5 = new PdfPCell(new Paragraph(RazaoSocial, f1));
            tab1.setColspan(4);
            tab2.setColspan(1);
            tab3.setColspan(3);
            tab4.setColspan(1);
            tab5.setColspan(3);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(1);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(1);
            tab2.setBorderWidthRight(1);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(0);
            tab3.setBorderWidthLeft(0);
            tab3.setBorderWidthRight(1);
            tab4.setBorderWidthTop(0);
            tab4.setBorderWidthBottom(1);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(0);
            tab5.setBorderWidthRight(1);
            Tabela1.addCell(tab1);
            Tabela1.addCell(tab2);
            Tabela1.addCell(tab3);
            Tabela1.addCell(tab4);
            Tabela1.addCell(tab5);

            //Pessao Física beneficiária
            PdfPTable Tabela2 = new PdfPTable(4);
            Tabela2.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("2. Pessoa Física Beneficiária dos Rendimentos", f2));
            tab2 = new PdfPCell(new Paragraph("CPF", f5));
            tab3 = new PdfPCell(new Paragraph("Nome completo", f5));
            tab4 = new PdfPCell(new Paragraph(CPF, f1));
            tab5 = new PdfPCell(new Paragraph(Nome, f1));
            tab6 = new PdfPCell(new Paragraph("Natureza do rendimento", f5));
            tab7 = new PdfPCell(new Paragraph(Natureza, f1));
            tab1.setColspan(4);
            tab2.setColspan(1);
            tab3.setColspan(3);
            tab4.setColspan(1);
            tab5.setColspan(3);
            tab6.setColspan(4);
            tab7.setColspan(4);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(1);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(1);
            tab2.setBorderWidthRight(1);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(0);
            tab3.setBorderWidthLeft(0);
            tab3.setBorderWidthRight(1);
            tab4.setBorderWidthTop(0);
            tab4.setBorderWidthBottom(1);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(0);
            tab5.setBorderWidthRight(1);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(0);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(1);
            tab7.setBorderWidthTop(0);
            tab7.setBorderWidthBottom(1);
            tab7.setBorderWidthLeft(1);
            tab7.setBorderWidthRight(1);
            Tabela2.addCell(tab1);
            Tabela2.addCell(tab2);
            Tabela2.addCell(tab3);
            Tabela2.addCell(tab4);
            Tabela2.addCell(tab5);
            Tabela2.addCell(tab6);
            Tabela2.addCell(tab7);

            //Rendimento Tibutáveis, Deduções e Imposto sobre a Renda Retido na Fonte
            PdfPTable Tabela3 = new PdfPTable(4);
            Tabela3.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("3. Rendimentos Tibutáveis, Deduções e Imposto sobre a Renda Retido na Fonte", f2));
            tab2 = new PdfPCell(new Paragraph("Valores em Reais", f2));
            tab3 = new PdfPCell(new Paragraph("1. Total dos rendimentos (inclusive férias)", f3));
            tab4 = new PdfPCell(new Paragraph(TotalRendimentos, f3));
            tab5 = new PdfPCell(new Paragraph("2. Contribuição Previdenciária oficial", f3));
            tab6 = new PdfPCell(new Paragraph(INSS, f3));
            tab7 = new PdfPCell(new Paragraph("3. Contribuições a entidades de prividência complementar e a fundos de aposentadoria prog. individual  (Fapi)(preencher também o quadro 7)", f3));
            tab8 = new PdfPCell(new Paragraph(PrevPrivada, f3));
            tab9 = new PdfPCell(new Paragraph("4. Pensão alimentícia (preencher também o quadro 7)", f3));
            tab10 = new PdfPCell(new Paragraph(PensaoAlimenticia, f3));
            tab11 = new PdfPCell(new Paragraph("5. Imposto sobre a renda retido na fonte", f3));
            tab12 = new PdfPCell(new Paragraph(IRRF, f3));
            tab1.setColspan(3);
            tab2.setColspan(1);
            tab3.setColspan(3);
            tab4.setColspan(1);
            tab5.setColspan(3);
            tab6.setColspan(1);
            tab7.setColspan(3);
            tab8.setColspan(1);
            tab9.setColspan(3);
            tab10.setColspan(1);
            tab11.setColspan(3);
            tab12.setColspan(1);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab8.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab9.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab10.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab11.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab12.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(0);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(0);
            tab2.setBorderWidthRight(0);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(1);
            tab3.setBorderWidthLeft(1);
            tab3.setBorderWidthRight(0);
            tab4.setBorderWidthTop(1);
            tab4.setBorderWidthBottom(1);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(0);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(1);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(1);
            tab7.setBorderWidthTop(0);
            tab7.setBorderWidthBottom(1);
            tab7.setBorderWidthLeft(1);
            tab7.setBorderWidthRight(0);
            tab8.setBorderWidthTop(0);
            tab8.setBorderWidthBottom(1);
            tab8.setBorderWidthLeft(1);
            tab8.setBorderWidthRight(1);
            tab9.setBorderWidthTop(0);
            tab9.setBorderWidthBottom(1);
            tab9.setBorderWidthLeft(1);
            tab9.setBorderWidthRight(0);
            tab10.setBorderWidthTop(0);
            tab10.setBorderWidthBottom(1);
            tab10.setBorderWidthLeft(1);
            tab10.setBorderWidthRight(1);
            tab11.setBorderWidthTop(0);
            tab11.setBorderWidthBottom(1);
            tab11.setBorderWidthLeft(1);
            tab11.setBorderWidthRight(0);
            tab12.setBorderWidthTop(0);
            tab12.setBorderWidthBottom(1);
            tab12.setBorderWidthLeft(1);
            tab12.setBorderWidthRight(1);
            Tabela3.addCell(tab1);
            Tabela3.addCell(tab2);
            Tabela3.addCell(tab3);
            Tabela3.addCell(tab4);
            Tabela3.addCell(tab5);
            Tabela3.addCell(tab6);
            Tabela3.addCell(tab7);
            Tabela3.addCell(tab8);
            Tabela3.addCell(tab9);
            Tabela3.addCell(tab10);
            Tabela3.addCell(tab11);
            Tabela3.addCell(tab12);

            //Rendimentos Isentos e Não Tributáveis
            PdfPTable Tabela4 = new PdfPTable(4);
            Tabela4.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("4. Rendimentos Isentos e Não Tributáveis", f2));
            tab2 = new PdfPCell(new Paragraph("Valores em Reais", f2));
            tab3 = new PdfPCell(new Paragraph("1. Parcela isenta dos proventos de aposentadoria, reserva remunerada, reforma e pensão (65 anos ou mais)", f3));
            tab4 = new PdfPCell(new Paragraph(ParcelaIsenta, f3));
            tab5 = new PdfPCell(new Paragraph("2. Diárias e ajudas de custo", f3));
            tab6 = new PdfPCell(new Paragraph(Diarias, f3));
            tab7 = new PdfPCell(new Paragraph("3. Pensão e proventos de aposentadoria ou reforma por moléstia grave, proventos de aposentadoria ou reforma por acidente em serviço", f3));
            tab8 = new PdfPCell(new Paragraph(Pensao, f3));
            tab9 = new PdfPCell(new Paragraph("4. Lucros e dividendos, apurados a partir de 1996, pagos por pessoa jurídica (lucro real, presumido ou arbitrado)", f3));
            tab10 = new PdfPCell(new Paragraph(Lucros, f3));
            tab11 = new PdfPCell(new Paragraph("5. Valores pagos ao titular ou sócio da microempresa ou empresa de pequeno porte, exceto pro labore, aluguéis ou serviços prestados", f3));
            tab12 = new PdfPCell(new Paragraph(PagosSocios, f3));
            tab13 = new PdfPCell(new Paragraph("6. Indenizações por rescisão de contrato de trabalho, inclusive a título de PDV, e por acidente de trabalho", f3));
            tab14 = new PdfPCell(new Paragraph(Indenizacoes, f3));
            tab15 = new PdfPCell(new Paragraph("7. Outros", f3));
            tab16 = new PdfPCell(new Paragraph(Outros, f3));
            tab1.setColspan(3);
            tab2.setColspan(1);
            tab3.setColspan(3);
            tab4.setColspan(1);
            tab5.setColspan(3);
            tab6.setColspan(1);
            tab7.setColspan(3);
            tab8.setColspan(1);
            tab9.setColspan(3);
            tab10.setColspan(1);
            tab11.setColspan(3);
            tab12.setColspan(1);
            tab13.setColspan(3);
            tab14.setColspan(1);
            tab15.setColspan(3);
            tab16.setColspan(1);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab8.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab9.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab10.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab11.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab12.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab13.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab14.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab15.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab16.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(0);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(0);
            tab2.setBorderWidthRight(0);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(1);
            tab3.setBorderWidthLeft(1);
            tab3.setBorderWidthRight(0);
            tab4.setBorderWidthTop(1);
            tab4.setBorderWidthBottom(1);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(0);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(1);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(1);
            tab7.setBorderWidthTop(0);
            tab7.setBorderWidthBottom(1);
            tab7.setBorderWidthLeft(1);
            tab7.setBorderWidthRight(0);
            tab8.setBorderWidthTop(0);
            tab8.setBorderWidthBottom(1);
            tab8.setBorderWidthLeft(1);
            tab8.setBorderWidthRight(1);
            tab9.setBorderWidthTop(0);
            tab9.setBorderWidthBottom(1);
            tab9.setBorderWidthLeft(1);
            tab9.setBorderWidthRight(0);
            tab10.setBorderWidthTop(0);
            tab10.setBorderWidthBottom(1);
            tab10.setBorderWidthLeft(1);
            tab10.setBorderWidthRight(1);
            tab11.setBorderWidthTop(0);
            tab11.setBorderWidthBottom(1);
            tab11.setBorderWidthLeft(1);
            tab11.setBorderWidthRight(0);
            tab12.setBorderWidthTop(0);
            tab12.setBorderWidthBottom(1);
            tab12.setBorderWidthLeft(1);
            tab12.setBorderWidthRight(1);
            tab13.setBorderWidthTop(0);
            tab13.setBorderWidthBottom(1);
            tab13.setBorderWidthLeft(1);
            tab13.setBorderWidthRight(0);
            tab14.setBorderWidthTop(0);
            tab14.setBorderWidthBottom(1);
            tab14.setBorderWidthLeft(1);
            tab14.setBorderWidthRight(1);
            tab15.setBorderWidthTop(0);
            tab15.setBorderWidthBottom(1);
            tab15.setBorderWidthLeft(1);
            tab15.setBorderWidthRight(0);
            tab16.setBorderWidthTop(0);
            tab16.setBorderWidthBottom(1);
            tab16.setBorderWidthLeft(1);
            tab16.setBorderWidthRight(1);
            Tabela4.addCell(tab1);
            Tabela4.addCell(tab2);
            Tabela4.addCell(tab3);
            Tabela4.addCell(tab4);
            Tabela4.addCell(tab5);
            Tabela4.addCell(tab6);
            Tabela4.addCell(tab7);
            Tabela4.addCell(tab8);
            Tabela4.addCell(tab9);
            Tabela4.addCell(tab10);
            Tabela4.addCell(tab11);
            Tabela4.addCell(tab12);
            Tabela4.addCell(tab13);
            Tabela4.addCell(tab14);
            Tabela4.addCell(tab15);
            Tabela4.addCell(tab16);

            //Rendimentos sujeitos à Tributação Exclusiva (rendimento líquido)
            PdfPTable Tabela5 = new PdfPTable(4);
            Tabela5.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("5. Rendimentos sujeitos à Tributação Exclusiva (rendimento líquido)", f2));
            tab2 = new PdfPCell(new Paragraph("Valores em Reais", f2));
            tab3 = new PdfPCell(new Paragraph("1. Décimo terceiro salário", f3));
            tab4 = new PdfPCell(new Paragraph(Salario13, f3));
            tab5 = new PdfPCell(new Paragraph("2. Imposto sobre a renda retido na fonte sobre 13º salário", f3));
            tab6 = new PdfPCell(new Paragraph(IRRF13, f3));
            tab7 = new PdfPCell(new Paragraph("3. Outros", f3));
            tab8 = new PdfPCell(new Paragraph(Outros13, f3));
            tab1.setColspan(3);
            tab2.setColspan(1);
            tab3.setColspan(3);
            tab4.setColspan(1);
            tab5.setColspan(3);
            tab6.setColspan(1);
            tab7.setColspan(3);
            tab8.setColspan(1);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab8.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(0);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(0);
            tab2.setBorderWidthRight(0);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(1);
            tab3.setBorderWidthLeft(1);
            tab3.setBorderWidthRight(0);
            tab4.setBorderWidthTop(1);
            tab4.setBorderWidthBottom(1);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(0);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(1);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(1);
            tab7.setBorderWidthTop(0);
            tab7.setBorderWidthBottom(1);
            tab7.setBorderWidthLeft(1);
            tab7.setBorderWidthRight(0);
            tab8.setBorderWidthTop(0);
            tab8.setBorderWidthBottom(1);
            tab8.setBorderWidthLeft(1);
            tab8.setBorderWidthRight(1);
            Tabela5.addCell(tab1);
            Tabela5.addCell(tab2);
            Tabela5.addCell(tab3);
            Tabela5.addCell(tab4);
            Tabela5.addCell(tab5);
            Tabela5.addCell(tab6);
            Tabela5.addCell(tab7);
            Tabela5.addCell(tab8);

            //Rendimentos recebidos Acumuladamente - Art. 12-A da Lei nº7.713, de 1988 (sujeito à tributação exclusiva)
            //Cabeçalho da tabela 6
            PdfPTable Tabela6C = new PdfPTable(4);
            Tabela6C.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("6. Rendimentos recebidos Acumuladamente - Art. 12-A da Lei nº7.713, de 1988 (sujeito à tributação exclusiva)", f2));
            tab1.setColspan(4);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            Tabela6C.addCell(tab1);

            PdfPTable Tabela61 = new PdfPTable(8);
            Tabela61.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("6.1 Número de processo:", f1));
            tab2 = new PdfPCell(new Paragraph("Quantidade de meses", f1));
            tab3 = new PdfPCell(new Paragraph(QuantidadeMeses, f1));
            tab4 = new PdfPCell(new Paragraph("", f1));
            tab5 = new PdfPCell(new Paragraph("Natureza do Rendimento:", f1));
            tab6 = new PdfPCell(new Paragraph("Valores em Reais", f2));
            tab1.setColspan(4);
            tab2.setColspan(1);
            tab3.setColspan(1);
            tab4.setColspan(2);
            tab5.setColspan(6);
            tab6.setColspan(2);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_CENTER);
            tab3.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab1.setBorderWidthTop(1);
            tab1.setBorderWidthBottom(1);
            tab1.setBorderWidthLeft(1);
            tab1.setBorderWidthRight(1);
            tab2.setBorderWidthTop(1);
            tab2.setBorderWidthBottom(1);
            tab2.setBorderWidthLeft(0);
            tab2.setBorderWidthRight(1);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(1);
            tab3.setBorderWidthLeft(0);
            tab3.setBorderWidthRight(0);
            tab4.setBorderWidthTop(0);
            tab4.setBorderWidthBottom(0);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(0);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(0);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(0);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(0);
            Tabela61.addCell(tab1);
            Tabela61.addCell(tab2);
            Tabela61.addCell(tab3);
            Tabela61.addCell(tab4);
            Tabela61.addCell(tab5);
            Tabela61.addCell(tab6);

            PdfPTable Tabela6 = new PdfPTable(4);
            Tabela6.setWidthPercentage(100);
            tab3 = new PdfPCell(new Paragraph("1. Total dos rendimentos tributáveis (inclusive férias e décimo terceiro salário)", f3));
            tab4 = new PdfPCell(new Paragraph(TotalRendimentosAcumulado, f3));
            tab5 = new PdfPCell(new Paragraph("2. Exclusão: Despesas com a ação judicial", f3));
            tab6 = new PdfPCell(new Paragraph(DeducaoDespesasAcaoJudAcumulado, f3));
            tab7 = new PdfPCell(new Paragraph("3. Dedução: Contribuição previdência oficial", f3));
            tab8 = new PdfPCell(new Paragraph(DeducaoPrevidenciaOficialAcumulado, f3));
            tab9 = new PdfPCell(new Paragraph("4. Dedução: Pensão alimentícia (preencher também o quadro 7)", f3));
            tab10 = new PdfPCell(new Paragraph(DeducaoPensaoAlimentAcumulado, f3));
            tab11 = new PdfPCell(new Paragraph("5. Imposto sobre a renda retido na fonte", f3));
            tab12 = new PdfPCell(new Paragraph(IRRFAcumulado, f3));
            tab13 = new PdfPCell(new Paragraph("6. Rendimentos isentos de pensão, proventos de aposentadoria ou reforma por moléstia grave ou aposentadoria ou reforma por acidente em serviço", f3));
            tab14 = new PdfPCell(new Paragraph(RendimentosIsentosAcumulado, f3));
            tab3.setColspan(3);
            tab4.setColspan(1);
            tab5.setColspan(3);
            tab6.setColspan(1);
            tab7.setColspan(3);
            tab8.setColspan(1);
            tab9.setColspan(3);
            tab10.setColspan(1);
            tab11.setColspan(3);
            tab12.setColspan(1);
            tab13.setColspan(3);
            tab14.setColspan(1);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab8.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab9.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab10.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab11.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab12.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab13.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab14.setHorizontalAlignment(Element.ALIGN_RIGHT);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(1);
            tab3.setBorderWidthLeft(1);
            tab3.setBorderWidthRight(0);
            tab4.setBorderWidthTop(1);
            tab4.setBorderWidthBottom(1);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(1);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(0);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(1);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(1);
            tab7.setBorderWidthTop(0);
            tab7.setBorderWidthBottom(1);
            tab7.setBorderWidthLeft(1);
            tab7.setBorderWidthRight(0);
            tab8.setBorderWidthTop(0);
            tab8.setBorderWidthBottom(1);
            tab8.setBorderWidthLeft(1);
            tab8.setBorderWidthRight(1);
            tab9.setBorderWidthTop(0);
            tab9.setBorderWidthBottom(1);
            tab9.setBorderWidthLeft(1);
            tab9.setBorderWidthRight(0);
            tab10.setBorderWidthTop(0);
            tab10.setBorderWidthBottom(1);
            tab10.setBorderWidthLeft(1);
            tab10.setBorderWidthRight(1);
            tab11.setBorderWidthTop(0);
            tab11.setBorderWidthBottom(1);
            tab11.setBorderWidthLeft(1);
            tab11.setBorderWidthRight(0);
            tab12.setBorderWidthTop(0);
            tab12.setBorderWidthBottom(1);
            tab12.setBorderWidthLeft(1);
            tab12.setBorderWidthRight(1);
            tab13.setBorderWidthTop(0);
            tab13.setBorderWidthBottom(1);
            tab13.setBorderWidthLeft(1);
            tab13.setBorderWidthRight(0);
            tab14.setBorderWidthTop(0);
            tab14.setBorderWidthBottom(1);
            tab14.setBorderWidthLeft(1);
            tab14.setBorderWidthRight(1);
            Tabela6.addCell(tab3);
            Tabela6.addCell(tab4);
            Tabela6.addCell(tab5);
            Tabela6.addCell(tab6);
            Tabela6.addCell(tab7);
            Tabela6.addCell(tab8);
            Tabela6.addCell(tab9);
            Tabela6.addCell(tab10);
            Tabela6.addCell(tab11);
            Tabela6.addCell(tab12);
            Tabela6.addCell(tab13);
            Tabela6.addCell(tab14);

            //Informações Complementares
            PdfPTable Tabela7 = new PdfPTable(8);
            Tabela7.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("7. Informações Complementares", f2));
            tab2 = new PdfPCell(new Paragraph("", f2));
            tab3 = new PdfPCell(new Paragraph("Os rendimentos seguintes estão informados na linha 01, quadro 3 e/ou linha 03, quadro 05:", f1));
            if (!"".equals(TotalRendimentos)) {
                tab4 = new PdfPCell(new Paragraph(" - Rendimentos do trabalho assalariado: R$ " + TotalRendimentos, f1));
            } else {
                tab4 = new PdfPCell(new Paragraph("", f1));
            }
            if (!"".equals(Outros13)) {
                tab5 = new PdfPCell(new Paragraph(" - Outros Rendimentos sujeitos à Tributação Exclusiva: R$ " + Outros13, f1));
            } else {
                tab5 = new PdfPCell(new Paragraph("", f1));
            }
            tab6 = new PdfPCell(new Paragraph("", f1));
            tab7 = new PdfPCell(new Paragraph("", f1));
            tab8 = new PdfPCell(new Paragraph("", f1));
            tab9 = new PdfPCell(new Paragraph("", f1));
            tab10 = new PdfPCell(new Paragraph("", f1));
            tab1.setColspan(8);
            tab2.setColspan(8);
            tab3.setColspan(8);
            tab4.setColspan(8);
            tab5.setColspan(8);

            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);

            tab1.setBorderWidthTop(0);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(0);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(0);
            tab2.setBorderWidthRight(0);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(0);
            tab3.setBorderWidthLeft(1);
            tab3.setBorderWidthRight(1);
            tab4.setBorderWidthTop(0);
            tab4.setBorderWidthBottom(0);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(1);
            tab5.setBorderWidthTop(0);
            tab5.setBorderWidthBottom(0);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(1);

            Tabela7.addCell(tab1);
            Tabela7.addCell(tab2);
            Tabela7.addCell(tab3);
            Tabela7.addCell(tab4);
            Tabela7.addCell(tab5);

            for (DIRFDetPS ps : Dependentes) {
                tab6 = new PdfPCell(new Paragraph("Pagamentos a planos de saúde", f1));
                tab7 = new PdfPCell(new Paragraph("Valor pago no ano referente aos dependentes:", f1));
                tab8 = new PdfPCell(new Paragraph("CPF", f1));
                tab9 = new PdfPCell(new Paragraph("Nome", f1));
                tab10 = new PdfPCell(new Paragraph("Valor", f1));

                tab6.setColspan(8);
                tab7.setColspan(8);
                tab8.setColspan(2);
                tab9.setColspan(4);
                tab10.setColspan(2);

                tab6.setHorizontalAlignment(Element.ALIGN_LEFT);
                tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
                tab8.setHorizontalAlignment(Element.ALIGN_LEFT);
                tab9.setHorizontalAlignment(Element.ALIGN_LEFT);
                tab10.setHorizontalAlignment(Element.ALIGN_LEFT);

                tab6.setBorderWidthTop(0);
                tab6.setBorderWidthBottom(0);
                tab6.setBorderWidthLeft(1);
                tab6.setBorderWidthRight(1);
                tab7.setBorderWidthTop(0);
                tab7.setBorderWidthBottom(0);
                tab7.setBorderWidthLeft(1);
                tab7.setBorderWidthRight(1);
                tab8.setBorderWidthTop(0);
                tab8.setBorderWidthBottom(0);
                tab8.setBorderWidthLeft(1);
                tab8.setBorderWidthRight(0);
                tab9.setBorderWidthTop(0);
                tab9.setBorderWidthBottom(0);
                tab9.setBorderWidthLeft(0);
                tab9.setBorderWidthRight(0);
                tab10.setBorderWidthTop(0);
                tab10.setBorderWidthBottom(0);
                tab10.setBorderWidthLeft(0);
                tab10.setBorderWidthRight(1);

                if ("T".equals(ps.getTipoDep())) {
                    tab6.addElement(new Paragraph("Operadora: "
                            + FuncoesString.formatarString(ps.getCNPJOPSE(), "##.###.###/####-##")
                            + " - " + ps.getNomeOPSE(), f1));
                    tab6.addElement(new Paragraph("Valor pago no ano referente ao titular: "
                            + FuncoesString.formatarStringMoeda(ps.getValor().toPlainString(), true), f1));

                    Tabela7.addCell(tab6);
                } else {
                    try {
                        tab8.addElement(new Paragraph(FuncoesString.formatarString(ps.getCPFDep(), "###.###.###-##"), f1));
                    } catch (Exception e) {
                        tab8.addElement(new Paragraph("", f1));
                    }
                    tab9.addElement(new Paragraph(ps.getNome(), f1));
                    tab10.addElement(new Paragraph(FuncoesString.formatarStringMoeda(ps.getValor().toPlainString(), true), f1));

                    Tabela7.addCell(tab7);
                    Tabela7.addCell(tab8);
                    Tabela7.addCell(tab9);
                    Tabela7.addCell(tab10);
                }

            }

            //Responsável pelas informações
            PdfPTable Tabela8 = new PdfPTable(8);
            Tabela8.setWidthPercentage(100);
            tab1 = new PdfPCell(new Paragraph("8. Responsável pelas informações", f2));
            tab2 = new PdfPCell(new Paragraph("", f2));
            tab3 = new PdfPCell(new Paragraph("Nome", f5));
            tab4 = new PdfPCell(new Paragraph("Data", f5));
            tab5 = new PdfPCell(new Paragraph("Assinatura", f5));
            tab6 = new PdfPCell(new Paragraph(Responsavel, f1));
            tab7 = new PdfPCell(new Paragraph(Data, f1));
            tab8 = new PdfPCell(new Paragraph("", f1));
            tab9 = new PdfPCell(new Paragraph("Aprovado pela INRFB nº1.215, de 15 de dezembro de 2011", f5));
            tab1.setColspan(8);
            tab2.setColspan(2);
            tab3.setColspan(5);
            tab4.setColspan(1);
            tab5.setColspan(2);
            tab6.setColspan(5);
            tab7.setColspan(1);
            tab8.setColspan(2);
            tab9.setColspan(8);
            tab1.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab2.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab3.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab4.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab5.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab6.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab7.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab8.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab9.setHorizontalAlignment(Element.ALIGN_LEFT);
            tab1.setBorderWidthTop(1);
            tab1.setBorderWidthBottom(0);
            tab1.setBorderWidthLeft(0);
            tab1.setBorderWidthRight(0);
            tab2.setBorderWidthTop(0);
            tab2.setBorderWidthBottom(0);
            tab2.setBorderWidthLeft(0);
            tab2.setBorderWidthRight(0);
            tab3.setBorderWidthTop(1);
            tab3.setBorderWidthBottom(0);
            tab3.setBorderWidthLeft(1);
            tab3.setBorderWidthRight(0);
            tab4.setBorderWidthTop(1);
            tab4.setBorderWidthBottom(0);
            tab4.setBorderWidthLeft(1);
            tab4.setBorderWidthRight(0);
            tab5.setBorderWidthTop(1);
            tab5.setBorderWidthBottom(0);
            tab5.setBorderWidthLeft(1);
            tab5.setBorderWidthRight(1);
            tab6.setBorderWidthTop(0);
            tab6.setBorderWidthBottom(1);
            tab6.setBorderWidthLeft(1);
            tab6.setBorderWidthRight(0);
            tab7.setBorderWidthTop(0);
            tab7.setBorderWidthBottom(1);
            tab7.setBorderWidthLeft(1);
            tab7.setBorderWidthRight(0);
            tab8.setBorderWidthTop(0);
            tab8.setBorderWidthBottom(1);
            tab8.setBorderWidthLeft(1);
            tab8.setBorderWidthRight(1);
            tab9.setBorderWidthTop(0);
            tab9.setBorderWidthBottom(0);
            tab9.setBorderWidthLeft(0);
            tab9.setBorderWidthRight(0);
            Tabela8.addCell(tab1);
            //Tabela8.addCell(tab2);
            Tabela8.addCell(tab3);
            Tabela8.addCell(tab4);
            Tabela8.addCell(tab5);
            Tabela8.addCell(tab6);
            Tabela8.addCell(tab7);
            Tabela8.addCell(tab8);
            Tabela8.addCell(tab9);
            //document.add(img);
            document.add(TabelaCab);
            document.add(TabelaObs);
            document.add(p);
            document.add(Tabela1);
            document.add(Tabela2);
            document.add(Tabela3);
            document.add(Tabela4);
            document.add(Tabela5);
            document.add(Tabela6C);
            document.add(Tabela61);
            document.add(espaco);
            document.add(Tabela6);
            document.add(Tabela7);
            document.add(Tabela8);
        } catch (Exception e) {
            throw new Exception("Falha ao Informe Rendimentos - " + e.getMessage());
        }
    }

    public void FechaPdf() {
        document.close();
    }

    private static byte[] fileToByte(File imagem) throws Exception {
        FileInputStream fis = new FileInputStream(imagem);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int tamanho = fis.available();
        byte[] buffer = new byte[tamanho];
        int bytesRead;
        while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        fis.close();
        return baos.toByteArray();
    }

    public void service() throws Exception {
        OutputStream ouputStream = null;
        try {
            byte[] arquivo = null;
            file = new File(Arquivo);
            try {
                arquivo = fileToByte(file);
            } catch (Exception e) {
            }
            FacesContext context = FacesContext.getCurrentInstance();
            context.getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) context.getExternalContext().getResponse();
            response.setHeader("Content-type", "application/pdf");
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=" + file.getName());
            response.setHeader("pragma", "public");
            ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            context.responseComplete();
        } catch (Exception e) {
            throw new Exception("Falha ao finalizar arquivo - " + e.getMessage());
        }
    }

    public void sendFileResponse(FacesContext context, HttpServletResponse response) throws IOException {
        final int DEFAULT_BUFFER_SIZE = 10240; // 10 KiB
        FechaPdf();
        file = new File(Arquivo);

        BufferedInputStream input = null;
        BufferedOutputStream output = null;

        try {
            FileInputStream fileStream = new FileInputStream(file);
            input = new BufferedInputStream(fileStream, DEFAULT_BUFFER_SIZE);

            response.reset();
            response.setHeader("Content-type", "application/pdf");
            response.setContentLength((int) file.length());
            response.setHeader("Content-disposition", "inline; filename=" + file.getName());
            response.setHeader("pragma", "public");
            output = new BufferedOutputStream(response.getOutputStream(), DEFAULT_BUFFER_SIZE);

            byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            int length;
            while ((length = input.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }

            output.flush();
        } finally {
            closeResource(output);
            closeResource(input);
        }

        // Informa JSF para não manipular a response.
        // Importante, pois não fazê-lo irá logar a seguinte exceção nos logs:
        // java.lang.IllegalStateException: Cannot forward after response has been committed.
        context.responseComplete();
    }

    private static void closeResource(Closeable resource) throws IOException {
        if (resource != null) {
            try {
                resource.close();
            } catch (IOException e) {
                throw e;
            }
        }
    }

    public String getLogo(String banco) throws Exception {
        return LoginMB.getLogoS(banco);
        /*
        String url;
        switch (banco) {
            case "SATINVLRO":
                url = "assets/logos/logoinviseg.png";
                break;
            case "SATBIMBO":
                url = "assets/logos/logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
                url = "assets/logos/logo-piscina.png";
                break;
            case "SATPROSECUR":
                url = "assets/logos/LogoProsecur.jpg";
                break;
            case "SATIBL":
                url = "assets/logos/logo_ibl.jpg";
                break;
            case "SATSERVITE":
                url = "assets/logos/logo_SATSERVITE.jpg";
                break;
            case "SATINTERFORT":
                url = "assets/logos/logo_interfort.jpg";
                break;
            case "SATGLOVAL":
                url = "assets/logos/logo_gloval.png";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                 try {
                    FacesContext fc = FacesContext.getCurrentInstance();
                    String CodigoFilial = (String) fc.getExternalContext().getSessionMap().get("filial");
                    if (!CodigoFilial.equals("3001")) {
                        url = "assets/logos/logo_confederal.jpg";
                    } else {
                        url = "assets/logos/logo_confere.jpg";
                    }
                } catch (Exception ex) {
                    url = "assets/logos/logo_confederal.jpg";
                }
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
                url = "assets/img/logo_CPV.jpg";
                break;
            case "SATNORSERV":
            case "SATVANTEC":
                url = "assets/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "assets/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "assets/logos/logo_preserve.jpg";
                break;
            case "VSG":
                url = "assets/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "assets/logos/logo_tecnoseg.jpg";
                break;
            case "SATASC":
                url = "assets/logos/logoasc.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                url = "assets/logos/logo_AGIL.jpg";
                break;
            case "SATLOYAL":
                url = "assets/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "assets/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
                url = "assets/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "assets/logos/logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
                try {
                switch (this.codFil) {
                    case "1":
                        url = "assets/logos/logotransexcel.jpg";
                        break;
                    case "2001":
                        url = "assets/logos/logoDepa.jpg";
                        break;
                    case "3001":
                        url = "assets/logos/logoExcel.jpg";
                        break;
                    default:
                        url = "assets/logos/logotransexcel.jpg";
                }
            } catch (Exception e) {
                url = "assets/logos/logotransexcel.jpg";
            }
            break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "assets/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "assets/logos/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "assets/logos/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "assets/logos/LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "assets/logos/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "assets/logos/logosas.jpg";
                break;
            case "SATQUALIFOCO":
                url = "assets/logos/logo_qualifoco.jpg";
                break;
            case "SATTRANSPORTER":
                url = "assets/logos/Logo_Transporter.png";
                break;
            case "SATECOVISAO":
                url = "assets/logos/logo_ecovisao.jpg";
                break;
            case "SATGETLOCK":
                url = "assets/logos/logo_getlock.png";
                break;
            case "SATBRASIFORT":
                url = "assets/logos/Logo_Brasifort.png";
                break;
            case "SATCIT":
                url = "assets/logos/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "assets/logos/logo_shalom.png";
                break;
            case "SATMAXIMA":
                url = "assets/logos/logo_maxima.png";
                break;
            default:
                url = "assets/logos/logo.png";
        }
        return url;*/
    }

    private String TraduzSemana(int dia) {
        switch (dia) {
            case (1):
                return Messages.getMessageS("Domingo");
            case (2):
                return Messages.getMessageS("Segunda");
            case (3):
                return Messages.getMessageS("Terça");
            case (4):
                return Messages.getMessageS("Quarta");
            case (5):
                return Messages.getMessageS("Quinta");
            case (6):
                return Messages.getMessageS("Sexta");
            default:
                return Messages.getMessageS("Sabado");
        }
    }
}

package SasBeansCompostas;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

/**
 *
 * <AUTHOR>
 */
public class CxFGuiasGTVDTO implements Serializable {

    private String Rota;
    private Date Data;
    private String Sequencia;
    private String CodCli1;
    private String Hora1;
    private String ER;
    private int Parada;
    private String NRed;
    private String CliDst;
    private String NRedDst;
    private String Guia;
    private String Serie;
    private BigDecimal TotalGeral;
    private String CodFil;
    private String OS;
    private String Operador;
    private BigDecimal ValorGTV;
    private String ParadaRtGuias;
    private String CliOri;
    private String NRedOri;

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public Date getData() {
        return Data;
    }

    public void setData(Date Data) {
        this.Data = Data;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public int getParada() {
        return Parada;
    }

    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getCliDst() {
        return CliDst;
    }

    public void setCliDst(String CliDst) {
        this.CliDst = CliDst;
    }

    public String getNRedDst() {
        return NRedDst;
    }

    public void setNRedDst(String NRedDst) {
        this.NRedDst = NRedDst;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public BigDecimal getTotalGeral() {
        return TotalGeral;
    }

    public void setTotalGeral(BigDecimal TotalGeral) {
        this.TotalGeral = TotalGeral;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public BigDecimal getValorGTV() {
        return ValorGTV;
    }

    public void setValorGTV(BigDecimal ValorGTV) {
        this.ValorGTV = ValorGTV;
    }

    public String getParadaRtGuias() {
        return ParadaRtGuias;
    }

    public void setParadaRtGuias(String ParadaRtGuias) {
        this.ParadaRtGuias = ParadaRtGuias;
    }

    public String getCliOri() {
        return CliOri;
    }

    public void setCliOri(String CliOri) {
        this.CliOri = CliOri;
    }

    public String getNRedOri() {
        return NRedOri;
    }

    public void setNRedOri(String NRedOri) {
        this.NRedOri = NRedOri;
    }
}

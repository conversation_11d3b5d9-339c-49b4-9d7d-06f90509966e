package SasBeansCompostas;

import SasBeans.Funcion;
import SasBeans.Saspwac;

/**
 *
 * <AUTHOR>
 */
public class FuncSaspwSaspwac {

    private Funcion funcion;
    private Saspwac saspwac;

    /**
     * @return the funcion
     */
    public Funcion getFuncion() {
        return funcion;
    }

    /**
     * @param funcion the funcion to set
     */
    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    /**
     * @return the saspwac
     */
    public Saspwac getSaspwac() {
        return saspwac;
    }

    /**
     * @param saspwac the saspwac to set
     */
    public void setSaspwac(Saspwac saspwac) {
        this.saspwac = saspwac;
    }

}

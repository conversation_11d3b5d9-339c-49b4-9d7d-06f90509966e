/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels.containers;

import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Pedido;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PedidosLazyList extends LazyDataModel<Pedido> {

    private static final long serialVersionUID = 1L;
    private List<Pedido> pedidos;
    private final RotasSatWeb rotassatweb;
    private final Persistencia persistencia;
    private Map filters;

    public PedidosLazyList(Persistencia pst, Map filters) {
        this.rotassatweb = new RotasSatWeb();
        this.persistencia = pst;
        this.filters = filters;
    }

    @Override
    public List<Pedido> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.pedidos = this.rotassatweb.listagemPedidosContainersPaginada(first, pageSize, this.filters, this.persistencia);

            // set the total of players
            setRowCount(this.rotassatweb.contagemPedidosContainers(this.filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.pedidos;
    }

    @Override
    public Object getRowKey(Pedido pedido) {
        return pedido.getNumero().toBigInteger() + ";" + pedido.getCodFil().toBigInteger();
    }

    @Override
    public Pedido getRowData(String pedido) {
        try {
            String numero = pedido.split(";")[0];
            String codfil = pedido.split(";")[1];
            for (Pedido p : this.pedidos) {
                if (numero.equals(p.getNumero().toBigInteger().toString()) && codfil.equals(p.getCodFil().toBigInteger().toString())) {
                    return p;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

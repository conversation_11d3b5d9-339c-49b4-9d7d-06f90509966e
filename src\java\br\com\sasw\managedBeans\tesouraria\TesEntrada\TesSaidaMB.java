/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.tesouraria.TesEntrada;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Controller.Tesouraria.TesourariaSaidaController;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.SasPWFill;
import SasBeans.TesSaidas;
import SasBeans.TesSaidasDN;
import br.com.sasw.lazydatamodels.TesSaidaLazyList;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.sasbeans.TesSaidasMD;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.Funcionarios;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.utils.Mascaras.Data;
import static br.com.sasw.utils.Mascaras.Moeda;
import static br.com.sasw.utils.Mascaras.removeMascaraData;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "tesSaida")
@ViewScoped
public class TesSaidaMB implements Serializable {

    private Persistencia persistencia;
    private String caminho, banco, codPessoa, codFil, operador, dataTela, dataAnterior, dataPosterior, codCli1, data1, data2, tipoMov, htmlEdit, dadosComposicaoDN, dadosComposicaoMD, log, tableGuiasProcessadas;
    private final ArquivoLog logerro;
    private final Calendar calendar;
    private Date dataSelecionada1, dataInicio, dataFim;
    private List<Date> datasSelecionadas;
    private List<Clientes> listaTesouraria;
    private Filiais filialTela;
    private List<SasPWFill> filiais;
    private TesSaidas guiaSelecionada, lacreSelecionado;
    private final RotasSatWeb rotassatweb;
    private Map filters = new HashMap();
    private LazyDataModel<TesSaidas> lazyTesSaidas;
    private List<TesSaidas> listaLacres, guiasParaProcessar;
    private TesourariaSaidaController tesDao;
    private boolean pesquisaEfetuada;
    private List<Funcionarios> funcionarios;
    private int quantidadeGuias;

    public TesSaidaMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        LoginMB login = fc.getApplication().evaluateExpressionGet(fc, "#{login}", LoginMB.class);
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = ((BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa")).toBigInteger().toString();
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa + ".txt";
        logerro = new ArquivoLog();
        dataTela = DataAtual.getDataAtual("SQL");
        calendar = Calendar.getInstance();
        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(calendar.getTime()); // data inicial
        datasSelecionadas.add(calendar.getTime()); // data final
        datasSelecionadas.get(0).setTime(calendar.getTimeInMillis()); // alterando data inicial

        data2 = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data1 = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        dataInicio = datasSelecionadas.get(0);
        dataFim = datasSelecionadas.get(1);
        dataSelecionada1 = datasSelecionadas.get(0);
        dataInicio = calendar.getTime();
        dataFim = calendar.getTime();
        rotassatweb = new RotasSatWeb();
        dataAnterior = "";
        dataPosterior = "";
        codCli1 = "";
        tipoMov = "0";
        pesquisaEfetuada = false;
        funcionarios = new ArrayList<>();
        listaLacres = new ArrayList<>();
        htmlEdit = "<i class=\"fa fa-pencil-square-o\" aria-hidden=\"true\"></i>";
        lacreSelecionado = new TesSaidas();
        guiasParaProcessar = new ArrayList<>();
        quantidadeGuias = 0;

        try {
            persistencia = login.getPp();
            if (persistencia == null) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }

            filialTela = rotassatweb.buscaInfoFilial(codFil, persistencia);
            filiais = login.getFiliais();
            tesDao = new TesourariaSaidaController(persistencia);
            getTesouraia();
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                displayFatal(ex.getMessage());
            }
        }

        setFilters();
    }

    private LazyDataModel<TesSaidas> getTesouraia() {
        try {
            setFilters();

            this.lazyTesSaidas = new TesSaidaLazyList(this.persistencia, this.filters);
            consultarQtdeGuias();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.lazyTesSaidas;
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            limpaEfetuada();
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                getLazyTesSaidas();

                //PrimeFaces.current().ajax().update("main", "cabecalho", "totais");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarListaTesouraria() throws Exception {
        limpaEfetuada();
        listaTesouraria = tesDao.listaTesouraria(this.codFil);
    }

    public void carregarGrideGuias() {
        pesquisaEfetuada = true;
        getTesouraia();
    }

    public void limpaEfetuada() {
        pesquisaEfetuada = false;
    }

    public void salvarListaLacres() throws Exception {
        try {
            for (TesSaidas listaLacre : this.listaLacres) {
                listaLacre.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                listaLacre.setDt_Alter(getDataAtual("SQL"));
                listaLacre.setHr_Alter(getDataAtual("HORA"));
            }

            tesDao.gravarLacres(this.listaLacres, this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), this.guiaSelecionada.getMatrConf());
            consultarQtdeGuias();
            
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("DadosSalvosSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void consultarQtdeGuias() throws Exception {
        try {
            this.guiasParaProcessar = tesDao.quantidadeGuiasCxForte(this.codFil, this.codCli1.split(";")[0], this.data1, this.data2);

            this.tableGuiasProcessadas = "";

            for (TesSaidas guiasParaProcessar1 : this.guiasParaProcessar) {
                this.tableGuiasProcessadas += "<tr>";
                this.tableGuiasProcessadas += "  <td>" + guiasParaProcessar1.getCodCli1() + "</td>";
                this.tableGuiasProcessadas += "  <td>" + Data(guiasParaProcessar1.getData().split(" ")[0].replace("-", "")) + "</td>";
                this.tableGuiasProcessadas += "  <td>" + guiasParaProcessar1.getGuia().replace(".0", "") + "</td>";
                this.tableGuiasProcessadas += "  <td>" + guiasParaProcessar1.getSerie() + "</td>";
                this.tableGuiasProcessadas += "</tr>";
            }

            this.quantidadeGuias = this.guiasParaProcessar.size();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void processarGuias() throws Exception {
        try {
            if (this.quantidadeGuias > 0) {
                tesDao.processarGuias(this.codFil, this.data1, this.data2, RecortaAteEspaço(this.operador, 0, 10), getDataAtual("SQL"), getDataAtual("HORA"));
                PrimeFaces.current().executeScript("MostrarListaProcessadas('" + this.tableGuiasProcessadas + "');");
                
                consultarQtdeGuias();
                carregarGrideGuias();

                /*FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ProcessamentoEfetuado"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);*/
            } else {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("NaoExistemGuiasProcessar"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void preEdicao(ActionEvent actionEvent) throws Exception {
        if (null == guiaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneGuia"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            funcionarios = tesDao.listaFuncionarios(this.codFil);
            this.listaLacres = tesDao.listaLacres(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie());

            // Montar Composição Cédulas 
            List<TesSaidasDN> listaCedulas = tesDao.listaComposicaoDN(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie());
            StringBuilder strDN = new StringBuilder();

            for (TesSaidasDN listaCedula : listaCedulas) {
                if (!strDN.toString().equals("")) {
                    strDN.append(" | ");
                }

                try {
                    strDN.append(listaCedula.getQtde().replace(".0", ""));
                    strDN.append(" x ");
                    strDN.append(Moeda(listaCedula.getValor()));
                } catch (Exception ex) {

                }
            }

            if (!strDN.toString().equals("")) {
                this.dadosComposicaoDN = strDN.toString();
            } else {
                this.dadosComposicaoDN = getMessageS("NaoHaDados");
            }

            // Montar Composição Moedas
            List<TesSaidasMD> listaMoedas = tesDao.listaComposicaoMD(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie());
            StringBuilder strMD = new StringBuilder();

            for (TesSaidasMD listaMoeda : listaMoedas) {
                if (!strMD.toString().equals("")) {
                    strMD.append(" | ");
                }

                try {
                    strMD.append(listaMoeda.getQtde().replace(".0", ""));
                    strMD.append(" x ");
                    strMD.append(Moeda(listaMoeda.getValor()));
                } catch (Exception ex) {

                }
            }

            if (!strMD.toString().equals("")) {
                this.dadosComposicaoMD = strMD.toString();
            } else {
                this.dadosComposicaoMD = getMessageS("NaoHaDados");
            }

            try {
                PrimeFaces.current().resetInputs("formCadastrar");
            } catch (Exception e) {
            }
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        }
    }

    public void dataAnterior() {
        try {
            limpaEfetuada();
            this.calendar.setTime(this.datasSelecionadas.get(0));
            this.calendar.add(Calendar.DAY_OF_YEAR, -1);
            this.datasSelecionadas.get(0).setTime(this.calendar.getTimeInMillis());
            this.data1 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.calendar.setTime(this.datasSelecionadas.get(1));
            this.calendar.add(Calendar.DAY_OF_YEAR, -1);
            this.datasSelecionadas.get(1).setTime(this.calendar.getTimeInMillis());
            this.data2 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.dataInicio = this.datasSelecionadas.get(0);
            this.dataFim = this.datasSelecionadas.get(1);

            getLazyTesSaidas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void dataPosterior() {
        try {
            limpaEfetuada();
            this.calendar.setTime(this.datasSelecionadas.get(0));
            this.calendar.add(Calendar.DAY_OF_YEAR, 1);
            this.data1 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.calendar.setTime(this.datasSelecionadas.get(1));
            this.calendar.add(Calendar.DAY_OF_YEAR, 1);
            this.data2 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.dataInicio = this.datasSelecionadas.get(0);
            this.dataFim = this.datasSelecionadas.get(1);

            getLazyTesSaidas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void dblSelectLacre(SelectEvent event) throws Exception {
        editarLacre();
    }

    public void dblSelect(SelectEvent event) throws Exception {
        preEdicao(null);
    }

    public void novoLacre() {
        this.lacreSelecionado = new TesSaidas();
    }

    public void editarLacre() {
        if (null == this.lacreSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            PrimeFaces.current().executeScript("PF('dlgLacre').show();");
            PrimeFaces.current().ajax().update("formLacre");
        }
    }

    public void salvarLacre() {
        for (int I = 0; I < this.listaLacres.size(); I++) {
            if (this.listaLacres.get(I).getLacre().equals(this.lacreSelecionado.getLacre())) {
                this.listaLacres.remove(I);
                break;
            }
        }

        TesSaidas novoLacre = new TesSaidas();
        novoLacre.setLacre(this.lacreSelecionado.getLacre());
        this.listaLacres.add(novoLacre);
        PrimeFaces.current().executeScript("PF('dlgLacre').hide();");
        PrimeFaces.current().ajax().update("cadastrar");
        this.lacreSelecionado = new TesSaidas();
    }

    public void excluirLacre() {
        if (null == this.lacreSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            for (int I = 0; I < this.listaLacres.size(); I++) {
                if (this.listaLacres.get(I).getLacre().equals(this.lacreSelecionado.getLacre())) {
                    this.listaLacres.remove(I);
                    break;
                }
            }

            PrimeFaces.current().ajax().update("cadastrar");
        }
    }

    private void displayInfo(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayWarn(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayError(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayFatal(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_FATAL, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void logaErro(final Exception e, final String methodName) {
        displayError(e.getMessage());
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    private void setFilters() {
        String array[] = this.codCli1.split(";");

        filters = new HashMap();
        filters.put("CodFil", this.codFil);
        filters.put("CodCli1", array[0]);
        filters.put("DataInicio", this.data1);
        filters.put("DataFim", this.data2);
        filters.put("TipoMov", this.tipoMov);
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public String getDataTela() {
        return dataTela;
    }

    public Filiais getFilialTela() {
        return filialTela;
    }

    public void setFilialTela(Filiais filialTela) {
        this.filialTela = filialTela;
    }

    public List<SasPWFill> getFiliais() {
        return filiais;
    }

    public void setFiliais(List<SasPWFill> filiais) {
        this.filiais = filiais;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public LazyDataModel<TesSaidas> getLazyTesSaidas() {
        return lazyTesSaidas;
    }

    public void setLazyTesSaidas(LazyDataModel<TesSaidas> lazyTesSaidas) {
        this.lazyTesSaidas = lazyTesSaidas;
    }

    public String getDataPosterior() {
        return dataPosterior;
    }

    public void setDataPosterior(String dataPosterior) {
        this.dataPosterior = dataPosterior;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getCodCli1() {
        return codCli1;
    }

    public void setCodCli1(String codCli1) {
        this.codCli1 = codCli1;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public String getTipoMov() {
        return tipoMov;
    }

    public void setTipoMov(String tipoMov) {
        this.tipoMov = tipoMov;
    }

    public List<Clientes> getListaTesouraria() {
        return listaTesouraria;
    }

    public void setListaTesouraria(List<Clientes> listaTesouraria) {
        this.listaTesouraria = listaTesouraria;
    }

    public boolean isPesquisaEfetuada() {
        return pesquisaEfetuada;
    }

    public void setPesquisaEfetuada(boolean pesquisaEfetuada) {
        this.pesquisaEfetuada = pesquisaEfetuada;
    }

    public TesSaidas getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(TesSaidas guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public List<Funcionarios> getFuncionarios() {
        return funcionarios;
    }

    public void setFuncionarios(List<Funcionarios> funcionarios) {
        this.funcionarios = funcionarios;
    }

    public List<TesSaidas> getListaLacres() {
        return listaLacres;
    }

    public void setListaLacres(List<TesSaidas> listaLacres) {
        this.listaLacres = listaLacres;
    }

    public TesSaidas getLacreSelecionado() {
        return lacreSelecionado;
    }

    public void setLacreSelecionado(TesSaidas lacreSelecionado) {
        this.lacreSelecionado = lacreSelecionado;
    }

    public String getHtmlEdit() {
        return htmlEdit;
    }

    public void setHtmlEdit(String htmlEdit) {
        this.htmlEdit = htmlEdit;
    }

    public String getDadosComposicaoDN() {
        return dadosComposicaoDN;
    }

    public void setDadosComposicaoDN(String dadosComposicaoDN) {
        this.dadosComposicaoDN = dadosComposicaoDN;
    }

    public String getDadosComposicaoMD() {
        return dadosComposicaoMD;
    }

    public void setDadosComposicaoMD(String dadosComposicaoMD) {
        this.dadosComposicaoMD = dadosComposicaoMD;
    }

    public int getQuantidadeGuias() {
        return quantidadeGuias;
    }

    public void setQuantidadeGuias(int quantidadeGuias) {
        this.quantidadeGuias = quantidadeGuias;
    }

    public String getTableGuiasProcessadas() {
        return tableGuiasProcessadas;
    }

    public void setTableGuiasProcessadas(String tableGuiasProcessadas) {
        this.tableGuiasProcessadas = tableGuiasProcessadas;
    }
}

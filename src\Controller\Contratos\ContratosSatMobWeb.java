/*
 */
package Controller.Contratos;

import Dados.Persistencia;
import SasBeans.Bancos;
import SasBeans.Clientes;
import SasBeans.ContrVig;
import SasBeans.Contratos;
import SasBeans.ContratosDoctos;
import SasBeans.CtrItens;
import SasBeans.ReajustesGrp;
import SasBeans.SasPWFill;
import SasBeans.TbVal;
import SasDaos.BancosDao;
import SasDaos.ClientesDao;
import SasDaos.ContrVigDao;
import SasDaos.ContratosDao;
import SasDaos.ContratosDoctosDao;
import SasDaos.CtrItensDao;
import SasDaos.ReajustesGrpDao;
import SasDaos.SasPwFilDao;
import SasDaos.TbValDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ContratosSatMobWeb {

    public List<Bancos> obterListaIdentificacaoCliente(Persistencia persistencia) throws Exception {
        try {
            BancosDao bancosDao = new BancosDao();
            return bancosDao.obterListaBancos(persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscaidentificacaocliente<message>" + e.getMessage());
        }
    }

    public List<ReajustesGrp> obterListaReajustesGrp(Persistencia persistencia) throws Exception {
        try {
            ReajustesGrpDao reajustesGrpDao = new ReajustesGrpDao();
            return reajustesGrpDao.obterListaReajustesGrp(persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscagruposreajuste<message>" + e.getMessage());
        }
    }

    public List<String> obterDescricoes(Persistencia persistencia) throws Exception {
        try {
            TbValDao tbValDao = new TbValDao();
            return tbValDao.retornaListaString(42, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscadescricoes<message>" + e.getMessage());
        }
    }

    public List<TbVal> obterGruposPagamento(Persistencia persistencia) throws Exception {
        try {
            TbValDao tbValDao = new TbValDao();
            return tbValDao.retornaLista(46, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscagrupospagamento<message>" + e.getMessage());
        }
    }

    public void cadastrarContrato(Contratos contrato, Persistencia persistencia) throws Exception {
        try {
            ContratosDao contratosDao = new ContratosDao();
            contratosDao.inserirRegistros(contrato, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhaedicao<message>" + e.getMessage());
        }
    }

    public void editarContrato(Contratos contrato, Persistencia persistencia) throws Exception {
        try {
            ContratosDao contratosDao = new ContratosDao();
            contratosDao.editarContrato(contrato, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhaedicao<message>" + e.getMessage());
        }
    }

    public int obterSequencial(String banco, String tipoContrato, Persistencia persistencia) throws Exception {
        ContratosDao contratosDao = new ContratosDao();
        return contratosDao.obterProximoSequencial(banco, tipoContrato, persistencia);
    }

    /**
     * Busca os dados de uma filial
     *
     * @param codFil - Código da Filial
     * @param codPessoa
     * @param persistencia - conexão ao banco de dados
     * @return filial
     * @throws Exception
     */
    public SasPWFill buscaFilial(String codFil, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(codFil, new BigDecimal(codPessoa), persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscafilial<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarClientes(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.buscarClientes(query, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscafilial<message>" + e.getMessage());
        }
    }

    public Clientes obterCliente(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.getClientesMobile(codFil, codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscacliente<message>" + e.getMessage());
        }
    }

    public List<ContratosDoctos> listarDocumentosContrato(String contrato, String codFil, Persistencia persistencia) throws Exception {
        try {
            ContratosDoctosDao contratosDoctosDao = new ContratosDoctosDao();
            return contratosDoctosDao.listarDocumentos(contrato, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscacliente<message>" + e.getMessage());
        }
    }

    public ContratosDoctos inserirDocumento(ContratosDoctos contratosDoctos, Persistencia persistencia) throws Exception {
        try {
            ContratosDoctosDao contratosDoctosDao = new ContratosDoctosDao();
            int ordem, i = 0;

            while (true) {
                try {
                    i++;
                    ordem = contratosDoctosDao.maxOrdem(contratosDoctos.getContrato(), contratosDoctos.getCodFil(), contratosDoctos.getDtArquivo(), persistencia);
                    contratosDoctos.setOrdem(ordem);
                    contratosDoctosDao.inserirDocumento(contratosDoctos, persistencia);
                    break;
                } catch (Exception error) {
                    if (i == 20) {
                        throw new Exception(error);
                    }
                }
            }
            return contratosDoctos;
        } catch (Exception e) {
            throw new Exception("contratos.falhageral<message>" + e.getMessage());
        }
    }

    public void deletarDocumento(ContratosDoctos contratosDoctos, Persistencia persistencia) throws Exception {
        try {
            ContratosDoctosDao contratosDoctosDao = new ContratosDoctosDao();
            contratosDoctosDao.excluirDocumento(contratosDoctos, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Contagem do cadastro de contratos
     *
     * @param filtros - filtros para pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            ContratosDao contratosdao = new ContratosDao();
            return contratosdao.totalContratos(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de contratos
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Contratos> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {

        try {
            ContratosDao contratosdao = new ContratosDao();
            return contratosdao.listaPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhageral<message>" + e.getMessage());
        }
    }

    public void editarContratoAnexo(ContrVig contrVig, Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrVigDao = new ContrVigDao();
            contrVigDao.editarContrato(contrVig, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.editarContratoAnexo<message>" + e.getMessage());
        }
    }

    public void editarItemContrato(CtrItens ctrItens, Persistencia persistencia) throws Exception {
        try {
            CtrItensDao ctrItensDao = new CtrItensDao();
            ctrItensDao.editarCtrItens(ctrItens, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.editarItemContrato<message>" + e.getMessage());
        }
    }

    public void cadastrarItemContrato(ContrVig contrVig, CtrItens ctrItens, Persistencia persistencia) throws Exception {
        try {
            CtrItensDao ctrItensDao = new CtrItensDao();
            ctrItens.setContrato(contrVig.getContrato());
            ctrItens.setCodFil(contrVig.getCodFil().toString());
            ctrItensDao.gravarCtrItens(ctrItens, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.cadastrarItemContrato<message>" + e.getMessage());
        }
    }

//    /**
//     * Cadastrar um novo contrato
//     * @param contrato
//     * @param persistencia
//     * @throws Exception
//     */
//    public void cadastrar(Contratos contrato, Persistencia persistencia) throws Exception{
//        try{
//            ContratosDao contratosdao = new ContratosDao();
//            contratosdao.cadastrar(contrato, persistencia);
//        } catch(Exception e){
//            throw new Exception("contratos.falhageral<message>" + e.getMessage());
//        }
//    }
//
//    /**
//     * Atualiza dados de um contrato
//     * @param contrato
//     * @param persistencia
//     * @throws Exception
//     */
//    public void editar(Contratos contrato, Persistencia persistencia) throws Exception{
//        try{
//            ContratosDao contratosdao = new ContratosDao();
//            contratosdao.atualizar(contrato, persistencia);
//        } catch(Exception e){
//            throw new Exception("contratos.falhageral<message>" + e.getMessage());
//        }
//    }
}

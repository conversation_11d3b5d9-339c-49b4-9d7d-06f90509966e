/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.OLD.Persistencia_OLD;
import Dados.Persistencia;
import SasBeans.TesCofresMov;
import br.com.sasw.pacotesuteis.sasbeans.CataEquip;
import br.com.sasw.pacotesuteis.sasbeans.CataMov;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.formatarDataSQL;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CataMovDao {

    /**
     * Lista transações pendentes de sincronização com o Satellite.
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesCofresMov> listaTransacoesPendentes(Persistencia persistencia) throws Exception {
        try {
            List<TesCofresMov> retorno = new ArrayList<>();
            String sql = "Select top 300 Clientes.CodCofre, CataMov.Data, CataMov.Hora, Clientes.Codigo CodCliente, 9999 idUsuario,\n"
                    + "    'CATACASH' NomeUsuario, Valor ValorDeposito, 'BRL' TipoMoeda,\n"
                    + "    Case when CataMov.Operacao = '7' then 'DINHEIRO'\n"
                    + "    when CataMov.Operacao = '13' then 'COLETA'\n"
                    + "    else 'NIDENT' end TipoDeposito, \n"
                    + "    Convert(Varchar,Clientes.CodCofre)+Convert(Varchar,CataMov.Data,112)+Substring(Replace(CataMov.Hora,':',''),1,6) CodigoBarras,\n"
                    + "    'NORMAL' Status, 'Ger_SatMob' Operador,\n"
                    + "    CataMov.Dt_incl Dt_Incl, Substring(CataMov.Hr_incl,1,5) Hr_Incl\n"
                    + " from CataMov\n"
                    + "left join CataEquip on CataEquip.IDEquip = CataMov.IdEquip \n"
                    + "left join Clientes on Clientes.CodPtoCli+Cast(Clientes.CodCofre as Char) = CataEquip.Serial \n"
                    + "left join TesCofresMov on TesCofresMov.CodCofre = Clientes.CodCofre \n"
                    + "                       and TesCofresMov.Data = CataMov.Data \n"
                    + "                       and TesCofresMov.Hora = CataMov.Hora \n"
                    + "where TesCofresMov.id is null      \n"
                    + "  and Clientes.CodCofre is not null\n"
                    + "order by CataMov.data, CataMov.hora ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            TesCofresMov tesCofresMov;
            while (consulta.Proximo()) {
                tesCofresMov = new TesCofresMov();
                tesCofresMov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tesCofresMov.setData(consulta.getLocalDate("Data"));
                tesCofresMov.setHora(consulta.getString("Hora"));
                tesCofresMov.setCodCliente(consulta.getString("CodCliente"));
                tesCofresMov.setIdUsuario(consulta.getString("idUsuario"));
                tesCofresMov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tesCofresMov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tesCofresMov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tesCofresMov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tesCofresMov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tesCofresMov.setStatus(consulta.getString("Status"));
                tesCofresMov.setOperador(consulta.getString("Operador"));
                tesCofresMov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tesCofresMov.setHr_Incl(consulta.getString("Hr_Incl"));

                retorno.add(tesCofresMov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.listaTransacoesPendentes - " + e.getMessage() + "\r\n"
                    + "Select top 300 Clientes.CodCofre, CataMov.Data, CataMov.Hora, Clientes.Codigo CodCliente, 9999 idUsuario,\n"
                    + "    'CATACASH' NomeUsuario, Valor ValorDeposito, 'BRL' TipoMoeda,\n"
                    + "    Case when CataMov.Operacao = ' 7' then 'DINHEIRO'\n"
                    + "    when CataMov.Operacao = '13' then 'COLETA  '\n"
                    + "    else 'NIDENT' end TipoDeposito, '' CodigoBarras,\n"
                    + "    'NORMAL' Status, 'Ger_SatMob' Operador,\n"
                    + "    CataMov.Data Dt_Incl, Substring(CataMov.Hora,1,5) Hr_Incl\n"
                    + " from CataMov\n"
                    + "left join CataEquip on CataEquip.IDEquip = CataMov.IdEquip \n"
                    + "left join Clientes on Clientes.CodPtoCli+Cast(Clientes.CodCofre as Char) = CataEquip.Serial \n"
                    + "left join TesCofresMov on TesCofresMov.CodCofre = Clientes.CodCofre \n"
                    + "                       and TesCofresMov.Data = CataMov.Data \n"
                    + "                       and TesCofresMov.Hora = CataMov.Hora \n"
                    + "where TesCofresMov.id is null      \n"
                    + "  and Clientes.CodCofre is not null\n"
                    + "order by CataMov.data, CataMov.hora ;");
        }
    }

    /**
     * Lista transações pendentes de sincronização com o Satellite.
     *
     * @param idEquip
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesCofresMov> listaTransacoesPendentes(String idEquip, Persistencia persistencia) throws Exception {
        try {
            List<TesCofresMov> retorno = new ArrayList<>();
            String sql = "Select top 300 Clientes.CodCofre, CataMov.Data, CataMov.Hora, Clientes.Codigo CodCliente, 9999 idUsuario,\n"
                    + "    'CATACASH' NomeUsuario, Valor ValorDeposito, 'BRL' TipoMoeda,\n"
                    + "    Case when CataMov.Operacao = '7' then 'DINHEIRO'\n"
                    + "    when CataMov.Operacao = '13' then 'COLETA'\n"
                    + "    else 'NIDENT' end TipoDeposito, \n"
                    + "    Convert(Varchar,Clientes.CodCofre)+Convert(Varchar,CataMov.Data,112)+Substring(Replace(CataMov.Hora,':',''),1,6) CodigoBarras,\n"
                    + "    'NORMAL' Status, 'Ger_SatMob' Operador,\n"
                    + "    CataMov.Dt_incl Dt_Incl, Substring(CataMov.Hr_incl,1,5) Hr_Incl\n"
                    + " from CataMov (NoLock) \n"
                    + "left join CataEquip (NoLock) on CataEquip.IDEquip = CataMov.IdEquip \n"
                    + "left join Clientes (NoLock) on Clientes.CodPtoCli+Cast(Clientes.CodCofre as Char) = CataEquip.Serial \n"
                    + "left join TesCofresMov (NoLock) on TesCofresMov.CodCofre = Clientes.CodCofre \n"
                    + "                       and TesCofresMov.Data = CataMov.Data \n"
                    + "                       and TesCofresMov.Hora = CataMov.Hora \n"
                    + "where TesCofresMov.id is null      \n"
                    + "  and CataMov.IDEquip = ? \n"
                    + "  and Clientes.CodCofre is not null\n"
                    + "order by CataMov.data, CataMov.hora ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idEquip);
            consulta.select();
            TesCofresMov tesCofresMov;
            while (consulta.Proximo()) {
                tesCofresMov = new TesCofresMov();
                tesCofresMov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tesCofresMov.setData(consulta.getLocalDate("Data"));
                tesCofresMov.setHora(consulta.getString("Hora"));
                tesCofresMov.setCodCliente(consulta.getString("CodCliente"));
                tesCofresMov.setIdUsuario(consulta.getString("idUsuario"));
                tesCofresMov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tesCofresMov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tesCofresMov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tesCofresMov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tesCofresMov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tesCofresMov.setStatus(consulta.getString("Status"));
                tesCofresMov.setOperador(consulta.getString("Operador"));
                tesCofresMov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tesCofresMov.setHr_Incl(consulta.getString("Hr_Incl"));

                retorno.add(tesCofresMov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.listaTransacoesPendentes - " + e.getMessage() + "\r\n"
                    + "Select top 300 Clientes.CodCofre, CataMov.Data, CataMov.Hora, Clientes.Codigo CodCliente, 9999 idUsuario,\n"
                    + "    'CATACASH' NomeUsuario, Valor ValorDeposito, 'BRL' TipoMoeda,\n"
                    + "    Case when CataMov.Operacao = ' 7' then 'DINHEIRO'\n"
                    + "    when CataMov.Operacao = '13' then 'COLETA  '\n"
                    + "    else 'NIDENT' end TipoDeposito, '' CodigoBarras,\n"
                    + "    'NORMAL' Status, 'Ger_SatMob' Operador,\n"
                    + "    CataMov.Data Dt_Incl, Substring(CataMov.Hora,1,5) Hr_Incl\n"
                    + " from CataMov\n"
                    + "left join CataEquip on CataEquip.IDEquip = CataMov.IdEquip \n"
                    + "left join Clientes on Clientes.CodPtoCli+Cast(Clientes.CodCofre as Char) = CataEquip.Serial \n"
                    + "left join TesCofresMov on TesCofresMov.CodCofre = Clientes.CodCofre \n"
                    + "                       and TesCofresMov.Data = CataMov.Data \n"
                    + "                       and TesCofresMov.Hora = CataMov.Hora \n"
                    + "where TesCofresMov.id is null      \n"
                    + "  and Clientes.CodCofre is not null\n"
                    + "order by CataMov.data, CataMov.hora ;");
        }
    }

    /**
     * Lista transações pendentes de sincronização com o Satellite.
     *
     * @param idEquip
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesCofresMov> listaTransacoesPendentesSerial(String serial, Persistencia persistencia) throws Exception {
        try {
            List<TesCofresMov> retorno = new ArrayList<>();
            String sql = "Select top 300 Clientes.CodCofre, CataMov.Data, CataMov.Hora, Clientes.Codigo CodCliente, 9999 idUsuario,\n"
                    + "    'CATACASH' NomeUsuario, Valor ValorDeposito, 'BRL' TipoMoeda,\n"
                    + "    Case when CataMov.Operacao = '7' then 'DINHEIRO'\n"
                    + "    when CataMov.Operacao = '13' then 'COLETA'\n"
                    + "    else 'NIDENT' end TipoDeposito, \n"
                    + "    Convert(Varchar,Clientes.CodCofre)+Convert(Varchar,CataMov.Data,112)+Substring(Replace(CataMov.Hora,':',''),1,6) CodigoBarras,\n"
                    + "    'NORMAL' Status, 'Ger_SatMob' Operador,\n"
                    + "    CataMov.Dt_incl Dt_Incl, Substring(CataMov.Hr_incl,1,5) Hr_Incl\n"
                    + " from CataMov (NoLock) \n"
                    + "left join CataEquip (NoLock) on CataEquip.IDEquip = CataMov.IdEquip \n"
                    + "left join Clientes (NoLock) on Clientes.CodPtoCli+Cast(Clientes.CodCofre as Char) = CataEquip.Serial \n"
                    + "left join TesCofresMov (NoLock) on TesCofresMov.CodCofre = Clientes.CodCofre \n"
                    + "                       and TesCofresMov.Data = CataMov.Data \n"
                    + "                       and TesCofresMov.Hora = CataMov.Hora \n"
                    + "where TesCofresMov.id is null      \n"
                    + "  and CataEquip.Serial = ? \n"
                    + "  and Clientes.CodCofre is not null\n"
                    + "order by CataMov.data, CataMov.hora ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(serial);
            consulta.select();
            TesCofresMov tesCofresMov;
            while (consulta.Proximo()) {
                tesCofresMov = new TesCofresMov();
                tesCofresMov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tesCofresMov.setData(consulta.getLocalDate("Data"));
                tesCofresMov.setHora(consulta.getString("Hora"));
                tesCofresMov.setCodCliente(consulta.getString("CodCliente"));
                tesCofresMov.setIdUsuario(consulta.getString("idUsuario"));
                tesCofresMov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tesCofresMov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tesCofresMov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tesCofresMov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tesCofresMov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tesCofresMov.setStatus(consulta.getString("Status"));
                tesCofresMov.setOperador(consulta.getString("Operador"));
                tesCofresMov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tesCofresMov.setHr_Incl(consulta.getString("Hr_Incl"));

                retorno.add(tesCofresMov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.listaTransacoesPendentesSerial - " + e.getMessage() + "\r\n"
                    + "Select top 300 Clientes.CodCofre, CataMov.Data, CataMov.Hora, Clientes.Codigo CodCliente, 9999 idUsuario,\n"
                    + "    'CATACASH' NomeUsuario, Valor ValorDeposito, 'BRL' TipoMoeda,\n"
                    + "    Case when CataMov.Operacao = '7' then 'DINHEIRO'\n"
                    + "    when CataMov.Operacao = '13' then 'COLETA'\n"
                    + "    else 'NIDENT' end TipoDeposito, \n"
                    + "    Convert(Varchar,Clientes.CodCofre)+Convert(Varchar,CataMov.Data,112)+Substring(Replace(CataMov.Hora,':',''),1,6) CodigoBarras,\n"
                    + "    'NORMAL' Status, 'Ger_SatMob' Operador,\n"
                    + "    CataMov.Dt_incl Dt_Incl, Substring(CataMov.Hr_incl,1,5) Hr_Incl\n"
                    + " from CataMov (NoLock) \n"
                    + "left join CataEquip (NoLock) on CataEquip.IDEquip = CataMov.IdEquip \n"
                    + "left join Clientes (NoLock) on Clientes.CodPtoCli+Cast(Clientes.CodCofre as Char) = CataEquip.Serial \n"
                    + "left join TesCofresMov (NoLock) on TesCofresMov.CodCofre = Clientes.CodCofre \n"
                    + "                       and TesCofresMov.Data = CataMov.Data \n"
                    + "                       and TesCofresMov.Hora = CataMov.Hora \n"
                    + "where TesCofresMov.id is null      \n"
                    + "  and CataEquip.Serial = " + serial + " \n"
                    + "  and Clientes.CodCofre is not null\n"
                    + "order by CataMov.data, CataMov.hora ;");
        }
    }

    /**
     * Inserir transação no banco
     *
     * @param cataMov
     * @param persistencia
     * @throws Exception
     */
    public void inserirTransacao(CataMov cataMov, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO CataMov (ID, IDEquip, Data, Hora, Operacao, Fluxo, Valor, Dt_incl, Hr_incl, Lido) \n"
                    + " VALUES(?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cataMov.getID());
            consulta.setString(cataMov.getIDEquip());
            consulta.setString(cataMov.getData());
            consulta.setString(cataMov.getHora());
            consulta.setString(cataMov.getOperacao());
            consulta.setString(cataMov.getFluxo());
            consulta.setString(cataMov.getValor());
            consulta.setString(cataMov.getDt_incl());
            consulta.setString(cataMov.getHr_incl());
            consulta.setString(cataMov.getLido());
            consulta.insert();
            consulta.close();
            // Incuir Carlos 18/07/2023 regra Corpvs
            if (persistencia.getEmpresa().contains("CORPVS")) {
                String vSQLEx = "";
                String vSQL = "";
                String vID = "";
                String vIDCPV = ""; 
                Persistencia vPersistencia = null;
                vPersistencia.setEmpresa("SATCORPVSPE");
                vSQL = "Select Max(ID) ID from TesCofresMov"
                        + " where ID < 9000000";
                Consulta qCPVPE;
                Consulta qCPV;
                Consulta qTmpX;
                qCPVPE = new Consulta(vSQL, vPersistencia);
                vID = qCPVPE.getString("ID");
                vSQL = "Select top 1 ID, Data, Hora from TesCofresMov"
                        + " where ID < 9000000"
                        + " order by id Desc";
                qCPVPE = new Consulta(vSQL, vPersistencia);
                while (qCPVPE.Proximo()) {
                    vSQL = "Select top 1 ID, Data, Hora from TesCofresMov "
                            + " where Data = '" + formatarDataSQL(qCPVPE.getString("Data")) + "'"
                            + "   and Hora <= '" + qCPVPE.getString("Hora") + "'"
                            + " order by id";
                    qCPV = new Consulta(vSQL, vPersistencia);
                    vIDCPV = qCPV.getString("ID");
            for (int i = 0; i <= 2; i++) {                        
                /*
                vSQL := 'Select TesCofresMov.* from TesCofresMov '+
                        ' left join Clientes on Clientes.CodCofre = TesCofresMov.CodCofre '+
                        ' where Clientes.CodFil = 4 '+
                        '  and ID > '+FloattoStr(vIDCPV);
               if i = 1 then
                  vSQL := vSQL +'  and Data = '+Chr(39)+DTOS(qCPVPE.FieldByName('Data').AsDateTime)+Chr(39)+ // Nesse caso ve somente a data atual
                                '  and Hora > '+Chr(39)+qCPVPE.FieldByname('Hora').AsString+Chr(39)          // a partir da hora da ultima interface
               else if qCPVPE.FieldByName('Data').AsDateTime < Date then
                  vSQL := vSQL +'  and Data > '+Chr(39)+DTOS(qCPVPE.FieldByName('Data').AsDateTime)+Chr(39)
               else
                  Break; // não é data anterior não faz essa parte
               vSQL := vSQL + '  order by Data, hora ';
               Consulta_Pdr(vSQL, qCPV);
               while not qCPV.Eof do begin
                  LbLeitura.Caption := FormatFloat('000000', ValR(LbLeitura.Caption)+1);
                  LbLeitura.Repaint;
                  if not ((qCPV.FieldByName('Hora').AsString        = '00:00:00') and
                         (qCPV.FieldByName('NomeUsuario').AsString = 'Aut-Satellite')) then begin
                      LbGravacao.Caption := FormatFloat('000000', ValR(LbGravacao.Caption)+1);
                      LbGravacao.Repaint;
                      vCtSQL := vCTSQL + 1;
                      if vSQLEx <> '' then
                         vSQLEx := vSQLEx + ' ; ';
                      vID := vID + 1;

                      vSQLEx := vSQLEx +
                                'Insert into TesCofresMov Values ('+FloattoStr(vID)+','+
                                 FloattoStr(qCPV.FieldByName('CodCofre').AsFloat)+','+
                                 Chr(39)+DTOS(qCPV.FieldByName('Data').AsDateTime)+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('Hora').AsString+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('CodCliente').AsString+Chr(39)+','+
                                 FloattoStr(qCPV.FieldByName('IDUsuario').AsFloat)+','+
                                 Chr(39)+qCPV.FieldByName('NomeUsuario').AsString+Chr(39)+','+
                                 ArredondaStr(qCPV.FieldByName('ValorDeposito').AsFloat,15,2)+', '+
                                 Chr(39)+qCPV.FieldByName('TipoMoeda').AsString+Chr(39)+','+
                                  Chr(39)+qCPV.FieldByName('TipoDeposito').AsString+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('CodigoBarras').AsString+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('Status').AsString+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('Operador').AsString+Chr(39)+','+
                                 Chr(39)+DTOS(qCPV.FieldByName('Dt_incl').AsdateTime)+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('Hr_incl').AsString+Chr(39)+','+
                                 Chr(39)+qCPV.FieldByName('Marca').AsString+Chr(39)+','+
                                 Chr(39)+DTOS(date)+Chr(39)+','+
                                 Chr(39)+HHMMSS(Time)+Chr(39)+')';
                   end;
                  if vCtSQL > 100 then begin
                     SQL_PdrWide(vSQLEx, 'DriverWEB');
                     vCTSQL := 0;
                     vSQLEX := '';
                  end;         
            end;
       end;
                
       if vCtSQL > 1then begin
          vCTSQL := 0;
          SQL_PdrWide(vSQLEx, 'DriverWEB');
       end;     
                */
            }
                }
            }
            //
        } catch (Exception e) {
            throw new Exception("CataMovDao.inserirTransacao - " + e.getMessage() + "\r\n"
                    + " INSERT INTO CataMov (ID, IDEquip, Data, Hora, Operacao, Fluxo, Valor, Dt_incl, Hr_incl, Lido) "
                    + " VALUES(" + cataMov.getID() + "," + cataMov.getIDEquip() + "," + cataMov.getData() + "," + cataMov.getHora() + ","
                    + cataMov.getOperacao() + "," + cataMov.getFluxo() + "," + cataMov.getValor() + "," + cataMov.getDt_incl() + ","
                    + cataMov.getHr_incl() + "," + cataMov.getLido() + ")");
        }
    }

    /**
     * Inserir transação no banco
     *
     * @param cataMov
     * @param serialNumber
     * @param persistencia
     * @throws Exception
     */
    public void inserirTransacao(CataMov cataMov, String serialNumber, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO CataMov (ID, IDEquip, Data, Hora, Operacao, Fluxo, Valor, Dt_incl, Hr_incl, Lido) \n"
                    + " VALUES(?,(Select TOP 1 IDEquip from CataEQUIP where Serial = ?),?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cataMov.getID());
            consulta.setString(serialNumber);
            consulta.setString(cataMov.getData());
            consulta.setString(cataMov.getHora());
            consulta.setString(cataMov.getOperacao());
            consulta.setString(cataMov.getFluxo());
            consulta.setString(cataMov.getValor());
            consulta.setString(cataMov.getDt_incl());
            consulta.setString(cataMov.getHr_incl());
            consulta.setString(cataMov.getLido());
            consulta.insert();
            consulta.close();
            //Incluir Parametro Corpvs - 

        } catch (Exception e) {
            throw new Exception("CataMovDao.inserirTransacao - " + e.getMessage() + "\r\n"
                    + " INSERT INTO CataMov (ID, IDEquip, Data, Hora, Operacao, Fluxo, Valor, Dt_incl, Hr_incl, Lido) "
                    + " VALUES(" + cataMov.getID() + "," + cataMov.getIDEquip() + "," + cataMov.getData() + "," + cataMov.getHora() + ","
                    + cataMov.getOperacao() + "," + cataMov.getFluxo() + "," + cataMov.getValor() + "," + cataMov.getDt_incl() + ","
                    + cataMov.getHr_incl() + "," + cataMov.getLido() + ")");
        }
    }

    /**
     * Buscar idequip
     *
     * @param serialNumber
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String buscarIDEquip(String serialNumber, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select TOP 1 IDEquip from CataEQUIP where Serial = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(serialNumber);
            consulta.select();
            String retorno = "";
            if (consulta.Proximo()) {
                retorno = consulta.getString("IDEquip");
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.buscarIDEquip - " + e.getMessage() + "\r\n"
                    + " Select TOP 1 IDEquip from CataEQUIP where Serial = " + serialNumber);
        }
    }

    /**
     * Verifica existência da transação antes de tentar inserir
     *
     * @param id
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeTransacao(String id, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 * FROM CataMov WHERE ID = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(id);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.existeTransacao - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 * FROM CataMov WHERE ID = " + id);
        }
    }

    public List<CataMov> obterCofresAntesUltimaMovimentacao(String data, Persistencia persistencia) throws Exception {
        try {
            List<CataMov> retorno = new ArrayList<>();
            String sql = "SELECT Serial, CONVERT(VarChar,MAX(CataMov.Data), 23) Data,\n"
                    + "(SELECT MAX(Hora) FROM CataMov c WHERE c.Data = MAX(CataMov.Data)) Hora\n"
                    + "FROM CataEquip \n"
                    + "LEFT JOIN CataMov ON CataMov.IDEquip = CataEquip.IDEquip \n"
                    + "GROUP BY Serial \n"
                    + "HAVING MAX(CataMov.Data) < ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.select();
            CataMov cataMov;
            while (consulta.Proximo()) {
                cataMov = new CataMov();
                cataMov.setData(consulta.getString("Data"));
                cataMov.setHora(consulta.getString("Hora"));
                cataMov.setIDEquip(consulta.getString("Serial"));
                retorno.add(cataMov);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.obterCofresAntesUltimaMovimentacao  - " + e.getMessage() + "\r\n"
                    + "SELECT Serial, CONVERT(VarChar,MAX(CataMov.Data), 23) Data,\n"
                    + "(SELECT MAX(Hora) FROM CataMov c WHERE c.Data = MAX(CataMov.Data)) Hora\n"
                    + "FROM CataEquip\n"
                    + "LEFT JOIN CataMov ON CataMov.IDEquip = CataEquip.IDEquip \n"
                    + "GROUP BY Serial\n"
                    + "HAVING MAX(CataMov.Data) < " + data);
        }
    }

    public String obterDataUltimaMovimentacao(Persistencia persistencia) throws Exception {
        try {
            String retorno = null;
            String sql = "SELECT CONVERT(VarChar, ISNULL(MAX(Data),GETDATE()), 23) Data FROM CataMov";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getString("Data");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.obterDataUltimaMovimentacao - " + e.getMessage() + "\r\n"
                    + " SELECT CONVERT(VarChar, ISNULL(MAX(Data),GETDATE()), 23) Data FROM CataMov");
        }
    }

    public String obterDataHoraUltimaMovimentacao(Persistencia persistencia) throws Exception {
        try {
            String retorno = "";
            String sql = "SELECT (CONVERT(VarChar, MAX(Data), 23)+'T'+MIN(Hora)) Data \n"
                    + " FROM (SELECT MAX(Data) Data, MAX(Hora) Hora FROM CataMov GROUP BY IDEquip) a";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getString("Data");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.obterDataHoraUltimaMovimentacao - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 CONCAT(CONVERT(VarChar, Data, 23), 'T', Hora) Data FROM CataMov ORDER BY Data DESC, Hora DESC");
        }
    }

    public CataMov obterDataHoraUltimaMovimentacao(Persistencia persistencia, String idEquip) throws Exception {
        try {
            CataMov retorno = new CataMov();
            String sql = "SELECT \n"
                    + " Case when DateDiff(Hour, Max(Dt_Envio), Getdate()) >= 24 then CONVERT(VarChar, Dateadd(day,-3,MAX(Data)),23) + ' 00:00:00' else  \n"
                    + "isnull((CONVERT(VarChar, MAX(Data), 23)+' '+MIN(Hora)), '2020-01-01 00:00:00') end Data, sum(isnull(qtdCataEquip,0)) qtdCataEquip \n"
                    + "FROM (SELECT Top 01 CataMov.Data, CataMov.Hora Hora, count(CataEquip.Serial) qtdCataEquip, \n"
                    + " cataequip.Dt_Envio "
                    + "FROM CataMov (Nolock)\n"
                    + "Left join CataEquip  on CataMov.IDEquip = CataEquip.IDEquip\n"
                    + " where CataMov.IDEquip = ?\n"
                    + " Group by CataMov.Data, CataMov.Hora, cataequip.Dt_Envio\n"
                    + "Order by Data Desc, Hora Desc) a";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idEquip);
            consulta.select();
            if (consulta.Proximo()) {
                retorno.setData(consulta.getString("Data"));
                retorno.setFluxo(consulta.getString("qtdCataEquip"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.obterDataHoraUltimaMovimentacao - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "isnull((CONVERT(VarChar, MAX(Data), 23)+' '+MIN(Hora)), '2020-01-01 00:00:00') Data, count(isnull(qtdCataEquip,0)) qtdCataEquip \n"
                    + "FROM (SELECT Top 01 CataMov.Data, CataMov.Hora Hora, count(CataEquip.Serial) qtdCataEquip\n"
                    + "FROM CataMov (Nolock)\n"
                    + "Left join CataEquip  on CataMov.IDEquip = CataEquip.IDEquip\n"
                    + " where CataMov.IDEquip = ? \n"
                    + " Group by CataMov.Data, CataMov.Hora\n"
                    + "Order by Data Desc, Hora Desc) a");
        }
    }

    public CataEquip getCodCofreEquip(Persistencia_OLD persistencia) throws Exception {
        try {
            String sql = "Select Establishment, UserName, Password, SerialNumber, NoteQuantityLimit, MachineID, AllocationGEO from settings \n"
                    + "Left join onlinesafeinformations  on onlinesafeinformations.MachineSerial = settings.SerialNumber\n"
                    + "limit 1;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            CataEquip cataEquip = null;
            while (consulta.Proximo()) {
                cataEquip = new CataEquip();
                cataEquip.setSerial(consulta.getString("SerialNumber"));
                cataEquip.setIDEquip(consulta.getString("MachineID"));
                cataEquip.setGeo(consulta.getString("AllocationGEO"));
                cataEquip.setAddress(consulta.getString("Establishment"));
            }
            consulta.Close();
            return cataEquip;
        } catch (Exception e) {
            throw new Exception(" Failed to list - " + e.getMessage());
        }
    }

    public CataEquip getCodCofreEquipv2(Persistencia_OLD persistencia) throws Exception {
        try {
            String sql = "Select Establishment, UserName, Password, SerialNumber, NoteQuantityLimit, SerialNumber as MachineID, '0' as AllocationGEO from settings \n"
                    + "limit 1";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            CataEquip cataEquip = null;
            while (consulta.Proximo()) {
                cataEquip = new CataEquip();
                cataEquip.setSerial(consulta.getString("SerialNumber"));
                cataEquip.setIDEquip(consulta.getString("MachineID"));
                cataEquip.setGeo(consulta.getString("AllocationGEO"));
                cataEquip.setAddress(consulta.getString("Establishment"));
            }
            consulta.Close();
            return cataEquip;
        } catch (Exception e) {
            throw new Exception(" Failed to list - " + e.getMessage());
        }
    }

    public List<CataMov> getRecordsEquip(Persistencia_OLD persistencia, String data, String idEquip) throws Exception {
        try {
            List<CataMov> retorno = new ArrayList<>();
            String sql = "Select 7 Operacao, DepositDate Data, SessionId, UniqueId, TotalValue Valor from deposits where DepositDate >= ? \n"
                    + "union\n"
                    + "Select 13 Operacao, CollectDate Data, SessionId, UniqueId, (TotalValueManual+TotalValueAutomatic) Valor from collects where CollectDate >= ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(data);
            consulta.select();
            CataMov catamov = null;
            while (consulta.Proximo()) {
                catamov = new CataMov();
                catamov.setID(consulta.getString("UniqueId"));
                catamov.setData(consulta.getString("Data"));
                catamov.setOperacao(consulta.getString("Operacao"));
                catamov.setValor(consulta.getString("Valor"));
                catamov.setFluxo("0");
                catamov.setIDEquip(idEquip);
                retorno.add(catamov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list - " + e.getMessage());
        }
    }

    /**
     * Verifica existência tabela de integração
     *
     * @param tabela
     * @param database
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeTabelaIntegracao(String tabela, String database, Persistencia_OLD persistencia) throws Exception {
        try {
            Boolean retorno = false;
            String sql = " SELECT count(*) qtde FROM information_schema.tables \n"
                    + "WHERE table_schema = ? \n"
                    + "AND table_name = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(database);
            consulta.setString(tabela);
            consulta.select();
            if (consulta.Proximo()) {
                if (consulta.getString("qtde").equals("0")) {
                    retorno = false;
                } else {
                    retorno = true;
                }
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.existeTabelaIntegracao - " + e.getMessage());
        }
    }

    public void createTableIntegrations(Persistencia_OLD persistencia) throws Exception {
        try {
            String sql = "CREATE TABLE integrations (\n"
                    + "  Id int(11) NOT NULL AUTO_INCREMENT,\n"
                    + "  Date datetime NOT NULL,\n"
                    + "  Type int(11) NOT NULL,\n"
                    + "  UniqueId char(36) DEFAULT NULL,\n"
                    + "  PRIMARY KEY (Id)\n"
                    + ")";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.insert();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("CataMovDao.createTableIntegrations - " + e.getMessage());
        }
    }

    public List<CataMov> getTransacoesPendentes(Persistencia_OLD persistencia, String idEquip) throws Exception {
        try {
            List<CataMov> retorno = new ArrayList<>();
            String sql = " Select 7 Operacao, DepositDate Data, SessionId, UniqueId, TotalValue Valor \n"
                    + " from deposits \n"
                    + " where DepositDate >= NOW() + Interval - 16 day \n"
                    + "     and Status <> '13' and UniqueId not in \n"
                    + "         (Select UniqueId from integrations where date >= Cast(NOW() + Interval - 16 day as date) and Type = \"7\")\n"
                    + "union\n"
                    + " Select 13 Operacao, CollectDate Data, SessionId, UniqueId, (TotalValueManual+TotalValueAutomatic) Valor \n"
                    + " from collects \n"
                    + " where CollectDate >= NOW() + Interval - 16 day \n"
                    + "     and UniqueId not in \n"
                    + "         (Select UniqueId from integrations where date >= Cast(NOW() + Interval - 16 day as date) and Type = \"13\")\n"
                    + "limit 20;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            CataMov catamov = null;
            while (consulta.Proximo()) {
                catamov = new CataMov();
                catamov.setID(consulta.getString("UniqueId"));
                catamov.setData(consulta.getString("Data"));
                catamov.setOperacao(consulta.getString("Operacao"));
                catamov.setValor(consulta.getString("Valor"));
                catamov.setFluxo("0");
                catamov.setIDEquip(idEquip);
                retorno.add(catamov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CataMovDao.getTransacoesPendentes - " + e.getMessage());
        }
    }

    public void insertIntegrations(Persistencia_OLD persistencia, String date, String type, String uniqueId) throws Exception {
        try {
            String sql = "Insert into integrations (Date, Type, UniqueId) values \n"
                    + " (?, ?, ?);";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(date);
            consulta.setString(type);
            consulta.setString(uniqueId);
            consulta.insert();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("CataMovDao.insertIntegrations - " + e.getMessage());
        }
    }

}

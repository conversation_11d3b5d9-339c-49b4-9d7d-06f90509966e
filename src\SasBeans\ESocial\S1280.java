/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S1280 {

    public S1280() {
        infoSubstPatr_indSubstPatr = "1";
        infoSubstPatr_percRedContrib = "0";
        ideEvento_indGuia = "1";
        ideEvento_procEmi = "1";
        ideEvento_verProc = "Satellite eSocial";
        ideEvento_indRetif = "1";
        infoAtivConcom_fatorMes = "04500";
        infoAtivConcom_fator13 = "04500";
        ideEvento_nrRecibo = "";
    }

    private String sucesso;

    private String evtInfoComplPer_id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_indGuia;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String infoSubstPatr_indSubstPatr;
    private String infoSubstPatr_percRedContrib;
    private String infoSubstPatrOpPort_codLotacao;
    private String infoAtivConcom_fatorMes;
    private String infoAtivConcom_fator13;

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 53 * hash + Objects.hashCode(this.evtInfoComplPer_id);
//        hash = 53 * hash + Objects.hashCode(this.infoPgto_tpPgto);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S1280 other = (S1280) obj;
        if (!Objects.equals(this.evtInfoComplPer_id, other.evtInfoComplPer_id)) {
            return false;
        }
//        if (!Objects.equals(this.infoPgto_tpPgto, other.infoPgto_tpPgto)) {
//            return false;
//        }
        return true;
    }

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtInfoComplPer_id() {
        return evtInfoComplPer_id;
    }

    public void setEvtInfoComplPer_id(String evtInfoComplPer_id) {
        this.evtInfoComplPer_id = evtInfoComplPer_id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_indApuracao() {
        return ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getIdeEvento_indGuia() {
        return ideEvento_indGuia;
    }

    public void setIdeEvento_indGuia(String ideEvento_indGuia) {
        this.ideEvento_indGuia = ideEvento_indGuia;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getInfoSubstPatr_indSubstPatr() {
        return infoSubstPatr_indSubstPatr;
    }

    public void setInfoSubstPatr_indSubstPatr(String infoSubstPatr_indSubstPatr) {
        this.infoSubstPatr_indSubstPatr = infoSubstPatr_indSubstPatr;
    }

    public String getInfoSubstPatr_percRedContrib() {
        return infoSubstPatr_percRedContrib;
    }

    public void setInfoSubstPatr_percRedContrib(String infoSubstPatr_percRedContrib) {
        this.infoSubstPatr_percRedContrib = infoSubstPatr_percRedContrib;
    }

    public String getInfoSubstPatrOpPort_codLotacao() {
        return infoSubstPatrOpPort_codLotacao;
    }

    public void setInfoSubstPatrOpPort_codLotacao(String infoSubstPatrOpPort_codLotacao) {
        this.infoSubstPatrOpPort_codLotacao = infoSubstPatrOpPort_codLotacao;
    }

    public String getInfoAtivConcom_fatorMes() {
        return infoAtivConcom_fatorMes;
    }

    public void setInfoAtivConcom_fatorMes(String infoAtivConcom_fatorMes) {
        this.infoAtivConcom_fatorMes = infoAtivConcom_fatorMes;
    }

    public String getInfoAtivConcom_fator13() {
        return infoAtivConcom_fator13;
    }

    public void setInfoAtivConcom_fator13(String infoAtivConcom_fator13) {
        this.infoAtivConcom_fator13 = infoAtivConcom_fator13;
    }
}

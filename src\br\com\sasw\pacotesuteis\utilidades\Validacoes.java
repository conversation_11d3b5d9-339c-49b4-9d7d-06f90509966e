/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 *
 * <AUTHOR>
 */
public class Validacoes {

    /**
     * Validação de tamanho e de obrigatoriedade.
     *
     * @param texto
     * @param tamanho
     * @param obrigatorio
     * @return
     */
    public static boolean validacaoTamanho(String texto, int tamanho, boolean obrigatorio) {
        boolean retorno = true;
        try {
            if (texto != null
                    && texto.length() > tamanho) {
                retorno = false;
                return false;
            }

            if (obrigatorio
                    && (texto == null || texto.equals(""))) {
                retorno = false;
                return false;
            }
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }

    /**
     * Valida se o texto é uma data. Retorna falso para strings vazias
     *
     * @param data
     * @param formato
     * @return
     */
    public static boolean validacaoData(String data, String formato) {
        boolean retorno = true;
        try {
            if (data == null
                    || data.equals("")) {
                retorno = false;
                return false;
            }

            LocalDate.parse(data, DateTimeFormatter.ofPattern(formato));
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }

    /**
     * Valida se o texto é uma hora. Retorna falso para strings vazias
     *
     * @param hora
     * @param formato
     * @return
     */
    public static boolean validacaoHora(String hora, String formato) {
        boolean retorno = true;
        try {
            if (hora == null
                    || hora.equals("")
                    || hora.length() != formato.length()) {
                retorno = false;
                return false;
            }

            LocalTime.parse(hora, DateTimeFormatter.ofPattern(formato));
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }

    /**
     * Valida se o texto é um número hexadecimal. Retorna falso para strings
     * vazias
     *
     * @param hora
     * @param formato
     * @return
     */
    public static boolean validacaoHexadecimal(String numero) {
        boolean retorno = true;
        try {
            if (numero == null
                    || numero.equals("")) {
                retorno = false;
                return false;
            }

            Integer.parseInt(numero, 16);
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }

    /**
     * Valida se o número é inteiro e menor que tamanho. Retorna falso para
     * strings vazias
     *
     * @param hora
     * @param formato
     * @return
     */
    public static boolean validacaoInteiro(String numero, int tamanho) {
        boolean retorno = true;
        try {
            if (numero == null
                    || numero.equals("")) {
                retorno = false;
                return false;
            }

            if (Integer.parseInt(numero) > tamanho) {
                retorno = false;
                return false;
            }
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }

    /**
     * Valida se o número é menor que tamanho.
     *
     * @param hora
     * @param formato
     * @return
     */
    public static boolean validacaoInteiro(int numero, int tamanho) {
        boolean retorno = true;
        try {
            if (numero > tamanho) {
                retorno = false;
                return false;
            }
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }

    /**
     * Valida se o número é menor que tamanho.
     *
     * @param hora
     * @param formato
     * @return
     */
    public static boolean validacaoValor(BigDecimal numero, String tamanho, int decimal) {
        boolean retorno = true;
        try {
            retorno = numero.scale() <= decimal && numero.compareTo(new BigDecimal(tamanho)) < 1;
        } catch (Exception e) {
            retorno = false;
        } finally {
            return retorno;
        }
    }
}

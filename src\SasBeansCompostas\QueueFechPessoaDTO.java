package SasBeansCompostas;

import SasBeans.Pessoa;
import SasBeans.QueueFech;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class QueueFechPessoaDTO {

    private QueueFech queueFech;
    private Pessoa pessoa;

    public QueueFechPessoaDTO() {
        this.queueFech = new QueueFech();
        this.pessoa = new Pessoa();
    }

    public QueueFechPessoaDTO(QueueFech queueFech, Pessoa pessoa) {
        this.queueFech = queueFech;
        this.pessoa = pessoa;
    }

    public QueueFechPessoaDTO(QueueFechPessoaDTO original) {
        this.queueFech = new QueueFech(original.getQueueFech());
        this.pessoa = new Pessoa(original.getPessoa());
    }

    public QueueFech getQueueFech() {
        return queueFech;
    }

    public void setQueueFech(QueueFech queueFech) {
        this.queueFech = queueFech;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    @Override
    public String toString() {
        return "QueueFechPessoaDTO{" + "queueFech=" + queueFech + ", pessoa=" + pessoa + '}';
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + Objects.hashCode(this.queueFech);
        hash = 71 * hash + Objects.hashCode(this.pessoa);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final QueueFechPessoaDTO other = (QueueFechPessoaDTO) obj;
        if (!Objects.equals(this.queueFech, other.queueFech)) {
            return false;
        }
        if (!Objects.equals(this.pessoa, other.pessoa)) {
            return false;
        }
        return true;
    }

}

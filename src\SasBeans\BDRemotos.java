package SasBeans;

import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class BDRemotos {

    private String BancoDados;
    private String Empresa;
    private String HostName;
    private String BDNome;
    private String FonteODBC;
    private String Usuario;
    private String Senha;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public String getBancoDados() {
        return BancoDados;
    }

    public void setBancoDados(String BancoDados) {
        this.BancoDados = BancoDados;
    }

    public String getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    public String getHostName() {
        return HostName;
    }

    public void setHostName(String HostName) {
        this.HostName = HostName;
    }

    public String getBDNome() {
        return BDNome;
    }

    public void setBDNome(String BDNome) {
        this.BDNome = BDNome;
    }

    public String getFonteODBC() {
        return FonteODBC;
    }

    public void setFonteODBC(String FonteODBC) {
        this.FonteODBC = FonteODBC;
    }

    public String getUsuario() {
        return Usuario;
    }

    public void setUsuario(String Usuario) {
        this.Usuario = Usuario;
    }

    public String getSenha() {
        return Senha;
    }

    public void setSenha(String Senha) {
        this.Senha = Senha;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

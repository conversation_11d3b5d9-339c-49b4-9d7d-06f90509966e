/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.DashBoard;

import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.DashBoardDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DashBoard {

    public List<SasBeansCompostas.DashBoard> obterEstatisticaPorDia(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            DashBoardDao dashDao = new DashBoardDao();
            return dashDao.obterEstatisticaPorDia(persistencia, Ano, Mes, CodFil);
        } catch (Exception e) {
            throw new Exception("DashBoard.obterEstatisticaPorDia<message>" + e.getMessage());
        }
    }

    public List<SasBeansCompostas.DashBoard> obterQdesPorRota(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            DashBoardDao dashDao = new Dash<PERSON>oard<PERSON>ao();
            return dashDao.obterQdesPorRota(persisten<PERSON>, An<PERSON>, <PERSON><PERSON>, CodFil);
        } catch (Exception e) {
            throw new Exception("DashBoard.obterQdesPorRota<message>" + e.getMessage());
        }
    }

    public List<SasBeansCompostas.DashBoard> obterQdeNovoTipoServico(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            DashBoardDao dashDao = new DashBoardDao();
            return dashDao.obterQdeNovoTipoServico(persistencia, Ano, Mes, CodFil);
        } catch (Exception e) {
            throw new Exception("DashBoard.obterQdesPorRota<message>" + e.getMessage());
        }
    }

    public List<SasBeansCompostas.DashBoard> obterQdeNovoRamoAtiv(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            DashBoardDao dashDao = new DashBoardDao();
            return dashDao.obterQdeNovoRamoAtiv(persistencia, Ano, Mes, CodFil);
        } catch (Exception e) {
            throw new Exception("DashBoard.obterQdesPorRota<message>" + e.getMessage());
        }
    }

    public List<SasBeansCompostas.DashBoard> obterQdeNovoClienteTipo(Persistencia persistencia, String Ano, String Mes, String CodFil) throws Exception {
        try {
            DashBoardDao dashDao = new DashBoardDao();
            return dashDao.obterQdeNovoClienteTipo(persistencia, Ano, Mes, CodFil);
        } catch (Exception e) {
            throw new Exception("DashBoard.obterQdesPorRota<message>" + e.getMessage());
        }
    }

}

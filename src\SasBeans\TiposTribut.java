package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TiposTribut {

    private Integer codigo;
    private String descricao;
    private BigDecimal indice;
    private BigDecimal reducBase;
    private Integer classeFiscal;
    private String codInterf;
    private String contaDeb;
    private String contaCred;
    private String ccusto;
    private Integer histPadrao;
    private String CST;
    private BigDecimal aliqPIS;
    private BigDecimal PIS;
    private String PISCST;
    private BigDecimal aliqCOFINS;
    private BigDecimal COFINS;
    private String COFINSCST;
    private BigDecimal aliqCSL;
    private BigDecimal CSL;
    private String CSLCST;
    private String IPICST;
    private String IPIEnq;
    private String operador;
    private String dtAlter;
    private String hrAlter;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public BigDecimal getIndice() {
        return indice;
    }

    public void setIndice(BigDecimal indice) {
        this.indice = indice;
    }

    public BigDecimal getReducBase() {
        return reducBase;
    }

    public void setReducBase(BigDecimal reducBase) {
        this.reducBase = reducBase;
    }

    public Integer getClasseFiscal() {
        return classeFiscal;
    }

    public void setClasseFiscal(Integer classeFiscal) {
        this.classeFiscal = classeFiscal;
    }

    public String getCodInterf() {
        return codInterf;
    }

    public void setCodInterf(String codInterf) {
        this.codInterf = codInterf;
    }

    public String getContaDeb() {
        return contaDeb;
    }

    public void setContaDeb(String contaDeb) {
        this.contaDeb = contaDeb;
    }

    public String getContaCred() {
        return contaCred;
    }

    public void setContaCred(String contaCred) {
        this.contaCred = contaCred;
    }

    public String getCcusto() {
        return ccusto;
    }

    public void setCcusto(String ccusto) {
        this.ccusto = ccusto;
    }

    public Integer getHistPadrao() {
        return histPadrao;
    }

    public void setHistPadrao(Integer histPadrao) {
        this.histPadrao = histPadrao;
    }

    public String getCST() {
        return CST;
    }

    public void setCST(String CST) {
        this.CST = CST;
    }

    public BigDecimal getAliqPIS() {
        return aliqPIS;
    }

    public void setAliqPIS(BigDecimal aliqPIS) {
        this.aliqPIS = aliqPIS;
    }

    public BigDecimal getPIS() {
        return PIS;
    }

    public void setPIS(BigDecimal PIS) {
        this.PIS = PIS;
    }

    public String getPISCST() {
        return PISCST;
    }

    public void setPISCST(String PISCST) {
        this.PISCST = PISCST;
    }

    public BigDecimal getAliqCOFINS() {
        return aliqCOFINS;
    }

    public void setAliqCOFINS(BigDecimal aliqCOFINS) {
        this.aliqCOFINS = aliqCOFINS;
    }

    public BigDecimal getCOFINS() {
        return COFINS;
    }

    public void setCOFINS(BigDecimal COFINS) {
        this.COFINS = COFINS;
    }

    public String getCOFINSCST() {
        return COFINSCST;
    }

    public void setCOFINSCST(String COFINSCST) {
        this.COFINSCST = COFINSCST;
    }

    public BigDecimal getAliqCSL() {
        return aliqCSL;
    }

    public void setAliqCSL(BigDecimal aliqCSL) {
        this.aliqCSL = aliqCSL;
    }

    public BigDecimal getCSL() {
        return CSL;
    }

    public void setCSL(BigDecimal CSL) {
        this.CSL = CSL;
    }

    public String getCSLCST() {
        return CSLCST;
    }

    public void setCSLCST(String CSLCST) {
        this.CSLCST = CSLCST;
    }

    public String getIPICST() {
        return IPICST;
    }

    public void setIPICST(String IPICST) {
        this.IPICST = IPICST;
    }

    public String getIPIEnq() {
        return IPIEnq;
    }

    public void setIPIEnq(String IPIEnq) {
        this.IPIEnq = IPIEnq;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDtAlter() {
        return dtAlter;
    }

    public void setDtAlter(String dtAlter) {
        this.dtAlter = dtAlter;
    }

    public String getHrAlter() {
        return hrAlter;
    }

    public void setHrAlter(String hrAlter) {
        this.hrAlter = hrAlter;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 43 * hash + Objects.hashCode(this.codigo);
        hash = 43 * hash + Objects.hashCode(this.descricao);
        hash = 43 * hash + Objects.hashCode(this.indice);
        hash = 43 * hash + Objects.hashCode(this.reducBase);
        hash = 43 * hash + Objects.hashCode(this.classeFiscal);
        hash = 43 * hash + Objects.hashCode(this.codInterf);
        hash = 43 * hash + Objects.hashCode(this.contaDeb);
        hash = 43 * hash + Objects.hashCode(this.contaCred);
        hash = 43 * hash + Objects.hashCode(this.ccusto);
        hash = 43 * hash + Objects.hashCode(this.histPadrao);
        hash = 43 * hash + Objects.hashCode(this.CST);
        hash = 43 * hash + Objects.hashCode(this.aliqPIS);
        hash = 43 * hash + Objects.hashCode(this.PIS);
        hash = 43 * hash + Objects.hashCode(this.PISCST);
        hash = 43 * hash + Objects.hashCode(this.aliqCOFINS);
        hash = 43 * hash + Objects.hashCode(this.COFINS);
        hash = 43 * hash + Objects.hashCode(this.COFINSCST);
        hash = 43 * hash + Objects.hashCode(this.aliqCSL);
        hash = 43 * hash + Objects.hashCode(this.CSL);
        hash = 43 * hash + Objects.hashCode(this.CSLCST);
        hash = 43 * hash + Objects.hashCode(this.IPICST);
        hash = 43 * hash + Objects.hashCode(this.IPIEnq);
        hash = 43 * hash + Objects.hashCode(this.operador);
        hash = 43 * hash + Objects.hashCode(this.dtAlter);
        hash = 43 * hash + Objects.hashCode(this.hrAlter);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TiposTribut other = (TiposTribut) obj;
        if (!Objects.equals(this.codigo, other.codigo)) {
            return false;
        }
        return true;
    }

}

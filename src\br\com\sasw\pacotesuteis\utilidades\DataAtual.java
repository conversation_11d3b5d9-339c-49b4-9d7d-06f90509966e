package br.com.sasw.pacotesuteis.utilidades;

import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.ResolverStyle;
import java.time.temporal.ChronoField;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

/**
 *
 * <AUTHOR>
 */
public class DataAtual {

    /**
     * Devolve a data hora atual SQL - yyyyMMdd SQL-L - yyyy-MM-dd HORA - HH:mm
     * TELA - dd/MM/yyyy DDMMAAAA - ddMMyyyy HHMMSS - HHmmss
     *
     * @param Param - String que indica o tipo de dados retornado
     * @return
     */
    public static String getDataAtual(String Param) {
        Date data_atual;
        SimpleDateFormat data_formatada = null;
        String retorno = "";
        boolean valido = false;
        data_atual = new Date();
        if (Param.equals("SQL")) {
            data_formatada = new SimpleDateFormat("yyyyMMdd"); //Param=SQL
            valido = true;
        } else if (Param.equals("SQL-L")) {
            data_formatada = new SimpleDateFormat("yyyy-MM-dd"); //Param=SQL
            valido = true;
        } else if (Param.equals("HORA")) {
            data_formatada = new SimpleDateFormat("HH:mm"); //Param=Hora
            valido = true;
        }  else if (Param.equals("HORASEGUNDOS")) {
            data_formatada = new SimpleDateFormat("HH:mm:ss"); //Param=Hora
            valido = true;
        } else if (Param.equals("TELA")) {
            data_formatada = new SimpleDateFormat("dd/MM/yyyy"); //Param=TELA
            valido = true;
        } else if (Param.equals("DDMMAAAA")) {
            data_formatada = new SimpleDateFormat("ddMMyyyy"); //Param=DDMMAAAA
            valido = true;
        } else if (Param.equals("COMPET")) {
            data_formatada = new SimpleDateFormat("yyyy-MM"); //Param=COMPET
            valido = true;
        } else if (Param.equals("HHMMSS")) {
            data_formatada = new SimpleDateFormat("HHmmss"); //Param=HHMMSS
            valido = true;
        } else if (Param.equals("HH")) {
            data_formatada = new SimpleDateFormat("HH"); //Param=HH
            valido = true;
        } else if (Param.equals("mili")) {
            data_formatada = new SimpleDateFormat("ss.S"); //Param=HHMMSS
            valido = true;
        }
        if (valido) {
            retorno = data_formatada.format(data_atual);
        }
        return retorno;
    }

    /**
     * Devolve a data hora atual considerando o fuso horário do usuário SQL -
     * yyyyMMdd SQL-L - yyyy-MM-dd HORA - HH:mm TELA - dd/MM/yyyy DDMMAAAA -
     * ddMMyyyy HHMMSS - HHmmss
     *
     * @param Param
     * @param fusoHorario
     * @return
     */
    public static String getDataAtual(String Param, long fusoHorario) {
        LocalDateTime data_atual = LocalDateTime.now();
        data_atual = data_atual.plusHours(fusoHorario);

        DateTimeFormatter data_formatada = null;
        String retorno = "";
        boolean valido = false;
        switch (Param) {
            case "SQL":
                data_formatada = DateTimeFormatter.ofPattern("yyyyMMdd"); //Param=SQL
                valido = true;
                break;
            case "SQL-L":
                data_formatada = DateTimeFormatter.ofPattern("yyyy-MM-dd"); //Param=SQL
                valido = true;
                break;
            case "HORA":
                data_formatada = DateTimeFormatter.ofPattern("HH:mm"); //Param=Hora
                valido = true;
                break;
            case "TELA":
                data_formatada = DateTimeFormatter.ofPattern("dd/MM/yyyy"); //Param=TELA
                valido = true;
                break;
            case "DDMMAAAA":
                data_formatada = DateTimeFormatter.ofPattern("ddMMyyyy"); //Param=DDMMAAAA
                valido = true;
                break;
            case "HHMMSS":
                data_formatada = DateTimeFormatter.ofPattern("HHmmss"); //Param=HHMMSS
                valido = true;
                break;
            default:
                break;
        }
        if (valido) {
            retorno = data_atual.format(data_formatada);
        }
        return retorno;
    }

    /**
     * Valida data passada no formato de String
     *
     * @param dateStr - String formato - dd/MM/yyyy
     * @return - true para valido e false para invalido
     * @throws Exception - Pode gerar exception
     */
    public static boolean isDate(String dateStr) throws Exception {
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        Calendar cal = new GregorianCalendar();
        df.setLenient(false);
// gerando o calendar  
        cal.setTime(df.parse(dateStr));
// separando os dados da string para comparacao e validacao  
        String[] data = dateStr.split("/");
        String dia = data[0];
        String mes = data[1];
        String ano = data[2];
// testando se hah discrepancia entre a data que foi  
// inserida no caledar e a data que foi passada como  
// string. se houver diferenca, a data passada era  
// invalida  
        if ((new Integer(dia)).intValue() != (new Integer(cal.get(Calendar.DAY_OF_MONTH))).intValue()) {
// dia nao casou  
            return (false);
        } else if ((new Integer(mes)).intValue() != (new Integer(cal.get(Calendar.MONTH) + 1)).intValue()) {
// mes nao casou  
            return (false);
        } else if ((new Integer(ano)).intValue() != (new Integer(cal.get(Calendar.YEAR))).intValue()) {
// ano nao casou  
            return (false);
        }
        return (true);
    }

    /**
     * Inverte data para usar com sql
     *
     * @param value - String nos formatos ddMMyyyy ou dd/MM/yyyy
     * @return - String no formato yyyy-MM-dd
     * @throws Exception - gera erro em caso de problema com a String
     */
    public static String inverteData2(String value) throws Exception {
        if (value.indexOf("/") == -1) {
            return value.substring(4) + "-" + value.substring(2, 4) + "-" + value.substring(0, 2);
        } else {
            return value.substring(6) + "-" + value.substring(3, 5) + "-" + value.substring(0, 2);
        }
    }

    /**
     * Inverte data para usar com sql
     *
     * @param value - String nos formatos ddMMyyyy ou dd/MM/yyyy
     * @return - String no formato yyyyMMdd
     * @throws Exception - gera erro em caso de problema com a String
     */
    public static String inverteData(String value) throws Exception {
        if (value.indexOf("/") == -1) {
            return value.substring(4) + value.substring(2, 4) + value.substring(0, 2);
        } else {
            return value.substring(6) + value.substring(3, 5) + value.substring(0, 2);
        }
    }

    /**
     * Transforma data invertida em data no formato normal
     *
     * @param value - data a ser passada ao formato normal
     * @param formato - ddMMaaaa - sem barras - dd/MM/aaaa - com barras
     * @return - data já no formato normal
     * @throws Exception - caso gere algum erro
     */
    public static String voltaData(String value, String formato) throws Exception {
        try {
            /*if(value==null||value.equals("")){
            if(formato.equals("ddMMaaaa")){
                return "00000000";
            }
            else{
                return "00/00/0000";
            }
        }*/
            if ((value.contains("/")) || (value.contains("-"))) {
                if (formato.equals("ddMMaaaa")) {
                    return value.substring(8, 10) + value.substring(5, 7) + value.substring(0, 4);
                } else if (formato.equals("dd/MM/aaaa")) {
                    return value.substring(8, 10) + "/" + value.substring(5, 7) + "/" + value.substring(0, 4);
                } else {
                    return value;
                }
            } else {
                if (formato.equals("ddMMaaaa")) {
                    return value.substring(6, 8) + value.substring(4, 6) + value.substring(0, 4);
                } else if (formato.equals("dd/MM/aaaa")) {
                    return value.substring(6, 8) + "/" + value.substring(4, 6) + "/" + value.substring(0, 4);
                } else {
                    return value;
                }

            }
        } catch (Exception e) {
            if (formato.equals("ddMMaaaa")) {
                return "00000000";
            } else {
                return "00/00/0000";
            }
        }
    }

    /**
     * Encontra o n-esimo dia util
     *
     * @param initialDate - Data inicial
     * @param qtdias - quantidade de dias uteis que desejamos encontrar
     * @return - devolve o dia do mes
     */
    public static int getWorkingDays(Date initialDate, int qtdias) {

        int quartoDia = 0;
        int workingDays = 0;
        Calendar calendar = new GregorianCalendar();

        //Setando o calendar com a Data Inicial  
        calendar.setTime(initialDate);

        for (int i = 0; i <= 10; i++) {

            if (!(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY)) {
                workingDays++;
                if (workingDays == 4) {
                    quartoDia = calendar.get(Calendar.DATE);
                }
            }
            calendar.add(Calendar.DATE, 1);
        }

        return quartoDia;
    }

    /**
     * Transforma string em data
     *
     * @param stringDate - data em string
     * @return - retorna data
     */
    public static Date stringToDate(String stringDate) throws Exception {

        Date date = null;

        try {
            SimpleDateFormat sdf
                    = new SimpleDateFormat("dd/MM/yyyy", new Locale("pt_BR"));
            date = sdf.parse(stringDate);

        } catch (ParseException e) {
            throw new Exception("Erro ao converter String em data- " + e.getMessage());
        }

        return date;
    }

    /**
     * Verifica se a emissao e menor que o vencimento
     *
     * @param emissao data de emissao
     * @param vencimento date de vencimento
     * @return true se menor e false se maior
     */
    public static boolean verificavencimento(Date emissao, Date vencimento) throws Exception {
        boolean data;
        try {
            if (emissao.before(vencimento)) {
                data = true;
            } else if (emissao.after(vencimento)) {
                data = false;
            } else {
                data = true;
            }
        } catch (Exception e) {
            throw new Exception("Erro ao comparar data -" + e.getMessage());
        }
        return data;
    }

    /**
     * Retorna o ultimo dia do mes informado
     *
     * @param Inicio - primeiro dia do mes
     * @return - retorna o ultimo dia do mes
     * @throws Exception
     */
    public static String ultimo_dia_mes(String Inicio) throws Exception {
        Calendar cal = GregorianCalendar.getInstance();
        cal.setTime(new Date());
        cal.set(Integer.parseInt(Inicio.substring(6)), Integer.parseInt(Inicio.substring(3, 5)), Integer.parseInt(Inicio.substring(0, 2)));

        int dia = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        return (String.valueOf(dia));

    }

    /**
     * Calcula a diferenca entre 2 datas em dias
     *
     * @param inicio - data inicial
     * @param fim - data final
     * @param formato - mascara da data a calcular
     * @return - numero de dias de diferenca
     * @throws Exception
     */
    public static long calcular(String inicio, String fim, String formato) throws Exception {
        try {
            DateFormat df = new SimpleDateFormat(formato);
            Date dtInicial = df.parse(inicio);
            Date dtFinal = df.parse(fim);
            return (dtFinal.getTime() - dtInicial.getTime() + 3600000L) / 86400000L;
        } catch (Exception e) {
            throw new Exception("Erro ao fazer diferenca de datas- " + e.getMessage());
        }
    }

    /**
     * Calcula uma data pela data passada
     *
     * @param data - data base
     * @param diferenca - quantidade de dias, pode ser positivo ou negativo
     * @param formato - mascara da data
     * @return - retorna uma string com a data no formato passado
     * @throws Exception
     */
    public static String getData(String data, int diferenca, String formato) throws Exception {
        SimpleDateFormat tesedata = new SimpleDateFormat(formato);
        Calendar calendar = Calendar.getInstance();
        java.util.Date dtstart;
        java.util.Date dtbefore;
        try {
            dtstart = str2Data(data, formato);//new Date();
            calendar.setTime(dtstart);
            calendar.add(Calendar.DATE, diferenca);
            dtbefore = calendar.getTime();
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
        return tesedata.format(dtbefore);
    }

    /**
     * Converte uma String para um objeto Date. Caso a String seja vazia ou
     * nula, retorna null - para facilitar em casos onde formulários podem ter
     * campos de datas vazios.
     *
     * @param data String no formato dd/MM/yyyy a ser formatada
     * @return Date Objeto Date ou null caso receba uma String vazia ou nula
     * @throws Exception Caso a String esteja no formato errado
     */
    public static Date str2Data(String data, String formato) throws Exception {
        if (data == null || data.equals("")) {
            return null;
        }
        Date date = null;
        try {
            DateFormat formatter = new SimpleDateFormat(formato);
            date = formatter.parse(data);
        } catch (ParseException e) {
            throw e;
        }
        return date;
    }

    /**
     * transforma LocalDate em java.sql.Date
     *
     * @param lc - Data em LocalDate
     * @return - retornar null caso o parametro seja null
     */
    public static java.sql.Date LC2Date(LocalDate lc) {
        java.sql.Date retorno;
        try {
            retorno = java.sql.Date.valueOf(lc);
        } catch (Exception e) {
            retorno = null;
        }
        return retorno;
    }

    /**
     * Converte o TimeZone
     *
     * @param ldt - Data hora de entrada
     * @param ZoneTimeOriginal - Time zone original
     * @param ZoneTimeDestino - Time zone de destino
     * @return - Devolve o LocalDateTime equivalente entrada - 2016/05/12T15:00
     * (GMT) saida - 2016/05/12T12:00 (America/Sao_Paulo);
     * @throws Exception
     */
    public static LocalDateTime TrocaZoneTime(LocalDateTime ldt, ZoneId ZoneTimeOriginal, ZoneId ZoneTimeDestino) throws Exception {
        try {
            ZoneId zoneOri = ZoneTimeOriginal;
            ZoneId zoneDst = ZoneTimeDestino;

            ZonedDateTime zd = ldt.atZone(zoneOri);
            ZonedDateTime zdd = zd.withZoneSameInstant(zoneDst);

            return zdd.toLocalDateTime();
        } catch (Exception e) {
            throw new Exception("Falha ao mudar ZoneTime \r\n" + e.getMessage());
        }
    }

    /**
     * Retorna a quantidade de dias úteis do primeiro dia do mês até a data
     * passada;
     *
     * @param data
     * @return
     * @throws Exception
     */
    public static int diasUteis(LocalDate data) throws Exception {
        try {
            int dias = 0;
            LocalDate primeiroDiaMes = LocalDate.of(data.getYear(), data.getMonth(), 1);
            while (primeiroDiaMes.isBefore(data) || primeiroDiaMes.isEqual(data)) {
                if (!(primeiroDiaMes.getDayOfWeek() == DayOfWeek.SATURDAY
                        || primeiroDiaMes.getDayOfWeek() == DayOfWeek.SUNDAY)) {
                    dias++;
                }
                primeiroDiaMes = primeiroDiaMes.plusDays(1);
            }
            return dias;
        } catch (Exception e) {
            throw new Exception("Falha ao mudar ZoneTime \r\n" + e.getMessage());
        }
    }

    /**
     * HORA no formato 'HH:mm'
     *
     * @param hora
     * @return
     */
    public static int horasMinutos(String hora) {
        try {
            String[] s = hora.split(":");
            return (new BigInteger(s[0]).multiply(new BigInteger("60")).add(new BigInteger(s[1]))).intValue();
        } catch (Exception e) {
            return -1;
        }
    }

    public static String formatarDataSQL(String data) {
        DateTimeFormatter df;
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            df.withResolverStyle(ResolverStyle.LENIENT);
            return LocalDate.parse(data, df).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            df.withResolverStyle(ResolverStyle.LENIENT);
            return LocalDate.parse(data, df).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
            df.withResolverStyle(ResolverStyle.LENIENT);
            return LocalDate.parse(data, df).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            df.withResolverStyle(ResolverStyle.LENIENT);
            return LocalDate.parse(data, df).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
        }
        return data;
    }

    public static String formatarHoraSQL(String hora) {
        DateTimeFormatter df;
        try {
            df = DateTimeFormatter.ofPattern("hh:mm a");
            return LocalTime.parse(hora.toUpperCase(), df).format(DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("HHmm");
            return LocalTime.parse(hora.toUpperCase(), df).format(DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("H:mm:");
            return LocalTime.parse(hora.toUpperCase(), df).format(DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
        }

        try {
            if (hora.contains(" : ")) {
                return "";
            }
        } catch (Exception e) {
        }

        return hora;
    }

    /**
     * <p>
     * Checa se calendário é do dia atual.</p>
     *
     * @param calendario o calendário, não nulo
     * @return true se calendário é do dia atual
     * @throws IllegalArgumentException se calendário é <code>null</code>
     */
    public static boolean isToday(final Calendar calendario) {
        if (calendario == null) {
            throw new IllegalArgumentException("The calendar must not be null");
        }

        Calendar today = Calendar.getInstance();
        return calendario.get(Calendar.ERA) == today.get(Calendar.ERA)
                && calendario.get(Calendar.YEAR) == today.get(Calendar.YEAR)
                && calendario.get(Calendar.DAY_OF_YEAR) == today.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * <p>
     * Checa se calendário é do dia atual.</p>
     *
     * @param dataString o calendário, não nulo
     * @param formato o formato da data, e.g. yyyyMMdd
     * @return true se calendário é do dia atual
     * @throws IllegalArgumentException se calendário é <code>null</code>
     */
    public static boolean isToday(final String dataString, final String formato) {
        Calendar cal = null;
        try {
            cal = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat(formato);
            cal.setTime(sdf.parse(dataString));
        } catch (Exception e) {
            throw new IllegalArgumentException("The calendar must valid");
        }

        return isToday(cal);
    }
    
    /**
     * <p>
     * Checa se calendário é do dia atual.</p>
     *
     * @param dataString o calendário, não nulo
     * @param formato o formato da data, e.g. yyyyMMdd
     * @return true se calendário é do dia atual
     * @throws IllegalArgumentException se calendário é <code>null</code>
     */
    public static boolean isTodayOrFuture(final String dataString, final String formato) {
        try {
            DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                    .appendPattern(formato)
                    .optionalStart()
                    .appendPattern(" HH:mm:ss.S")
                    .optionalEnd()
                    .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
                    .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
                    .toFormatter();

            LocalDate date = LocalDate.parse(dataString, formatter);
            LocalDate today = LocalDate.now();

            return date.compareTo(today) >= 0;
        } catch (Exception e) {
            throw new IllegalArgumentException("The calendar must valid");
        }
    }
}

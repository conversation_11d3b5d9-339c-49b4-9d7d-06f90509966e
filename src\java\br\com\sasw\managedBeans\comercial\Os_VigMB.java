/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.comercial;

import Arquivo.ArquivoLog;
import Controller.OS_Vig.OS_VigSatMobWeb;
import Dados.Persistencia;
import SasBeans.Bancos;
import SasBeans.Clientes;
import SasBeans.ContrVig;
import SasBeans.CtrItens;
import SasBeans.Fat_Grp;
import SasBeans.Filiais;
import SasBeans.OS_VITens;
import SasBeans.OS_Vig;
import SasBeans.SasPWFill;
import SasBeans.TipoSrvCli;
import SasDaos.CtrItensDao;
import SasDaos.OS_VItensDao;
import SasDaos.OS_VigDao;
import br.com.sasw.lazydatamodels.comercial.OS_VigLazyList;
import br.com.sasw.pacotesuteis.sasbeans.FatISSGrp;
import br.com.sasw.pacotesuteis.sasbeans.HT_NF;
import br.com.sasw.pacotesuteis.sasbeans.OS_VFreq;
import br.com.sasw.pacotesuteis.sasdaos.OS_VFreqDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "os_vig")
@ViewScoped
public class Os_VigMB implements Serializable {

    private Persistencia persistencia;
    private String log, dataTela, itemFatTipoCalc, itemFatQtde, itemFatObservacao, itemFatMensagem, opcaoPesquisa, os;
    private final String caminho;
    private final String banco;
    private final String operador;
    private final String codPessoa, codFil;
    private final ArquivoLog logerro;
    private Map filters, TAtend, TCob, TCar, TipoSrv, TRota, Texportar, tipoCalc, tipoPosto;
    private int flag, flagTipoSrvCli, flagAgrupadorNF;
    private boolean mostraFiliais, limpaFiltros, mostraAtivos, mostraVencer,
            viaCxF, entregaSab, entregaDom, entregaFer, editandoFrequencia, editandoOsItens,
            todosDiasUteis, todosDiasMes;
    private SasPWFill filial;
    private OS_VigSatMobWeb os_VigSatMobWeb;
    private LazyDataModel<OS_Vig> os_vigs = null;
    private OS_Vig os_vigSelecionado;
    private Filiais filiais;
    private Clientes cliFat, cliente, cliDst;
    private List<Clientes> cliFatList, clientes, cliDstList;
    private List<CtrItens> ctrItensList;
    private CtrItens ctrItem, ctrItensListSelecionado, ctrItensListVazio;
    private CtrItensDao daoCtrItens;
    private OS_VItensDao daoVitens;
    private List<TipoSrvCli> tipoSrvCliList;
    private TipoSrvCli tipoSrvCli, novoTipoSrvCli;
    private ContrVig contrato;
    private List<ContrVig> contratos;
    private Fat_Grp agrupador, agrupador2, novoAgrupador;
    private List<Fat_Grp> agrupadorList, agrupador2List;
    private List<OS_VITens> os_vitens;
    private OS_VITens os_vitem, os_itemSelecionado;
    private List<OS_VFreq> os_VFreqList;
    private OS_VFreq frequenciaSelecionada, frequenciaEdicao;
    private Bancos bb;
    private List<Bancos> bancos;
    private FatISSGrp fatISSGrp;
    private List<FatISSGrp> fatISSGrpList;
    private HT_NF codHist, codHistExt;
    private List<HT_NF> codHistList, codHistExtList;
    private boolean mostrarValores, mostrarValores1, mostrarValores2;
    private final List<String> frequenciaDias;
    private List<String> frequenciaDiasSelecionados;
    private final List<String> frequenciaDU;
    private List<String> frequenciaDUSelecionados;
    private final int DIAS_SIZE = 31, DU_SIZE = 27;
    private OS_VFreqDao os_FreqDao;

    public Os_VigMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        os = (String) fc.getExternalContext().getSessionMap().get("os");
        codPessoa = ((BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa")).toBigInteger().toString();
        log = new String();
        ctrItensListVazio = new CtrItens();
        os_vitem = new OS_VITens();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + getDataAtual("SQL") + "\\" + codPessoa + ".txt";
        logerro = new ArquivoLog();
        dataTela = getDataAtual("SQL");
        mostrarValores1 = false;
        mostrarValores2 = false;
        mostrarValores = false;
        mostraAtivos = true;
        opcaoPesquisa = "Cliente";
        daoCtrItens = new CtrItensDao();
        daoVitens = new OS_VItensDao();
        frequenciaDias = new ArrayList<>();
        frequenciaDU = new ArrayList<>();
        editandoOsItens = false;

        for (int i = 0; i < DIAS_SIZE; i++) {
            frequenciaDias.add(Integer.toString(i + 1));
            if (i < DU_SIZE) {
                frequenciaDU.add(Integer.toString(i + 1));
            }
        }
    }

    public void Persistencias(Persistencia persistencia) {
        try {
            this.persistencia = persistencia;
            if (null == this.persistencia) {
                throw new Exception(getMessageS("ImpossivelConectarBanco") + " " + this.banco);
            }

            // TODO: colocar persistencia no construtor
            os_VigSatMobWeb = new OS_VigSatMobWeb(persistencia);
            os_FreqDao = new OS_VFreqDao(this.persistencia);
            this.filters = new HashMap();
            this.filters.put(" OS_Vig.codfil = ? ", this.codFil);
            this.filters.put(" OS_Vig.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = '" + this.banco + "')", this.codPessoa);
            this.filters.put(" OS_Vig.OS = ? ", this.os == null ? "" : this.os);
            this.filters.put(" CliOri.NRed LIKE ? ", "");
            this.filters.put(" OS_Vig.NRedDst LIKE ? ", "");
            this.filters.put(" OS_Vig.NRedFat LIKE ? ", "");
            this.filters.put(" OS_Vig.Agrupador = ? ", "");
            this.filters.put(" CliOri.Agencia LIKE ? ", "");
            this.filters.put(" OS_Vig.Situacao = ? ", "A");
            this.filters.put(" OS_Vig.DtInicio <= ? ", this.dataTela);
            this.filters.put(" OS_Vig.DtFim >= ? ", this.dataTela);

            this.filiais = this.os_VigSatMobWeb.buscaInfoFilial(this.codFil, this.persistencia);
            this.tipoSrvCliList = this.os_VigSatMobWeb.listarTipoSrvCli(this.persistencia);

            this.TAtend = gerarTAtend();
            this.TCob = gerarTCob();
            this.TCar = gerarTCar();
            this.TipoSrv = gerarTipoSrv();
            this.TRota = gerarTRota();
            this.Texportar = gerarTexportar();

            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().replace("os", null);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private void displayInfo(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayWarn(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayError(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    private void displayFatal(String mensagemDicionario) {
        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_FATAL, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    public LazyDataModel<OS_Vig> getAllOS_Vig() {
        try {
            if (this.os_vigs == null) {
                this.filters.replace(" OS_Vig.codfil = ? ", this.codFil);
                this.filters.replace(" OS_Vig.codfil in (select filiais.codfil"
                        + "                          from saspw"
                        + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                        + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                        + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                        + "                          where saspw.codpessoa = ? and paramet.path = '" + this.banco + "')", this.codPessoa);
                this.filters.replace(" OS_Vig.OS = ? ", this.os == null ? "" : this.os);
                this.filters.replace(" CliOri.NRed LIKE ? ", "");
                this.filters.replace(" OS_Vig.NRedDst LIKE ? ", "");
                this.filters.replace(" OS_Vig.NRedFat LIKE ? ", "");
                this.filters.replace(" OS_Vig.Agrupador = ? ", "");
                this.filters.replace(" CliOri.Agencia LIKE ? ", "");
                this.filters.replace(" OS_Vig.Situacao = ? ", "A");
                this.filters.replace(" OS_Vig.DtInicio <= ? ", this.dataTela);
                this.filters.replace(" OS_Vig.DtFim >= ? ", this.dataTela);
                this.os_vigs = new OS_VigLazyList(this.persistencia, this.filters);
            } else {
                ((OS_VigLazyList) this.os_vigs).setFilters(this.filters);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.os_vigs;
    }

    public void selecionarOS() {
        if (this.os_vigSelecionado != null && this.os_vigSelecionado.getOS() != null) {
            FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("os", this.os_vigSelecionado.getOS().replace(".0", ""));
        }
    }

    private LazyDataModel<OS_Vig> getAllOS_VigPesquisa() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        dt.setFirst(0);

//        os_vigs = new OS_VigLazyList(persistencia);
//        try {
//            total = this.os_VigSatMobWeb.totalListaPaginada(filters, persistencia);
//        } catch (Exception e) {
//            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
//            FacesContext.getCurrentInstance().addMessage(null, mensagem);
//            log = this.getClass().getSimpleName() + "\r\n"
//                    + Thread.currentThread().getStackTrace()[1].getMethodName()
//                    + "\r\n" + e.getMessage() + "\r\n";
//            logerro.Grava(log, caminho);
//        }
        return os_vigs;
    }

    public void preCadastro() {
        try {
            this.flag = 1;

            this.os_vigSelecionado = new OS_Vig();
            this.os_vigSelecionado.setCodFil(this.codFil);
            this.os_vigSelecionado.setSituacao("A");
            this.os_vigSelecionado.setKM("0");
            this.os_vigSelecionado.setKMTerra("0");
            this.os_vigSelecionado.setGTVQtde("0");
            this.os_vigSelecionado.setGTVEstMin("0");

            this.filial = this.os_VigSatMobWeb.buscaFilial(this.codFil, this.codPessoa, this.persistencia);

            this.tipoSrvCli = new TipoSrvCli();

            this.cliFat = null;
            this.cliFatList = new ArrayList<>();
            this.cliFatList.add(this.cliFat);

            this.cliente = null;
            this.clientes = new ArrayList<>();
            this.clientes.add(this.cliente);

            this.cliDst = null;
            this.cliDstList = new ArrayList<>();
            this.cliDstList.add(this.cliDst);

            this.contrato = null;
            this.contratos = new ArrayList<>();
            this.contratos.add(this.contrato);

            this.agrupador = null;
            this.agrupadorList = new ArrayList<>();
            this.agrupadorList.add(this.agrupador);

            this.agrupador2 = null;
            this.agrupador2List = new ArrayList<>();
            this.agrupador2List.add(this.agrupador2);

            this.viaCxF = false;
            this.entregaSab = false;
            this.entregaDom = false;
            this.entregaFer = false;

            this.os_vitens = new ArrayList<>();

            TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
            tabs.setActiveIndex(0);
            PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
            PrimeFaces.current().ajax().update("formCadastrar");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void atualizarViaCxForte() {
        if (this.viaCxF) {
            this.os_vigSelecionado.setViaCxF("S");
        } else {
            this.os_vigSelecionado.setViaCxF("N");
            this.os_vigSelecionado.setDiasCst(0);
            this.entregaSab = false;
            this.os_vigSelecionado.setEntregaSab("N");
            this.entregaDom = false;
            this.os_vigSelecionado.setEntregaDom("N");
            this.entregaFer = false;
            this.os_vigSelecionado.setEntregaFer("N");
        }
    }

    public void dblItensFaturamento(SelectEvent event) {
        this.os_itemSelecionado = (OS_VITens) event.getObject();
        preCadastroItensEditar();
    }

    public void preCadastroItens() {
        try {
            this.editandoOsItens = false;
            daoCtrItens = new CtrItensDao();

            this.ctrItensList = this.daoCtrItens.listarItensFaturamentoOS(this.os_vigSelecionado.getContrato(), this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), "0", this.persistencia);
            this.os_vitem = new OS_VITens();
            this.ctrItensListSelecionado = new CtrItens();

            if (null != this.os_vigSelecionado.getDtFim()
                    && !this.os_vigSelecionado.getDtFim().equals("")) {
                this.os_vitem.setDtFim(this.os_vigSelecionado.getDtFim());
            }

            if (null != this.os_vigSelecionado.getDtInicio()
                    && !this.os_vigSelecionado.getDtInicio().equals("")) {
                this.os_vitem.setDtInicio(this.os_vigSelecionado.getDtInicio());
            }

            PrimeFaces.current().resetInputs("formCadastrarItensFaturamento:cadastrarItens");
            PrimeFaces.current().executeScript("PF('dlgCadastrarItensFaturamento').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void preCadastroItensEditar() {
        try {
            if (null != this.os_itemSelecionado) {
                this.editandoOsItens = true;

                this.ctrItensList = this.daoCtrItens.listarItensFaturamentoOS(this.os_vigSelecionado.getContrato(), this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), this.os_itemSelecionado.getTipoPosto(), this.persistencia);

                this.ctrItensListSelecionado = new CtrItens();
                this.ctrItensListSelecionado.setDescricao(this.os_itemSelecionado.getTipoPostoDesc());
                this.ctrItensListSelecionado.setTipoPosto(this.os_itemSelecionado.getTipoPosto());
                this.ctrItensListSelecionado.setTipoCalc(this.os_itemSelecionado.getTipoCalc());
                this.ctrItensListSelecionado.setContrato(this.os_vigSelecionado.getContrato());
                this.ctrItensListSelecionado.setCodFil(this.codFil);

                this.os_vitem = new OS_VITens(this.os_itemSelecionado.getOS(),
                        this.os_itemSelecionado.getCodFil(),
                        this.os_itemSelecionado.getTipoPosto(),
                        this.os_itemSelecionado.getDtInicio(),
                        this.os_itemSelecionado.getDtFim(),
                        this.os_itemSelecionado.getQtde(),
                        this.os_itemSelecionado.getObs(),
                        this.os_itemSelecionado.getMsgExtrato(),
                        this.os_itemSelecionado.getValor(),
                        this.os_itemSelecionado.getOperador(),
                        this.os_itemSelecionado.getDt_Alter(),
                        this.os_itemSelecionado.getHr_Alter(),
                        this.os_itemSelecionado.getCHSeman(),
                        this.os_itemSelecionado.getCHMensal(),
                        this.os_itemSelecionado.getSalario(),
                        this.os_itemSelecionado.getTipoPostoDesc(),
                        this.os_itemSelecionado.getTipoCalc());

                PrimeFaces.current().resetInputs("formCadastrarItensFaturamento:cadastrarItens");
                PrimeFaces.current().executeScript("PF('dlgCadastrarItensFaturamento').show();");
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void preCadastroItensExcluir() {
        try {
            if (null != this.os_itemSelecionado) {

                this.os_itemSelecionado.setOS(this.os_vigSelecionado.getOS());
                this.daoVitens.Excluir(this.os_itemSelecionado, this.persistencia);
                this.os_itemSelecionado = new OS_VITens();
                this.os_vitens = this.os_VigSatMobWeb.listarOS_VItens(this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), this.persistencia);
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void preCadastroItensContrato() {
        try {
            this.mostrarValores = false;
            mostrarValoresGrid();
            this.ctrItem = null;
            this.ctrItensList = this.os_VigSatMobWeb.listarItensFaturamentoContrato(this.os_vigSelecionado.getContrato(), this.os_vigSelecionado.getCodFil(), this.persistencia);

            PrimeFaces.current().ajax().update("formCadastrarItensContrato");
            PrimeFaces.current().executeScript("PF('dlgCadastrarItensContrato').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void mostrarValoresGrid() {
        if (this.mostrarValores) {
            if (this.os_vigSelecionado.getTipoOS() != null
                    && this.os_vigSelecionado.getTipoOS().equals("4")) {
                this.mostrarValores2 = true;
            } else {
                this.mostrarValores1 = true;
            }
        } else {
            this.mostrarValores1 = false;
            this.mostrarValores2 = false;
        }
    }

    public void cadastrar() {
        try {

            if (this.os_vigSelecionado.getDescricao() != null) {
                this.os_vigSelecionado.setDescricao(this.os_vigSelecionado.getDescricao().toUpperCase());
            }
            if (this.os_vigSelecionado.getAditivo() != null) {
                this.os_vigSelecionado.setAditivo(this.os_vigSelecionado.getAditivo().toUpperCase());
            }
            if (this.os_vigSelecionado.getCCusto() != null) {
                this.os_vigSelecionado.setCCusto(this.os_vigSelecionado.getCCusto().toUpperCase());
            }
            if (this.os_vigSelecionado.getMsgExtrato() != null) {
                this.os_vigSelecionado.setMsgExtrato(this.os_vigSelecionado.getMsgExtrato().toUpperCase());
            }

            this.os_vigSelecionado.setViaCxF(this.viaCxF ? "S" : "N");
            this.os_vigSelecionado.setEntregaSab(this.entregaSab ? "S" : "N");
            this.os_vigSelecionado.setEntregaDom(this.entregaDom ? "S" : "N");
            this.os_vigSelecionado.setEntregaFer(this.entregaFer ? "S" : "N");

            this.os_vigSelecionado.setOperIncl(RecortaAteEspaço(this.operador, 0, 10).toUpperCase());
            this.os_vigSelecionado.setOperador(RecortaAteEspaço(this.operador, 0, 10).toUpperCase());

            this.os_vigSelecionado.setDt_Incl(getDataAtual("SQL"));
            this.os_vigSelecionado.setDt_Alter(getDataAtual("SQL"));

            this.os_vigSelecionado.setHr_Incl(getDataAtual("HORA"));
            this.os_vigSelecionado.setHr_Alter(getDataAtual("HORA"));

            this.os_VigSatMobWeb.inserirOS_Vig(this.os_vigSelecionado, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void clonarOS() throws Exception {
        OS_VigDao osVigDao = new OS_VigDao();
        osVigDao.clonarOS(this.os_vigSelecionado, this.persistencia);

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(filters);
        getAllOS_Vig();
        dt.setFirst(0);

        this.os_vigSelecionado = new OS_Vig();

        PrimeFaces.current().executeScript("$.MsgBoxAzulOk('" + getMessageS("Aviso") + "','" + getMessageS("OsClonadaSucesso") + "');");
    }

    public void preClone() {
        if (null == this.os_vigSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneOS"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {

            this.os_vigSelecionado.setOperador(RecortaAteEspaço("CLN-" + this.operador, 0, 10).toUpperCase());
            this.os_vigSelecionado.setDt_Alter(getDataAtual("SQL"));
            this.os_vigSelecionado.setHr_Alter(getDataAtual("HORA"));

            StringBuilder str = new StringBuilder();

            str.append("$.MsgBoxVerdeSimNao('").append(getMessageS("Atencao")).append("',");
            str.append("                    '").append(getMessageS("PerguntaClone")).append(" <b>' + ").append(this.os_vigSelecionado.getOS()).append(" + '</b>?',");
            str.append("                    '").append(getMessageS("Sim")).append("',");
            str.append("                    '").append(getMessageS("Nao")).append("',");
            str.append("                    function () {");
            str.append("                        rcClonarOS();");
            str.append("                    },");
            str.append("                    null);");

            PrimeFaces.current().executeScript(str.toString());
        }
    }

    public void preEdicaoNovo(OS_Vig osVigSel) {
        this.os_vigSelecionado = osVigSel;
        preEdicao();
    }
    
    public void preEdicao() {
        if (null == this.os_vigSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneOS"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                OS_Vig GuardaOS = this.os_vigSelecionado;
                this.preCadastro();
                this.os_vigSelecionado = GuardaOS;

                this.flag = 2;

                this.filial = this.os_VigSatMobWeb.buscaFilial(this.os_vigSelecionado.getCodFil(), this.codPessoa, this.persistencia);

                this.tipoSrvCli = new TipoSrvCli();
                this.tipoSrvCli.setCodigo(this.os_vigSelecionado.getCodSrv());
                if (this.tipoSrvCliList.indexOf(this.tipoSrvCli) >= 0) {
                    this.tipoSrvCli = this.tipoSrvCliList.get(this.tipoSrvCliList.indexOf(this.tipoSrvCli));
                }

                this.cliFat = this.os_VigSatMobWeb.obterCliente(this.os_vigSelecionado.getCliFat(), this.os_vigSelecionado.getCodFil(), this.persistencia);
                this.cliFatList = new ArrayList<>();
                this.cliFatList.add(this.cliFat);

                this.cliente = this.os_VigSatMobWeb.obterCliente(this.os_vigSelecionado.getCliente(), this.os_vigSelecionado.getCodFil(), this.persistencia);
                this.clientes = new ArrayList<>();
                this.clientes.add(this.cliente);

                this.cliDst = this.os_VigSatMobWeb.obterCliente(this.os_vigSelecionado.getCliDst(), this.os_vigSelecionado.getCodFil(), this.persistencia);
                this.cliDstList = new ArrayList<>();
                this.cliDstList.add(this.cliDst);

                this.contrato = this.os_VigSatMobWeb.obterContrato(this.os_vigSelecionado.getContrato(), this.os_vigSelecionado.getCodFil(), this.persistencia); // Contrato, CodFil
                this.contratos = new ArrayList<>();
                this.contratos.add(this.contrato);

                this.agrupador = null;
                this.agrupador = this.os_VigSatMobWeb.buscarFat_Grp(this.os_vigSelecionado.getAgrupador(), this.os_vigSelecionado.getCodFil(), this.persistencia);
                this.agrupadorList = new ArrayList<>();
                this.agrupadorList.add(this.agrupador);

                this.agrupador2 = null;
                this.agrupador2 = this.os_VigSatMobWeb.buscarFat_Grp(this.os_vigSelecionado.getOSGrp(), this.os_vigSelecionado.getCodFil(), this.persistencia);
                this.agrupador2List = new ArrayList<>();
                this.agrupador2List.add(this.agrupador2);

                this.viaCxF = this.os_vigSelecionado.getViaCxF().equals("S");
                this.entregaSab = this.os_vigSelecionado.getEntregaSab().equals("S");
                this.entregaDom = this.os_vigSelecionado.getEntregaDom().equals("S");
                this.entregaFer = this.os_vigSelecionado.getEntregaFer().equals("S");

                this.os_vitens = this.os_VigSatMobWeb.listarOS_VItens(this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), this.persistencia);
                this.os_VFreqList = this.os_VigSatMobWeb.listarOS_VFreq(this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), this.persistencia);

                TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
                tabs.setActiveIndex(0);
                PrimeFaces.current().ajax().update("formCadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, this.caminho);
            }
        }
    }

    public void editar() {
        try {

            if (this.os_vigSelecionado.getDescricao() != null) {
                this.os_vigSelecionado.setDescricao(this.os_vigSelecionado.getDescricao().toUpperCase());
            }
            if (this.os_vigSelecionado.getAditivo() != null) {
                this.os_vigSelecionado.setAditivo(this.os_vigSelecionado.getAditivo().toUpperCase());
            }
            if (this.os_vigSelecionado.getCCusto() != null) {
                this.os_vigSelecionado.setCCusto(this.os_vigSelecionado.getCCusto().toUpperCase());
            }
            if (this.os_vigSelecionado.getMsgExtrato() != null) {
                this.os_vigSelecionado.setMsgExtrato(this.os_vigSelecionado.getMsgExtrato().toUpperCase());
            }

            this.os_vigSelecionado.setViaCxF(this.viaCxF ? "S" : "N");
            this.os_vigSelecionado.setEntregaSab(this.entregaSab ? "S" : "N");
            this.os_vigSelecionado.setEntregaDom(this.entregaDom ? "S" : "N");
            this.os_vigSelecionado.setEntregaFer(this.entregaFer ? "S" : "N");

            this.os_vigSelecionado.setOperador(RecortaAteEspaço(this.operador, 0, 10).toUpperCase());

            this.os_vigSelecionado.setDt_Alter(getDataAtual("SQL"));

            this.os_vigSelecionado.setHr_Alter(getDataAtual("HORA"));

            this.os_VigSatMobWeb.atualizarOS_Vig(this.os_vigSelecionado, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void prePesquisa() {
        this.opcaoPesquisa = "Cliente";
        this.os_vigSelecionado = new OS_Vig();
        this.os_vigSelecionado.setCodFil(this.codFil);
        PrimeFaces.current().resetInputs("formPesquisar");
        PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
    }

    public void pesquisar() {

        this.filters.replace(" OS_Vig.codfil = ? ", this.os_vigSelecionado.getCodFil() == null || this.os_vigSelecionado.getCodFil().equals("")
                ? "" : this.os_vigSelecionado.getCodFil());
        this.filters.replace(" Clientes.nome like ? ", this.os_vigSelecionado.getNome() == null || this.os_vigSelecionado.getNome().equals("")
                ? "" : "%" + this.os_vigSelecionado.getNome() + "%");
        this.filters.replace(" OS_Vig.OS = ? ", this.os_vigSelecionado.getOS() == null || this.os_vigSelecionado.getOS().equals("")
                ? "" : this.os_vigSelecionado.getOS());
        this.filters.replace(" CliOri.NRed LIKE ? ", this.os_vigSelecionado.getNRed() == null || this.os_vigSelecionado.getNRed().equals("")
                ? "" : "%" + this.os_vigSelecionado.getNRed() + "%");
        this.filters.replace(" OS_Vig.NRedDst LIKE ? ", this.os_vigSelecionado.getNRedDst() == null || this.os_vigSelecionado.getNRedDst().equals("")
                ? "" : "%" + this.os_vigSelecionado.getNRedDst() + "%");
        this.filters.replace(" OS_Vig.NRedFat LIKE ? ", this.os_vigSelecionado.getNRedFat() == null || this.os_vigSelecionado.getNRedFat().equals("")
                ? "" : "%" + this.os_vigSelecionado.getNRedFat() + "%");
        this.filters.replace(" OS_Vig.Agrupador = ? ", this.os_vigSelecionado.getAgrupador() == null || this.os_vigSelecionado.getAgrupador().equals("")
                ? "" : this.os_vigSelecionado.getAgrupador());
        this.filters.replace(" CliOri.Agencia LIKE ? ", this.os_vigSelecionado.getAgencia() == null || this.os_vigSelecionado.getAgencia().equals("")
                ? "" : "%" + this.os_vigSelecionado.getAgencia() + "%");

        this.filters.replace(" OS_Vig.DtInicio <= ? ", "");
        this.filters.replace(" OS_Vig.DtFim >= ? ", "");

        getAllOS_Vig();
    }

    public void mostrarFiliais() {
        this.filters.replace(" OS_Vig.codfil = ? ", this.mostraFiliais ? "" : this.codFil);
        getAllOS_Vig();
    }

    public void mostrarAtivos() {
        this.filters.replace(" OS_Vig.DtInicio <= ? ", this.mostraAtivos ? this.dataTela : "");
        this.filters.replace(" OS_Vig.DtFim >= ? ", this.mostraAtivos ? this.dataTela : "");
        this.filters.replace(" OS_Vig.Situacao = ? ", this.mostraAtivos ? "A" : "");
        getAllOS_Vig();
    }

    public void iniciarTabela(OS_Vig osvig, String componente) {
        os_vigSelecionado = osvig;

        filters.replace(" OS_Vig.codfil = ? ", codFil);
        filters.replace(" OS_Vig.codfil in (select filiais.codfil"
                + "    from saspw"
                + "    inner join saspwfil on saspwfil.nome = saspw.nome"
                + "    inner join filiais on filiais.codfil = saspwfil.codfilac"
                + "    inner join paramet on paramet.filial_pdr = filiais.codfil"
                + "    where saspw.codpessoa = ? and paramet.path = '" + banco + "')", codPessoa);
        filters.replace(" OS_Vig.OS = ? ", osvig.getOS() == null ? "" : osvig.getOS());
        filters.replace(" CliOri.NRed LIKE ? ", "");
        filters.replace(" OS_Vig.NRedDst LIKE ? ", "");
        filters.replace(" OS_Vig.NRedFat LIKE ? ", "");
        filters.replace(" OS_Vig.Agrupador = ? ", "");
        filters.replace(" CliOri.Agencia LIKE ? ", "");

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(componente);
        dt.setFilters(filters);
        getAllOS_Vig();
        dt.setFirst(0);
    }

    public void limparFiltros() {
        this.filters.replace(" OS_Vig.codfil = ? ", this.codFil);
        this.filters.replace(" OS_Vig.codfil in (select filiais.codfil"
                + "    from saspw"
                + "    inner join saspwfil on saspwfil.nome = saspw.nome"
                + "    inner join filiais on filiais.codfil = saspwfil.codfilac"
                + "    inner join paramet on paramet.filial_pdr = filiais.codfil"
                + "    where saspw.codpessoa = ? and paramet.path = '" + this.banco + "')", this.codPessoa);
        this.filters.replace(" OS_Vig.OS = ? ", "");
        this.filters.replace(" CliOri.NRed LIKE ? ", "");
        this.filters.replace(" OS_Vig.NRedDst LIKE ? ", "");
        this.filters.replace(" OS_Vig.NRedFat LIKE ? ", "");
        this.filters.replace(" OS_Vig.Agrupador = ? ", "");
        this.filters.replace(" CliOri.Agencia LIKE ? ", "");
        this.filters.replace(" OS_Vig.DtInicio <= ? ", this.dataTela);
        this.filters.replace(" OS_Vig.DtFim >= ? ", this.dataTela);

        this.mostraFiliais = false;
        this.mostraAtivos = false;
        this.mostraVencer = false;
        this.limpaFiltros = false;

        getAllOS_Vig();
    }

    public void limparFiltrosDeComponente(String componente) {
        filters.replace(" OS_Vig.codfil = ? ", codFil);
        filters.replace(" OS_Vig.codfil in (select filiais.codfil"
                + "    from saspw"
                + "    inner join saspwfil on saspwfil.nome = saspw.nome"
                + "    inner join filiais on filiais.codfil = saspwfil.codfilac"
                + "    inner join paramet on paramet.filial_pdr = filiais.codfil"
                + "    where saspw.codpessoa = ? and paramet.path = '" + banco + "')", codPessoa);
        filters.replace(" OS_Vig.OS = ? ", "");
        filters.replace(" CliOri.NRed LIKE ? ", "");
        filters.replace(" OS_Vig.NRedDst LIKE ? ", "");
        filters.replace(" OS_Vig.NRedFat LIKE ? ", "");
        filters.replace(" OS_Vig.Agrupador = ? ", "");
        filters.replace(" CliOri.Agencia LIKE ? ", "");

        mostraFiliais = false;
        mostraAtivos = false;
        mostraVencer = false;
        limpaFiltros = false;

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(componente);
        dt.setFilters(filters);
        getAllOS_Vig();
        dt.setFirst(0);
    }

    public void selecionarFilial(SelectEvent event) {
        this.filial = ((SasPWFill) event.getObject());

        this.os_vigSelecionado = new OS_Vig();
        this.os_vigSelecionado.setCodFil(this.filial.getCodfilAc());

        this.tipoSrvCli = new TipoSrvCli();

        this.cliFat = null;
        this.cliFatList = new ArrayList<>();
        this.cliFatList.add(this.cliFat);

        this.cliente = null;
        this.clientes = new ArrayList<>();
        this.clientes.add(this.cliente);

        this.cliDst = null;
        this.cliDstList = new ArrayList<>();
        this.cliDstList.add(this.cliDst);

        this.contrato = null;
        this.contratos = new ArrayList<>();
        this.contratos.add(this.contrato);

        this.agrupador = null;
        this.agrupadorList = new ArrayList<>();
        this.agrupadorList.add(this.agrupador);

        this.agrupador2 = null;
        this.agrupador2List = new ArrayList<>();
        this.agrupador2List.add(this.agrupador2);

        this.os_vitens = new ArrayList<>();
    }

    public void selecionarCodSrv(SelectEvent event) {
        // this.tipoSrvCli = ((TipoSrvCli) event.getObject());
        this.os_vigSelecionado.setCodSrv(this.tipoSrvCli.getCodigo());
    }

    public List<Clientes> listarCliFat(String query) {
        try {
            this.cliFatList = this.os_VigSatMobWeb.buscaClientes(this.os_vigSelecionado.getCodFil(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.cliFatList;
    }

    public void selecionarCliFat(SelectEvent event) {
        this.cliFat = ((Clientes) event.getObject());
        this.os_vigSelecionado.setCliFat(this.cliFat.getCodigo());
        this.os_vigSelecionado.setNRedFat(this.cliFat.getNRed());
    }

    public List<ContrVig> listarContrato(String query) {
        try {
            this.contratos = this.os_VigSatMobWeb.buscaContratos(this.os_vigSelecionado.getCodFil(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.contratos;
    }

    public void selecionarContrato(SelectEvent event) {
        this.contrato = ((ContrVig) event.getObject());
        this.os_vigSelecionado.setContrato(this.contrato.getContrato());
    }

    public List<Fat_Grp> listarAgrupador(String query) {
        try {
            this.agrupadorList = this.os_VigSatMobWeb.buscaFat_Grp(this.os_vigSelecionado.getCodFil(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.agrupadorList;
    }

    public void selecionarAgrupador(SelectEvent event) {
        this.agrupador = ((Fat_Grp) event.getObject());
        this.os_vigSelecionado.setAgrupador(this.agrupador.getCodigo());
    }

    public List<Fat_Grp> listaragrupador2(String query) {
        try {
            this.agrupador2List = this.os_VigSatMobWeb.buscaFat_Grp(this.os_vigSelecionado.getCodFil(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.agrupador2List;
    }

    public void selecionaragrupador2(SelectEvent event) {
        this.agrupador2 = ((Fat_Grp) event.getObject());
        this.os_vigSelecionado.setOSGrp(this.agrupador2.getCodigo());
    }

    public List<Clientes> listarCliente(String query) {
        try {
            this.clientes = this.os_VigSatMobWeb.buscaClientes(this.os_vigSelecionado.getCodFil(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.clientes;
    }

    public void selecionarCliente(SelectEvent event) {
        this.cliente = ((Clientes) event.getObject());
        this.os_vigSelecionado.setCliente(this.cliente.getCodigo());
        this.os_vigSelecionado.setNRed(this.cliente.getNRed());
    }

    public List<Clientes> listarCliDst(String query) {
        try {
            this.cliDstList = this.os_VigSatMobWeb.buscaClientes(this.os_vigSelecionado.getCodFil(), query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.cliDstList;
    }

    public void selecionarCliDst(SelectEvent event) {
        this.cliDst = ((Clientes) event.getObject());
        this.os_vigSelecionado.setCliDst(this.cliDst.getCodigo());
        this.os_vigSelecionado.setNRedDst(this.cliDst.getNRed());
    }

    public void preCadastroCodgioServico() {
        try {
            this.flagTipoSrvCli = 1;
            this.novoTipoSrvCli = new TipoSrvCli();
            this.bb = null;
            this.bancos = new ArrayList<>();
            this.bancos.add(this.bb);
            PrimeFaces.current().resetInputs("formCodigoServico:cadastrar");
            PrimeFaces.current().ajax().update("formCodigoServico");
            PrimeFaces.current().executeScript("PF('dlgCodigoServico').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void buscarCodigoServico() {
        try {
            TipoSrvCli aux = this.os_VigSatMobWeb.buscarTipoSrvCli(this.novoTipoSrvCli.getCodigo(), this.persistencia);
            if (aux != null) {
                this.flagTipoSrvCli = 2;
                this.novoTipoSrvCli = aux;

                this.bb = this.os_VigSatMobWeb.buscarBanco(this.novoTipoSrvCli.getBanco(), this.persistencia);
                this.bancos = new ArrayList<>();
                this.bancos.add(this.bb);
                PrimeFaces.current().ajax().update("formCodigoServico:cadastrar");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void cadastrarCodgioServico() {
        try {
            if (this.novoTipoSrvCli.getDescricao() != null) {
                this.novoTipoSrvCli.setDescricao(this.novoTipoSrvCli.getDescricao().toUpperCase());
            }
            if (this.novoTipoSrvCli.getAditivo() != null) {
                this.novoTipoSrvCli.setAditivo(this.novoTipoSrvCli.getAditivo().toUpperCase());
            }

            this.novoTipoSrvCli.setDt_Alter(getDataAtual("SQL"));
            this.novoTipoSrvCli.setHr_Alter(getDataAtual("HORA"));
            this.novoTipoSrvCli.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.os_VigSatMobWeb.cadastrarTipoSrvCli(this.novoTipoSrvCli, this.persistencia);

            this.tipoSrvCliList = this.os_VigSatMobWeb.listarTipoSrvCli(this.persistencia);
            this.tipoSrvCli = this.novoTipoSrvCli;
            this.os_vigSelecionado.setCodSrv(this.tipoSrvCli.getCodigo());

            PrimeFaces.current().executeScript("PF('dlgCodigoServico').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void editarCodgioServico() {
        try {
            if (this.novoTipoSrvCli.getDescricao() != null) {
                this.novoTipoSrvCli.setDescricao(this.novoTipoSrvCli.getDescricao().toUpperCase());
            }
            if (this.novoTipoSrvCli.getAditivo() != null) {
                this.novoTipoSrvCli.setAditivo(this.novoTipoSrvCli.getAditivo().toUpperCase());
            }

            this.novoTipoSrvCli.setDt_Alter(getDataAtual("SQL"));
            this.novoTipoSrvCli.setHr_Alter(getDataAtual("HORA"));
            this.novoTipoSrvCli.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.os_VigSatMobWeb.editarTipoSrvCli(this.novoTipoSrvCli, this.persistencia);

            this.tipoSrvCliList = this.os_VigSatMobWeb.listarTipoSrvCli(this.persistencia);
            this.tipoSrvCli = this.novoTipoSrvCli;
            this.os_vigSelecionado.setCodSrv(this.tipoSrvCli.getCodigo());

            PrimeFaces.current().executeScript("PF('dlgCodigoServico').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    private Map gerarTAtend() {
        Map map = new HashMap<>();
        map.put(getMessageS("AssistenciaTecnica"), "AST");
        map.put(getMessageS("ColetaBau"), "COB");
        map.put(getMessageS("Compartilhado"), "CPT");
        map.put(getMessageS("Custodia"), "CST");
        map.put(getMessageS("PorEquipamento"), "EQP");
        map.put(getMessageS("Especial"), "ESP");
        map.put(getMessageS("Eventual1"), "EV1");
        map.put(getMessageS("Eventual2"), "EV2");
        map.put(getMessageS("PorPonto"), "PTO");
        map.put(getMessageS("Rotineiro"), "ROT");
        map.put(getMessageS("CarroDedicado"), "DED");
        map.put(getMessageS("DefinidoContrato"), "XXX");
        return map;
    }

    private Map gerarTCob() {
        Map map = new HashMap<>();
        map.put(getMessageS("Embarque"), "EMB");
        map.put(getMessageS("Mensal"), "MEN");
        map.put(getMessageS("EmbarqueMensalizado"), "PFE");
        map.put(getMessageS("Quantidade"), "QTD");
        map.put(getMessageS("Interbancario"), "INT");
        map.put(getMessageS("DefinidoContrato"), "XXX");
        return map;
    }

    private Map gerarTCar() {
        Map map = new HashMap<>();
        map.put(getMessageS("AeronaveFretada"), "AFR");
        map.put(getMessageS("AeronaveRegular"), "ARE");
        map.put(getMessageS("CarroForte"), "CFT");
        map.put(getMessageS("CarroLeve"), "CLV");
        map.put(getMessageS("DefinidoContrato"), "XXX");
        return map;
    }

    private Map gerarTipoSrv() {
        Map map = new HashMap<>();
        map.put(getMessageS("Rotineiro"), "R");
        map.put(getMessageS("Eventual"), "V");
        map.put(getMessageS("Especial"), "E");
        map.put(getMessageS("AdValorem"), "A");
        map.put(getMessageS("Extraordinario"), "X");
        map.put(getMessageS("ProcessamentoEnvelopesDestino"), "P");
        map.put(getMessageS("Monitoramento"), "M");
        map.put(getMessageS("Todos"), "T");
        return map;
    }

    private Map gerarTRota() {
        Map map = new HashMap<>();
        map.put(getMessageS("Entrega"), "E");
        map.put(getMessageS("Recolhimento"), "R");
        map.put(getMessageS("Todos"), "T");
        return map;
    }

    private Map gerarTexportar() {
        Map map = new HashMap<>();
        map.put(getMessageS("Exportar"), "S");
        map.put(getMessageS("NaoExportar"), "N");
        map.put(getMessageS("ExportarDBFCliente"), "C");
        return map;
    }

    public List<Bancos> listarBancos(String query) {
        try {
            this.bancos = this.os_VigSatMobWeb.buscarBancos(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.bancos;
    }

    public void selecionarBanco(SelectEvent event) {
        this.bb = ((Bancos) event.getObject());
        this.novoTipoSrvCli.setBanco(this.bb.getBanco());
    }

    public void preCadastroAgrupadorNF() {
        try {
            this.flagAgrupadorNF = 1;
            this.novoAgrupador = new Fat_Grp();
            this.novoAgrupador.setCodFil(this.os_vigSelecionado.getCodFil());
            PrimeFaces.current().resetInputs("formAgrupador:cadastrar");
            PrimeFaces.current().ajax().update("formAgrupador");
            PrimeFaces.current().executeScript("PF('dlgAgrupador').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void buscarAgrupadorNF() {
        try {
            Fat_Grp aux = this.os_VigSatMobWeb.buscarFat_Grp(this.novoAgrupador.getCodigo(), this.novoAgrupador.getCodFil(), this.persistencia);
            if (aux != null) {
                this.flagAgrupadorNF = 2;
                this.novoAgrupador = aux;

                this.fatISSGrp = this.os_VigSatMobWeb.buscarFatISSGrp(this.novoAgrupador.getAgrupISS(), this.persistencia);
                this.fatISSGrpList = new ArrayList<>();
                this.fatISSGrpList.add(this.fatISSGrp);

                this.codHist = this.os_VigSatMobWeb.buscarHT_NF(this.novoAgrupador.getCodHist(), this.novoAgrupador.getCodFil(), this.persistencia);
                this.codHistList = new ArrayList<>();
                this.codHistList.add(this.codHist);

                this.codHistExt = this.os_VigSatMobWeb.buscarHT_NF(this.novoAgrupador.getCodHistExt(), this.novoAgrupador.getCodFil(), this.persistencia);
                this.codHistExtList = new ArrayList<>();
                this.codHistExtList.add(this.codHistExt);

                PrimeFaces.current().ajax().update("formAgrupador:cadastrar");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public List<FatISSGrp> listarFatISSGrp(String query) {
        try {
            this.fatISSGrpList = this.os_VigSatMobWeb.buscaFatISSGrp(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.fatISSGrpList;
    }

    public void selecionarFatISSGrp(SelectEvent event) {
        this.fatISSGrp = ((FatISSGrp) event.getObject());
        this.novoAgrupador.setAgrupISS(this.fatISSGrp.getCodigo());
    }

    public List<HT_NF> listarCodHist(String query) {
        try {
            this.codHistList = this.os_VigSatMobWeb.buscaHT_NF(query, this.os_vigSelecionado.getCodFil(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.codHistList;
    }

    public void selecionarCodHist(SelectEvent event) {
        this.codHist = ((HT_NF) event.getObject());
        this.novoAgrupador.setCodHist(this.codHist.getCodigo());
    }

    public List<HT_NF> listarCodHistExt(String query) {
        try {
            this.codHistExtList = this.os_VigSatMobWeb.buscaHT_NF(query, this.os_vigSelecionado.getCodFil(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.codHistExtList;
    }

    public void selecionarCodHistExt(SelectEvent event) {
        this.codHistExt = ((HT_NF) event.getObject());
        this.novoAgrupador.setCodHistExt(this.codHistExt.getCodigo());
    }

    public void preEdicaoItensContrato() {
        try {
            if (null == this.ctrItem) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneCtrItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.tipoPosto = gerarTipoPosto();
                this.tipoCalc = gerarTipoCal();

                PrimeFaces.current().resetInputs("formItensContrato:cadastrar");
                PrimeFaces.current().ajax().update("formItensContrato:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgItensContrato').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void ativarModalFrequenciaCadastro() {
        frequenciaSelecionada = null;
        preEdicaoFrequencia();
        PrimeFaces.current().resetInputs("formFrequencia:edicao");
        PrimeFaces.current().executeScript("PF('dlgFrequencia').show();");
    }

    public void ativarModalFrequenciaEdicao() {
        if (frequenciaSelecionada == null) {
            displayInfo("SelecioneFrequencia");
            return;
        }
        preEdicaoFrequencia();
        PrimeFaces.current().resetInputs("formFrequencia:edicao");
        PrimeFaces.current().executeScript("PF('dlgFrequencia').show();");
    }

    public void frequenciaExcluir() throws Exception {
        try {
            if (this.frequenciaSelecionada == null) {
                displayInfo("SelecioneFrequencia");
                return;
            }

            this.os_FreqDao.delete(this.frequenciaSelecionada);
            this.os_VFreqList = this.os_VigSatMobWeb.listarOS_VFreq(this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    private List deserializar(String serializado, int maxSize) {
        List list = new ArrayList<>();

        if (serializado == null) {
            return list;
        }

        int actualSize = Math.min(maxSize, serializado.length());
        for (int i = 0; i < actualSize; i++) {
            if (serializado.charAt(i) == '1') {
                list.add(Integer.toString(i + 1));
            }
        }

        return list;
    }

    private String serializar(List<String> lista, int size) {
        char[] repeat = new char[size];
        Arrays.fill(repeat, '0');
        StringBuilder serializado = new StringBuilder(new String(repeat));

        for (int i = 0; i < lista.size(); i++) {
            int dia = Integer.parseInt(lista.get(i));
            serializado.setCharAt(dia - 1, '1');
        }

        return serializado.toString();
    }

    public void adicionarItemFatura() throws Exception {
        this.os_vitem.setCodFil(this.codFil);
        this.os_vitem.setTipoPosto(this.ctrItensListSelecionado.getTipoPosto());
        this.os_vitem.setTipoPostoDesc(this.ctrItensListSelecionado.getDescricao());
        this.os_vitem.setTipoCalc(this.ctrItensListSelecionado.getTipoCalc());
        if (null == this.os_vitem.getValor()
                || this.os_vitem.getValor().equals("")) {
            this.os_vitem.setValor("0");
        }
        if (null == this.os_vitem.getQtde()
                || this.os_vitem.getQtde().equals("")) {
            this.os_vitem.setQtde("0");
        }
        this.os_vitem.setOperador(RecortaAteEspaço(this.operador, 0, 10).toUpperCase());
        this.os_vitem.setDt_Alter(getDataAtual("SQL"));
        this.os_vitem.setHr_Alter(getDataAtual("HORA"));

        OS_VITens objItem = new OS_VITens(this.os_vigSelecionado.getOS(),
                this.os_vitem.getCodFil(),
                this.os_vitem.getTipoPosto(),
                this.os_vitem.getDtInicio(),
                this.os_vitem.getDtFim(),
                this.os_vitem.getQtde(),
                this.os_vitem.getObs(),
                this.os_vitem.getMsgExtrato(),
                this.os_vitem.getValor(),
                this.os_vitem.getOperador(),
                this.os_vitem.getDt_Alter(),
                this.os_vitem.getHr_Alter(),
                this.os_vitem.getCHSeman(),
                this.os_vitem.getCHMensal(),
                this.os_vitem.getSalario(),
                this.os_vitem.getTipoPostoDesc(),
                this.os_vitem.getTipoCalc());

        if (!editandoOsItens) {
            this.daoVitens.Inserir(objItem, this.persistencia);
        } else {
            this.daoVitens.Atualizar(objItem, this.persistencia);
        }

        this.os_vitens = this.os_VigSatMobWeb.listarOS_VItens(this.os_vigSelecionado.getOS(), this.os_vigSelecionado.getCodFil(), this.persistencia);
        this.os_vitem = new OS_VITens();

        if (null != this.os_vigSelecionado.getDtFim()
                && !this.os_vigSelecionado.getDtFim().equals("")) {
            this.os_vitem.setDtFim(this.os_vigSelecionado.getDtFim());
        }

        if (null != this.os_vigSelecionado.getDtInicio()
                && !this.os_vigSelecionado.getDtInicio().equals("")) {
            this.os_vitem.setDtInicio(this.os_vigSelecionado.getDtInicio());
        }

        PrimeFaces.current().executeScript("PF('dlgCadastrarItensFaturamento').hide();");
    }

    public void onChangeDiasMes() {
        if (todosDiasMes) {
            for (int i = 0; i < DIAS_SIZE; i++) {
                frequenciaDiasSelecionados.add(Integer.toString(i + 1));
            }
        } else {
            frequenciaDiasSelecionados = new ArrayList<>();
        }
    }

    public void onChangeDiasDU() {
        if (todosDiasUteis) {
            for (int i = 0; i < DU_SIZE; i++) {
                frequenciaDUSelecionados.add(Integer.toString(i + 1));
            }
        } else {
            frequenciaDUSelecionados = new ArrayList<>();
        }
    }

    public void preEdicaoFrequencia() {
        if (frequenciaSelecionada == null) {
            editandoFrequencia = false;
            frequenciaEdicao = new OS_VFreq();
            frequenciaEdicao.setOS(os_vigSelecionado.getOS());
            frequenciaEdicao.setCodFil(os_vigSelecionado.getCodFil());
            frequenciaDiasSelecionados = deserializar(frequenciaEdicao.getDias(), DIAS_SIZE);
            frequenciaDUSelecionados = deserializar(frequenciaEdicao.getDU(), DU_SIZE);
            todosDiasMes = false;
            todosDiasUteis = false;
        } else {
            editandoFrequencia = true;
            frequenciaEdicao = new OS_VFreq(frequenciaSelecionada);
            frequenciaDiasSelecionados = deserializar(frequenciaEdicao.getDias(), DIAS_SIZE);
            frequenciaDUSelecionados = deserializar(frequenciaEdicao.getDU(), DU_SIZE);
            todosDiasMes = frequenciaEdicao.getDias().equals(String.join("", Collections.nCopies(DIAS_SIZE, "1")));
            todosDiasUteis = frequenciaEdicao.getDU().equals(String.join("", Collections.nCopies(DU_SIZE, "1")));
        }
    }

    public void inserirOS_VFreq() {
        try {
            os_VigSatMobWeb.insertOS_VFreq(frequenciaEdicao);
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void updateOS_VFreq() {
        try {
            os_VigSatMobWeb.updateOS_VFreq(frequenciaSelecionada, frequenciaEdicao);
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void gravarEntrada() {
        frequenciaEdicao.setDias(serializar(frequenciaDiasSelecionados, DIAS_SIZE));
        frequenciaEdicao.setDU(serializar(frequenciaDUSelecionados, DU_SIZE));
        frequenciaEdicao.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            sdf.setLenient(false);
            Date hora1 = sdf.parse(frequenciaEdicao.getHora1());
            Date hora2 = sdf.parse(frequenciaEdicao.getHora2());
            if (hora2.before(hora1)) {
                throw new Exception("TODO: hora 2 antes de hora1");
            }

            Format formatter = new SimpleDateFormat("HH:mm");
            String h1 = formatter.format(hora1);
            String h2 = formatter.format(hora2);
            frequenciaEdicao.setHora1(h1);
            frequenciaEdicao.setHora2(h2);

            if (frequenciaSelecionada == null) {
                inserirOS_VFreq();
                editandoFrequencia = true;
            } else {
                updateOS_VFreq();
            }

            frequenciaSelecionada = new OS_VFreq(frequenciaEdicao);
            os_VFreqList = os_VigSatMobWeb.listarOS_VFreq(os_vigSelecionado.getOS(), os_vigSelecionado.getCodFil(), persistencia);
            displayInfo("CadastroSucesso");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:tabelaFrequencia:tabelaFrequenciaServicos");
            PrimeFaces.current().ajax().update("formFrequencia:edicao");
            PrimeFaces.current().executeScript("PF('dlgFrequencia').hide();");

        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    private Map gerarTipoPosto() {
        Map map = new HashMap<>();
        map.put(getMessageS("Embarques"), "010");
        map.put(getMessageS("EmbarquesFranquia"), "011");
        map.put(getMessageS("EmbarquesFranquia"), "012");
        map.put(getMessageS("EmbNumerarioConcomitDiaria"), "013");
        map.put(getMessageS("EmbEnvelopeConcomitDiaria"), "014");
        map.put(getMessageS("EmbSuprimentoDifenciado"), "015");
        map.put(getMessageS("EmbRecolhimentoDifenciado"), "016");
        map.put(getMessageS("SuprimentoConcomitante"), "017");
        map.put(getMessageS("CargaDescarga"), "018");
        map.put(getMessageS("EmbarquesCobrancaEntrega"), "019");
        map.put(getMessageS("AdValorem"), "020");
        map.put(getMessageS("TempoEsperaMinutos"), "030");
        map.put(getMessageS("TempoEsperaHora"), "031");
        map.put(getMessageS("Envelopes"), "040");
        map.put(getMessageS("Bananinha"), "041");
        map.put(getMessageS("Sangria"), "042");
        map.put(getMessageS("KitTroco"), "046");
        map.put(getMessageS("ChequesUnitario"), "047");
        map.put(getMessageS("UnidadesProdServ"), "049");
        map.put(getMessageS("Milheiros"), "050");
        map.put(getMessageS("MilheirosBom"), "051");
        map.put(getMessageS("MilheirosDilacerado"), "052");
        map.put(getMessageS("MilheirosMoedas"), "053");
        map.put(getMessageS("MilheirosCheques"), "054");
        map.put(getMessageS("MilheirosCheques"), "055");
        map.put(getMessageS("MilheirosTicket"), "056");
        map.put(getMessageS("MilheirosCheques"), "057");
        map.put(getMessageS("MilheirosSuprimento"), "058");
        map.put(getMessageS("MilheirosSuprimentoMoedas"), "059");
        map.put(getMessageS("Custodia"), "060");
        map.put(getMessageS("CustodiaMoedas"), "061");
        map.put(getMessageS("CustodiaProdutosServi"), "062");
        map.put(getMessageS("CustodiaPermanenicaDi"), "063");
        map.put(getMessageS("CustodiaPassagem"), "064");
        map.put(getMessageS("KMRodados"), "070");
        map.put(getMessageS("KMTerra"), "072");
        map.put(getMessageS("FixoMensal"), "080");
        map.put(getMessageS("FixoMensalAdValorem"), "081");
        map.put(getMessageS("FixoMensalTesouraria"), "086");
        map.put(getMessageS("TempoCoberturaPagamento"), "090");
        map.put(getMessageS("Malotes"), "091");
        map.put(getMessageS("EntregaRecolhimentoMalotes"), "092");
        map.put(getMessageS("EnvelopeSangria"), "093");
        return map;
    }

    private Map gerarTipoCal() {
        Map map = new HashMap<>();
        map.put(getMessageS("PorMes"), "M");
        map.put(getMessageS("PorDia"), "D");
        map.put(getMessageS("PorHora"), "H");
        map.put(getMessageS("PorQtde"), "Q");
        map.put(getMessageS("IndiceSValor"), "I");
        return map;
    }

    public String getOperador() {
        return operador;
    }

    public boolean isMostraFiliais() {
        return mostraFiliais;
    }

    public void setMostraFiliais(boolean mostraFiliais) {
        this.mostraFiliais = mostraFiliais;
    }

    public boolean isLimpaFiltros() {
        return limpaFiltros;
    }

    public void setLimpaFiltros(boolean limpaFiltros) {
        this.limpaFiltros = limpaFiltros;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public boolean isMostraAtivos() {
        return mostraAtivos;
    }

    public void setMostraAtivos(boolean mostraAtivos) {
        this.mostraAtivos = mostraAtivos;
    }

    public boolean isMostraVencer() {
        return mostraVencer;
    }

    public void setMostraVencer(boolean mostraVencer) {
        this.mostraVencer = mostraVencer;
    }

    public OS_Vig getOs_vigSelecionado() {
        return os_vigSelecionado;
    }

    public void setOs_vigSelecionado(OS_Vig os_vigSelecionado) {
        this.os_vigSelecionado = os_vigSelecionado;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public List<Clientes> getCliFatList() {
        return cliFatList;
    }

    public void setCliFatList(List<Clientes> cliFatList) {
        this.cliFatList = cliFatList;
    }

    public Clientes getCliFat() {
        return cliFat;
    }

    public void setCliFat(Clientes cliFat) {
        this.cliFat = cliFat;
    }

    public ContrVig getContrato() {
        return contrato;
    }

    public void setContrato(ContrVig contrato) {
        this.contrato = contrato;
    }

    public List<ContrVig> getContratos() {
        return contratos;
    }

    public void setContratos(List<ContrVig> contratos) {
        this.contratos = contratos;
    }

    public Fat_Grp getAgrupador() {
        return agrupador;
    }

    public void setAgrupador(Fat_Grp agrupador) {
        this.agrupador = agrupador;
    }

    public Fat_Grp getAgrupador2() {
        return agrupador2;
    }

    public void setAgrupador2(Fat_Grp agrupador2) {
        this.agrupador2 = agrupador2;
    }

    public List<Fat_Grp> getAgrupadorList() {
        return agrupadorList;
    }

    public void setAgrupadorList(List<Fat_Grp> agrupadorList) {
        this.agrupadorList = agrupadorList;
    }

    public List<Fat_Grp> getAgrupador2List() {
        return agrupador2List;
    }

    public void setAgrupador2List(List<Fat_Grp> agrupador2List) {
        this.agrupador2List = agrupador2List;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public Clientes getCliDst() {
        return cliDst;
    }

    public void setCliDst(Clientes cliDst) {
        this.cliDst = cliDst;
    }

    public List<Clientes> getClientes() {
        return clientes;
    }

    public void setClientes(List<Clientes> clientes) {
        this.clientes = clientes;
    }

    public List<Clientes> getCliDstList() {
        return cliDstList;
    }

    public void setCliDstList(List<Clientes> cliDstList) {
        this.cliDstList = cliDstList;
    }

    public boolean isViaCxF() {
        return viaCxF;
    }

    public void setViaCxF(boolean viaCxF) {
        this.viaCxF = viaCxF;
    }

    public boolean isEntregaSab() {
        return entregaSab;
    }

    public void setEntregaSab(boolean entregaSab) {
        this.entregaSab = entregaSab;
    }

    public boolean isEntregaDom() {
        return entregaDom;
    }

    public void setEntregaDom(boolean entregaDom) {
        this.entregaDom = entregaDom;
    }

    public boolean isEntregaFer() {
        return entregaFer;
    }

    public void setEntregaFer(boolean entregaFer) {
        this.entregaFer = entregaFer;
    }

    public List<OS_VITens> getOs_vitens() {
        return os_vitens;
    }

    public void setOs_vitens(List<OS_VITens> os_vitens) {
        this.os_vitens = os_vitens;
    }

    public List<CtrItens> getCtrItensList() {
        return ctrItensList;
    }

    public void setCtrItensList(List<CtrItens> ctrItensList) {
        this.ctrItensList = ctrItensList;
    }

    public List<TipoSrvCli> getTipoSrvCliList() {
        return tipoSrvCliList;
    }

    public void setTipoSrvCliList(List<TipoSrvCli> tipoSrvCliList) {
        this.tipoSrvCliList = tipoSrvCliList;
    }

    public TipoSrvCli getTipoSrvCli() {
        return tipoSrvCli;
    }

    public void setTipoSrvCli(TipoSrvCli tipoSrvCli) {
        this.tipoSrvCli = tipoSrvCli;
    }

    public CtrItens getCtrItensListSelecionado() {
        return ctrItensListSelecionado;
    }

    public void setCtrItensListSelecionado(CtrItens ctrItensListSelecionado) {
        this.ctrItensListSelecionado = ctrItensListSelecionado;
    }

    public String getItemFatTipoCalc() {
        return itemFatTipoCalc;
    }

    public void setItemFatTipoCalc(String itemFatTipoCalc) {
        this.itemFatTipoCalc = itemFatTipoCalc;
    }

    public String getItemFatQtde() {
        return itemFatQtde;
    }

    public void setItemFatQtde(String itemFatQtde) {
        this.itemFatQtde = itemFatQtde;
    }

    public String getItemFatObservacao() {
        return itemFatObservacao;
    }

    public void setItemFatObservacao(String itemFatObservacao) {
        this.itemFatObservacao = itemFatObservacao;
    }

    public String getItemFatMensagem() {
        return itemFatMensagem;
    }

    public void setItemFatMensagem(String itemFatMensagem) {
        this.itemFatMensagem = itemFatMensagem;
    }

    public CtrItens getCtrItensListVazio() {
        return ctrItensListVazio;
    }

    public void setCtrItensListVazio(CtrItens ctrItensListVazio) {
        this.ctrItensListVazio = ctrItensListVazio;
    }

    public List<OS_VFreq> getOs_VFreqList() {
        return os_VFreqList;
    }

    public void setOs_VFreqList(List<OS_VFreq> os_VFreqList) {
        this.os_VFreqList = os_VFreqList;
    }

    public TipoSrvCli getNovoTipoSrvCli() {
        return novoTipoSrvCli;
    }

    public void setNovoTipoSrvCli(TipoSrvCli novoTipoSrvCli) {
        this.novoTipoSrvCli = novoTipoSrvCli;
    }

    public Map getTAtend() {
        return TAtend;
    }

    public Map getTCob() {
        return TCob;
    }

    public Map getTCar() {
        return TCar;
    }

    public Map getTipoSrv() {
        return TipoSrv;
    }

    public Map getTRota() {
        return TRota;
    }

    public Map getTexportar() {
        return Texportar;
    }

    public Bancos getBb() {
        return bb;
    }

    public void setBb(Bancos bb) {
        this.bb = bb;
    }

    public List<Bancos> getBancos() {
        return bancos;
    }

    public void setBancos(List<Bancos> bancos) {
        this.bancos = bancos;
    }

    public int getFlagTipoSrvCli() {
        return flagTipoSrvCli;
    }

    public void setFlagTipoSrvCli(int flagTipoSrvCli) {
        this.flagTipoSrvCli = flagTipoSrvCli;
    }

    public int getFlagAgrupadorNF() {
        return flagAgrupadorNF;
    }

    public void setFlagAgrupadorNF(int flagAgrupadorNF) {
        this.flagAgrupadorNF = flagAgrupadorNF;
    }

    public Fat_Grp getNovoAgrupador() {
        return novoAgrupador;
    }

    public void setNovoAgrupador(Fat_Grp novoAgrupador) {
        this.novoAgrupador = novoAgrupador;
    }

    public FatISSGrp getFatISSGrp() {
        return fatISSGrp;
    }

    public void setFatISSGrp(FatISSGrp fatISSGrp) {
        this.fatISSGrp = fatISSGrp;
    }

    public List<FatISSGrp> getFatISSGrpList() {
        return fatISSGrpList;
    }

    public void setFatISSGrpList(List<FatISSGrp> fatISSGrpList) {
        this.fatISSGrpList = fatISSGrpList;
    }

    public HT_NF getCodHist() {
        return codHist;
    }

    public void setCodHist(HT_NF codHist) {
        this.codHist = codHist;
    }

    public List<HT_NF> getCodHistList() {
        return codHistList;
    }

    public void setCodHistList(List<HT_NF> codHistList) {
        this.codHistList = codHistList;
    }

    public HT_NF getCodHistExt() {
        return codHistExt;
    }

    public void setCodHistExt(HT_NF codHistExt) {
        this.codHistExt = codHistExt;
    }

    public List<HT_NF> getCodHistExtList() {
        return codHistExtList;
    }

    public void setCodHistExtList(List<HT_NF> codHistExtList) {
        this.codHistExtList = codHistExtList;
    }

    public boolean isMostrarValores1() {
        return mostrarValores1;
    }

    public void setMostrarValores1(boolean mostrarValores1) {
        this.mostrarValores1 = mostrarValores1;
    }

    public boolean isMostrarValores2() {
        return mostrarValores2;
    }

    public void setMostrarValores2(boolean mostrarValores2) {
        this.mostrarValores2 = mostrarValores2;
    }

    public boolean isMostrarValores() {
        return mostrarValores;
    }

    public void setMostrarValores(boolean mostrarValores) {
        this.mostrarValores = mostrarValores;
    }

    public CtrItens getCtrItem() {
        return ctrItem;
    }

    public void setCtrItem(CtrItens ctrItem) {
        this.ctrItem = ctrItem;
    }

    public Map getTipoCalc() {
        return tipoCalc;
    }

    public void setTipoCalc(Map tipoCalc) {
        this.tipoCalc = tipoCalc;
    }

    public Map getTipoPosto() {
        return tipoPosto;
    }

    public void setTipoPosto(Map tipoPosto) {
        this.tipoPosto = tipoPosto;
    }

    public String getOpcaoPesquisa() {
        return opcaoPesquisa;
    }

    public void setOpcaoPesquisa(String opcaoPesquisa) {
        this.opcaoPesquisa = opcaoPesquisa;
    }

    public OS_VFreq getFrequenciaSelecionada() {
        return frequenciaSelecionada;
    }

    public void setFrequenciaSelecionada(OS_VFreq frequenciaSelecionada) {
        this.frequenciaSelecionada = frequenciaSelecionada;
    }

    public OS_VFreq getFrequenciaEdicao() {
        return frequenciaEdicao;
    }

    public void setFrequenciaEdicao(OS_VFreq frequenciaEdicao) {
        this.frequenciaEdicao = frequenciaEdicao;
    }

    public List<String> getFrequenciaDiasSelecionados() {
        return frequenciaDiasSelecionados;
    }

    public void setFrequenciaDiasSelecionados(List<String> frequenciaDiasSelecionados) {
        this.frequenciaDiasSelecionados = frequenciaDiasSelecionados;
    }

    public List<String> getFrequenciaDUSelecionados() {
        return frequenciaDUSelecionados;
    }

    public void setFrequenciaDUSelecionados(List<String> frequenciaDUSelecionados) {
        this.frequenciaDUSelecionados = frequenciaDUSelecionados;
    }

    public List<String> getFrequenciaDias() {
        return frequenciaDias;
    }

    public List<String> getFrequenciaDU() {
        return frequenciaDU;
    }

    public boolean isEditandoFrequencia() {
        return editandoFrequencia;
    }

    public boolean isTodosDiasUteis() {
        return todosDiasUteis;
    }

    public void setTodosDiasUteis(boolean todosDiasUteis) {
        this.todosDiasUteis = todosDiasUteis;
    }

    public boolean isTodosDiasMes() {
        return todosDiasMes;
    }

    public void setTodosDiasMes(boolean todosDiasMes) {
        this.todosDiasMes = todosDiasMes;
    }

    public OS_VITens getOs_vitem() {
        return os_vitem;
    }

    public void setOs_vitem(OS_VITens os_vitem) {
        this.os_vitem = os_vitem;
    }

    public OS_VITens getOs_itemSelecionado() {
        return os_itemSelecionado;
    }

    public void setOs_itemSelecionado(OS_VITens os_itemSelecionado) {
        this.os_itemSelecionado = os_itemSelecionado;
    }
}

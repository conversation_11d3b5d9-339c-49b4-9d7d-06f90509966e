/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class Rastrear {

    private BigDecimal Codigo;
    private BigDecimal CODCLI;
    private String ID_MODULO;
    private String Placa;
    private String Latitude;
    private String Longitude;
    private LocalDate Data;
    private String Hora;
    private String Velocidade;
    private String Direcao;
    private String Rua;
    private String Cidade;
    private String Integrador;
    private BigDecimal Enviado;
    private BigDecimal EnvioMaspasR;
    private BigDecimal EnvioCliente;
    private BigDecimal InsereBD;
    private LocalDate DtTrans;
    private String HrTrans;
    private String CodMem;
    private String Satelite;
    private String Gps;
    private String Movimento;
    private String Violacao;
    private String StatusPorta;
    private String Precisao;
    private BigDecimal Matr;
    private BigDecimal Batida;

    private String codFil;
    private String nome;
    private String rota;
    private String SeqRota;
    private String veiculo;
    private String modeloVeiculo;

    private String nomeMotorista;

    private String hora1;
    private String hora2;
    private String hora3;
    private String hora4;

    private String recOk;
    private String recPd;
    private String entOk;
    private String entPd;

    private String recGuias;
    private String recLacres;
    private String recValor;
    private String recPdGuias;
    private String recPdLacres;
    private String recPdValor;
    private String entGuias;
    private String entLacres;
    private String entValor;
    private String entPdGuias;
    private String entPdLacres;
    private String entPdValor;

    private String cxfEntValor;
    private String vlrEntDir;

    private String atendimento;

    private String servAtrasados;
    private String trajetoDiaPontoAntHora;
    private String trajetoDiaPontoAntLat;
    private String trajetoDiaPontoAntLon;
    private String trajetoDiaPontoUltComHora;
    private String trajetoDiaPontoUltComLat;
    private String trajetoDiaPontoUltComLon;
    private String trajetoDiaPontoProxHora;
    private String trajetoDiaPontoProxLat;
    private String trajetoDiaPontoProxLon;
    
    private String srvEfetivos; 
    private String srvAdiantados;
    private String srvAtrasados;
    private String srvPendentes;

    public String getServAtrasados() {
        return servAtrasados;
    }

    public void setServAtrasados(String servAtrasados) {
        this.servAtrasados = servAtrasados;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public String getCxfEntValor() {
        return cxfEntValor;
    }

    public void setCxfEntValor(String cxfEntValor) {
        this.cxfEntValor = cxfEntValor;
    }

    public String getVlrEntDir() {
        return vlrEntDir;
    }

    public void setVlrEntDir(String vlrEntDir) {
        this.vlrEntDir = vlrEntDir;
    }

    public String getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(String atendimento) {
        this.atendimento = atendimento;
    }

    public String getNomeMotorista() {
        return nomeMotorista;
    }

    public void setNomeMotorista(String nomeMotorista) {
        this.nomeMotorista = nomeMotorista;
    }

    public String getModeloVeiculo() {
        return modeloVeiculo;
    }

    public void setModeloVeiculo(String modeloVeiculo) {
        this.modeloVeiculo = modeloVeiculo;
    }

    public String getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(String veiculo) {
        this.veiculo = veiculo;
    }

    /**
     * @return the Codigo
     */
    public BigDecimal getCodigo() {
        return Codigo;
    }

    /**
     * @param Codigo the Codigo to set
     */
    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    /**
     * @return the CODCLI
     */
    public BigDecimal getCODCLI() {
        return CODCLI;
    }

    /**
     * @param CODCLI the CODCLI to set
     */
    public void setCODCLI(String CODCLI) {
        try {
            this.CODCLI = new BigDecimal(CODCLI);
        } catch (Exception e) {
            this.CODCLI = new BigDecimal("0");
        }
    }

    /**
     * @return the ID_MODULO
     */
    public String getID_MODULO() {
        return ID_MODULO;
    }

    /**
     * @param ID_MODULO the ID_MODULO to set
     */
    public void setID_MODULO(String ID_MODULO) {
        this.ID_MODULO = ID_MODULO;
    }

    /**
     * @return the Placa
     */
    public String getPlaca() {
        return Placa;
    }

    /**
     * @param Placa the Placa to set
     */
    public void setPlaca(String Placa) {
        this.Placa = Placa;
    }

    /**
     * @return the Latitude
     */
    public String getLatitude() {
        return Latitude;
    }

    /**
     * @param Latitude the Latitude to set
     */
    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    /**
     * @return the Longitude
     */
    public String getLongitude() {
        return Longitude;
    }

    /**
     * @param Longitude the Longitude to set
     */
    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    /**
     * @return the Data
     */
    public LocalDate getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    /**
     * @return the Hora
     */
    public String getHora() {
        return Hora;
    }

    /**
     * @param Hora the Hora to set
     */
    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    /**
     * @return the Velocidade
     */
    public String getVelocidade() {
        return Velocidade;
    }

    /**
     * @param Velocidade the Velocidade to set
     */
    public void setVelocidade(String Velocidade) {
        this.Velocidade = Velocidade;
    }

    /**
     * @return the Direcao
     */
    public String getDirecao() {
        return Direcao;
    }

    /**
     * @param Direcao the Direcao to set
     */
    public void setDirecao(String Direcao) {
        this.Direcao = Direcao;
    }

    /**
     * @return the Rua
     */
    public String getRua() {
        return Rua;
    }

    /**
     * @param Rua the Rua to set
     */
    public void setRua(String Rua) {
        this.Rua = Rua;
    }

    /**
     * @return the Cidade
     */
    public String getCidade() {
        return Cidade;
    }

    /**
     * @param Cidade the Cidade to set
     */
    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    /**
     * @return the Integrador
     */
    public String getIntegrador() {
        return Integrador;
    }

    /**
     * @param Integrador the Integrador to set
     */
    public void setIntegrador(String Integrador) {
        this.Integrador = Integrador;
    }

    /**
     * @return the Enviado
     */
    public BigDecimal getEnviado() {
        return Enviado;
    }

    /**
     * @param Enviado the Enviado to set
     */
    public void setEnviado(String Enviado) {
        try {
            this.Enviado = new BigDecimal(Enviado);
        } catch (Exception e) {
            this.Enviado = new BigDecimal("0");
        }
    }

    /**
     * @return the EnvioMaspasR
     */
    public BigDecimal getEnvioMaspasR() {
        return EnvioMaspasR;
    }

    /**
     * @param EnvioMaspasR the EnvioMaspasR to set
     */
    public void setEnvioMaspasR(String EnvioMaspasR) {
        try {
            this.EnvioMaspasR = new BigDecimal(EnvioMaspasR);
        } catch (Exception e) {
            this.EnvioMaspasR = new BigDecimal("0");
        }
    }

    /**
     * @return the EnvioCliente
     */
    public BigDecimal getEnvioCliente() {
        return EnvioCliente;
    }

    /**
     * @param EnvioCliente the EnvioCliente to set
     */
    public void setEnvioCliente(String EnvioCliente) {
        try {
            this.EnvioCliente = new BigDecimal(EnvioCliente);
        } catch (Exception e) {
            this.EnvioCliente = new BigDecimal("0");
        }
    }

    /**
     * @return the InsereBD
     */
    public BigDecimal getInsereBD() {
        return InsereBD;
    }

    /**
     * @param InsereBD the InsereBD to set
     */
    public void setInsereBD(String InsereBD) {
        try {
            this.InsereBD = new BigDecimal(InsereBD);
        } catch (Exception e) {
            this.InsereBD = new BigDecimal("0");
        }
    }

    /**
     * @return the DtTrans
     */
    public LocalDate getDtTrans() {
        return DtTrans;
    }

    /**
     * @param DtTrans the DtTrans to set
     */
    public void setDtTrans(LocalDate DtTrans) {
        try {
            this.DtTrans = DtTrans;
        } catch (Exception e) {
            this.DtTrans = null;
        }
    }

    /**
     * @return the HrTrans
     */
    public String getHrTrans() {
        return HrTrans;
    }

    /**
     * @param HrTrans the HrTrans to set
     */
    public void setHrTrans(String HrTrans) {
        this.HrTrans = HrTrans;
    }

    /**
     * @return the CodMem
     */
    public String getCodMem() {
        return CodMem;
    }

    /**
     * @param CodMem the CodMem to set
     */
    public void setCodMem(String CodMem) {
        this.CodMem = CodMem;
    }

    /**
     * @return the Satelite
     */
    public String getSatelite() {
        return Satelite;
    }

    /**
     * @param Satelite the Satelite to set
     */
    public void setSatelite(String Satelite) {
        this.Satelite = Satelite;
    }

    /**
     * @return the Gps
     */
    public String getGps() {
        return Gps;
    }

    /**
     * @param Gps the Gps to set
     */
    public void setGps(String Gps) {
        this.Gps = Gps;
    }

    /**
     * @return the Movimento
     */
    public String getMovimento() {
        return Movimento;
    }

    /**
     * @param Movimento the Movimento to set
     */
    public void setMovimento(String Movimento) {
        this.Movimento = Movimento;
    }

    /**
     * @return the Violacao
     */
    public String getViolacao() {
        return Violacao;
    }

    /**
     * @param Violacao the Violacao to set
     */
    public void setViolacao(String Violacao) {
        this.Violacao = Violacao;
    }

    /**
     * @return the StatusPorta
     */
    public String getStatusPorta() {
        return StatusPorta;
    }

    /**
     * @param StatusPorta the StatusPorta to set
     */
    public void setStatusPorta(String StatusPorta) {
        this.StatusPorta = StatusPorta;
    }

    /**
     * @return the Precisao
     */
    public String getPrecisao() {
        return Precisao;
    }

    /**
     * @param Precisao the Precisao to set
     */
    public void setPrecisao(String Precisao) {
        this.Precisao = Precisao;
    }

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the Batida
     */
    public BigDecimal getBatida() {
        return Batida;
    }

    /**
     * @param Batida the Batida to set
     */
    public void setBatida(String Batida) {
        try {
            this.Batida = new BigDecimal(Batida);
        } catch (Exception e) {
            this.Batida = new BigDecimal("0");
        }
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getRota() {
        return rota;
    }

    public void setRota(String rota) {
        this.rota = rota;
    }

    public String getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getHora2() {
        return hora2;
    }

    public void setHora2(String hora2) {
        this.hora2 = hora2;
    }

    public String getHora3() {
        return hora3;
    }

    public void setHora3(String hora3) {
        this.hora3 = hora3;
    }

    public String getHora4() {
        return hora4;
    }

    public void setHora4(String hora4) {
        this.hora4 = hora4;
    }

    public String getRecGuias() {
        return recGuias;
    }

    public void setRecGuias(String recGuias) {
        this.recGuias = recGuias;
    }

    public String getRecLacres() {
        return recLacres;
    }

    public void setRecLacres(String recLacres) {
        this.recLacres = recLacres;
    }

    public String getRecValor() {
        return recValor;
    }

    public void setRecValor(String recValor) {
        this.recValor = recValor;
    }

    public String getRecPdGuias() {
        return recPdGuias;
    }

    public void setRecPdGuias(String recPdGuias) {
        this.recPdGuias = recPdGuias;
    }

    public String getRecPdLacres() {
        return recPdLacres;
    }

    public void setRecPdLacres(String recPdLacres) {
        this.recPdLacres = recPdLacres;
    }

    public String getRecPdValor() {
        return recPdValor;
    }

    public void setRecPdValor(String recPdValor) {
        this.recPdValor = recPdValor;
    }

    public String getEntGuias() {
        return entGuias;
    }

    public void setEntGuias(String entGuias) {
        this.entGuias = entGuias;
    }

    public String getEntLacres() {
        return entLacres;
    }

    public void setEntLacres(String entLacres) {
        this.entLacres = entLacres;
    }

    public String getEntValor() {
        return entValor;
    }

    public void setEntValor(String entValor) {
        this.entValor = entValor;
    }

    public String getEntPdGuias() {
        return entPdGuias;
    }

    public void setEntPdGuias(String entPdGuias) {
        this.entPdGuias = entPdGuias;
    }

    public String getEntPdLacres() {
        return entPdLacres;
    }

    public void setEntPdLacres(String entPdLacres) {
        this.entPdLacres = entPdLacres;
    }

    public String getEntPdValor() {
        return entPdValor;
    }

    public void setEntPdValor(String entPdValor) {
        this.entPdValor = entPdValor;
    }

    public String getRecOk() {
        return recOk;
    }

    public void setRecOk(String recOk) {
        this.recOk = recOk;
    }

    public String getRecPd() {
        return recPd;
    }

    public void setRecPd(String recPd) {
        this.recPd = recPd;
    }

    public String getEntOk() {
        return entOk;
    }

    public void setEntOk(String entOk) {
        this.entOk = entOk;
    }

    public String getEntPd() {
        return entPd;
    }

    public void setEntPd(String entPd) {
        this.entPd = entPd;
    }

    @Override
    public String toString() {
        return Hora;
    }

    public String getTrajetoDiaPontoAntHora() {
        return trajetoDiaPontoAntHora;
    }

    public void setTrajetoDiaPontoAntHora(String trajetoDiaPontoAntHora) {
        this.trajetoDiaPontoAntHora = trajetoDiaPontoAntHora;
    }

    public String getTrajetoDiaPontoAntLat() {
        return trajetoDiaPontoAntLat;
    }

    public void setTrajetoDiaPontoAntLat(String trajetoDiaPontoAntLat) {
        this.trajetoDiaPontoAntLat = trajetoDiaPontoAntLat;
    }

    public String getTrajetoDiaPontoAntLon() {
        return trajetoDiaPontoAntLon;
    }

    public void setTrajetoDiaPontoAntLon(String trajetoDiaPontoAntLon) {
        this.trajetoDiaPontoAntLon = trajetoDiaPontoAntLon;
    }

    public String getTrajetoDiaPontoUltComHora() {
        return trajetoDiaPontoUltComHora;
    }

    public void setTrajetoDiaPontoUltComHora(String trajetoDiaPontoUltComHora) {
        this.trajetoDiaPontoUltComHora = trajetoDiaPontoUltComHora;
    }

    public String getTrajetoDiaPontoUltComLat() {
        return trajetoDiaPontoUltComLat;
    }

    public void setTrajetoDiaPontoUltComLat(String trajetoDiaPontoUltComLat) {
        this.trajetoDiaPontoUltComLat = trajetoDiaPontoUltComLat;
    }

    public String getTrajetoDiaPontoUltComLon() {
        return trajetoDiaPontoUltComLon;
    }

    public void setTrajetoDiaPontoUltComLon(String trajetoDiaPontoUltComLon) {
        this.trajetoDiaPontoUltComLon = trajetoDiaPontoUltComLon;
    }

    public String getTrajetoDiaPontoProxHora() {
        return trajetoDiaPontoProxHora;
    }

    public void setTrajetoDiaPontoProxHora(String trajetoDiaPontoProxHora) {
        this.trajetoDiaPontoProxHora = trajetoDiaPontoProxHora;
    }

    public String getTrajetoDiaPontoProxLat() {
        return trajetoDiaPontoProxLat;
    }

    public void setTrajetoDiaPontoProxLat(String trajetoDiaPontoProxLat) {
        this.trajetoDiaPontoProxLat = trajetoDiaPontoProxLat;
    }

    public String getTrajetoDiaPontoProxLon() {
        return trajetoDiaPontoProxLon;
    }

    public void setTrajetoDiaPontoProxLon(String trajetoDiaPontoProxLon) {
        this.trajetoDiaPontoProxLon = trajetoDiaPontoProxLon;
    }
    
    public String getSrvEfetivos() {
        return srvEfetivos;
    }

    public void setSrvEfetivos(String srvEfetivos) {
        this.srvEfetivos = srvEfetivos;
    }

    public String getSrvAdiantados() {
        return srvAdiantados;
    }

    public void setSrvAdiantados(String srvAdiantados) {
        this.srvAdiantados = srvAdiantados;
    }

    public String getSrvAtrasados() {
        return srvAtrasados;
    }

    public void setSrvAtrasados(String srvAtrasados) {
        this.srvAtrasados = srvAtrasados;
    }

    public String getSrvPendentes() {
        return srvPendentes;
    }

    public void setSrvPendentes(String srvPendentes) {
        this.srvPendentes = srvPendentes;
    }    
}

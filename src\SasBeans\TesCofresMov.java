/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TesCofresMov {

    private BigDecimal id;
    private BigDecimal CodCofre;
    private LocalDate Data;
    private String Hora;
    private String CodCliente;
    private String idUsuario;
    private String NomeUsuario;
    private BigDecimal ValorDeposito;
    private String TipoMoeda;
    private String TipoDeposito;
    private String CodigoBarras;
    private String Status;
    private String Operador;
    private LocalDate Dt_Incl;
    private String Hr_Incl;

    private String NRedCofre;
    private String Cidade;
    private String UF;
    private String Envelope;
    private String ValorUltColeta;
    private String SaldoFisCst;
    private String CredProxDU;
    private String Detalhes;
    
    private String DataUltimaComunicacao;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public void setId(String id) {
        try {
            this.id = new BigDecimal(id);
        } catch (Exception e) {
            this.id = new BigDecimal("-1");
        }
    }

    public BigDecimal getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(BigDecimal CodCofre) {
        this.CodCofre = CodCofre;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getCodCliente() {
        return CodCliente;
    }

    public void setCodCliente(String CodCliente) {
        this.CodCliente = CodCliente;
    }

    public String getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(String idUsuario) {
        this.idUsuario = idUsuario;
    }

    public String getNomeUsuario() {
        return NomeUsuario;
    }

    public void setNomeUsuario(String NomeUsuario) {
        this.NomeUsuario = NomeUsuario;
    }

    public BigDecimal getValorDeposito() {
        return ValorDeposito;
    }

    public void setValorDeposito(BigDecimal ValorDeposito) {
        this.ValorDeposito = ValorDeposito;
    }

    public String getTipoMoeda() {
        return TipoMoeda;
    }

    public void setTipoMoeda(String TipoMoeda) {
        this.TipoMoeda = TipoMoeda;
    }

    public String getTipoDeposito() {
        return TipoDeposito;
    }

    public void setTipoDeposito(String TipoDeposito) {
        this.TipoDeposito = TipoDeposito;
    }

    public String getCodigoBarras() {
        return CodigoBarras;
    }

    public void setCodigoBarras(String CodigoBarras) {
        this.CodigoBarras = CodigoBarras;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getNRedCofre() {
        return NRedCofre;
    }

    public void setNRedCofre(String NRedCofre) {
        this.NRedCofre = NRedCofre;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getEnvelope() {
        return Envelope;
    }

    public void setEnvelope(String Envelope) {
        this.Envelope = Envelope;
    }

    public String getValorUltColeta() {
        return ValorUltColeta;
    }

    public void setValorUltColeta(String ValorUltColeta) {
        this.ValorUltColeta = ValorUltColeta;
    }

    public String getSaldoFisCst() {
        return SaldoFisCst;
    }

    public void setSaldoFisCst(String SaldoFisCst) {
        this.SaldoFisCst = SaldoFisCst;
    }

    public String getCredProxDU() {
        return CredProxDU;
    }

    public void setCredProxDU(String CredProxDU) {
        this.CredProxDU = CredProxDU;
    }

    public String getDetalhes() {
        return Detalhes;
    }

    public void setDetalhes(String Detalhes) {
        this.Detalhes = Detalhes;
    }

    public String getDataUltimaComunicacao() {
        return DataUltimaComunicacao;
    }

    public void setDataUltimaComunicacao(String DataUltimaComunicacao) {
        this.DataUltimaComunicacao = DataUltimaComunicacao;
    }

    @Override
    public String toString() {
        return "TesCofresMov{" + "id=" + id + ", CodCofre=" + CodCofre + ", Data=" + Data + ", Hora=" + Hora + ", CodCliente=" + CodCliente + ", idUsuario=" + idUsuario + ", NomeUsuario=" + NomeUsuario + ", ValorDeposito=" + ValorDeposito + ", TipoMoeda=" + TipoMoeda + ", TipoDeposito=" + TipoDeposito + ", CodigoBarras=" + CodigoBarras + ", Status=" + Status + ", Operador=" + Operador + ", Dt_Incl=" + Dt_Incl + ", Hr_Incl=" + Hr_Incl + '}';
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.CodigoBarras);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TesCofresMov other = (TesCofresMov) obj;
        if (!Objects.equals(this.CodigoBarras, other.CodigoBarras)) {
            return false;
        }
        return true;
    }
}

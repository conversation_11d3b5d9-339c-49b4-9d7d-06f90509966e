<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <h:form id="tabelaFrequencia">
        <div class="col-md-11 col-sm-11 col-xs-11" style="width:calc(100% - 62px) !important; padding:0px !important; margin:0px !important; height:195px !important; overflow:auto;">
        <p:dataTable
            id="tabelaFrequenciaServicos"
            value="#{os_vig.os_VFreqList}"
            var="os_vfreq"
            rowKey="#{os_vfreq.OS}-#{os_vfreq.codFil}-#{os_vfreq.diaSem}-#{os_vfreq.hora1}"
            selection="#{os_vig.frequenciaSelecionada}"
            selectionMode="single"
            styleClass="tabela"
            class="DataGrid"
            resizableColumns="true"
            reflow="true"
            style="padding:0px !important; margin-top:0px !important; margin-left:0px !important; max-height:188px !important"
            emptyMessage="#{localemsgs.SemRegistros}">

            <p:ajax
                event="rowDblselect"
                listener="#{os_vig.ativarModalFrequenciaEdicao}"
                update="msgs formFrequencia:edicao formCadastrar:tabs:tabelaFrequencia:tabelaFrequenciaServicos"/>

            <p:column headerText="#{localemsgs.Dia}" class="text-center">
                <h:outputText value="#{os_vfreq.diaSem}" title="#{os_vfreq.diaSem}" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.DiaSem}" class="text-center">
                <h:outputText value="#{os_vfreq.diaSem}" title="#{os_vfreq.diaSem}" converter="conversorDiaSemana" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.Hora1}" class="text-center">
                <h:outputText value="#{os_vfreq.hora1}" title="#{os_vfreq.hora1}" converter="conversorHora" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                <h:outputText value="#{os_vfreq.tipo}" title="#{os_vfreq.tipo}" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.Hora2}" class="text-center">
                <h:outputText value="#{os_vfreq.hora2}" title="#{os_vfreq.hora2}" converter="conversorHora" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.RotaC}" class="text-center">
                <h:outputText value="#{os_vfreq.rotaC}" title="#{os_vfreq.rotaC}" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.Operador}" class="text-center">
                <h:outputText value="#{os_vfreq.operador}" title="#{os_vfreq.operador}" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                <h:outputText value="#{os_vfreq.dt_Alter}" title="#{os_vfreq.dt_Alter}" converter="conversorData" class="text-center"/>
            </p:column>
            <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                <h:outputText value="#{os_vfreq.hr_Alter}" title="#{os_vfreq.hr_Alter}" converter="conversorHora" class="text-center"/>
            </p:column>
        </p:dataTable>
        </div>
        <div class="col-md-1 col-sm-1 col-xs-1" style="width: 58px; margin-top: 8px; padding:0px !important; margin:0px !important; height:190px; margin-left:4px !important; border:thin solid #DDD;background-color:#EEE;">
            <p:panel style="width:100% !important; width:100%; text-align:center; margin-top:10px !important; background: transparent">
                <p:commandLink title="#{localemsgs.Adicionar}"
                               actionListener="#{os_vig.ativarModalFrequenciaCadastro()}"
                               process="@form"
                               update="msgs formFrequencia:edicao formCadastrar:tabs:tabelaFrequencia:tabelaFrequenciaServicos">
                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40" style="margin-top:20px; margin-bottom:2px !important"/>
                </p:commandLink>

                <p:commandLink title="#{localemsgs.Editar}"
                               actionListener="#{os_vig.ativarModalFrequenciaEdicao}"
                               process="@form"
                               update="msgs formFrequencia:edicao formCadastrar:tabs:tabelaFrequencia:tabelaFrequenciaServicos">
                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40" style="margin-bottom:2px" />
                </p:commandLink>
                <p:commandLink actionListener="#{os_vig.frequenciaExcluir}"
                               process="@form"
                                    update="msgs tabelaFrequenciaServicos"
                                title="#{localemsgs.Excluir}">
                     <p:graphicImage url="../assets/img/icone_redondo_excluir.png" width="40" height="40" />
                 </p:commandLink>
            </p:panel>
        </div>
    </h:form>
</ui:composition>
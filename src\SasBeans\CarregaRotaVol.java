/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CarregaRotaVol {

    private String oriInf;
    private String PreOrder;

    private String cxforte;

    private Rotas rota;
    private Rt_Perc rt_perc;
    private Rt_PercSla rt_percsla;
    private Pessoa pessoa;
    private Funcion funcion;
    private Escala escala;
    private Clientes CliOri;
    private Clientes CliDst;
    private Clientes CliFat;
    private Filiais filiais;
    private OS_Vig OS;
    private List<GuiasList> guias;
    private List<CxFGuiasVol> containers;

    public String getOriInf() {
        return oriInf;
    }

    public void setOriInf(String oriInf) {
        this.oriInf = oriInf;
    }

    public String getPreOrder() {
        return PreOrder;
    }

    public void setPreOrder(String PreOrder) {
        this.PreOrder = PreOrder;
    }

    public String getCxforte() {
        return cxforte;
    }

    public void setCxforte(String cxforte) {
        this.cxforte = cxforte;
    }

    public Rotas getRota() {
        return rota;
    }

    public void setRota(Rotas rota) {
        this.rota = rota;
    }

    public Rt_Perc getRt_perc() {
        return rt_perc;
    }

    public void setRt_perc(Rt_Perc rt_perc) {
        this.rt_perc = rt_perc;
    }

    public Rt_PercSla getRt_percsla() {
        return rt_percsla;
    }

    public void setRt_percsla(Rt_PercSla rt_percsla) {
        this.rt_percsla = rt_percsla;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public Escala getEscala() {
        return escala;
    }

    public void setEscala(Escala escala) {
        this.escala = escala;
    }

    public Clientes getCliOri() {
        return CliOri;
    }

    public void setCliOri(Clientes CliOri) {
        this.CliOri = CliOri;
    }

    public Clientes getCliDst() {
        return CliDst;
    }

    public void setCliDst(Clientes CliDst) {
        this.CliDst = CliDst;
    }

    public Clientes getCliFat() {
        return CliFat;
    }

    public void setCliFat(Clientes CliFat) {
        this.CliFat = CliFat;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public OS_Vig getOS() {
        return OS;
    }

    public void setOS(OS_Vig OS) {
        this.OS = OS;
    }

    public List<GuiasList> getGuias() {
        return guias;
    }

    public void setGuias(List<GuiasList> guias) {
        this.guias = guias;
    }

    public List<CxFGuiasVol> getContainers() {
        return containers;
    }

    public void setContainers(List<CxFGuiasVol> containers) {
        this.containers = containers;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.rt_perc);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CarregaRotaVol other = (CarregaRotaVol) obj;
        if (!Objects.equals(this.rt_perc, other.rt_perc)) {
            return false;
        }
        return true;
    }
}

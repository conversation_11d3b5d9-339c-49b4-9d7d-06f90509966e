package SasBeans;

/**
 * <AUTHOR>
 */
public class ClientesGuia {

    private int parada;

    private String er;
    private String hrChegada;
    private String hrSaida;
    private String nredOrigem;
    private String enderecoOrigem;
    private String bairroOrigem;
    private String cidadeOrigem;
    private String estadoOrigem;
    private String nredDestino;
    private String emailDestino;
    private String emailOrigem;
    private String enderecoDestino;
    private String bairroDestino;
    private String cidadeDestino;
    private String estadoDestino;
    private String codigoDestino;

    public ClientesGuia() {
        parada = 0;

        er = "";
        hrChegada = "";
        hrSaida = "";
        nredOrigem = "";
        enderecoOrigem = "";
        bairroOrigem = "";
        cidadeDestino = "";
        cidadeOrigem = "";
        estadoOrigem = "";
        nredDestino = "";
        enderecoDestino = "";
        bairroDestino = "";
        estadoDestino = "";
    }

    public String getEr() {
        return er;
    }

    public void setEr(String er) {
        this.er = er;
    }

    public int getParada() {
        return parada;
    }

    public void setParada(int parada) {
        this.parada = parada;
    }

    public String getHrChegada() {
        return hrChegada;
    }

    public void setHrChegada(String hrChegada) {
        this.hrChegada = hrChegada;
    }

    public String getHrSaida() {
        return hrSaida;
    }

    public void setHrSaida(String hrSaida) {
        this.hrSaida = hrSaida;
    }

    public String getNredOrigem() {
        return nredOrigem;
    }

    public void setNredOrigem(String nredOrigem) {
        this.nredOrigem = nredOrigem;
    }

    public String getEnderecoOrigem() {
        return enderecoOrigem;
    }

    public void setEnderecoOrigem(String enderecoOrigem) {
        this.enderecoOrigem = enderecoOrigem;
    }

    public String getBairroOrigem() {
        return bairroOrigem;
    }

    public void setBairroOrigem(String bairroOrigem) {
        this.bairroOrigem = bairroOrigem;
    }

    public String getCidadeOrigem() {
        return cidadeOrigem;
    }

    public void setCidadeOrigem(String cidadeOrigem) {
        this.cidadeOrigem = cidadeOrigem;
    }

    public String getEstadoOrigem() {
        return estadoOrigem;
    }

    public void setEstadoOrigem(String estadoOrigem) {
        this.estadoOrigem = estadoOrigem;
    }

    public String getNredDestino() {
        return nredDestino;
    }

    public void setNredDestino(String nredDestino) {
        this.nredDestino = nredDestino;
    }

    public String getEnderecoDestino() {
        return enderecoDestino;
    }

    public void setEnderecoDestino(String enderecoDestino) {
        this.enderecoDestino = enderecoDestino;
    }

    public String getBairroDestino() {
        return bairroDestino;
    }

    public void setBairroDestino(String bairroDestino) {
        this.bairroDestino = bairroDestino;
    }

    public String getCidadeDestino() {
        return cidadeDestino;
    }

    public void setCidadeDestino(String cidadeDestino) {
        this.cidadeDestino = cidadeDestino;
    }

    public String getEstadoDestino() {
        return estadoDestino;
    }

    public void setEstadoDestino(String estadoDestino) {
        this.estadoDestino = estadoDestino;
    }

    public String getEmailDestino() {
        return emailDestino;
    }

    public void setEmailDestino(String emailDestino) {
        this.emailDestino = emailDestino;
    }

    public String getEmailOrigem() {
        return emailOrigem;
    }

    public void setEmailOrigem(String emailOrigem) {
        this.emailOrigem = emailOrigem;
    }

    public String getCodigoDestino() {
        return codigoDestino;
    }

    public void setCodigoDestino(String codigoDestino) {
        this.codigoDestino = codigoDestino;
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FatTVGuias {

    private BigDecimal CodFil;
    private BigDecimal Guia;
    private String Serie;
    private String CliFat;
    private String Agencia;
    private LocalDate Data;
    private String HrCheg;
    private String HrSaida;
    private String Concomit;
    private BigDecimal OS;
    private String TipoSrv;
    private String TipoSrvSat;
    private BigDecimal Valor;
    private Integer Embarques;
    private Integer TempoEspera;
    private BigDecimal Malotes;
    private BigDecimal Milheiros;
    private BigDecimal Envelopes;
    private BigDecimal Sangrias;
    private BigDecimal Tickets;
    private Integer DiasCustodia;
    private BigDecimal KmTotal;
    private BigDecimal KmConj;
    private BigDecimal KmTerra;
    private BigDecimal Sequencia;
    private Integer Parada;
    private String PedidoCli;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getCliFat() {
        return CliFat;
    }

    public void setCliFat(String CliFat) {
        this.CliFat = CliFat;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public String getHrCheg() {
        return HrCheg;
    }

    public void setHrCheg(String HrCheg) {
        this.HrCheg = HrCheg;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public String getConcomit() {
        return Concomit;
    }

    public void setConcomit(String Concomit) {
        this.Concomit = Concomit;
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    public String getTipoSrvSat() {
        return TipoSrvSat;
    }

    public void setTipoSrvSat(String TipoSrvSat) {
        this.TipoSrvSat = TipoSrvSat;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public Integer getEmbarques() {
        return Embarques;
    }

    public void setEmbarques(Integer Embarques) {
        this.Embarques = Embarques;
    }

    public Integer getTempoEspera() {
        return TempoEspera;
    }

    public void setTempoEspera(Integer TempoEspera) {
        this.TempoEspera = TempoEspera;
    }

    public BigDecimal getMalotes() {
        return Malotes;
    }

    public void setMalotes(String Malotes) {
        try {
            this.Malotes = new BigDecimal(Malotes);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getMilheiros() {
        return Milheiros;
    }

    public void setMilheiros(String Milheiros) {
        try {
            this.Milheiros = new BigDecimal(Milheiros);
        } catch (Exception e) {
            this.Milheiros = new BigDecimal("0");
        }
    }

    public BigDecimal getEnvelopes() {
        return Envelopes;
    }

    public void setEnvelopes(String Envelopes) {
        try {
            this.Envelopes = new BigDecimal(Envelopes);
        } catch (Exception e) {
            this.Envelopes = new BigDecimal("0");
        }
    }

    public BigDecimal getSangrias() {
        return Sangrias;
    }

    public void setSangrias(String Sangrias) {
        try {
            this.Sangrias = new BigDecimal(Sangrias);
        } catch (Exception e) {
            this.Sangrias = new BigDecimal("0");
        }
    }

    public BigDecimal getTickets() {
        return Tickets;
    }

    public void setTickets(String Tickets) {
        try {
            this.Tickets = new BigDecimal(Tickets);
        } catch (Exception e) {
            this.Tickets = new BigDecimal("0");
        }
    }

    public Integer getDiasCustodia() {
        return DiasCustodia;
    }

    public void setDiasCustodia(Integer DiasCustodia) {
        this.DiasCustodia = DiasCustodia;
    }

    public BigDecimal getKmTotal() {
        return KmTotal;
    }

    public void setKmTotal(String KmTotal) {
        try {
            this.KmTotal = new BigDecimal(KmTotal);
        } catch (Exception e) {
            this.KmTotal = new BigDecimal("0");
        }
    }

    public BigDecimal getKmConj() {
        return KmConj;
    }

    public void setKmConj(String KmConj) {
        try {
            this.KmConj = new BigDecimal(KmConj);
        } catch (Exception e) {
            this.KmConj = new BigDecimal("0");
        }
    }

    public BigDecimal getKmTerra() {
        return KmTerra;
    }

    public void setKmTerra(String KmTerra) {
        try {
            this.KmTerra = new BigDecimal(KmTerra);
        } catch (Exception e) {
            this.KmTerra = new BigDecimal("0");
        }
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public Integer getParada() {
        return Parada;
    }

    public void setParada(Integer Parada) {
        this.Parada = Parada;
    }

    public String getPedidoCli() {
        return PedidoCli;
    }

    public void setPedidoCli(String PedidoCli) {
        this.PedidoCli = PedidoCli;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

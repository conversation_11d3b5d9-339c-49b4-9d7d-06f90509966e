/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class TesMoedas {
    
    private String CodMoeda;
    private String Descricao;
    private String Valor;
    private String DtCotacao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    
    private String Codigo;

    public void TesMoedas() {
        this.CodMoeda = "";
        this.Codigo = "";
        this.Valor = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
    }

    public String getCodMoeda() {
        return CodMoeda;
    }

    public void setCodMoeda(String CodMoeda) {
        this.CodMoeda = CodMoeda;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDtCotacao() {
        return DtCotacao;
    }

    public void setDtCotacao(String DtCotacao) {
        this.DtCotacao = DtCotacao;
    }
}

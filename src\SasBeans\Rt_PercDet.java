package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercDet {

    private BigDecimal Sequencia;
    private Integer Parada;
    private BigDecimal CodFil;
    private BigDecimal KM;

    public Rt_PercDet() {
        this.Sequencia = new BigDecimal("0");
        this.Parada = 0;
        this.CodFil = new BigDecimal("0");
        this.KM = new BigDecimal("0");
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public Integer getParada() {
        return Parada;
    }

    public void setParada(Integer Parada) {
        this.Parada = Parada;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getKM() {
        return KM;
    }

    public void setKM(String KM) {
        try {
            this.KM = new BigDecimal(KM);
        } catch (Exception e) {
            this.KM = new BigDecimal("0");
        }
    }
}

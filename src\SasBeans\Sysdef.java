package SasBeans;

import java.util.Objects;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class Sysdef {

    private String codGrupo;
    private String codigo;
    private String subSistema;
    private String descricao;
    private Boolean inlcusao;
    private Boolean alteracao;
    private Boolean exclusao;
    private Boolean somenteVisualizacao;

    public String getCodGrupo() {
        return codGrupo;
    }

    public void setCodGrupo(String codGrupo) {
        this.codGrupo = codGrupo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getSubSistema() {
        return subSistema;
    }

    public void setSubSistema(String subSistema) {
        this.subSistema = subSistema;
    }

    public Boolean getInlcusao() {
        return inlcusao;
    }

    public void setInlcusao(Boolean inlcusao) {
        this.inlcusao = inlcusao;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public Boolean getExclusao() {
        return exclusao;
    }

    public void setExclusao(Boolean exclusao) {
        this.exclusao = exclusao;
    }

    public Boolean getSomenteVisualizacao() {
        return somenteVisualizacao;
    }

    public void setSomenteVisualizacao(Boolean somenteVisualizacao) {
        this.somenteVisualizacao = somenteVisualizacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.codigo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Sysdef other = (Sysdef) obj;
        if (!Objects.equals(this.codigo, other.codigo)) {
            return false;
        }
        return true;
    }
}

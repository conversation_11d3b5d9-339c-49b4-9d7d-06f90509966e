/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class TesEntStat {

    private BigDecimal Guia;
    private String Serie;
    private String Docto;
    private BigDecimal Matr;
    private BigDecimal CodPessoa;
    private int Camera;
    private String HrInicio;
    private String HrFinal;
    private BigDecimal Tempo;
    private BigDecimal QtdeDH;
    private BigDecimal QtdeMD;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getDocto() {
        return Docto;
    }

    public void setDocto(String Docto) {
        this.Docto = Docto;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    public int getCamera() {
        return Camera;
    }

    public void setCamera(int Camera) {
        this.Camera = Camera;
    }

    public String getHrInicio() {
        return HrInicio;
    }

    public void setHrInicio(String HrInicio) {
        this.HrInicio = HrInicio;
    }

    public String getHrFinal() {
        return HrFinal;
    }

    public void setHrFinal(String HrFinal) {
        this.HrFinal = HrFinal;
    }

    public BigDecimal getTempo() {
        return Tempo;
    }

    public void setTempo(BigDecimal Tempo) {
        this.Tempo = Tempo;
    }

    public BigDecimal getQtdeDH() {
        return QtdeDH;
    }

    public void setQtdeDH(String QtdeDH) {
        try {
            this.QtdeDH = new BigDecimal(QtdeDH);
        } catch (Exception e) {
            this.QtdeDH = new BigDecimal("0");
        }
    }

    public BigDecimal getQtdeMD() {
        return QtdeMD;
    }

    public void setQtdeMD(String QtdeMD) {
        try {
            this.QtdeMD = new BigDecimal(QtdeMD);
        } catch (Exception e) {
            this.QtdeMD = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

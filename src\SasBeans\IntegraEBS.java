/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class IntegraEBS {

    private String id;
    private String alarmGroupId;
    
    private String occurrenceDate;
    private String receiveDate;
    private String detectDate;
    private String alarmGroupDate;
    
    private String eventType;
    private String deviceId;
    private String deviceName;
    private String deviceSerialNo;
    private String type;
    private String displayedType;
    private String rawOsmEventType;
    private String eventData;
    private String locationOnly;
    
    private String codfil;
    private String operador;
    private String dtAlter;
    private String hrAlter;
    
    private String dtUltOcor;
    private String hrUltOcor;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAlarmGroupId() {
        return alarmGroupId;
    }

    public void setAlarmGroupId(String alarmGroupId) {
        this.alarmGroupId = alarmGroupId;
    }

    public String getOccurrenceDate() {
        return occurrenceDate;
    }

    public void setOccurrenceDate(String occurrenceDate) {
        this.occurrenceDate = occurrenceDate;
    }

    public String getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(String receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getDetectDate() {
        return detectDate;
    }

    public void setDetectDate(String detectDate) {
        this.detectDate = detectDate;
    }

    public String getAlarmGroupDate() {
        return alarmGroupDate;
    }

    public void setAlarmGroupDate(String alarmGroupDate) {
        this.alarmGroupDate = alarmGroupDate;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceSerialNo() {
        return deviceSerialNo;
    }

    public void setDeviceSerialNo(String deviceSerialNo) {
        this.deviceSerialNo = deviceSerialNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDisplayedType() {
        return displayedType;
    }

    public void setDisplayedType(String displayedType) {
        this.displayedType = displayedType;
    }

    public String getRawOsmEventType() {
        return rawOsmEventType;
    }

    public void setRawOsmEventType(String rawOsmEventType) {
        this.rawOsmEventType = rawOsmEventType;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }

    public String getLocationOnly() {
        return locationOnly;
    }

    public void setLocationOnly(String locationOnly) {
        this.locationOnly = locationOnly;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDtAlter() {
        return dtAlter;
    }

    public void setDtAlter(String dtAlter) {
        this.dtAlter = dtAlter;
    }

    public String getHrAlter() {
        return hrAlter;
    }

    public void setHrAlter(String hrAlter) {
        this.hrAlter = hrAlter;
    }    

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }    
    
    public String getDtUltOcor() {
        return dtUltOcor;
    }

    public void setDtUltOcor(String dtUltOcor) {
        this.dtUltOcor = dtUltOcor;
    }

    public String getHrUltOcor() {
        return hrUltOcor;
    }

    public void setHrUltOcor(String hrUltOcor) {
        this.hrUltOcor = hrUltOcor;
    }    

}

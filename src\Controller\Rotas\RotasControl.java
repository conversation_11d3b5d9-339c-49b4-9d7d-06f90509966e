package Controller.Rotas;

import Dados.Persistencia;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasDaos.RotasDao;
import SasDaos.Rt_PercDao;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class RotasControl {

    /**
     * Faz inserção de rotas
     *
     * @param rota - Objeto de rota a ser inserido Sequencia e número de Rota
     * será inserido automaticamente
     * @param rt_perc - Objeto de parada de rota a ser inserido
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void RotaInserir(Rotas rota, Rt_Perc rt_perc, Persistencia persistencia) throws Exception {
        try {

            Rotas rotas = rota;
            Rt_Perc rt_percs;

            RotasDao rotasdao = new RotasDao();
            Rt_PercDao rt_percdao = new Rt_PercDao();

            BigDecimal Sequencia = new BigDecimal("1");

            //inserção da rota
            rotas.setRota(rotasdao.MaxRota(rota.getCodFil(), rota.getData(), persistencia));
            rotas.setSequencia(Sequencia.add(rotasdao.MaxSequencia(persistencia)).toPlainString());
            rotasdao.Inserir(rotas, persistencia);

            Sequencia = rotas.getSequencia();
            //inserção da parada
            rt_percs = rt_perc;
            rt_percs.setSequencia(Sequencia.toPlainString());
            rt_percs.setParada(1 + rt_percdao.getMaxParada(Sequencia, persistencia));
            rt_percdao.Inserir(rt_percs, persistencia);
        } catch (Exception e) {
            throw new Exception("Falha ao inserir rota /r/n" + e.getMessage());
        }
    }

    /**
     * Inserção de parada de rota
     *
     * @param rt_perc - Objeto de parada de rotas
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void InserirParada(Rt_Perc rt_perc, Persistencia persistencia) throws Exception {
        try {
            Rt_Perc rt_percs;
            Rt_PercDao rt_percdao = new Rt_PercDao();
            //inserção da parada
            rt_percs = rt_perc;
            rt_percs.setParada(1 + rt_percdao.getMaxParada(rt_perc.getSequencia(), persistencia));
            rt_percdao.Inserir(rt_percs, persistencia);
        } catch (Exception e) {
            throw new Exception("Falha ao inserir parada na rota\r\n" + e.getMessage());
        }
    }
}

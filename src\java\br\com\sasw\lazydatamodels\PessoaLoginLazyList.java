/*
 */
package br.com.sasw.lazydatamodels;

import Dados.Persistencia;
import SasBeans.Saspw;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PessoaLoginLazyList extends LazyDataModel<UsuarioSatMobWeb> {

    private static final long serialVersionUID = 1L;
    private List<UsuarioSatMobWeb> usuarios;
    private final AcessosSatMobWeb acessossatmobweb;
    private Persistencia persistencia, satellite;

    public PessoaLoginLazyList(Persistencia pst, Persistencia sat) {
        this.acessossatmobweb = new AcessosSatMobWeb();
        this.persistencia = pst;
        this.satellite = sat;
    }

    @Override
    public List<UsuarioSatMobWeb> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.usuarios = this.acessossatmobweb.listagemUsuarios(first, pageSize, filters, this.persistencia, this.satellite);

            // set the total of players
            setRowCount(this.acessossatmobweb.contagemAcessos(filters, this.persistencia, this.satellite));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.usuarios;
    }

    @Override
    public Object getRowKey(UsuarioSatMobWeb usuario) {
        if (null == usuario.getSaspw()) {
            usuario.setSaspw(new Saspw());
        }
        return usuario.getSaspw().getNome();
    }

    @Override
    public UsuarioSatMobWeb getRowData(String nome) {
        for (UsuarioSatMobWeb usuario : this.usuarios) {
            if (nome.equals(usuario.getSaspw().getNome())) {
                return usuario;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public Persistencia getSatellite() {
        return satellite;
    }

    public void setSatellite(Persistencia satellite) {
        this.satellite = satellite;
    }
}

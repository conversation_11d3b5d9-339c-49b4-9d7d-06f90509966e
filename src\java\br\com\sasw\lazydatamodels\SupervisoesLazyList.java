/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Supervisao.Supervisoes;
import Dados.Persistencia;
import SasBeansCompostas.TmktDetPstPstServClientes;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class SupervisoesLazyList extends LazyDataModel<TmktDetPstPstServClientes> {

    private static final long serialVersionUID = 1L;
    private List<TmktDetPstPstServClientes> supervisoes;
    private final Supervisoes supervisaosatmobweb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public SupervisoesLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.supervisaosatmobweb = new Supervisoes();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<TmktDetPstPstServClientes> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.supervisoes = this.supervisaosatmobweb.ListagemPaginada(first, pageSize, filters, this.codPessoa, this.persistencia);

            // set the total of players
            setRowCount(this.supervisaosatmobweb.Contagem(filters, this.codPessoa, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.supervisoes;
    }

    @Override
    public Object getRowKey(TmktDetPstPstServClientes supervisao) {
        return supervisao.tmktdetpst.getSequencia().toPlainString();
    }

    @Override
    public TmktDetPstPstServClientes getRowData(String sequencia) {
        for (TmktDetPstPstServClientes supervisao : this.supervisoes) {
            if (sequencia.equals(supervisao.tmktdetpst.getSequencia().toPlainString())) {
                return supervisao;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

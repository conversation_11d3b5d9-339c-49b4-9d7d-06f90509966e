/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class CtrOperV {

    private BigDecimal Numero;
    private BigDecimal CodFil;
    private String Data;
    private String Periodo;
    private String Posto;
    private String Mesario;
    private BigDecimal FuncAus;
    private String Motivo_Aus;
    private BigDecimal FuncSubs;
    private String Motivo_Subs;
    private String Hora_Extra;
    private BigDecimal VR;
    private BigDecimal VT;
    private BigDecimal Hospedagem;
    private String Hora1;
    private String Hora2;
    private String Hora3;
    private String Hora4;
    private BigDecimal HEDiurna;
    private BigDecimal HENoturna;
    private BigDecimal Nro_HE;
    private BigDecimal HsItinere;
    private String RefTipo2;
    private String CodSrv;
    private String Notas;
    private String Obs;
    private String Pedido;
    private int Intraj;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Oper_Excl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String Flag_Caixa;
    private String Flag_NF;
    private String Flag_Excl;
    private String Dt_Faturado;
    private BigDecimal NumCalculo;

//    public CtrOperV(){
//    this.Numero = new BigDecimal("0");
//    this.CodFil = new BigDecimal("0"); 
//    this.Data = "";
//    this.Periodo = "";
//    this.Posto = ""; 
//    this.Mesario = "";
//    this.FuncAus = new BigDecimal("0"); 
//    this.Motivo_Aus = "";
//    this.FuncSubs = new BigDecimal("0");      
//    this.Motivo_Subs = "";
//    this.Hora_Extra = "";
//    this.VR = new BigDecimal("0");       
//    this.VT = new BigDecimal("0");      
//    this.Hospedagem = new BigDecimal("0");       
//    this.Hora1 = "";
//    this.Hora2 = "";
//    this.Hora3 = "";
//    this.Hora4 = "";               
//    this.HEDiurna = new BigDecimal("0");       
//    this.HENoturna = new BigDecimal("0");       
//    this.Nro_HE = new BigDecimal("0");       
//    this.HsItinere = new BigDecimal("0");       
//    this.RefTipo2 = "";
//    this.CodSrv = "";
//    this.Notas = "";
//    this.Obs = "";
//    this.Pedido = ""; 
//    this.Intraj = 0;
//    this.Operador = "";
//    this.Dt_Alter = "";       
//    this.Hr_Alter = "";
//    this.Oper_Excl = "";
//    this.Dt_Excl = "";
//    this.Hr_Excl = "";
//    this.Flag_Caixa = "";
//    this.Flag_NF = "";
//    this.Flag_Excl = ""; 
//    this.Dt_Faturado = "";
//    this.NumCalculo = new BigDecimal("0");
//    }
    /**
     * @return the Numero
     */
    public BigDecimal getNumero() {
        return Numero;
    }

    /**
     * @param Numero the Numero to set
     */
    public void setNumero(String Numero) {
        try {
            this.Numero = new BigDecimal(Numero);
        } catch (Exception e) {
            this.Numero = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(BigDecimal CodFil) {
        try {
            this.CodFil = CodFil;
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }

    }

    /**
     * @return the Data
     */
    public String getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(String Data) {
        this.Data = Data;
    }

    /**
     * @return the Periodo
     */
    public String getPeriodo() {
        return Periodo;
    }

    /**
     * @param Periodo the Periodo to set
     */
    public void setPeriodo(String Periodo) {
        this.Periodo = Periodo;
    }

    /**
     * @return the Posto
     */
    public String getPosto() {
        return Posto;
    }

    /**
     * @param Posto the Posto to set
     */
    public void setPosto(String Posto) {
        this.Posto = Posto;
    }

    /**
     * @return the Mesario
     */
    public String getMesario() {
        return Mesario;
    }

    /**
     * @param Mesario the Mesario to set
     */
    public void setMesario(String Mesario) {
        this.Mesario = Mesario;
    }

    /**
     * @return the FuncAus
     */
    public BigDecimal getFuncAus() {
        return FuncAus;
    }

    /**
     * @param FuncAus the FuncAus to set
     */
    public void setFuncAus(String FuncAus) {
        try {
            this.FuncAus = new BigDecimal(FuncAus);
        } catch (Exception e) {
            this.FuncAus = new BigDecimal("0");
        }
    }

    /**
     * @param FuncAus the FuncAus to set
     */
    public void setFuncAus(BigDecimal FuncAus) {
        try {
            this.FuncAus = FuncAus;
        } catch (Exception e) {
            this.FuncAus = new BigDecimal("0");
        }
    }

    /**
     * @return the Motivo_Aus
     */
    public String getMotivo_Aus() {
        return Motivo_Aus;
    }

    /**
     * @param Motivo_Aus the Motivo_Aus to set
     */
    public void setMotivo_Aus(String Motivo_Aus) {
        this.Motivo_Aus = Motivo_Aus;
    }

    /**
     * @return the FuncSubs
     */
    public BigDecimal getFuncSubs() {
        return FuncSubs;
    }

    /**
     * @param FuncSubs the FuncSubs to set
     */
    public void setFuncSubs(String FuncSubs) {
        try {
            this.FuncSubs = new BigDecimal(FuncSubs);
        } catch (Exception e) {
            this.FuncSubs = new BigDecimal("0");
        }
    }

    /**
     * @return the Motivo_Subs
     */
    public String getMotivo_Subs() {
        return Motivo_Subs;
    }

    /**
     * @param Motivo_Subs the Motivo_Subs to set
     */
    public void setMotivo_Subs(String Motivo_Subs) {
        this.Motivo_Subs = Motivo_Subs;
    }

    /**
     * @return the Hora_Extra
     */
    public String getHora_Extra() {
        return Hora_Extra;
    }

    /**
     * @param Hora_Extra the Hora_Extra to set
     */
    public void setHora_Extra(String Hora_Extra) {
        this.Hora_Extra = Hora_Extra;
    }

    /**
     * @return the VR
     */
    public BigDecimal getVR() {
        return VR;
    }

    /**
     * @param VR the VR to set
     */
    public void setVR(String VR) {
        try {
            this.VR = new BigDecimal(VR);
        } catch (Exception e) {
            this.VR = new BigDecimal("0");
        }
    }

    /**
     * @return the VT
     */
    public BigDecimal getVT() {
        return VT;
    }

    /**
     * @param VT the VT to set
     */
    public void setVT(String VT) {
        try {
            this.VT = new BigDecimal(VT);
        } catch (Exception e) {
            this.VT = new BigDecimal("0");
        }
    }

    /**
     * @return the Hospedagem
     */
    public BigDecimal getHospedagem() {
        return Hospedagem;
    }

    /**
     * @param Hospedagem the Hospedagem to set
     */
    public void setHospedagem(String Hospedagem) {
        try {
            this.Hospedagem = new BigDecimal(Hospedagem);
        } catch (Exception e) {
            this.Hospedagem = new BigDecimal("0");
        }
    }

    /**
     * @return the Hora1
     */
    public String getHora1() {
        return Hora1;
    }

    /**
     * @param Hora1 the Hora1 to set
     */
    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    /**
     * @return the Hora2
     */
    public String getHora2() {
        return Hora2;
    }

    /**
     * @param Hora2 the Hora2 to set
     */
    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    /**
     * @return the Hora3
     */
    public String getHora3() {
        return Hora3;
    }

    /**
     * @param Hora3 the Hora3 to set
     */
    public void setHora3(String Hora3) {
        this.Hora3 = Hora3;
    }

    /**
     * @return the Hora4
     */
    public String getHora4() {
        return Hora4;
    }

    /**
     * @param Hora4 the Hora4 to set
     */
    public void setHora4(String Hora4) {
        this.Hora4 = Hora4;
    }

    /**
     * @return the HEDiurna
     */
    public BigDecimal getHEDiurna() {
        return HEDiurna;
    }

    /**
     * @param HEDiurna the HEDiurna to set
     */
    public void setHEDiurna(String HEDiurna) {
        try {
            this.HEDiurna = new BigDecimal(HEDiurna);
        } catch (Exception e) {
            this.HEDiurna = new BigDecimal("0");
        }
    }

    /**
     * @return the HENoturna
     */
    public BigDecimal getHENoturna() {
        return HENoturna;
    }

    /**
     * @param HENoturna the HENoturna to set
     */
    public void setHENoturna(String HENoturna) {
        try {
            this.HENoturna = new BigDecimal(HENoturna);
        } catch (Exception e) {
            this.HENoturna = new BigDecimal("0");
        }
    }

    /**
     * @return the Nro_HE
     */
    public BigDecimal getNro_HE() {
        return Nro_HE;
    }

    /**
     * @param Nro_HE the Nro_HE to set
     */
    public void setNro_HE(String Nro_HE) {
        try {
            this.Nro_HE = new BigDecimal(Nro_HE);
        } catch (Exception e) {
            this.Nro_HE = new BigDecimal("0");
        }
    }

    /**
     * @return the HsItinere
     */
    public BigDecimal getHsItinere() {
        return HsItinere;
    }

    /**
     * @param HsItinere the HsItinere to set
     */
    public void setHsItinere(String HsItinere) {
        try {
            this.HsItinere = new BigDecimal(HsItinere);
        } catch (Exception e) {
            this.HsItinere = new BigDecimal("0");
        }
    }

    /**
     * @return the RefTipo2
     */
    public String getRefTipo2() {
        return RefTipo2;
    }

    /**
     * @param RefTipo2 the RefTipo2 to set
     */
    public void setRefTipo2(String RefTipo2) {
        this.RefTipo2 = RefTipo2;
    }

    /**
     * @return the CodSrv
     */
    public String getCodSrv() {
        return CodSrv;
    }

    /**
     * @param CodSrv the CodSrv to set
     */
    public void setCodSrv(String CodSrv) {
        this.CodSrv = CodSrv;
    }

    /**
     * @return the Notas
     */
    public String getNotas() {
        return Notas;
    }

    /**
     * @param Notas the Notas to set
     */
    public void setNotas(String Notas) {
        this.Notas = Notas;
    }

    /**
     * @return the Obs
     */
    public String getObs() {
        return Obs;
    }

    /**
     * @param Obs the Obs to set
     */
    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    /**
     * @return the Pedido
     */
    public String getPedido() {
        return Pedido;
    }

    /**
     * @param Pedido the Pedido to set
     */
    public void setPedido(String Pedido) {
        this.Pedido = Pedido;
    }

    /**
     * @return the Intraj
     */
    public int getIntraj() {
        return Intraj;
    }

    /**
     * @param Intraj the Intraj to set
     */
    public void setIntraj(String Intraj) {
        try {
            this.Intraj = Integer.parseInt(Intraj);
        } catch (Exception e) {
            this.Intraj = 0;
        }

    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the Oper_Excl
     */
    public String getOper_Excl() {
        return Oper_Excl;
    }

    /**
     * @param Oper_Excl the Oper_Excl to set
     */
    public void setOper_Excl(String Oper_Excl) {
        this.Oper_Excl = Oper_Excl;
    }

    /**
     * @return the Dt_Excl
     */
    public String getDt_Excl() {
        return Dt_Excl;
    }

    /**
     * @param Dt_Excl the Dt_Excl to set
     */
    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    /**
     * @return the Hr_Excl
     */
    public String getHr_Excl() {
        return Hr_Excl;
    }

    /**
     * @param Hr_Excl the Hr_Excl to set
     */
    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    /**
     * @return the Flag_Caixa
     */
    public String getFlag_Caixa() {
        return Flag_Caixa;
    }

    /**
     * @param Flag_Caixa the Flag_Caixa to set
     */
    public void setFlag_Caixa(String Flag_Caixa) {
        this.Flag_Caixa = Flag_Caixa;
    }

    /**
     * @return the Flag_NF
     */
    public String getFlag_NF() {
        return Flag_NF;
    }

    /**
     * @param Flag_NF the Flag_NF to set
     */
    public void setFlag_NF(String Flag_NF) {
        this.Flag_NF = Flag_NF;
    }

    /**
     * @return the Flag_Excl
     */
    public String getFlag_Excl() {
        return Flag_Excl;
    }

    /**
     * @param Flag_Excl the Flag_Excl to set
     */
    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    /**
     * @return the Dt_Faturado
     */
    public String getDt_Faturado() {
        return Dt_Faturado;
    }

    /**
     * @param Dt_Faturado the Dt_Faturado to set
     */
    public void setDt_Faturado(String Dt_Faturado) {
        this.Dt_Faturado = Dt_Faturado;
    }

    /**
     * @return the NumCalculo
     */
    public BigDecimal getNumCalculo() {
        return NumCalculo;
    }

    /**
     * @param NumCalculo the NumCalculo to set
     */
    public void setNumCalculo(String NumCalculo) {
        try {
            this.NumCalculo = new BigDecimal(NumCalculo);
        } catch (Exception e) {
            this.NumCalculo = new BigDecimal("0");
        }
    }

}

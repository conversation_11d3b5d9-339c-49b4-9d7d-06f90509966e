<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:cadastros="http://xmlns.jcp.org/jsf/composite/cadastros"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
            </f:metadata>

            <cadastros:header
                id="cabecalho"
                titulo="#{localemsgs.Propostas}"
                imagem="../assets/img/icone_satmob_proposta.png"
                data="#{pessoa.dataTela}"
                descricao="#{pessoa.filiais.descricao}"
                endereco="#{pessoa.filiais.endereco}"
                bairro="#{pessoa.filiais.bairro}"
                cidade="#{pessoa.filiais.cidade}"
                UF="#{pessoa.filiais.UF}"
                />
            <h:form id="main" style="padding:0px !important">
                <div id="divLoad" class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding:0px !important; height:calc(100vh - 92px) !important; border:none !important">
                   <iframe src="https://cliente.sasw.com.br/satmob/proposta.html?param=SPM&amp;language=en&amp;codfil=1" style="width:100%;height:100%;border:0;padding:0px !important; margin:0px !important"></iframe>
                </div>
            </h:form>   

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

/*
 */
package br.com.sasw.managedBeans.comercial;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Controller.Contatos.ContatoSatMobWeb;
import Controller.Login.LoginSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Controller.TbVal.ControlerTbVal;
import Dados.Persistencia;
import SasBeans.Contatos;
import SasBeans.Filiais;
import SasBeans.Municipios;
import SasBeans.SasPWFill;
import SasBeans.TbVal;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.lazydatamodels.ContatosLazyList;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "contatos")
@ViewScoped
public class ContatosMB implements Serializable {

    private Persistencia persistencia;
    private ContatoSatMobWeb contatossatmob;
    private BigDecimal codPessoa;
    private String filialDesc, banco, operador, log, caminho, escolha, situacao,
            codfil, dataTela;
    private ArquivoLog logerro;
    private static Date ultimoDia;
    private int total, flag;
    private Map filters, mapSituacao, mapOrigem;
    private LazyDataModel<Contatos> contatos = null;
    private Contatos contatoSelecionado, novoContato, contatoPesquisa;    
    
    private List<Municipios> cidades;
    private ClientesSatMobWeb clientessatmobweb;
    private LoginSatMobWeb loginsatmobweb;
    private ControlerTbVal tbvalsatmobweb;
    private SasPWFill filial;
    private List<TbVal> sit, origem;
    private Boolean mostrarFiliais, limparFiltros;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private final String CODFIL = " codfil = ? ",
            CODIGO = " Codigo = ? ",
            CODCLI = " codcli = ? ",
            NOME = " nome LIKE ? ",
            FANTASIA = " fantasia LIKE ? ",
            SITUACAO = " situacao = ? ",
            TPCLI = " tpcli = ? ",
            DT_ALTER = " Dt_Alter = ? ";
    private String EMPRESA;
    private String chavePesquisa = "NOME";

    public ContatosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        contatossatmob = new ContatoSatMobWeb();
        contatoSelecionado = new Contatos();
        clientessatmobweb = new ClientesSatMobWeb();
        loginsatmobweb = new LoginSatMobWeb();
        tbvalsatmobweb = new ControlerTbVal();
        escolha = "cgc";
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
    }

    public void Persistencias(Persistencia pstLocal) {
        try {
            this.persistencia = pstLocal;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            filters = new HashMap();
            filters.put(CODFIL, codfil);
            EMPRESA = " codfil in (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and "
                    + " paramet.path = '"
//                    + (persistencia.getEmpresa().equals("SATCONFEDERALBSB") ? "SatCVB" : persistencia.getEmpresa())
                    + (persistencia.getEmpresa())
                    + "') ";
            filters.put(EMPRESA, "");
            filters.put(CODIGO, "");
            filters.put(CODCLI, "");
            filters.put(NOME, "");
            filters.put(FANTASIA, "");
            filters.put(SITUACAO, "");
            filters.put(TPCLI, "");
            filters.put(DT_ALTER, "");
            total = contatossatmob.contagem(filters, persistencia);
            sit = tbvalsatmobweb.Listagem(31, persistencia);
            mapSituacao = new HashMap<>();
            for (TbVal tbval : this.sit) {
                mapSituacao.put(tbval.getCodigo().toString(), tbval.getDescricao());
            }
            origem = this.tbvalsatmobweb.Listagem(32, persistencia);
            mapOrigem = new HashMap<>();
            for (TbVal tbval : origem) {
                mapOrigem.put(tbval.getCodigo().toString(), tbval.getDescricao());
            }
            filiais = rotassatweb.buscaInfoFilial(codfil, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void lazyGetAllContatos() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(filters);
            contatos = new ContatosLazyList(persistencia);
            total = contatossatmob.contagem(filters, persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void Cadastrar() {
        try {
            novoContato.setCodFil(this.filial.getCodfilAc());
            novoContato.setSituacao(new BigDecimal(this.situacao));
            novoContato.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            novoContato.setHr_Alter(DataAtual.getDataAtual("HORA"));
            novoContato.setDt_Alter(DataAtual.getDataAtual("SQL"));

            novoContato.setNome(novoContato.getNome().toUpperCase());
            novoContato.setFantasia(novoContato.getFantasia().toUpperCase());
            novoContato.setEndereco(novoContato.getEndereco().toUpperCase());
            novoContato.setBairro(novoContato.getBairro().toUpperCase());
            novoContato.setCidade(novoContato.getCidade().toUpperCase());
            novoContato.setUF(novoContato.getUF().toUpperCase());
            novoContato.setIM(novoContato.getIM().toUpperCase());
            novoContato.setIE(novoContato.getIE().toUpperCase());
            novoContato.setContato(novoContato.getContato().toUpperCase());

            novoContato.setFone1(Mascaras.removeMascara(novoContato.getFone1()));
            novoContato.setFone2(Mascaras.removeMascara(novoContato.getFone2()));
            novoContato.setCEP(Mascaras.removeMascara(novoContato.getCEP()));

            if (!novoContato.getCPF().isEmpty()) {
                novoContato.setCPF(Mascaras.removeMascara(novoContato.getCPF()));
                if (!ValidadorCPF_CNPJ.ValidarCPF(novoContato.getCPF())) {
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }

            if (!novoContato.getCNPJ().isEmpty()) {
                novoContato.setCNPJ(Mascaras.removeMascara(novoContato.getCNPJ()));
                if (!ValidadorCPF_CNPJ.ValidarCNPJ(novoContato.getCNPJ())) {
                    throw new Exception(Messages.getMessageS("CNPJInvalido"));
                }
            }

            novoContato.setOperIncl(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            try {
                novoContato.setObs(novoContato.getObs().toUpperCase());
            } catch (Exception e2) {
            }
            this.contatossatmob.cadastrar(novoContato, this.persistencia);
            this.total = this.contatossatmob.contagem(filters, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Editar() {
        try {
            novoContato.setCodFil(this.filial.getCodfilAc());
            novoContato.setOperador(FuncoesString.RecortaAteEspaço(operador, 0, 10));
            novoContato.setHr_Alter(DataAtual.getDataAtual("HORA"));
            novoContato.setDt_Alter(DataAtual.getDataAtual("SQL"));

            novoContato.setNome(novoContato.getNome().toUpperCase());
            novoContato.setFantasia(novoContato.getFantasia().toUpperCase());
            novoContato.setEndereco(novoContato.getEndereco().toUpperCase());
            novoContato.setBairro(novoContato.getBairro().toUpperCase());
            novoContato.setCidade(novoContato.getCidade().toUpperCase());
            novoContato.setUF(novoContato.getUF().toUpperCase());
            novoContato.setIM(novoContato.getIM().toUpperCase());
            novoContato.setIE(novoContato.getIE().toUpperCase());
            novoContato.setContato(novoContato.getContato().toUpperCase());

            novoContato.setFone1(Mascaras.removeMascara(novoContato.getFone1()));
            novoContato.setFone2(Mascaras.removeMascara(novoContato.getFone2()));
            novoContato.setCEP(Mascaras.removeMascara(novoContato.getCEP()));

            if (!novoContato.getCPF().isEmpty()) {
                novoContato.setCPF(Mascaras.removeMascara(novoContato.getCPF()));
                if (!ValidadorCPF_CNPJ.ValidarCPF(novoContato.getCPF())) {
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }

            if (!novoContato.getCNPJ().isEmpty()) {
                novoContato.setCNPJ(Mascaras.removeMascara(novoContato.getCNPJ()));
                if (!ValidadorCPF_CNPJ.ValidarCNPJ(novoContato.getCNPJ())) {
                    throw new Exception(Messages.getMessageS("CNPJInvalido"));
                }
            }

            try {
                novoContato.setObs(novoContato.getObs().toUpperCase());
            } catch (Exception e2) {
            }

            if (!this.situacao.equals(novoContato.getSituacao().toBigInteger().toString())) {
                novoContato.setDtSituacao(DataAtual.getDataAtual("SQL"));
            }
            novoContato.setSituacao(new BigDecimal(this.situacao));
            this.contatossatmob.editar(novoContato, this.persistencia);
            this.total = this.contatossatmob.contagem(filters, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void Endereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(novoContato.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                novoContato.setBairro(obj.get("bairro").toString());
                novoContato.setEndereco(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    novoContato.setCidade("BRASILIA");
                    novoContato.setUF("DF");
                } else {
                    this.cidades = this.clientessatmobweb.ListaMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistencia);
                    novoContato.setCidade(this.cidades.get(0).getNome());
                    novoContato.setUF(this.cidades.get(0).getUF().substring(0, 2));
                }
                novoContato.setCEP(novoContato.getCEP().substring(0, 2) + "."
                        + novoContato.getCEP().substring(2, 5) + "-"
                        + novoContato.getCEP().substring(5, 8));
                PrimeFaces.current().executeScript("PF('dlgOk').show()");
            } else {
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void PesquisaPaginada() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (null != novoContato.getCodFil()) {
            filters.replace(CODFIL, novoContato.getCodFil().toPlainString());
            filters.replace(EMPRESA, "");
            this.mostrarFiliais = false;
        } else {
            filters.replace(CODFIL, "");
            filters.replace(EMPRESA, this.codPessoa.toPlainString());
            this.mostrarFiliais = true;
        }

        if (novoContato.getCodCli().equals("")) {
            filters.replace(CODCLI, "");
        } else {
            filters.replace(CODCLI, novoContato.getCodCli());
        }

        if (novoContato.getNome().equals("")) {
            filters.replace(NOME, "");
        } else {
            filters.replace(NOME, "%" + novoContato.getNome() + "%");
        }

        if (novoContato.getFantasia().equals("")) {
            filters.replace(FANTASIA, "");
        } else {
            filters.replace(FANTASIA, "%" + novoContato.getFantasia() + "%");
        }

        if (null == novoContato.getSituacao()) {
            filters.replace(SITUACAO, "");
        } else {
            filters.replace(SITUACAO, novoContato.getSituacao().toBigInteger().toString());
        }

        if (null == novoContato.getTpCli()) {
            filters.replace(TPCLI, "");
        } else {
            filters.replace(TPCLI, novoContato.getTpCli());
        }

        dt.setFilters(filters);
        lazyGetAllContatos();
        dt.setFirst(0);
    }

    public void pesquisarUnico() {
        limparPesquisa();
        replaceFilter();
        lazyGetAllContatos();
    }

    private void replaceFilter() {
        try {
            switch (chavePesquisa) {
                case "CODFIL":
                    filters.replace(CODFIL, contatoPesquisa.getCodFil().toString());
                    return;
                case "CODIGO":
                    filters.replace(CODIGO, contatoPesquisa.getCodigo());
                    return;
                case "NOME":
                    filters.replace(NOME, "%" + contatoPesquisa.getNome() + "%");
                    return;
                case "FANTASIA":
                    filters.replace(FANTASIA, "%" + contatoPesquisa.getFantasia() + "%");
                    return;
                case "CODCLI":
                    filters.replace(CODCLI, contatoPesquisa.getCodCli());
                    return;
                default:
                    throw new Exception("CampoNaoExiste");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public void PrePesquisa() {
        contatoPesquisa = new Contatos();
    }

    public void PreCadastro() {
        novoContato = new Contatos();
        this.flag = 1;
        this.situacao = new String();
        try {
            this.filial = this.loginsatmobweb.BuscaFilial(this.codfil, this.codPessoa, this.persistencia);
            novoContato.setCodFil(this.filial.getCodfilAc());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        PrimeFaces.current().resetInputs("formCadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    public void PreEdicao() {
        if (null == this.contatoSelecionado) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SelecioneContato"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.flag = 2;
                this.filial = this.loginsatmobweb.BuscaFilial(this.contatoSelecionado.getCodFil().toString(), this.codPessoa, this.persistencia);
                novoContato = this.contatoSelecionado;
                if (novoContato.getCNPJ().equals("")) {
                    this.escolha = "cpf";
                } else {
                    this.escolha = "cgc";
                }
                this.situacao = novoContato.getSituacao().toBigInteger().toString();
                if (!novoContato.getCEP().equals("") && !novoContato.getCEP().contains(".")) {
                    try {
                        novoContato.setCEP(novoContato.getCEP().substring(0, 2) + "."
                                + novoContato.getCEP().substring(2, 5) + "-"
                                + novoContato.getCEP().substring(5, 8));
                    } catch (Exception e2) {
                    }
                }
                PrimeFaces.current().resetInputs("formCadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show()");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void MostrarFiliais() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        if (this.mostrarFiliais) {
            filters.replace(CODFIL, "");
            filters.replace(EMPRESA, this.codPessoa.toPlainString());
        } else {
            filters.replace(CODFIL, this.codfil);
            filters.replace(EMPRESA, "");
        }
        dt.setFilters(filters);
        lazyGetAllContatos();
        dt.setFirst(0);
    }

    public void LimparFiltros() {
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        limparFiltros = false;
        mostrarFiliais = false;
        filters.replace(CODFIL, codfil);
        filters.replace(EMPRESA, "");
        filters.replace(CODCLI, "");
        filters.replace(NOME, "");
        filters.replace(FANTASIA, "");
        filters.replace(SITUACAO, "");
        filters.replace(TPCLI, "");
        dt.setFilters(filters);
        lazyGetAllContatos();
        dt.setFirst(0);
    }

    public void limparPesquisa() {
        filters.replace(CODFIL, codfil);
        filters.replace(EMPRESA, "");
        filters.replace(CODCLI, "");
        filters.replace(NOME, "");
        filters.replace(FANTASIA, "");
        filters.replace(SITUACAO, "");
        filters.replace(TPCLI, "");
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public Contatos getContatoSelecionado() {
        return contatoSelecionado;
    }

    public void setContatoSelecionado(Contatos contatoSelecionado) {
        this.contatoSelecionado = contatoSelecionado;
    }

    public Contatos getNovoContato() {
        return novoContato;
    }

    public void setNovoContato(Contatos novoContato) {
        this.novoContato = novoContato;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public List<TbVal> getSit() {
        return sit;
    }

    public void setSit(List<TbVal> sit) {
        this.sit = sit;
    }

    public List<TbVal> getOrigem() {
        return origem;
    }

    public void setOrigem(List<TbVal> origem) {
        this.origem = origem;
    }

    public Map getMapSituacao() {
        return mapSituacao;
    }

    public void setMapSitucao(Map mapSituacao) {
        this.mapSituacao = mapSituacao;
    }

    public Map getMapOrigem() {
        return mapOrigem;
    }

    public void setMapOrigem(Map mapOrigem) {
        this.mapOrigem = mapOrigem;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public LazyDataModel<Contatos> getAllContatos() {
        if (contatos == null) {
            lazyGetAllContatos();
        }

        return contatos;
    }

    public Contatos getContatoPesquisa() {
        return contatoPesquisa;
    }

    public void setContatoPesquisa(Contatos contatoPesquisa) {
        this.contatoPesquisa = contatoPesquisa;
    }
    
    public LazyDataModel<Contatos> getAllContatosData() {
        if (this.contatos == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace("Dt_Alter = ?", this.dataTela);            
//            this.filters.replace("posto", this.secao == null ? "" : this.secao);
            dt.setFilters(this.filters);
            this.contatos = new ContatosLazyList(this.persistencia);

        }
        return this.contatos;
    }
    
    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.replace("Dt_alter = ?", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllContatos();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.replace("dt_Alter = ?", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllContatos();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData(SelectEvent data) {
        this.dataTela = (String) data.getObject();
        this.filters.replace("Dt_Alter = ?", this.dataTela);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllContatos();
        dt.setFirst(0);
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }
}

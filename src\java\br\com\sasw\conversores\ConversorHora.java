/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import br.com.sasw.utils.Mascaras;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorHora")
public class ConversorHora implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return Mascaras.removeMascaraHora(value);
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        DateTimeFormatter df;
        Mascaras mascaras = new Mascaras();
        try {
            df = DateTimeFormatter.ofPattern("HH:mm");
            return LocalTime.parse(value.toString(), df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("HHmm");
            return LocalTime.parse(value.toString(), df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
        } catch (Exception e) {
        }
        try {
            df = DateTimeFormatter.ofPattern("H:mm:");
            return LocalTime.parse(value.toString(), df).format(DateTimeFormatter.ofPattern(mascaras.getPadraoHora()));
        } catch (Exception e) {
        }

        try {
            if (value.toString().contains(" : ")) {
                return "";
            }
        } catch (Exception e) {
        }

        return value.toString();
    }
}

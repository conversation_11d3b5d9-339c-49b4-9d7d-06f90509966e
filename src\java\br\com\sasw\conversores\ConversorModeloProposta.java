/*
 */
package br.com.sasw.conversores;

import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorModeloProposta")
public class ConversorModeloProposta implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return new BigDecimal(value);
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        return new BigDecimal(value.toString()).toBigInteger().toString();
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Pedido {

    private BigDecimal Numero;
    private BigDecimal CodFil;
    private String Data;
    private String Tipo;
    private String CodCli1;
    private String NRed1;
    private String Regiao1;
    private String Hora1O;
    private String Hora2O;
    private String CodCli2;
    private String NRed2;
    private String Regiao2;
    private String Hora1D;
    private String Hora2D;
    private String CodCli3;
    private BigDecimal SeqSuprim;
    private String Solicitante;
    private String PedidoCliente;
    private BigDecimal Valor;
    private String Obs;
    private String ClassifSrv;
    private String OperIncl;
    private String RotaCanc;
    private BigDecimal SeqCanc;
    private String Dt_Incl;
    private String Hr_Incl;
    private String OS;
    private int ChequesQtde;
    private BigDecimal ChequesValor;
    private String TicketsQtde;
    private BigDecimal TicketsValor;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String OperExcl;
    private String Dt_Excl;
    private String Hr_Excl;
    private String Situacao;
    private BigDecimal SeqRota;
    private int Parada;
    private String HrOper;
    private String Flag_Excl;

    private String Ende1;
    private String Bairro1;
    private String Cidade1;
    private String Uf1;

    private String Ende2;
    private String Bairro2;
    private String Cidade2;
    private String Uf2;

    private String RegiaoDesc1;
    private String RegiaoDesc2;

    private String TipoNoPedido;

    private String ChavePrimaria;

    private String QtdeCacambas;
    private String QtdeOrcamento;

    private String DataRec;
    private String HoraRec;
    private String StatusRec;
    private String ValorRec;
    private String GuiasRec;

    private String DataEnt;
    private String HoraEnt;
    private String StatusEnt;
    private String ValorEnt;
    private String GuiasEnt;

    private String Secao;
    private String QtdeCafe;
    private String QtdeAlmoco;
    private String QtdeJanta;
    private String QtdeCeia;

    private String Cli1Lat;
    private String Cli1Lon;
    private String Cli2Lat;
    private String Cli2Lon;

    private String Cli1Nred;
    private String Cli1Nome;
    private String Cli1Ende;
    private String Cli1Bairro;
    private String Cli1Cidade;
    private String Cli1Estado;
    private String Cli1CEP;

    private String Cli2Nred;
    private String Cli2Nome;
    private String Cli2Ende;
    private String Cli2Bairro;
    private String Cli2Cidade;
    private String Cli2Estado;
    private String Cli2CEP;

    private String NumeroPesquisa;
    private String CodFilPesquisa;

    private boolean Azul;
    private String Rota;

    private String codCliReaproveitaOrigem;
    private String codCliReaproveitaDestino;

    private String tipoMoeda;

    public Pedido() {
        this.NRed1 = "";
        this.NRed2 = "";
        this.NumeroPesquisa = "";
        this.CodFilPesquisa = "";
    }

    private List<PedidoDN> listaPedidosComposicao;

    /**
     * @return the Numero
     */
    public BigDecimal getNumero() {
        return Numero;
    }

    /**
     * @param Numero the Numero to set
     */
    public void setNumero(String Numero) {
        try {
            this.Numero = new BigDecimal(Numero);
        } catch (Exception e) {
            this.Numero = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(BigDecimal CodFil) {
        try {
            this.CodFil = CodFil;
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }

    }

    /**
     * @return the Data
     */
    public String getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(String Data) {
        this.Data = Data;
    }

    /**
     * @return the Tipo
     */
    public String getTipo() {
        return Tipo;
    }

    /**
     * @param Tipo the Tipo to set
     */
    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    /**
     * @return the CodCli1
     */
    public String getCodCli1() {
        return CodCli1;
    }

    /**
     * @param CodCli1 the CodCli1 to set
     */
    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    /**
     * @return the NRed1
     */
    public String getNRed1() {
        return NRed1;
    }

    /**
     * @param NRed1 the NRed1 to set
     */
    public void setNRed1(String NRed1) {
        this.NRed1 = NRed1;
    }

    /**
     * @return the Regiao1
     */
    public String getRegiao1() {
        return Regiao1;
    }

    /**
     * @param Regiao1 the Regiao1 to set
     */
    public void setRegiao1(String Regiao1) {
        this.Regiao1 = Regiao1;
    }

    /**
     * @return the Hora1O
     */
    public String getHora1O() {
        return Hora1O;
    }

    /**
     * @param Hora1O the Hora1O to set
     */
    public void setHora1O(String Hora1O) {
        this.Hora1O = Hora1O;
    }

    /**
     * @return the Hora2O
     */
    public String getHora2O() {
        return Hora2O;
    }

    /**
     * @param Hora2O the Hora2O to set
     */
    public void setHora2O(String Hora2O) {
        this.Hora2O = Hora2O;
    }

    /**
     * @return the CodCli2
     */
    public String getCodCli2() {
        return CodCli2;
    }

    /**
     * @param CodCli2 the CodCli2 to set
     */
    public void setCodCli2(String CodCli2) {
        this.CodCli2 = CodCli2;
    }

    /**
     * @return the NRed2
     */
    public String getNRed2() {
        return NRed2;
    }

    /**
     * @param NRed2 the NRed2 to set
     */
    public void setNRed2(String NRed2) {
        this.NRed2 = NRed2;
    }

    /**
     * @return the Regiao2
     */
    public String getRegiao2() {
        return Regiao2;
    }

    /**
     * @param Regiao2 the Regiao2 to set
     */
    public void setRegiao2(String Regiao2) {
        this.Regiao2 = Regiao2;
    }

    /**
     * @return the Hora1D
     */
    public String getHora1D() {
        return Hora1D;
    }

    /**
     * @param Hora1D the Hora1D to set
     */
    public void setHora1D(String Hora1D) {
        this.Hora1D = Hora1D;
    }

    /**
     * @return the Hora2D
     */
    public String getHora2D() {
        return Hora2D;
    }

    /**
     * @param Hora2D the Hora2D to set
     */
    public void setHora2D(String Hora2D) {
        this.Hora2D = Hora2D;
    }

    /**
     * @return the CodCli3
     */
    public String getCodCli3() {
        return CodCli3;
    }

    /**
     * @param CodCli3 the CodCli3 to set
     */
    public void setCodCli3(String CodCli3) {
        this.CodCli3 = CodCli3;
    }

    /**
     * @return the SeqSuprim
     */
    public BigDecimal getSeqSuprim() {
        return SeqSuprim;
    }

    /**
     * @param SeqSuprim the SeqSuprim to set
     */
    public void setSeqSuprim(String SeqSuprim) {
        try {
            this.SeqSuprim = new BigDecimal(SeqSuprim);
        } catch (Exception e) {
            this.SeqSuprim = new BigDecimal("0");
        }
    }

    /**
     * @return the Solicitante
     */
    public String getSolicitante() {
        return Solicitante;
    }

    /**
     * @param Solicitante the Solicitante to set
     */
    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    /**
     * @return the PedidoCliente
     */
    public String getPedidoCliente() {
        return PedidoCliente;
    }

    /**
     * @param PedidoCliente the PedidoCliente to set
     */
    public void setPedidoCliente(String PedidoCliente) {
        this.PedidoCliente = PedidoCliente;
    }

    /**
     * @return the Valor
     */
    public BigDecimal getValor() {
        return Valor;
    }

    /**
     * @param Valor the Valor to set
     */
    public void setValor(BigDecimal Valor) {
        try {
            this.Valor = Valor;
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }

    }

    /**
     * @return the Obs
     */
    public String getObs() {
        return Obs;
    }

    /**
     * @param Obs the Obs to set
     */
    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    /**
     * @return the ClassifSrv
     */
    public String getClassifSrv() {
        return ClassifSrv;
    }

    /**
     * @param ClassifSrv the ClassifSrv to set
     */
    public void setClassifSrv(String ClassifSrv) {
        this.ClassifSrv = ClassifSrv;
    }

    /**
     * @return the OperIncl
     */
    public String getOperIncl() {
        return OperIncl;
    }

    /**
     * @param OperIncl the OperIncl to set
     */
    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    /**
     * @return the RotaCanc
     */
    public String getRotaCanc() {
        return RotaCanc;
    }

    /**
     * @param RotaCanc the RotaCanc to set
     */
    public void setRotaCanc(String RotaCanc) {
        this.RotaCanc = RotaCanc;
    }

    /**
     * @return the SeqCanc
     */
    public BigDecimal getSeqCanc() {
        return SeqCanc;
    }

    /**
     * @param SeqCanc the SeqCanc to set
     */
    public void setSeqCanc(String SeqCanc) {
        try {
            this.SeqCanc = new BigDecimal(SeqCanc);
        } catch (Exception e) {
            this.SeqCanc = new BigDecimal("0");
        }
    }

    /**
     * @return the Dt_Incl
     */
    public String getDt_Incl() {
        return Dt_Incl;
    }

    /**
     * @param Dt_Incl the Dt_Incl to set
     */
    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    /**
     * @return the Hr_Incl
     */
    public String getHr_Incl() {
        return Hr_Incl;
    }

    /**
     * @param Hr_Incl the Hr_Incl to set
     */
    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    /**
     * @return the OS
     */
    public String getOS() {
        return OS;
    }

    /**
     * @param OS the OS to set
     */
    public void setOS(String OS) {
        this.OS = OS;
    }

    /**
     * @return the ChequesQtde
     */
    public int getChequesQtde() {
        return ChequesQtde;
    }

    /**
     * @param ChequesQtde the ChequesQtde to set
     */
    public void setChequesQtde(int ChequesQtde) {
        this.ChequesQtde = ChequesQtde;
    }

    /**
     * @return the ChequesValor
     */
    public BigDecimal getChequesValor() {
        return ChequesValor;
    }

    /**
     * @param ChequesValor the ChequesValor to set
     */
    public void setChequesValor(String ChequesValor) {
        try {
            this.ChequesValor = new BigDecimal(ChequesValor);
        } catch (Exception e) {
            this.ChequesValor = new BigDecimal("0");
        }
    }

    /**
     * @return the TicketsQtde
     */
    public String getTicketsQtde() {
        return TicketsQtde;
    }

    /**
     * @param TicketsQtde the TicketsQtde to set
     */
    public void setTicketsQtde(String TicketsQtde) {
        this.TicketsQtde = TicketsQtde;
    }

    /**
     * @return the TicketsValor
     */
    public BigDecimal getTicketsValor() {
        return TicketsValor;
    }

    /**
     * @param TicketsValor the TicketsValor to set
     */
    public void setTicketsValor(String TicketsValor) {
        try {
            this.TicketsValor = new BigDecimal(TicketsValor);
        } catch (Exception e) {
            this.TicketsValor = new BigDecimal("0");
        }
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the OperExcl
     */
    public String getOperExcl() {
        return OperExcl;
    }

    /**
     * @param OperExcl the OperExcl to set
     */
    public void setOperExcl(String OperExcl) {
        this.OperExcl = OperExcl;
    }

    /**
     * @return the Dt_Excl
     */
    public String getDt_Excl() {
        return Dt_Excl;
    }

    /**
     * @param Dt_Excl the Dt_Excl to set
     */
    public void setDt_Excl(String Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    /**
     * @return the Hr_Excl
     */
    public String getHr_Excl() {
        return Hr_Excl;
    }

    /**
     * @param Hr_Excl the Hr_Excl to set
     */
    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }

    /**
     * @return the Situacao
     */
    public String getSituacao() {
        return Situacao;
    }

    /**
     * @param Situacao the Situacao to set
     */
    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    /**
     * @return the SeqRota
     */
    public BigDecimal getSeqRota() {
        return SeqRota;
    }

    /**
     * @param SeqRota the SeqRota to set
     */
    public void setSeqRota(String SeqRota) {
        try {
            this.SeqRota = new BigDecimal(SeqRota);
        } catch (Exception e) {
            this.SeqRota = new BigDecimal("0");
        }
    }

    /**
     * @return the Parada
     */
    public int getParada() {
        return Parada;
    }

    /**
     * @param Parada the Parada to set
     */
    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    /**
     * @return the HrOper
     */
    public String getHrOper() {
        return HrOper;
    }

    /**
     * @param HrOper the HrOper to set
     */
    public void setHrOper(String HrOper) {
        this.HrOper = HrOper;
    }

    /**
     * @return the Flag_Excl
     */
    public String getFlag_Excl() {
        return Flag_Excl;
    }

    /**
     * @param Flag_Excl the Flag_Excl to set
     */
    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public String getChavePrimaria() {
        return Situacao + ";" + Numero + ";" + CodFil + ";";
    }

    public String getTipoNoPedido() {
        return TipoNoPedido;
    }

    public void setTipoNoPedido(String TipoNoPedido) {
        this.TipoNoPedido = TipoNoPedido;
    }

    public String getEnde1() {
        return Ende1;
    }

    public void setEnde1(String Ende1) {
        this.Ende1 = Ende1;
    }

    public String getBairro1() {
        return Bairro1;
    }

    public void setBairro1(String Bairro1) {
        this.Bairro1 = Bairro1;
    }

    public String getCidade1() {
        return Cidade1;
    }

    public void setCidade1(String Cidade1) {
        this.Cidade1 = Cidade1;
    }

    public String getUf1() {
        return Uf1;
    }

    public void setUf1(String Uf1) {
        this.Uf1 = Uf1;
    }

    public String getEnde2() {
        return Ende2;
    }

    public void setEnde2(String Ende2) {
        this.Ende2 = Ende2;
    }

    public String getBairro2() {
        return Bairro2;
    }

    public void setBairro2(String Bairro2) {
        this.Bairro2 = Bairro2;
    }

    public String getCidade2() {
        return Cidade2;
    }

    public void setCidade2(String Cidade2) {
        this.Cidade2 = Cidade2;
    }

    public String getUf2() {
        return Uf2;
    }

    public void setUf2(String Uf2) {
        this.Uf2 = Uf2;
    }

    public String getRegiaoDesc1() {
        return RegiaoDesc1;
    }

    public void setRegiaoDesc1(String RegiaoDesc1) {
        this.RegiaoDesc1 = RegiaoDesc1;
    }

    public String getRegiaoDesc2() {
        return RegiaoDesc2;
    }

    public void setRegiaoDesc2(String RegiaoDesc2) {
        this.RegiaoDesc2 = RegiaoDesc2;
    }

    public String getQtdeCacambas() {
        return QtdeCacambas;
    }

    public void setQtdeCacambas(String QtdeCacambas) {
        this.QtdeCacambas = QtdeCacambas;
    }

    public String getQtdeOrcamento() {
        return QtdeOrcamento;
    }

    public void setQtdeOrcamento(String QtdeOrcamento) {
        this.QtdeOrcamento = QtdeOrcamento;
    }

    public String getDataRec() {
        return DataRec;
    }

    public void setDataRec(String DataRec) {
        this.DataRec = DataRec;
    }

    public String getHoraRec() {
        return HoraRec;
    }

    public void setHoraRec(String HoraRec) {
        this.HoraRec = HoraRec;
    }

    public String getStatusRec() {
        return StatusRec;
    }

    public void setStatusRec(String StatusRec) {
        this.StatusRec = StatusRec;
    }

    public String getValorRec() {
        return ValorRec;
    }

    public void setValorRec(String ValorRec) {
        this.ValorRec = ValorRec;
    }

    public String getGuiasRec() {
        return GuiasRec;
    }

    public void setGuiasRec(String GuiasRec) {
        this.GuiasRec = GuiasRec;
    }

    public String getDataEnt() {
        return DataEnt;
    }

    public void setDataEnt(String DataEnt) {
        this.DataEnt = DataEnt;
    }

    public String getHoraEnt() {
        return HoraEnt;
    }

    public void setHoraEnt(String HoraEnt) {
        this.HoraEnt = HoraEnt;
    }

    public String getStatusEnt() {
        return StatusEnt;
    }

    public void setStatusEnt(String StatusEnt) {
        this.StatusEnt = StatusEnt;
    }

    public String getValorEnt() {
        return ValorEnt;
    }

    public void setValorEnt(String ValorEnt) {
        this.ValorEnt = ValorEnt;
    }

    public String getGuiasEnt() {
        return GuiasEnt;
    }

    public void setGuiasEnt(String GuiasEnt) {
        this.GuiasEnt = GuiasEnt;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getQtdeCafe() {
        return QtdeCafe;
    }

    public void setQtdeCafe(String QtdeCafe) {
        this.QtdeCafe = QtdeCafe;
    }

    public String getQtdeAlmoco() {
        return QtdeAlmoco;
    }

    public void setQtdeAlmoco(String QtdeAlmoco) {
        this.QtdeAlmoco = QtdeAlmoco;
    }

    public String getQtdeJanta() {
        return QtdeJanta;
    }

    public void setQtdeJanta(String QtdeJanta) {
        this.QtdeJanta = QtdeJanta;
    }

    public String getQtdeCeia() {
        return QtdeCeia;
    }

    public void setQtdeCeia(String QtdeCeia) {
        this.QtdeCeia = QtdeCeia;
    }

    public String getCli1Lat() {
        return Cli1Lat;
    }

    public void setCli1Lat(String Cli1Lat) {
        this.Cli1Lat = Cli1Lat;
    }

    public String getCli1Lon() {
        return Cli1Lon;
    }

    public void setCli1Lon(String Cli1Lon) {
        this.Cli1Lon = Cli1Lon;
    }

    public String getCli2Lat() {
        return Cli2Lat;
    }

    public void setCli2Lat(String Cli2Lat) {
        this.Cli2Lat = Cli2Lat;
    }

    public String getCli2Lon() {
        return Cli2Lon;
    }

    public void setCli2Lon(String Cli2Lon) {
        this.Cli2Lon = Cli2Lon;
    }

    public String getCli1Nred() {
        return Cli1Nred;
    }

    public void setCli1Nred(String Cli1Nred) {
        this.Cli1Nred = Cli1Nred;
    }

    public String getCli1Nome() {
        return Cli1Nome;
    }

    public void setCli1Nome(String Cli1Nome) {
        this.Cli1Nome = Cli1Nome;
    }

    public String getCli1Ende() {
        return Cli1Ende;
    }

    public void setCli1Ende(String Cli1Ende) {
        this.Cli1Ende = Cli1Ende;
    }

    public String getCli1Bairro() {
        return Cli1Bairro;
    }

    public void setCli1Bairro(String Cli1Bairro) {
        this.Cli1Bairro = Cli1Bairro;
    }

    public String getCli1Cidade() {
        return Cli1Cidade;
    }

    public void setCli1Cidade(String Cli1Cidade) {
        this.Cli1Cidade = Cli1Cidade;
    }

    public String getCli1Estado() {
        return Cli1Estado;
    }

    public void setCli1Estado(String Cli1Estado) {
        this.Cli1Estado = Cli1Estado;
    }

    public String getCli1CEP() {
        return Cli1CEP;
    }

    public void setCli1CEP(String Cli1CEP) {
        this.Cli1CEP = Cli1CEP;
    }

    public String getCli2Nred() {
        return Cli2Nred;
    }

    public void setCli2Nred(String Cli2Nred) {
        this.Cli2Nred = Cli2Nred;
    }

    public String getCli2Nome() {
        return Cli2Nome;
    }

    public void setCli2Nome(String Cli2Nome) {
        this.Cli2Nome = Cli2Nome;
    }

    public String getCli2Ende() {
        return Cli2Ende;
    }

    public void setCli2Ende(String Cli2Ende) {
        this.Cli2Ende = Cli2Ende;
    }

    public String getCli2Bairro() {
        return Cli2Bairro;
    }

    public void setCli2Bairro(String Cli2Bairro) {
        this.Cli2Bairro = Cli2Bairro;
    }

    public String getCli2Cidade() {
        return Cli2Cidade;
    }

    public void setCli2Cidade(String Cli2Cidade) {
        this.Cli2Cidade = Cli2Cidade;
    }

    public String getCli2Estado() {
        return Cli2Estado;
    }

    public void setCli2Estado(String Cli2Estado) {
        this.Cli2Estado = Cli2Estado;
    }

    public String getCli2CEP() {
        return Cli2CEP;
    }

    public void setCli2CEP(String Cli2CEP) {
        this.Cli2CEP = Cli2CEP;
    }

    public boolean isAzul() {
        return Azul;
    }

    public void setAzul(boolean Azul) {
        this.Azul = Azul;
    }

    public List<PedidoDN> getListaPedidosComposicao() {
        return listaPedidosComposicao;
    }

    public void setListaPedidosComposicao(List<PedidoDN> listaPedidosComposicao) {
        this.listaPedidosComposicao = listaPedidosComposicao;
    }

    public String getNumeroPesquisa() {
        return NumeroPesquisa;
    }

    public void setNumeroPesquisa(String NumeroPesquisa) {
        this.NumeroPesquisa = NumeroPesquisa;
    }

    public String getCodFilPesquisa() {
        return CodFilPesquisa;
    }

    public void setCodFilPesquisa(String CodFilPesquisa) {
        this.CodFilPesquisa = CodFilPesquisa;
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getCodCliReaproveitaOrigem() {
        return codCliReaproveitaOrigem;
    }

    public void setCodCliReaproveitaOrigem(String codCliReaproveitaOrigem) {
        this.codCliReaproveitaOrigem = codCliReaproveitaOrigem;
    }

    public String getCodCliReaproveitaDestino() {
        return codCliReaproveitaDestino;
    }

    public void setCodCliReaproveitaDestino(String codCliReaproveitaDestino) {
        this.codCliReaproveitaDestino = codCliReaproveitaDestino;
    }

    public String getTipoMoeda() {
        return tipoMoeda;
    }

    public void setTipoMoeda(String tipoMoeda) {
        this.tipoMoeda = tipoMoeda;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 37 * hash + Objects.hashCode(this.Numero);
        hash = 37 * hash + Objects.hashCode(this.CodFil);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Pedido other = (Pedido) obj;
        if (!Objects.equals(this.Numero, other.Numero)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "Pedido{" + "Numero=" + Numero + ", CodFil=" + CodFil + '}';
    }
}

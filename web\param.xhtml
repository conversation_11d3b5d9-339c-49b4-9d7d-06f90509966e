<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="assets/css/param.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/font-awesome.min.css" rel="stylesheet" />
            <style>
                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    .sate-login-primo{
                        margin-top:150px !important;
                    }
                    
                    .btnLogar{
                        position:absolute;
                        right:0px;
                    }
                    
                    .rodape-links{
                        position:absolute;
                        left:0px;
                        width:88px;
                    }
                }
                
                @media(max-height: 720px){
                    #divImg27Anos{
                        display: none !important
                    }
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{login.ListarFiliais}" />
            </f:metadata>
            <p:growl id="msgs" />
            <h:form id="formFil" class="form-inline">
                <div id="divImg27Anos" style="position: absolute; right: 0; left: 0; bottom: 50px; margin: auto; width: 200px; text-align: center; height: 60px;"><a href="https://gruposas.com.br" target="_blank"><img src="https://gruposas.com.br/wp-content/uploads/2021/01/saswbrlogo.png" style="height: 50px" /></a></div>
                <div class="h-wrapper-com-footer" style="background: url('assets/img/#{login.cli eq null or login.cli eq ''?'st_back.png':''}') no-repeat top center; background-size: 62%;">
                    <div style="#{login.cli eq null or login.cli eq ''? 'display: none;':''}position: absolute; top: -0px; right: 0; left: 0; bottom: 0; margin: auto; width: 200px; text-align: center; height: 100px;"><img src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_#{login.cli}.png" style="max-height: 100px" /></div>
                    
                    
                    <div class="sate-login-primo">
                        <div class="form-group">
                            <div class="mt" style="color:black">#{localemsgs.Empresa}:</div>
                            <p:selectOneMenu id="param" value="#{login.empresa}" converter="omnifaces.SelectItemsConverter" style="height: 40px;"
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Empresa}" filter="true"
                                             filterMatchMode="contains" >
                                <f:selectItems value="#{login.empresas}" var="empresas" itemValue="#{empresas}"
                                               itemLabel="#{login.NomeEmpresa(empresas.bancoDados)}"  noSelectionValue="Selecione"/>
                                <p:ajax event="itemSelect" listener="#{login.ListarFiliais}" update="formFil :msgs" />
                            </p:selectOneMenu>
                        </div>

                        <div class="form-group">
                            <p:focus context="formFil:subFil"/>
                            <div class="mt" style="color:black">#{localemsgs.Filial}:</div>

                            <p:selectOneMenu id="subFil" value="#{login.filial}" converter="omnifaces.SelectItemsConverter" 
                                             styleClass="form-control" required="true" 
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                             style="width: 100%; height: 40px;"
                                             filter="true" filterMatchMode="contains" >
                                <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}"
                                               itemLabel="#{filiais.descricao}"  noSelectionValue="Selecione"/>
                            </p:selectOneMenu>

                        </div>
                        <div class="btnLogar">
                            <div class="mt hidden-xs">&nbsp;</div>
                            <p:commandLink id="btnLogar" 
                                           update=":msgs" action="#{login.SelecionarFilial}">
                                <p:graphicImage url="assets/img/icone_ok.png" width="40" height="40" />
                            </p:commandLink>
                            <p:defaultCommand target="btnLogar"/>
                        </div>
                    </div>
                    <div class="rodape-links">
                        <ul>
                            <li class="rodape-text">
                            </li>
                            <li class="rodape-text">
                                <p:commandLink id="btnLogout" action="#{login.logOut}" style="color:black">
                                    <p:graphicImage url="assets/img/icone_voltar_branco.png"  height="30" width="30" /> #{localemsgs.Voltar}
                                </p:commandLink>
                            </li>
                        </ul>
                    </div>
                </div>
            </h:form>
            
            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>
           
            <footer>
            <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                <div class="container" style="min-height:10px !important;max-height:40px !important;">
                    <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                        <table class="footer-time" style="min-height:10px !important">
                            <tr>
                                <td>
                                    <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                        <table class="footer-user" style="min-height:40px !important">
                            <tr>
                                <td style="vertical-align:middle !important">#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                        <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <tr>
                                <td><img src="assets/img/logo_satweb.png" /></td>
                                <td>
                                    <h:form>
                                        <h:commandLink actionListener="#{localeController.increment}" 
                                                       action="#{localeController.getLocales}" >
                                            <p:graphicImage url="assets/img/#{localeController.number}.png" height="25" />
                                        </h:commandLink>
                                    </h:form>   
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </footer>
        </h:body>
    </f:view> 
</html>

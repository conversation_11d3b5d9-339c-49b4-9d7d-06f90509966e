/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import Controller.SatMobEW.SatMobEWSatWeb;
import Dados.Persistencia;
import SasBeans.Rondas;
import SasBeansCompostas.LogsSatMobEW;
import br.com.sasw.lazydatamodels.LogsSatMobEWLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.chart.HorizontalBarChartModel;
import org.primefaces.model.map.DefaultMapModel;
import org.primefaces.model.map.MapModel;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "inspecaoVeiculo")
@ViewScoped
public class InspecaoVeiculoMB implements Serializable {

    private BigDecimal codPessoa;
    private Persistencia persistencia, satellite;
    private String codFil, banco, operador, nomeFilial, caminho, log, secao, fotoRelatorio, dataTela, imagemTipo, html, centroMapa, zoomMapa,
            titulo, senhaAtual, novaSenha, matricula, nivel;
    private static Date ultimoDia;
    private ArquivoLog logerro;
    private SatMobEWSatWeb satMobEWSatWeb;
    private Map filters;
    private int posFotoRelatorio, total;
    private List<String> fotosRelatorio;
    private List<Rondas> rondas;
    private LazyDataModel<LogsSatMobEW> logsSatMobEWs;
    private LogsSatMobEW logsSatMobEWSelecionado;
    private MapModel mapa;
    private HorizontalBarChartModel graficoRonda;
    private StreamedContent arquivoRelatorio;
    private boolean supervisor, filtroWeb;

    public InspecaoVeiculoMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        secao = (String) fc.getExternalContext().getSessionMap().get("posto");
        nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
        matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        satMobEWSatWeb = new SatMobEWSatWeb();

        dataTela = DataAtual.getDataAtual("SQL");
        ultimoDiadoMes();

        mapa = new DefaultMapModel();
        supervisor = !nivel.equals("8");
    }

    public void Persistencia(Persistencia pp, Persistencia satellite) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.satellite = satellite;
            if (null == this.satellite) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.filters = new HashMap();
            this.filters.put("data", this.dataTela);
            this.filters.put("codfil", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.put("posto", this.secao == null ? "" : this.secao);
            this.filters.put("matricula", "");
            this.filters.put("codpessoa", "");
            this.filters.put("filtroWeb", (this.matricula == null || this.matricula.equals("0") || this.matricula.equals(""))
                    && !this.supervisor ? "1" : "0");
            this.total = this.satMobEWSatWeb.contagem(this.filters, !this.supervisor ? this.codPessoa : null, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<LogsSatMobEW> getAllLogs() {
        if (this.logsSatMobEWs == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace("data", this.dataTela);
            this.filters.replace("codfil", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.replace("posto", this.secao == null ? "" : this.secao);
            //dt.setFilters(this.filters);
            this.logsSatMobEWs = new LogsSatMobEWLazyList(this.persistencia, !this.supervisor ? this.codPessoa : null, this.filters);
        }
        try {
            this.total = this.satMobEWSatWeb.contagem(this.filters, !this.supervisor ? this.codPessoa : null, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.logsSatMobEWs;
    }

    public void dataAnterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            this.dataTela = tesedata.format(dtbefore);
            this.filters.replace("data", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllLogs();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosterior() {
        try {
            Calendar calendar = Calendar.getInstance();
            Date date = new SimpleDateFormat("yyyyMMdd").parse(this.dataTela);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, +1);
            Date dtbefore = calendar.getTime();
            SimpleDateFormat tesedata = new SimpleDateFormat("yyyyMMdd");
            dataTela = tesedata.format(dtbefore);
            this.filters.replace("data", this.dataTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllLogs();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarData(SelectEvent data) {
        Date date = (Date) data.getObject();
        this.dataTela = Date2String(date);
        this.filters.replace("data", this.dataTela);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllLogs();
        dt.setFirst(0);
    }

    /**
     * Tranforma Date em String
     *
     * @param date Data a ser formatada
     * @return String no formato yyyyMMdd
     */
    public String Date2String(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * Procura o último dia do mês atual
     */
    public static void ultimoDiadoMes() {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(new Date());

        int dia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int mes = (calendar.get(Calendar.MONDAY) + 1);
        int ano = calendar.get(Calendar.YEAR);

        try {
            ultimoDia = (new SimpleDateFormat("yyyy-MM-dd")).parse(ano + "-" + mes + "-" + dia);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void rowDblselect(SelectEvent event) {
        this.logsSatMobEWSelecionado = (LogsSatMobEW) event.getObject();
        buttonAction();
    }

    public void rowSelect(SelectEvent event) {
        this.logsSatMobEWSelecionado = (LogsSatMobEW) event.getObject();
    }

    public void buttonAction() {
        if (null == this.logsSatMobEWSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneRelatorio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.html = "";
                this.posFotoRelatorio = 0;
                this.fotosRelatorio = new ArrayList<>();
                this.fotoRelatorio = "";
                this.rondas = new ArrayList<>();
                gerarRelatorio();
                PrimeFaces.current().resetInputs("relatorio:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgRelatorio').show();");
            } catch (Exception e) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void gerarRelatorio() throws Exception {
        String foto;
        String idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
//        switch(this.logsSatMobEWSelecionado.getTipo()){
//            case "1":
//                if(this.logsSatMobEWSelecionado.getFotos() > 0){
//                    foto = "https://mobile.sasw.com.br:9091/satellite/fotos/"+this.persistencia.getEmpresa()+"/ponto/";
//                    foto += FuncoesString.RecortaString(this.logsSatMobEWSelecionado.getChave().split(";")[0], 0, 10).replaceAll("-", "")+ "/";
//                    foto += FuncoesString.PreencheEsquerda(this.logsSatMobEWSelecionado.getChave().split(";")[1].replace(".0", ""), 8, "0")
//                            + "_" + this.logsSatMobEWSelecionado.getFotos() + ".jpg";
//                    this.fotosRelatorio.add(foto);
//                    this.fotoRelatorio = this.fotosRelatorio.get(0);
//                }
//                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioBatida(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
//                this.mapa = new DefaultMapModel();
//                this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
//                        new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
//                        this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
//                        "https://mobile.sasw.com.br:9091/satmobile/img/pin_funcionario.png"));
//                this.centroMapa = this.logsSatMobEWSelecionado.getLatitude()+","+this.logsSatMobEWSelecionado.getLongitude();
//                this.zoomMapa = "17";
//                this.titulo = Messages.getMessageS(this.logsSatMobEWSelecionado.getFotos() % 2 == 0 ? "CheckOut" : "CheckIn");
//                break;
//            case "2":
//                if(this.logsSatMobEWSelecionado.getFotos() > 0){
//                    for(int i = 1; i <= this.logsSatMobEWSelecionado.getFotos(); i++){
//                        foto = "https://mobile.sasw.com.br:9091/satellite/fotos/"+this.persistencia.getEmpresa()+"/";
//                        foto += FuncoesString.RecortaString(this.logsSatMobEWSelecionado.getData(), 0, 10).replaceAll("-", "")+ "/";
//                        foto += FuncoesString.PreencheEsquerda(this.logsSatMobEWSelecionado.getChave().split(";")[0]
//                                .replace(".0", ""), 8, "0") + "_" + i + ".jpg";
//
//                        this.fotosRelatorio.add(foto);
//                    }
//                    this.fotoRelatorio = this.fotosRelatorio.get(0);
//                }
//                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioReport(this.logsSatMobEWSelecionado, idioma, false, this.persistencia));
//                this.mapa = new DefaultMapModel();
//                this.mapa.addOverlay(new Marker(new LatLng(new BigDecimal(this.logsSatMobEWSelecionado.getLatitude()).doubleValue(),
//                        new BigDecimal(this.logsSatMobEWSelecionado.getLongitude()).doubleValue()),
//                        this.logsSatMobEWSelecionado.getFuncionario(), this.logsSatMobEWSelecionado,
//                        "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
//                this.centroMapa = this.logsSatMobEWSelecionado.getLatitude()+","+this.logsSatMobEWSelecionado.getLongitude();
//                this.zoomMapa = "17";
//                this.titulo = Messages.getMessageS("Relatorio");
//                
//                this.filtroWeb = this.satMobEWSatWeb.isFiltroWeb(this.logsSatMobEWSelecionado, this.persistencia);
//                break;
//            case "3":
//                this.rondas = this.satMobEWSatWeb.obterRondas(this.logsSatMobEWSelecionado, this.persistencia);
//                this.html = Messages.replaceTags(this.satMobEWSatWeb.getRelatorioRondas(this.logsSatMobEWSelecionado, this.rondas,
//                        idioma, false, this.persistencia));
//                this.mapa = new DefaultMapModel();
//                Polyline polyline = new Polyline();
//                List<LatLng> latLngs = new ArrayList<>();
//                LatLng latlng;
//                for(Rondas ronda : this.rondas){
//                    latlng = new LatLng(new BigDecimal(ronda.getLatitude()).doubleValue(),
//                            new BigDecimal(ronda.getLongitude()).doubleValue());
//                    latLngs.add(latlng);
//                    polyline.getPaths().add(latlng);
//                    this.mapa.addOverlay(new Marker(latlng, ronda.getDescricao()+": "+Messages.getHoraS(ronda.getHr_alter(), "HH:mm"),
//                            this.logsSatMobEWSelecionado, "https://mobile.sasw.com.br:9091/satmobile/img/pin_geral.png"));
//                }
//                latlng = Coordenadas.getCentroMapa(latLngs);
//                this.centroMapa = latlng.getLat()+","+latlng.getLng();
//                polyline.setStrokeWeight(10);
//                polyline.setStrokeColor("#FF9900");
//                polyline.setStrokeOpacity(0.7);
//                this.mapa.addOverlay(polyline);
//                this.zoomMapa = "19";
//                this.titulo = Messages.getMessageS("Ronda");
//                
//                this.graficoRonda = new HorizontalBarChartModel();
//                
//                String chaveRonda[] = this.logsSatMobEWSelecionado.getDetalhes().split(";");
//                BigDecimal rondasHora   = new BigDecimal(chaveRonda[2]);
//                BigDecimal rondasTotal  = new BigDecimal(chaveRonda[3]);
//                
//                ChartSeries completas = new ChartSeries();
//                completas.setLabel(Messages.getMessageS("Completas"));
//                completas.set("",rondasHora);
//                
//                ChartSeries total = new ChartSeries();
//                total.setLabel(Messages.getMessageS("Total"));
//                total.set("",rondasTotal);
//                
//                this.graficoRonda.setTitle(Messages.getMessageS("Conformidade"));
//                this.graficoRonda.addSeries(completas);
//                this.graficoRonda.addSeries(total);
//                this.graficoRonda.setStacked(true);
//                this.graficoRonda.setLegendPosition("e");
//                this.graficoRonda.setShowDatatip(false);
//                this.graficoRonda.setShadow(false);
//                
//                break;
//        }

    }

    public void gerarRelatorioDownload() throws Exception {
        String idioma = FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
        InputStream stream = null;
        String nomeArquivo = "file.pdf";
        switch (this.logsSatMobEWSelecionado.getTipo()) {
            case "1":
                nomeArquivo = this.titulo + "_" + this.logsSatMobEWSelecionado.getFotos()
                        + "_" + this.logsSatMobEWSelecionado.getChave().split(";")[1].replace(".0", "")
                        + "_" + this.logsSatMobEWSelecionado.getChave().split(";")[0].replace("-", "") + ".pdf";
                stream = new ByteArrayInputStream(Messages.replaceTags(
                        this.satMobEWSatWeb.getRelatorioBatida(this.logsSatMobEWSelecionado, idioma, true, this.persistencia)).getBytes());
                break;
            case "2":
                nomeArquivo = "report_" + this.logsSatMobEWSelecionado.getChave().split(";")[0].replace(".0", "") + ".pdf";
                stream = new ByteArrayInputStream(Messages.replaceTags(
                        this.satMobEWSatWeb.getRelatorioReport(this.logsSatMobEWSelecionado, idioma, true, this.persistencia)).getBytes());
                break;
            case "3":
                nomeArquivo = this.titulo + "_" + this.logsSatMobEWSelecionado.getChave().split(";")[0]
                        + this.logsSatMobEWSelecionado.getChave().split(";")[1] + "_" + "pdf";
                stream = new ByteArrayInputStream(Messages.replaceTags(
                        this.satMobEWSatWeb.getRelatorioRondas(this.logsSatMobEWSelecionado, this.rondas, idioma, true, this.persistencia)).getBytes());
                break;
        }

        ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
        ITextRenderer renderer = new ITextRenderer();
        Tidy tidy = new Tidy();
        tidy.setShowWarnings(false);
        Document doc = tidy.parseDOM(stream, null);
        renderer.setDocument(doc, null);
        renderer.layout();
        renderer.createPDF(osPdf);

        InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

        arquivoRelatorio = new DefaultStreamedContent(inputPDF, "pdf", nomeArquivo);
        osPdf.close();
        stream.close();
        inputPDF.close();

    }

    public void avancarFotoRelatorio() {
        if (this.posFotoRelatorio + 1 == this.fotosRelatorio.size()) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosFim"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoRelatorio = this.fotosRelatorio.get(this.posFotoRelatorio + 1);
            this.posFotoRelatorio = this.posFotoRelatorio + 1;
        }
    }

    public void voltarFotoRelatorio() {
        if (this.posFotoRelatorio == 0) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SemMaisFotosInicio"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.fotoRelatorio = this.fotosRelatorio.get(this.posFotoRelatorio - 1);
            this.posFotoRelatorio = this.posFotoRelatorio - 1;
        }
    }

    public void trocarSenha() {
        try {
            Boolean senhaValida = this.satMobEWSatWeb.verificaSenha(this.codPessoa, this.senhaAtual, this.persistencia);
            if (senhaValida) {
                this.satMobEWSatWeb.trocarSenhaCliente(this.codPessoa, this.novaSenha,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia, this.satellite);
            } else {
                throw new Exception("SenhaIncorreta");
            }
            PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
            PrimeFaces.current().executeScript("PF('dlgOk').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaAlteradaSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarFiltroWeb() {
        try {
            this.satMobEWSatWeb.atualizaFiltroWeb(this.logsSatMobEWSelecionado, this.filtroWeb, this.persistencia);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS(this.filtroWeb ? "RelatorioTornadoPublico" : "RelatorioTornadoPrivado"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public String getFotoRelatorio() {
        return fotoRelatorio;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public LogsSatMobEW getLogsSatMobEWSelecionado() {
        return logsSatMobEWSelecionado;
    }

    public void setLogsSatMobEWSelecionado(LogsSatMobEW logsSatMobEWSelecionado) {
        this.logsSatMobEWSelecionado = logsSatMobEWSelecionado;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public String getImagemTipo(String tipo) {
        switch (tipo) {
            case "1":
                imagemTipo = "../assets/img/icone_batidadeponto.png";
                break;
            case "2":
                imagemTipo = "../assets/img/icone_relatorio.png";
                break;
            case "3":
                imagemTipo = "../assets/img/icone_ronda.png";
                break;
        }
        return imagemTipo;
    }

    public void setImagemTipo(String imagemTipo) {
        this.imagemTipo = imagemTipo;
    }

    public Date getUltimoDia() {
        return ultimoDia;
    }

    public List<String> getFotosRelatorio() {
        return fotosRelatorio;
    }

    public void setFotosRelatorio(List<String> fotosRelatorio) {
        this.fotosRelatorio = fotosRelatorio;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public MapModel getMapa() {
        return mapa;
    }

    public void setMapa(MapModel mapa) {
        this.mapa = mapa;
    }

    public String getCentroMapa() {
        return centroMapa;
    }

    public void setCentroMapa(String centroMapa) {
        this.centroMapa = centroMapa;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getZoomMapa() {
        return zoomMapa;
    }

    public void setZoomMapa(String zoomMapa) {
        this.zoomMapa = zoomMapa;
    }

    public String getSenhaAtual() {
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) {
        this.senhaAtual = senhaAtual;
    }

    public String getNovaSenha() {
        return novaSenha;
    }

    public void setNovaSenha(String novaSenha) {
        this.novaSenha = novaSenha;
    }

    public List<Rondas> getRondas() {
        return rondas;
    }

    public void setRondas(List<Rondas> rondas) {
        this.rondas = rondas;
    }

    public HorizontalBarChartModel getGraficoRonda() {
        return graficoRonda;
    }

    public void setGraficoRonda(HorizontalBarChartModel graficoRonda) {
        this.graficoRonda = graficoRonda;
    }

    public StreamedContent getArquivoRelatorio() {
        return arquivoRelatorio;
    }

    public void setArquivoRelatorio(StreamedContent arquivoRelatorio) {
        this.arquivoRelatorio = arquivoRelatorio;
    }

    public boolean isSupervisor() {
        return !this.nivel.equals("8");
    }

    public void setSupervisor(boolean supervisor) {
        this.supervisor = supervisor;
    }

    public boolean isFiltroWeb() {
        return filtroWeb;
    }

    public void setFiltroWeb(boolean filtroWeb) {
        this.filtroWeb = filtroWeb;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

}

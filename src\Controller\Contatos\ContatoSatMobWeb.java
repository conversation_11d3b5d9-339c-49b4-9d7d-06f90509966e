/*
 */
package Controller.Contatos;

import Dados.Persistencia;
import SasBeans.Contatos;
import SasDaos.ContatosDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ContatoSatMobWeb {

    /**
     * Contagem do cadastro de contatos
     *
     * @param filtros - filtros para pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            ContatosDao contatosdao = new ContatosDao();
            retorno = contatosdao.totalContatos(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("contatos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de contatos
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Contatos> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {

        try {
            List<Contatos> retorno;
            ContatosDao contatosdao = new ContatosDao();
            retorno = contatosdao.listaPaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("contatos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cadastrar um novo contato
     *
     * @param contato
     * @param persistencia
     * @throws Exception
     */
    public void cadastrar(Contatos contato, Persistencia persistencia) throws Exception {
        try {
            ContatosDao contatosdao = new ContatosDao();
            contatosdao.cadastrar(contato, persistencia);
        } catch (Exception e) {
            throw new Exception("contatos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Atualiza dados de um contato
     *
     * @param contato
     * @param persistencia
     * @throws Exception
     */
    public void editar(Contatos contato, Persistencia persistencia) throws Exception {
        try {
            ContatosDao contatosdao = new ContatosDao();
            contatosdao.atualizar(contato, persistencia);
        } catch (Exception e) {
            throw new Exception("contatos.falhageral<message>" + e.getMessage());
        }
    }
}

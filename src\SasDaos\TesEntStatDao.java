/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesEntStat;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class TesEntStatDao {

    /**
     * Busca um registro específico na tabela TesEntStat
     *
     * @param guia Valor da guia
     * @param serie Valor da série
     * @param docto Número da sangria
     * @param persistencia Conexão com o banco
     * @return Retorna um registro específico
     * @throws Exception
     */
    public TesEntStat BuscaTesEntStat(BigDecimal guia, String serie, String docto, Persistencia persistencia) throws Exception {
        TesEntStat retorno = new TesEntStat();

        try {
            String sql = "SELECT * FROM TesEntStat"
                    + "WHERE guia = ? "
                    + "AND serie = ? "
                    + "AND docto = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = new TesEntStat();
                retorno.setGuia(consulta.getString("guia"));
                retorno.setSerie(consulta.getString("serie"));
                retorno.setDocto(consulta.getString("docto"));
                retorno.setMatr(consulta.getString("matr"));
                retorno.setCodPessoa(consulta.getString("codpessoa"));
                retorno.setCamera(consulta.getInt("camera"));
                retorno.setHrInicio(consulta.getString("hrInicio"));
                retorno.setHrFinal(consulta.getString("hrfinal"));
                retorno.setTempo(consulta.getBigDecimal("tempo"));
                retorno.setQtdeDH(consulta.getString("qtdedh"));
                retorno.setQtdeMD(consulta.getString("qtdemd"));
                retorno.setOperador(consulta.getString("operador"));
                retorno.setDt_Alter(consulta.getLocalDate("dt_alter"));
                retorno.setHr_Alter(consulta.getString("hr_alter"));

            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao Busca em TesEntStat - " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Verifica se existe a sangria
     *
     * @param serie Valor da série
     * @param guia Valor da guia
     * @param docto Número da sangria
     * @param persistencia Conexão com o banco de dados
     * @return Retorna True se existe Sangria
     * @throws Exception
     */
    public Boolean ExisteSangria(String serie, String guia, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "SELECT guia, serie, docto from tesentstat"
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.setString(docto);
            consult.select();
            String s = new String(), g = new String(), d = new String();
            while (consult.Proximo()) {
                s = consult.getString("serie");
                g = consult.getString("guia");
                d = consult.getString("docto");
            }
            consult.Close();
            if (s.equals(serie) && g.contains(guia) && d.equals(docto)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new Exception("Falha ao buscar sangria - " + e.getMessage());
        }
    }

    /**
     * Inicia a contagem da sangria
     *
     * @param serie Valor da série
     * @param guia Valor da guia
     * @param docto Número da sangria
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void IniciaContagem(String serie, String guia, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "update tesentstat set hrinicio = ?"
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar tesentstat - " + e.getMessage());
        }
    }

    /**
     * Finaliza a contagem da sangria fazendo a atualização dos valores de hora
     * final e tempo
     *
     * @param serie Valor da série
     * @param guia Valor da guia
     * @param docto Número da sangria
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void FinalizaContagem(String serie, String guia, String docto, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "SELECT hrinicio from tesentstat"
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.setString(docto);
            consult.select();
            String hrinicio = new String();
            while (consult.Proximo()) {
                hrinicio = consult.getString("hrinicio");
            }
            consult.Close();
            String hrfinal = DataAtual.getDataAtual("HORA");
            hrfinal = hrfinal.replace(":", "");
            hrinicio = hrinicio.replace(":", "");
            int tempo;
            try {
                tempo = Integer.parseInt(hrfinal) - Integer.parseInt(hrinicio);
            } catch (Exception e2) {
                tempo = 0;
            }

            sql = "update tesentstat set hrfinal = ?, tempo = ?"
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setInt(tempo);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar tesentstat - " + e.getMessage());
        }
    }

    /**
     * Insere os valores na tabela TesEntStat pelo mobile
     *
     * @param tesEntStat Objeto TesEntStat
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void InsereMobile(TesEntStat tesEntStat, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "insert into tesentstat (guia, serie, docto, matr, codpessoa, camera, hrinicio, dt_alter, hr_alter, operador)"
                    + " values (?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(tesEntStat.getGuia());
            consulta.setString(tesEntStat.getSerie());
            consulta.setString(tesEntStat.getDocto());
            consulta.setBigDecimal(tesEntStat.getMatr());
            consulta.setBigDecimal(tesEntStat.getCodPessoa());
            consulta.setInt(tesEntStat.getCamera());
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(tesEntStat.getOperador());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir em tesentstat - " + e.getMessage());
        }
    }
}

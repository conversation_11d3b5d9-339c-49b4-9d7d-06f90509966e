/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.faturamento;

import Arquivo.ArquivoLog;
import Controller.NFiscal.NFiscalSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.NFiscal;
import SasBeans.SasPWFill;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcento;
import br.com.sasw.lazydatamodels.NFiscalLazyList;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named(value = "nfiscal")
@ViewScoped
public class NFiscalMB implements Serializable {

    private final NFiscalSatMobWeb nFiscalSatMobWeb;
    private Persistencia persistencia;
    private final ArquivoLog logerro;
    private final String nomeFilial, banco, operador, caminho;
    private String log, data1, data2, nomeArquivo, caminhoArquivo, codfil;
    private Date dataSelecionada1, dataSelecionada2;
    private final BigDecimal codPessoa;
    private LazyDataModel<NFiscal> nFiscais = null;
    private NFiscal selecionado;
    private Map filters;
    private int total;
    private StreamedContent arquivoDownload;
    private UploadedFile arquivoUpload;
    private SasPWFill filial;
    private boolean mostrarFiliais, limparFiltros, mostrarAtivos;
    private Clientes cliente;
    private List<Clientes> clientes;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;

    public NFiscalMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        rotassatweb = new RotasSatWeb();

        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataSelecionada2 = c.getTime();
        data2 = dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataSelecionada1 = c.getTime();
        data1 = dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        nFiscalSatMobWeb = new NFiscalSatMobWeb();
        logerro = new ArquivoLog(this.getClass().getSimpleName());
    }

    public void Persistencias(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }

            this.filters = new HashMap();
            this.filters.put(" Data > ? ", this.data2);
            this.filters.put(" Data < ? ", this.data1);
            this.filters.put(" NFiscal.CodFil = ? ", this.codfil);
            this.filters.put(" NFiscal.Serie = ? ", "");
            this.filters.put(" NFiscal.Praca = ? ", "");
            this.filters.put(" NFiscal.Numero = ? ", "");
            this.filters.put(" NFiscal.Situacao = ? ", "A");
            this.filters.put(" NFiscal.CliFat = ? ", "");

            this.mostrarAtivos = true;

            this.total = this.nFiscalSatMobWeb.total(this.filters, this.codPessoa, this.persistencia);
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../index.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<NFiscal> getAllNFiscal() {
        if (this.nFiscais == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace(" Data > ? ", this.data2);
            this.filters.replace(" Data < ? ", this.data1);
            this.filters.replace(" NFiscal.CodFil = ? ", this.codfil);
            this.filters.replace(" NFiscal.Serie = ? ", "");
            this.filters.replace(" NFiscal.Praca = ? ", "");
            this.filters.replace(" NFiscal.Numero = ? ", "");
            this.filters.replace(" NFiscal.Situacao = ? ", "A");
            this.filters.replace(" NFiscal.CliFat = ? ", "");
            dt.setFilters(this.filters);
            this.nFiscais = new NFiscalLazyList(this.persistencia, this.codPessoa);
        }
        try {
            this.total = this.nFiscalSatMobWeb.total(this.filters, this.codPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.nFiscais;
    }

    public void selecionarData() {
        try {
            this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (this.dataSelecionada2.after(this.dataSelecionada1)) {
                throw new Exception("IntervaloInvalido");
            }

            this.filters.replace(" Data > ? ", this.data2);
            this.filters.replace(" Data < ? ", this.data1);

            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            gerarLog("Listando guias");
            getAllNFiscal();
            dt.setFirst(0);

            this.total = this.nFiscalSatMobWeb.total(this.filters, this.codPessoa, this.persistencia);

            PrimeFaces.current().ajax().update("msgs", "main", "cabecalho");
            PrimeFaces.current().executeScript("PF('oCalendarios').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarDownloadDanfe() {

        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneNFiscal"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.nomeArquivo = "DANFE_" + this.selecionado.getNumeroFormatado() + "_" + this.selecionado.getNRed() + ".pdf";

//                this.caminhoArquivo = "http://cliente.sasw.com.br/" 
                this.caminhoArquivo = "http://*************/"
                        + removeAcento(getMessageS(this.persistencia.getEmpresa())) + "/RPS/"
                        + FuncoesString.PreencheEsquerda(this.codfil, 4, "0") + "/DANFE_"
                        + this.selecionado.getNumeroFormatado() + "_" + this.selecionado.getNRed() + ".pdf";

                InputStream stream = new URL(this.caminhoArquivo.replace(" ", "%20")).openStream();
                this.arquivoDownload = new DefaultStreamedContent(stream, "pdf", this.nomeArquivo);

//                stream.close();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ArquivoNaoEncontrado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void gerarDownloadNfe() {

        if (null == this.selecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneNFiscal"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.nomeArquivo = "NFE_" + FuncoesString.PreencheEsquerda(this.selecionado.getNumero().replace(".0", ""), 5, "0") + ".xml";

//                this.caminhoArquivo = "http://cliente.sasw.com.br/"
                this.caminhoArquivo = "http://*************/"
                        + removeAcento(getMessageS(this.persistencia.getEmpresa())) + "/RPS/"
                        + FuncoesString.PreencheEsquerda(this.codfil, 4, "0") + "/NFE_"
                        + FuncoesString.PreencheEsquerda(this.selecionado.getNumero().replace(".0", ""), 5, "0") + ".xml";

                InputStream stream = new URL(this.caminhoArquivo.replace(" ", "%20")).openStream();
                this.arquivoDownload = new DefaultStreamedContent(stream, "xml", this.nomeArquivo);

//                stream.close();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ArquivoNaoEncontrado"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void realizarUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                this.arquivoUpload = fileUploadEvent.getFile();
                String boundary = Long.toHexString(System.currentTimeMillis()); // Just generate some unique random value.
                String CRLF = "\r\n"; // Line separator required by multipart/form-data.

//                URLConnection connection = new URL("http://cliente.sasw.com.br/SendFile.php").openConnection();
                URLConnection connection = new URL("http://*************/SendFile.php").openConnection();
                connection.setDoOutput(true);
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

                OutputStream output = connection.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, Charset.forName("UTF-8")), true);

                // Enviando caminho para salvar o arquivo
                writer.append("--" + boundary).append(CRLF);
                writer.append("Content-Disposition: form-data; name=\"path\"").append(CRLF);
                writer.append("Content-Type: text/plain; charset=" + Charset.forName("UTF-8")).append(CRLF);
                writer.append(CRLF).append(removeAcento(getMessageS(this.persistencia.getEmpresa())) + "/RPS/"
                        + FuncoesString.PreencheEsquerda(this.codfil, 4, "0") + "/").append(CRLF).flush();

                // Enviando o arquivo
                writer.append("--" + boundary).append(CRLF);
                writer.append("Content-Disposition: form-data; name=\"arquivo\"; filename=\"" + "NFE_"
                        + FuncoesString.PreencheEsquerda(this.selecionado.getNumero().replace(".0", ""), 5, "0") + ".xml").append(CRLF);
                writer.append("Content-Type: text/plain; charset=" + Charset.forName("UTF-8")).append(CRLF);
                writer.append(CRLF).flush();

                output.write(this.arquivoUpload.getContents());
                output.flush();

                writer.append(CRLF).flush();
                writer.append("--" + boundary + "--").append(CRLF).flush();

                int responseCode = ((HttpURLConnection) connection).getResponseCode();
                System.out.println(responseCode); // Should be 200

                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ImportacaoCompleta"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().executeScript("PF('dlgUpload').hide();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void prePesquisa() {
        try {
            this.selecionado = new NFiscal();
            this.cliente = new Clientes();
            this.clientes = new ArrayList<>();
            this.clientes.add(this.cliente);
            if (this.mostrarFiliais) {
                this.filial = new SasPWFill();
            } else {
                this.filial = this.nFiscalSatMobWeb.buscaFilial(this.codfil, this.codPessoa, this.persistencia);
            }
            PrimeFaces.current().executeScript("PF('dlgPesquisar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Clientes> buscarClientes(String query) {
        try {
            this.clientes = this.nFiscalSatMobWeb.buscarClientes(this.codPessoa, query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.clientes;
    }

    public void pesquisar() {
        try {
            this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (this.dataSelecionada2.after(this.dataSelecionada1)) {
                throw new Exception("IntervaloInvalido");
            }

            this.filters.replace(" Data > ? ", this.data2);
            this.filters.replace(" Data < ? ", this.data1);
            if (this.filial == null || this.filial.getCodfilAc() == null || this.filial.getCodfilAc().equals("0")) {
                this.filters.replace(" NFiscal.CodFil = ? ", "");
            } else {
                this.filters.replace(" NFiscal.CodFil = ? ", this.filial.getCodfilAc());
                this.mostrarFiliais = !this.codfil.equals(this.filial.getCodfilAc());
            }
            this.filters.replace(" NFiscal.Serie = ? ", this.selecionado.getSerie() == null || this.selecionado.getSerie().equals("") ? "" : this.selecionado.getSerie());
            this.filters.replace(" NFiscal.Praca = ? ", this.selecionado.getPraca() == null || this.selecionado.getPraca().equals("") ? "" : this.selecionado.getPraca());
            this.filters.replace(" NFiscal.Numero = ? ", this.selecionado.getNumero() == null || this.selecionado.getNumero().equals("") ? "" : this.selecionado.getNumero());

            if (this.selecionado.getSituacao() == null || this.selecionado.getSituacao().equals("")) {
                this.filters.replace(" NFiscal.Situacao = ? ", "");
            } else {
                this.mostrarAtivos = this.selecionado.getSituacao().equals("A");
                this.filters.replace(" NFiscal.Situacao = ? ", this.selecionado.getSituacao());
            }

            this.filters.replace(" NFiscal.CliFat = ? ", this.cliente == null || this.cliente.getCodigo() == null || this.cliente.getCodigo().equals("") ? "" : this.cliente.getCodigo());

            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllNFiscal();
            dt.setFirst(0);

            this.total = this.nFiscalSatMobWeb.total(this.filters, this.codPessoa, this.persistencia);

            PrimeFaces.current().ajax().update("msgs", "main", "cabecalho", "corporativo");
            PrimeFaces.current().executeScript("PF('dlgPesquisar').hide()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void mostraFiliais() {
        if (this.mostrarFiliais) {
            this.filters.replace(" NFiscal.CodFil = ? ", "");
        } else {
            this.filters.replace(" NFiscal.CodFil = ? ", this.codfil);
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllNFiscal();
        dt.setFirst(0);
    }

    public void mostraAtivos() {
        if (this.mostrarAtivos) {
            this.filters.replace(" NFiscal.Situacao = ? ", "A");
        } else {
            this.filters.replace(" NFiscal.Situacao = ? ", "");
        }
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllNFiscal();
        dt.setFirst(0);
    }

    public void limpaFiltros() {
        this.filters.replace(" Data > ? ", this.data2);
        this.filters.replace(" Data < ? ", this.data1);
        this.filters.replace(" NFiscal.CodFil = ? ", this.codfil);
        this.mostrarFiliais = false;
        this.filters.replace(" NFiscal.Serie = ? ", "");
        this.filters.replace(" NFiscal.Praca = ? ", "");
        this.filters.replace(" NFiscal.Numero = ? ", "");
        this.filters.replace(" NFiscal.Situacao = ? ", "A");
        this.mostrarAtivos = true;
        this.filters.replace(" NFiscal.CliFat = ? ", "");

        this.limparFiltros = false;

        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllNFiscal();
        dt.setFirst(0);
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public NFiscal getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(NFiscal selecionado) {
        this.selecionado = selecionado;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }

    public void gerarLog(String texto) {
        this.logerro.GravaMetodos(texto, this.caminho);
    }

    public StreamedContent getArquivoDownload() {
        return arquivoDownload;
    }

    public void setArquivoDownload(StreamedContent arquivoDownload) {
        this.arquivoDownload = arquivoDownload;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public boolean isLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public boolean isMostrarAtivos() {
        return mostrarAtivos;
    }

    public void setMostrarAtivos(boolean mostrarAtivos) {
        this.mostrarAtivos = mostrarAtivos;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public List<Clientes> getClientes() {
        return clientes;
    }

    public void setClientes(List<Clientes> clientes) {
        this.clientes = clientes;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }
}

package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FuncionVerbasConsig {
    private BigDecimal CodFil;
    private BigDecimal Matr;
    private String Verba;
    private LocalDate DtValidade;
    private String Banco;
    private String Contrato;
    private String Obs;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(BigDecimal Matr) {
        this.Matr = Matr;
    }

    public String getVerba() {
        return Verba;
    }

    public void setVerba(String Verba) {
        this.Verba = Verba;
    }

    public LocalDate getDtValidade() {
        return DtValidade;
    }

    public void setDtValidade(LocalDate DtValidade) {
        this.DtValidade = DtValidade;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    
}

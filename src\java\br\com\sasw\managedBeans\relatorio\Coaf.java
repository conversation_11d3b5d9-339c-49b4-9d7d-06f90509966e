/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.relatorio;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeansCompostas.CoafComunicacoes;
import SasDaos.CoafDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

/**
 *
 * <AUTHOR>
 */
@Named(value = "Coaf")
@ViewScoped
public class Coaf implements Serializable {

    private BigDecimal codPessoa;
    private Persistencia persistencia, satellite;
    private String codFil, banco, operador, nomeFilial, caminho, log, secao, dataTela, matricula, nivel, dataInicio, dataFinal;
    private Date dataObjetoInicio, dataObjetoFim;
    private ArquivoLog logerro;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private CoafComunicacoes coafComunicSelecionado;
    private List<CoafComunicacoes> listaCoafComunic;
    private CoafDao coafDao;

    public Coaf() throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        secao = (String) fc.getExternalContext().getSessionMap().get("posto");
        nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
        matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        log = new String();
        rotassatweb = new RotasSatWeb();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        this.dataTela = DataAtual.getDataAtual("SQL");
        coafDao = new CoafDao();
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        c.set(Calendar.DAY_OF_MONTH, 1);
        dataObjetoInicio = c.getTime();
        dataInicio = dataObjetoInicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        dataObjetoFim = c.getTime();
        dataFinal = dataObjetoFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

    }

    public void Persistencia(Persistencia pp, Persistencia satellite) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.satellite = satellite;
            if (null == this.satellite) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarComunicacoes() throws Exception {
        listaCoafComunic = coafDao.listagemComunicacoes(this.codFil, this.dataInicio, this.dataFinal, "S", this.persistencia);
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public CoafComunicacoes getCoafComunicSelecionado() {
        return coafComunicSelecionado;
    }

    public void setCoafComunicSelecionado(CoafComunicacoes coafComunicSelecionado) {
        this.coafComunicSelecionado = coafComunicSelecionado;
    }

    public List<CoafComunicacoes> getListaCoafComunic() {
        return listaCoafComunic;
    }

    public void setListaCoafComunic(List<CoafComunicacoes> listaCoafComunic) {
        this.listaCoafComunic = listaCoafComunic;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }
}

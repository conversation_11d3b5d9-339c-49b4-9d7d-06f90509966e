package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Clientes {

    private BigDecimal CodFil;
    private String Banco;
    private String TpCli;
    private String CodCli;
    private String Agencia;
    private String SubAgencia;
    private String Lote;
    private String NRed;
    private String Nome;
    private String Ende;
    private String Bairro;
    private String CodCidade;
    private String Cidade;
    private String Estado;
    private String CEP;
    private String Fone1;
    private String Fone2;
    private String Fax;
    private String Contato;
    private String Email;
    private String SenhaWEB;
    private String RamoAtiv;
    private String Regiao;
    private String Latitude;
    private String Longitude;
    private String PrdApoio;
    private String Risco;
    private int Malotes;
    private BigDecimal NroChave;
    private int GrpChave;
    private String CGC;
    private String IE;
    private String Insc_Munic;
    private String CEI;
    private String CPF;
    private String RG;
    private String RateioFat;
    private String RateioTes;
    private int DiaFechaFat;
    private int DiaVencNF;
    private String RetencoesFat;
    private String MarcaATM;
    private String Retorno;
    private String Cheque;
    private BigDecimal Vr_A;
    private BigDecimal Ced_A;
    private BigDecimal Ced_AP;
    private BigDecimal Vr_B;
    private BigDecimal Ced_B;
    private BigDecimal Ced_BP;
    private BigDecimal Vr_C;
    private BigDecimal Ced_C;
    private BigDecimal Ced_CP;
    private BigDecimal Vr_D;
    private BigDecimal Ced_D;
    private BigDecimal Ced_DP;
    private BigDecimal Vr_E;
    private BigDecimal Ced_E;
    private BigDecimal Ced_EP;
    private String EndCob;
    private BigDecimal CodCidCod;
    private String CidCob;
    private String UFCob;
    private String CEPCob;
    private String EmailCob;
    private String Obs;
    private String Situacao;
    private String InterfExt;
    private String CodExt;
    private String CodIntCli;
    private String CodPtoCli;
    private BigDecimal CercaElet;
    private String DtSituacao;
    private LocalDate Dt_Cad;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private LocalDate Dt_UltMov;
    private String Oper_Inc;
    private String Oper_Alt;
    private String Codigo;
    private String CCusto;
    private String TipoPagto;
    private BigDecimal Limite;
    private String LimiteSeguro;
    private String LimiteColeta;
    private String Proprietario;
    private String Representante;
    private String AtivEconomica;
    private String CodCofre;
    private String GrpRota;
    private String Envelope;
    private String Patrimonio;
    
    private String Foto;
    private boolean Selecionado;

    public Clientes() {
        this.CodFil = new BigDecimal("0");
        this.Banco = "";
        this.TpCli = "";
        this.CodCli = "";
        this.Agencia = "";
        this.SubAgencia = "";
        this.Lote = "";
        this.NRed = "";
        this.Nome = "";
        this.Ende = "";
        this.Bairro = "";
        this.CodCidade = "";
        this.Cidade = "";
        this.Estado = "";
        this.CEP = "";
        this.Fone1 = "";
        this.Fone2 = "";
        this.Fax = "";
        this.Contato = "";
        this.Email = "";
        this.SenhaWEB = "";
        this.RamoAtiv = "0";
        this.Regiao = "";
        this.Latitude = "";
        this.Longitude = "";
        this.PrdApoio = "";
        this.Risco = "";
        this.Malotes = 0;
        this.NroChave = new BigDecimal("0");
        this.GrpChave = 0;
        this.CGC = "";
        this.IE = "";
        this.Insc_Munic = "";
        this.CPF = "";
        this.RG = "";
        this.RateioFat = "";
        this.RateioTes = "";
        this.DiaFechaFat = 0;
        this.DiaVencNF = 0;
        this.MarcaATM = "";
        this.Retorno = "";
        this.Cheque = "";
        this.Foto = "";
        this.Vr_A = new BigDecimal("0");
        this.Ced_A = new BigDecimal("0");
        this.Ced_AP = new BigDecimal("0");
        this.Vr_B = new BigDecimal("0");
        this.Ced_B = new BigDecimal("0");
        this.Ced_BP = new BigDecimal("0");
        this.Vr_C = new BigDecimal("0");
        this.Ced_C = new BigDecimal("0");
        this.Ced_CP = new BigDecimal("0");
        this.Vr_D = new BigDecimal("0");
        this.Ced_D = new BigDecimal("0");
        this.Ced_DP = new BigDecimal("0");
        this.Vr_E = new BigDecimal("0");
        this.Ced_E = new BigDecimal("0");
        this.Ced_EP = new BigDecimal("0");
        this.EndCob = "";
        this.CodCidCod = new BigDecimal("0");
        this.CidCob = "";
        this.UFCob = "";
        this.CEPCob = "";
        this.EmailCob = "";
        this.Obs = "";
        this.Situacao = "";
        this.InterfExt = "";
        this.CodExt = "";
        this.CodIntCli = "";
        this.CodPtoCli = "";
        this.CercaElet = new BigDecimal("0");
        this.DtSituacao = "";
        this.Dt_Cad = null;
        this.Dt_Alter = null;
        this.Hr_Alter = "";
        this.Dt_UltMov = null;
        this.Oper_Inc = "";
        this.Oper_Alt = "";
        this.Codigo = "";
        this.CCusto = "";
        this.TipoPagto = "";
        this.Limite = new BigDecimal("0");
        this.Proprietario = "";
        this.Representante = "";
        this.AtivEconomica = "";
        this.CodCofre = "";
    }

    public Clientes(Clientes copia) {

        this.CodFil = copia.CodFil;
        this.Banco = copia.Banco;
        this.TpCli = copia.TpCli;
        this.CodCli = copia.CodCli;
        this.Agencia = copia.Agencia;
        this.SubAgencia = copia.SubAgencia;
        this.Lote = copia.Lote;
        this.NRed = copia.NRed;
        this.Nome = copia.Nome;
        this.Ende = copia.Ende;
        this.Bairro = copia.Bairro;
        this.CodCidade = copia.CodCidade;
        this.Cidade = copia.Cidade;
        this.Estado = copia.Estado;
        this.CEP = copia.CEP;
        this.Fone1 = copia.Fone1;
        this.Fone2 = copia.Fone2;
        this.Fax = copia.Fax;
        this.Contato = copia.Contato;
        this.Email = copia.Email;
        this.SenhaWEB = copia.SenhaWEB;
        this.RamoAtiv = copia.RamoAtiv;
        this.Regiao = copia.Regiao;
        this.Latitude = copia.Latitude;
        this.Longitude = copia.Longitude;
        this.PrdApoio = copia.PrdApoio;
        this.Risco = copia.Risco;
        this.Malotes = copia.Malotes;
        this.NroChave = copia.NroChave;
        this.GrpChave = copia.GrpChave;
        this.CGC = copia.CGC;
        this.IE = copia.IE;
        this.Insc_Munic = copia.Insc_Munic;
        this.CPF = copia.CPF;
        this.RG = copia.RG;
        this.RateioFat = copia.RateioFat;
        this.RateioTes = copia.RateioTes;
        this.DiaFechaFat = copia.DiaFechaFat;
        this.DiaVencNF = copia.DiaVencNF;
        this.RetencoesFat = copia.RetencoesFat;
        this.MarcaATM = copia.MarcaATM;
        this.Retorno = copia.Retorno;
        this.Cheque = copia.Cheque;
        this.Vr_A = copia.Vr_A;
        this.Ced_A = copia.Ced_A;
        this.Ced_AP = copia.Ced_AP;
        this.Vr_B = copia.Vr_B;
        this.Ced_B = copia.Ced_B;
        this.Ced_BP = copia.Ced_BP;
        this.Vr_C = copia.Vr_C;
        this.Ced_C = copia.Ced_C;
        this.Ced_CP = copia.Ced_CP;
        this.Vr_D = copia.Vr_D;
        this.Ced_D = copia.Ced_D;
        this.Ced_DP = copia.Ced_DP;
        this.Vr_E = copia.Vr_E;
        this.Ced_E = copia.Ced_E;
        this.Ced_EP = copia.Ced_EP;
        this.EndCob = copia.EndCob;
        this.CodCidCod = copia.CodCidCod;
        this.CidCob = copia.CidCob;
        this.UFCob = copia.UFCob;
        this.CEPCob = copia.CEPCob;
        this.EmailCob = copia.EmailCob;
        this.Obs = copia.Obs;
        this.Situacao = copia.Situacao;
        this.InterfExt = copia.InterfExt;
        this.CodExt = copia.CodExt;
        this.CodIntCli = copia.CodIntCli;
        this.CodPtoCli = copia.CodPtoCli;
        this.CercaElet = copia.CercaElet;
        this.DtSituacao = copia.DtSituacao;
        this.Dt_Cad = copia.Dt_Cad;
        this.Dt_Alter = copia.Dt_Alter;
        this.Hr_Alter = copia.Hr_Alter;
        this.Dt_UltMov = copia.Dt_UltMov;
        this.Oper_Inc = copia.Oper_Inc;
        this.Oper_Alt = copia.Oper_Alt;
        this.Codigo = copia.Codigo;
        this.CCusto = copia.CCusto;
        this.TipoPagto = copia.TipoPagto;
        this.Limite = copia.Limite;
        this.LimiteSeguro = copia.LimiteSeguro;
        this.LimiteColeta = copia.LimiteColeta;
        this.Proprietario = copia.Proprietario;
        this.Representante = copia.Representante;
        this.AtivEconomica = copia.AtivEconomica;
        this.CodCofre = copia.CodCofre;
        this.Envelope = copia.Envelope;
        this.Patrimonio = copia.Patrimonio;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(BigDecimal CodFil) {
        try {
            this.CodFil = CodFil;
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(Integer CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getTpCli() {
        return TpCli;
    }

    public void setTpCli(String TpCli) {
        this.TpCli = TpCli;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getSubAgencia() {
        return SubAgencia;
    }

    public void setSubAgencia(String SubAgencia) {
        this.SubAgencia = SubAgencia;
    }

    public String getLote() {
        return Lote;
    }

    public void setLote(String Lote) {
        this.Lote = Lote;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getEnde() {
        return Ende;
    }

    public void setEnde(String Ende) {
        this.Ende = Ende;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCodCidade() {
        return CodCidade;
    }

    public void setCodCidade(String CodCidade) {
        this.CodCidade = CodCidade;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getEstado() {
        return Estado;
    }

    public void setEstado(String Estado) {
        this.Estado = Estado;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone1() {
        return Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getFone2() {
        return Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getFax() {
        return Fax;
    }

    public void setFax(String Fax) {
        this.Fax = Fax;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getSenhaWEB() {
        return SenhaWEB;
    }

    public void setSenhaWEB(String SenhaWEB) {
        this.SenhaWEB = SenhaWEB;
    }

    public String getRamoAtiv() {
        return RamoAtiv;
    }

    public void setRamoAtiv(String RamoAtiv) {
        this.RamoAtiv = RamoAtiv;
    }

    public String getRegiao() {
        return Regiao;
    }

    public void setRegiao(String Regiao) {
        this.Regiao = Regiao;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getPrdApoio() {
        return PrdApoio;
    }

    public void setPrdApoio(String PrdApoio) {
        this.PrdApoio = PrdApoio;
    }

    public String getRisco() {
        return Risco;
    }

    public void setRisco(String Risco) {
        this.Risco = Risco;
    }

    public int getMalotes() {
        return Malotes;
    }

    public void setMalotes(int Malotes) {
        this.Malotes = Malotes;
    }

    public BigDecimal getNroChave() {
        return NroChave;
    }

    public void setNroChave(String NroChave) {
        try {
            this.NroChave = new BigDecimal(NroChave);
        } catch (Exception E) {
            this.NroChave = new BigDecimal("0");
        }
    }

    public int getGrpChave() {
        return GrpChave;
    }

    public void setGrpChave(int GrpChave) {
        this.GrpChave = GrpChave;
    }

    public String getCGC() {
        return CGC;
    }

    public void setCGC(String CGC) {
        this.CGC = CGC;
    }

    public String getIE() {
        return IE;
    }

    public void setIE(String IE) {
        this.IE = IE;
    }

    public String getInsc_Munic() {
        return Insc_Munic;
    }

    public void setInsc_Munic(String Insc_Munic) {
        this.Insc_Munic = Insc_Munic;
    }

    public String getCEI() {
        return CEI;
    }

    public void setCEI(String CEI) {
        this.CEI = CEI;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getRateioFat() {
        return RateioFat;
    }

    public void setRateioFat(String RateioFat) {
        this.RateioFat = RateioFat;
    }

    public String getRateioTes() {
        return RateioTes;
    }

    public void setRateioTes(String RateioTes) {
        this.RateioTes = RateioTes;
    }

    public int getDiaFechaFat() {
        return DiaFechaFat;
    }

    public void setDiaFechaFat(int DiaFechaFat) {
        this.DiaFechaFat = DiaFechaFat;
    }

    public int getDiaVencNF() {
        return DiaVencNF;
    }

    public void setDiaVencNF(int DiaVencNF) {
        this.DiaVencNF = DiaVencNF;
    }

    public String getMarcaATM() {
        return MarcaATM;
    }

    public void setMarcaATM(String MarcaATM) {
        this.MarcaATM = MarcaATM;
    }

    public String getRetorno() {
        return Retorno;
    }

    public void setRetorno(String Retorno) {
        this.Retorno = Retorno;
    }

    public String getCheque() {
        return Cheque;
    }

    public void setCheque(String Cheque) {
        this.Cheque = Cheque;
    }

    public BigDecimal getVr_A() {
        return Vr_A;
    }

    public void setVr_A(String Vr_A) {
        try {
            this.Vr_A = new BigDecimal(Vr_A);
        } catch (Exception e) {
            this.Vr_A = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_A() {
        return Ced_A;
    }

    public void setCed_A(String Ced_A) {
        try {
            this.Ced_A = new BigDecimal(Ced_A);
        } catch (Exception e) {
            this.Ced_A = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_AP() {
        return Ced_AP;
    }

    public void setCed_AP(String Ced_AP) {
        try {
            this.Ced_AP = new BigDecimal(Ced_AP);
        } catch (Exception e) {
            this.Ced_AP = new BigDecimal("0");
        }
    }

    public BigDecimal getVr_B() {
        return Vr_B;
    }

    public void setVr_B(String Vr_B) {
        try {
            this.Vr_B = new BigDecimal(Vr_B);
        } catch (Exception e) {
            this.Vr_B = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_B() {
        return Ced_B;
    }

    public void setCed_B(String Ced_B) {
        try {
            this.Ced_B = new BigDecimal(Ced_B);
        } catch (Exception e) {
            this.Ced_B = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_BP() {
        return Ced_BP;
    }

    public void setCed_BP(String Ced_BP) {
        try {
            this.Ced_BP = new BigDecimal(Ced_BP);
        } catch (Exception e) {
            this.Ced_BP = new BigDecimal("0");
        }
    }

    public BigDecimal getVr_C() {
        return Vr_C;
    }

    public void setVr_C(String Vr_C) {
        try {
            this.Vr_C = new BigDecimal(Vr_C);
        } catch (Exception e) {
            this.Vr_C = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_C() {
        return Ced_C;
    }

    public void setCed_C(String Ced_C) {
        try {
            this.Ced_C = new BigDecimal(Ced_C);
        } catch (Exception e) {
            this.Ced_C = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_CP() {
        return Ced_CP;
    }

    public void setCed_CP(String Ced_CP) {
        try {
            this.Ced_CP = new BigDecimal(Ced_CP);
        } catch (Exception e) {
            this.Ced_CP = new BigDecimal("0");
        }
    }

    public BigDecimal getVr_D() {
        return Vr_D;
    }

    public void setVr_D(String Vr_D) {
        try {
            this.Vr_D = new BigDecimal(Vr_D);
        } catch (Exception e) {
            this.Vr_D = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_D() {
        return Ced_D;
    }

    public void setCed_D(String Ced_D) {
        try {
            this.Ced_D = new BigDecimal(Ced_D);
        } catch (Exception e) {
            this.Ced_D = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_DP() {
        return Ced_DP;
    }

    public void setCed_DP(String Ced_DP) {
        try {
            this.Ced_DP = new BigDecimal(Ced_DP);
        } catch (Exception e) {
            this.Ced_DP = new BigDecimal("0");
        }
    }

    public BigDecimal getVr_E() {
        return Vr_E;
    }

    public void setVr_E(String Vr_E) {
        try {
            this.Vr_E = new BigDecimal(Vr_E);
        } catch (Exception e) {
            this.Vr_E = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_E() {
        return Ced_E;
    }

    public void setCed_E(String Ced_E) {
        try {
            this.Ced_E = new BigDecimal(Ced_E);
        } catch (Exception e) {
            this.Ced_E = new BigDecimal("0");
        }
    }

    public BigDecimal getCed_EP() {
        return Ced_EP;
    }

    public void setCed_EP(String Ced_EP) {
        try {
            this.Ced_EP = new BigDecimal(Ced_EP);
        } catch (Exception e) {
            this.Ced_EP = new BigDecimal("0");
        }
    }

    public String getEndCob() {
        return EndCob;
    }

    public void setEndCob(String EndCob) {
        this.EndCob = EndCob;
    }

    public BigDecimal getCodCidCod() {
        return CodCidCod;
    }

    public void setCodCidCod(String CodCidCod) {
        try {
            this.CodCidCod = new BigDecimal(CodCidCod);
        } catch (Exception e) {
            this.CodCidCod = new BigDecimal("0");
        }
    }

    public String getCidCob() {
        return CidCob;
    }

    public void setCidCob(String CidCob) {
        this.CidCob = CidCob;
    }

    public String getUFCob() {
        return UFCob;
    }

    public void setUFCob(String UFCob) {
        this.UFCob = UFCob;
    }

    public String getCEPCob() {
        return CEPCob;
    }

    public void setCEPCob(String CEPCob) {
        this.CEPCob = CEPCob;
    }

    public String getEmailCob() {
        return EmailCob;
    }

    public void setEmailCob(String EmailCob) {
        this.EmailCob = EmailCob;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getInterfExt() {
        return InterfExt;
    }

    public void setInterfExt(String InterfExt) {
        this.InterfExt = InterfExt;
    }

    public String getCodExt() {
        return CodExt;
    }

    public void setCodExt(String CodExt) {
        this.CodExt = CodExt;
    }

    public String getCodIntCli() {
        return CodIntCli;
    }

    public void setCodIntCli(String CodIntCli) {
        this.CodIntCli = CodIntCli;
    }

    public String getCodPtoCli() {
        return CodPtoCli;
    }

    public void setCodPtoCli(String CodPtoCli) {
        this.CodPtoCli = CodPtoCli;
    }

    public BigDecimal getCercaElet() {
        return CercaElet;
    }

    public void setCercaElet(String CercaElet) {
        try {
            this.CercaElet = new BigDecimal(CercaElet);
        } catch (Exception e) {
            this.CercaElet = new BigDecimal("0");
        }
    }

    public String getDtSituacao() {
        return DtSituacao;
    }

    public void setDtSituacao(String DtSituacao) {
        this.DtSituacao = DtSituacao;
    }

    public LocalDate getDt_Cad() {
        return Dt_Cad;
    }

    public void setDt_Cad(LocalDate Dt_Cad) {
        this.Dt_Cad = Dt_Cad;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public LocalDate getDt_UltMov() {
        return Dt_UltMov;
    }

    public void setDt_UltMov(LocalDate Dt_UltMov) {
        this.Dt_UltMov = Dt_UltMov;
    }

    public String getOper_Inc() {
        return Oper_Inc;
    }

    public void setOper_Inc(String Oper_Inc) {
        this.Oper_Inc = Oper_Inc;
    }

    public String getOper_Alt() {
        return Oper_Alt;
    }

    public void setOper_Alt(String Oper_Alt) {
        this.Oper_Alt = Oper_Alt;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getTipoPagto() {
        return TipoPagto;
    }

    public void setTipoPagto(String TipoPagto) {
        this.TipoPagto = TipoPagto;
    }

    public BigDecimal getLimite() {
        return Limite;
    }

    public void setLimite(String Limite) {
        try {
            this.Limite = new BigDecimal(Limite);
        } catch (Exception e) {
            this.Limite = new BigDecimal("0");
        }
    }

    public String getProprietario() {
        return Proprietario;
    }

    public void setProprietario(String Proprietario) {
        this.Proprietario = Proprietario;
    }

    public String getRepresentante() {
        return Representante;
    }

    public void setRepresentante(String Representante) {
        this.Representante = Representante;
    }

    public String getAtivEconomica() {
        return AtivEconomica;
    }

    public void setAtivEconomica(String AtivEconomica) {
        this.AtivEconomica = AtivEconomica;
    }

    public String getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        try {
            this.CodCofre = CodCofre.replace(".0", "");
        } catch (Exception e) {
            this.CodCofre = CodCofre;
        }
    }

    public String getRetencoesFat() {
        return RetencoesFat;
    }

    public void setRetencoesFat(String RetencoesFat) {
        this.RetencoesFat = RetencoesFat;
    }

    public String getLimiteSeguro() {
        return LimiteSeguro;
    }

    public void setLimiteSeguro(String LimiteSeguro) {
        this.LimiteSeguro = LimiteSeguro;
    }

    public String getLimiteColeta() {
        return LimiteColeta;
    }

    public void setLimiteColeta(String LimiteColeta) {
        this.LimiteColeta = LimiteColeta;
    }

    public String getEnvelope() {
        return Envelope;
    }

    public void setEnvelope(String Envelope) {
        this.Envelope = Envelope;
    }

    public String getPatrimonio() {
        return Patrimonio;
    }

    public void setPatrimonio(String Patrimonio) {
        this.Patrimonio = Patrimonio;
    }

    public String getGrpRota() {
        return GrpRota;
    }

    public void setGrpRota(String GrpRota) {
        this.GrpRota = GrpRota;
    }

    public String getFoto() {
        return Foto;
    }

    public void setFoto(String Foto) {
        this.Foto = Foto;
    }

    public boolean isSelecionado() {
        return Selecionado;
    }

    public void setSelecionado(boolean Selecionado) {
        this.Selecionado = Selecionado;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.CodFil);
        hash = 29 * hash + Objects.hashCode(this.Codigo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Clientes other = (Clientes) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "Clientes{" + "CodFil=" + CodFil + ", Banco=" + Banco + ", TpCli=" + TpCli + ", CodCli=" + CodCli + ", Codigo=" + Codigo + "}";
    }
}

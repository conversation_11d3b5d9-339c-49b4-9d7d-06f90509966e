package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class TesCofresRes {

    private BigDecimal CodCofre;
    private LocalDate Data;
    private int DiaSem;
    private String DiaSemT;
    private String Feriado;
    private BigDecimal VlrDep;
    private BigDecimal VlrRecDepD0;
    private String HrRecDepD0;
    private BigDecimal VlrCredRecD0;
    private BigDecimal VlrCredCorteD0;
    private BigDecimal VlrTotalCred;
    private BigDecimal VlrRecApos;
    private String HrRecApos;
    private BigDecimal VlrD0Apos;
    private BigDecimal VlrD1Apos;
    private BigDecimal VlrTotalRec;
    private BigDecimal VlrDepProxDU;
    private BigDecimal SaldoFisTotal;
    private BigDecimal SaldoFisCred;
    private BigDecimal SaldoFisCst;
    private String DataStr;
    private String CredD0;
    private String CredD1;
    private String NRed;
    
    private final BigDecimal ZERO = new BigDecimal("0");

    public BigDecimal getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        this.CodCofre = new BigDecimal(CodCofre);
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    /**
     * Retorna o dia da semana na forma de inteiro
     *
     * @return 1- Domingo 2- Segunda 3- Terça 4- Quarta 5- Quinta 6- Sexta 7-
     * Sábado
     */
    public int getDiaSem() {
        return DiaSem;
    }

    public void setDiaSem(int DiaSem) {
        this.DiaSem = DiaSem;
        switch (DiaSem) {
            case (1):
                DiaSemT = "Domingo";
                break;
            case (2):
                DiaSemT = "Segunda";
                break;
            case (3):
                DiaSemT = "Terça";
                break;
            case (4):
                DiaSemT = "Quarta";
                break;
            case (5):
                DiaSemT = "Quinta";
                break;
            case (6):
                DiaSemT = "Sexta";
                break;
            case (7):
                DiaSemT = "Sábado";
                break;
        }
    }

    public void setDiaSemT(String DiaSemT) {
        this.DiaSemT = DiaSemT;
    }

    public String getDiaSemT() {
        return this.DiaSemT;
    }

    public String getFeriado() {
        return Feriado;
    }

    public void setFeriado(String Feriado) {
        this.Feriado = Feriado;
    }

    public BigDecimal getVlrDep() {
        return VlrDep;
    }

    public void setVlrDep(String VlrDep) {
        try {
            this.VlrDep = new BigDecimal(VlrDep);
        } catch (Exception e) {
            this.VlrDep = new BigDecimal("0");
        }
    }

    public BigDecimal getVlrRecDepD0() {
        return VlrRecDepD0;
    }

    public void setVlrRecDepD0(String VlrRecDepD0) {
        try {
            this.VlrRecDepD0 = new BigDecimal(VlrRecDepD0);
        } catch (Exception e) {
            this.VlrRecDepD0 = new BigDecimal("0");
        }
    }

    public String getHrRecDepD0() {
        return HrRecDepD0;
    }

    public void setHrRecDepD0(String HrRecDepD0) {
        this.HrRecDepD0 = HrRecDepD0;
    }

    public BigDecimal getVlrCredRecD0() {
        return VlrCredRecD0;
    }

    public void setVlrCredRecD0(String VlrCredRecD0) {
        try {
            this.VlrCredRecD0 = new BigDecimal(VlrCredRecD0);
        } catch (Exception e) {
            this.VlrCredRecD0 = new BigDecimal(VlrCredRecD0);
        }
    }

    public BigDecimal getVlrCredCorteD0() {
        return VlrCredCorteD0;
    }

    public void setVlrCredCorteD0(String VlrCredCorteD0) {
        try {
            this.VlrCredCorteD0 = new BigDecimal(VlrCredCorteD0);
        } catch (Exception e) {
            this.VlrCredCorteD0 = new BigDecimal("0");
        }
    }

    public BigDecimal getVlrTotalCred() {
        return VlrTotalCred;
    }

    public void setVlrTotalCred(String VlrTotalCred) {
        try {
            this.VlrTotalCred = new BigDecimal(VlrTotalCred);
        } catch (Exception e) {
            this.VlrTotalCred = new BigDecimal(VlrTotalCred);
        }
    }

    public BigDecimal getVlrRecApos() {
        return VlrRecApos;
    }

    public void setVlrRecApos(String VlrRecApos) {
        try {
            this.VlrRecApos = new BigDecimal(VlrRecApos);
        } catch (Exception e) {
            this.VlrRecApos = new BigDecimal("0");
        }
    }

    public String getHrRecApos() {
        return HrRecApos;
    }

    public void setHrRecApos(String HrRecApos) {
        this.HrRecApos = HrRecApos;
    }

    public BigDecimal getVlrD0Apos() {
        return VlrD0Apos;
    }

    public void setVlrD0Apos(String VlrD0Apos) {
        try {
            this.VlrD0Apos = new BigDecimal(VlrD0Apos);
        } catch (Exception e) {
            this.VlrD0Apos = new BigDecimal(VlrD0Apos);
        }
    }

    public BigDecimal getVlrD1Apos() {
        return VlrD1Apos;
    }

    public void setVlrD1Apos(String VlrD1Apos) {
        try {
            this.VlrD1Apos = new BigDecimal(VlrD1Apos);
        } catch (Exception e) {
            this.VlrD1Apos = new BigDecimal("0");
        }
    }

    public BigDecimal getVlrTotalRec() {
        return VlrTotalRec;
    }

    public void setVlrTotalRec(String VlrTotalRec) {
        try {
            this.VlrTotalRec = new BigDecimal(VlrTotalRec);
        } catch (Exception e) {
            this.VlrTotalRec = new BigDecimal("0");
        }
    }

    public BigDecimal getVlrDepProxDU() {
        return VlrDepProxDU;
    }

    public void setVlrDepProxDU(String VlrDepProxDU) {
        try {
            this.VlrDepProxDU = new BigDecimal(VlrDepProxDU);
        } catch (Exception e) {
            this.VlrDepProxDU = new BigDecimal("0");
        }
    }

    public BigDecimal getSaldoFisTotal() {
        return SaldoFisTotal;
    }

    public void setSaldoFisTotal(String SaldoFisTotal) {
        try {
            this.SaldoFisTotal = new BigDecimal(SaldoFisTotal);
        } catch (Exception e) {
            this.SaldoFisTotal = new BigDecimal("0");
        }
    }

    public BigDecimal getSaldoFisCred() {
        return SaldoFisCred;
    }

    public void setSaldoFisCred(String SaldoFisCred) {
        try {
            this.SaldoFisCred = new BigDecimal(SaldoFisCred);
        } catch (Exception e) {
            this.SaldoFisCred = new BigDecimal("0");
        }
    }

    public BigDecimal getSaldoFisCst() {
        return SaldoFisCst;
    }

    public void setSaldoFisCst(String SaldoFisCst) {
        try {
            this.SaldoFisCst = new BigDecimal(SaldoFisCst);
        } catch (Exception e) {
            this.SaldoFisCst = new BigDecimal("0");
        }
    }

    public String getDataStr() {
        return DataStr;
    }

    public void setDataStr(String DataStr) {
        this.DataStr = DataStr;
    }

    public String getCredD0() {
        return CredD0;
    }

    public void setCredD0(String CredD0) {
        this.CredD0 = CredD0;
    }

    public String getCredD1() {
        return CredD1;
    }

    public void setCredD1(String CredD1) {
        this.CredD1 = CredD1;
    }

    /**
     * @return the NRed
     */
    public String getNRed() {
        return NRed;
    }

    /**
     * @param NRed the NRed to set
     */
    public void setNRed(String NRed) {
        this.NRed = NRed;
    }
    
    public TesCofresRes(){
        this.CodCofre = ZERO;
        this.CredD0 = "";
        this.CredD1 = "";        
        this.Data = LocalDate.now();
        this.DataStr = "";
        this.DiaSem = 0;  
        this.DiaSemT = "";
        this.Feriado = "";
        this.HrRecApos = "";
        this.HrRecDepD0 = "";
        this.NRed = "";
        this.SaldoFisCred = ZERO;
        this.SaldoFisCst = ZERO;
        this.SaldoFisTotal = ZERO;
        this.VlrCredCorteD0 = ZERO;
        this.VlrCredRecD0 = ZERO;
        this.VlrD0Apos = ZERO;
        this.VlrD1Apos = ZERO;
        this.VlrDep = ZERO;
        this.VlrDepProxDU = ZERO;
        this.VlrRecApos = ZERO;
        this.VlrRecDepD0 = ZERO;
        this.VlrTotalCred = ZERO;
        this.VlrTotalRec = ZERO;                
    }
}


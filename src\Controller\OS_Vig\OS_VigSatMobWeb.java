package Controller.OS_Vig;

import Dados.Persistencia;
import SasBeans.Bancos;
import SasBeans.Clientes;
import SasBeans.ContrVig;
import SasBeans.ContratosDoctos;
import SasBeans.ContratosReaj;
import SasBeans.CtrItens;
import SasBeans.CtrItensAntReajustes;
import SasBeans.Fat_Grp;
import SasBeans.Filiais;
import SasBeans.OS_VITens;
import SasBeans.OS_Vig;
import SasBeans.SasPWFill;
import SasBeans.TipoSrvCli;
import SasDaos.BancosDao;
import SasDaos.ClientesDao;
import SasDaos.ContrVigDao;
import SasDaos.ContratosDoctosDao;
import SasDaos.ContratosReajDao;
import SasDaos.CtrItensAntReajustesDao;
import SasDaos.CtrItensDao;
import SasDaos.Fat_GrpDao;
import SasDaos.FiliaisDao;
import SasDaos.OS_VItensDao;
import SasDaos.OS_VigDao;
import SasDaos.SasPwFilDao;
import br.com.sasw.pacotesuteis.sasbeans.FatISSGrp;
import br.com.sasw.pacotesuteis.sasbeans.HT_NF;
import br.com.sasw.pacotesuteis.sasbeans.OS_VFreq;
import br.com.sasw.pacotesuteis.sasdaos.FatISSGrpDao;
import br.com.sasw.pacotesuteis.sasdaos.HT_NFDao;
import br.com.sasw.pacotesuteis.sasdaos.OS_VFreqDao;
import br.com.sasw.pacotesuteis.sasdaos.TipoSrvCliDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class OS_VigSatMobWeb {

    private OS_VFreqDao os_VFreqDao;

    public OS_VigSatMobWeb() {
    }

    public OS_VigSatMobWeb(Persistencia persistencia) {
        os_VFreqDao = new OS_VFreqDao(persistencia);
    }

    public Bancos buscarBanco(String banco, Persistencia persistencia) throws Exception {
        try {
            BancosDao bancosDao = new BancosDao();
            return bancosDao.buscarBanco(banco, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Bancos> buscarBancos(String query, Persistencia persistencia) throws Exception {
        try {
            BancosDao bancosDao = new BancosDao();
            return bancosDao.buscarBancos(query, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<OS_VFreq> listarOS_VFreq(String os, String codFil, Persistencia persistencia) throws Exception {
        try {
            OS_VFreqDao os_VFreqDao = new OS_VFreqDao();
            return os_VFreqDao.listarOS_VFreq(os, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    private boolean isEmpty(String field) {
        return field == null || field.equals("");
    }

    private boolean isOS_VFreqReady(OS_VFreq frequencia) {
        return !(isEmpty(frequencia.getOS())) || isEmpty(frequencia.getCodFil())
                || isEmpty(frequencia.getDiaSem()) || isEmpty(frequencia.getHora1())
                || isEmpty(frequencia.getTipo()) || isEmpty(frequencia.getHora2())
                || isEmpty(frequencia.getDias()) || isEmpty(frequencia.getDU())
                || isEmpty(frequencia.getOperador());
    }

    private boolean isSameOS_VFreqId(OS_VFreq antigo, OS_VFreq frequencia) {
        return (frequencia.getOS() == null ? antigo.getOS() == null : frequencia.getOS().equals(antigo.getOS()))
                && (frequencia.getCodFil() == null ? antigo.getCodFil() == null : frequencia.getCodFil().equals(antigo.getCodFil()))
                && (frequencia.getDiaSem() == null ? antigo.getDiaSem() == null : frequencia.getDiaSem().equals(antigo.getDiaSem()))
                && (frequencia.getHora1() == null ? antigo.getHora1() == null : frequencia.getHora1().equals(antigo.getHora1()));
    }

    public void insertOS_VFreq(OS_VFreq frequencia) throws Exception {
        try {
            if (isOS_VFreqReady(frequencia)) {
                os_VFreqDao.insert(frequencia);
            } else {
                throw new Exception("ErroValidacao");
            }
        } catch (Exception e) {
            throw e;
            // throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void updateOS_VFreq(OS_VFreq antigo, OS_VFreq frequencia) throws Exception {
        try {
            if (isOS_VFreqReady(frequencia)) {
                if (isSameOS_VFreqId(antigo, frequencia)) {
                    os_VFreqDao.update(antigo, frequencia);
                } else {
                    OS_VFreq record = os_VFreqDao.getById(frequencia.getOS(),
                            frequencia.getCodFil(),
                            frequencia.getDiaSem(),
                            frequencia.getHora1());

                    if (record == null) {
                        os_VFreqDao.update(antigo, frequencia);
                    } else {
                        throw new Exception("HorarioCadastrado");
                    }
                }
            } else {
                throw new Exception("ErroValidacao");
            }
        } catch (Exception e) {
            throw e;
            // throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public TipoSrvCli buscarTipoSrvCli(String codigo, Persistencia persistencia) throws Exception {
        try {
            TipoSrvCliDao tipoSrvCliDao = new TipoSrvCliDao();
            return tipoSrvCliDao.buscarTipoSrvCli(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TipoSrvCli> listarTipoSrvCli(Persistencia persistencia) throws Exception {
        try {
            TipoSrvCliDao tipoSrvCliDao = new TipoSrvCliDao();
            return tipoSrvCliDao.listarTipoSrvCli(persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void cadastrarTipoSrvCli(TipoSrvCli tipoSrvCli, Persistencia persistencia) throws Exception {
        try {
            TipoSrvCliDao tipoSrvCliDao = new TipoSrvCliDao();
            tipoSrvCliDao.inserirTipoSrvCli(tipoSrvCli, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void editarTipoSrvCli(TipoSrvCli tipoSrvCli, Persistencia persistencia) throws Exception {
        try {
            TipoSrvCliDao tipoSrvCliDao = new TipoSrvCliDao();
            tipoSrvCliDao.atualizarTipoSrvCli(tipoSrvCli, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<FatISSGrp> buscaFatISSGrp(String query, Persistencia persistencia) throws Exception {
        try {
            FatISSGrpDao fatISSGrpDao = new FatISSGrpDao();
            return fatISSGrpDao.listarFatISSGrp(query, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public FatISSGrp buscarFatISSGrp(String codigo, Persistencia persistencia) throws Exception {
        try {
            FatISSGrpDao fatISSGrpDao = new FatISSGrpDao();
            return fatISSGrpDao.buscarFatISSGrp(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public HT_NF buscarHT_NF(String codigo, String codfil, Persistencia persistencia) throws Exception {
        try {
            HT_NFDao ht_NFDao = new HT_NFDao();
            return ht_NFDao.buscarHT_NF(codigo, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<HT_NF> buscaHT_NF(String query, String codfil, Persistencia persistencia) throws Exception {
        try {
            HT_NFDao ht_NFDao = new HT_NFDao();
            return ht_NFDao.listarHT_NF(query, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<OS_VITens> listarOS_VItens(String OS, String codFil, Persistencia persistencia) throws Exception {
        try {
            OS_VItensDao os_vitensDao = new OS_VItensDao();
            return os_vitensDao.listarOS_VItens(codFil, OS, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Fat_Grp> buscaFat_Grp(String codFil, String query, Persistencia persistencia) throws Exception {
        try {
            Fat_GrpDao fat_GrpDao = new Fat_GrpDao();
            return fat_GrpDao.buscarFat_GrpList(query, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Fat_Grp buscarFat_Grp(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {
            Fat_GrpDao fat_GrpDao = new Fat_GrpDao();
            return fat_GrpDao.buscarFat_Grp(codigo, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscaClientes(String codFil, String query, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.buscarClientes(query, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca os dados de uma filial
     *
     * @param CodFil - Código da Filial
     * @param CodPessoa
     * @param persistencia - conexão ao banco de dados
     * @return filial
     * @throws Exception
     */
    public SasPWFill buscaFilial(String CodFil, String CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(CodFil, new BigDecimal(CodPessoa), persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<ContrVig> buscaContratos(String codFil, String query, Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrVigDao = new ContrVigDao();
            return contrVigDao.listarContratos(codFil, query, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public ContrVig obterContrato(String contrato, String codFil, Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrVigDao = new ContrVigDao();
            return contrVigDao.buscarContrato(codFil, contrato, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public boolean inserirSubContrato(ContrVig subContrato, Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrVigDao = new ContrVigDao();
            contrVigDao.inserirContrato(subContrato, persistencia);
            return true;
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<ContrVig> listarSubContratosPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrVigDao = new ContrVigDao();
            return contrVigDao.listarSubContratosPaginada(primeiro, linhas, idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public int contagemSubContratos(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            ContrVigDao contrVigDao = new ContrVigDao();
            return contrVigDao.contagemSubContratos(idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Clientes obterCliente(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.getClientesMobile(codFil, codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<OS_Vig> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao oS_VigDao = new OS_VigDao();
            return oS_VigDao.listaPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Integer totalListaPaginada(Map filtros, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao oS_VigDao = new OS_VigDao();
            return oS_VigDao.totalListaPaginada(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirOS_Vig(OS_Vig os_vig, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigdao = new OS_VigDao();
            os_vigdao.inserirOS_Vig(os_vig, persistencia);
        } catch (Exception e) {
            throw new Exception("Os_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void atualizarOS_Vig(OS_Vig os_vig, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigdao = new OS_VigDao();
            os_vigdao.atualizarOS_Vig(os_vig, persistencia);
        } catch (Exception e) {
            throw new Exception("Os_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirOS_VigSimplificado(OS_Vig os_vig, OS_VITens os_vitens, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigdao = new OS_VigDao();
            OS_VItensDao os_vitensdao = new OS_VItensDao();

            ContrVigDao contrvigdao = new ContrVigDao();
            CtrItensDao ctritensdao = new CtrItensDao();

            //Gerando Contrato Anexo
            if (os_vig.getContrato() != null) {

            }

            //Caso o Contrato Anexo seja gerado com sucesso
            if (os_vig.getContrato() != null) {

                //Gerando itens Contrato Anexo
                //Gerando OS_Vig
                //Gerando OS_Vitens
            }
        } catch (Exception e) {
            throw new Exception("Os_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<ContratosDoctos> listarDocumentosContrato(String contrato, String codFil, Persistencia persistencia) throws Exception {
        try {
            ContratosDoctosDao contratosDoctosDao = new ContratosDoctosDao();
            return contratosDoctosDao.listarDocumentos(contrato, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscacliente<message>" + e.getMessage());
        }
    }

    public List<CtrItens> listarItensFaturamentoContrato(String contrato, String codFil, Persistencia persistencia) throws Exception {
        try {
            CtrItensDao ctritenDao = new CtrItensDao();
            return ctritenDao.listarItensFaturamento(contrato, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscacliente<message>" + e.getMessage());
        }
    }

    public List<CtrItens> listarItensPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CtrItensDao ctritenDao = new CtrItensDao();
            return ctritenDao.listarItensPaginada(primeiro, linhas, idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("contratos.falhabuscacliente<message>" + e.getMessage());
        }
    }

    public int contagemItens(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CtrItensDao dao = new CtrItensDao();
            return dao.contagemItens(idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("OS_VigSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CtrItensAntReajustes> listarItensAnterioresPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CtrItensAntReajustesDao dao = new CtrItensAntReajustesDao();
            return dao.listarItensAnterioresPaginada(primeiro, linhas, idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("VigSatMobWeb.listarItensAnterioresPaginada<message>" + e.getMessage());
        }
    }

    public int contagemItensAnteriores(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CtrItensAntReajustesDao dao = new CtrItensAntReajustesDao();
            return dao.contagemItensAnteriores(idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("VigSatMobWeb.contagemItensAnteriores(<message>" + e.getMessage());
        }
    }

    public List<ContratosReaj> listarReajustesPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            ContratosReajDao dao = new ContratosReajDao();
            return dao.listarReajustesPaginada(primeiro, linhas, idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("VigSatMobWeb.listarItensAnterioresPaginada<message>" + e.getMessage());
        }
    }

    public int contagemReajustes(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            ContratosReajDao dao = new ContratosReajDao();
            return dao.contagemReajustes(idContrato, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("VigSatMobWeb.contagemReajustes(<message>" + e.getMessage());
        }
    }
}

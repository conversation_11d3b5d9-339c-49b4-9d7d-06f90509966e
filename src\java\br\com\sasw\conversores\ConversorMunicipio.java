/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import SasBeans.Municipios;
import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorMunicipios")
public class ConversorMunicipio implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Municipios municipios = new Municipios();

        municipios.setCodigo(new BigDecimal(value));

        return municipios;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return ((Municipios) value).getCodigo().toPlainString();
        } catch (Exception e) {
            return null;
        }
    }

}

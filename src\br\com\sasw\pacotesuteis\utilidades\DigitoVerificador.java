package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public class DigitoVerificador {

    /**
     * Devolve o Digito Verificador em modulo 11
     *
     * @param Vcalc - Numero a ser calculado o digito
     * @return - String com o Digito Verificador
     */
    public static String Modulo11(String Vcalc) throws Exception {
        String retorno;
        int I, Soma = 0, Mult = 1, resultado;
        try {
            for (I = Vcalc.length() - 1; I >= 0; I--) {
                Mult = Mult + 1;
                Soma += Integer.parseInt(Vcalc.substring(I, I + 1)) * Mult;
                if (Mult == 9) {
                    Mult = 1;
                }
            }
            resultado = Soma % 11;
            if (resultado <= 1) {
                retorno = "0";
            } else {
                resultado = 11 - resultado;
                retorno = String.valueOf(resultado);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Erro ao calcular DV modulo 11" + e.getMessage());
        }
    }

}

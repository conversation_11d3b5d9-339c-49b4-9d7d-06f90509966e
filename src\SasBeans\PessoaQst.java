/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PessoaQst {

    public PessoaQst() {
        // void constructor
    }

    private BigDecimal CodPessoa;
    private Integer CodQuestao;
    private Integer OrdQuestao;
    private Integer Resposta;
    private String RespostaDet;
    private LocalDate Data;
    private String Hora;

    public BigDecimal getCodPessoa() {
        return this.CodPessoa;
    }

    public void setCodPessoa(String codPessoa) {
        try {
            this.CodPessoa = new BigDecimal(codPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    public Integer getCodQuestao() {
        return this.CodQuestao;
    }

    public void setCodQuestao(Integer codQuestao) {
        this.CodQuestao = codQuestao;
    }

    public Integer getOrdQuestao() {
        return OrdQuestao;
    }

    public void setOrdQuestao(Integer OrdQuestao) {
        this.OrdQuestao = OrdQuestao;
    }

    public Integer getResposta() {
        return this.Resposta;
    }

    public void setResposta(Integer resposta) {
        this.Resposta = resposta;
    }

    public String getRespostaDet() {
        return this.RespostaDet;
    }

    public void setRespostaDet(String respostaDet) {
        this.RespostaDet = respostaDet;
    }

    public LocalDate getData() {
        return this.Data;
    }

    public void setData(LocalDate data) {
        this.Data = data;
    }

    public String getHora() {
        return this.Hora;
    }

    public void setHora(String hora) {
        this.Hora = hora;
    }
}

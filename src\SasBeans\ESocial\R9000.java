/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class R9000 {

    private String evtExclusao_id;
    private int sucesso;

    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideContri_tpInsc;
    private String ideContri_nrInsc;
    private String infoExclusao_tpEvento;
    private String infoExclusao_nrRecEvt;
    private String infoExclusao_perApur;

    public String getEvtExclusao_id() {
        return evtExclusao_id;
    }

    public void setEvtExclusao_id(String evtExclusao_id) {
        this.evtExclusao_id = evtExclusao_id;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeContri_tpInsc() {
        return ideContri_tpInsc;
    }

    public void setIdeContri_tpInsc(String ideContri_tpInsc) {
        this.ideContri_tpInsc = ideContri_tpInsc;
    }

    public String getIdeContri_nrInsc() {
        return ideContri_nrInsc;
    }

    public void setIdeContri_nrInsc(String ideContri_nrInsc) {
        this.ideContri_nrInsc = ideContri_nrInsc;
    }

    public String getInfoExclusao_tpEvento() {
        return infoExclusao_tpEvento;
    }

    public void setInfoExclusao_tpEvento(String infoExclusao_tpEvento) {
        this.infoExclusao_tpEvento = infoExclusao_tpEvento;
    }

    public String getInfoExclusao_nrRecEvt() {
        return infoExclusao_nrRecEvt;
    }

    public void setInfoExclusao_nrRecEvt(String infoExclusao_nrRecEvt) {
        this.infoExclusao_nrRecEvt = infoExclusao_nrRecEvt;
    }

    public String getInfoExclusao_perApur() {
        return infoExclusao_perApur;
    }

    public void setInfoExclusao_perApur(String infoExclusao_perApur) {
        this.infoExclusao_perApur = infoExclusao_perApur;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.SatEmail;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SatEmailDao {

    // create
    public boolean gravaSatEmail(SatEmail satemail, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into satemail (Sequencia,Host,Port,UserName,Password,FromAddress,"
                + "FromName,Dt_Alter,Hr_Alter) "
                + "Values (?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(satemail.getSequencia());
            consulta.setString(satemail.getHost());
            consulta.setInt(satemail.getPort());
            consulta.setString(satemail.getUserName());
            consulta.setString(satemail.getPassword());
            consulta.setString(satemail.getFromAddress());
            consulta.setString(satemail.getFromName());
            consulta.setString(satemail.getDt_Alter().toString());
            consulta.setString(satemail.getHr_Alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // read
    public List<SatEmail> buscaSatEmail(Persistencia persistencia) throws Exception {
        List<SatEmail> listSatEmail;
        try {
            SatEmail satemail;
            Consulta consult = new Consulta("select Sequencia,Host,Port,UserName,Password,FromAddress,"
                    + "FromName,Dt_Alter,Hr_Alter "
                    + "from satemail", persistencia);
            consult.select();
            listSatEmail = new ArrayList();
            while (consult.Proximo()) {
                satemail = new SatEmail();
                satemail.setSequencia(consult.getString("Sequencia"));
                satemail.setHost(consult.getString("Host"));
                satemail.setPort(consult.getInt("Port"));
                satemail.setUserName(consult.getString("UserName"));
                satemail.setPassword(consult.getString("Password"));
                satemail.setFromAddress(consult.getString("FromAddress"));
                satemail.setFromName(consult.getString("FromName"));
                satemail.setDt_Alter(consult.getDate("Dt_Alter").toLocalDate());
                satemail.setHr_Alter(consult.getString("Hr_Alter"));
                listSatEmail.add(satemail);
            }
            consult.Close();
        } catch (Exception e) {
            listSatEmail = null;
            throw new Exception("Falha ao executar buscaSatEmail " + e.getMessage());
        }
        return listSatEmail;
    }

    // update
    public void atualizarSatEmail(SatEmail satemail, Persistencia persistencia) throws Exception {
        String sql = "update satemail set Sequencia=?, Host=?, Port=?, UserName=?, Password=?, "
                + "FromAddress=?, FromName=?, Dt_Alter=?, Hr_Alter=? "
                + "where Sequencia=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(satemail.getSequencia());
            consulta.setString(satemail.getHost());
            consulta.setInt(satemail.getPort());
            consulta.setString(satemail.getUserName());
            consulta.setString(satemail.getPassword());
            consulta.setString(satemail.getFromAddress());
            consulta.setString(satemail.getFromName());
            consulta.setString(satemail.getDt_Alter().toString());
            consulta.setString(satemail.getHr_Alter());
            consulta.setBigDecimal(satemail.getSequencia());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao executar atualizarSatEmail " + e.getMessage());
        }
    }

    // delete
    public void deleteSatEmail(SatEmail satemail, Persistencia persistencia) throws Exception {
        String sql = "delete from satemail "
                + "where Sequencia=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(satemail.getSequencia());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao executar deleteSatEmail " + e.getMessage());
        }
    }
}

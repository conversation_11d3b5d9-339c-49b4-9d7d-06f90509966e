/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PropCml {

    private BigDecimal CodContato;
    private BigDecimal CodFil;
    private BigDecimal Codigo;
    private BigDecimal CodModelo;
    private BigDecimal CodPessoa;
    private BigDecimal CodPessoa2;
    private String Contato;
    private String Contrato;
    private LocalDate Data;
    private String Descricao;
    private String Descricao2;
    private String Descricao3;
    private String Descricao4;
    private String Dt_Alter;
    private LocalDate Dt_Incl;
    private LocalDate DtEntrega;
    private LocalDate DtLibera;
    private LocalDate DtValidade;
    private String email;
    private String Extraordin;
    private String Garantia;
    private String Hr_Alter;
    private String Hr_Incl;
    private String HrLibera;
    private BigDecimal Numero;
    private String ObsLibera;
    private String Operador;
    private String OperIncl;
    private String OperLibera;
    private String PrazoEntrega;
    private BigDecimal Proposta;
    private String RefProp;
    private String Sexo;
    private BigDecimal Situacao;
    private String TipoProp;
    private String Validade;
    private BigDecimal ValorPrevFat;
    private BigDecimal ValorProd;
    private BigDecimal ValorServ;

    private String NomeContato;
    private String NomeConsultor;
    private String NomeSituacao;

    public PropCml() {
        this.CodContato = new BigDecimal(0);
        this.CodFil = new BigDecimal(0);
        this.Codigo = new BigDecimal(0);
        this.CodModelo = new BigDecimal(0);
        this.CodPessoa = new BigDecimal(0);
        this.CodPessoa2 = new BigDecimal(0);
        this.Contato = "";
        this.Contrato = "";
        this.Data = LocalDate.now();
        this.Descricao = "";
        this.Descricao2 = "";
        this.Descricao3 = "";
        this.Descricao4 = "";
        this.Dt_Alter = "";
        this.Dt_Incl = LocalDate.now();
        this.DtEntrega = LocalDate.now();
        this.DtLibera = LocalDate.now();
        this.DtValidade = LocalDate.now();
        this.email = "";
        this.Extraordin = "";
        this.Garantia = "";
        this.Hr_Alter = "";
        this.Hr_Incl = "";
        this.HrLibera = "";
        this.Numero = new BigDecimal(0);
        this.ObsLibera = "";
        this.Operador = "";
        this.OperIncl = "";
        this.OperLibera = "";
        this.PrazoEntrega = "";
        this.Proposta = new BigDecimal(0);
        this.RefProp = "";
        this.Sexo = "";
        this.Situacao = new BigDecimal(0);
        this.TipoProp = "";
        this.Validade = "";
        this.ValorPrevFat = new BigDecimal(0);
        this.ValorProd = new BigDecimal(0);
        this.ValorServ = new BigDecimal(0);
    }

    public BigDecimal getCodContato() {
        return CodContato;
    }

    public void setCodContato(BigDecimal CodContato) {
        this.CodContato = CodContato;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = new BigDecimal(CodFil);
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public BigDecimal getCodModelo() {
        return CodModelo;
    }

    public void setCodModelo(BigDecimal CodModelo) {
        this.CodModelo = CodModelo;
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(BigDecimal CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public BigDecimal getCodPessoa2() {
        return CodPessoa2;
    }

    public void setCodPessoa2(BigDecimal CodPessoa2) {
        this.CodPessoa2 = CodPessoa2;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDescricao2() {
        return Descricao2;
    }

    public void setDescricao2(String Descricao2) {
        this.Descricao2 = Descricao2;
    }

    public String getDescricao3() {
        return Descricao3;
    }

    public void setDescricao3(String Descricao3) {
        this.Descricao3 = Descricao3;
    }

    public String getDescricao4() {
        return Descricao4;
    }

    public void setDescricao4(String Descricao4) {
        this.Descricao4 = Descricao4;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public LocalDate getDtEntrega() {
        return DtEntrega;
    }

    public void setDtEntrega(LocalDate DtEntrega) {
        this.DtEntrega = DtEntrega;
    }

    public LocalDate getDtLibera() {
        return DtLibera;
    }

    public void setDtLibera(LocalDate DtLibera) {
        this.DtLibera = DtLibera;
    }

    public LocalDate getDtValidade() {
        return DtValidade;
    }

    public void setDtValidade(LocalDate DtValidade) {
        this.DtValidade = DtValidade;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getExtraordin() {
        return Extraordin;
    }

    public void setExtraordin(String Extraordin) {
        this.Extraordin = Extraordin;
    }

    public String getGarantia() {
        return Garantia;
    }

    public void setGarantia(String Garantia) {
        this.Garantia = Garantia;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getHrLibera() {
        return HrLibera;
    }

    public void setHrLibera(String HrLibera) {
        this.HrLibera = HrLibera;
    }

    public BigDecimal getNumero() {
        return Numero;
    }

    public void setNumero(BigDecimal Numero) {
        this.Numero = Numero;
    }

    public String getObsLibera() {
        return ObsLibera;
    }

    public void setObsLibera(String ObsLibera) {
        this.ObsLibera = ObsLibera;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getOperLibera() {
        return OperLibera;
    }

    public void setOperLibera(String OperLibera) {
        this.OperLibera = OperLibera;
    }

    public String getPrazoEntrega() {
        return PrazoEntrega;
    }

    public void setPrazoEntrega(String PrazoEntrega) {
        this.PrazoEntrega = PrazoEntrega;
    }

    public BigDecimal getProposta() {
        return Proposta;
    }

    public void setProposta(BigDecimal Proposta) {
        this.Proposta = Proposta;
    }

    public String getRefProp() {
        return RefProp;
    }

    public void setRefProp(String RefProp) {
        this.RefProp = RefProp;
    }

    public String getSexo() {
        return Sexo;
    }

    public void setSexo(String Sexo) {
        this.Sexo = Sexo;
    }

    public BigDecimal getSituacao() {
        return Situacao;
    }

    public void setSituacao(BigDecimal Situacao) {
        try {
            this.Situacao = Situacao;
        } catch (Exception e) {
            this.Situacao = BigDecimal.ZERO;
        }
    }

    public String getTipoProp() {
        return TipoProp;
    }

    public void setTipoProp(String TipoProp) {
        this.TipoProp = TipoProp;
    }

    public String getValidade() {
        return Validade;
    }

    public void setValidade(String Validade) {
        this.Validade = Validade;
    }

    public BigDecimal getValorPrevFat() {
        return ValorPrevFat;
    }

    public void setValorPrevFat(BigDecimal ValorPrevFat) {
        this.ValorPrevFat = ValorPrevFat;
    }

    public BigDecimal getValorProd() {
        return ValorProd;
    }

    public void setValorProd(BigDecimal ValorProd) {
        this.ValorProd = ValorProd;
    }

    public BigDecimal getValorServ() {
        return ValorServ;
    }

    public void setValorServ(BigDecimal ValorServ) {
        this.ValorServ = ValorServ;
    }

    public String getNomeContato() {
        return NomeContato;
    }

    public void setNomeContato(String NomeContato) {
        this.NomeContato = NomeContato;
    }

    public String getNomeConsultor() {
        return NomeConsultor;
    }

    public void setNomeConsultor(String NomeConsultor) {
        this.NomeConsultor = NomeConsultor;
    }

    public String getNomeSituacao() {
        return NomeSituacao;
    }

    public void setNomeSituacao(String NomeSituacao) {
        this.NomeSituacao = NomeSituacao;
    }
}

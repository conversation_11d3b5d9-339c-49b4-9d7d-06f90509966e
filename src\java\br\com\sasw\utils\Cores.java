/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.utils;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Cores {

    public static int gerarCor(int n, int total) {
        if (n < 1) {
            n = 1; // defaults to one color - avoid divide by zero
        }
        return n * (360 / total) % 360;
    }

    public static List<String> gerarCoresAlternadas(int total) {
        List<String> retorno = new ArrayList<>();

        for (int i = 0, j = 0; i < total; i++) {
            if (i % 2 == 0) {
                retorno.add("hsl(" + gerarCor(j, total) + ", 100%, 50%)");
            } else {
                retorno.add("hsl(" + gerarCor(total / 2 + j, total) + ", 100%, 50%)");
                j++;
            }
        }

        return retorno;
    }
}

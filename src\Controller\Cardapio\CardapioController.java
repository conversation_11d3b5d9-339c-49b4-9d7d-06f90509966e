/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Cardapio;

import Dados.Persistencia;
import SasBeans.Cardapio;
import SasBeans.CardapioDia;
import SasBeansCompostas.CardapioDiaDietaCardapio;
import SasDaos.CardapioDao;
import SasDaos.CardapioDiaDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CardapioController {

    public List<CardapioDia> listarCardapiosDiaPaginada(
            int primeiro,
            int linhas,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.listarCardapiosDiaPaginada(primeiro, linhas, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.listarCardapiosDiaPaginada - " + e.getMessage());
        }
    }

    public int listarCardapiosDiaPaginada(
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.contarCardapiosDiaTotal(filters, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.listarCardapiosDiaPaginada - " + e.getMessage());
        }
    }

    public List<Cardapio> listarCardapiosPaginada(
            int primeiro,
            int linhas,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CardapioDao dao = new CardapioDao();
            return dao.listarCardapiosPaginada(primeiro, linhas, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.listarCardapiosPaginada - " + e.getMessage());
        }
    }

    public List<Cardapio> listaCardapiosCadastrados(Persistencia persistencia) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.listaCardapiosCadastrados(persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.listaCardapiosCadastrados - " + e.getMessage());
        }
    }

    public List<Cardapio> listaCardapiosCadastradosPaginado(int primeiro, int ultimo, Persistencia persistencia, String where) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.listaCardapiosCadastradosPaginado(primeiro, ultimo, persistencia, where);
        } catch (Exception e) {
            throw new Exception("CardapioController.listaCardapiosCadastrados - " + e.getMessage());
        }
    }

    public void salvarCadastroCardapio(Cardapio cardapio, Persistencia persistencia) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            dao.salvarCadastroCardapio(cardapio, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.salvarCadastroCardapio - " + e.getMessage());
        }
    }

    public int contarCardapiosTotal(
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CardapioDao dao = new CardapioDao();
            return dao.contarCardapiosTotal(filters, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.listarCardapiosPaginada - " + e.getMessage());
        }
    }

    public List<CardapioDiaDietaCardapio> listarCardapioDiaDietaCardapioPaginada(
            int primeiro,
            int linhas,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.listarCardapiosDiaComDietaECardapioPaginada(primeiro, linhas, filters, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.listarCardapioDiaDietaCardapioPaginada - " + e.getMessage());
        }
    }

    public int contarCardapiosDiaComDietaECardapioTotal(
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.contarCardapiosDiaComDietaECardapioTotal(filters, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.contarCardapiosDiaComDietaECardapioTotal - " + e.getMessage());
        }
    }

    // TODO
    public int cardapioDeDia() {
        CardapioDao dao = new CardapioDao();

        return -1;
    }

    public boolean cadastrarCardapioDia(CardapioDia cardapioDia, Persistencia persistencia)
            throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.cadastrarCardapioDia(cardapioDia, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.contarCardapiosDiaComDietaECardapioTotal - " + e.getMessage());
        }
    }

    public boolean editarCardapioDia(CardapioDia cardapioDia, Persistencia persistencia)
            throws Exception {
        try {
            CardapioDiaDao dao = new CardapioDiaDao();
            return dao.editarCardapioDia(cardapioDia, persistencia);
        } catch (Exception e) {
            throw new Exception("CardapioController.contarCardapiosDiaComDietaECardapioTotal - " + e.getMessage());
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class RHHorario {

    private BigDecimal CodFil;
    private BigDecimal Codigo;
    private String Descricao;
    private BigDecimal PontoElet;
    private BigDecimal HorarioFixo;
    private String TipoComp;
    private BigDecimal CHComp;
    private BigDecimal HsMaxDiaSem;
    private BigDecimal HsMaxSabDom;
    private BigDecimal HsMaxDiaSem2;
    private BigDecimal HsMaxSabDom2;
    private BigDecimal JornadaMax;
    private BigDecimal IntervMin;
    private BigDecimal D1;
    private String Hora101;
    private String Hora102;
    private String Hora103;
    private String Hora104;
    private BigDecimal D2;
    private String Hora201;
    private String Hora202;
    private String Hora203;
    private String Hora204;
    private BigDecimal D3;
    private String Hora301;
    private String Hora302;
    private String Hora303;
    private String Hora304;
    private BigDecimal D4;
    private String Hora401;
    private String Hora402;
    private String Hora403;
    private String Hora404;
    private BigDecimal D5;
    private String Hora501;
    private String Hora502;
    private String Hora503;
    private String Hora504;
    private BigDecimal D6;
    private String Hora601;
    private String Hora602;
    private String Hora603;
    private String Hora604;
    private BigDecimal D7;
    private String Hora701;
    private String Hora702;
    private String Hora703;
    private String Hora704;
    private BigDecimal D8;
    private String Hora801;
    private String Hora802;
    private String Hora803;
    private String Hora804;
    private BigDecimal HrDUDiu;
    private BigDecimal HrDUNotDiu;
    private BigDecimal HrDUNot;
    private BigDecimal HrSabDiu;
    private BigDecimal HrSabNotDiu;
    private BigDecimal HrSabNot;
    private BigDecimal HrDomDiu;
    private BigDecimal HrDomNotDiu;
    private BigDecimal HrDomNot;
    private BigDecimal HECC;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public BigDecimal getPontoElet() {
        return PontoElet;
    }

    public void setPontoElet(BigDecimal PontoElet) {
        this.PontoElet = PontoElet;
    }

    public BigDecimal getHorarioFixo() {
        return HorarioFixo;
    }

    public void setHorarioFixo(BigDecimal HorarioFixo) {
        this.HorarioFixo = HorarioFixo;
    }

    public String getTipoComp() {
        return TipoComp;
    }

    public void setTipoComp(String TipoComp) {
        this.TipoComp = TipoComp;
    }

    public BigDecimal getCHComp() {
        return CHComp;
    }

    public void setCHComp(BigDecimal CHComp) {
        this.CHComp = CHComp;
    }

    public BigDecimal getHsMaxDiaSem() {
        return HsMaxDiaSem;
    }

    public void setHsMaxDiaSem(BigDecimal HsMaxDiaSem) {
        this.HsMaxDiaSem = HsMaxDiaSem;
    }

    public BigDecimal getHsMaxSabDom() {
        return HsMaxSabDom;
    }

    public void setHsMaxSabDom(BigDecimal HsMaxSabDom) {
        this.HsMaxSabDom = HsMaxSabDom;
    }

    public BigDecimal getHsMaxDiaSem2() {
        return HsMaxDiaSem2;
    }

    public void setHsMaxDiaSem2(BigDecimal HsMaxDiaSem2) {
        this.HsMaxDiaSem2 = HsMaxDiaSem2;
    }

    public BigDecimal getHsMaxSabDom2() {
        return HsMaxSabDom2;
    }

    public void setHsMaxSabDom2(BigDecimal HsMaxSabDom2) {
        this.HsMaxSabDom2 = HsMaxSabDom2;
    }

    public BigDecimal getJornadaMax() {
        return JornadaMax;
    }

    public void setJornadaMax(BigDecimal JornadaMax) {
        this.JornadaMax = JornadaMax;
    }

    public BigDecimal getIntervMin() {
        return IntervMin;
    }

    public void setIntervMin(BigDecimal IntervMin) {
        this.IntervMin = IntervMin;
    }

    public BigDecimal getD1() {
        return D1;
    }

    public void setD1(BigDecimal D1) {
        this.D1 = D1;
    }

    public String getHora101() {
        return Hora101;
    }

    public void setHora101(String Hora101) {
        this.Hora101 = Hora101;
    }

    public String getHora102() {
        return Hora102;
    }

    public void setHora102(String Hora102) {
        this.Hora102 = Hora102;
    }

    public String getHora103() {
        return Hora103;
    }

    public void setHora103(String Hora103) {
        this.Hora103 = Hora103;
    }

    public String getHora104() {
        return Hora104;
    }

    public void setHora104(String Hora104) {
        this.Hora104 = Hora104;
    }

    public BigDecimal getD2() {
        return D2;
    }

    public void setD2(BigDecimal D2) {
        this.D2 = D2;
    }

    public String getHora201() {
        return Hora201;
    }

    public void setHora201(String Hora201) {
        this.Hora201 = Hora201;
    }

    public String getHora202() {
        return Hora202;
    }

    public void setHora202(String Hora202) {
        this.Hora202 = Hora202;
    }

    public String getHora203() {
        return Hora203;
    }

    public void setHora203(String Hora203) {
        this.Hora203 = Hora203;
    }

    public String getHora204() {
        return Hora204;
    }

    public void setHora204(String Hora204) {
        this.Hora204 = Hora204;
    }

    public BigDecimal getD3() {
        return D3;
    }

    public void setD3(BigDecimal D3) {
        this.D3 = D3;
    }

    public String getHora301() {
        return Hora301;
    }

    public void setHora301(String Hora301) {
        this.Hora301 = Hora301;
    }

    public String getHora302() {
        return Hora302;
    }

    public void setHora302(String Hora302) {
        this.Hora302 = Hora302;
    }

    public String getHora303() {
        return Hora303;
    }

    public void setHora303(String Hora303) {
        this.Hora303 = Hora303;
    }

    public String getHora304() {
        return Hora304;
    }

    public void setHora304(String Hora304) {
        this.Hora304 = Hora304;
    }

    public BigDecimal getD4() {
        return D4;
    }

    public void setD4(BigDecimal D4) {
        this.D4 = D4;
    }

    public String getHora401() {
        return Hora401;
    }

    public void setHora401(String Hora401) {
        this.Hora401 = Hora401;
    }

    public String getHora402() {
        return Hora402;
    }

    public void setHora402(String Hora402) {
        this.Hora402 = Hora402;
    }

    public String getHora403() {
        return Hora403;
    }

    public void setHora403(String Hora403) {
        this.Hora403 = Hora403;
    }

    public String getHora404() {
        return Hora404;
    }

    public void setHora404(String Hora404) {
        this.Hora404 = Hora404;
    }

    public BigDecimal getD5() {
        return D5;
    }

    public void setD5(BigDecimal D5) {
        this.D5 = D5;
    }

    public String getHora501() {
        return Hora501;
    }

    public void setHora501(String Hora501) {
        this.Hora501 = Hora501;
    }

    public String getHora502() {
        return Hora502;
    }

    public void setHora502(String Hora502) {
        this.Hora502 = Hora502;
    }

    public String getHora503() {
        return Hora503;
    }

    public void setHora503(String Hora503) {
        this.Hora503 = Hora503;
    }

    public String getHora504() {
        return Hora504;
    }

    public void setHora504(String Hora504) {
        this.Hora504 = Hora504;
    }

    public BigDecimal getD6() {
        return D6;
    }

    public void setD6(BigDecimal D6) {
        this.D6 = D6;
    }

    public String getHora601() {
        return Hora601;
    }

    public void setHora601(String Hora601) {
        this.Hora601 = Hora601;
    }

    public String getHora602() {
        return Hora602;
    }

    public void setHora602(String Hora602) {
        this.Hora602 = Hora602;
    }

    public String getHora603() {
        return Hora603;
    }

    public void setHora603(String Hora603) {
        this.Hora603 = Hora603;
    }

    public String getHora604() {
        return Hora604;
    }

    public void setHora604(String Hora604) {
        this.Hora604 = Hora604;
    }

    public BigDecimal getD7() {
        return D7;
    }

    public void setD7(BigDecimal D7) {
        this.D7 = D7;
    }

    public String getHora701() {
        return Hora701;
    }

    public void setHora701(String Hora701) {
        this.Hora701 = Hora701;
    }

    public String getHora702() {
        return Hora702;
    }

    public void setHora702(String Hora702) {
        this.Hora702 = Hora702;
    }

    public String getHora703() {
        return Hora703;
    }

    public void setHora703(String Hora703) {
        this.Hora703 = Hora703;
    }

    public String getHora704() {
        return Hora704;
    }

    public void setHora704(String Hora704) {
        this.Hora704 = Hora704;
    }

    public BigDecimal getD8() {
        return D8;
    }

    public void setD8(BigDecimal D8) {
        this.D8 = D8;
    }

    public String getHora801() {
        return Hora801;
    }

    public void setHora801(String Hora801) {
        this.Hora801 = Hora801;
    }

    public String getHora802() {
        return Hora802;
    }

    public void setHora802(String Hora802) {
        this.Hora802 = Hora802;
    }

    public String getHora803() {
        return Hora803;
    }

    public void setHora803(String Hora803) {
        this.Hora803 = Hora803;
    }

    public String getHora804() {
        return Hora804;
    }

    public void setHora804(String Hora804) {
        this.Hora804 = Hora804;
    }

    public BigDecimal getHrDUDiu() {
        return HrDUDiu;
    }

    public void setHrDUDiu(BigDecimal HrDUDiu) {
        this.HrDUDiu = HrDUDiu;
    }

    public BigDecimal getHrDUNotDiu() {
        return HrDUNotDiu;
    }

    public void setHrDUNotDiu(BigDecimal HrDUNotDiu) {
        this.HrDUNotDiu = HrDUNotDiu;
    }

    public BigDecimal getHrDUNot() {
        return HrDUNot;
    }

    public void setHrDUNot(BigDecimal HrDUNot) {
        this.HrDUNot = HrDUNot;
    }

    public BigDecimal getHrSabDiu() {
        return HrSabDiu;
    }

    public void setHrSabDiu(BigDecimal HrSabDiu) {
        this.HrSabDiu = HrSabDiu;
    }

    public BigDecimal getHrSabNotDiu() {
        return HrSabNotDiu;
    }

    public void setHrSabNotDiu(BigDecimal HrSabNotDiu) {
        this.HrSabNotDiu = HrSabNotDiu;
    }

    public BigDecimal getHrSabNot() {
        return HrSabNot;
    }

    public void setHrSabNot(BigDecimal HrSabNot) {
        this.HrSabNot = HrSabNot;
    }

    public BigDecimal getHrDomDiu() {
        return HrDomDiu;
    }

    public void setHrDomDiu(BigDecimal HrDomDiu) {
        this.HrDomDiu = HrDomDiu;
    }

    public BigDecimal getHrDomNotDiu() {
        return HrDomNotDiu;
    }

    public void setHrDomNotDiu(BigDecimal HrDomNotDiu) {
        this.HrDomNotDiu = HrDomNotDiu;
    }

    public BigDecimal getHrDomNot() {
        return HrDomNot;
    }

    public void setHrDomNot(BigDecimal HrDomNot) {
        this.HrDomNot = HrDomNot;
    }

    public BigDecimal getHECC() {
        return HECC;
    }

    public void setHECC(BigDecimal HECC) {
        this.HECC = HECC;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 23 * hash + Objects.hashCode(this.CodFil);
        hash = 23 * hash + Objects.hashCode(this.Codigo);
        hash = 23 * hash + Objects.hashCode(this.Descricao);
        hash = 23 * hash + Objects.hashCode(this.PontoElet);
        hash = 23 * hash + Objects.hashCode(this.HorarioFixo);
        hash = 23 * hash + Objects.hashCode(this.TipoComp);
        hash = 23 * hash + Objects.hashCode(this.CHComp);
        hash = 23 * hash + Objects.hashCode(this.HsMaxDiaSem);
        hash = 23 * hash + Objects.hashCode(this.HsMaxSabDom);
        hash = 23 * hash + Objects.hashCode(this.HsMaxDiaSem2);
        hash = 23 * hash + Objects.hashCode(this.HsMaxSabDom2);
        hash = 23 * hash + Objects.hashCode(this.JornadaMax);
        hash = 23 * hash + Objects.hashCode(this.IntervMin);
        hash = 23 * hash + Objects.hashCode(this.D1);
        hash = 23 * hash + Objects.hashCode(this.Hora101);
        hash = 23 * hash + Objects.hashCode(this.Hora102);
        hash = 23 * hash + Objects.hashCode(this.Hora103);
        hash = 23 * hash + Objects.hashCode(this.Hora104);
        hash = 23 * hash + Objects.hashCode(this.D2);
        hash = 23 * hash + Objects.hashCode(this.Hora201);
        hash = 23 * hash + Objects.hashCode(this.Hora202);
        hash = 23 * hash + Objects.hashCode(this.Hora203);
        hash = 23 * hash + Objects.hashCode(this.Hora204);
        hash = 23 * hash + Objects.hashCode(this.D3);
        hash = 23 * hash + Objects.hashCode(this.Hora301);
        hash = 23 * hash + Objects.hashCode(this.Hora302);
        hash = 23 * hash + Objects.hashCode(this.Hora303);
        hash = 23 * hash + Objects.hashCode(this.Hora304);
        hash = 23 * hash + Objects.hashCode(this.D4);
        hash = 23 * hash + Objects.hashCode(this.Hora401);
        hash = 23 * hash + Objects.hashCode(this.Hora402);
        hash = 23 * hash + Objects.hashCode(this.Hora403);
        hash = 23 * hash + Objects.hashCode(this.Hora404);
        hash = 23 * hash + Objects.hashCode(this.D5);
        hash = 23 * hash + Objects.hashCode(this.Hora501);
        hash = 23 * hash + Objects.hashCode(this.Hora502);
        hash = 23 * hash + Objects.hashCode(this.Hora503);
        hash = 23 * hash + Objects.hashCode(this.Hora504);
        hash = 23 * hash + Objects.hashCode(this.D6);
        hash = 23 * hash + Objects.hashCode(this.Hora601);
        hash = 23 * hash + Objects.hashCode(this.Hora602);
        hash = 23 * hash + Objects.hashCode(this.Hora603);
        hash = 23 * hash + Objects.hashCode(this.Hora604);
        hash = 23 * hash + Objects.hashCode(this.D7);
        hash = 23 * hash + Objects.hashCode(this.Hora701);
        hash = 23 * hash + Objects.hashCode(this.Hora702);
        hash = 23 * hash + Objects.hashCode(this.Hora703);
        hash = 23 * hash + Objects.hashCode(this.Hora704);
        hash = 23 * hash + Objects.hashCode(this.D8);
        hash = 23 * hash + Objects.hashCode(this.Hora801);
        hash = 23 * hash + Objects.hashCode(this.Hora802);
        hash = 23 * hash + Objects.hashCode(this.Hora803);
        hash = 23 * hash + Objects.hashCode(this.Hora804);
        hash = 23 * hash + Objects.hashCode(this.HrDUDiu);
        hash = 23 * hash + Objects.hashCode(this.HrDUNotDiu);
        hash = 23 * hash + Objects.hashCode(this.HrDUNot);
        hash = 23 * hash + Objects.hashCode(this.HrSabDiu);
        hash = 23 * hash + Objects.hashCode(this.HrSabNotDiu);
        hash = 23 * hash + Objects.hashCode(this.HrSabNot);
        hash = 23 * hash + Objects.hashCode(this.HrDomDiu);
        hash = 23 * hash + Objects.hashCode(this.HrDomNotDiu);
        hash = 23 * hash + Objects.hashCode(this.HrDomNot);
        hash = 23 * hash + Objects.hashCode(this.HECC);
        hash = 23 * hash + Objects.hashCode(this.Operador);
        hash = 23 * hash + Objects.hashCode(this.Dt_Alter);
        hash = 23 * hash + Objects.hashCode(this.Hr_Alter);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RHHorario other = (RHHorario) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }
}

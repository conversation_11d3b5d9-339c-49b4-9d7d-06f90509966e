/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Tesouraria;

import Controller.CxForte.CustodiaSatMobWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.OS_Vig;
import SasBeans.TesEntrada;
import SasBeans.TesSaidas;
import SasBeansCompostas.TesConta;
import SasDaos.ClientesDao;
import SasDaos.CxFGuiasDao;
import SasDaos.GTVSeqDao;
import SasDaos.OS_VigDao;
import SasDaos.TesEntradaDao;
import SasDaos.TesSaidasDao;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TesourariaController {

    private final Persistencia persistencia;
    private final GTVSeqDao gtvSeqDao;
    private final TesEntradaDao tesEntradaDao;
    private final TesSaidasDao tesSaidasDao;
    private final CxFGuiasDao cxfguiasDao;
    private final ClientesDao clientesDao;
    private final OS_VigDao OSVigDao;
    private final CustodiaSatMobWeb custodiaSatMobWeb;

    public TesourariaController(Persistencia persistencia) {
        this.persistencia = persistencia;
        tesEntradaDao = new TesEntradaDao(persistencia);
        tesSaidasDao = new TesSaidasDao(persistencia);
        gtvSeqDao = new GTVSeqDao();
        cxfguiasDao = new CxFGuiasDao(persistencia);
        clientesDao = new ClientesDao();
        OSVigDao = new OS_VigDao();
        custodiaSatMobWeb = new CustodiaSatMobWeb();
    }

    public List<TesEntrada> allTesEntradasPaginada(
            int first,
            int pageSize,
            Map<String, Object> filters
    ) throws Exception {
        BigDecimal codFil = filters.get("codFil") != null ? new BigDecimal((String) filters.get("codFil")) : null;
        String data = (String) filters.get("data");
        boolean somentePendentes = (boolean) filters.get("somentePendentes");
        String guia = filters.get("guia") != null ? (String) filters.get("guia") : null;
        String cliente1 = (String) filters.get("cliente1");
        String cliente2 = (String) filters.get("cliente2");

        return tesEntradaDao.allTesEntradasPaginada(
                first,
                pageSize,
                codFil,
                data,
                somentePendentes,
                guia,
                cliente1,
                cliente2
        );
    }

    public int contagemTesEntradas(Map<String, Object> filters) throws Exception {
        BigDecimal codFil = filters.get("codFil") != null ? new BigDecimal((String) filters.get("codFil")) : null;
        String data = (String) filters.get("data");
        boolean somentePendentes = (boolean) filters.get("somentePendentes");
        String guia = filters.get("guia") != null ? (String) filters.get("guia") : null;
        String cliente1 = (String) filters.get("cliente1");
        String cliente2 = (String) filters.get("cliente2");

        return tesEntradaDao.contagemTesEntradas(
                codFil,
                data,
                somentePendentes,
                guia,
                cliente1,
                cliente2
        );
    }

    public List<TesConta> allTesContaPaginada(
            int first,
            int pageSize,
            Map<String, String> filters
    ) throws Exception {
        String codigo = filters.get("codigo") != null ? (String) filters.get("codigo") : null;
        String descricao = filters.get("descricao") != null ? (String) filters.get("descricao") : null;

        return tesEntradaDao.allTesContaPaginada(
                first,
                pageSize,
                codigo,
                descricao
        );
    }

    public TesConta getTesContaById(String codigo) throws Exception {
        return tesEntradaDao.getTesContaById(codigo);
    }

    public TesConta getTiposrvcliById(String codigo) throws Exception {
        return tesEntradaDao.getTiposrvcliById(codigo);
    }

    public boolean validaSerieGuia(String serie) throws Exception {
        return gtvSeqDao.validaSerieGuia(persistencia, serie.toUpperCase());
    }

    public CxFGuias getCxFGuiasById(String codFil, String guia, String serie) throws Exception {
        return cxfguiasDao.getCxFGuiasById(codFil, guia, serie.toUpperCase());
    }

    public Clientes buscarCliente(String codigo, String codFil, Persistencia persistencia) throws Exception {
        return clientesDao.buscarCliente(codigo, codFil, persistencia);
    }

    public TesConta getTesContaDeTesouraria() throws Exception {
        return tesEntradaDao.getTesContaDeTesouraria();
    }

    public TesSaidas getTesSaidasById(String codFil, String guia, String serie) throws Exception {
        return tesSaidasDao.getTesSaidasById(codFil, guia, serie);
    }

    // obtem TesEntrada já existente, ou monta uma parcial não-existente em banco
    public TesEntrada obterTesentrada(
            String codFilNum,
            String guia,
            String serie,
            String codPessoa,
            String banco
    ) throws Exception {
        return tesEntradaDao.getTesEntradasById(codFilNum, guia, serie, codPessoa, banco);
    }

    public TesEntrada obterTesEntradaDeCxFGuia(
            String codFilNum,
            String guia,
            String serie,
            String codPessoa,
            String banco
    ) throws Exception {
        CxFGuias cxFGuia = getCxFGuiasById(codFilNum, guia, serie);
        return cxFGuia != null
                ? obterTesEntradaDeCxFGuia(cxFGuia, codFilNum, codPessoa, banco)
                : null;
    }

    private TesEntrada obterTesEntradaDeCxFGuia(CxFGuias cxFGuia, String codFil, String codPessoa, String banco) throws Exception {
        TesEntrada tesEntrada = new TesEntrada();

        String codFilCx = cxFGuia.getCodFil();
        tesEntrada.setCodFil(codFilCx);
        tesEntrada.setCodCli1(cxFGuia.getCliOri());
        tesEntrada.setNRed1(cxFGuia.getNRedCli());
        tesEntrada.setCodCli1(cxFGuia.getCliDst());
        tesEntrada.setNRed2(cxFGuia.getNRedDst());
        tesEntrada.setOS(cxFGuia.getOS());
        tesEntrada.setOs_vig(obterOSDeCxFGuia(cxFGuia, codFil, codPessoa, banco));
        tesEntrada.setConta(obterContaDeCxFGuia());

        return tesEntrada;
    }

    public boolean guiaNaoBaixada(CxFGuias cxFGuia) {
        // TODO: colocar regras distintas para CONFEDERAL e TRANSFEDERAL
        return !cxFGuia.getCliDst().equals("")
                && !cxFGuia.getDtSai().toString().equals("");
    }

    private OS_Vig obterOSDeCxFGuia(CxFGuias cxFGuia, String codFil, String codPessoa, String banco) throws Exception {
        String OS = cxFGuia.getOS();
        return OSVigDao.getOS_VigById(OS, codFil, codPessoa, banco, persistencia);
    }

    private TesConta obterContaDeCxFGuia() throws Exception {
        TesConta contaEdicao = getTesContaDeTesouraria();
        if (contaEdicao == null) {
            contaEdicao = new TesConta();
            contaEdicao.setTipo("0000");
        }
        return contaEdicao;
    }

    public TesEntrada obterTesEntradaDeTesSaida(
            String codFilNum,
            String guia,
            String serie,
            String codPessoa,
            String banco
    ) throws Exception {
        TesSaidas tesSaida = getTesSaidasById(codFilNum, guia, serie);
        return tesSaida != null
                ? obterTesEntradaDeTesSaida(tesSaida)
                : null;
    }

    private TesEntrada obterTesEntradaDeTesSaida(TesSaidas tesSaida) {
        TesEntrada tesEntrada = new TesEntrada();
        tesEntrada.setCodCli1(tesSaida.getCodCli1());
        tesEntrada.setCodCli2(tesSaida.getCodCli2());
        tesEntrada.setValor(tesSaida.getTotalGeral());

        String data = tesSaida.getData();
        if (!data.equals("")) {
            tesEntrada.setData(data);
        }

        return tesEntrada;
    }

    public List<CxFGuiasVol> buscarVolumes(String codFil, String guia, String serie) throws Exception {
        return custodiaSatMobWeb.listarVolumesGuia(guia, serie, codFil, persistencia);
    }
}

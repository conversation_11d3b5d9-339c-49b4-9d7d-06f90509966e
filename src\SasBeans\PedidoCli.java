/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PedidoCli {

    private BigDecimal Numero;
    private BigDecimal CodFil;
    private LocalDate Data;
    private String CodCli;
    private String Tipo;
    private String Arq;
    private String Origem;
    private String CNPJOri;
    private String TranspOri;
    private String CodExt;
    private int QtdeMalote;
    private int NumMalote;
    private String Banco;
    private String PedidoCli;
    private String PontoAtend;

    /**
     *
     * @return Numero
     */
    public BigDecimal getNumero() {
        return this.Numero;
    }

    /**
     * Set Numero
     *
     * @param Numero
     */
    public void setNumero(String Numero) {
        try {
            this.Numero = new BigDecimal(Numero);
        } catch (Exception e) {
            this.Numero = new BigDecimal("0");
        }
    }

    /**
     *
     * @return CodFil
     */
    public BigDecimal getCodFil() {
        return this.CodFil;
    }

    /**
     * Set CodFil
     *
     * @param CodFil
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Data
     */
    public LocalDate getData() {
        return this.Data;
    }

    /**
     * Set Data
     *
     * @param Data
     */
    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    /**
     *
     * @return CodCli
     */
    public String getCodCli() {
        return this.CodCli;
    }

    /**
     * Set CodCli
     *
     * @param CodCli
     */
    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    /**
     *
     * @return Tipo
     */
    public String getTipo() {
        return this.Tipo;
    }

    /**
     * Return Tipo
     *
     * @param Tipo
     */
    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    /**
     *
     * @return Arq
     */
    public String getArq() {
        return this.Arq;
    }

    /**
     * Set Arq
     *
     * @param Arq
     */
    public void setArq(String Arq) {
        this.Arq = Arq;
    }

    /**
     *
     * @return Origem
     */
    public String getOrigem() {
        return this.Origem;
    }

    /**
     * Set Origem
     *
     * @param Origem
     */
    public void setOrigem(String Origem) {
        this.Origem = Origem;
    }

    /**
     *
     * @return CNPJOri
     */
    public String getCNPJOri() {
        return this.CNPJOri;
    }

    /**
     * Set CNPJOri
     *
     * @param CNPJOri
     */
    public void setCNPJOri(String CNPJOri) {
        this.CNPJOri = CNPJOri;
    }

    /**
     *
     * @return TranspOri
     */
    public String getTranspOri() {
        return this.TranspOri;
    }

    /**
     * Set TranspOri
     *
     * @param TranspOri
     */
    public void setTranspOri(String TranspOri) {
        this.TranspOri = TranspOri;
    }

    /**
     *
     * @return CodExt
     */
    public String getCodExt() {
        return this.CodExt;
    }

    /**
     * Set CodExt
     *
     * @param CodExt
     */
    public void setCodExt(String CodExt) {
        this.CodExt = CodExt;
    }

    /**
     *
     * @return QtdeMalote
     */
    public int getQtdeMalote() {
        return this.QtdeMalote;
    }

    /**
     * Set QtdeMalote
     *
     * @param QtdeMalote
     */
    public void setQtdeMalote(int QtdeMalote) {
        this.QtdeMalote = QtdeMalote;
    }

    /**
     *
     * @return NumMalote
     */
    public int getNumMalote() {
        return this.NumMalote;
    }

    /**
     * Set NumMalote
     *
     * @param NumMalote
     */
    public void setNumMalote(int NumMalote) {
        this.NumMalote = NumMalote;
    }

    /**
     *
     * @return Banco
     */
    public String getBanco() {
        return this.Banco;
    }

    /**
     * Set Banco
     *
     * @param Banco
     */
    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    /**
     *
     * @return PedidoCli
     */
    public String getPedidoCli() {
        return this.PedidoCli;
    }

    /**
     * Set PedidoCli
     *
     * @param PedidoCli
     */
    public void setPedidoCli(String PedidoCli) {
        this.PedidoCli = PedidoCli;
    }

    /**
     *
     * @return PontoAtend
     */
    public String getPontoAtend() {
        return this.PontoAtend;
    }

    /**
     * Set PontoAtend
     *
     * @param PontoAtend
     */
    public void setPontoAtend(String PontoAtend) {
        this.PontoAtend = PontoAtend;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans.formatadas;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCxForte {

    private String QtdeGuias;
    private String QtdeVolumes;
    private String Parada;
    private String NRed;
    private String Hora;
    private String Data;
    private String NomeCompleto;

    public String getQtdeGuias() {
        return QtdeGuias;
    }

    public void setQtdeGuias(String QtdeGuias) {
        this.QtdeGuias = QtdeGuias;
    }

    public String getQtdeVolumes() {
        return QtdeVolumes;
    }

    public void setQtdeVolumes(String QtdeVolumes) {
        this.QtdeVolumes = QtdeVolumes;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getNomeCompleto() {
        return NomeCompleto;
    }

    public void setNomeCompleto(String NomeCompleto) {
        this.NomeCompleto = NomeCompleto;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }
}

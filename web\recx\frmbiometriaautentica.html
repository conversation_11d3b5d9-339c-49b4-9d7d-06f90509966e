<!DOCTYPE html>
<!--
//SASW Tecnologia
//SATMOB
//FrmBiometriaAutentica -HTML
//Autor : <PERSON>
//Data: 03/03/2022
//
//Atualizações de versão: 
//
//-->
<html lang="pt-BR">

    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <title>FrmBiometriaAutentica</title>
        <script defer src="face-api.min.js"></script>
        <script defer src="frmbiometriaautentica.js"></script>

        <link href="frmbiometriaautentica.css" rel="stylesheet"/>

    </head>
    <body>
        <div class="frmBiometriaAutentica">
            <div class="frmBiometriaAutentica-corpo">
                <div id="painelMenu" style="display: flex; justify-content: center; align-items: center;">
                    <div id="painelMenuEsq" style="width: 30%; display: flex; justify-content: center; align-items: center;">
                                        <div id="navbar-esq" style="width: 30%">
                                           <img style="border-radius: 100%; width: 70%;margin-left: 2%"src="layout-imgs/SatMobEw.png" alt="SatMOB" class="d-inline-block""/>
                                           <!--label style="width: 100%; font-size: 10px; font-weight: 500; color:#fff; margin-left: 2%; font-family: tahoma;">SatMob<label/-->
                                        </div>
                    </div>
                    <div style="width: 40%; display: table; justify-content: center; align-items: center;">
                        <img id="editlogo" style="width: 75%;" src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_satsasex.png" alt="SatMOB" />
                    </div>
                    <div style="width: 30%; display: table; justify-content: center; align-items: center;">
                        <!-- <span style="color: #ffffff">Modo:</span> -->
                        <input hidden id="editModo" 
                               style="dis;play: block; margin-left: auto; margin-right: auto; background-color: white; margin-top: 2%; font-size: auto; background-color: #002172; color: #ffffff; text-align: center; border: 0 none; outline: 0; width: 50%" readonly>

                    </div>
                </div>

                <div id="painelMensagens" style="display: none; justify-content: center; align-items: center; flex-direction: column; margin-top: 5%;">
                    <input type="text" id="editMensagem" style="padding: 3px 5px; margin: 1%; font-size: medium; border-radius: 10px; text-align:center; " size="60px" readonly/>
                </div>

               
                <div id="dlg-login" class="modal">
                   <div class="modal-content">
                      <div class="modal-header">
                          <label>Autenticação de Usuário</label>
                      </div>
                      <div class="modal-body" id="modal-textoCentral" style="display:flex; justify-content: center; align-items: center;  margin-top: 2%; flex-direction: column; ">
                          <div style="display:flex; justify-content: center; align-items: center;  margin-top: 2%;">
                                <img id="imgLogin" style="border-radius: 75%" src="layout-imgs/FotoND.jpg" width="75" height="80">
                                <div style="isplay:flex; justify-content: center; align-items: center;  margin-top: 2%; flex-direction: column; margin-left: 5%;">
                                    <label>Matrícula:</label>
                                    <input placeholder="Matrícula" type="text" id="editMatr"  
                                           style="padding: 1% 1%; margin: 1%; font-size: lower; border-radius: 10px; background-color: white; text-align:center; color: #000000;" size="15px" readonly/>
                                    <input hidden placeholder="Nome" type="text" id="editNome" 
                                           style="padding: 1% 1%; margin: 1%; font-size: lower; border-radius: 10px; background-color: white; text-align:center; color: #000000;" size="35px" readonly/>
                                    <input placeholder="Senha" type ="password" id="editSenha" 
                                           style="padding: 3px 5px; margin: 1%; font-size: medium; border-radius: 10px; float: left" ><br>
                                </div>
                          </div>
                          <div id="painelMensagensdlg" style="display: none; justify-content: center; align-items: center; flex-direction: column; margin-top: 5%;">
                                <input type="text" id="editMensagemdlg" style="background-color: #B0C4DE; padding: 1px 1px; margin: 1%; font-size: medium; border-radius: 10px; text-align:center; " size="40px" readonly/>
                          </div>
                            
                      </div>
                      <div class="modal-footer">
                          <button onclick="validarSenha()" id="btnEnviar" class="btn btn-primary" style="width: 75px">Enviar</button>
                          <button onclick="atualizar()" id="btnSairdlg" class="btn btn-primary" style="margin-left: 10px; width:75px;">Sair</button>
                         <!--br> 
                         <button class="btn" onclick="closeModal('dv-modal')">Enviar</button>
                         <br-->
                      </div>
                   </div>
                </div>
                

                

                <form target="POST">
                    <textarea hidden id="memoBase64Img1" name="base_img"></textarea>
                    <textarea hidden id="memoBase64Img2" name="base_img2"></textarea>
                    <textarea hidden id="memoBase64Img3" name="base_img3"></textarea>
                    <textarea hidden id="memoBase64Img4" name="base_img4"></textarea>

                    <img hidden id="vImgSaidaBox" src="layout-imgs/FotoND.jpg" width="90" height="95" >
                </form>

                <div hidden id="painelPrinc">
                    <section id="painelFoto" style="display: flex; justify-content: center; align-items: center;  margin-top: 2%;">
                        <div id="painelEsquerdo1" style="display: table; width: 5%; justify-content: center; align-items: center;  margin-top: 2%;">
                        </div>

                        <div id="border_wrap" style="display: none; overflow: hidden; justify-content: center; align-items: center; width: 80%; height: 35%">
                                            <video id="webCamera" style="position: fixed; margin: 35px; object-fit: cover;" width="496" height="344" autoplay muted></video>
                                                <div id="divPersonagem" style="display: flex; ">
                                                   <img hidden id="painelImgPersonagem" style="position: fixed; top: 20%; margin-left: 65%; border-radius: 75px" src="layout-imgs/sasw_sorrindo.gif" width="70" height="70"><br>
                                                   <input hidden id="editLbAviso" value="Sorria!" type="text" style="position: fixed; top:40%;margin-left:65%;text-align: center; border: 0 none; outline: 0; background-color: transparent" size="10" readonly/>
                                                </div>
                                                <div id="divMaskwebCam" style="position: relative; margin: 0 auto; text-align: center">
                                                    <img id="imgborder_wrap" style="margin-left: 0px" src="layout-imgs/background-bluex.png" width="100%" height="100%" >
                                                        <!-- width="517" height="492"  -->
                                            </div>
                        </div>

                        <div id="painelFotoReconhecida" style="display: none; overflow: hidden; justify-content: center; align-items: center; width: 75%;">
                            <img id="imgFotoReconhecida" style="position: fixed; width: 100%" src="layout-imgs/FotoND.jpg" > <!-- width="496" height="444" 496 344 -->
                                                <div id="divMaskFotoRec" style="position: relative; margin: 0 auto; text-align: center">
                                                    <img id="imgFotoRec" style="margin-left: 0px" src="layout-imgs/background-greenx.png" width="100%"  >				
                                                        <!-- width="517" height="492"  -->
                                            </div>
                        </div>

                        <div id="painelDireito2" style="display: table; width: 5%; justify-content: center; align-items: center;  margin-top: 2%;">
                        </div>
                    </section>
                </div>

                        <div id="painelResultado" style="display: none; justify-content: center; align-items: center;  margin-top: 1%;">
                           <label id="lbResultado" style="color:blue; font-family: 'Courier New'; font-weight:500;"></label>
                        </div>


                <div id="painelBotoes" style="display: flex; justify-content: center; align-items: center;  margin-top: 1%;">
                    <div style="display: flex; justify-content: center; align-items: center;  margin-top: 2%;">
                        <button hidden type="button" id="btncadBio1" onclick="habilitaBotoesFoto()"><div><img src="layout-imgs/imgbiometria.png" height="40px" ></div><div>Cadastrar Biometria</div></button>

                        <button hidden type="button" id="btnAutenticarUsuario" onclick="autenticarUsuario()"><div><img src="layout-imgs/imgbiometria.png" height="40px" ></div><div>Autentica Usuário</div></button>

                        <button hidden id="btnCompara3e4" onclick="comparaFoto3e4()">
                            Comparar Foto 3 e 4
                        </button>

                        <button hidden id="btnFoto1" onclick="cadastraFoto1()">
                            Capturar Foto 1
                        </button>

                        <button hidden id="btnFoto2" onclick="cadastraFoto2()">
                            Capturar Foto 2
                        </button>
                        <div id="painelSair">
                            <button type="button" id="btnSair" onclick="atualizar()"><div><img src="layout-imgs/btncancelar.png" height="40px" ></div><div>Sair</div></button>
                        </div>
                    </div>
                    <br>
                </div>
                <div id="painelEsquerdo" style="display: none; width: 100%; justify-content: center; align-items: center;  margin-top: 1%;">
                            <span hidden id="lbHora">00h</span><span hidden id="lbMinuto">00m</span><span hidden id="lbSegundo">00</span><br>
                            <img id="imgSrv1" src="layout-imgs/FotoND.jpg" width="55" height="57">
                            <img id="imgSrv2" src="layout-imgs/FotoND.jpg" width="55" height="57">
                            <img id="imgLocal1" src="layout-imgs/FotoND.jpg" width="55" height="57">
                            <img id="imgLocal2" src="layout-imgs/FotoND.jpg" width="55" height="57">
                            <img hidden  id="imgAutentica1" src="layout-imgs/FotoND.jpg" width="55" height="57">
                            <img hidden  id="imgAutentica2" src="layout-imgs/FotoND.jpg" width="55" height="57">
                </div>		
                <div id="painelDireito" style="display: none; width: 100; justify-content: center; align-items: center ">
                            <div id="painelAnaliseComparacao" style="display: none;">
                                <span>#1: </span><input id="editFaceApiCompara1"  type="text" style="background: transparent; margin-right: 10px" size="5" readonly/>
                                <span>#2: </span><input id="editFaceApiCompara2"  type="text" style="background: transparent; margin-right: 10px" size="5" readonly/>
                                <span>#3: </span><input id="editFaceApiCompara3"  type="text" style="background: transparent; margin-right: 10px" size="5" readonly/>
                                <span>#4: </span><input id="editFaceApiCompara4"  type="text" style="background: transparent; margin-right: 10px" size="5" readonly/>
                            </div>
                </div>		
                <div id="painelOpcoes" style="display: flex; justify-content: center; align-items: center;  margin-top: 2%;">
                     <div>
                        <input type = "checkbox" id="chkDetalhes" style="" value="1" onclick="chkDetalhesClick()" >
                        <label for="chkDetalhes">Mostrar detalhes:</label>
                     </div>
                </div>	
           </div>
        </div>
    </body>
</html>
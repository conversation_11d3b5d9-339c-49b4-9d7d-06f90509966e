/*
 */
package br.com.sasw.conversores;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorDataHora")
public class ConversorDataHora implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        DateTimeFormatter data = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        LocalDateTime dia = null;
        try {
            dia = LocalDateTime.parse(value.toString(), DateTimeFormatter.ISO_DATE_TIME);
            return dia.format(data);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return value.toString();
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2230;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2230Dao {

    public List<S2230> get(String codFil, String compet, String ambiente, boolean inicio, Persistencia persistencia) throws Exception {
        try {
            List<S2230> retorno = new ArrayList<>();
            String sql = "Select * from ("
                    + " Select '1' ideEvento_indRetif, '' ideEvento_nrRecibo, '1' ideEvento_procEmi, 'Satellite eSocial' ideEvento_verProc,"
                    + " '' iniAfastamento_observacao, '' ideVinculo_codCateg, '' iniAfastamento_tpAcidTransito,  "
                    + " '' infoCessao_cnpjCess, '' infoCessao_infOnus, '' infoMandSind_infOnusRemun, "
                    + " '' infoRetif_origRetif, '' infoRetif_tpProc, '' infoRetif_nrProc, '' iniAfastamento_infoMesmoMtv,  "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, "
                    + " Funcion.CPF ideVinculo_cpfTrab, Funcion.PIS ideVinculo_nisTrab, Funcion.Matr ideVinculo_matricula, "
                    + " Case when Len(Funcion.CodPonto) > 0 then CONVERT(VARCHAR, Funcion.CodPonto)  else CONVERT(VARCHAR, Funcion.Matr)  end ideVinculo_matriculaCodPonto,"
                    + " substring(replace(convert(varchar,F3_Afast.Dt_Ini,111),'/','-'),0,11) iniAfastamento_dtIniAfast, substring(replace(convert(varchar,FPFerias.DtINicioAq,111),'/','-'),0,11) iniAfastamento_dtInicio,   substring(replace(convert(varchar,FPFerias.DtFinalAq,111),'/','-'),0,11) iniAfastamento_dtFim,  "
                    //+ " Case when F3_Afast.Motivo = 'AT.MEDICO' and Len(isnull(F3_Afast.CID,'')) > 0 then '01' " //Acidente/Doença do trabalho
                    //+ "      when F3_Afast.Motivo = 'AT.MEDICO' and Len(isnull(F3_Afast.CID,'')) = 0 then '03' " //Acidente/Doença não relacionada ao trabalho
                    + " Case when F3_Afast.Motivo = 'AT.MEDICO' then '03' " //Acidente/Doença não relacionada ao trabalho
                    + "      when F3_Afast.MotivoSEFIP = 'O1' then '01' "   //O1 Afast Temp por Ac Trab prazo superior 15 dias
                    + "      when F3_Afast.MotivoSEFIP = 'O2' then '01' "   //O2 Novo Afast Temp por mesmo Ac Trab
                    + "      when F3_Afast.MotivoSEFIP = 'O3' then '01' "   // 
                    + "      when F3_Afast.MotivoSEFIP = 'P1' then '03' "   //
                    + "      when F3_Afast.MotivoSEFIP = 'P2' then '03' "   //
                    + "      when F3_Afast.MotivoSEFIP = 'P3' then '03' "   //
                    + "      when F3_Afast.MotivoSEFIP = 'U3' then '06' "   //
                    + "      when F3_Afast.Motivo      = 'FERIAS' then '15' "
                    + "      when F3_Afast.MotivoSEFIP = 'Q1' then '17' "
                    + "      when F3_Afast.MotivoSEFIP = 'Q2' then '17' "
                    + "      when F3_Afast.MotivoSEFIP = 'Q3' then '19' "
                    + "      when F3_Afast.MotivoSEFIP = 'Q4' then '20'	"
                    + "      when F3_Afast.MotivoSEFIP = 'Q5' then '20'	"
                    + "      when F3_Afast.MotivoSEFIP = 'Q6' then '20' "
                    + "      when F3_Afast.MotivoSEFIP = 'W' then '24'	 "
                    + "      when F3_Afast.MotivoSEFIP = 'R' then '29' "
                    + "      when F3_Afast.Motivo      = 'OUTROS' and F3_Afast.OBS = 'CARCERE' then '11' "
                    + " end iniAfastamento_codMotAfast, "
                    + " F3_Afast.CID infoAtestado_codCID, DateDiff(DD, Convert(Date,F3_Afast.Dt_Ini),"
                    + " Convert(Date,F3_Afast.Dt_Fim))+1 infoAtestado_qtdDiasAfast, Fornec.CNPJ infoMandSind_cnpjSind, "
                    + " substring(replace(convert(varchar,F3_Afast.Dt_Fim,111),'/','-'),0,11) fimAfastamento_dtTermAfast, "
                    + " FornecMedico.Empresa emitente_nmEmit, "
                    + " Case when FornecMedico.IM like'%CRM%' then '1' \n"
                    + "	     when FornecMedico.IM like'%CRO%' then '2'\n"
                    + "	     when FornecMedico.IM like'%RMS%' then '3' end emitente_ideOC, "
                    //+ " Substring(Isnull(FornecMedico.IM,' '),CharIndex(' ',Isnull(FornecMedico.IM,' '))+1,CharIndex('/',Isnull(FornecMedico.IM,' '))-(CharIndex(' ',Isnull(FornecMedico.IM,' '))+1)) emitente_nrOc, \n"
                    + " Substring(Isnull(FornecMedico.IM,' '),CharIndex(' ',Isnull(FornecMedico.IM,' '))+1, Case when (CharIndex('/',Isnull(FornecMedico.IM,' '))-(CharIndex(' ',Isnull(FornecMedico.IM,' '))+1)) <= 0 then 1 else (CharIndex('/',Isnull(FornecMedico.IM,' '))-(CharIndex(' ',Isnull(FornecMedico.IM,' '))+1)) end )  emitente_nrOc, \n"
                    + " Substring(Isnull(FornecMedico.IM,' '),CharIndex('/',Isnull(FornecMedico.IM,' '))+1,2) emitente_ufOC, "                    
                    + " (select max(sucesso) from ("
                    + " (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + " From XmleSocial z where z.Identificador = Funcion.Matr "
                    + "                and z.evento = 'S-2230' "
                    + "                and z.CodFil = ? "
                    + "                and z.Compet = ? "
                    + "                and z.Ambiente = ? "
                    + "                and z.Tipo = ? "
                    + "                and (z.Xml_Retorno like '%aguardando%' "
                    + "                        or z.Xml_Retorno = '' "
                    + "                        or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) \n"
                    + " union "
                    + " (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + " From XmleSocial z where z.Identificador = Funcion.Matr "
                    + "                and z.evento = 'S-2230' "
                    + "                and z.CodFil = ? "
                    + "                and z.Compet = ? "
                    + "                and z.Ambiente = ? "
                    + "                and z.Tipo = ? "
                    + "                and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                        or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
                    + " union "
                    + " (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + " From XmleSocial z where z.Identificador = Funcion.Matr "
                    + "                and z.evento = 'S-2230' "
                    + "                and z.CodFil = ? "
                    + "                and z.Compet = ? "
                    + "                and z.Ambiente = ? "
                    + "                and z.Tipo = ? "
                    + "                and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "                    
                    + " From F3_Afast "
                    + " Left join Funcion on Funcion.Matr = F3_Afast.Matr "
                    + " Left Join FPFerias on FPFerias.Matr = F3_Afast.Matr "
                    + "                   and FPFerias.DtInicioFer = F3_Afast.Dt_ini "
                    + "                   and FPFerias.DtFinalFer = F3_Afast.Dt_Fim "
                    + " Left join Filiais on Filiais.CodFil = Funcion.CodFil "
                    + " Left join Sindicatos  on Sindicatos.Codigo = Funcion.Sindicato "
                    + " Left join Fornec  on Fornec.Codigo = Sindicatos.CodForn "
                    + " Left join Fornec FornecMedico  on FornecMedico.Codigo = F3_Afast.CodForn "
                    + " Where Funcion.CodFil = ? "
                    + "   and (DateDiff(DD, Convert(Date,F3_Afast.Dt_Ini),Convert(Date,F3_Afast.Dt_Fim))+1) > 2";
            if (inicio) {
                if (compet.equals("2018-03")) {
                    sql += " and substring(convert(varchar,F3_Afast.Dt_Ini,121),1,7) <= ? and F3_Afast.Dt_Fim >= '2018-01-01' ";
                } else {
                    sql += " and substring(convert(varchar,F3_Afast.Dt_Ini,121),1,7) = ? ";
                }
            } else {
                sql += " and substring(convert(varchar,F3_Afast.Dt_Fim,121),1,7) = ? "
                        + " and F3_Afast.MotivoSEFIP <> 'FERIAS' \n";
            }
            sql += " Union "
                    + " Select '1' ideEvento_indRetif, '' ideEvento_nrRecibo, '1' ideEvento_procEmi, 'Satellite eSocial' ideEvento_verProc,"
                    + " '' iniAfastamento_observacao, '' ideVinculo_codCateg, '' iniAfastamento_tpAcidTransito,  "
                    + " '' infoCessao_cnpjCess, '' infoCessao_infOnus, '' infoMandSind_infOnusRemun, "
                    + " '' infoRetif_origRetif, '' infoRetif_tpProc, '' infoRetif_nrProc, '' iniAfastamento_infoMesmoMtv, "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, "
                    + " Funcion.CPF ideVinculo_cpfTrab, Funcion.PIS ideVinculo_nisTrab, Funcion.Matr ideVinculo_matricula, "
                    + " Case when Len(Funcion.CodPonto) > 0 then CONVERT(VARCHAR, Funcion.CodPonto)  else CONVERT(VARCHAR, Funcion.Matr)  end ideVinculo_matriculaCodPonto,"                    
                    + " substring(replace(convert(varchar,F2_Ficha.Data,111),'/','-'),0,11) iniAfastamento_dtIniAfast, '' iniAfastamento_dtInicio,   '' iniAfastamento_dtFim, '30' iniAfastamento_codMotAfast, "
                    + " '' infoAtestado_codCID, '' infoAtestado_qtdDiasAfast, '' infoMandSind_cnpjSind, "
                    + " substring(replace(convert(varchar,F2_Ficha.Data+Dias,111),'/','-'),0,11) fimAfastamento_dtTermAfast, "
                    + " '' emitente_nmEmit, "
                    + " '' emitente_ideOC, "
                    + " '' emitente_nrOc, "
                    + " '' emitente_ufOC, "
                    + " (select max(sucesso) from ("
                    + " (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + " From XmleSocial z where z.Identificador = Funcion.Matr "
                    + "                and z.evento = 'S-2230' "
                    + "                and z.CodFil = ? "
                    + "                and z.Compet = ? "
                    + "                and z.Ambiente = ? "
                    + "                and z.Tipo = ? "
                    + "                and (z.Xml_Retorno like '%aguardando%' "
                    + "                        or z.Xml_Retorno = '' "
                    + "                        or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + " (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + " From XmleSocial z where z.Identificador = Funcion.Matr "
                    + "                and z.evento = 'S-2230' "
                    + "                and z.CodFil = ? "
                    + "                and z.Compet = ? "
                    + "                and z.Ambiente = ? "
                    + "                and z.Tipo = ? "
                    + "                and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                        or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + " (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + " From XmleSocial z where z.Identificador = Funcion.Matr "
                    + "                and z.evento = 'S-2230' "
                    + "                and z.CodFil = ? "
                    + "                and z.Compet = ? "
                    + "                and z.Ambiente = ? "
                    + "                and z.Tipo = ? "
                    + "                and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " from F2_Ficha "
                    + " Left join Funcion on Funcion.Matr = F2_Ficha.Matr "
                    + " Left join Filiais on Filiais.CodFil = Funcion.CodFil "
                    + " Where Historico = 'SUSPENSÃO' and Funcion.CodFil = ? ";
            if (inicio) {
                sql += " and substring(convert(varchar,F2_Ficha.Data,121),1,7) = ?) z ";
            } else {
                sql += " and substring(convert(varchar,F2_Ficha.Data,121),1,7) = ?) z ";
            }
            sql += " Where (Len(iniAfastamento_codMotAfast) > 0 or Len(infoAtestado_codCID) > 0) "
                    + " ORDER BY sucesso asc, ideVinculo_matricula asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(inicio ? "INICIO" : "FIM");
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(inicio ? "INICIO" : "FIM");
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(inicio ? "INICIO" : "FIM");
            consulta.setString(codFil);
            consulta.setString(compet);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(inicio ? "INICIO" : "FIM");
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(inicio ? "INICIO" : "FIM");
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(inicio ? "INICIO" : "FIM");
            consulta.setString(codFil);
            consulta.setString(compet);

            consulta.select();
            S2230 s2230;
            while (consulta.Proximo()) {
                s2230 = new S2230();
                s2230.setSucesso(consulta.getInt("sucesso"));
                s2230.setIdeEvento_indRetif(consulta.getString("ideEvento_indRetif"));
                s2230.setIdeEvento_nrRecibo(consulta.getString("ideEvento_nrRecibo"));
                s2230.setIdeEvento_tpAmb(ambiente);
                s2230.setIdeEvento_procEmi(consulta.getString("ideEvento_procEmi"));
                s2230.setIdeEvento_verProc(consulta.getString("ideEvento_verProc"));
                s2230.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2230.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2230.setIdeVinculo_cpfTrab(consulta.getString("ideVinculo_cpfTrab"));
                s2230.setIdeVinculo_nisTrab(consulta.getString("ideVinculo_nisTrab"));
                
                if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) || (persistencia.getEmpresa().equals("SATSEFIX")) ||
                    (consulta.getString("ideVinculo_matriculaCodPonto") != "")) {
                    s2230.setCodPonto(consulta.getString("ideVinculo_matriculaCodPonto"));
                } else if (persistencia.getEmpresa().contains("SATAGIL")) {
                    s2230.setCodPonto(consulta.getString("ideVinculo_matriculaCodPonto"));
                } else {
                    s2230.setCodPonto(consulta.getString("ideVinculo_matricula"));
                }
                s2230.setIdeVinculo_matricula(consulta.getString("ideVinculo_matricula"));
                s2230.setIdeVinculo_codCateg(consulta.getString("ideVinculo_codCateg"));
                s2230.setIniAfastamento_dtIniAfast(consulta.getString("iniAfastamento_dtIniAfast"));
                s2230.setIniAfastamento_codMotAfast(consulta.getString("iniAfastamento_codMotAfast"));
                s2230.setIniAfastamento_infoMesmoMtv(consulta.getString("iniAfastamento_infoMesmoMtv"));
                s2230.setIniAfastamento_tpAcidTransito(consulta.getString("iniAfastamento_tpAcidTransito"));
                s2230.setIniAfastamento_observacao(consulta.getString("iniAfastamento_observacao"));
                
                // Trata Ferias Carlos 12/07/2022
                if (consulta.getString("iniAfastamento_codMotAfast").equals("15")){
                    s2230.setIniAfastamento_dtInicio(consulta.getString("iniAfastamento_dtInicio"));
                    s2230.setIniAfastamento_dtFim(consulta.getString("iniAfastamento_dtFim"));
                }else{
                    s2230.setIniAfastamento_dtInicio("");
                    s2230.setIniAfastamento_dtFim("");
                }
                
                s2230.setInfoAtestado_codCID(consulta.getString("infoAtestado_codCID"));
                s2230.setInfoAtestado_qtdDiasAfast(consulta.getString("infoAtestado_qtdDiasAfast"));
                s2230.setEmitente_nmEmit(consulta.getString("emitente_nmEmit"));
                s2230.setEmitente_ideOC(consulta.getString("emitente_ideOC"));
                s2230.setEmitente_nrOc(consulta.getString("emitente_nrOc"));
                s2230.setEmitente_ufOC(consulta.getString("emitente_ufOC"));
                s2230.setInfoCessao_cnpjCess(consulta.getString("infoCessao_cnpjCess"));
                s2230.setInfoCessao_infOnus(consulta.getString("infoCessao_infOnus"));
                s2230.setInfoMandSind_cnpjSind(consulta.getString("infoMandSind_cnpjSind"));
                s2230.setInfoMandSind_infOnusRemun(consulta.getString("infoMandSind_infOnusRemun"));
                s2230.setInfoRetif_origRetif(consulta.getString("infoRetif_origRetif"));
                s2230.setInfoRetif_tpProc(consulta.getString("infoRetif_tpProc"));
                s2230.setInfoRetif_nrProc(consulta.getString("infoRetif_nrProc"));
                s2230.setFimAfastamento_dtTermAfast(consulta.getString("fimAfastamento_dtTermAfast"));
                retorno.add(s2230);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2230Dao.get - " + e.getMessage());
        }
    }
}

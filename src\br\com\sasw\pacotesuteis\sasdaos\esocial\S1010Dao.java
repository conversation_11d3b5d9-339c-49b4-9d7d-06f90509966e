/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1010;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1010Dao {

    public List<S1010> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql1 = "Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc "
                    + " From Filiais "
                    + " Where Filiais.CodFil = ? ";
            Consulta consulta = new Consulta(sql1, persistencia);
            consulta.setString(codFil);
            consulta.select();
            String ideEmpregador_tpInsc = "", ideEmpregador_nrInsc = "";
            while (consulta.Proximo()) {
                ideEmpregador_tpInsc = consulta.getString("ideEmpregador_tpInsc");
                ideEmpregador_nrInsc = consulta.getString("ideEmpregador_nrInsc");
            }
            String sql
                    = " Select Verbas.Verba ideRubrica_CodRubr, Verbas.Verba ideRubrica_ideTabRubr,\n "
                    + " Verbas.Descricao dadosRubrica_dscRubr, \n "
                    + " Case when Verbas.Formula = '0001' then 1000  \n " //Salario 
                    + "         when Verbas.Formula = '0150' then 1002  \n " //DSR 
                    + "         when Verbas.Formula = '0101' then 1003  \n " //HORA EXTRA 50% 
                    + "         when Verbas.Formula = '0102' then 1003  \n " //HORA EXTRA 60% 
                    + "         when Verbas.Formula = '0103' then 1003  \n " //HORA EXTRA 70% 
                    + "         when Verbas.Formula = '0104' then 1003  \n " //HORA EXTRA 25% 
                    + "         when Verbas.Formula = '0109' then 1003  \n " //HORA EXTRA 50% NOT 
                    + "         when Verbas.Formula = '0111' then 1003  \n " //HORA EXTRA 100% 
                    + "         when Verbas.Formula = '0112' then 1003  \n " //HORA EXTRA 100% NOT 
                    + "         when Verbas.Formula = '0301' then 1003  \n " //HORA EXTRA 65% 
                    + "         when Verbas.Formula = '0302' then 1003  \n " //HORA EXTRA 75% 
                    + "         when Verbas.Formula = '0106' then 1006  \n " //INTRAJ 50% 
                    + "         when Verbas.Formula = '0107' then 1006  \n " //INTRAJ 60% 
                    + "         when Verbas.Formula = '0108' then 1006  \n " //INTRAJ 70% 
                    + "         when Verbas.Formula = '0005' then 1020  \n " //FERIAS 
                    + "         when Verbas.Formula = '0194' then 1023  \n " //ABONO 
                    + "         when Verbas.Formula = '0195' then 1023  \n " //1/3 ABONO 
                    + "         when Verbas.Formula = '0181' then 1024  \n " //FERIAS EM DOBRO 
                    + "         when Verbas.Formula = '1189' then 1024  \n " //1/3 FERIAS EM DOBRO 
                    + "         when Verbas.Formula = '0018' then 1050  \n " //FALTA JUSTIFICADA 
                    + "         when Verbas.Formula = '0142' then 1202  \n " //INSALUBRIDADE 
                    + "         when Verbas.Formula = '0146' then 1202  \n " //INSALUBRIDADE S/ SM 
                    + "         when Verbas.Formula = '0141' then 1203  \n " //PERICULOSIDADE 
                    + "         when Verbas.Formula = '0121' then 1205  \n " //AD NOT 20% 
                    + "         when Verbas.Formula = '0122' then 1205  \n " //HE NOT REDDUZIDA 
                    + "         when Verbas.Formula = '0123' then 1205  \n " //HE NOT PRORROGADA
                    + "         when Verbas.Formula = '0124' then 1205  \n " //AD NOT 12% 
                    + "         when Verbas.Formula = '0125' then 1205  \n " //AD NOT 14,2% 
                    + "         when Verbas.Formula = '0126' then 1205  \n " //AD NOT 14,2% C/PERICULOSIDADE 
                    + "         when Verbas.Formula = '0127' then 1205  \n " //AD NOT 40% 
                    + "         when Verbas.Formula = '0128' then 1205  \n " //AD NOT RED 50%
                    + "         when Verbas.Formula = '0129' then 1205  \n " //AD NOT RED 50% + 20%
                    + "         when Verbas.Formula = '0298' then 1205  \n " //AD NOT 25% 
                    + "         when Verbas.Formula = '0299' then 1205  \n " //AD NOT 30% 
                    + "         when Verbas.Formula = '0300' then 1205  \n " //AD NOT 35% 
                    + "         when Verbas.Formula = '0303' then 1205  \n " //AD NOT 22.5% 
                    + "         when Verbas.Formula = '0011' then 1350  \n " //BOLSA ESTAGIO
                    + "         when Verbas.Formula = '0004' then 1409  \n " //SALARIO FAMILIA 
                    + "         when Verbas.Formula = '9997' then 2930  \n " //SALDO DEVEDOR EMPRESTIMO 
                    + "         when Verbas.Formula = '9998' then 2930  \n " //SALDO DEVEDOR DESCONTO 
                    + "         when Verbas.Formula = '0009' then 3508  \n " //PRO-LABORE
                    + "         when Verbas.Formula = '0006' then 4050  \n " //SALARIO MATERNIDADE 
                    + "         when Verbas.Formula = '0017' then 4050  \n " //SALARIO MATERNIDADE MEDIAS
                    + "         when Verbas.Formula = '0207' then 4051  \n " //13 SALARIO MATERNIDADE 
                    + "         when Verbas.Formula = '0199' then 5001  \n " //13 SALARIO  
                    + "         when Verbas.Formula = '0200' then 5001  \n " //13 SALARIO MEDIA
                    + "         when Verbas.Formula = '0161' then 5501  \n " //ADIANTAMENTO SALARIO 
                    + "         when Verbas.Formula = '0198' then 5504  \n " //13 SALARIO ADIANTAMENTO 
                    + "         when Verbas.Formula = '0167' then 5504  \n " //13 SALARIO S/ARV
                    + "         when Verbas.Formula = '0166' then 5504  \n " //13 SALARIO S/TEMPO SERV
                    + "         when Verbas.Formula = '0214' then 6001  \n " //13 SALARIO AV IND 
                    + "         when Verbas.Formula = '0192' then 6002  \n " //13 SALARIO PROP 
                    + "         when Verbas.Formula = '0193' then 6002  \n " //13 SALARIO PROP MEDIA
                    + "         when Verbas.Formula = '0191' then 6003  \n " //AVISO PREVIO IND 
                    + "         when Verbas.Formula = '0208' then 6003  \n " //AVISO PREVIO IND MEDIA
                    + "         when Verbas.Formula = '0185' then 6006  \n " //FERIAS PROPORCIONAIS 
                    + "         when Verbas.Formula = '0186' then 6006  \n " //FERIAS PROPORCIONAIS MEDIA
                    + "         when Verbas.Formula = '0183' then 6007  \n " //FERIAS VENCIDAS 
                    + "         when Verbas.Formula = '0184' then 6007  \n " //FERIAS VENCIDAS MEDIA
                    + "         when Verbas.Formula = '0267' then 6102  \n " //MULTA 7238 
                    + "         when Verbas.Formula = '0266' then 6104  \n " //MULTA ART 479 
                    + "         when Verbas.Formula = '0265' then 6129  \n " //MULTA ART 478 
                    + "         when Verbas.Formula = '0264' then 6901  \n " //DESCONTO AV PREVIO 
                    + "         when Verbas.Formula = '0251' then 9200  \n " //DESCONTO ADIANTAMENTO SALARIO
                    + "         when Verbas.Formula = '0002' then 9201  \n " //INSS 
                    + "         when Verbas.Formula = '0012' then 9201  \n " //INSS 13 SALARIO 
                    + "         when Verbas.Formula = '0003' then 9203  \n " //IRRF 
                    + "         when Verbas.Formula = '0013' then 9203  \n " //IRRF 13 SALARIO 
                    + "         \n " //when Verbas.Formula = '0023' then 9205  --IRRF FERIAS 
                    + "         when Verbas.Formula = '0201' then 9209  \n " //FALTAS 
                    + "         when Verbas.Formula = '0202' then 9210  \n " //DSR FALTAS 
                    + "         when Verbas.Formula = '0244' then 9210  \n " //DESCONTO DSR S/ARV
                    + "         when Verbas.Formula = '0221' then 9213  \n " //PENSAO ALIMENTICIA 
                    + "         when Verbas.Formula = '0222' then 9213  \n " //PENSAO ALIMENTICIA 
                    + "         when Verbas.Formula = '0223' then 9213  \n " //PENSAO ALIMENTICIA 
                    + "         when Verbas.Formula = '0225' then 9213  \n " //PENSAO ALIMENTICIA 
                    + "         when Verbas.Formula = '0226' then 9213  \n " //PENSAO ALIMENTICIA 
                    + "         when Verbas.Formula = '0271' then 9214  \n " //ADIANTAMENTO 13 SALARIO 
                    + "         when Verbas.Formula = '0272' then 9214  \n " //ADIANTAMENTO 13 SALARIO 2 PARC 
                    + "         when Verbas.Formula = '0211' then 9216  \n " //VT 
                    + "         when Verbas.Formula = '0216' then 9216  \n " //VT 3% 
                    + "         when Verbas.PlanoSaude = 'S' then 9219  \n " //Plano Saude 
                    //     + "         when Verbas.Formula = '0212' then 9220  \n " //VA 
                    + "         \n " //when Verbas.Formula = '0281' then 9230  --CONTRIBUICAO SINDICAL 
                    + "         when Verbas.Descricao like '%MENSALIDADE%SINDICAL%' then 9231\n "
                    + "         when Verbas.Descricao like '%CONTRIBUICAO%ASSISTENCIAL%' then 9232\n "
                    + "         when Verbas.NatESocial <> '' then Verbas.NatESocial \n " //Padronizado ESocial                   
                    + "         when Verbas.Tipo = 'V' then 1099  \n " //OUTRAS VERBAS SALARIAIS 
                    + "         when Verbas.Tipo = 'D' then 9290  \n " //OUTROS DESCONTOS 
                    + " end dadosRubrica_natRubr,  \n "
                    + " Case when Verbas.Tipo = 'V' then 1 else 2 end dadosRubrica_tpRubr, \n "
                    + " Case when Verbas.Formula = '0002' then '31' "
                    + "      when Verbas.Formula = '0012' then '32' "
                    + "      when Verbas.Formula = '0006' then '21' "
                    + "      when Verbas.INSS = 'S'  \n "
                    + "       and Verbas.Descricao not like '%13%'  \n "
                    + "       and Verbas.Formula <> '0006'  \n " //SALARIO MATERNIDADE 
                    + "       and Verbas.Formula <> '0004'  \n " //SALARIO FAMILIA  
                    + "      Then '11'  \n " //Incidencia Mensal 
                    + "      when Verbas.INSS = 'S'  \n "
                    + "           and Verbas.Descricao like '%13%'  \n "
                    + "          and Verbas.Formula <> '0006'  \n " //SALARIO MATERNIDADE 
                    + "          and Verbas.Formula <> '0004'  \n " //SALARIO FAMILIA  
                    + "          Then '12'  \n " //Incidencia 13 
                    + "      when Verbas.INSS = 'S'       \n "
                    + "          and Verbas.Formula = '0207'  \n " //13 SALARIO MATERNIDADE         
                    + "          Then '12'  \n " //13 Salario Maternidade 
                    //+ "     when Verbas.INSS = 'S'          \n "
                    //+ "          and Verbas.Formula = '0004'  \n " //SALARIO FAMILIA  
                    + "     when Verbas.Formula = '0004'          \n " //SALARIO FAMILIA  
                    + "          Then '51'  \n " //Incidencia 13 
                    + "     when Verbas.INSS = 'N' then '00' end dadosRubrica_codIncCP,  \n " //Não é base de calculo 
                    + " Case when Verbas.Formula in ('0183','0184','0185','0186','0187','0188','0189','1189','0190','0194','0195')"
                    + "           then '00' "
                    + "      when Verbas.Formula in ('0003','0033')\n "
                    + "        then '31'\n "
                    + "      when Verbas.Formula in ('0181','0182')\n "
                    + "        then '93'\n "                    
                    + "      when Verbas.Formula in('0221','0222','0223','0225','0226') \n "
                    + "          then '51'  \n " //Dedução Base Pensao Alimenticia                     
                    + "        when Verbas.Formula in ('0013')\n "
                    + "        then '32'\n "
                    + "        when Verbas.Formula in ('0023')\n "
                    + "        then '33'\n "
                    + "        when Verbas.IR = 'S'  \n "
                    + "       and Verbas.Descricao not like '%13%'  \n "
                    + "          and Verbas.Descricao not like '%Ferias%'  \n "
                    + "          then '11'  \n " //Remuneração mensal 
                    + "      when Verbas.IR = 'S'  \n "
                    + "        and Verbas.Descricao like '%13%'  \n "
                    + "           and Verbas.Formula not in('0002','0012') \n "
                    + "          then '12'  \n " //13 Salario 
                    + "      when Verbas.IR = 'S'  \n "
                    + "        and Verbas.Descricao like '%Ferias%'  \n "
                    + "          then '11'  \n " //Ferias 
                    + "      when Verbas.Formula in('0002') \n "
                    + "          then '41'  \n " //Dedução Base INSS 
                    + "      when Verbas.Formula in('0012') \n "
                    + "          then '42'  \n " //Dedução Base INSS                     
                    + "      when Verbas.Formula in('0187') \n "
                    + "          then '92'  \n " //Dedução Base INSS                                         
                    + "      when Verbas.IR = 'N' \n "
                    + "          then '9' end dadosRubrica_codIncIRRF, \n " //Rendimento Não Tributavel 
                    + " Case when Verbas.FGTS = 'S' \n "
                    + "           and Verbas.Descricao like '%13%' \n "
                    + "           then '12'  \n " //13 Salario 
                    + "      when Verbas.FGTS = 'S' \n "
                    + "           and Verbas.Formula in ('0191') \n "
                    + "           then '21'  \n " //Aviso Previo 
                    + "      when Verbas.FGTS = 'S' \n "
                    + "          and Verbas.Formula not in ('0191') \n "
                    + "           and Verbas.Descricao not like '%13%' \n "
                    + "          then '11'  \n " //Mensal 
                    + "      when Verbas.FGTS = 'N' then '00' end dadosRubrica_codIncFGTS,  \n " //Nao é base de calculo 
                    + "     '00' dadosRubrica_codIncSIND,  \n " //Não é base de calculo 
                    + "     (select max(sucesso) from  (  "
                    + "         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "             From XmleSocial z  "
                    + "             where z.Identificador = Verbas.Verba   "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil =  ? \n"
                    + "                 and z.Compet =  ? \n"
                    + "                 and z.Ambiente =  ? \n"
                    + "                 and (z.Xml_Retorno like '%aguardando%'  "
                    + "                         or z.Xml_Retorno = '' "
                    + "                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + "     union  "
                    + "         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "             From XmleSocial z   "
                    + "             where z.Identificador = Verbas.Verba  "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil =  ? \n"
                    + "                 and z.Compet =  ? \n"
                    + "                 and z.Ambiente =  ? \n"
                    + "                 and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + "     union  "
                    + "         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "             From XmleSocial z   "
                    + "             where z.Identificador = Verbas.Verba  "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil =  ? \n"
                    + "                 and z.Compet =  ? \n"
                    + "                 and z.Ambiente =  ? \n"
                    + "                 and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  "
                    + " from Verbas "
                    + " Where Len(Formula) > 0 "
                    + "   and Tipo In ('V', 'D') "
                    //+ "  and Verbas.Verba in (Select  Verba from Verbas where Descricao like '%Ferias%' and verba not in (7015,7021,7031,7032,7033))"
                    + " union "
                    + " Select  "
                    + " '9'+Verbas.Verba ideRubrica_CodRubr, '9'+Verbas.Verba ideRubrica_ideTabRubr, "
                    + " Verbas.Descricao dadosRubrica_dscRubr,  "
                    + " Case when Verbas.Formula = '0002' then 9201  " //INSS  
                    + " end dadosRubrica_natRubr,   "
                    + " Case when Verbas.Tipo = 'V' then 1 else 2 end dadosRubrica_tpRubr,  "
                    + " Case when Verbas.INSS = 'S'   "
                    + "       and Verbas.Descricao not like '%13%'   "
                    + " 	  and Verbas.Formula <> '0006'  " //SALARIO MATERNIDADE  
                    + " 	  and Verbas.Formula <> '0004'  " //SALARIO FAMILIA   
                    + " 	  Then '31'  " //Incidencia Mensal       
                    + "     when Verbas.INSS = 'N' then '00' end dadosRubrica_codIncCP,  " //Não é base de calculo  
                    + " Case when Verbas.IR = 'S'   "
                    + "       and Verbas.Descricao not like '%13%'   "
                    + " 	  and Verbas.Descricao not like '%Ferias%'   "
                    + " 	  then '33'  " //Remuneração mensal  
                    + "      when Verbas.IR = 'N'  "
                    + " 	  then '9' end dadosRubrica_codIncIRRF, " //Rendimento Não Tributavel  
                    + " Case when Verbas.FGTS = 'S'  "
                    + "           and Verbas.Descricao like '%13%'  "
                    + "           then '12'  " //13 Salario  
                    + "      when Verbas.FGTS = 'S'  "
                    + "           and Verbas.Formula in ('0191')  "
                    + "           then '21'  " //Aviso Previo  
                    + "      when Verbas.FGTS = 'S'  "
                    + " 	  and Verbas.Formula not in ('0191')  "
                    + "           and Verbas.Descricao not like '%13%'  "
                    + " 	  then '11'  " //Mensal  
                    + "      when Verbas.FGTS = 'N' then '00' end dadosRubrica_codIncFGTS,  " //Nao é base de calculo  
                    + "     '00' dadosRubrica_codIncSIND,  " //Não é base de calculo  
                    + "     (select max(sucesso) from  (  "
                    + "         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "             From XmleSocial z  "
                    + "             where z.Identificador = '9'+Verbas.Verba   "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil =  ? \n"
                    + "                 and z.Compet =  ? \n"
                    + "                 and z.Ambiente =  ? \n"
                    + "                 and (z.Xml_Retorno like '%aguardando%'  "
                    + "                         or z.Xml_Retorno = '' "
                    + "                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + "     union  "
                    + "         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "             From XmleSocial z   "
                    + "             where z.Identificador = '9'+Verbas.Verba  "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil =  ? \n"
                    + "                 and z.Compet =  ? \n"
                    + "                 and z.Ambiente =  ? \n"
                    + "                 and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + "     union  "
                    + "         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "             From XmleSocial z   "
                    + "             where z.Identificador = '9'+Verbas.Verba  "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil =  ? \n"
                    + "                 and z.Compet =  ? \n"
                    + "                 and z.Ambiente =  ? \n"
                    + "                 and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  "
                    + " from Verbas  "
                    + " Where Verbas.Formula = '0002' "
                    + " ORDER BY sucesso asc, Verbas.Verba asc ";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.select();
            List<S1010> retorno = new ArrayList<>();
            S1010 s1010;
            while (consulta.Proximo()) {
                s1010 = new S1010();
                s1010.setSucesso(consulta.getInt("sucesso"));
                s1010.setIdeEvento_procEmi("1");
                s1010.setIdeEvento_verProc("Satellite eSocial");
                s1010.setIdeEmpregador_tpInsc("1");
                s1010.setIdeEmpregador_nrInsc(ideEmpregador_nrInsc);
                s1010.setIdeEmpregador_tpInsc(ideEmpregador_tpInsc);
                s1010.setIdeRubrica_codRubr(consulta.getString("ideRubrica_codRubr"));
                s1010.setIdeRubrica_ideTabRubr(consulta.getString("ideRubrica_ideTabRubr"));
                s1010.setDadosRubrica_dscRubr(consulta.getString("dadosRubrica_dscRubr"));
                s1010.setDadosRubrica_natRubr(consulta.getString("dadosRubrica_natRubr"));
                s1010.setDadosRubrica_tpRubr(consulta.getString("dadosRubrica_tpRubr"));
                s1010.setDadosRubrica_codIncCP(consulta.getString("dadosRubrica_codIncCP"));
                if (consulta.getString("dadosRubrica_codIncIRRF").equals("00")) {
                    s1010.setDadosRubrica_codIncIRRF("12");
                } else {
                    s1010.setDadosRubrica_codIncIRRF(consulta.getString("dadosRubrica_codIncIRRF"));
                }
                s1010.setDadosRubrica_codIncFGTS(consulta.getString("dadosRubrica_codIncFGTS"));
                s1010.setDadosRubrica_codIncSIND(consulta.getString("dadosRubrica_codIncSIND"));
                retorno.add(s1010);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1010Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Verbas.Verba ideRubrica_CodRubr, Verbas.Verba ideRubrica_ideTabRubr,"
                    + " Verbas.Descricao dadosRubrica_dscRubr, "
                    + " Case when Verbas.Formula = '0001' then 1000 " //Salario 
                    + "         when Verbas.Formula = '0150' then 1002 " //DSR 
                    + " 	when Verbas.Formula = '0101' then 1003 " //HORA EXTRA 50% 
                    + "         when Verbas.Formula = '0102' then 1003 " //HORA EXTRA 60% 
                    + " 	when Verbas.Formula = '0103' then 1003 " //HORA EXTRA 70% 
                    + " 	when Verbas.Formula = '0104' then 1003 " //HORA EXTRA 25% 
                    + " 	when Verbas.Formula = '0109' then 1003 " //HORA EXTRA 50% NOT 
                    + " 	when Verbas.Formula = '0111' then 1003 " //HORA EXTRA 100% 
                    + " 	when Verbas.Formula = '0112' then 1003 " //HORA EXTRA 100% NOT 
                    + " 	when Verbas.Formula = '0301' then 1003 " //HORA EXTRA 65% 
                    + " 	when Verbas.Formula = '0302' then 1003 " //HORA EXTRA 75% 
                    + " 	when Verbas.Formula = '0106' then 1006 " //INTRAJ 50% 
                    + " 	when Verbas.Formula = '0107' then 1006 " //INTRAJ 60% 
                    + " 	when Verbas.Formula = '0108' then 1006 " //INTRAJ 70% 
                    + " 	when Verbas.Formula = '0005' then 1020 " //FERIAS 
                    + " 	when Verbas.Formula = '0194' then 1023 " //ABONO 
                    + " 	when Verbas.Formula = '0195' then 1023 " //1/3 ABONO 
                    + " 	when Verbas.Formula = '0181' then 1024 " //FERIAS EM DOBRO 
                    + " 	when Verbas.Formula = '1189' then 1024 " //1/3 FERIAS EM DOBRO 
                    + " 	when Verbas.Formula = '0018' then 1050 " //FALTA JUSTIFICADA 
                    + " 	when Verbas.Formula = '0142' then 1202 " //INSALUBRIDADE 
                    + " 	when Verbas.Formula = '0146' then 1202 " //INSALUBRIDADE S/ SM 
                    + " 	when Verbas.Formula = '0141' then 1203 " //PERICULOSIDADE 
                    + " 	when Verbas.Formula = '0121' then 1205 " //AD NOT 20% 
                    + " 	when Verbas.Formula = '0124' then 1205 " //AD NOT 12% 
                    + "         when Verbas.Formula = '0125' then 1205 " //AD NOT 14,2% 
                    + " 	when Verbas.Formula = '0126' then 1205 " //AD NOT 14,2% C/PERICULOSIDADE 
                    + " 	when Verbas.Formula = '0127' then 1205 " //AD NOT 40% 
                    + " 	when Verbas.Formula = '0298' then 1205 " //AD NOT 25% 
                    + " 	when Verbas.Formula = '0299' then 1205 " //AD NOT 30% 
                    + " 	when Verbas.Formula = '0300' then 1205 " //AD NOT 35% 
                    + " 	when Verbas.Formula = '0303' then 1205 " //AD NOT 22.5% 
                    + " 	when Verbas.Formula = '0004' then 1409 " //SALARIO FAMILIA 
                    + " 	when Verbas.Formula = '9997' then 2930 " //SALDO DEVEDOR EMPRESTIMO 
                    + "         when Verbas.Formula = '9998' then 2930 " //SALDO DEVEDOR DESCONTO 
                    + " 	when Verbas.Formula = '0009' then 3505 " //SALDO DEVEDOR DESCONTO 
                    + " 	when Verbas.Formula = '0006' then 4050 " //SALARIO MATERNIDADE 
                    + " 	when Verbas.Formula = '0207' then 4051 " //13 SALARIO MATERNIDADE 
                    + " 	when Verbas.Formula = '0199' then 5001 " //13 SALARIO  
                    + "         when Verbas.Formula = '0161' then 5501 " //ADIANTAMENTO SALARIO 
                    + " 	when Verbas.Formula = '0198' then 5504 " //13 SALARIO ADIANTAMENTO 
                    + " 	when Verbas.Formula = '0214' then 6001 " //13 SALARIO AV IND 
                    + " 	when Verbas.Formula = '0192' then 6002 " //13 SALARIO PROP 
                    + " 	when Verbas.Formula = '0191' then 6003 " //AVISO PREVIO IND 
                    + " 	when Verbas.Formula = '0185' then 6006 " //FERIAS PROPORCIONAIS 
                    + " 	when Verbas.Formula = '0183' then 6007 " //FERIAS VENCIDAS 
                    + " 	when Verbas.Formula = '0267' then 6102 " //MULTA 7238 
                    + " 	when Verbas.Formula = '0266' then 6104 " //MULTA ART 479 
                    + " 	when Verbas.Formula = '0265' then 6129 " //MULTA ART 478 
                    + " 	when Verbas.Formula = '0264' then 6901 " //DESCONTO AV PREVIO 
                    + " 	when Verbas.Formula = '0002' then 9201 " //INSS 
                    + " 	when Verbas.Formula = '0012' then 9201 " //INSS 13 SALARIO 
                    + " 	when Verbas.Formula = '0003' then 9203 " //IRRF 
                    + " 	when Verbas.Formula = '0013' then 9203 " //IRRF 13 SALARIO 
                    + " 	when Verbas.Formula = '0023' then 9205 " //IRRF FERIAS 
                    + " 	when Verbas.Formula = '0201' then 9209 " //FALTAS 
                    + " 	when Verbas.Formula = '0203' then 9210 " //DSR FALTAS 
                    + " 	when Verbas.Formula = '0221' then 9213 " //PENSAO ALIMENTICIA 
                    + " 	when Verbas.Formula = '0222' then 9213 " //PENSAO ALIMENTICIA 
                    + " 	when Verbas.Formula = '0223' then 9213 " //PENSAO ALIMENTICIA 
                    + " 	when Verbas.Formula = '0225' then 9213 " //PENSAO ALIMENTICIA 
                    + " 	when Verbas.Formula = '0271' then 9214 " //ADIANTAMENTO 13 SALARIO 
                    + " 	when Verbas.Formula = '0272' then 9214 " //ADIANTAMENTO 13 SALARIO 2 PARC 
                    + " 	when Verbas.Formula = '0211' then 9216 " //VT 
                    + " 	when Verbas.Formula = '0216' then 9216 " //VT 3% 
                    + " 	when Verbas.PlanoSaude = 'S' then 9219 " //Plano Saude 
                    //+ " 	when Verbas.Formula = '0212' then 9220 " //VA 
                    + " 	when Verbas.Formula = '0281' then 9230 " //CONTRIBUICAO SINDICAL 
                    + " 	when Verbas.NatEsocial <> '' then Verbas.NatEsocial " //                    
                    + " 	when Verbas.Tipo = 'V' then 1099 " //OUTRAS VERBAS SALARIAIS 
                    + " 	when Verbas.Tipo = 'D' then 9290 " //OUTROS DESCONTOS 
                    + " 	end dadosRubrica_natRubr,  "
                    + " Case when Verbas.Tipo = 'V' then 1 else 2 end dadosRubrica_tpRubr, "
                    + " Case when Verbas.Formula = '0002' then '31' "
                    + "      when Verbas.Formula = '0012' then '32'  "
                    + "      when Verbas.Formula = '0006' then '21' "
                    + "      when Verbas.Formula in('0221','0222','0223','0225') "
                    + " 	  then '51' " //Dedução Base Pensao Alimenticia 
                    + "       when Verbas.INSS = 'S'  "
                    + "           and Verbas.Descricao not like '%13%'  "
                    + " 	  and Verbas.Formula <> '0006' " //SALARIO MATERNIDADE 
                    + " 	  and Verbas.Formula <> '0004' " //SALARIO FAMILIA  
                    + " 	  Then '11' " //Incidencia Mensal 
                    + "      when Verbas.INSS = 'S'  "
                    + "           and Verbas.Descricao like '%13%'  "
                    + " 	  and Verbas.Formula <> '0006' " //SALARIO MATERNIDADE 
                    + " 	  and Verbas.Formula <> '0004' " //SALARIO FAMILIA  
                    + " 	  Then '12' " //Incidencia 13 
                    + "      when Verbas.INSS = 'S'       "
                    + " 	  and Verbas.Formula = '0207' " //13 SALARIO MATERNIDADE	   
                    + " 	  Then '22' " //13 Salario Maternidade 
                    //+ "     when Verbas.INSS = 'S'       	   "
                    //+ " 	  and Verbas.Formula = '0004' " //SALARIO FAMILIA  
                    + "     when Verbas.Formula = '0004'       	   "
                    + " 	  Then '51' " //Incidencia 13 
                    + "     when Verbas.INSS = 'N' then '00' end dadosRubrica_codIncCP, " //Não é base de calculo 
                    + " Case when Verbas.Formula in ('0181','0182','0183','0184','0185','0186','0187','0188','0189','1189','0190','0194','0195')"
                    + "           then '00' "
                    + "      when Verbas.IR = 'S'  "
                    + "           and Verbas.Descricao not like '%13%'  "
                    + " 	  and Verbas.Descricao not like '%Ferias%'  "
                    + " 	  then '11' " //Remuneração mensal 
                    + "      when Verbas.IR = 'S'  "
                    + "           and Verbas.Descricao like '%13%'  "
                    + " 	  then '12' " //13 Salario 
                    + "      when Verbas.IR = 'S'  "
                    + "           and Verbas.Descricao like '%Ferias%'  "
                    //+ " 	  then '13' " //Ferias 
                    + " 	  then '11' " //Incidencia para Mensal, alteração 30/10/2018.
                    + "      when Verbas.Formula in('0002','0012') "
                    + " 	  then '41' " //Dedução Base INSS 
                    + "      when Verbas.IR = 'N' "
                    + " 	  then '9' end dadosRubrica_codIncIRRF," //Rendimento Não Tributavel 
                    + " Case when Verbas.FGTS = 'S' "
                    + "           and Verbas.Descricao like '%13%' "
                    + "           then '12' " //13 Salario 
                    + "      when Verbas.FGTS = 'S' "
                    + "           and Verbas.Formula in ('0191') "
                    + "           then '21' " //Aviso Previo 
                    + "      when Verbas.FGTS = 'S' "
                    + " 	  and Verbas.Formula not in ('0191') "
                    + "           and Verbas.Descricao not like '%13%' "
                    + " 	  then '11' " //Mensal 
                    + "      when Verbas.FGTS = 'N' then '00' end dadosRubrica_codIncFGTS, " //Nao é base de calculo 
                    + "     '00' dadosRubrica_codIncSIND, " //Não é base de calculo 
                    + "     (select max(sucesso) from  (  "
                    + "         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "             From XmleSocial z  "
                    + "             where z.Identificador = Verbas.Verba   "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil = " + codFil
                    + "                 and z.Compet = " + compet
                    + "                 and z.Ambiente = " + ambiente
                    + "                 and (z.Xml_Retorno like '%aguardando%'  "
                    + "                         or z.Xml_Retorno = '' "
                    + "                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + "     union  "
                    + "         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "             From XmleSocial z   "
                    + "             where z.Identificador = Verbas.Verba  "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil = " + codFil
                    + "                 and z.Compet = " + compet
                    + "                 and z.Ambiente = " + ambiente
                    + "                 and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + "     union  "
                    + "         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "             From XmleSocial z   "
                    + "             where z.Identificador = Verbas.Verba  "
                    + "                 and z.evento = 'S-1010' "
                    + "                 and z.CodFil = " + codFil
                    + "                 and z.Compet = " + compet
                    + "                 and z.Ambiente = " + ambiente
                    + "                 and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso  "
                    + " from Verbas "
                    + " Where Len(Formula) > 0 "
                    + "   and Tipo In ('V', 'D') "
                    + " ORDER BY sucesso asc, Verbas.Verba asc ");
        }
    }
}

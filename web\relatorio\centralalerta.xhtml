<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} - #{localemsgs.CentralAlerta}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/pessoas.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/common-layout.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th:nth-child(1),
                    .DataGrid thead tr td:nth-child(1){
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid thead tr td:nth-child(2){
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }

                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid thead tr td:nth-child(3){
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid thead tr td:nth-child(4),
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid thead tr td:nth-child(6){
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formPesquisaRapida .ui-radiobutton {
                    background: transparent !important;
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                .semPaddingLateral {
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }

                #divTopoTela2 {
                    display: flex;
                }

                .tituloPagina{
                    font-size:13pt !important;
                    font-weight:bold !important;
                    line-height: 25px !important;
                    display:block;
                    font-family:'Open Sans', sans-serif !important;
                    color:#022B4A !important;
                    margin-left:10px !important;
                }

                .tituloDataHora{
                    font-size:8pt !important;
                    font-weight:600 !important;
                    line-height: 10px !important;
                    display:block;
                    font-family:'Open Sans', sans-serif !important;
                    color:#404040 !important;
                    margin-left:10px !important;
                }

                .equal {
                    display: flex;
                    display: -webkit-flex;
                    flex-wrap: wrap;
                    align-content: flex-start;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header class="fixed-header">
                    <h:form id="cabecalho">
                        <div class="cabecalho equal" style="padding-top: 4px;">
                            <div id="divTopoTela2"
                                 class="col-xs-6 col-sm-3">
                                <img src="../assets/img/icone_Dashboard_cliente.png" height="40"/>

                                <div>
                                    <label class="tituloPagina">#{localemsgs.CentralAlerta}</label>
                                    <label class="tituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{centralAlerta.dataInicio}" converter="conversorData" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{centralAlerta.dataFim}" converter="conversorData"/></span>
                                    </label>
                                </div>
                            </div>

                            <div class="col-xs-6 col-sm-1 col-sm-push-8"
                                 style="display: flex; justify-content: flex-end; padding: 2px;">
                                <p:commandLink title="Home"
                                               ajax="false"
                                               action="/menu.xhtml?faces-redirect=true">
                                    <p:graphicImage url="../assets/img/icone_home_branco.png" height="40"/>
                                </p:commandLink>

                                <p:commandLink title="#{localemsgs.Voltar}"
                                               ajax="false"
                                               onclick="window.history.back();" action="#">
                                    <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                </p:commandLink>
                            </div>

                            <div id="divDadosFilial"
                                 class="col-xs-12 col-sm-4"
                                 style="text-align: center !important;">
                                <label class="FilialNome">#{centralAlerta.filialTela.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                <label class="FilialEndereco">#{centralAlerta.filialTela.endereco}</label>
                                <label class="FilialBairroCidade">#{centralAlerta.filialTela.bairro}, #{centralAlerta.filialTela.cidade}/#{centralAlerta.filialTela.UF}</label>
                            </div>

                            <div id="divCalendario"
                                 class="col-xs-12 col-sm-4 col-sm-pull-1"
                                 style="display: flex; justify-content: center; padding-right: 15px;">
                                <!--Botão voltar-->
                                <p:commandLink action="#{centralAlerta.mudarMes(-1)}" update="msgs cabecalho main:tabela">
                                    <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 40px"/>  
                                </p:commandLink>

                                <p:commandLink
                                    id="calendar"
                                    oncomplete="PF('oCalendarios').loadContents();"
                                    styleClass="botao"
                                    update="#{cc.attrs.update}"
                                    >
                                    <p:graphicImage 
                                        url="../assets/img/icone_escaladodia.png"
                                        style="align-self: center;height: 40px"
                                        />
                                </p:commandLink>

                                <!--Botão avançar-->
                                <p:commandLink action="#{centralAlerta.mudarMes(1)}" update="msgs cabecalho main:tabela">
                                    <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 40px"/>
                                </p:commandLink>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form>
                    <p:overlayPanel
                        id="calendarios"
                        for="cabecalho:calendar"
                        showEffect="fade"
                        hideEffect="fade"
                        dynamic="true"
                        dismissable="true"
                        style="font-size: 14px;"
                        widgetVar="oCalendarios"
                        my="top"
                        at="bottom"
                        class="overlay"
                        >
                        <div class="ui-grid-row ui-grid-responsive">
                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row"
                                     style="margin: 5px">
                                    <h:outputText id="cal1"
                                                  value="#{localemsgs.DataInicial}:"
                                                  title="#{localemsgs.DataInicial}"/>
                                </div>
                                <div class="ui-grid-row"
                                     style="margin: 5px">
                                    <p:calendar id="calendario1"
                                                styleClass="calendario"
                                                value="#{centralAlerta.dataInicio}"
                                                converter="conversorLocalDate"
                                                mask="true"
                                                title="#{localemsgs.DataInicial}"
                                                label="#{localemsgs.DataInicial}"
                                                pattern="#{mascaras.getPadraoDataS()}"
                                                locale="#{localeController.getCurrentLocale()}"
                                                />
                                </div>
                            </div>

                            <div class="ui-grid-col-6">
                                <div class="ui-grid-row"
                                     style="margin: 5px">
                                    <h:outputText id="cal2"
                                                  value="#{localemsgs.DataFinal}:"
                                                  title="#{localemsgs.DataFinal}"/>
                                </div>
                                <div class="ui-grid-row" style="margin: 5px">
                                    <p:calendar id="calendario2"
                                                styleClass="calendario"
                                                value="#{centralAlerta.dataFim}"
                                                converter="conversorLocalDate"
                                                mask="true"
                                                title="#{localemsgs.DataFinal}"
                                                label="#{localemsgs.DataFinal}"
                                                pattern="#{mascaras.getPadraoDataS()}"
                                                locale="#{localeController.getCurrentLocale()}"/>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: right; float: right">
                            <p:commandLink action="#{centralAlerta.selecionarData}"
                                           style="float: right"
                                           update="main msgs cabecalho">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                            </p:commandLink>
                        </div>
                    </p:overlayPanel>
                </h:form>

                <h:form id="main">
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{centralAlerta.listaQueueFechPessoa}"
                                        paginator="true"
                                        rows="50"
                                        reflow="true"
                                        rowsPerPageTemplate="50"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.QueueFech}"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        selectionMode="single"
                                        styleClass="tabela"
                                        selection="#{centralAlerta.queueFechPessoaSelecionado}"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        class="tabela DataGrid"
                                        scrollHeight="100%"
                                        var="queue"
                                        rowKey="#{queue.queueFech.sequencia}"
                                        style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax event="rowDblselect" listener="#{centralAlerta.selecionarQueue}" update="formEdicao msgs"/>

                                        <p:column headerText="#{localemsgs.Data}" class="text-center">
                                            <h:outputText value="#{queue.queueFech.data}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hora}" class="text-center">
                                            <h:outputText value="#{queue.queueFech.hora}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Solicitante}" class="text-center">
                                            <h:outputText value="#{queue.pessoa.nome}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.TipoMensagem}" class="text-center">
                                            <h:outputText value="#{queue.queueFech.comando_Ref}" class="text-center"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.StatusMensagem}" class="text-center">
                                            <h:outputText value="#{queue.queueFech.comando_Crt}" class="text-center"/>
                                        </p:column>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 110px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}"
                                           actionListener="#{centralAlerta.actionListenerEditar}"
                                           update="msgs formEdicao:panelEditar cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="display:none; padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                           oncomplete="PF('dlgPesquisaRapida').show();"
                                           actionListener="#{centralAlerta.prePesquisaCadastro}"
                                           update="formPesquisaRapida:panelPesquisaRapida cabecalho">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="display:none; padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}"
                                           action="#{centralAlerta.limparFiltrosCadastro}"
                                           update="msgs main:tabela cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>

                </h:form>

                <h:form class="form-inline" id="formEdicao">
                    <p:hotkey bind="esc" oncomplete="PF('dlgEdicao').hide()"/>

                    <p:dialog widgetVar="dlgEdicao"
                              positionType="absolute"
                              focus="pathSrv"
                              responsive="true"
                              draggable="false"
                              modal="true"
                              closable="true"
                              resizable="false"
                              dynamic="true"
                              showEffect="drop"
                              hideEffect="drop"
                              closeOnEscape="false"
                              id="dlgEdicao"
                              styleClass="dialogo"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgEdicao').closeIcon.unbind('click');

                                //register your own
                                PF('dlgEdicao').closeIcon.click(function (e) {
                                    $("#formEdicao\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar"
                                         style="display: none"
                                         oncomplete="PF('dlgEdicao').hide()"
                                         id="botaoFechar"/>

                        <f:facet name="header">
                            <img src="../assets/img/icone_Dashboard_cliente.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText
                                value="#{localemsgs.Editar}"
                                style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="panelEditar"
                                 style="background-color: transparent; max-width: 100% !important; padding-right:0px !important; padding-left:8px !important;"
                                 class="cadastrar">
                            <p:panelGrid columns="2"
                                         columnClasses="ui-grid-col-2,ui-grid-col-10"
                                         layout="grid"
                                         styleClass="ui-panelgrid-blank">
                                <p:outputLabel value="#{localemsgs.Sequencia}: "/>
                                <h:outputText value="#{centralAlerta.queueFechPessoaEdicao.queueFech.sequencia}"
                                              converter="conversor0"/>

                                <p:outputLabel for="pathSrv" value="#{localemsgs.PathSrv}: "/>
                                <p:inputText id="pathSrv"
                                             value="#{centralAlerta.queueFechPessoaEdicao.queueFech.pathSrv}"
                                             required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.descricao}"
                                             label="#{localemsgs.PathSrv}"
                                             style="width: 100%"
                                             maxlength="60">
                                    <p:watermark for="pathSrv"
                                                 value="#{localemsgs.PathSrv}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="1"
                                         columnClasses="ui-grid-col-12"
                                         layout="grid"
                                         styleClass="ui-panelgrid-blank"
                                         style="margin-top:6px; padding-left:0px !important">
                                <p:commandLink title="#{localemsgs.Salve}"
                                               action="#{centralAlerta.editarQueue()}"
                                               update="main formEdicao:panelEditar msgs"
                                               style="width:100%">
                                    <label class="btn btn-lg btn-success"
                                           style="width:100% !important;margin-left: 0px;">
                                        <i class="fa fa-save"></i>&#xA0;#{localemsgs.Salve}</label>
                                </p:commandLink>
                            </p:panelGrid>

                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Pesquisar rápida -->
                <h:form id="formPesquisaRapida">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_cardapio1.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarCardapio}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent;margin-top:0px !important;">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top:0px !important;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{centralAlerta.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection"
                                        >
                                        <f:selectItem itemLabel="#{localemsgs.descricao}" itemValue="D" />
                                        <f:selectItem itemLabel="#{localemsgs.Especificacao}" itemValue="E" />
                                        <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="C" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{centralAlerta.chavePesquisa eq 'D'}" value="#{localemsgs.descricao}: "/>
                                        <p:outputLabel for="opcao" rendered="#{centralAlerta.chavePesquisa eq 'E'}" value="#{localemsgs.Especificacao}: "/>
                                        <p:outputLabel for="opcao" rendered="#{centralAlerta.chavePesquisa eq 'C'}" value="#{localemsgs.Codigo}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{centralAlerta.valorPesquisa}"
                                            required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Especificacao}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div style="top:15px !important">
                                <p:commandLink id="botaoPesquisaRapida"
                                               action="#{centralAlerta.pesquisaRapidaCadastro()}"
                                               update=" :main:tabela :msgs cabecalho"
                                               title="#{localemsgs.Pesquisar}" style="margin-top:10px !important">
                                    <label class="btn btn-lg btn-primary"
                                           style="width:100% !important;margin-left: 0px; margin-top:10px !important">
                                        <i class="fa fa-search"></i>&#xA0;#{localemsgs.Pesquisar}</label>
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>

            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

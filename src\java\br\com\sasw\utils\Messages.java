/*
 */
package br.com.sasw.utils;

import br.com.sasw.pacotesuteis.utilidades.Extenso;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.ResourceBundle;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.faces.context.FacesContext;

/**
 *
 * <AUTHOR>
 */
public class Messages {

    private static Locale locale;
    private static final String BASE_NAME = "br.com.sasw.messages.messages";

    /**
     * Retorna a data no formato do idioma
     *
     * @param data
     * @param formatoEntrada - formato de entrada: yyyy-MM-dd, yyyyMMdd, etc...
     * @return
     */
    public static String getDataS(String data, String formatoEntrada) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        String message;
        try {
            LocalDate datetime = LocalDate.parse(data, DateTimeFormatter.ofPattern(formatoEntrada));
            switch (locale.getLanguage()) {
                case "en":
                    message = datetime.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
                    break;
                default:
                    message = datetime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            }
        } catch (Exception e) {
            message = data;
        }
        return message;
    }

    /**
     * Retorna a data no formato do idioma
     *
     * @param hora
     * @param formatoEntrada - formato de entrada: hh:mm a, HH:mm, HH:mm:ss.S
     * etc...
     * @return
     */
    public static String getHoraS(String hora, String formatoEntrada) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        String message;
        try {
            LocalTime datetime = LocalTime.parse(hora, DateTimeFormatter.ofPattern(formatoEntrada));
            switch (locale.getLanguage()) {
                case "en":
                    message = datetime.format(DateTimeFormatter.ofPattern("hh:mm a"));
                    break;
                default:
                    message = datetime.format(DateTimeFormatter.ofPattern("HH:mm"));
            }
        } catch (Exception e) {
            message = hora;
        }
        return message;
    }

    public static String replaceTags(String original) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        Pattern TAG = Pattern.compile("([@][a-zA-Z0-9]*)");
        Matcher matcher = TAG.matcher(original);
        String retorno = original;
        while (matcher.find()) {
            try {
                retorno = retorno.replace(matcher.group(1), ResourceBundle.getBundle(BASE_NAME, locale).getString(matcher.group(1).replace("@", "")));
            } catch (Exception e) {

            }
        }
        return retorno;
    }

    public static String getMessageS(String key) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        String message;
        try {
            message = ResourceBundle.getBundle(BASE_NAME, locale).getString(key);
        } catch (Exception e) {
            message = key;
        }
        return message;
    }

    public static String getValorExtensoS(String valor) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        return "(" + Extenso.valorPorExtenso(Double.parseDouble(valor), locale.getLanguage()).toUpperCase() + " "
                + FuncoesString.PreencheEsquerda("/", 40, "/ ") + ")";
    }

    public static String getValorExtensoS(String valor, String moeda) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        return "(" + Extenso.valorPorExtenso(Double.parseDouble(valor), locale.getLanguage(), moeda).toUpperCase() + " "
                + FuncoesString.PreencheEsquerda("/", 40, "/ ") + ")";
    }

    public static String getValorExtensoMoedaS(String valor) {
        locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
        return Extenso.valorPorExtenso(Double.parseDouble(valor), locale.getLanguage()).toUpperCase();
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.List;
import java.util.Objects;

/**
 * S-2299 - Desligamento
 *
 * <AUTHOR>
 */
public class S2299 {

    private String evtDeslig_Id;
    private int sucesso;
    private String matr;
    private String infoDeslig_qtdDiasInterm;
    private String ideEvento_indRetif;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideVinculo_cpfTrab;
    private String ideVinculo_nisTrab;
    private String ideVinculo_matricula;
    private String ideVinculo_trabIntermitente;
    

    private String infoDeslig_mtvDeslig;
    private String infoDeslig_dtDeslig;
    private String infoDeslig_indPagtoAPI;
    private String infoDeslig_dtProjFimAPI;
    private String infoDeslig_pensAlim;
    private String infoDeslig_percAliment;
    private String infoDeslig_vrAlim;
    private String infoDeslig_nrCertObito;
    private String infoDeslig_indCumprParc;
    private String ideEstabLot_tpInsc;
    private String ideEstabLot_nrInsc;
    private String ideEstabLot_codLotacao;
    private String ideEvento_compet;
    private String sucessaoVinc_cnpjSucessora;
    private String detAvPrevio_dtAvPrevio;
    
    private String matrNaoCodPonto;
    

    public String getMatr() {
        return matr;
    }

    public String getMatrFloat() {
        return matrNaoCodPonto;
    }
    
    
    public void setMatr(String matr) {
        this.matr = matr;
    }

    public String getSucessaoVinc_cnpjSucessora() {
        return sucessaoVinc_cnpjSucessora;
    }

    public void setSucessaoVinc_cnpjSucessora(String sucessaoVinc_cnpjSucessora) {
        this.sucessaoVinc_cnpjSucessora = sucessaoVinc_cnpjSucessora;
    }

    public String getdetAvPrevio_dtAvPrevio() {
        return detAvPrevio_dtAvPrevio;
    }

    public void setdetAvPrevio_dtAvPrevio(String detAvPrevio_dtAvPrevio) {
        this.detAvPrevio_dtAvPrevio = detAvPrevio_dtAvPrevio;
    }    
    
    
    public String getIdeEvento_compet() {
        return ideEvento_compet;
    }

    public void setIdeEvento_compet(String ideEvento_compet) {
        this.ideEvento_compet = ideEvento_compet;
    }

    private String dmDev_ideDmDev;
    private InfoPerApur infoPerApur_ideEstabLot;
    //private String infoDeslig_qtdDiasInterm;

    public String getInfoDeslig_qtdDiasInterm() {
        return infoDeslig_qtdDiasInterm;
    }

    public void setInfoDeslig_qtdDiasInterm(String infoDeslig_qtdDiasInterm) {
        this.infoDeslig_qtdDiasInterm = infoDeslig_qtdDiasInterm;
    }

    public String getEvtDeslig_Id() {
        return evtDeslig_Id == null ? "" : evtDeslig_Id;
    }

    public void setEvtDeslig_Id(String evtDeslig_Id) {
        this.evtDeslig_Id = evtDeslig_Id;
    }

    //public String getInfoDeslig_qtdDiasInterm() {
    //    return infoDeslig_qtdDiasInterm == null ? "" : infoDeslig_qtdDiasInterm;
    //}
    //public void setInfoDeslig_qtdDiasInterm(String infoDeslig_qtdDiasInterm) {
    //    this.infoDeslig_qtdDiasInterm = infoDeslig_qtdDiasInterm;
    //}
    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif == null ? "" : ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb == null ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi == null ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc == null ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc == null ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc == null ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab == null ? "" : ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_nisTrab() {
        return ideVinculo_nisTrab == null ? "" : ideVinculo_nisTrab;
    }

    public void setIdeVinculo_nisTrab(String ideVinculo_nisTrab) {
        this.ideVinculo_nisTrab = ideVinculo_nisTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula == null ? "" : ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getInfoDeslig_mtvDeslig() {
        return infoDeslig_mtvDeslig == null ? "" : infoDeslig_mtvDeslig;
    }

    public void setInfoDeslig_mtvDeslig(String infoDeslig_mtvDeslig) {
        this.infoDeslig_mtvDeslig = infoDeslig_mtvDeslig;
    }

    public String getInfoDeslig_dtDeslig() {
        return infoDeslig_dtDeslig == null ? "" : infoDeslig_dtDeslig;
    }

    public void setInfoDeslig_dtDeslig(String infoDeslig_dtDeslig) {
        this.infoDeslig_dtDeslig = infoDeslig_dtDeslig;
    }

    public String getInfoDeslig_indPagtoAPI() {
        return infoDeslig_indPagtoAPI == null ? "" : infoDeslig_indPagtoAPI;
    }

    public void setInfoDeslig_indPagtoAPI(String infoDeslig_indPagtoAPI) {
        this.infoDeslig_indPagtoAPI = infoDeslig_indPagtoAPI;
    }

    public String getInfoDeslig_dtProjFimAPI() {
        return infoDeslig_dtProjFimAPI == null ? "" : infoDeslig_dtProjFimAPI;
    }

    public void setInfoDeslig_dtProjFimAPI(String infoDeslig_dtProjFimAPI) {
        this.infoDeslig_dtProjFimAPI = infoDeslig_dtProjFimAPI;
    }

    public String getInfoDeslig_pensAlim() {
        return infoDeslig_pensAlim == null ? "" : infoDeslig_pensAlim;
    }

    public void setInfoDeslig_pensAlim(String infoDeslig_pensAlim) {
        this.infoDeslig_pensAlim = infoDeslig_pensAlim;
    }

    public String getInfoDeslig_percAliment() {
        return infoDeslig_percAliment == null ? "" : infoDeslig_percAliment;
    }

    public void setInfoDeslig_percAliment(String infoDeslig_percAliment) {
        this.infoDeslig_percAliment = infoDeslig_percAliment;
    }

    public String getInfoDeslig_vrAlim() {
        return infoDeslig_vrAlim == null ? "" : infoDeslig_vrAlim;
    }

    public void setInfoDeslig_vrAlim(String infoDeslig_vrAlim) {
        this.infoDeslig_vrAlim = infoDeslig_vrAlim;
    }

    public String getInfoDeslig_nrCertObito() {
        return infoDeslig_nrCertObito == null ? "" : infoDeslig_nrCertObito;
    }

    public void setInfoDeslig_nrCertObito(String infoDeslig_nrCertObito) {
        this.infoDeslig_nrCertObito = infoDeslig_nrCertObito;
    }

    public String getInfoDeslig_indCumprParc() {
        return infoDeslig_indCumprParc == null ? "" : infoDeslig_indCumprParc;
    }

    public void setInfoDeslig_indCumprParc(String infoDeslig_indCumprParc) {
        this.infoDeslig_indCumprParc = infoDeslig_indCumprParc;
    }

    public InfoPerApur getInfoPerApur_ideEstabLot() {
        return infoPerApur_ideEstabLot;
    }

    public void setInfoPerApur_ideEstabLot(InfoPerApur infoPerApur_ideEstabLot) {
        this.infoPerApur_ideEstabLot = infoPerApur_ideEstabLot;
    }

    public String getIdeEstabLot_tpInsc() {
        return ideEstabLot_tpInsc == null ? "" : ideEstabLot_tpInsc;
    }

    public void setIdeEstabLot_tpInsc(String ideEstabLot_tpInsc) {
        this.ideEstabLot_tpInsc = ideEstabLot_tpInsc;
    }

    public String getIdeEstabLot_nrInsc() {
        return ideEstabLot_nrInsc == null ? "" : ideEstabLot_nrInsc;
    }

    public void setIdeEstabLot_nrInsc(String ideEstabLot_nrInsc) {
        this.ideEstabLot_nrInsc = ideEstabLot_nrInsc;
    }

    public String getIdeEstabLot_codLotacao() {
        return ideEstabLot_codLotacao == null ? "" : ideEstabLot_codLotacao;
    }

    public void setIdeEstabLot_codLotacao(String ideEstabLot_codLotacao) {
        this.ideEstabLot_codLotacao = ideEstabLot_codLotacao;
    }

    public String getDmDev_ideDmDev() {
        return dmDev_ideDmDev == null ? "" : dmDev_ideDmDev;
    }

    public void setDmDev_ideDmDev(String dmDev_ideDmDev) {
        this.dmDev_ideDmDev = dmDev_ideDmDev;
    }

    public static class InfoPerApur {

        private List<DetVerbas> ideEstabLot_detVerbas;
        private List<DetOper> infoSaudeColet_detOper;

        public List<DetVerbas> getIdeEstabLot_detVerbas() {
            return ideEstabLot_detVerbas;
        }

        public void setIdeEstabLot_detVerbas(List<DetVerbas> ideEstabLot_detVerbas) {
            this.ideEstabLot_detVerbas = ideEstabLot_detVerbas;
        }

        public List<DetOper> getInfoSaudeColet_detOper() {
            return infoSaudeColet_detOper;
        }

        public void setInfoSaudeColet_detOper(List<DetOper> infoSaudeColet_detOper) {
            this.infoSaudeColet_detOper = infoSaudeColet_detOper;
        }
    }

    public static class DetVerbas {

        private String detVerbas_codRubr;
        private String detVerbas_ideTabRubr;
        private String detVerbas_qtdRubr;
        private String detVerbas_vrUnit;
        private String detVerbas_vrRubr;
        private DescFolha descFolha; 

        public DescFolha getDescFolha() {
            return descFolha;
        }

        public void setDescFolha(DescFolha descFolha) {
            this.descFolha = descFolha;
        }

        public String getDetVerbas_codRubr() {
            return detVerbas_codRubr == null ? "" : detVerbas_codRubr;
        }

        public void setDetVerbas_codRubr(String detVerbas_codRubr) {
            this.detVerbas_codRubr = detVerbas_codRubr;
        }

        public String getDetVerbas_ideTabRubr() {
            return detVerbas_ideTabRubr == null ? "" : detVerbas_ideTabRubr;
        }

        public void setDetVerbas_ideTabRubr(String detVerbas_ideTabRubr) {
            this.detVerbas_ideTabRubr = detVerbas_ideTabRubr;
        }

        public String getDetVerbas_qtdRubr() {
            return detVerbas_qtdRubr == null ? "" : detVerbas_qtdRubr;
        }

        public void setDetVerbas_qtdRubr(String detVerbas_qtdRubr) {
            this.detVerbas_qtdRubr = detVerbas_qtdRubr;
        }

        public String getDetVerbas_vrUnit() {
            return detVerbas_vrUnit == null ? "" : detVerbas_vrUnit;
        }

        public void setDetVerbas_vrUnit(String detVerbas_vrUnit) {
            this.detVerbas_vrUnit = detVerbas_vrUnit;
        }

        public String getDetVerbas_vrRubr() {
            return detVerbas_vrRubr == null ? "" : detVerbas_vrRubr;
        }

        public void setDetVerbas_vrRubr(String detVerbas_vrRubr) {
            this.detVerbas_vrRubr = detVerbas_vrRubr;
        }
    }

    public static class DetOper {

        private String detOper_cnpjOper;
        private String detOper_regANS;
        private String detOper_vrPgTit;
        private List<DetPlano> detOper_detPlano;

        public String getDetOper_cnpjOper() {
            return detOper_cnpjOper == null ? "" : detOper_cnpjOper;
        }

        public void setDetOper_cnpjOper(String detOper_cnpjOper) {
            this.detOper_cnpjOper = detOper_cnpjOper;
        }

        public String getDetOper_regANS() {
            return detOper_regANS == null ? "" : detOper_regANS;
        }

        public void setDetOper_regANS(String detOper_regANS) {
            this.detOper_regANS = detOper_regANS;
        }

        public String getDetOper_vrPgTit() {
            return detOper_vrPgTit == null ? "" : detOper_vrPgTit;
        }

        public void setDetOper_vrPgTit(String detOper_vrPgTit) {
            this.detOper_vrPgTit = detOper_vrPgTit;
        }

        public List<DetPlano> getDetOper_detPlano() {
            return detOper_detPlano;
        }

        public void setDetOper_detPlano(List<DetPlano> detOper_detPlano) {
            this.detOper_detPlano = detOper_detPlano;
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 97 * hash + Objects.hashCode(this.detOper_regANS);
            hash = 97 * hash + Objects.hashCode(this.detOper_vrPgTit);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DetOper other = (DetOper) obj;
            if (!Objects.equals(this.detOper_regANS, other.detOper_regANS)) {
                return false;
            }
            return true;
        }
    }

    public static class DetPlano {

        private String detPlano_tpDep;
        private String detPlano_cpfDep;
        private String detPlano_nmDep;
        private String detPlano_dtNascto;
        private String detPlano_vlrPgDep;

        public String getDetPlano_tpDep() {
            return detPlano_tpDep == null ? "" : detPlano_tpDep;
        }

        public void setDetPlano_tpDep(String detPlano_tpDep) {
            this.detPlano_tpDep = detPlano_tpDep;
        }

        public String getDetPlano_cpfDep() {
            return detPlano_cpfDep == null ? "" : detPlano_cpfDep;
        }

        public void setDetPlano_cpfDep(String detPlano_cpfDep) {
            this.detPlano_cpfDep = detPlano_cpfDep;
        }

        public String getDetPlano_nmDep() {
            return detPlano_nmDep == null ? "" : detPlano_nmDep;
        }

        public void setDetPlano_nmDep(String detPlano_nmDep) {
            this.detPlano_nmDep = detPlano_nmDep;
        }

        public String getDetPlano_dtNascto() {
            return detPlano_dtNascto == null ? "" : detPlano_dtNascto;
        }

        public void setDetPlano_dtNascto(String detPlano_dtNascto) {
            this.detPlano_dtNascto = detPlano_dtNascto;
        }

        public String getDetPlano_vlrPgDep() {
            return detPlano_vlrPgDep == null ? "" : detPlano_vlrPgDep;
        }

        public void setDetPlano_vlrPgDep(String detPlano_vlrPgDep) {
            this.detPlano_vlrPgDep = detPlano_vlrPgDep;
        }
    }
    
    public static class DescFolha {
        private String descFolha_instFinanc;
        private String descFolha_nrDoc;
        private String descFolha_observacao;      

        public String getDescFolha_instFinanc() {
            return descFolha_instFinanc;
        }

        public void setDescFolha_instFinanc(String descFolha_instFinanc) {
            this.descFolha_instFinanc = descFolha_instFinanc;
        }

        public String getDescFolha_nrDoc() {
            return descFolha_nrDoc;
        }

        public void setDescFolha_nrDoc(String descFolha_nrDoc) {
            this.descFolha_nrDoc = descFolha_nrDoc;
        }

        public String getDescFolha_observacao() {
            return descFolha_observacao;
        }

        public void setDescFolha_observacao(String descFolha_observacao) {
            this.descFolha_observacao = descFolha_observacao;
        }
    };

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 11 * hash + Objects.hashCode(this.ideVinculo_matricula);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S2299 other = (S2299) obj;
        if (!Objects.equals(this.ideVinculo_matricula, other.ideVinculo_matricula)) {
            return false;
        }
        return true;
    }

    public String getIdeVinculo_trabIntermitente() {
        return ideVinculo_trabIntermitente;
    }

    public void setIdeVinculo_trabIntermitente(String ideVinculo_trabIntermitente) {
        this.ideVinculo_trabIntermitente = ideVinculo_trabIntermitente;
    }
    
    public void setMatrFloat(String matrFloat)  {
        this.matrNaoCodPonto = matrFloat;
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.GTV;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.TesSaidas;
import SasBeansCompostas.GTVPedidoOSClienteTesSaida;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GTVDao {

    private String sql;

    /**
     * Faz a validação da GTV
     *
     * @param CodFil - Código da Filial
     * @param Guia - Número da Guia
     * @param Serie - Série da Guia
     * @param persistencia - Conexão
     * @return
     * @throws Exception
     */
    public GTV validaGTV(String CodFil, String Guia, String Serie, Persistencia persistencia) throws Exception {
        //ResultSet rssg;
        GTV gtv;
        sql = "select CodFil, OS, Situacao, Operador from GTV where CodFil = ? and G<PERSON><PERSON> = ? and Serie = ?";
        try {
            Consulta rssg = new Consulta(sql, persistencia);
            rssg.setString(CodFil);
            rssg.setString(Guia);
            rssg.setString(Serie);
            rssg.select();

            gtv = new GTV();

            while (rssg.Proximo()) {
                try {
                    gtv.setOS(rssg.getString("OS"));
                } catch (Exception e) {
                    gtv.setOS("0");
                }
                try {
                    gtv.setSituacao(rssg.getString("Situacao"));
                } catch (Exception e) {
                    gtv.setSituacao("-1");
                }
                gtv.setCodFil(CodFil);
                gtv.setGuia(Guia);
                gtv.setSerie(Serie);
            }
            rssg.Close();
            return gtv;
        } catch (Exception e) {
            throw new Exception("GTVDao.validaGTV - " + e.getMessage() + "\r\n"
                    + "select CodFil, OS, Situacao, Operador from GTV where CodFil = " + CodFil + " and Guia = " + Guia + " and Serie = " + Serie);
        }
    }

    public List<GTV> existeGTV(String Os, String Guia, String Serie, Persistencia persistencia) throws Exception {

        List<GTV> lGTV = new ArrayList<GTV>();
        Consulta meuState;
        sql = " Select Situacao from GTV where Guia=? and Serie=? ";
        String sql1 = " Update GTV set  Situacao = '8', OS = ? where Guia = ? and Serie = ? ";
        try {
            Consulta rsgtv = new Consulta(sql, persistencia);
            rsgtv.setString(Guia);
            rsgtv.setString(Serie);
            rsgtv.select();
            while (rsgtv.Proximo()) {
                GTV oGTV = new GTV();
                oGTV.setSituacao(rsgtv.getString("Situacao"));

                if (!"6".equals(oGTV.getSituacao()) && !"8".equals(oGTV.getSituacao())) {

                    meuState = new Consulta(sql1, persistencia);
                    meuState.setString(Os);
                    meuState.setString(Guia);
                    meuState.setString(Serie);
                    meuState.update();
                    meuState.close();
                }

                lGTV.add(oGTV);
            }
            rsgtv.Close();
            return lGTV;
        } catch (Exception e) {
            throw new Exception("GTVDao.existeGTV - " + e.getMessage());
        }
    }

    /*
     * Atualiza a guia em GTV
     * @param CodFil - Parada da Rota
     * @param guia - Número da Guia a verificar
     * @param serie - Série da Guia a verificar
     * @param persistencia - Conexão ao banco
     * @param OS - Número os
     * @throws Exception 
     */
    public void atualGTV(Persistencia persistencia, String OS,
            String CodFil, String guia, String serie) throws Exception {
        sql = "update GTV "
                + " set Situacao='8',OS=? "
                + " where "
                + " CodFil=? "
                + " and Guia= ?"
                + " and Serie=? ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("GTVDao.atualGTV - " + e.getMessage() + "\r\n"
                    + "update GTV "
                    + " set Situacao='8',OS=" + OS
                    + " where "
                    + " CodFil=" + CodFil
                    + " and Guia= " + guia
                    + " and Serie=" + serie);
        }
    }

    /**
     * Apaga a guia em GTV
     *
     * @param codFil - Parada da Rota
     * @param guia - Número da Guia a verificar
     * @param serie - Série da Guia a verificar
     * @param persistencia - Conexão ao banco
     * @throws Exception
     */
    public void apagaGuia(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        sql = "delete from gtv where"
                + " guia=?"
                + " and serie=?"
                + " and codfil=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("GTVDao.apagaGuia - " + e.getMessage() + "\r\n"
                    + "delete from gtv where"
                    + " guia=" + guia
                    + " and serie=" + serie
                    + " and codfil=" + codFil);
        }
    }

    public void AdicionaGTVpe(Persistencia persistencia, String CodFil, String guia, String serie,
            String OS, String Dt_alter, String Hr_Alter) throws Exception {

        try {

            sql = "Insert into GTV (CodFil, Guia, Serie, DtGeracao, OS, Situacao, Status, Operador, Dt_alter, Hr_alter) "
                    + "values (?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(Dt_alter);
            consulta.setString(OS);
            consulta.setString("8");
            consulta.setString("PD");
            consulta.setString("SATMOB");
            consulta.setString(Dt_alter);
            consulta.setString(Hr_Alter);
            consulta.insert();
            consulta.close();
            //Verifica se a serie é de RPV
            if (serie.equals("_53")) {
                String sql2 = "UPDATE GTVseq SET sequencia = ?, operador = ?, dt_alter = ?, hr_alter = ? WHERE serie = ? AND codFil = ?";
                consulta = new Consulta(sql2, persistencia);
                consulta.setString(guia);
                consulta.setString("SATMOB");
                consulta.setString(Dt_alter);
                consulta.setString(Hr_Alter);
                consulta.setString(serie);
                consulta.setString(CodFil);
                consulta.update();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("GTVDao.AdicionaGTVpe - " + e.getMessage() + "\r\n"
                    + "Insert into GTV (CodFil, Guia, Serie, DtGeracao, OS, Situacao, Status, Operador, Dt_alter, Hr_alter) "
                    + "values (" + CodFil + "," + guia + "," + serie + "," + Dt_alter + "," + OS + "," + "8" + "," + "PD" + "," + "SATMOB" + "," + Dt_alter + "," + Hr_Alter + ")");
        }
    }

    /**
     * Adiciona GTV
     *
     * @param persistencia - Conexão ao banco
     * @param CodFil - Codigo filial
     * @param guia - Número da guia
     * @param serie - Número série
     * @param rota - Seqüência de Rota
     * @param parada - Número da Parada
     * @param OS - Número OS
     * @param Dt_alter - Data alteração
     * @param Hr_Alter - Hora alteração
     */
    public void AdicionaGTV(Persistencia persistencia, String CodFil, String guia, String serie,
            String rota, String parada, String OS, String Dt_alter, String Hr_Alter) throws Exception {

        try {
            sql = "insert into GTV (CodFil,Guia,Serie,SeqRota,Parada,Situacao,OS,Operador,Dt_Alter,Hr_Alter) "
                    + "values (?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(rota);
            consulta.setString(parada);
            consulta.setString("8");
            consulta.setString(OS);
            consulta.setString("SATMOB");
            consulta.setString(Dt_alter);
            consulta.setString(Hr_Alter);
            consulta.insert();
            consulta.close();
            
            //Verifica se a serie é de RPV
            if (serie.equals("_53")) {
                String sql2 = "UPDATE GTVseq SET sequencia = ?, operador = ?, dt_alter = ?, hr_alter = ? WHERE serie = ? AND codFil = ?";
                consulta = new Consulta(sql2, persistencia);
                consulta.setString(guia);
                consulta.setString("SATMOB");
                consulta.setString(Dt_alter);
                consulta.setString(Hr_Alter);
                consulta.setString(serie);
                consulta.setString(CodFil);
                consulta.update();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("GTVDao.AdicionaGTV - " + e.getMessage() + "\r\n"
                    + "insert into GTV (CodFil,Guia,Serie,SeqRota,Parada,Situacao,OS,Operador,Dt_Alter,Hr_Alter) "
                    + "values (" + CodFil + "," + guia + "," + serie + "," + rota + "," + parada + "," + "8" + "," + OS + "," + "SATMOB" + "," + Dt_alter + "," + Hr_Alter + ")");
        }
    }

    /**
     * Adiciona GTV
     *
     * @param persistencia - Conexão ao banco
     * @param CodFil - Codigo filial
     * @param guia - Número da guia
     * @param serie - Número série
     * @param sequencia - Seqüência de Rota
     * @param parada - Número da Parada
     * @param Situacao - Situação da GTV
     * @param OS - Número OS
     * @param Dt_alter - Data alteração
     * @param Operador - Operador
     * @param Hr_Alter - Hora alteração
     */
    public void AdicionaGTV(Persistencia persistencia, String CodFil, String guia, String serie,
            String sequencia, String parada, String Situacao, String OS,
            String Operador, String Dt_alter, String Hr_Alter) throws Exception {

        try {
            sql = "insert into GTV (CodFil,Guia,Serie,SeqRota,Parada,Situacao,OS,Operador,Dt_Alter,Hr_Alter) "
                    + "values (?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(Situacao);
            consulta.setString(OS);
            consulta.setString(Operador);
            consulta.setString(Dt_alter);
            consulta.setString(Hr_Alter);
            consulta.insert();
            consulta.close();

            //Verifica se a serie é de RPV
            if (serie.equals("_53")) {
                String sql2 = "UPDATE GTVseq SET sequencia = ?, operador = ?, dt_alter = ?, hr_alter = ? WHERE serie = ? AND codFil = ?";
                consulta = new Consulta(sql2, persistencia);
                consulta.setString(guia);
                consulta.setString("SATMOB");
                consulta.setString(Dt_alter);
                consulta.setString(Hr_Alter);
                consulta.setString(serie);
                consulta.setString(CodFil);
                consulta.update();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("GTVDao.AdicionaGTV - " + e.getMessage());
        }
    }

    /**
     * Atualiza GTV
     *
     * @param persistencia - Conexão ao banco
     * @param CodFil - Codigo filial
     * @param guia - Número da guia
     * @param serie - Número série
     * @param Dt_alter - Data alteração
     * @param Hr_Alter - Hora alteração
     * @param obs - Observação
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaGTV(Persistencia persistencia, String Dt_alter, String Hr_Alter, String obs,
            String CodFil, String guia, String serie) throws Exception {
        sql = "update GTV set Situacao='8', "
                + " Operador='SATMOB', "
                + " Dt_Alter=?,"
                + " Hr_Alter=?,"
                + " Obs=?"
                + " where CodFil=?"
                + " and Guia=?"
                + " and Serie=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Dt_alter);
            consulta.setString(Hr_Alter);
            consulta.setString(obs);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("GTVDao.atualizaGTV - " + e.getMessage() + "\r\n"
                    + "update GTV set Situacao='8', "
                    + " Operador='SATMOB', "
                    + " Dt_Alter=" + Dt_alter + ","
                    + " Hr_Alter=" + Hr_Alter + ","
                    + " Obs=" + obs
                    + " where CodFil=" + CodFil
                    + " and Guia=" + guia
                    + " and Serie=" + serie);
        }
    }

    /**
     * Atualiza GTV
     *
     * @param persistencia - Conexão ao banco
     * @param CodFil - Codigo filial
     * @param guia - Número da guia
     * @param serie - Número série
     * @param Dt_alter - Data alteração
     * @param Hr_Alter - Hora alteração
     * @param obs - Observação
     * @param OS
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaGTV(Persistencia persistencia, String Dt_alter, String Hr_Alter, String obs,
            String CodFil, String guia, String serie, String OS) throws Exception {
        sql = "update GTV set Situacao = '8', "//fechada no operacional
                + " Operador = 'SATMOB', "
                + " Dt_Alter = ?,"
                + " Hr_Alter = ?,"
                + " Obs = ?,"
                + " OS = ?"
                + " where CodFil=?"
                + " and Guia=?"
                + " and Serie=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Dt_alter);
            consulta.setString(Hr_Alter);
            consulta.setString(obs);
            consulta.setString(OS);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("GTVDao.atualizaGTV - " + e.getMessage() + "\r\n"
                    + "update GTV set Situacao = '8', "
                    + " Operador = 'SATMOB', "
                    + " Dt_Alter = " + Dt_alter + ","
                    + " Hr_Alter = " + Hr_Alter + ","
                    + " Obs = " + obs + ","
                    + " OS = " + OS
                    + " where CodFil=" + CodFil
                    + " and Guia=" + guia
                    + " and Serie=" + serie);
        }
    }

    public void deletaGTV(Persistencia persistencia, String CodFil, String guia, String serie) throws Exception {
        sql = "delete from GTV "
                + " where "
                + " CodFil=? "
                + " and Guia=? "
                + " and Serie=? ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("GTVDao.deletaGTV - " + e.getMessage() + "\r\n"
                    + "delete from GTV "
                    + " where "
                    + " CodFil=" + CodFil
                    + " and Guia=" + guia
                    + " and Serie=" + serie);
        }
    }

    /**
     * Busca GTV com os dados passados
     *
     * @param Guia - Número da Guia
     * @param Serie - Série da Guia
     * @param CodFil - Código da Filial
     * @param persistencia - Conexão com banco de dados
     * @return - Dados retornados OS e Situacao
     * @throws Exception
     */
    public List<GTV> getGTV(String Guia, String Serie, String CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "select OS,Situacao from GTV where CodFil = ? and Guia = ? and Serie = ?";
            GTV gtv;
            List<GTV> lgtv = new ArrayList();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setString(Guia);
            consult.setString(Serie);
            consult.select();
            while (consult.Proximo()) {
                gtv = new GTV();
                gtv.setOS(consult.getString("OS"));
                gtv.setSituacao(consult.getString("Situacao"));
                gtv.setCodFil(CodFil);
                gtv.setGuia(Guia);
                gtv.setSerie(Serie);
                lgtv.add(gtv);
            }
            consult.Close();
            return lgtv;
        } catch (Exception e) {
            throw new Exception("GTVDao.getGTV - " + e.getMessage() + "\r\n"
                    + "select OS,Situacao from GTV where CodFil = " + CodFil + " and Guia = " + Guia + " and Serie = " + Serie);
        }
    }

    /**
     * Realiza a geração da guia
     *
     * @param serie serie que deseja consultar
     * @param persistencia Conexão com o banco de dados
     * @param RPV Numero da rpv
     * @param codfil Codigo da filial
     * @return guia
     * @throws Exception
     */
    public BigDecimal gerarGuia(String serie, String RPV, String codfil, Persistencia persistencia) throws Exception {
        BigDecimal guia = new BigDecimal("0");        
        String sqlI ="";
        String sqlS ="";
        try {
            sqlI = "INSERT INTO gtv (codfil, guia, serie, pedido, situacao) VALUES(?,"
                    + "CONVERT(bigint, (SELECT COALESCE((SELECT ISNULL(MAX(Guia) + 1, 4000001) guia FROM GTV WHERE Serie = ? AND CONVERT(bigint, Guia) BETWEEN 4000001 AND 6000000), \n"                    
                    +  "                (SELECT ISNULL(MAX(Guia) + 1, 1) guia FROM GTV WHERE Serie = ? AND CONVERT(bigint, Guia) < 99999999)))), ?, ?,'0')";

            sqlS = "SELECT Guia FROM GTV WHERE CodFil = ? and Pedido = ?";

            //Executando insert
            Consulta consulta = new Consulta(sqlI, persistencia);
            consulta.setString(codfil);
            consulta.setString(serie);
            consulta.setString(serie);
            consulta.setString(serie);
            consulta.setString(RPV);
            consulta.insert();

            //Consulta guia
            consulta = new Consulta(sqlS, persistencia);
            consulta.setString(codfil);
            consulta.setString(RPV);
            consulta.select();

            guia = null;
            while (consulta.Proximo()) {
                guia = consulta.getBigDecimal("guia");
            }

            if (guia == null) {
                guia = new BigDecimal("0");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("GTVDao.gerarGuia 1 - " + e.getMessage()+"-SQL1:"+sqlI+"-sql1:"+sqlS);
        }
        return guia;
    }

    /**
     * Realiza a geração da guia
     *
     * @param serie serie que deseja consultar
     * @param persistencia Conexão com o banco de dados
     * @param RPV Numero da rpv
     * @param OS
     * @param codfil Codigo da filial
     * @return guia
     * @throws Exception
     */
    public BigDecimal gerarGuia(String serie, String RPV, String codfil, String OS, String SeqRota, String Parada, Persistencia persistencia) throws Exception {
        BigDecimal guia = new BigDecimal("0");
        String sqlI ="";
        String sqlS ="";
        try {
            sqlI = "INSERT INTO gtv (codfil, guia, serie, pedido, situacao, OS, SeqRota, Parada) VALUES(?,"
                    + "CONVERT(bigint, (SELECT ISNULL(MAX(Guia) + 1, 1) guia FROM GTV WHERE Serie = ? AND Guia < 99999999)), ?, ?,'0', ?, ?, ?)";

            sqlS = "SELECT Guia FROM GTV WHERE CodFil = ? and Pedido = ?";

            //Executando insert
            Consulta consulta = new Consulta(sqlI, persistencia);
            consulta.setString(codfil);
            consulta.setString(serie);
            consulta.setString(serie);
            consulta.setString(RPV);
            consulta.setString(OS);
            consulta.setString(SeqRota);
            consulta.setString(Parada);
            consulta.insert();

            //Consulta guia
            consulta = new Consulta(sqlS, persistencia);
            consulta.setString(codfil);
            consulta.setString(RPV);
            consulta.select();

            guia = null;
            while (consulta.Proximo()) {
                guia = consulta.getBigDecimal("guia");
            }

            if (guia == null) {
                guia = new BigDecimal("0");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("GTVDao.gerarGuia 2 - " + e.getMessage()+"-SQL1:"+sqlI+"-sql1:"+sqlS);
        }
        return guia;
    }

    public void updateGuiaSerie(
            String codFil,
            String guia,
            String serie,
            String OS,
            Persistencia persistencia
    ) throws Exception {
        String sql = "UPDATE GTV SET\n"
                + "    Situacao = '4',\n"
                + "    OS = ? \n"
                + "WHERE CodFil = ? \n"
                + "    AND Guia   = ? \n"
                + "    AND Serie  = ? \n"
                + "    AND Situacao < '4';";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(codFil);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.update();
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public void updateOSDeGTV(
            String codFil,
            String guia,
            String serie,
            String OS,
            Persistencia persistencia
    ) throws Exception {
        String sql = "UPDATE GTV\n"
                + "SET OS = ? \n"
                + "WHERE GTV.Guia = ? \n"
                + "  AND GTV.Serie = ? \n"
                + "  AND GTV.CodFil = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);

            consulta.update();
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public List<GTVPedidoOSClienteTesSaida> getGuiasByPedido(
            String codFil,
            String pedidoNumero,
            Persistencia persistencia
    ) throws Exception {
        String sql = "SELECT Clientes.NRed AS Origem,\n"
                + "               GTV.Guia,\n"
                + "               GTV.Serie,\n"
                + "               Tes.TipoMov,\n"
                + "               PEdido.Valor AS TotalGeral,\n"
                + "               (SELECT ISNULL(SUM(PedidoDN.Qtde * PedidoDN.Codigo), 0)\n"
                + "                FROM PedidoDN\n"
                + "                WHERE PedidoDN.CodFil = OS_Vig.CodFil\n"
                + "                  AND PedidoDN.Numero = Pedido.Numero) AS TotalDN,\n"
                + "               (SELECT ISNULL(SUM(PedidoMD.Qtde * PedidoMD.Codigo / 100), 0)\n"
                + "                FROM PedidoMD\n"
                + "                WHERE PedidoMD.CodFil = OS_Vig.CodFil\n"
                + "                  AND PedidoMD.Numero = Pedido.Numero) AS TotalMoeda,\n"
                + "               Pedido.ChequesQtde,\n"
                + "               PEdido.ChequesValor,\n"
                + "               PEdido.TicketsQtde,\n"
                + "               PEdido.TicketsValor,\n"
                + "               Tes.KitTrocoQtde,\n"
                + "               Tes.Diferenca,\n"
                + "               Tes.TotalDD,\n"
                + "               Tes.ContaTes,\n"
                + "               Tes.TipoSrv,\n"
                + "               OS_Vig.CodSrv,\n"
                + "               PEdido.Obs,\n"
                + "               PEdido.Situacao,\n"
                + "               GTV.Pedido,\n"
                + "               Tes.MatrConf,\n"
                + "               Tes.HrInicio,\n"
                + "               Tes.HrFinal,\n"
                + "               Tes.Camera,\n"
                + "               Tes.Baixa,\n"
                + "               Tes.Retorno,\n"
                + "               Pedido.Data AS Dt_Pedido,\n"
                + "               Tes.Operador,\n"
                + "               Tes.Dt_alter,\n"
                + "               Tes.Hr_Alter\n"
                + "FROM GTV\n"
                + "         LEFT JOIN OS_Vig\n"
                + "                   ON OS_Vig.OS = GTV.OS\n"
                + "         LEFT JOIN Pedido\n"
                + "                   ON GTV.Pedido = Pedido.Numero\n"
                + "                       AND Pedido.CodFil = GTV.CodFil\n"
                + "         LEFT JOIN Clientes\n"
                + "                   ON OS_Vig.CliDst = Clientes.Codigo\n"
                + "                       AND Clientes.CodFil = OS_Vig.CodFil\n"
                + "         LEFT JOIN TEsSaidas Tes\n"
                + "                   ON GTV.CodFil = Tes.CodFil\n"
                + "                       AND GTV.Guia = Tes.Guia\n"
                + "                       AND GTV.Serie = Tes.Serie\n"
                + "WHERE OS_Vig.CodFil = ? \n"
                + "  AND Pedido.Numero = ? \n"
                + "ORDER BY Pedido, OS_Vig.OS";

        List<GTVPedidoOSClienteTesSaida> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(pedidoNumero);

            consulta.select();
            while (consulta.Proximo()) {
                GTV gtv = new GTV();
                OS_Vig osvig = new OS_Vig();
                Pedido pedido = new Pedido();
                Clientes cliente = new Clientes();
                TesSaidas tesSaida = new TesSaidas();
                GTVPedidoOSClienteTesSaida dto = new GTVPedidoOSClienteTesSaida(gtv, pedido, osvig, cliente, tesSaida);

                cliente.setNRed(consulta.getString("Origem"));
                gtv.setGuia(consulta.getString("Guia"));
                tesSaida.setTipoMov(consulta.getString("TipoMov"));
                gtv.setSerie(consulta.getString("Serie"));
                dto.setTotalGeral(consulta.getBigDecimal("TotalGeral"));
                dto.setTotalDN(consulta.getBigDecimal("TotalDN"));
                dto.setTotalMoeda(consulta.getBigDecimal("TotalMoeda"));
                pedido.setChequesQtde(consulta.getInt("ChequesQtde"));
                pedido.setChequesValor(consulta.getString("ChequesValor"));
                pedido.setTicketsQtde(consulta.getString("TicketsQtde"));
                pedido.setTicketsValor(consulta.getString("TicketsValor"));
                tesSaida.setKitTrocoQtde(consulta.getString("KitTrocoQtde"));
                tesSaida.setDiferenca(consulta.getString("Diferenca"));
                tesSaida.setTotalDD(consulta.getString("TotalDD"));
                tesSaida.setContaTes(consulta.getString("ContaTes"));
                tesSaida.setTipoSrv(consulta.getString("TipoSrv"));
                osvig.setCodSrv(consulta.getString("CodSrv"));
                pedido.setObs(consulta.getString("Obs"));
                pedido.setSituacao(consulta.getString("Situacao"));
                pedido.setPedidoCliente(consulta.getString("Pedido"));
                tesSaida.setMatrConf(consulta.getString("MatrConf"));
                tesSaida.setHrInicio(consulta.getString("HrInicio"));
                tesSaida.setHrFinal(consulta.getString("HrFinal"));
                tesSaida.setCamera(consulta.getInt("Camera"));
                tesSaida.setBaixa(consulta.getString("Baixa"));
                cliente.setRetorno(consulta.getString("Retorno"));
                dto.setDataPedido(consulta.getLocalDate("Dt_Pedido"));
                gtv.setOperador(consulta.getString("Operador"));
                gtv.setDt_alter(consulta.getString("Dt_alter"));
                gtv.setHr_Alter(consulta.getString("Hr_Alter"));

                lista.add(dto);
            }

            return lista;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public int getQuantityGTVInInterval(
            String serie,
            int guia,
            int size,
            Persistencia persistencia
    ) throws Exception {
        String sql = "SELECT Count(*) Qtd\n"
                + "FROM GTV\n"
                + "WHERE Guia >= (? + 1) \n"
                + "  AND Guia <= ? \n"
                + "  AND Serie = ? ";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setInt(guia);
            consulta.setInt(guia + size);
            consulta.setString(serie);

            consulta.select();
            if (consulta.Proximo()) {
                return consulta.getInt("Qtd");
            }

            return 0;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

}

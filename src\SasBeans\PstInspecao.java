/*
 * To change this license header+";"+ choose License Headers in Project Properties.
 * To change this template file+";"+ choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class PstInspecao {

    private String Codigo;
    private String CodInspecao;
    private String Secao;
    private String Codfil;
    private String Data;
    private String Sequencia;
    private String Pergunta;
    private String Resposta;
    private String CaminhoImagem;
    private String CaminhoVideo;
    private String CaminhoAudio;
    private String Matr;
    private String Veiculo;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Latitude;
    private String Longitude;
    private String CodOperador;

    private String Local;
    private String Inspetor;

    private String ChavePrimaria;

    public String getCodInspecao() {
        return CodInspecao;
    }

    public void setCodInspecao(String CodInspecao) {
        this.CodInspecao = CodInspecao;
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getCodfil() {
        return Codfil;
    }

    public void setCodfil(String Codfil) {
        this.Codfil = Codfil;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getPergunta() {
        return Pergunta;
    }

    public void setPergunta(String Pergunta) {
        this.Pergunta = Pergunta;
    }

    public String getResposta() {
        return Resposta;
    }

    public void setResposta(String Resposta) {
        this.Resposta = Resposta;
    }

    public String getCaminhoImagem() {
        return CaminhoImagem;
    }

    public void setCaminhoImagem(String CaminhoImagem) {
        this.CaminhoImagem = CaminhoImagem;
    }

    public String getCaminhoVideo() {
        return CaminhoVideo;
    }

    public void setCaminhoVideo(String CaminhoVideo) {
        this.CaminhoVideo = CaminhoVideo;
    }

    public String getCaminhoAudio() {
        return CaminhoAudio;
    }

    public void setCaminhoAudio(String CaminhoAudio) {
        this.CaminhoAudio = CaminhoAudio;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }

    public String getVeiculo() {
        return Veiculo;
    }

    public void setVeiculo(String Veiculo) {
        this.Veiculo = Veiculo;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getLocal() {
        return Local;
    }

    public void setLocal(String Local) {
        this.Local = Local;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getCodOperador() {
        return CodOperador;
    }

    public void setCodOperador(String CodOperador) {
        this.CodOperador = CodOperador;
    }

    public String getInspetor() {
        return Inspetor;
    }

    public void setInspetor(String Inspetor) {
        this.Inspetor = Inspetor;
    }

    public String getChavePrimaria() {
        return Codigo + ";" + Secao + ";" + Codfil + ";" + Data + ";" + Matr + ";" + Pergunta + ";";
    }
}

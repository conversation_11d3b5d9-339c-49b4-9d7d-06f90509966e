<!DOCTYPE html>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<html>
    <head>
        <title>G<PERSON>a</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
        <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
        <script src="../assets/scripts/jquery.qrcode.js" type="text/javascript"></script>
        <script src="../assets/scripts/qrcode.js" type="text/javascript"></script>
        <style>

        </style>
    </head>
    <body style="margin: 0px; padding: 4px; width: 100%; height: 100%; font-family: Arial; font-size: 11pt">
        <label id="load" style="color: #999; font-size: 16pt; width: 100%; text-align: center; margin-top: 50px;"><i class="far fa-refresh"></i>&nbsp;&nbsp;Aguarde, Carregando Guia ...</label>
        <div id="divGuia" style="display: none; width: calc(100% - 20px); height: 100%; border: thin solid #CCC; padding: 10px;">
            <table style="width: 100%; border-spacing: 0px">
                <tr>
                    <td style="vertical-align: middle; text-align: center; width: 150px;">
                        <img id="imgEmpresa" style="max-height: 80px; margin-bottom: 10px;"/>
                    </td>
                    <td style="vertical-align: middle; text-align: center; width: 50%;">
                        <b><span ref="TopoEmitNome"></span></b><br>
                        <span ref="TopoEmitEnd"></span>, <span ref="TopoEmitBairro"></span><br>
                        <span ref="TopoEmitCidade"></span>/<span ref="TopoEmitUF"></span> - <span ref="TopoEmitCEP"></span><br>
                        CNPJ:  <span ref="TopoEmitCNPJ"></span>  Telefone:  <span ref="TopoEmitTelefone"></span>
                    </td>
                    <td style="vertical-align: middle; text-align: center;">
                        <b>GUIA ELETRÔNICA DE TRANSPORTE DE VALORES</b>
                    </td>
                </tr>
            </table>

            <table style="width: 100%; border: none; border-spacing: 0px; margin-top: 8px;">
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <b>Cliente</b>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <span ref="ClienteNome"></span><br>
                        Endereço:  <span ref="ClienteEndereco"></span><br>
                        <span ref="ClienteBairro"></span>, <span ref="ClienteCidade"></span>/<span ref="ClienteCEP"></span><br>
                        CNPJ:  <span ref="ClienteCNPJ"></span>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <b>Dados da Origem</b>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: center; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        Remetente: <span ref="OrigemRemetente"></span><br>
                        Endereço: <span ref="OrigemEndereco"></span><br>
                        <span ref="OrigemBairro"></span> , <span ref="OrigemCidade"></span>/<span ref="OrigemUF"></span>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: center; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <table style="width: 100%">
                            <tr>
                                <td style="width: 33%; text-align: center">Data:  <span ref="OrigemData"></span></td>
                                <td style="width: 33%; text-align: center">Veículo:  <span ref="OrigemVeiculo"></span></td>
                                <td style="width: 33%; text-align: center">Hora: <span ref="OrigemHora"></span></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <b>Dados da Destino</b>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: center; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        Destinatário <span ref="Destinatario"></span><br>
                        Endereço: <span ref="DestinatarioEndereco"></span><br>
                        <span ref="DestinatarioBairro"></span> , <span ref="DestinatarioCidade"></span>/<span ref="DestinatarioUF"></span>
                    </td>
                </tr>

                <tr>
                    <td style="vertical-align: middle; text-align: center; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <table style="width: 100%">
                            <tr>
                                <td style="width: 33%; text-align: center">Data:  <span ref="DestinoData"></span></td>
                                <td style="width: 33%; text-align: center">Veículo:  <span ref="DestinoVeiculo"></span></td>
                                <td style="width: 33%; text-align: center">Hora: <span ref="DestinoHora"></span></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <b>Discriminação, valor e identificação da carga</b>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        Valor Declarado:  R$ <span ref="valorDeclarado"></span><br>
                        (<span ref="valorDeclaradoExtenso"></span> / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / / /)
                    </td>
                </tr>
                <!--<tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <b>Identificação de Malote</b>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: left; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        Moedas:<br>
                        <b><span ref="moedas"></span></b>
                    </td>
                </tr>-->
                <tr>
                    <td style="vertical-align: middle; text-align: center; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        Recebemos os volumes citados nessa Guia, declaramos e reconhecemos como entreges sem vestígio de violação, em perfeitas condições, e em especial intactos os respectivos selos de segurança - lacres numerados descritos
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: center; border-top: thin solid #000;border-left: thin solid #000;border-right: thin solid #000; padding: 5px;">
                        <table style="width: 100%">
                            <tr>
                                <td style="width: 50%; text-align: center; height: 130px;vertical-align: top; text-align: center;">Ass Remetente:<br>
                                    <img id="imgAssRemet" style="max-height: 70px; margin-top: 10px;"/>
                                    <label id="lblAssRemet" style="display: block; width: 100%; text-align: center; margin-top: 15px; font-weight: bold;"></label>
                                </td>
                                <td style="width: 50%; text-align: center; border-left: thin solid #000; height: 130px;vertical-align: top; text-align: center;">Ass Destinatário:<br>
                                    <img id="imgAssDest" style="max-height: 70px; margin-top: 10px;"/>
                                    <label id="lblAssDest" style="display: block; width: 100%; text-align: center; margin-top: 15px; font-weight: bold;"></label>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: center; border: thin solid #000; padding: 5px;">
                        <b>Autenticação</b><br>
                        <label style="width: 100%; text-align:center; font-weight: 500;"><span ref="chCTE"></span></label>
                    </td>
                </tr>
                <tr>
                    <td style="vertical-align: middle; text-align: center; border: thin solid #000; padding: 5px;">
                        <b>Acessar CTe</b><br>
                <center><label style="text-align:center; font-weight: bold; margin-left : 40px" id="qrCode"></label></center>
                </td>
                </tr>
            </table>
        </div>
        <script>
            $(document).ready(function () {
                CarregarDados();
            });

            function CarregarDados() {
                
                if (ObterParamURL('empresa') && ObterParamURL('guia') && ObterParamURL('serie')) {
                    $.ajax({
                        url: 'https://mobile.sasw.com.br/SatWebService/api/ws-importacao/gtve/consulta/' + ObterParamURL('empresa') + '/' + ObterParamURL('guia') + '/' + ObterParamURL('serie'),
                        method: 'POST'
                    })
                            .done(function (response) {
                                $('#imgEmpresa').attr('src', 'https://mobile.sasw.com.br:9091/satmobile/logos/logo_' + ObterParamURL('empresa') + '.png');
                                
                                let AssinaDest = response.AssinaDestinatario;
                                let AssinaDestImagem = response.AssinaDestinatarioImagem;
                                let AssinaRemet = response.AssinaRemetente;
                                let AssinaRemetImagem = response.AssinaRemetenteImagem;
                                
                                if(null != AssinaDestImagem && AssinaDestImagem != ''){
                                    $('#imgAssDest').attr('src', AssinaDestImagem);
                                }
                                
                                if(null != AssinaDest && AssinaDest != ''){
                                    $('#lblAssDest').text(AssinaDest);
                                }
                                
                                if(null != AssinaRemetImagem && AssinaRemetImagem != ''){
                                    $('#imgAssRemet').attr('src', AssinaRemetImagem);
                                }
                                
                                if(null != AssinaRemet && AssinaRemet != ''){
                                    $('#lblAssRemet').text(AssinaRemet);
                                }
                                
                                let xmlEnvio = ReplaceAll(response.xml_Envio, '\n', '');
                                let xmlRetorno = ReplaceAll(response.xml_Retorno, '\n', '');

                                $('[ref="TopoEmitNome"]').text($(xmlEnvio).find('xNome').first().text());
                                $('[ref="TopoEmitEnd"]').text($(xmlEnvio).find('enderEmit').first().find('xLgr').text());
                                $('[ref="TopoEmitBairro"]').text($(xmlEnvio).find('enderEmit').first().find('xBairro').text());
                                $('[ref="TopoEmitCidade"]').text($(xmlEnvio).find('enderEmit').first().find('xMun').text());
                                $('[ref="TopoEmitUF"]').text($(xmlEnvio).find('enderEmit').first().find('UF').text());
                                $('[ref="TopoEmitCEP"]').text($(xmlEnvio).find('enderEmit').first().find('CEP').text());
                                $('[ref="TopoEmitCNPJ"]').text(FormatarCNPJ($(xmlEnvio).find('emit').first().find('CNPJ').text()));
                                $('[ref="TopoEmitTelefone"]').text(FormatarTelefone($(xmlEnvio).find('fone').first().text()));

                                $('[ref="ClienteNome"]').text($(xmlEnvio).find('rem').first().find('xnome').text());
                                $('[ref="ClienteEndereco"]').text($(xmlEnvio).find('enderReme').first().find('xLgr').text());
                                $('[ref="ClienteBairro"]').text($(xmlEnvio).find('enderReme').first().find('xBairro').text());
                                $('[ref="ClienteCidade"]').text($(xmlEnvio).find('enderReme').first().find('xMun').text());
                                $('[ref="ClienteUF"]').text($(xmlEnvio).find('enderReme').first().find('UF').text());
                                $('[ref="ClienteCEP"]').text($(xmlEnvio).find('enderReme').first().find('CEP').text());
                                $('[ref="ClienteCNPJ"]').text(FormatarCNPJ($(xmlEnvio).find('rem').first().find('CNPJ').text()));

                                $('[ref="OrigemRemetente"]').text($(xmlEnvio).find('rem').first().find('xnome').text());
                                $('[ref="OrigemEndereco"]').text($(xmlEnvio).find('enderReme').first().find('xLgr').text());
                                $('[ref="OrigemBairro"]').text($(xmlEnvio).find('enderReme').first().find('xBairro').text());
                                $('[ref="OrigemCidade"]').text($(xmlEnvio).find('enderReme').first().find('xMun').text());
                                $('[ref="OrigemUF"]').text($(xmlEnvio).find('enderReme').first().find('UF').text());

                                $('[ref="Destinatario"]').text($(xmlEnvio).find('dest').first().find('xnome').text());
                                $('[ref="DestinatarioEndereco"]').text($(xmlEnvio).find('enderDest').first().find('xLgr').text());
                                $('[ref="DestinatarioBairro"]').text($(xmlEnvio).find('enderDest').first().find('xBairro').text());
                                $('[ref="DestinatarioCidade"]').text($(xmlEnvio).find('enderDest').first().find('xMun').text());
                                $('[ref="DestinatarioUF"]').text($(xmlEnvio).find('enderDest').first().find('UF').text());

                                let DataSplit = $(xmlEnvio).find('dhSaidaOrig').first().text().split('T')[0].split('-');

                                $('[ref="OrigemData"]').text(DataSplit[2] + '/' + DataSplit[1] + '/' + DataSplit[0]);
                                $('[ref="OrigemVeiculo"]').text($(xmlEnvio).find('placa').first().text() + ' / ' + $(xmlEnvio).find('detGTV').first().find('UF').text());
                                $('[ref="OrigemHora"]').text($(xmlEnvio).find('dhSaidaOrig').first().text().split('T')[1].split('-')[0]);

                                let DataSplitDest = $(xmlEnvio).find('dhChegadaDest').first().text().split('T')[0].split('-');
                                $('[ref="DestinoData"]').text(DataSplitDest[2] + '/' + DataSplitDest[1] + '/' + DataSplitDest[0]);
                                $('[ref="DestinoVeiculo"]').text($(xmlEnvio).find('placa').first().text() + ' / ' + $(xmlEnvio).find('detGTV').first().find('UF').text());
                                $('[ref="DestinoHora"]').text($(xmlEnvio).find('dhChegadaDest').first().text().split('T')[1].split('-')[0]);

                                let ArrayMoedas = new Array();

                                $(xmlEnvio).find('xMoedaEstr').each(function () {
                                    if (!ArrayMoedas.includes($(this).text()))
                                        ArrayMoedas.push($(this).text());
                                });

                                ArrayMoedas.sort();

                                for (i = 0; i < ArrayMoedas.length; i++) {
                                    if ($('[ref="moedas"]').text() !== '')
                                        $('[ref="moedas"]').text($('[ref="moedas"]').text() + ', ' + ArrayMoedas[i]);
                                    else
                                        $('[ref="moedas"]').text(ArrayMoedas[i]);
                                }

                                let ValorDeclarado = 0;

                                $(xmlEnvio).find('vEspecie').each(function () {
                                    ValorDeclarado += parseFloat($(this).text());
                                });


                                $('[ref="valorDeclaradoExtenso"]').text(ValorDeclarado.toString().split('.')[0].extenso().toUpperCase() + ' REAIS');

                                let centavos = '';
                                try{
                                    centavos = ' E ' + ValorDeclarado.toString().split('.')[1].extenso().toUpperCase() + ' CENTAVOS'
                                }
                                catch(e){
                                    
                                }

                                $('[ref="valorDeclaradoExtenso"]').text($('[ref="valorDeclaradoExtenso"]').text() + centavos);
                                
                                ValorDeclarado = ValorDeclarado.formatMoney(2, "", ".", ",").toString();

                                $('[ref="valorDeclarado"]').text(ValorDeclarado);
                                $('[ref="chCTE"]').text($(xmlRetorno).find('chCTe').first().text());

                                $('#load').css('display', 'none');
                                $('#divGuia').css('display', '');

                                let CodigoPt = $(xmlRetorno).find('chCTe').first().text();
                                let tpAmb = $(xmlRetorno).find('tpAmb').first().text();

                                $('#qrCode').append('<center><div style="margin-top: 15px; width: 198px !important; height: 198px !important; text-align: center !important; margin-right: 15px;" tipo="qrCode"><div id="qrCode_1"></div></center>');

                                let IdObj = '#qrCode_1';
                                let linkQr = "https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + CodigoPt + "&tpAmb=" + tpAmb;
                                
                                jQuery(IdObj).qrcode({
                                    text: linkQr,
                                    width: 170,
                                    height: 170
                                });
                            })
                            .fail(function () {
                                $('#divGuia, #load').css('display', 'none');
                                $.MsgBoxVermelhoOk('Aviso', 'Erro ao consultar Guia!', function () {
                                    location.reload();
                                });
                            });
                } else {
                    $('#load').html('Informe na URL os parametros obrigatórios');
                }
            }
        </script>
    </body>
</html>

package br.com.sasw.pacotesuteis.utilidades;

import BeansAuxiliares.Anexo;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import org.apache.commons.mail.EmailAttachment;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;
import org.apache.commons.mail.MultiPartEmail;
import org.apache.commons.mail.SimpleEmail;

/**
 *
 * <AUTHOR>
 */
public class EnvioEmailSPM {

    /**
     * Envia um email simples (somente texto) Necessario incluir as biblioteca
     * do commons-mail e do javamail
     *
     * @param smtp - url do servidor de smtp
     * @param dest_email - email do destinatario
     * @param dest_nome - nome do destinatario
     * @param remet_email - email do remetente
     * @param remet_nome - nome do remetente
     * @param assunto - assunto do email
     * @param mensagem - corpo do email
     * @param aut_login - login de autenticacao (normalmente proprio email)
     * @param aut_senha - senha de autenticacao (normalmente senha do email)
     * @param porta - porta do servidor de smtp
     * @throws EmailException - erro gerado
     */
    public static void enviaEmailSimples(String smtp, String dest_email, String dest_nome,
            String remet_email, String remet_nome, String assunto, String mensagem,
            String aut_login, String aut_senha, int porta) throws EmailException {
        try {
            SimpleEmail email;
            email = new SimpleEmail();
            email.setHostName(smtp); // o servidor SMTP para envio do e-mail  
            email.addTo(dest_email, dest_nome); //destinatário  
            email.setFrom(remet_email, remet_nome); // remetente  
            email.setSubject(assunto); // assunto do e-mail  
            email.setMsg(mensagem); //conteudo do e-mail  
            email.setAuthentication(aut_login, aut_senha);
            email.setSmtpPort(porta);
            email.setSSLOnConnect(email.isSSLOnConnect());
            email.setSslSmtpPort(String.valueOf(porta));
            email.setStartTLSEnabled(true);
            email.send();
        } catch (EmailException e) {
            throw new EmailException("Falha ao enviar email para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    /**
     * Envia um email simples (somente texto) Necessario incluir as biblioteca
     * do commons-mail e do javamail
     *
     * @param smtp - url do servidor de smtp
     * @param dest_email - email do destinatario
     * @param remet_email - email do remetente
     * @param remet_nome - nome do remetente
     * @param assunto - assunto do email
     * @param mensagem - corpo do email
     * @param aut_login - login de autenticacao (normalmente proprio email)
     * @param aut_senha - senha de autenticacao (normalmente senha do email)
     * @param porta - porta do servidor de smtp
     * @throws EmailException - erro gerado
     */
    public static void enviaEmailSimples(String smtp, String dest_email,
            String remet_email, String remet_nome, String assunto, String mensagem,
            String aut_login, String aut_senha, int porta) throws EmailException {
        StringTokenizer destinatario = new StringTokenizer(dest_email, ";");
        try {
            SimpleEmail email;
            email = new SimpleEmail();
            email.setHostName(smtp); // o servidor SMTP para envio do e-mail  
            while (destinatario.hasMoreTokens()) {
                email.addTo(destinatario.nextToken()); //destinatário  
            }
            email.setFrom(remet_email, remet_nome); // remetente  
            email.setSubject(assunto); // assunto do e-mail  
            email.setMsg(mensagem); //conteudo do e-mail  
            email.setAuthentication(aut_login, aut_senha);
            email.setSmtpPort(porta);
            //email.setSSLOnConnect(email.isSSLOnConnect());
            email.setSSLOnConnect(false);
            email.setSslSmtpPort(String.valueOf(porta));
            email.setStartTLSEnabled(true);
            email.send();
        } catch (EmailException e) {
            throw new EmailException("Falha ao enviar email para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    /**
     * Envio de email com arquivos em anexo Necessario incluir as biblioteca do
     * commons-mail e do javamail
     *
     * @param smtp - url do servidor de smtp
     * @param dest_email - email do destinatario
     * @param remet_email - email do remetente
     * @param remet_nome - nome do remetente
     * @param assunto - assunto do email
     * @param mensagem - corpo do email
     * @param aut_login - login de autenticacao (normalmente proprio email)
     * @param aut_senha - senha de autenticacao (normalmente senha do email)
     * @param porta - porta do servidor de smtp
     * @param anexos - lista com os dados dos arquivo que serão anexados
     * @throws EmailException
     */
    public static void enviaEmailComAnexo(String smtp, String dest_email,
            String remet_email, String remet_nome, String assunto, String mensagem,
            String aut_login, String aut_senha, int porta, List<Anexo> anexos) throws Exception {
        try {
            List<EmailAttachment> Anexos = new ArrayList();
            EmailAttachment anexo1;
            for (Anexo ax : anexos) {
                // cria o anexo 1.  
                anexo1 = new EmailAttachment();
                anexo1.setPath(ax.getEndereco_Anexo()); //caminho do arquivo (RAIZ_PROJETO/teste/teste.txt)  
                anexo1.setDisposition(EmailAttachment.ATTACHMENT);
                anexo1.setDescription(ax.getDescricao_Anexo());
                anexo1.setName(ax.getNome_Anexo());
                Anexos.add(anexo1);
            }

            // configura o email  
            MultiPartEmail email = new MultiPartEmail();
            email.setHostName(smtp); // o servidor SMTP para envio do e-mail  
            email.addTo(dest_email, ""); //destinatário  
            email.setFrom(remet_email, remet_nome); // remetente  
            email.setSubject(assunto); // assunto do e-mail  
            email.setMsg(mensagem); //conteudo do e-mail  
            email.setAuthentication(aut_login, aut_senha);
            email.setStartTLSEnabled(true);
            //email.setStartTLSEnabled(false);
            email.setSSLOnConnect(false);
            email.setSmtpPort(porta);
            //email.setSSL(true);  
            //email.setTLS(true);  

            // adiciona arquivo(s) anexo(s)  
            for (EmailAttachment ea : Anexos) {
                email.attach(ea);
            }
            // envia o email  
            email.send();
        } catch (Exception e) {
            throw new EmailException("Falha ao enviar email com anexo para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    public static void enviaEmailComAnexo(String smtp, String dest_email, String[] bcc,
            String remet_email, String remet_nome, String assunto, String mensagem,
            String aut_login, String aut_senha, int porta, List<Anexo> anexos) throws Exception {
        try {
            List<EmailAttachment> Anexos = new ArrayList();
            EmailAttachment anexo1;
            for (Anexo ax : anexos) {
                // cria o anexo 1.  
                anexo1 = new EmailAttachment();
                anexo1.setPath(ax.getEndereco_Anexo()); //caminho do arquivo (RAIZ_PROJETO/teste/teste.txt)  
                anexo1.setDisposition(EmailAttachment.ATTACHMENT);
                anexo1.setDescription(ax.getDescricao_Anexo());
                anexo1.setName(ax.getNome_Anexo());
                Anexos.add(anexo1);
            }

            // configura o email  
            MultiPartEmail email = new MultiPartEmail();
            email.setHostName(smtp); // o servidor SMTP para envio do e-mail  
            email.addTo(dest_email); //destinatário  
            email.addBcc(bcc); //cópias ocultas  
            email.setFrom(remet_email, remet_nome); // remetente  
            email.setSubject(assunto); // assunto do e-mail  
            email.setMsg(mensagem); //conteudo do e-mail  
            email.setAuthentication(aut_login, aut_senha);
            email.setStartTLSEnabled(true);
            //email.setStartTLSEnabled(false);
            email.setSSLOnConnect(false);
            email.setSmtpPort(porta);
            //email.setSSL(true);  
            //email.setTLS(true);  

            // adiciona arquivo(s) anexo(s)  
            for (EmailAttachment ea : Anexos) {
                email.attach(ea);
            }
            // envia o email  
            email.send();
        } catch (Exception e) {
            throw new EmailException("Falha ao enviar email com anexo para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    /**
     * Envio de email em formato html com arquivos em anexo Necessario incluir
     * as biblioteca do commons-mail e do javamail
     *
     * @param smtp - url do servidor de smtp
     * @param dest_email - email do destinatario
     * @param remet_email - email do remetente
     * @param remet_nome - nome do remetente
     * @param assunto - assunto do email
     * @param mensagem - corpo do email
     * @param aut_login - login de autenticacao (normalmente proprio email)
     * @param aut_senha - senha de autenticacao (normalmente senha do email)
     * @param porta - porta do servidor de smtp
     * @param anexos - lista com os dados dos arquivo que serão anexados
     * @throws EmailException
     */
    public static void enviaEmailFormatoHtml(String smtp, String dest_email,
            String remet_email, String remet_nome, String assunto, String mensagem,
            String aut_login, String aut_senha, int porta, List<Anexo> anexos) throws Exception {
        try {
            List<EmailAttachment> Anexos = new ArrayList();
            EmailAttachment anexo1;
            for (Anexo ax : anexos) {
                // cria o anexo 1.  
                anexo1 = new EmailAttachment();
                anexo1.setPath(ax.getEndereco_Anexo()); //caminho do arquivo (RAIZ_PROJETO/teste/teste.txt)  
                anexo1.setDisposition(EmailAttachment.ATTACHMENT);
                anexo1.setDescription(ax.getDescricao_Anexo());
                anexo1.setName(ax.getNome_Anexo());
                Anexos.add(anexo1);
            }
            // Create the email message
            HtmlEmail email = new HtmlEmail();
            email.setHostName(smtp);
            email.addTo(dest_email, "");
            email.setFrom(remet_email, remet_nome);
            email.setSubject(assunto);
            email.setAuthentication(aut_login, aut_senha);

            email.setStartTLSEnabled(true);
            //email.setStartTLSEnabled(false);
            email.setSSLOnConnect(false);
            email.setSmtpPort(porta);

            // embed the image and get the content id
            //URL url = new URL("http://www.apache.org/images/asf_logo_wide.gif");
            //String cid = email.embed(url, "Apache logo");
            // set the html message
            //email.setHtmlMsg("<html>The apache logo - <img src=\"cid:"+cid+"\"></html>");
            email.setHtmlMsg(mensagem);

            // set the alternative message
            email.setTextMsg("Seu e-mail não aceita HTML, click no link "
                    + "https://mobile.sasw.com.br:9091/satmobile/html-link/tecnoseg.html para ver o conteúdo");

            // adiciona arquivo(s) anexo(s)  
            for (EmailAttachment ea : Anexos) {
                email.attach(ea);
            }

            // send the email
            email.send();
        } catch (EmailException e) {
            throw new EmailException("Falha ao enviar email html para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    public static void enviaEmailFormatoHtml(String smtp, String dest_email, String[] bcc,
            String remet_email, String remet_nome, String assunto, String mensagem,
            String aut_login, String aut_senha, int porta, List<Anexo> anexos) throws Exception {
        try {
            List<EmailAttachment> Anexos = new ArrayList();
            EmailAttachment anexo1;
            for (Anexo ax : anexos) {
                // cria o anexo 1.  
                anexo1 = new EmailAttachment();
                anexo1.setPath(ax.getEndereco_Anexo()); //caminho do arquivo (RAIZ_PROJETO/teste/teste.txt)  
                anexo1.setDisposition(EmailAttachment.ATTACHMENT);
                anexo1.setDescription(ax.getDescricao_Anexo());
                anexo1.setName(ax.getNome_Anexo());
                Anexos.add(anexo1);
            }
            // Create the email message
            HtmlEmail email = new HtmlEmail();
            email.setHostName(smtp);
//            email.addTo(dest_email);
            email.addBcc(bcc);
            email.setFrom(remet_email, remet_nome);
            email.setSubject(assunto);
            email.setAuthentication(aut_login, aut_senha);

            email.setStartTLSEnabled(true);
            //email.setStartTLSEnabled(false);
            email.setSSLOnConnect(false);
            email.setSmtpPort(porta);

            // embed the image and get the content id
            //URL url = new URL("http://www.apache.org/images/asf_logo_wide.gif");
            //String cid = email.embed(url, "Apache logo");
            // set the html message
            //email.setHtmlMsg("<html>The apache logo - <img src=\"cid:"+cid+"\"></html>");
            email.setHtmlMsg(mensagem);

            // set the alternative message
            email.setTextMsg("Seu e-mail não aceita HTML, click no link "
                    + "https://mobile.sasw.com.br:9091/satmobile/html-link/tecnoseg.html para ver o conteúdo");

            // adiciona arquivo(s) anexo(s)  
            for (EmailAttachment ea : Anexos) {
                email.attach(ea);
            }

            // send the email
            email.send();
        } catch (Exception e) {
            throw new Exception("Falha ao enviar email html para : " + dest_email + "\r\n" + e.getMessage() + "\r\n" + e.getCause());
        }
    }

    /**
     * Envio de email em formato html com arquivos em anexo Necessario incluir
     * as biblioteca do commons-mail e do javamail
     *
     * @param smtp - url do servidor de smtp
     * @param dest_email - email do destinatario
     * @param dest_nome - nome do destinatario
     * @param remet_email - email do remetente
     * @param remet_nome - nome do remetente
     * @param assunto - assunto do email
     * @param mensagem - corpo do email
     * @param mensagemSimples - corpo do email em texto
     * @param aut_login - login de autenticacao (normalmente proprio email)
     * @param aut_senha - senha de autenticacao (normalmente senha do email)
     * @param porta - porta do servidor de smtp
     * @throws EmailException
     */
    public static void enviaEmailFormatoHtmlSemAnexo(String smtp, String dest_email, String dest_nome,
            String remet_email, String remet_nome, String assunto, String mensagem, String mensagemSimples,
            String aut_login, String aut_senha, int porta) throws EmailException {
        try {
            // Create the email message
            HtmlEmail email = new HtmlEmail();
            email.setHostName(smtp);
            email.addTo(dest_email, dest_nome);
            email.setFrom(remet_email, remet_nome);
            email.setSubject(assunto);
            email.setAuthentication(aut_login, aut_senha);

            email.setStartTLSEnabled(true);
            email.setSSLOnConnect(false);
            email.setSmtpPort(porta);

            email.setHtmlMsg(mensagem);

            // set the alternative message
            email.setTextMsg(mensagemSimples);

            // send the email
            email.send();
        } catch (EmailException e) {
            throw new EmailException("Falha ao enviar email html para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    public static void enviaEmailFormatoHtmlSemAnexo(String smtp, String[] dest_email,
            String remet_email, String remet_nome, String assunto, String mensagem, String mensagemSimples,
            String aut_login, String aut_senha, int porta) throws EmailException {
        try {
            // Create the email message
            HtmlEmail email = new HtmlEmail();
            email.setHostName(smtp);
            email.addTo(dest_email);
            email.setFrom(remet_email, remet_nome);
            email.setSubject(assunto);
            email.setAuthentication(aut_login, aut_senha);

            email.setStartTLSEnabled(true);
            email.setSSLOnConnect(false);
            email.setSmtpPort(porta);

            email.setHtmlMsg(mensagem);

            // set the alternative message
            email.setTextMsg(mensagemSimples);

            // send the email
            email.send();
        } catch (EmailException e) {
            throw new EmailException("Falha ao enviar email html para : " + dest_email + "\r\n" + e.getMessage());
        }
    }

    public static String anexo(String empresa, String nome, String extensao) {
        return empresa + (nome.length() > (50 - extensao.length() - empresa.length()) ? nome.substring(0, 50 - extensao.length() - empresa.length()) : nome) + extensao;
    }
}

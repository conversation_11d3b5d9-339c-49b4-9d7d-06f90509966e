/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.SasPWFill;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;

/**
 *
 * <AUTHOR>
 */
public class BaseBeanMB implements Serializable {

    protected Persistencia persistencia;
    protected String caminho;
    protected String operador;
    protected LoginMB login;
    protected ArquivoLog logerro;
    protected String codFil;
    protected Filiais filialTela;
    protected String dataTela;
    private String banco;
    private String codPessoa;
    private RotasSatWeb rotassatweb;
    private Map<String, Object> session;
    private List<SasPWFill> filiais;
    private String log;

    public BaseBeanMB() throws Exception {
        String LOG_BASE_PATH = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\";
        FacesContext fc = FacesContext.getCurrentInstance();
        login = fc.getApplication().evaluateExpressionGet(fc, "#{login}", LoginMB.class);
        session = fc.getExternalContext().getSessionMap();
        codFil = (String) session.get("filial");
        banco = (String) session.get("banco");
        operador = (String) session.get("nome");
        codPessoa = ((BigDecimal) session.get("codpessoa")).toBigInteger().toString();
        log = new String();
        dataTela = DataAtual.getDataAtual("SQL");
        caminho = LOG_BASE_PATH + banco + "\\" + dataTela + "\\" + codPessoa + ".txt";
        logerro = new ArquivoLog();
        filiais = login.getFiliais();

        // persistência e serviços
        persistencia = login.getPp();
        rotassatweb = new RotasSatWeb();
        try {
            if (persistencia == null) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }

            filialTela = rotassatweb.buscaInfoFilial(codFil, persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                displayFatal(ex.getMessage());
            }
        }
    }

    private void displayOverlay(String mensagemDicionario, FacesMessage.Severity severity) {
        FacesMessage mensagem = new FacesMessage(severity, Messages.getMessageS(mensagemDicionario), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    protected void displayInfo(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_INFO);
    }

    protected void displayWarn(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_WARN);
    }

    protected void displayError(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_ERROR);
    }

    protected void displayFatal(String mensagemDicionario) {
        displayOverlay(mensagemDicionario, FacesMessage.SEVERITY_FATAL);
    }

    protected void logaErro(Exception e, String methodName) {
        log = this.getClass().getSimpleName() + "\r\n"
                + methodName + "\r\n"
                + e.getMessage() + "\r\n";
        logerro.Grava(log, caminho);
    }

    protected void mudarFilialTela() {
        try {
            filialTela = rotassatweb.buscaInfoFilial(codFil, persistencia);
        } catch (Exception e) {
            logaErro(e, Thread.currentThread().getStackTrace()[1].getMethodName());
            displayError(e.getMessage()); // FIXME: colocar mensagem decente
        }
    }

    public List<SasPWFill> getFiliais() {
        return filiais;
    }

    public Filiais getFilialTela() {
        return filialTela;
    }

    public void setFilialTela(Filiais filialTela) {
        this.filialTela = filialTela;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Rh_Ctrl;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rh_CtrlDao {

    String sql;

    /*
     * Busca periodos para folha de ponto
     * @param sCodfil  - Código filial
     * @param sMatricula  - Código matrícula
     * @param persistencia  - Conexão ao Banco
     * @return - Lista de registros
     * @throws Exception 
     */
    public List<Rh_Ctrl> getRHCtrl(String sCodfil, String sMatricula, Persistencia persistencia) throws Exception {
        sql = "Select top 6 rh_ctrl.CodFil, rh_ctrl.Matr, "
                + " Convert(VarChar, rh_ctrl.Dt_Ini, 103) DtIni, "
                + " Convert(VarChar, rh_ctrl.Dt_Fim, 103) DtFim "
                + " from Rh_Ctrl "
                + " left join fpperiodos on fpperiodos.dtiniciop = rh_ctrl.dt_ini"
                + "                     and fpperiodos.dtfinalp = rh_ctrl.dt_fim"
                //                + " where rh_ctrl.CodFil = ?"
                //                + " and rh_ctrl.Matr = ?"
                + " where rh_ctrl.Matr in (Select x.Matr from Funcion x where x.CPF in (Select z.CPF From Funcion z WHERE z.Matr = ?) )"
                //            + " and fpperiodos.dtfechapto>=?"
                + " group by rh_ctrl.CodFil, rh_ctrl.Matr, rh_ctrl.Dt_Ini, rh_ctrl.Dt_Fim, rh_ctrl.CodFil "
                + " order by rh_ctrl.Dt_Ini desc";

        Rh_Ctrl oRh_Ctrl;
        List<Rh_Ctrl> lRh_Ctrl;
        try {
            Consulta consult = new Consulta(sql, persistencia);
//            consult.setString(sCodfil);
            consult.setString(sMatricula);
            //           consult.setString(Utilidades.DataAtual.getDataAtual("SQL")); //incluido para listar somente pontos abertos

            consult.select();
            lRh_Ctrl = new ArrayList();
            while (consult.Proximo()) {
                oRh_Ctrl = new Rh_Ctrl();
                oRh_Ctrl.setCodFil(consult.getString("CodFil"));
                oRh_Ctrl.setMatr(consult.getString("Matr"));
                oRh_Ctrl.setDt_Ini(consult.getString("DtIni"));
                oRh_Ctrl.setDt_Fim(consult.getString("DtFim"));
                lRh_Ctrl.add(oRh_Ctrl);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar controle RH - " + e.getMessage());
        }
        return lRh_Ctrl;
    }

    public List<Rh_Ctrl> getRHCtrl_Matr(String sCodfil, String sMatricula, Persistencia persistencia) throws Exception {
        sql = "select Matr, CodFil, Nome_Guer, Situacao, Dt_Ini, Dt_Fim, SupervDiu, SupervNot, HorasTrab,  "
                + "    HorasExtr, HsNeg, HE50, HE100, HE70, HE3, HECC1, HECC2, HECC3, HESup, AdNot, HsNotRed, HsAdNotProrrog,  "
                + "    IntraJ, DiasTrab, DiasFolga, Faltas, Suspensao, DiasFerias, DtRetorno, DtIniFer, DtFimFer,  "
                + "    HsAbnFalta, HsAbnFeriado, HsProjecao, HsInc, FaltasJust, HsAtMedico, Reciclagem, Sindicato,  "
                + "    Transito, DiasInsal, DiasPeric, DiasRonda, DiasCHSup, DiasEscNormal, DiasFerTrab, HEFeriado,  "
                + "    HSFeriado, Secao, Local, Regional, DescDSR, Calculo, PEL, QtdeFT, Operador, Dt_Alter, Hr_Alter "
                + "from Rh_Ctrl "
                + "where Matr = ? and codfil = ?";

        Rh_Ctrl oRh_Ctrl;
        List<Rh_Ctrl> lRh_Ctrl;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sMatricula);
            consult.setString(sCodfil);
            consult.select();
            lRh_Ctrl = new ArrayList();
            while (consult.Proximo()) {
                oRh_Ctrl = new Rh_Ctrl();
                oRh_Ctrl.setMatr(consult.getString("Matr"));
                oRh_Ctrl.setCodFil(consult.getString("CodFil"));
                oRh_Ctrl.setNome_Guer(consult.getString("Nome_Guer"));
                oRh_Ctrl.setSituacao(consult.getString("Situacao"));
                oRh_Ctrl.setDt_Ini(consult.getString("Dt_Ini"));
                oRh_Ctrl.setDt_Fim(consult.getString("Dt_Fim"));
                oRh_Ctrl.setSupervDiu(consult.getString("SupervDiu"));
                oRh_Ctrl.setSupervNot(consult.getString("SupervNot"));
                oRh_Ctrl.setHorasTrab(consult.getString("HorasTrab"));
                oRh_Ctrl.setHorasExtr(consult.getString("HorasExtr"));
                oRh_Ctrl.setHsNeg(consult.getString("HsNeg"));
                oRh_Ctrl.setHE50(consult.getString("HE50"));
                oRh_Ctrl.setHE100(consult.getString("HE100"));
                oRh_Ctrl.setHE70(consult.getString("HE70"));
                oRh_Ctrl.setHE3(consult.getString("HE3"));
                oRh_Ctrl.setHECC1(consult.getString("HECC1"));
                oRh_Ctrl.setHECC2(consult.getString("HECC2"));
                oRh_Ctrl.setHECC3(consult.getString("HECC3"));
                oRh_Ctrl.setHESup(consult.getString("HESup"));
                oRh_Ctrl.setAdNot(consult.getString("AdNot"));
                oRh_Ctrl.setHsNotRed(consult.getString("HsNotRed"));
                oRh_Ctrl.setHsAdNotProrrog(consult.getString("HsAdNotProrrog"));
                oRh_Ctrl.setIntraJ(consult.getString("IntraJ"));
                oRh_Ctrl.setDiasTrab(consult.getString("DiasTrab"));
                oRh_Ctrl.setDiasFolga(consult.getString("DiasFolga"));
                oRh_Ctrl.setFaltas(consult.getString("Faltas"));
                oRh_Ctrl.setSuspensao(consult.getString("Suspensao"));
                oRh_Ctrl.setDiasFerias(consult.getString("DiasFerias"));
                oRh_Ctrl.setDtRetorno(consult.getString("DtRetorno"));
                oRh_Ctrl.setDtIniFer(consult.getString("DtIniFer"));
                oRh_Ctrl.setDtFimFer(consult.getString("DtFimFer"));
                oRh_Ctrl.setHsAbnFalta(consult.getString("HsAbnFalta"));
                oRh_Ctrl.setHsAbnFeriado(consult.getString("HsAbnFeriado"));
                oRh_Ctrl.setHsProjecao(consult.getString("HsProjecao"));
                oRh_Ctrl.setHsInc(consult.getString("HsInc"));
                oRh_Ctrl.setHsAtMedico(consult.getString("HsAtMedico"));
                oRh_Ctrl.setReciclagem(consult.getString("Reciclagem"));
                oRh_Ctrl.setSindicato(consult.getString("Sindicato"));
                oRh_Ctrl.setTransito(consult.getString("Transito"));
                oRh_Ctrl.setDiasInsal(consult.getString("DiasInsal"));
                oRh_Ctrl.setDiasPeric(consult.getString("DiasPeric"));
                oRh_Ctrl.setDiasRonda(consult.getString("DiasRonda"));
                oRh_Ctrl.setDiasCHSup(consult.getString("DiasCHSup"));
                oRh_Ctrl.setDiasEscNormal(consult.getString("DiasEscNormal"));
                oRh_Ctrl.setDiasFerTrab(consult.getString("DiasFerTrab"));
                oRh_Ctrl.setHEFeriado(consult.getString("HEFeriado"));
                oRh_Ctrl.setHSFeriado(consult.getString("HSFeriado"));
                oRh_Ctrl.setSecao(consult.getString("Secao"));
                oRh_Ctrl.setLocal(consult.getString("Local"));
                oRh_Ctrl.setRegional(consult.getInt("Regional"));
                oRh_Ctrl.setDescDSR(consult.getString("DescDSR"));
                oRh_Ctrl.setCalculo(consult.getString("Calculo"));
                oRh_Ctrl.setPEL(consult.getInt("PEL"));
                oRh_Ctrl.setQtdeFT(consult.getString("QtdeFT"));
                oRh_Ctrl.setOperador(consult.getString("Operador"));
                oRh_Ctrl.setDt_Alter(consult.getString("Dt_Alter"));
                oRh_Ctrl.setHr_Alter(consult.getString("Hr_Alter"));

                lRh_Ctrl.add(oRh_Ctrl);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar controle RH - " + e.getMessage());
        }
        return lRh_Ctrl;
    }
}

package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public class Horario {

    /**
     * Converte minutos em horas formatda
     *
     * @param minutos - minutos em inteiros
     * @return Hora formatada em (HH:mm)
     */
    public static String min2hora(int minutos) {
        String Ret;
        int aux1, aux2;
        aux1 = minutos % 60;
        aux2 = minutos / 60;
        if (aux1 / 10 == 0) {
            if (aux2 / 10 == 0) {
                Ret = "0" + String.valueOf(aux2) + ":0" + String.valueOf(aux1);
            } else {
                Ret = String.valueOf(aux2) + ":0" + String.valueOf(aux1);
            }
        } else {
            if (aux2 / 10 == 0) {
                Ret = "0" + String.valueOf(aux2) + ":" + String.valueOf(aux1);
            } else {
                Ret = String.valueOf(aux2) + ":" + String.valueOf(aux1);
            }
        }
        return Ret;
    }

    /**
     * Diferenca entre Hora1 e Hora 2
     *
     * @param Hora1
     * @param Hora2
     * @return minutos em inteiros
     * @throws java.lang.Exception
     */
    public static int iDifHora1Hora2min(String Hora1, String Hora2) throws Exception {
        int resultado;
        try {
            resultado = getHora2Minuto(Hora1) - getHora2Minuto(Hora2);
            return resultado;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    /**
     * Diferenca entre Hora1 e Hora 2
     *
     * @param Hora1
     * @param Hora2
     * @return horario formatado
     * @throws java.lang.Exception
     */
    public static String iDifHora1Hora2(String Hora1, String Hora2) throws Exception {
        int resultado;
        try {
            resultado = getHora2Minuto(Hora1) - getHora2Minuto(Hora2);
            return min2hora(resultado);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    /**
     * Converte um horario em minutos inteiros
     *
     * @param Hora nos formatos HH:mm ou HHmm ou H:mm ou Hmm
     * @return
     * @throws Exception
     */
    public static int getHora2Minuto(String Hora) throws Exception {
        String H = setHora(Hora);
        int aux1, aux2, aux3;
        int resultado;
        try {
            aux1 = Integer.parseInt(H);
            aux2 = aux1 % 100;
            aux3 = aux1 / 100;
            resultado = aux2 + (aux3 * 60);
            return resultado;
        } catch (NumberFormatException e) {
            throw new Exception("Erro ao converter horario para inteiros");
        }
    }

    private static String setHora(String H) throws Exception {
        String Aux1, Aux2;
        if (H.contains(":")) {
            Aux1 = H.substring(0, H.indexOf(":"));
            Aux2 = H.substring(H.indexOf(":") + 1);
            return Aux1 + Aux2;
        } else {
            return H;
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class SatEmail {

    private BigDecimal Sequencia;
    private String Host;
    private int Port;
    private String UserName;
    private String Password;
    private String FromAddress;
    private String FromName;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    /**
     *
     * @return Sequencia
     */
    public BigDecimal getSequencia() {
        return this.Sequencia;
    }

    /**
     * Set Sequencia
     *
     * @param Sequencia
     */
    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Host
     */
    public String getHost() {
        return this.Host;
    }

    /**
     * Set Host
     *
     * @param Host
     */
    public void setHost(String Host) {
        this.Host = Host;
    }

    /**
     *
     * @return Port
     */
    public int getPort() {
        return this.Port;
    }

    /**
     * Set Port
     *
     * @param Port
     */
    public void setPort(int Port) {
        this.Port = Port;
    }

    /**
     *
     * @return UserName
     */
    public String getUserName() {
        return this.UserName;
    }

    /**
     * Set UserName
     *
     * @param UserName
     */
    public void setUserName(String UserName) {
        this.UserName = UserName;
    }

    /**
     *
     * @return Password
     */
    public String getPassword() {
        return this.Password;
    }

    /**
     * Set Password
     *
     * @param Password
     */
    public void setPassword(String Password) {
        this.Password = Password;
    }

    /**
     *
     * @return FromAddress
     */
    public String getFromAddress() {
        return this.FromAddress;
    }

    /**
     * Set FromAddress
     *
     * @param FromAddress
     */
    public void setFromAddress(String FromAddress) {
        this.FromAddress = FromAddress;
    }

    /**
     *
     * @return FromName
     */
    public String getFromName() {
        return this.FromName;
    }

    /**
     * Set FromName
     *
     * @param FromName
     */
    public void setFromName(String FromName) {
        this.FromName = FromName;
    }

    /**
     *
     * @return Date
     */
    public LocalDate getDt_Alter() {
        return this.Dt_Alter;
    }

    /**
     * Set Date
     *
     * @param Dt_Alter
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     *
     * @return Hr_Alter
     */
    public String getHr_Alter() {
        return this.Hr_Alter;
    }

    /**
     * Set Hr_Alter
     *
     * @param Hr_Alter
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

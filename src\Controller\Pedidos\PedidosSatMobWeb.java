/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.Pedidos;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.CxForte;
import SasBeans.Filiais;
import SasBeans.GTVSeq;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.PedidoDN;
import SasBeans.SasPWFill;
import SasBeans.TesMoedas;
import SasBeansCompostas.GTVPedidoOSClienteTesSaida;
import SasBeansCompostas.TesFechaGeracaoGTV;
import SasDaos.ClientesDao;
import SasDaos.CxForteDao;
import SasDaos.FiliaisDao;
import SasDaos.GTVDao;
import SasDaos.GTVSeqDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import SasDaos.RotasDao;
import SasDaos.SasPwFilDao;
import SasDaos.TesFechaDao;
import br.com.sasw.pacotesuteis.sasbeans.compostas.Rt_PercRotasClientes;
import br.com.sasw.pacotesuteis.sasdaos.compostas.Rt_PercRotasClientesDao;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PedidosSatMobWeb {

    public List<Rt_PercRotasClientes> listarRotasCancelamento(String query, String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            Rt_PercRotasClientesDao rt_PercRotasClientesDao = new Rt_PercRotasClientesDao();
            return rt_PercRotasClientesDao.listarClientesRota(query, codFil, data, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.inserirPedido(pedido, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void editarPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.editarPedido(pedido, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesMoedas> buscaCedulasMoedas(String MoedaCedula, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.buscaCedulasMoedas(MoedaCedula, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pedido> listagemPaginada(int primeiro, int linhas, Map filtros, List<CxForte> cxForteList, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.listaPaginada(primeiro, linhas, filtros, cxForteList, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }
    
    public List<Pedido> listagemPaginada(int primeiro, int linhas, Map filtros, List<CxForte> cxForteList, Boolean acessoAdm, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.listaPaginada(primeiro, linhas, filtros, cxForteList, acessoAdm, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Integer totalPedidos(Map filtros, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.totalPedidos(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<PedidoDN> buscarComposicoes(String numero, String codFil, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.buscarComposicoes(numero, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<PedidoDN> buscarComposicoesDN(String numero, String codFil, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.buscarComposicoesDN(numero, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<PedidoDN> buscarComposicoesMD(String numero, String codFil, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.buscarComposicoesMD(numero, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscaFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFill(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Clientes buscarCliente(String CodFil, String Codigo, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.getClientesMobile(CodFil, Codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscaClientes(String codFil, String query, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.buscarClientes(query, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public OS_Vig buscarOS_Vig(String codFil, String os, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_VigDao = new OS_VigDao();
            return os_VigDao.obterOS(codFil, os, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public OS_Vig buscarOS_VigTrajeto(String cliente, String clidst, String codFil, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_VigDao = new OS_VigDao();
            return os_VigDao.obterOSTrajeto(cliente, clidst, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public OS_Vig buscarOS_Pedido(String cliente, String clidst, String codFil, String codCaixaForte, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_VigDao = new OS_VigDao();
            return os_VigDao.obterOSPedido(cliente, clidst, codFil, codCaixaForte, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxForte> listarCaixasForte(String CodFil, Persistencia persistencia) throws Exception {
        try {
            CxForteDao cxfguiasDao = new CxForteDao();
            return cxfguiasDao.listarCaixasForte(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("PedidosSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<GTVPedidoOSClienteTesSaida> getGuiasByPedido(String codFil, String pedido, Persistencia persistencia) throws Exception {
        GTVDao dao = new GTVDao();
        return dao.getGuiasByPedido(codFil, pedido, persistencia);
    }

    public List<GTVSeq> getAllByCodFil(String CodFil, Persistencia persistencia) throws Exception {
        GTVSeqDao dao = new GTVSeqDao(persistencia);
        return dao.getAllByCodFil(CodFil);
    }

    public List<TesFechaGeracaoGTV> getTesFechasGeracaoGTV(String codFil, Date date, Persistencia persistencia) throws Exception {
        TesFechaDao dao = new TesFechaDao(persistencia);
        Format formatter = new SimpleDateFormat("yyyyMMdd");
        String strDate = formatter.format(date);

        return dao.getTesFechasGeracaoGTV(codFil, strDate);
    }

    public int getQuantityGTVInInterval(
            String serie,
            int guia,
            int size,
            Persistencia persistencia
    ) throws Exception {
        GTVDao dao = new GTVDao();
        return dao.getQuantityGTVInInterval(serie, guia, size, persistencia);
    }

    public void gerarSaidasTesouraria(
            String data,
            String codFil,
            String serie,
            int guia,
            int size,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {

    }

    public void gerarSaidasTesouraria(
            String data,
            String codFil,
            List<TesFechaGeracaoGTV> selectedTesFecha,
            String serie,
            int guia,
            int size,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        int quantidade = 0;
        try {
            GTVDao dao = new GTVDao();
            quantidade = dao.getQuantityGTVInInterval(serie, guia, size, persistencia);
        } catch (Exception e) {
            throw new Exception("ErroLerBD");
        }

        if (quantidade != 0) {
            throw new Exception("GuiasJaGeradasSequencia");
        }

        try {
            RotasDao rotasDao = new RotasDao();

            for (TesFechaGeracaoGTV item : selectedTesFecha) {
                String tesouraria = item.getTesFecha().getCodCli();
                rotasDao.gerarSaidasTesouraria(data, codFil, serie, tesouraria,
                        operador, dt_alter, hr_alter, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("ErroSalvarBD");
        }
    }
}

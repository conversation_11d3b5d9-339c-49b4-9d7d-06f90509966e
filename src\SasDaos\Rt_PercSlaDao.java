/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;
import java.math.BigDecimal;
import java.sql.SQLException;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercSlaDao {

    public void inserirHorarioChegada(BigDecimal sequencia, int parada, BigDecimal codFil, String hrChegVei, String op,
            String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Rt_PercSla (sequencia, parada, codfil, HrChegVei, operador, OperIncl,"
                    + " Dt_Incl, Hr_Incl, Dt_Alter, Hr_Alter) \n"
                    + " SELECT TOP 1 ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n"
                    + "                    FROM (SELECT\n"
                    + "                          COUNT(*) AS qtde_cadastrado \n"
                    + "                          FROM Rt_PercSla\n"
                    + "                          WHERE Sequencia = ? AND Parada = ?) AS A\n"
                    + "                    WHERE A.qtde_cadastrado = 0  ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setInt(parada);
            consulta.setBigDecimal(codFil);
            consulta.setString(hrChegVei);
            consulta.setString(op);
            consulta.setString(op);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);

            consulta.setBigDecimal(sequencia);
            consulta.setInt(parada);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercSlaDao.inserirHorarioChegada - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Rt_PercSla (sequencia, parada, codfil, HrChegVei, operador, OperIncl,"
                    + " Dt_Incl, Hr_Incl, Dt_Alter, Hr_Alter) \n SELECT TOP 1 " + sequencia + "," + parada + "," + codFil + "," + hrChegVei + "," + op + "," + op + ","
                    + dataAtual + "," + horaAtual + "," + dataAtual + "," + horaAtual
                    + "                    FROM (SELECT\n"
                    + "                          COUNT(*) AS qtde_cadastrado \n"
                    + "                          FROM Rt_PercSla\n"
                    + "                          WHERE Sequencia = " + sequencia + " AND Parada = " + parada + ") AS A\n"
                    + "                    WHERE A.qtde_cadastrado = 0  ");
        }
    }

    public void updateBaixaHorarios(String Sequencia, String Parada, String HrSaida, String HrCheg,
            String Atraso, String Tempo_Espera, String data_sql, String operador, String horaAtual,
            Persistencia persistencia) throws Exception {
        Horaminuto conversor = new Horaminuto();
        conversor.setHoras(HrSaida, HrCheg);
        String TempoEspera = String.valueOf(conversor.iDifHora1Hora2min());
        try {

            String sql = "UPDATE Rt_PercSla SET HrCheg = ?, Atraso = ?, HrSaida =?, "
                    + " Operador=?, Dt_Alter=?, Hr_Alter = ?, TempoEspera =? "
                    + " WHERE Sequencia =? AND Parada =?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(HrCheg);
            consulta.setString(Atraso);
            consulta.setString(HrSaida);
            consulta.setString(operador);
            consulta.setString(data_sql);
            consulta.setString(horaAtual);
            consulta.setString(TempoEspera);
            consulta.setString(Sequencia);
            consulta.setString(Parada);
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new Exception("Rt_PercSlaDao.updateBaixaHorarios - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_PercSla SET HrCheg = " + HrCheg + ", Atraso = " + Atraso + ", HrSaida =" + HrSaida + ", "
                    + " Operador=" + operador + ", Dt_Alter=" + data_sql + ", Hr_Alter = " + horaAtual + ", TempoEspera =" + TempoEspera
                    + " WHERE Sequencia =" + Sequencia + " AND Parada =" + Parada);
        }
    }

    public void inserirHorarioSaida(String Sequencia, String Parada, String HrSaidaVei, String horaAtual,
            String data_sql, String operador, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Rt_PercSla (HrSaidaVei, Operador, Dt_Alter, Hr_Alter, Sequencia, Parada)\n"
                    + "SELECT ?, ?, ?, ?, ?, ? \n"
                    + "FROM (SELECT COUNT(*) AS Qtde \n"
                    + "    FROM Rt_PercSla\n"
                    + "    WHERE Sequencia = ? AND Parada = ?) AS A\n"
                    + "WHERE A.Qtde = 0;"
                    + "\n"
                    + "UPDATE Rt_PercSla SET HrSaidaVei =?, \n"
                    + " Operador=?, Dt_Alter=?, Hr_Alter = ? \n"
                    + " WHERE Sequencia =? AND Parada =?\n";
            Consulta consulta = new Consulta(sql, persistencia);
            // INSERT
            consulta.setString(HrSaidaVei);
            consulta.setString(operador);
            consulta.setString(data_sql);
            consulta.setString(horaAtual);
            consulta.setString(Sequencia);
            consulta.setString(Parada);

            consulta.setString(Sequencia);
            consulta.setString(Parada);

            // UPDATE
            consulta.setString(HrSaidaVei);
            consulta.setString(operador);
            consulta.setString(data_sql);
            consulta.setString(horaAtual);
            consulta.setString(Sequencia);
            consulta.setString(Parada);
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new Exception("Rt_PercSlaDao.inserirHorarioSaida - " + e.getMessage() + "\r\n"
                    + "UPDATE Rt_PercSla SET HrSaidaVei =" + HrSaidaVei + ", "
                    + " Operador=" + operador + ", Dt_Alter=" + data_sql + ", Hr_Alter = " + horaAtual
                    + " WHERE Sequencia =" + Sequencia + " AND Parada =" + Parada);
        }
    }
}

<eSocial xmlns="http://www.esocial.gov.br/schema/evt/evtAltCadastral/v_S_01_03_00">
    <evtAltCadastral Id="evtAltCadastral_Id">
        <ideEvento>
            <indRetif>ideEvento_indRetif</indRetif>
            <nrRecibo>ideEvento_nrRecibo</nrRecibo>
            <tpAmb>ideEvento_tpAmb</tpAmb>
            <procEmi>ideEvento_procEmi</procEmi>
            <verProc>ideEvento_verProc</verProc>
        </ideEvento>
        <ideEmpregador>
            <tpInsc>ideEmpregador_tpInsc</tpInsc>
            <nrInsc>ideEmpregador_nrInsc</nrInsc>
        </ideEmpregador>
        <ideTrabalhador>
            <cpfTrab>ideTrabalhador_cpfTrab</cpfTrab>
        </ideTrabalhador>
        <alteracao>
            <dtAlteracao>alteracao_dtAlteracao</dtAlteracao>
            <dadosTrabalhador>                
                <nmTrab>dadosTrabalhador_nmTrab</nmTrab>
                <sexo>dadosTrabalhador_sexo</sexo>
                <racaCor>dadosTrabalhador_racaCor</racaCor>
                <estCiv>dadosTrabalhador_estCiv</estCiv>
                <grauInstr>dadosTrabalhador_grauInstr</grauInstr>
                <paisNac>nascimento_paisNac</paisNac>                
                <endereco>
                    <brasil>
                        <tpLograd>brasil_tpLograd</tpLograd>
                        <dscLograd>brasil_dscLograd</dscLograd>
                        <nrLograd>brasil_nrLograd</nrLograd>
                        <complemento>brasil_complemento</complemento>
                        <bairro>brasil_bairro</bairro>
                        <cep>brasil_cep</cep>
                        <codMunic>brasil_codMunic</codMunic>
                        <uf>brasil_uf</uf>
                    </brasil>
                </endereco>
                <infoDeficiencia>
                    <defFisica>infoDeficiencia_defFisica</defFisica>
                    <defVisual>infoDeficiencia_defVisual</defVisual>
                    <defAuditiva>infoDeficiencia_defAuditiva</defAuditiva>
                    <defMental>infoDeficiencia_defMental</defMental>
                    <defIntelectual>infoDeficiencia_defIntelectual</defIntelectual>
                    <reabReadap>infoDeficiencia_reabReadap</reabReadap>
                    <infoCota>infoDeficiencia_infoCota</infoCota>
                    <observacao>infoDeficiencia_observacao</observacao>
                </infoDeficiencia>
                dependentes                    
                <contato>
                    <fonePrinc>contato_fonePrinc</fonePrinc>                    
                    <emailPrinc>contato_emailPrinc</emailPrinc>                    
                </contato>
            </dadosTrabalhador>
        </alteracao>
    </evtAltCadastral>
</eSocial>
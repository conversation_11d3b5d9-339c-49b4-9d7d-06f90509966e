<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                body, head{
                    background: #FFF !important;
                }

                .ListaPosicoes{
                    height: calc(100vh - 0px);
                }

                .ListaPosicoes{
                    border-left: thin solid #DDD !important;
                }

                @media(min-width: 0px) and (max-width: 767px){
                    .ListaPosicoes{
                        height: calc(50vh - 0px);
                        border-left: thin solid #DDD !important;
                    }

                    .ListaPosicoes{
                        border-bottom: thin solid #DDD !important;
                    }
                }

                thead tr th{
                    text-align: center;
                    height: 35px;
                    border-top: none !important;
                    border-bottom: none !important;
                    background: linear-gradient(to bottom, #597d98, #4b708d) !important;
                    font-size: 9pt !Important;
                }

                thead tr:first-child th{
                    padding-top: 0px !important;
                    border-bottom: none !important;
                }

                thead tr:last-child th{
                    padding: 0px 6px 6px 6px !important;
                    border-top: none !important;
                }

                #tblGrid tbody tr td{
                    text-transform: none !important;
                    text-align: center;
                }

                #tblGrid tbody tr td:last-child{
                    width: 200px;
                }

                .CabecalhoFixo {
                    position: fixed !important;
                    width: auto !important;
                    border: none !important;
                    background-color: #FFF;
                    box-shadow: 0px -4px 0px #FFF;
                    z-index: 99999;
                    margin-top:0px !important;
                }

            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;">
            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewParam name="codfil" value="#{valores.codfil}" />
                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                <f:viewParam name="dataTela" value="#{valores.dataTela}" />
                <f:viewAction action="#{valores.carregarListaPosicoes()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <h:form id="dados" onsubmit="return false">
                    <label id="lblCarregando" style="color: #BBB; font-size: 26pt; width:100%; height: 30px; position:absolute; top:0;right:0;bottom:0;left:0;margin: auto; font-weight: 500; text-align: center"><i class="fa fa-refresh fa-spin fa-fw"></i>&nbsp;&nbsp;Carregando ...</label>
                    <div ref="Dados" class="col-md-6 col-sm-6 col-xs-12 ListaPosicoes" style="display:none; background: repeating-linear-gradient(45deg, #F9F9F9, #F3F3F3 10px, #FFF 10px, #FFF 20px); overflow: hidden !important; padding:10px !Important">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="overflow: auto !important; padding:0px !Important; max-height: 100%">
                            <table id="cabecalhoGride" class="DataGrid" style="background-color: #FFF; display:none">
                                <thead class="CabecalhoFixo">
                                    <tr>
                                        <th>#{localemsgs.Rota}</th>
                                        <th>#{localemsgs.Hora}</th>
                                        <th>#{localemsgs.Latitude}</th>
                                        <th>#{localemsgs.Longitude}</th>
                                        <th>#{localemsgs.Mapa}</th>
                                    </tr>
                                    <tr>
                                        <th colspan="5">
                                            <p:inputMask class="form-control" styleClass="form-control" mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                         id="txtPesquisa" style="width: 100%">
                                                <p:watermark for="txtPesquisa" value="#{localemsgs.PesquisarHorario} ..." />
                                            </p:inputMask>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                            <table id="tblGrid" class="DataGrid" style="background-color: #FFF;">
                                <thead>
                                    <tr>
                                        <th>#{localemsgs.Rota}</th>
                                        <th>#{localemsgs.Hora}</th>
                                        <th>#{localemsgs.Latitude}</th>
                                        <th>#{localemsgs.Longitude}</th>
                                        <th>#{localemsgs.Mapa}</th>
                                    </tr>
                                    <tr>
                                        <th colspan="5">
                                            <input type="text" class="form-control" placeholder="#{localemsgs.ProcessandoAguarde}" disabled="disabled" style="font-weight: 500 !important" />
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <h:outputText value="#{valores.listaPosicoesRota}" escape="false" />
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div ref="Dados" class="col-md-6 col-sm-6 col-xs-12 ListaPosicoes" style="display:none;background-color: #EEE; padding:0px !important">
                        <label id="lblAguardando" style="position:absolute; top:0;right:0;bottom:0;left:0;margin:auto; font-size:16pt;color:#BBB; max-height:150px; width:100%;text-align:center; text-shadow: 1px 1px #FFF !important">
                            <i class="fa fa-map-marker" style="font-size:80pt !important"></i>
                            <span style="display:block">#{localemsgs.SelecioneParaVisualizar}</span>
                        </label>
                        <div id="Mapa" style="width: 100%; height: 100%; display:none">

                        </div>
                    </div>
                    <script type="text/javascript">
                        // <![CDATA[
                        var poly;
                        var map;
                        var markers;
                        var iconServico = '';
                        var latServico = '';
                        var lonServico = '';

                        $.extend({
                            CarregarMapa: function (CallBack, inLatitude, inLongitude, inIcone) {
                                // Criar Mapa
                                var inZoom = !inLatitude ? 11 : 14;

                                if (!inLatitude)
                                    inLatitude = -19.9048497;
                                if (!inLongitude)
                                    inLongitude = -43.9648571;

                                map = new google.maps.Map(document.getElementById('Mapa'), {
                                    zoom: inZoom,
                                    center: {lat: parseFloat(inLatitude), lng: parseFloat(inLongitude)}
                                });

                                // Criar objeto de Desenho
                                poly = new google.maps.Polyline({
                                    strokeColor: 'rgba(221, 2, 2, 1)',
                                    strokeOpacity: 0.9,
                                    strokeWeight: 3
                                });

                                poly.setMap(map);

                                // Classe para deixar "Elementos de interesse" invisiveis no mapa
                                var styles = {
                                    default: null,
                                    hide: [
                                        {
                                            featureType: 'poi.business',
                                            stylers: [{visibility: 'off'}]
                                        },
                                        {
                                            featureType: 'transit',
                                            elementType: 'labels.icon',
                                            stylers: [{visibility: 'off'}]
                                        }
                                    ]
                                };

                                // Adicionar classe ao mapa
                                var styleControl = document.getElementById('style-selector-control');
                                map.controls[google.maps.ControlPosition.TOP_LEFT].push(styleControl);
                                map.setOptions({styles: styles['hide']});

                                if (inIcone) {
                                    let Titulo = '#{localemsgs.ROTA}: ' + $tr.find('td:nth-child(1)').text() + '\n#{localemsgs.Hora}: ' + $tr.find('td:nth-child(2)').text() + '\nLat: ' + $tr.find('td:nth-child(3)').text() + '\nLon: ' + $tr.find('td:nth-child(4)').text();

                                    // Criar marcador da Rota
                                    markers = new google.maps.Marker({
                                        position: {lat: parseFloat(inLatitude), lng: parseFloat(inLongitude)},
                                        title: Titulo,
                                        map: map,
                                        icon: inIcone
                                    });

                                    // Criar marcador do serviço
                                    if (iconServico && iconServico.trim() != '') {
                                        markers = new google.maps.Marker({
                                            position: {lat: parseFloat(latServico), lng: parseFloat(lonServico)},
                                            map: map,
                                            icon: iconServico
                                        });
                                    }
                                }

                                if (typeof CallBack === 'function')
                                    CallBack.call();
                            }
                        });

                        function FixarHeader(callBack) {
                            setTimeout(function () {
                                if (typeof callBack === 'function') {
                                    setTimeout(function () {
                                        $('#lblCarregando').css('display', 'none');
                                        $('div[ref="Dados"]').css('display', '');

                                        if (typeof callBack === 'function')
                                            callBack.call();
                                    }, 1500);
                                } else {
                                    $('#lblCarregando').css('display', 'none');
                                    $('div[ref="Dados"]').css('display', '');
                                }

                                $('#tblGrid thead').remove();
                                $('#tblGrid').css('margin-top', '75px');
                                $('#cabecalhoGride').css('display', '');

                                $Origem = $('#tblGrid tbody tr:first-child');

                                $('thead tr:first-child').find("th").each(function (i) {
                                    $(this).attr('style', "min-width:" + $Origem.find("td").eq(i).outerWidth() + "px;" +
                                            "width:" + $Origem.find("td").eq(i).outerWidth() + "px;" +
                                            "max-width:" + $Origem.find("td").eq(i).outerWidth() + "px;");
                                });
                            }, 500);
                        }

                        function initMap(inLat,
                                inLon,
                                inIcon) {
                            if (inLat && inLon && inIcon) {
                                $('#lblAguardando').css('display', 'none');
                                $('#Mapa').css('display', '').html('');
                                $.CarregarMapa(null, inLat, inLon, inIcon);
                            }
                        }

                        function FiltrarGride() {
                            if ($('[id*="txtPesquisa"]').val().trim() === '') {
                                $('#tblGrid tbody tr').css('display', '');
                                setTimeout(function () {
                                    $('#tblGrid').parent('div').scrollTop(0);
                                }, 100);
                            } else {
                                var value = $('[id*="txtPesquisa"]').val().toLowerCase();

                                $("#tblGrid tbody tr").filter(function () {
                                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                                });
                            }
                        }

                        $(document).ready(function () {
                            FixarHeader(function () {
                                if (ObterParamURL('icon') && ObterParamURL('icon').trim() != '') {
                                    iconServico = ObterParamURL('icon');
                                    if (ObterParamURL('lat') && ObterParamURL('lat').trim() != '')
                                        latServico = ObterParamURL('lat');
                                    if (ObterParamURL('lon') && ObterParamURL('lon').trim() != '')
                                        lonServico = ObterParamURL('lon');
                                    if (ObterParamURL('hora') && ObterParamURL('hora').trim() != '')
                                        $('[id*="txtPesquisa"]').val(ObterParamURL('hora')).blur();

                                    $('#tblGrid tbody tr:not([style*="display:none"]):not([style*="display: none"]) td a').click();
                                }
                            });
                        })
                                .on('click', '#tblGrid tbody tr td a', function () {
                                    $tr = $(this).parent('td').parent('tr');

                                    initMap($tr.find('td:nth-child(3)').text(),
                                            $tr.find('td:nth-child(4)').text(),
                                            'https://mobile.sasw.com.br:9091/satmobile/pins/pin_azul_caminhao.png');
                                })
                                .on('blur', '[id*="txtPesquisa"]', function () {
                                    if ($(this).val().trim().indexOf('_') > -1)
                                        $(this).val('');

                                    FiltrarGride();
                                })
                                .on('keyup', '[id*="txtPesquisa"]', function () {
                                    var keycode = (event.keyCode ? event.keyCode : (event.which ? event.which : event.charCode));

                                    if (keycode === 13) {
                                        $(this).blur();
                                        return false;
                                    } else
                                        return true;
                                })
                                ;
                        // ]]>
                    </script>
                    <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                </h:form>
            </div>
        </h:body>
    </f:view>
</html>

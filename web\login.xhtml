<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <link rel="icon" type="image/png" href="assets/novo_layout/images/icons/favicon.ico"/>
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/bootstrap/css/bootstrap.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/fonts/font-awesome-4.7.0/css/font-awesome.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/fonts/Linearicons-Free-v1.0.0/icon-font.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/animate/animate.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/css-hamburgers/hamburgers.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/select2/select2.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/css/util.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/css/main.css" />
            <style>          
                input:-webkit-autofill {
                    -webkit-box-shadow: 0 0 0px 1000px #e6e6e6 inset;
                    font-size: 15px;
                }
                
                div.ui-selectonemenu div.ui-selectonemenu-trigger{
                    margin-top: 18px;
                }
                
                @media (max-width: 450px) {
                    #imgLogosRodape{
                        height: 40px;
                    }
                    
                    [id*="imgBandeiras"]{
                        height: 30px;
                    }
                    
                    [id*="login"] .p-b-15 > img{
                        height: 60px;
                    }
                }
            </style>    
        </h:head>
        <h:body id="h" style="overflow:hidden !important">
            <f:metadata>
                <f:viewAction action="#{login.limparSession}" immediate="true"/>
                <f:viewParam name="empresa" value="#{login.cli}"/>
                <f:viewParam name="tipo" value="#{login.tipoLogin}"/>

                <f:viewParam name="Login" value="#{login.portalLogin}"/>
                <f:viewParam name="Email" value="#{login.portalEmail}"/>
                <f:viewParam name="Senha" value="#{login.portalSenha}"/>
                <f:viewAction action="#{login.onRefresh}"/>
            </f:metadata>

            <p:growl id="msgs" widgetVar="msgs" />

            <div class="limiter">

                <h:form class="form-inline login100-form validate-form" id="login" style="overflow:hidden !important;">
                    <div class="container-login100">
                        <div class="wrap-login100 p-l-50 p-r-50 p-t-37 p-b-10">
                            <span class="login100-form-title p-b-15">
                                <img src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_#{login.cli eq null or login.cli eq ''? 'SATMOB':login.cli}.png" alt="logocliente" style="max-height: 80px" />

                            </span>

                            <span class="login100-form-title p-b-15">
                                <img src="assets/novo_layout/images/#{login.tipoLogin eq 'PC'?'logo_portaldocolaborador':'logo_sistemacorporativo'}.jpg" alt="logo" />
                            </span>

                            <div class="wrap-input100 validate-input m-b-16" data-validate = "Matrícula obrigatória">
                                <h:outputText value="#{localemsgs.EmpresaServico}:" rendered="#{login.exibirEmpresas}" style="display: none !important"/>
                                <h:inputHidden id="txtUrlAtual" value="#{login.enderecoNavegador}"></h:inputHidden>
                                <p:inputText id="email" autocomplete="false" value="#{login.email}" rendered="#{!login.exibirEmpresas}"
                                             required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                             styleClass="input100"/>
                                <p:watermark for="email" value="#{login.tipoLogin ne null and login.tipoLogin eq 'PC' ? localemsgs.Matricula : localemsgs.Email}" 
                                             rendered="#{!login.exibirEmpresas}"/>

                                <p:selectOneMenu id="param" value="#{login.empresa}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Empresa}"
                                                 styleClass="input100"
                                                 filter="true" filterMatchMode="contains" rendered="#{login.exibirEmpresas}" style="height: 62px; padding-left: 10px !important; padding-top: 15px">
                                    <f:selectItems value="#{login.usuarios.pessoaLogin}" var="empresas" itemValue="#{empresas}"
                                                   itemLabel="#{login.NomeEmpresa(empresas.bancoDados)}"  noSelectionValue="Selecione"/>
                                </p:selectOneMenu>


                                <span class="focus-input100"></span>
                                <span class="symbol-input100" style="display:#{!login.exibirEmpresas?'':'none'}">
                                    <span class="lnr lnr-#{login.tipoLogin eq 'PC'?'user':'envelope'}"></span>
                                </span>
                            </div>

                            <div class="wrap-input100 validate-input m-b-16" data-validate = "Senha obrigatória">
                                <p:password id="pw" value="#{login.pwweb}" styleClass="input100" required="true" autocomplete="false"
                                            redisplay="true" rendered="#{!login.exibirEmpresas}"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}">
                                    <p:watermark for="pw" value="#{login.cli eq null ? localemsgs.Senha : localemsgs.Senha}" />
                                </p:password>

                                <p:selectOneMenu id="servico" value="#{login.pessoaPortalSrv}" converter="omnifaces.SelectItemsConverter" 
                                                 styleClass="input100" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Servico}"
                                                 filter="true" filterMatchMode="contains" rendered="#{login.exibirEmpresas}" style="height: 62px; padding-left: 10px !important; padding-top: 15px">
                                    <f:selectItems value="#{login.usuarios.pessoaPortalSrv}" var="servicos" itemValue="#{servicos}"
                                                   itemLabel="#{servicos.descricao}"  noSelectionValue="Selecione"/>
                                </p:selectOneMenu>


                                <span class="focus-input100"></span>
                                <span class="symbol-input100" style="display:#{!login.exibirEmpresas?'':'none'}">
                                    <span class="lnr lnr-lock"></span>
                                </span>
                            </div>

                            <div class="container-login100-form-btn p-t-0">
                                <p:commandButton class="login100-form-btn" value="#{localemsgs.Entrar}" id="btnLogar" 
                                                 action="#{login.Logar}" style="width: 100%" rendered="#{!login.exibirEmpresas}"
                                                 update="msgs login"/>

                                <p:commandButton class="login100-form-btn" value="#{localemsgs.Acessar}" id="btnAcessar" 
                                                 action="#{login.acessarServico}" style="width: 100%" rendered="#{login.exibirEmpresas}"
                                                 update="msgs login"/>
                            </div>

                            <div class="container-login100-form-btn p-t-15"></div>

                            <p:commandLink target="_blank" ajax="false" action="#{login.manual}" styleClass="btn-icones m-b-10">
                                <p:graphicImage url="assets/novo_layout/images/icone_download_manual.png" height="25" style="width: 25px !important" />#{localemsgs.Manual}
                            </p:commandLink>

                            <p:commandLink target="_blank" oncomplete="PF('dlgPrimeiroAcesso').show()" update="primeiroacesso" styleClass="btn-icones m-b-10" style="margin-left: 7px">
                                <p:graphicImage url="assets/novo_layout/images/icone_primeiroacesso.png" height="25" style="width: 25px !important;" />#{localemsgs.PrimeiroAcesso}
                            </p:commandLink>


                            <p:commandLink target="_blank" oncomplete="PF('dlgValidarCC').show()" action="#{login.setValidadorCC('')}"
                                           update="validarCC" styleClass="btn-icones m-b-10">
                                <p:graphicImage url="assets/novo_layout/images/icone_satmob_contracheque.png" height="25" style="width: 25px !important" />#{localemsgs.ValidarCC}
                            </p:commandLink>

                            <p:commandLink target="_blank" actionListener="#{login.preTrocarSenha}" 
                                           oncomplete="PF('dlgEsqueciSenha').show()" update="esqueciSenha" styleClass="btn-icones m-b-10" style="margin-left: 7px">
                                <p:graphicImage url="assets/novo_layout/images/icone_trocarsenha.png" height="25" style="width: 25px !important" />#{localemsgs.EsqueciSenha}
                            </p:commandLink>

                            <div class="text-center w-full p-t-42 p-b-22">
                                <span class="txt1">
                                    <a href="https://gruposas.com.br" target="_blank"><img id="imgLogosRodape" src="https://gruposas.com.br/wp-content/uploads/2021/09/logos_satmobsasw.png" height="50" /></a>
                                </span>

                                <h:commandLink actionListener="#{localeController.increment}" 
                                               action="#{localeController.getLocales}">
                                    <p:graphicImage url="assets/img/#{localeController.number}.png" height="40" id="imgBandeiras" />
                                </h:commandLink>
                            </div>
                        </div>
                    </div>

                    <script src="assets/scripts/capson.js"></script>
                    <script>
                        $(document).ready(function () {
                            $('[id*="txtUrlAtual"]').val(window.location.href);

                            $(window).capslockstate();

                            $(window).bind("capsOn", function (event) {
                                if ($("#login\\:pw:focus").length > 0) {
                                    PF('msgs').renderMessage({"summary": "Tecla CapsLk pressionada.", "severity": "info"});
                                }
                            });
                            $("#login\\:pw").bind("focusin", function (event) {
                                if ($(window).capslockstate("state") === true) {
                                    PF('msgs').renderMessage({"summary": "Tecla CapsLk pressionada.", "severity": "info"});
                                }
                            });
                        });
                    </script>
                </h:form>
            </div>
            <div>
                <h:form id="validarCC">
                    <p:dialog widgetVar="dlgValidarCC" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop" style="max-width:90% !important; border-top:4px solid #3C8DBC !important">
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_contracheque_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.ValidarCC}" style="color:#3C8DBC" /> 
                        </f:facet>
                        <p:panel>
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-4" style="align-self: flex-start">
                                        <p:outputLabel for="codigo" value="#{localemsgs.Codigo}: "/>
                                    </div>
                                    <div class="ui-grid-col-8" style="align-self: flex-start">
                                        <p:inputText id="codigo" value="#{login.validadorCC}"
                                                     label="#{localemsgs.Codigo}" styleClass="form-control"/>
                                        <p:watermark for="codigo" value="#{localemsgs.Codigo}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="text-align:right !important">
                                    <p:commandLink id="cadastro"
                                                   update="msgs" action="#{login.CodigoValida}"
                                                   title="#{localemsgs.Concluido}" ajax="false">
                                        <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="primeiroacesso">
                    <p:dialog widgetVar="dlgPrimeiroAcesso" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop" style="max-width:90% !important; border-top:4px solid #3C8DBC !important">
                        <f:facet name="header">
                            <img src="assets/img/icone_cadastros.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PrimeiroAcesso}" style="color:#3C8DBC;" /> 
                        </f:facet>
                        <p:panel>
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-4" style="align-self: flex-start">
                                        <p:outputLabel for="matricula" value="#{localemsgs.Matricula}: "/>
                                    </div>
                                    <div class="ui-grid-col-8" style="align-self: flex-start">
                                        <p:inputText id="matricula" value="#{login.matricula}"
                                                     label="#{localemsgs.Matricula}" styleClass="form-control"/>
                                        <p:watermark for="matricula" value="#{localemsgs.Matricula}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row">
                                    <div class="ui-grid-col-4" style="align-self: flex-start">
                                        <p:outputLabel for="empresa" value="#{localemsgs.Empresa}: "/>
                                    </div>
                                    <div class="ui-grid-col-8" style="align-self: flex-start">
                                        <p:inputText id="empresa" value="#{login.param}"
                                                     label="#{localemsgs.Empresa}" styleClass="form-control"/>
                                        <p:watermark for="empresa" value="#{localemsgs.Empresa}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="text-align:right !important; padding-top: 8px !important;">
                                    <p:commandLink id="cadastro" action="#{login.PrimeiroAcesso}" update="msgs"
                                                   title="#{localemsgs.Concluido}">
                                        <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="cliente">
                    <p:dialog widgetVar="dlgSelecionarCliente" positionType="absolute" id="dlgClientes"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">  
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientes" style="width: 500px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabela" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabela"
                                             style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                             styleClass="tabela DataGrid" filteredValue="#{login.clientesFiltrados}">
                                    <f:facet name="footer">                                       
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="container col-md-12 row" style="white-space:nowrap">
                                                <div class="col-md-6" style="color:black; text-align: left; font-weight: normal; white-space:nowrap; padding-left:0px !important;">
                                                    <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                    <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                                </div>
                                                <div class="col-md-6" style="color:black; text-align: right; font-weight: normal; white-space:nowrap; padding-left:0px !important;">
                                                    <h:outputText value="#{localemsgs.Buscar}: " />
                                                    <p:inputText id="globalFilter" onkeypress="if (event.keyCode == 13) {
                                                                PF('tabela').filter();
                                                                return false;
                                                            }"
                                                                 style="width:150px;"/>
                                                </div>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectGTVIndex}" update="msgs"/>
                                    <p:column rendered="#{!login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Empresa}" style="width: 145px" filterBy="#{cli.operador}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.operador}" title="#{cli.operador}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.Pref}" style="width: 60px" filterBy="#{cli.agencia}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.agencia}" title="#{cli.agencia}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column rendered="#{login.pp.empresa.contains('CONFEDERAL')}" headerText="#{localemsgs.NSOP}" style="width: 60px" filterBy="#{cli.subAgencia}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.subAgencia}" title="#{cli.subAgencia}">
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{login.pp.empresa.contains('CONFEDERAL') ? localemsgs.Agencia: localemsgs.Cliente}" filterBy="#{cli.nomeCli}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}" />
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteGTVIndex}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="esqueciSenha">
                    <p:dialog widgetVar="dlgEsqueciSenha" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" width="440"
                              showEffect="drop" hideEffect="drop" style="max-width:90% !important; border-top:4px solid #3C8DBC !important">
                        <f:facet name="header">
                            <img src="assets/img/icone_configuracoes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#3C8DBC" /> 
                        </f:facet>
                        <p:panel id="trocarSenha">
                            <div class="ui-grid ui-grid-responsive">
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="email" value="#{localemsgs.Email}: " rendered="#{login.naoPossuoSenhaDia eq false}"/>
                                        <p:outputLabel for="email" value="#{localemsgs.EmpresaMatricula}"  rendered="#{login.naoPossuoSenhaDia}"/>
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="email" value="#{login.email}" styleClass="form-control"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"/>
                                        <p:watermark for="email" value="#{localemsgs.Email}"  rendered="#{login.naoPossuoSenhaDia eq false}"/>
                                        <p:watermark for="email" value="#{localemsgs.EmpresaMatricula}"  rendered="#{login.naoPossuoSenhaDia}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="senhadia" value="#{localemsgs.SenhaDia}: "/>
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="senhadia" value="#{login.senhaDia}" disabled="#{login.naoPossuoSenhaDia}"
                                                     label="#{localemsgs.SenhaDia}" styleClass="form-control"/>
                                        <p:watermark for="senhadia" value="#{localemsgs.SenhaDia}"/>
                                    </div>
                                </div>
                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-11" style="text-align: right">
                                        <p:outputLabel for="senhadia" value="#{localemsgs.NaoPossuoSenhaDia}"/>
                                    </div>
                                    <div class="ui-grid-col-1" style="text-align: right">
                                        <p:selectBooleanCheckbox value="#{login.naoPossuoSenhaDia}">
                                            <p:ajax update="trocarSenha" listener="#{login.naoPossuo}"/>
                                        </p:selectBooleanCheckbox>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="campo1" rendered="#{login.rand eq 1}" value="#{localemsgs.RGn}: " />
                                        <p:outputLabel for="campo1" rendered="#{login.rand eq 2}" value="#{localemsgs.CidadeRes}: " />
                                        <p:outputLabel for="campo1" rendered="#{login.rand eq 3}" value="#{localemsgs.CidadeRes}: " />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="campo1" style="width: 100%" value="#{login.validacao1}" rendered="#{login.rand ne 0}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="campo2" rendered="#{login.rand eq 1}" value="#{localemsgs.CPFn}: " />
                                        <p:outputLabel for="campo2" rendered="#{login.rand eq 2}" value="#{localemsgs.CPFn}: " />
                                        <p:outputLabel for="campo2" rendered="#{login.rand eq 3}" value="#{localemsgs.Dt_Nascn}: " />
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="campo2" style="width: 100%" value="#{login.validacao2}" rendered="#{login.rand ne 0}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px">
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:outputLabel for="novaSenhaTexto" rendered="#{login.senhaEsquecida ne null}"
                                                       value="#{localemsgs.NovaSenha}: " style="color: #022a48"/>
                                    </div>
                                    <div class="ui-grid-col-6" style="align-self: flex-start">
                                        <p:inputText id="novaSenhaTexto" style="width: 100%; font-weight: bold; opacity: 0.5;"
                                                     value="#{login.senhaEsquecida}" rendered="#{login.senhaEsquecida ne null}"/>
                                    </div>
                                </div>

                                <div class="ui-grid-row" style="padding-bottom: 5px; text-align:right !important; width:100% !important">

                                    <p:commandLink id="cadastro" action="#{login.trocarSenha}" update="msgs esqueciSenha:trocarSenha"
                                                   title="#{localemsgs.TrocarSenha}">
                                        <p:graphicImage url="assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>


                                    <p:commandLink id="fechar" oncomplete="PF('dlgEsqueciSenha').hide();"
                                                   title="#{localemsgs.Fechar}" style="margin-left:8px !important;">
                                        <p:graphicImage url="assets/img/icone_fechar.png" width="40" height="40" />
                                    </p:commandLink>

                                </div>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="clienteEW">
                    <p:dialog widgetVar="dlgSelecionarClienteEW" positionType="absolute" id="dlgClienteEW"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">  
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientesEW" style="width: 500px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabelaClienteEW" value="#{login.postos}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.postoSelecionado}" rowKey="#{cli.secao}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabelaClienteEW"
                                             style="font-size: 12px; float: left" styleClass="tabela" filteredValue="#{login.postosFiltrados}">
                                    <f:facet name="footer">
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                            </div>
                                            <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                                <p:inputText id="globalFilter" onkeyup="PF('tabelaClienteEW').filter()" style="width:150px;"/>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectEWIndex}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 150px" filterBy="#{cli.secao}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.secao}" title="#{cli.secao}" >
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" filterBy="#{cli.local}" style="width: 285px"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.local}" title="#{cli.local}" converter="tradutor"/>
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteEWIndex}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>

                <h:form id="clienteCofre">
                    <p:dialog widgetVar="dlgClienteCofre" positionType="absolute" id="dlgClienteCofre"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                              showEffect="drop" hideEffect="drop" closeOnEscape="false"
                              style="width: 800px; height: 385px; background-image: url('assets/img/menu_fundo.png');
                              background-size: 750px 430px; left:200px">  
                        <f:facet name="header">
                            <img src="assets/img/icone_satmob_clientes.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.SelecioneCliente}" style="color:#022a48" /> 
                        </f:facet>
                        <p:panel id="tabelaClientesCofre" style="width: 500px; background: transparent">
                            <div class="form-inline">
                                <p:dataTable id="tabelaClienteCofre" value="#{login.clientes}" emptyMessage="#{localemsgs.SemRegistros}"
                                             var="cli" resizableColumns="true" selection="#{login.selecionado}" rowKey="#{cli.codCli}"
                                             scrollable="true" scrollHeight="200" selectionMode="single" widgetVar="tabelaClienteCofre"
                                             style="font-size: 12px; float: left" styleClass="tabela" filteredValue="#{login.clientesFiltrados}">
                                    <f:facet name="footer">
                                        <div class="ui-grid-row ui-grid-responsive">
                                            <div class="ui-grid-col-6" style="color:black; text-align: left; font-weight: normal">
                                                <h:outputText value="#{localemsgs.VerTodos}: "/>
                                                <p:selectBooleanCheckbox value="#{login.verTodos}" />
                                            </div>
                                            <div class="ui-grid-col-6" style="color:black; text-align: right; font-weight: normal">
                                                <h:outputText value="#{localemsgs.Buscar}: " />
                                                <p:inputText id="globalFilter" onkeyup="PF('tabelaClienteCofre').filter()" style="width:150px;"/>
                                            </div>
                                        </div>
                                    </f:facet>
                                    <p:ajax event="rowDblselect" listener="#{login.dblSelectCofreIndex}" update="msgs"/>
                                    <p:column headerText="#{localemsgs.Codigo}" style="width: 150px" filterBy="#{cli.codCli}"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.codCli}" title="#{cli.codCli}" >
                                        </h:outputText>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Cliente}" filterBy="#{cli.nomeCli}" style="width: 285px"
                                              filterMatchMode="contains">
                                        <h:outputText value="#{cli.nomeCli}" title="#{cli.nomeCli}" converter="tradutor"/>
                                    </p:column>
                                </p:dataTable>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="btnSelecionar" action="#{login.selecionarClienteCofreIndex}"
                                               title="#{localemsgs.Selecionar}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog> 
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>
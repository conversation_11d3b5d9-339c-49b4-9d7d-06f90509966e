<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/index.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/stylePage.css" rel="stylesheet" />
            <script src="assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .jconfirm-content{
                    display: flex;
                }
            </style>
        </h:head>
        <h:body style="background-color: #FFF">
            <f:metadata>
                <f:viewParam name="keyId" value="#{login.token}"/>
                <f:viewAction action="#{login.tratarToken}" />
            </f:metadata>

            <p:growl id="msgs" widgetVar="msgs" />

            <h:form id="main" style="background-color: transparent">
                <p:panel id="pnlUpload" style="display: none">
                    <p:remoteCommand name="rc" 
                                     process="txtNomeArq" oncomplete="AbrirSelecao();" />

                    <p:inputText id="txtNomeArq" value="#{login.nomeArq}"></p:inputText>

                    <p:fileUpload id="uploadArquivo"
                                  fileUploadListener="#{login.HandleFileUpload}"
                                  allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|doc?x|xls?x|ppt?x|txt|zip|rar|ret|rem|csv|pdf|PNG|JPE?G|GIF|BMP|DOC?X|XLS?X|PPT?X|PDF|TXT|ZIP|RAR|RET|REM|CSV)$/"
                                  auto="true" multiple="false"
                                  process="@this txtNomeArq"
                                  invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                  dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                  update="msgs" skinSimple="true" previewWidth="10"
                                  style="width:10px; height:10px;"></p:fileUpload>
                </p:panel>

                <p:panel id="pnlListaArquivos" class="col-md-12 col-sm-12 col-xs-12" style="background-color: transparent">
                    <div class="col-md-2 col-sm-3 col-xs-6" style="height: 200px; cursor: pointer; ">
                        <div id="btAdd" class="col-md-12 col-sm-12 col-xs-12" style="cursor: pointer; text-align: center; height: 100%; background-color: #DDD; border: 3px solid #CCC; color: #555; font-size: 16pt; border-radius: 10px; display: flex; align-items: center; justify-content: center; ">
                            <label style="width: 100%;cursor: pointer; ">
                                <i class="fa fa-plus-square" style="font-size: 30pt;"></i><br />
                                #{localemsgs.Adicionar}</label>
                        </div>
                    </div>
                </p:panel>

                <a id="lnkDownload" href="#" download="download" hidden="hidden"></a>

                <script>
// <![CDATA[

                    function CarregarArquivos(Dados) {
                        try {
                            $frmUpload.close();
                        } catch (e) {
                        }
                        let HtmlIcones = '';

                        Dados.split('=*=').forEach(function (item) {
                            let NomeArquivo = item.split('|*|')[0];
                            let Icone = '<i class="fa fa-file-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                            let Link = item.split('|*|')[2];

                            switch (ReplaceAll(item.split('|*|')[1], '.', '')) {
                                case 'xls':
                                case 'xlsx':
                                case 'csv':
                                    Icone = '<i class="fa fa-file-excel-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                    break;

                                case 'doc':
                                case 'docx':
                                    Icone = '<i class="fa fa-file-word-o" aria-hidden="true" style="font-size: 30pt;"></i>';

                                    break;

                                case 'pdf':
                                    Icone = '<i class="fa fa-file-pdf-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                    break;

                                case 'txt':
                                case 'ret':
                                case 'rem':
                                    Icone = '<i class="fa fa-file-text-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                    break;

                                case 'zip':
                                case 'rar':
                                    Icone = '<i class="fa fa-file-archive-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                    break

                                case 'ppt':
                                case 'pptx':
                                    Icone = '<i class="fa fa-file-powerpoint-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                    break

                                case 'png':
                                case 'jpg':
                                case 'jpeg':
                                case 'tiff':
                                case 'bmp':
                                case 'gif':
                                    Icone = '<i class="fa fa-file-image-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                    break
                            }


                            HtmlIcones += '<div class="col-md-2 col-sm-3 col-xs-6" style="height: 200px; cursor: pointer; ">';
                            HtmlIcones += '    <div class="col-md-12 col-sm-12 col-xs-12 btIcone" ref-nome="' + NomeArquivo.toLowerCase() + '" ref-link="' + Link + '" style="cursor: pointer; text-align: center; height: 100%; color: #555; font-size: 10pt; border-radius: 10px; display: flex; align-items: center; justify-content: center; border: thin solid #CCC; position: relative;">';
                            HtmlIcones += '        <label style="width: 100%;cursor: pointer; ">';
                            HtmlIcones += '            ' + Icone + '<br />';
                            HtmlIcones += '            ' + NomeArquivo + '</label>';
                            HtmlIcones += '    </div>';
                            HtmlIcones += '</div>';
                        });


                        $('[id$="pnlListaArquivos"]').find('.btIcone').parent('div').remove();
                        $('[id$="pnlListaArquivos"]').append(HtmlIcones);
                    }

                    function AbrirSelecao() {
                        $('[id*="uploadArquivo_input"]').click();
                        //$('[id*="uploadArquivo_input"]').trigger("click");
                    }

                    function AbrirFormAdd() {
                        let HTML = '';
                        HTML += '<div class="col-md-12 col-sm-12 col-xs-12">';
                        HTML += '  <label style="text-align: left !important; width: 100%;">#{localemsgs.NomeArquivo} <font color="red">(*)</font></label>';
                        HTML += '  <input type="text" class="form-control" id="txtRefNomeArquivo" />';
                        HTML += '</div>';


                        $frmUpload = $.alert({
                            icon: 'fa fa-upload',
                            theme: 'modern',
                            closeIcon: true,
                            type: 'blue',
                            columnClass: 'large',
                            title: '#{localemsgs.UploadArquivo}',
                            content: HTML,
                            buttons: {
                                cancel: {
                                    text: '#{localemsgs.Cancelar}'
                                },
                                ok: {
                                    text: '#{localemsgs.SelecionarArquivo}',
                                    btnClass: 'btn-blue',
                                    id: 'teste',
                                    action: function () {

                                        if ($('#txtRefNomeArquivo').val() != '') {
                                            $('[id$="txtNomeArq"]').val($('#txtRefNomeArquivo').val());
                                            rc();
                                        } else {
                                            $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.InformeNomeArquivo}', function () {
                                                $('#txtRefNomeArquivo').focus();
                                            });
                                        }

                                        return false;
                                    }
                                }
                            },
                            onContentReady: function () {
                                if (ObterParamURL('nomeArquivo') && ObterParamURL('nomeArquivo') != ''){
                                    $('#txtRefNomeArquivo').val(ObterParamURL('nomeArquivo').split('.')[0]);
                                    setTimeout(function(){
                                        $('.jconfirm-buttons .btn-blue').click();
                                    }, 1000);
                                }
                            }
                        });
                    }

                    $(document).ready(function () {
                        if (ObterParamURL('nomeArquivo') && ObterParamURL('nomeArquivo') != '') {
                            $('[id$="pnlListaArquivos"]').css('display', 'none');

                            setTimeout(function () {
                                if ($('.btIcone[ref-nome="' + ObterParamURL('nomeArquivo').toLowerCase() + '"]').length > 0) {
                                    // Existe
                                    $('.btIcone[ref-nome="' + ObterParamURL('nomeArquivo').toLowerCase() + '"]').click();
                                } else {
                                    // Não Existe
                                    $('[id$="txtNomeArq"]').val(ObterParamURL('nomeArquivo').split('.')[0]);
                                    AbrirFormAdd();
                                }
                            }, 200);
                        }
                    }).on('click', '#btAdd', function () {
                        AbrirFormAdd();
                    }).on('click', '.btIcone', function () {
                        downloadURI($(this).attr('ref-link'), $(this).attr('ref-nome'));

                    }).on('mouseenter', '.btIcone', function () {
                        $('#btVirtualDownload').remove();

                        let btVirtualDownload = '<label id="btVirtualDownload" style="width: calc(100% - 2px); left: 1px; height: 40px; padding-top: 8px; position: absolute; bottom: -3px; border-radius: 0px 0px 10px 10px; background-color: forestgreen; color: #FFF; font-size: 14pt; border: none;">#{localemsgs.Download}</label>';
                        $(this).append(btVirtualDownload);
                    })
                            .on('mouseleave', '.btIcone', function () {
                                $('#btVirtualDownload').remove();
                            });


                    function downloadURI(fileURL, fileName) {
                        // for non-IE
                        if (!window.ActiveXObject) {
                            var save = document.createElement('a');
                            save.href = fileURL;
                            save.target = '_blank';
                            save.download = fileName || 'unknown';

                            var evt = new MouseEvent('click', {
                                'view': window,
                                'bubbles': true,
                                'cancelable': false
                            });
                            save.dispatchEvent(evt);

                            (window.URL || window.webkitURL).revokeObjectURL(save.href);
                        }

                        // for IE < 11
                        else if (!!window.ActiveXObject && document.execCommand) {
                            var _window = window.open(fileURL, '_blank');
                            _window.document.close();
                            _window.document.execCommand('SaveAs', true, fileName || fileURL)
                            _window.close();
                        }
                    }
                    // ]]>
                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>

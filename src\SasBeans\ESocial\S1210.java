/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class S1210 {

    public S1210() {
        this.ideBenef_infoPgto = new ArrayList<>();
    }

    private int sucesso;

    private String evtPgtos_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_indApuracao;
    private String ideEvento_perApur;
    private String ideEvento_indGuia;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideBenef_cpfBenef;
    private String ideBenef_matr;
    private String ideBenef_nome;
    private String deps_vrDedDep;

    private List<InfoPgto> ideBenef_infoPgto;

    private List<DetPgtoAnt> infoPagto_detPgtoAnt;
    
    private PlanSaude planSaude;

    private String idePais_codPais;
    private String idePais_indNIF;
    private String idePais_nifBenef;

    private String endExt_dscLograd;
    private String endExt_nrLograd;
    private String endExt_complem;
    private String endExt_bairro;
    private String endExt_nmCid;
    private String endExt_codPostal;

    private String dmDev_codCateg;

    public String getDmDev_codCateg() {
        return dmDev_codCateg;
    }

    public void setDmDev_codCateg(String dmDev_codCateg) {
        this.dmDev_codCateg = dmDev_codCateg;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 53 * hash + Objects.hashCode(this.ideBenef_matr);
//        hash = 53 * hash + Objects.hashCode(this.infoPgto_tpPgto);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final S1210 other = (S1210) obj;
        if (!Objects.equals(this.ideBenef_matr, other.ideBenef_matr)) {
            return false;
        }
//        if (!Objects.equals(this.infoPgto_tpPgto, other.infoPgto_tpPgto)) {
//            return false;
//        }
        return true;
    }

    public PlanSaude getPlanSaude() {
        return planSaude;
    }

    public void setPlanSaude(PlanSaude planSaude) {
        this.planSaude = planSaude;
    }
    
    public static class InfoPgto {

        private String infoPgto_dtPgto;
        private String infoPgto_tpPgto;
        private String infoPgto_indResBr;

        private DetPgtoFl infoPgto_detPgtoFl;
        private DetPgtoBenPr infoPgto_detPgtoBenPr;
        private DetPgtoFer infoPgto_detPgtoFer;

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 37 * hash + Objects.hashCode(this.infoPgto_tpPgto);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final InfoPgto other = (InfoPgto) obj;
            if (!Objects.equals(this.infoPgto_tpPgto, other.infoPgto_tpPgto)) {
                return false;
            }
            return true;
        }

        public String getInfoPgto_dtPgto() {
            return null == infoPgto_dtPgto ? "" : infoPgto_dtPgto;
        }

        public void setInfoPgto_dtPgto(String infoPgto_dtPgto) {
            this.infoPgto_dtPgto = infoPgto_dtPgto;
        }

        public String getInfoPgto_tpPgto() {
            return null == infoPgto_tpPgto ? "" : infoPgto_tpPgto;
        }

        public void setInfoPgto_tpPgto(String infoPgto_tpPgto) {
            this.infoPgto_tpPgto = infoPgto_tpPgto;
        }

        public String getInfoPgto_indResBr() {
            return null == infoPgto_indResBr ? "" : infoPgto_indResBr;
        }

        public void setInfoPgto_indResBr(String infoPgto_indResBr) {
            this.infoPgto_indResBr = infoPgto_indResBr;
        }

        public DetPgtoFl getInfoPgto_detPgtoFl() {
            return infoPgto_detPgtoFl;
        }

        public void setInfoPgto_detPgtoFl(DetPgtoFl infoPgto_detPgtoFl) {
            this.infoPgto_detPgtoFl = infoPgto_detPgtoFl;
        }

        public DetPgtoBenPr getInfoPgto_detPgtoBenPr() {
            return infoPgto_detPgtoBenPr;
        }

        public void setInfoPgto_detPgtoBenPr(DetPgtoBenPr infoPgto_detPgtoBenPr) {
            this.infoPgto_detPgtoBenPr = infoPgto_detPgtoBenPr;
        }

        public DetPgtoFer getInfoPgto_detPgtoFer() {
            return infoPgto_detPgtoFer;
        }

        public void setInfoPgto_detPgtoFer(DetPgtoFer infoPgto_detPgtoFer) {
            this.infoPgto_detPgtoFer = infoPgto_detPgtoFer;
        }
    }

    public static class DetPgtoFl {

        public DetPgtoFl() {
            this.detPgtoFl_retPgtoTot = new ArrayList<>();
            this.detPgtoFl_infoPgtoParc = new ArrayList<>();
        }

        private String detPgtoFl_perRef;
        private String detPgtoFl_ideDmDev;
        private String detPgtoFl_indPgtoTt;
        private String detPgtoFl_vrLiq;
        private String detPgtoFl_nrRecArq;
        private List<RetPgtoTot> detPgtoFl_retPgtoTot;
        private List<InfoPgtoParc> detPgtoFl_infoPgtoParc;

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 79 * hash + Objects.hashCode(this.detPgtoFl_ideDmDev);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DetPgtoFl other = (DetPgtoFl) obj;
            if (!Objects.equals(this.detPgtoFl_ideDmDev, other.detPgtoFl_ideDmDev)) {
                return false;
            }
            return true;
        }

        public String getDetPgtoFl_perRef() {
            return null == detPgtoFl_perRef ? "" : detPgtoFl_perRef;
        }

        public void setDetPgtoFl_perRef(String detPgtoFl_perRef) {
            this.detPgtoFl_perRef = detPgtoFl_perRef;
        }

        public String getDetPgtoFl_ideDmDev() {
            return null == detPgtoFl_ideDmDev ? "" : detPgtoFl_ideDmDev;
        }

        public void setDetPgtoFl_ideDmDev(String detPgtoFl_ideDmDev) {
            this.detPgtoFl_ideDmDev = detPgtoFl_ideDmDev;
        }

        public String getDetPgtoFl_indPgtoTt() {
            return null == detPgtoFl_indPgtoTt ? "" : detPgtoFl_indPgtoTt;
        }

        public void setDetPgtoFl_indPgtoTt(String detPgtoFl_indPgtoTt) {
            this.detPgtoFl_indPgtoTt = detPgtoFl_indPgtoTt;
        }

        public String getDetPgtoFl_vrLiq() {
            return null == detPgtoFl_vrLiq ? "" : detPgtoFl_vrLiq;
        }

        public void setDetPgtoFl_vrLiq(String detPgtoFl_vrLiq) {
            this.detPgtoFl_vrLiq = detPgtoFl_vrLiq;
        }

        public String getDetPgtoFl_nrRecArq() {
            return null == detPgtoFl_nrRecArq ? "" : detPgtoFl_nrRecArq;
        }

        public void setDetPgtoFl_nrRecArq(String detPgtoFl_nrRecArq) {
            this.detPgtoFl_nrRecArq = detPgtoFl_nrRecArq;
        }

        public List<RetPgtoTot> getDetPgtoFl_retPgtoTot() {
            return detPgtoFl_retPgtoTot;
        }

        public void setDetPgtoFl_retPgtoTot(List<RetPgtoTot> detPgtoFl_retPgtoTot) {
            this.detPgtoFl_retPgtoTot = detPgtoFl_retPgtoTot;
        }

        public List<InfoPgtoParc> getDetPgtoFl_infoPgtoParc() {
            return detPgtoFl_infoPgtoParc;
        }

        public void setDetPgtoFl_infoPgtoParc(List<InfoPgtoParc> detPgtoFl_infoPgtoParc) {
            this.detPgtoFl_infoPgtoParc = detPgtoFl_infoPgtoParc;
        }
    }

    public static class DetPgtoBenPr {

        public DetPgtoBenPr() {
            this.detPgtoBenPr_retPgtoTot = new ArrayList<>();
            this.detPgtoBenPr_infoPgtoParc = new ArrayList<>();
        }

        private String detPgtoBenPr_perRef;
        private String detPgtoBenPr_ideDmDev;
        private String detPgtoBenPr_indPgtoTt;
        private String detPgtoBenPr_vrLiq;
        private List<RetPgtoTot> detPgtoBenPr_retPgtoTot;
        private List<InfoPgtoParc> detPgtoBenPr_infoPgtoParc;

        @Override
        public int hashCode() {
            int hash = 5;
            hash = 79 * hash + Objects.hashCode(this.detPgtoBenPr_ideDmDev);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DetPgtoBenPr other = (DetPgtoBenPr) obj;
            if (!Objects.equals(this.detPgtoBenPr_ideDmDev, other.detPgtoBenPr_ideDmDev)) {
                return false;
            }
            return true;
        }

        public String getDetPgtoBenPr_perRef() {
            return null == detPgtoBenPr_perRef ? "" : detPgtoBenPr_perRef;
        }

        public void setDetPgtoBenPr_perRef(String detPgtoBenPr_perRef) {
            this.detPgtoBenPr_perRef = detPgtoBenPr_perRef;
        }

        public String getDetPgtoBenPr_ideDmDev() {
            return null == detPgtoBenPr_ideDmDev ? "" : detPgtoBenPr_ideDmDev;
        }

        public void setDetPgtoBenPr_ideDmDev(String detPgtoBenPr_ideDmDev) {
            this.detPgtoBenPr_ideDmDev = detPgtoBenPr_ideDmDev;
        }

        public String getDetPgtoBenPr_indPgtoTt() {
            return null == detPgtoBenPr_indPgtoTt ? "" : detPgtoBenPr_indPgtoTt;
        }

        public void setDetPgtoBenPr_indPgtoTt(String detPgtoBenPr_indPgtoTt) {
            this.detPgtoBenPr_indPgtoTt = detPgtoBenPr_indPgtoTt;
        }

        public String getDetPgtoBenPr_vrLiq() {
            return null == detPgtoBenPr_vrLiq ? "" : detPgtoBenPr_vrLiq;
        }

        public void setDetPgtoBenPr_vrLiq(String detPgtoBenPr_vrLiq) {
            this.detPgtoBenPr_vrLiq = detPgtoBenPr_vrLiq;
        }

        public List<RetPgtoTot> getDetPgtoBenPr_retPgtoTot() {
            return detPgtoBenPr_retPgtoTot;
        }

        public void setDetPgtoBenPr_retPgtoTot(List<RetPgtoTot> detPgtoBenPr_retPgtoTot) {
            this.detPgtoBenPr_retPgtoTot = detPgtoBenPr_retPgtoTot;
        }

        public List<InfoPgtoParc> getDetPgtoBenPr_infoPgtoParc() {
            return detPgtoBenPr_infoPgtoParc;
        }

        public void setDetPgtoBenPr_infoPgtoParc(List<InfoPgtoParc> detPgtoBenPr_infoPgtoParc) {
            this.detPgtoBenPr_infoPgtoParc = detPgtoBenPr_infoPgtoParc;
        }
    }

    public static class DetPgtoFer {

        public DetPgtoFer() {
            this.detPgtoFer_detRubrFer = new ArrayList<>();
        }

        private String detPgtoFer_codCateg;
        private String detPgtoFer_matricula;
        private String detPgtoFer_dtIniGoz;
        private String detPgtoFer_qtDias;
        private String detPgtoFer_vrLiq;
        private List<DetRubrFer> detPgtoFer_detRubrFer;

        public String getDetPgtoFer_codCateg() {
            return null == detPgtoFer_codCateg ? "" : detPgtoFer_codCateg;
        }

        public void setDetPgtoFer_codCateg(String detPgtoFer_codCateg) {
            this.detPgtoFer_codCateg = detPgtoFer_codCateg;
        }

        public String getDetPgtoFer_matricula() {
            return null == detPgtoFer_matricula ? "" : detPgtoFer_matricula;
        }

        public void setDetPgtoFer_matricula(String detPgtoFer_matricula) {
            this.detPgtoFer_matricula = detPgtoFer_matricula;
        }

        public String getDetPgtoFer_dtIniGoz() {
            return null == detPgtoFer_dtIniGoz ? "" : detPgtoFer_dtIniGoz;
        }

        public void setDetPgtoFer_dtIniGoz(String detPgtoFer_dtIniGoz) {
            this.detPgtoFer_dtIniGoz = detPgtoFer_dtIniGoz;
        }

        public String getDetPgtoFer_qtDias() {
            return null == detPgtoFer_qtDias ? "" : detPgtoFer_qtDias;
        }

        public void setDetPgtoFer_qtDias(String detPgtoFer_qtDias) {
            this.detPgtoFer_qtDias = detPgtoFer_qtDias;
        }

        public String getDetPgtoFer_vrLiq() {
            return null == detPgtoFer_vrLiq ? "" : detPgtoFer_vrLiq;
        }

        public void setDetPgtoFer_vrLiq(String detPgtoFer_vrLiq) {
            this.detPgtoFer_vrLiq = detPgtoFer_vrLiq;
        }

        public List<DetRubrFer> getDetPgtoFer_detRubrFer() {
            return detPgtoFer_detRubrFer;
        }

        public void setDetPgtoFer_detRubrFer(List<DetRubrFer> detPgtoFer_detRubrFer) {
            this.detPgtoFer_detRubrFer = detPgtoFer_detRubrFer;
        }
    }

    public String getIdeBenef_nome() {
        return ideBenef_nome;
    }

    public void setIdeBenef_nome(String ideBenef_nome) {
        this.ideBenef_nome = ideBenef_nome;
    }

    public String getIdeBenef_matr() {
        return ideBenef_matr;
    }

    public void setIdeBenef_matr(String ideBenef_matr) {
        this.ideBenef_matr = ideBenef_matr;
    }

    public List<InfoPgto> getIdeBenef_infoPgto() {
        return ideBenef_infoPgto;
    }

    public void setIdeBenef_infoPgto(List<InfoPgto> ideBenef_infoPgto) {
        this.ideBenef_infoPgto = ideBenef_infoPgto;
    }

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public List<DetPgtoAnt> getInfoPagto_detPgtoAnt() {
        return infoPagto_detPgtoAnt;
    }

    public void setInfoPagto_detPgtoAnt(List<DetPgtoAnt> infoPagto_detPgtoAnt) {
        this.infoPagto_detPgtoAnt = infoPagto_detPgtoAnt;
    }

    public String getIdePais_codPais() {
        return null == idePais_codPais ? "" : idePais_codPais;
    }

    public void setIdePais_codPais(String idePais_codPais) {
        this.idePais_codPais = idePais_codPais;
    }

    public String getIdePais_indNIF() {
        return null == idePais_indNIF ? "" : idePais_indNIF;
    }

    public void setIdePais_indNIF(String idePais_indNIF) {
        this.idePais_indNIF = idePais_indNIF;
    }

    public String getIdePais_nifBenef() {
        return null == idePais_nifBenef ? "" : idePais_nifBenef;
    }

    public void setIdePais_nifBenef(String idePais_nifBenef) {
        this.idePais_nifBenef = idePais_nifBenef;
    }

    public String getEndExt_dscLograd() {
        return null == endExt_dscLograd ? "" : endExt_dscLograd;
    }

    public void setEndExt_dscLograd(String endExt_dscLograd) {
        this.endExt_dscLograd = endExt_dscLograd;
    }

    public String getEndExt_nrLograd() {
        return null == endExt_nrLograd ? "" : endExt_nrLograd;
    }

    public void setEndExt_nrLograd(String endExt_nrLograd) {
        this.endExt_nrLograd = endExt_nrLograd;
    }

    public String getEndExt_complem() {
        return null == endExt_complem ? "" : endExt_complem;
    }

    public void setEndExt_complem(String endExt_complem) {
        this.endExt_complem = endExt_complem;
    }

    public String getEndExt_bairro() {
        return null == endExt_bairro ? "" : endExt_bairro;
    }

    public void setEndExt_bairro(String endExt_bairro) {
        this.endExt_bairro = endExt_bairro;
    }

    public String getEndExt_nmCid() {
        return null == endExt_nmCid ? "" : endExt_nmCid;
    }

    public void setEndExt_nmCid(String endExt_nmCid) {
        this.endExt_nmCid = endExt_nmCid;
    }

    public String getEndExt_codPostal() {
        return null == endExt_codPostal ? "" : endExt_codPostal;
    }

    public void setEndExt_codPostal(String endExt_codPostal) {
        this.endExt_codPostal = endExt_codPostal;
    }

    public String getEvtPgtos_Id() {
        return null == evtPgtos_Id ? "" : evtPgtos_Id;
    }

    public void setEvtPgtos_Id(String evtPgtos_Id) {
        this.evtPgtos_Id = evtPgtos_Id;
    }

    public String getIdeEvento_indRetif() {
        return null == ideEvento_indRetif ? "" : ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return null == ideEvento_nrRecibo ? "" : ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_indApuracao() {
        return null == ideEvento_indApuracao ? "" : ideEvento_indApuracao;
    }

    public void setIdeEvento_indApuracao(String ideEvento_indApuracao) {
        this.ideEvento_indApuracao = ideEvento_indApuracao;
    }

    public String getIdeEvento_perApur() {
        return null == ideEvento_perApur ? "" : ideEvento_perApur;
    }

    public void setIdeEvento_perApur(String ideEvento_perApur) {
        this.ideEvento_perApur = ideEvento_perApur;
    }

    public String getideEvento_indGuia() {
        return null == ideEvento_indGuia ? "" : ideEvento_indGuia;
    }

    public void setideEvento_indGuia(String ideEvento_perApur) {
        this.ideEvento_indGuia = ideEvento_perApur;
    }    
    
    public String getIdeEvento_tpAmb() {
        return null == ideEvento_tpAmb ? "" : ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return null == ideEvento_procEmi ? "" : ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return null == ideEvento_verProc ? "" : ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return null == ideEmpregador_tpInsc ? "" : ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return null == ideEmpregador_nrInsc ? "" : ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeBenef_cpfBenef() {
        return null == ideBenef_cpfBenef ? "" : ideBenef_cpfBenef;
    }

    public void setIdeBenef_cpfBenef(String ideBenef_cpfBenef) {
        this.ideBenef_cpfBenef = ideBenef_cpfBenef;
    }

    public String getDeps_vrDedDep() {
        return null == deps_vrDedDep ? "" : deps_vrDedDep;
    }

    public void setDeps_vrDedDep(String deps_vrDedDep) {
        this.deps_vrDedDep = deps_vrDedDep;
    }

    public static class RetPgtoTot {

        private String retPgtoTot_codRubr;
        private String retPgtoTot_ideTabRubr;
        private String retPgtoTot_qtdRubr;
        private String retPgtoTot_fatorRubr;
        private String retPgtoTot_vrUnit;
        private String retPgtoTot_vrRubr;
        private List<PenAlim> retPgtoTot_penAlim;

        @Override
        public int hashCode() {
            int hash = 5;
            hash = 53 * hash + Objects.hashCode(this.retPgtoTot_codRubr);
            hash = 53 * hash + Objects.hashCode(this.retPgtoTot_ideTabRubr);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final RetPgtoTot other = (RetPgtoTot) obj;
            if (!Objects.equals(this.retPgtoTot_codRubr, other.retPgtoTot_codRubr)) {
                return false;
            }
            if (!Objects.equals(this.retPgtoTot_ideTabRubr, other.retPgtoTot_ideTabRubr)) {
                return false;
            }
            return true;
        }

        public String getRetPgtoTot_codRubr() {
            return null == retPgtoTot_codRubr ? "" : retPgtoTot_codRubr;
        }

        public void setRetPgtoTot_codRubr(String retPgtoTot_codRubr) {
            this.retPgtoTot_codRubr = retPgtoTot_codRubr;
        }

        public String getRetPgtoTot_ideTabRubr() {
            return null == retPgtoTot_ideTabRubr ? "" : retPgtoTot_ideTabRubr;
        }

        public void setRetPgtoTot_ideTabRubr(String retPgtoTot_ideTabRubr) {
            this.retPgtoTot_ideTabRubr = retPgtoTot_ideTabRubr;
        }

        public String getRetPgtoTot_qtdRubr() {
            return null == retPgtoTot_qtdRubr ? "" : retPgtoTot_qtdRubr;
        }

        public void setRetPgtoTot_qtdRubr(String retPgtoTot_qtdRubr) {
            this.retPgtoTot_qtdRubr = retPgtoTot_qtdRubr;
        }

        public String getRetPgtoTot_fatorRubr() {
            return null == retPgtoTot_fatorRubr ? "" : retPgtoTot_fatorRubr;
        }

        public void setRetPgtoTot_fatorRubr(String retPgtoTot_fatorRubr) {
            this.retPgtoTot_fatorRubr = retPgtoTot_fatorRubr;
        }

        public String getRetPgtoTot_vrUnit() {
            return null == retPgtoTot_vrUnit ? "" : retPgtoTot_vrUnit;
        }

        public void setRetPgtoTot_vrUnit(String retPgtoTot_vrUnit) {
            this.retPgtoTot_vrUnit = retPgtoTot_vrUnit;
        }

        public String getRetPgtoTot_vrRubr() {
            return null == retPgtoTot_vrRubr ? "" : retPgtoTot_vrRubr;
        }

        public void setRetPgtoTot_vrRubr(String retPgtoTot_vrRubr) {
            this.retPgtoTot_vrRubr = retPgtoTot_vrRubr;
        }

        public List<PenAlim> getRetPgtoTot_penAlim() {
            return retPgtoTot_penAlim;
        }

        public void setRetPgtoTot_penAlim(List<PenAlim> retPgtoTot_penAlim) {
            this.retPgtoTot_penAlim = retPgtoTot_penAlim;
        }
    }

    public static class PenAlim {

        private String penAlim_cpfBenef;
        private String penAlim_dtNasctoBenef;
        private String penAlim_nmBenefic;
        private String penAlim_vlrPensao;
        private String penAlim_matr;

        public String getPenAlim_matr() {
            return penAlim_matr;
        }

        public void setPenAlim_matr(String penAlim_matr) {
            this.penAlim_matr = penAlim_matr;
        }

        @Override
        public int hashCode() {
            int hash = 5;
            hash = 79 * hash + Objects.hashCode(this.penAlim_matr);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final PenAlim other = (PenAlim) obj;
            if (!Objects.equals(this.penAlim_matr, other.penAlim_matr)) {
                return false;
            }
            return true;
        }

        public String getPenAlim_cpfBenef() {
            return null == penAlim_cpfBenef ? "" : penAlim_cpfBenef;
        }

        public void setPenAlim_cpfBenef(String penAlim_cpfBenef) {
            this.penAlim_cpfBenef = penAlim_cpfBenef;
        }

        public String getPenAlim_dtNasctoBenef() {
            return null == penAlim_dtNasctoBenef ? "" : penAlim_dtNasctoBenef;
        }

        public void setPenAlim_dtNasctoBenef(String penAlim_dtNasctoBenef) {
            this.penAlim_dtNasctoBenef = penAlim_dtNasctoBenef;
        }

        public String getPenAlim_nmBenefic() {
            return null == penAlim_nmBenefic ? "" : penAlim_nmBenefic;
        }

        public void setPenAlim_nmBenefic(String penAlim_nmBenefic) {
            this.penAlim_nmBenefic = penAlim_nmBenefic;
        }

        public String getPenAlim_vlrPensao() {
            return null == penAlim_vlrPensao ? "" : penAlim_vlrPensao;
        }

        public void setPenAlim_vlrPensao(String penAlim_vlrPensao) {
            this.penAlim_vlrPensao = penAlim_vlrPensao;
        }
    }

    public static class InfoPgtoParc {

        private String infoPgtoParc_matricula;
        private String infoPgtoParc_codRubr;
        private String infoPgtoParc_ideTabRubr;
        private String infoPgtoParc_qtdRubr;
        private String infoPgtoParc_fatorRubr;
        private String infoPgtoParc_vrUnit;
        private String infoPgtoParc_vrRubr;

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 89 * hash + Objects.hashCode(this.infoPgtoParc_codRubr);
            hash = 89 * hash + Objects.hashCode(this.infoPgtoParc_ideTabRubr);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final InfoPgtoParc other = (InfoPgtoParc) obj;
            if (!Objects.equals(this.infoPgtoParc_codRubr, other.infoPgtoParc_codRubr)) {
                return false;
            }
            if (!Objects.equals(this.infoPgtoParc_ideTabRubr, other.infoPgtoParc_ideTabRubr)) {
                return false;
            }
            return true;
        }

        public String getInfoPgtoParc_matricula() {
            return null == infoPgtoParc_matricula ? "" : infoPgtoParc_matricula;
        }

        public void setInfoPgtoParc_matricula(String infoPgtoParc_matricula) {
            this.infoPgtoParc_matricula = infoPgtoParc_matricula;
        }

        public String getInfoPgtoParc_codRubr() {
            return null == infoPgtoParc_codRubr ? "" : infoPgtoParc_codRubr;
        }

        public void setInfoPgtoParc_codRubr(String infoPgtoParc_codRubr) {
            this.infoPgtoParc_codRubr = infoPgtoParc_codRubr;
        }

        public String getInfoPgtoParc_ideTabRubr() {
            return null == infoPgtoParc_ideTabRubr ? "" : infoPgtoParc_ideTabRubr;
        }

        public void setInfoPgtoParc_ideTabRubr(String infoPgtoParc_ideTabRubr) {
            this.infoPgtoParc_ideTabRubr = infoPgtoParc_ideTabRubr;
        }

        public String getInfoPgtoParc_qtdRubr() {
            return null == infoPgtoParc_qtdRubr ? "" : infoPgtoParc_qtdRubr;
        }

        public void setInfoPgtoParc_qtdRubr(String infoPgtoParc_qtdRubr) {
            this.infoPgtoParc_qtdRubr = infoPgtoParc_qtdRubr;
        }

        public String getInfoPgtoParc_fatorRubr() {
            return null == infoPgtoParc_fatorRubr ? "" : infoPgtoParc_fatorRubr;
        }

        public void setInfoPgtoParc_fatorRubr(String infoPgtoParc_fatorRubr) {
            this.infoPgtoParc_fatorRubr = infoPgtoParc_fatorRubr;
        }

        public String getInfoPgtoParc_vrUnit() {
            return null == infoPgtoParc_vrUnit ? "" : infoPgtoParc_vrUnit;
        }

        public void setInfoPgtoParc_vrUnit(String infoPgtoParc_vrUnit) {
            this.infoPgtoParc_vrUnit = infoPgtoParc_vrUnit;
        }

        public String getInfoPgtoParc_vrRubr() {
            return null == infoPgtoParc_vrRubr ? "" : infoPgtoParc_vrRubr;
        }

        public void setInfoPgtoParc_vrRubr(String infoPgtoParc_vrRubr) {
            this.infoPgtoParc_vrRubr = infoPgtoParc_vrRubr;
        }
    }

    public static class DetRubrFer {

        private String detRubrFer_codRubr;
        private String detRubrFer_ideTabRubr;
        private String detRubrFer_qtdRubr;
        private String detRubrFer_fatorRubr;
        private String detRubrFer_vrUnit;
        private String detRubrFer_vrRubr;

        private List<PenAlim> detRubrFer_penAlim;

        @Override
        public int hashCode() {
            int hash = 7;
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DetRubrFer other = (DetRubrFer) obj;
            if (!Objects.equals(this.detRubrFer_codRubr, other.detRubrFer_codRubr)) {
                return false;
            }
            if (!Objects.equals(this.detRubrFer_ideTabRubr, other.detRubrFer_ideTabRubr)) {
                return false;
            }
            return true;
        }

        public String getDetRubrFer_codRubr() {
            return null == detRubrFer_codRubr ? "" : detRubrFer_codRubr;
        }

        public void setDetRubrFer_codRubr(String detRubrFer_codRubr) {
            this.detRubrFer_codRubr = detRubrFer_codRubr;
        }

        public String getDetRubrFer_ideTabRubr() {
            return null == detRubrFer_ideTabRubr ? "" : detRubrFer_ideTabRubr;
        }

        public void setDetRubrFer_ideTabRubr(String detRubrFer_ideTabRubr) {
            this.detRubrFer_ideTabRubr = detRubrFer_ideTabRubr;
        }

        public String getDetRubrFer_qtdRubr() {
            return null == detRubrFer_qtdRubr ? "" : detRubrFer_qtdRubr;
        }

        public void setDetRubrFer_qtdRubr(String detRubrFer_qtdRubr) {
            this.detRubrFer_qtdRubr = detRubrFer_qtdRubr;
        }

        public String getDetRubrFer_fatorRubr() {
            return null == detRubrFer_fatorRubr ? "" : detRubrFer_fatorRubr;
        }

        public void setDetRubrFer_fatorRubr(String detRubrFer_fatorRubr) {
            this.detRubrFer_fatorRubr = detRubrFer_fatorRubr;
        }

        public String getDetRubrFer_vrUnit() {
            return null == detRubrFer_vrUnit ? "" : detRubrFer_vrUnit;
        }

        public void setDetRubrFer_vrUnit(String detRubrFer_vrUnit) {
            this.detRubrFer_vrUnit = detRubrFer_vrUnit;
        }

        public String getDetRubrFer_vrRubr() {
            return null == detRubrFer_vrRubr ? "" : detRubrFer_vrRubr;
        }

        public void setDetRubrFer_vrRubr(String detRubrFer_vrRubr) {
            this.detRubrFer_vrRubr = detRubrFer_vrRubr;
        }

        public List<PenAlim> getDetRubrFer_penAlim() {
            return detRubrFer_penAlim;
        }

        public void setDetRubrFer_penAlim(List<PenAlim> detRubrFer_penAlim) {
            this.detRubrFer_penAlim = detRubrFer_penAlim;
        }
    }

    public static class DetPgtoAnt {

        private String detPgtoAnt_codCateg;
        private List<InfoPgtoAnt> detPgtoAnt_infoPgtoAnt;

        public String getDetPgtoAnt_codCateg() {
            return null == detPgtoAnt_codCateg ? "" : detPgtoAnt_codCateg;
        }

        public void setDetPgtoAnt_codCateg(String detPgtoAnt_codCateg) {
            this.detPgtoAnt_codCateg = detPgtoAnt_codCateg;
        }

        public List<InfoPgtoAnt> getDetPgtoAnt_infoPgtoAnt() {
            return detPgtoAnt_infoPgtoAnt;
        }

        public void setDetPgtoAnt_infoPgtoAnt(List<InfoPgtoAnt> detPgtoAnt_infoPgtoAnt) {
            this.detPgtoAnt_infoPgtoAnt = detPgtoAnt_infoPgtoAnt;
        }
    }

    public static class InfoPgtoAnt {

        private String infoPgtoAnt_tpBcIRRF;
        private String infoPgtoAnt_vrBcIRRF;

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 31 * hash + Objects.hashCode(this.infoPgtoAnt_tpBcIRRF);
            hash = 31 * hash + Objects.hashCode(this.infoPgtoAnt_vrBcIRRF);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final InfoPgtoAnt other = (InfoPgtoAnt) obj;
            if (!Objects.equals(this.infoPgtoAnt_tpBcIRRF, other.infoPgtoAnt_tpBcIRRF)) {
                return false;
            }
            if (!Objects.equals(this.infoPgtoAnt_vrBcIRRF, other.infoPgtoAnt_vrBcIRRF)) {
                return false;
            }
            return true;
        }

        public String getInfoPgtoAnt_tpBcIRRF() {
            return null == infoPgtoAnt_tpBcIRRF ? "" : infoPgtoAnt_tpBcIRRF;
        }

        public void setInfoPgtoAnt_tpBcIRRF(String infoPgtoAnt_tpBcIRRF) {
            this.infoPgtoAnt_tpBcIRRF = infoPgtoAnt_tpBcIRRF;
        }

        public String getInfoPgtoAnt_vrBcIRRF() {
            return null == infoPgtoAnt_vrBcIRRF ? "" : infoPgtoAnt_vrBcIRRF;
        }

        public void setInfoPgtoAnt_vrBcIRRF(String infoPgtoAnt_vrBcIRRF) {
            this.infoPgtoAnt_vrBcIRRF = infoPgtoAnt_vrBcIRRF;
        }

    }
    
    public static class PlanSaude {

        private String planSaude_cnpjOper;
        private String planSaude_vlrSaudeTit;

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 31 * hash + Objects.hashCode(this.planSaude_cnpjOper);
            hash = 31 * hash + Objects.hashCode(this.planSaude_cnpjOper);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final InfoPgtoAnt other = (InfoPgtoAnt) obj;
            if (!Objects.equals(this.planSaude_cnpjOper, other.infoPgtoAnt_tpBcIRRF)) {
                return false;
            }
            if (!Objects.equals(this.planSaude_vlrSaudeTit, other.infoPgtoAnt_vrBcIRRF)) {
                return false;
            }
            return true;
        }

        public String getPlanSaude_cnpjOper() {
            return null == planSaude_cnpjOper ? "" : planSaude_cnpjOper;
        }

        public void setPlanSaude_cnpjOper(String planSaude_cnpjOper) {
            this.planSaude_cnpjOper = planSaude_cnpjOper;
        }

        public String getPlanSaude_vlrSaudeTit() {
            return null == planSaude_vlrSaudeTit ? "" : planSaude_vlrSaudeTit;
        }

        public void setPlanSaude_vlrSaudeTit(String planSaude_vlrSaudeTit) {
            this.planSaude_vlrSaudeTit = planSaude_vlrSaudeTit;
        }

    }
    
}

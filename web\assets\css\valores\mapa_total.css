/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 15-May-2020, 10:16:32
    Author     : SAS<PERSON>ichard
*/

.ui-button-icon-left{
    position:absolute;
    z-index: 999 !important;
    width:40px !important;
    top:18px !important;
}

.ui-button-icon-left::before {
    font-size: 14pt !important;
    margin-left:3px !important;
}

.ui-lightbox-content-wrapper,
.ui-lightbox-content,
.ui-lightbox-content iframe{
    width:100% !important;
    height:100% !important;
    background-color: #ECF0F5 !important;
}

[id*="btAbrirModal"]{
    text-align:center !important;
    /*width:190px !important;*/
    width:100% !important;
    margin-top:12px;
    box-shadow:1px 1px 3px #666!important;
    font-size:12pt !important;
    padding:  0px !important;
}

div[id*="lighboxListaRotas"] .fa-list{
    top: 18px !important;
}

div[id*="lighboxListaRotas"] span[class*="ui-button-text"]{
    margin-top: -2px !important;
}

#btFechar{
    position: absolute;
    right: 0px;
    top:-20px;
    width: 75px !important;
    background-color: red !important;
    color:#FFF !important;
    font-size:8pt !important;
    font-weight: bold !important;
    text-align: center !important;
    border-radius: 3px !important;
    border:thin solid darkred !important;
    cursor:pointer;
}


.FundoMapa{
    height: 100% !important;
    background-color: #FFF;
    border: thin solid #CCC;
    padding:10px 0px 10px 15px !important;
    border-radius: 4px !important;
    border-top:4px solid #3C8DBC !important;
}

#main{
    background-color:#ECF0F5 !important;
    padding:16px 14px 14px 14px !important;
    overflow:hidden !important;
    height: calc(100% - 0px) !important
}

.Rotas{
    height:calc(100% - 0px) !important;
    max-height:calc(100% - 0px) !important;
    min-height:calc(100% - 0px) !important;
    overflow:hidden !important;
    padding:0px !important;
    width:100% !important;
    border-radius:4px !important;
    margin:0px !important;
    background-color: #FFF !important;
    /*box-shadow:2px 2px 3px #EEE;*/
}

#legend{
    padding:0px 3px 3px 3px !important;
    border:thin solid #DDD !important;
    overflow:auto !important;
    border-radius:4px 4px 0px 0px !important;
}

#legend div{
    width:100% !important;
    padding-top:6px !important;
    padding-left:8px !important;
    padding-right:8px !important;
    margin-bottom:10px !important;
}

.botao {
    display: inline-block !important;
    color: #fff !important;
    width: 125px !important;
    height: 40px !important;
    padding: 0 20px !important;
    background: #3479A3 !important;
    border-radius: 5px !important;
    outline: none !important;
    border: none !important;
    cursor: pointer !important;
    text-align: center !important;
    transition: all 0.2s linear !important;
    letter-spacing: 0.05em !important;
}

a:hover,
a:focus {
    color:#3479A3 !important;
}

#firstHeading{
    color: #3479A3;
    margin: 0px!important;
    line-height:25px !important;
}

.tableVeiculo {
    margin:-20px 0px 0px 0px !important;
    border-collapse: collapse;
}

.tableVeiculo td, .tableVeiculo th {
    border: 1px solid #ddd;
    padding: 5px;
}

.tableVeiculo tr:nth-child(even){background-color: #f2f2f2;}

.tableVeiculo tr:hover {background-color: #88BBD9;}

.tableVeiculo th {
    padding-bottom: 4px;
}

.gm-style-iw p{
    line-height: 12px !important;
}

.gm-style-iw{
    min-height: 330px !important;
    min-width: 410px !important;
}

.gm-style-iw div:first-child{
    padding:0px 4px 0px 0px !important;
    min-height: 170px !important;
    overflow:hidden !important;
}

.CheckBoxRota{
    width:20px !important;
    text-align: right !important;
    vertical-align: top !important;
}

.CheckBoxRota input{
    float:right;
    top:0px !important;
    margin-top:3px !important;
    cursor: pointer !important;
}

#legend table{
    width:100% !important;
}

.InfoColetaEntrega{
    font-size:8pt !important;
    text-align:center;
    width:120px !important;
    color:#FFF !important;
    background-color:#000 !important;
    font-weight:bold !important;
    padding:1px 4px 0px 4px !important;
    height:17px !important;
    border-radius:4px !important;
    box-shadow:2px 2px 4px #CCC !important;
}

.InfoColetaEntrega[tipo^="E"]{
    background-color:forestgreen !important;
    border:thin solid darkgreen;
}

.InfoColetaEntrega[tipo^="C"],
.InfoColetaEntrega[tipo^="R"]{
    background-color:red !important;
    border:thin solid darkred;
}

.DadosInfo{
    padding:0px !important;
    width:100%;
    border: thin solid #CCC;
}

.DadosInfo table{
    border-spacing:2px !important;
    border-collapse: separate;
    width:100%;
}

.DadosInfo table thead tr th{
    background-color:#303030 !important;
    color:#FFF !important;
    text-align:center !important;
    padding:3px 1px 3px 1px !important;
    font-weight:500 !important;
    height:20px !important;
}

.DadosInfo table tr td{
    border: thin solid #CCC;
    padding:3px !important;
    text-align:center !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {
    #firstHeading{
        font-size:12pt !important;
        line-height:15px !important;
    }

    #mapGoogle{
        height:430px !important;
    }

    #divFooterTimer{
        display:none !important;
    }

    .gm-style-iw{
        min-height: 200px !important;
        max-height: 200px !important;
        min-width: 300px !important;
        overflow-x:hidden !important;
        overflow-y:auto !important;
        font-size:8pt !important;
        padding-bottom:0px !important;
        margin:0px !important;
    }

    .gm-style-iw div:first-child{
        padding:0px 0px 0px 0px !important;
        min-height: 305px !important;
        max-height: 305px !important;
        min-width: 280px !important;
        max-width: 280px !important;
        overflow:hidden !important;
        overflow-x:auto !important;
        font-size:8pt !important;
        padding-bottom:8px !important;
        margin:0px !important;
    }

    .gm-style-iw p{
        max-width:400px;
        white-space: nowrap;
        text-align:left !important;
        height: 10px !important;
        line-height: 10px !important;
        padding:0px !important;
        margin:4px 0px 0px 0px !important;
    }

    .tableVeiculo{
        margin-top:0px !important;
    }

    #divQuadroResumo input[type="checkbox"] + label{
        font-size:8pt;
    }

    .InfoColetaEntrega{
        padding-top:2px !important;
        margin-top:-50px !important;
    }
}

.AbrirFiltros{
    position: absolute;
    z-index:1 !important;
    background-color:#000;
    color:#FFF;
    writing-mode: vertical-rl;
    text-orientation: upright !important;
    padding:10px 6px 10px 6px !important;
    top:75px;
    border-radius: 0px 8px 8px 0px;
    box-shadow: 1px 1px 2px #666;
    cursor: pointer;
    font-family: 'Open Sans', sans-serif;
    font-size:10pt !important;
    text-align:center !important;
}

#divQuadroResumo{
    position:absolute !important;
    width:290px !important;
    top:70px !important;
    z-index:1 !important;
    height: 150px;
}

#divQuadroResumo .FecharFiltros{
    position:absolute;
    right:-10px;
    top:-15px;
    color:#FFF;
    background-color:#000;
    width:31px;
    height: 31px;
    border-radius:50%;
    text-align: center;
    cursor: pointer;
    padding-top: 4px;
    padding-left: 1px;
    font-size:16pt !important;
    box-shadow:1px 1px 2px #666;
}

#divQuadroResumo .ItemResumo{
    background-color: rgba(211, 221, 228, 0.8) !important;
    width:100% !important;
    border-radius:12px;
    padding:1px 1px 0px 1px !important;
    box-shadow:1px  1px 3px #666;
    border-radius:4px;
}

#divQuadroResumo input[type="checkbox"]{
    padding:0px !important;
    background-color:black;
    border:thin solid #AAA !important;
    border-radius:2px !important;
    position:absolute;
    top: -6px;
}

#divQuadroResumo input[type="checkbox"] + label{
    font-family:'Open Sans', sans-serif !important;
    font-size:10pt;
    font-weight:600;
    color:#404040;
    margin:0px !important;
    padding:0px 0px 0px 16px !important;
    cursor:pointer;
    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
    line-height:10px !important;
    max-height:10px !important;
}

#divQuadroResumo .Item,
#divQuadroResumo .ItemZebrado{
    position:relative;
    width:calc(100% - 12px) !important;
    margin-left: 6px !important;
    padding:2px 8px 0px 8px !important;
    border-radius:2px;
}

#divQuadroResumo .ItemZebrado{
    background-color:rgba(187, 187, 187, 0.5) !important;
}

#divQuadroResumo #lblTituloFiltro{
    font-family:'Open Sans', sans-serif !important;
    font-size:11pt !important;
    font-weight:600 !important;
    color:#000 !important;
    padding:5px 0px 3px 10px !important;
    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
    border:none !important;
}

#divQuadroResumo .QdeStatus{
    position: absolute;
    width:40px;
    height:10px !important;
    border-radius:6px;
    right:6px;
    top: 9px;
    font-size:8pt !important;
    text-align:center;
    color:#FFF;
    font-weight:bold;
    padding:0x !important;
}

#divQuadroResumo .QdeStatus[cor="R"]{
    background-color:#F00;
}

#divQuadroResumo .QdeStatus[cor="G"]{
    background-color: #006633;
}

#divQuadroResumo .QdeStatus[cor="B"]{
    background-color:#2c58b1;
}

#divQuadroResumo .QdeStatus[cor="C"]{
    background-color:#505050;
}

.LightBox{
    position:absolute;
    width:100%;
    height:100%;
    background-color:rgba(0,0,0,0.7);
    z-index:9999;
}

.divFrame{
    position:absolute;
    width:92%;
    height: 92%;
    background-color: #FFF;
    border-radius:10px;
    z-index:99999;
    top:0;
    right:0;
    bottom:0;
    left:0;
    margin:auto;
    padding:0px !important;
    border:1px solid #333 !important;
    border-top:6px solid #3C8DBC !important;
    box-shadow:1px 1px 4px rgba(0,0,0,0.8);
}

.divFrame iframe{
    width:100%;
    height:100%;
    padding:0px;
    margin:0px;
    border:none !important;
    border-radius:10px;
}

.BotaoResumoQuadro{
    text-align:center !important;
    /*width:190px !important;*/
    width:100% !important;
    margin-top:12px;
    box-shadow:1px 1px 3px #666!important;
    font-size:12pt !important;
    padding:  0px !important;
}

div[id*="lightboxListaRotas_panel"]{
    width:85% !important;
    height:85% !important;
    top: 0 !important;
    bottom: 0 !important;
    margin: auto !important;
}

div[id*="lightboxListaPedidos_panel"]{
    width:85% !important;
    height:85% !important;
    top: 0 !important;
    bottom: 0 !important;
    margin: auto !important;
}

div[id*="lightboxListaPedidos"] .fa-list{
    top: 18px !important;
}

div[id*="lightboxListaRotas"] .fa-list{
    top: 18px !important;
}

div[id*="lightboxListaRotas"] span[class*="ui-button-text"]{
    margin-top: -2px !important;
}

div[id*="lightboxListaRotas"] span[class*="ui-button-text"]{
    margin-top: -2px !important;
}

.formPedido{
    border:none;
    color:#000;
    background-color:transparent;
    width:100% !important;
    text-align:center !important;
}


.cboPedidoRotas{
    min-width:100% !important;
    width:100% !important;
    max-width:100% !important;
    border:none !important;
    margin:0px !important;
    padding:0px 6px 0px 6px !important
}

[id*="hora1O_panel"] {
    z-index:999999 !important;
}

#btFecharDetalhePedido{
    display:none !important;
}

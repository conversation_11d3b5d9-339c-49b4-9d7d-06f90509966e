package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class PessoaLogin {

    private BigDecimal Codigo;
    private String BancoDados;
    private BigDecimal CodPessoaBD;
    private String Nivel;
    private String Operador;
    private String Dt_alter;
    private String Hr_Alter;

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public String getNivel() {
        return Nivel;
    }

    public void setNivel(String Nivel) {
        this.Nivel = Nivel;
    }

    public void setCodigo(BigDecimal Codigo) {
        try {
            this.Codigo = Codigo;
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public String getBancoDados() {
        return BancoDados;
    }

    public void setBancoDados(String BancoDados) {
        this.BancoDados = BancoDados;
    }

    public BigDecimal getCodPessoaBD() {
        return CodPessoaBD;
    }

    public void setCodPessoaBD(BigDecimal CodPessoaBD) {
        try {
            this.CodPessoaBD = CodPessoaBD;
        } catch (Exception e) {
            this.CodPessoaBD = new BigDecimal("0");
        }
    }

    public void setCodPessoaBD(String CodPessoaBD) {
        try {
            this.CodPessoaBD = new BigDecimal(CodPessoaBD);
        } catch (Exception e) {
            this.CodPessoaBD = new BigDecimal("0");
        }
    }

    public BigDecimal getCodPessoa() {
        return CodPessoaBD;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoaBD = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoaBD = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 89 * hash + Objects.hashCode(this.Codigo);
        hash = 89 * hash + Objects.hashCode(this.BancoDados);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PessoaLogin other = (PessoaLogin) obj;
        if (!Objects.equals(this.BancoDados, other.BancoDados)) {
            return false;
        }
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }
}

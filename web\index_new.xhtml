<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <!--<link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/index.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/stylePage.css" rel="stylesheet" />-->
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <link rel="icon" type="image/png" href="assets/novo_layout/images/icons/favicon.ico"/>
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/bootstrap/css/bootstrap.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/fonts/font-awesome-4.7.0/css/font-awesome.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/fonts/Linearicons-Free-v1.0.0/icon-font.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/animate/animate.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/css-hamburgers/hamburgers.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/vendor/select2/select2.min.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/css/util.css" />
            <link rel="stylesheet" type="text/css" href="assets/novo_layout/css/main.css" />
            <style>          
                /* .DataGrid tbody tr td{
                     text-align: center !important;
                 }
 
                 @media only screen and (max-width: 700px) and (min-width: 10px) {                                      
                     .rodape-links img{
                         width:10px !important;
                         height:10px !important;
                         margin-left:-14px !important;
                         position:absolute
 
 
                     }
                     .rodape-links li{
                         font-size: 8pt !important;
                         text-align:center !important;
                         padding-left:15px !important;
                     }
 
                     .rodape-links li:nth-child(3){
                         display:none;
                     }
 
                     .rodape-links{
                         text-align:center !important;
                         white-space:nowrap !important;
                     }
 
                     #login{
                         margin-top:-70px !important;
                     }
                 }
                
                #h-wrapper{
                    overflow:hidden !important; background: url('assets/img/st_back.png') no-repeat top center; background-size: 62%; position:fixed !important; bottom:0 !important;min-height:550px !important; height:100%; left:0; right:0; margin: auto;
                 }*/
                
                @media (max-width: 450px) {
                    #imgLogosRodape{
                        height: 40px;
                    }
                    
                    [id*="imgBandeiras"]{
                        height: 30px;
                    }
                    
                    [id*="login"] .p-b-15 > img{
                        height: 60px;
                    }
                }
            </style>    
        </h:head>
        <h:body id="h" style="overflow:hidden !important">
            <f:metadata>
                <f:viewAction action="#{login.limparSession}" immediate="true"/>
                <f:viewParam name="empresa" value="#{login.cli}"/>

                <f:viewParam name="Login" value="#{login.portalLogin}"/>
                <f:viewParam name="Email" value="#{login.portalEmail}"/>
                <f:viewParam name="Senha" value="#{login.portalSenha}"/>
                <f:viewAction action="#{login.onRefresh}"/>
            </f:metadata>

            <script>
                var sheets = document.styleSheets;
                console.log(document.styleSheets[0]);
                var sheet = document.styleSheets[6];
                function gup(name)
                {
                name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
                var regexS = "[\\?&amp;]" + name + "=([^&amp;#]*)";
                var regex = new RegExp(regexS);
                var results = regex.exec(window.location.href);
                if (results == null) {
                if (top.location.href.indexOf('spm.cash') > - 1)
                        top.location.href = 'https://spm.cashctr.com/MSL/msl/index.xhtml';
                return "";
                } else if (results[1] == 'SessaoExpirada')
                {
                var IdiomaSel = #{localeController.number};
                if (top.location.href.indexOf('spm.cash') > - 1) {
                switch (IdiomaSel.toString()) {
                case '1':
                        case '2':
                        alert("Por razones de seguridad, su sesión ha expirado. Por favor inicie sesión nuevamente.");
                break;
                case '3':
                        alert("For security reasons, your session has expired. Please log in again.");
                break;
                }
                } else {
                switch (IdiomaSel.toString()) {
                case '1':
                        alert("Por motivos de segurança, sua sessão expirou. Por favor, faça login novamente.");
                break;
                case '2':
                        alert("Por razones de seguridad, su sesión ha expirado. Por favor inicie sesión nuevamente.");
                break;
                case '3':
                        alert("For security reasons, your session has expired. Please log in again.");
                break;
                }
                }

                if (top.location.href.indexOf('spm.cash') > - 1)
                        top.location.href = 'https://spm.cashctr.com/MSL/msl/index.xhtml';
                else
                        top.location.href = 'index.xhtml'
                } else if (results[1] == 'CadastroSucesso')
                {
                alert("Senha cadastrada com sucesso. Efetue login digitando empresa@matricula e a senha cadastrada anteriormente.");
                if (top.location.href.indexOf('spm.cash') > - 1)
                        top.location.href = 'https://spm.cashctr.com/MSL/msl/index.xhtml';
                } else if (results[1] == 'ErroValidacao')
                {
                alert("Erro de validação. Tente novamente.");
                if (top.location.href.indexOf('spm.cash') > - 1)
                        top.location.href = 'https://spm.cashctr.com/MSL/msl/index.xhtml';
                } else {
                if (top.location.href.indexOf('spm.cash') > - 1)
                        top.location.href = 'https://spm.cashctr.com/MSL/msl/index.xhtml';
                return "_" + results[1];
                }
                }
                var sessao = gup('msg');
            </script>

            <p:growl id="msgs" widgetVar="msgs" />

            <div class="limiter">
                <div class="container-login100">
                    <div class="wrap-login100 p-l-50 p-r-50 p-t-37 p-b-10">
                        <h:form class="form-inline login100-form validate-form" id="login">
                            <span class="login100-form-title p-b-15">
                                <img src="https://mobile.sasw.com.br:9091/satmobile/logos/logo_#{login.cli eq null or login.cli eq ''? 'SATMOB':login.cli}.png" alt="logocliente" style="max-height: 80px" />
                            </span>

                            <span class="login100-form-title p-b-25"></span>

                            <div class="text-center w-full p-t-22 p-b-12"  >
                                <span class="login100-form-title p-b-15">
                                    <img style="cursor: pointer" src="assets/novo_layout/images/logo_sistemacorporativo.jpg" alt="logo" onclick="Redirect('login.xhtml', 'SC', '#{login.cli}')"/>
                                </span>
                            </div>

                            <div class="text-center w-full p-t-22 p-b-12">
                                <span class="login100-form-title p-b-15">
                                    <img style="cursor: pointer" src="assets/novo_layout/images/logo_portaldocolaborador.jpg" alt="logo" onclick="Redirect('login.xhtml', 'PC', '#{login.cli}')"/>
                                </span>
                            </div>

                            <div class="text-center w-full p-t-22 p-b-12">
                                <span class="login100-form-title p-b-15">
                                    <img style="cursor: pointer" src="assets/novo_layout/images/logo_transportedevalores.jpg" alt="logo" onclick="RedirectEgtv('#{login.cli}')"/>
                                </span>
                            </div>


                            <div class="text-center w-full p-t-42 p-b-22">
                                <span class="txt1">
                                    <a href="https://gruposas.com.br" target="_blank"><img id="imgLogosRodape" src="https://gruposas.com.br/wp-content/uploads/2021/09/logos_satmobsasw.png" height="50" /></a>
                                </span>

                                <h:commandLink actionListener="#{localeController.increment}" 
                                               action="#{localeController.getLocales}">
                                    <p:graphicImage url="assets/img/#{localeController.number}.png" height="40" id="imgBandeiras" />
                                </h:commandLink>
                            </div>
                        </h:form>
                    </div>
                </div>
            </div>

            <script src="assets/novo_layout/vendor/jquery/jquery-3.2.1.min.js"></script>
            <script src="assets/novo_layout/vendor/bootstrap/js/popper.js"></script>
            <script src="assets/novo_layout/vendor/bootstrap/js/bootstrap.min.js"></script>
            <script src="assets/novo_layout/vendor/select2/select2.min.js"></script>
            <script src="assets/novo_layout/js/main.js"></script>
            <script type="text/javascript">
                // <![CDATA[
                function Redirect(url, tipo, param){
                    let _url = url + '?tipo=' + tipo;
                    
                    if (null != param && param != ''){
                        _url += '&empresa='+param;
                    }
                    
                    top.location.href = _url;
               }
               
               function RedirectEgtv(param){
                   if (null != param && param != ''){
                       if(param.toUpperCase() == 'SATBRASIFORT'){
                           top.location.href = 'gtve/brasifort/index.xhtml';
                       }
                       else{
                           top.location.href = 'gtve/index.xhtml?empresa=' + param;    
                       }
                   }
                   else{
                       top.location.href = 'gtve/index.xhtml';
                   }
               }
               // ]]>
            </script>


            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class QueueGuias {

    private BigDecimal Sequencia;
    private int Parada;
    private BigDecimal Guia;
    private String Serie;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the sequencia
     */
    public BigDecimal getSequencia() {
        return Sequencia;
    }

    /**
     * @param sequencia the sequencia to set
     */
    public void setSequencia(String sequencia) {
        try {
            this.Sequencia = new BigDecimal(sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    /**
     * @return the Parada
     */
    public int getParada() {
        return Parada;
    }

    /**
     * @param Parada the Parada to set
     */
    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    /**
     * @return the Guia
     */
    public BigDecimal getGuia() {
        return Guia;
    }

    /**
     * @param Guia the Guia to set
     */
    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    /**
     * @return the Serie
     */
    public String getSerie() {
        return Serie;
    }

    /**
     * @param Serie the Serie to set
     */
    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

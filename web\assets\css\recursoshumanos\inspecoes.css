/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
*/
/* 
    Created on : 09-Jun-2020, 12:02:49
    Author     : SASWRichard
*/

@media only screen and (max-width: 700px) and (min-width: 10px) {

    /*  #divDadosFilial,
      #divDadosFilial div,
      .FilialNome,
      .FilialEndereco,
      .FilialBairroCidade{
          min-width:100% !important;
          width:100% !important;
          max-width:100% !important;
          text-align: center !important;
      }*/

    .ui-paginator-top {
        white-space: normal !important;
    }

    .tabela .ui-datatable-scrollable-body {
        flex-grow: 1;
        height: 100% !important;
    }
}


@media only screen and (max-width: 3000px) and (min-width: 701px) {
    .DataGrid [role="columnheader"] > span {
        top: -4px !important;
        position: relative !important;
    }

    .DataGrid{
        width:100% !important;
        border:none !important;
    }

    .DataGrid thead tr th,
    .DataGrid tbody tr td {
        min-width: 120px !important;
        max-width: 120px !important;
    }

    [id$="tabela"] thead > tr > th,
    [id$="tabela"] tbody > tr > td{
        min-width: 90px !important;
        max-width: 90px !important;
    }
}


[id*="exibicaoPstInspecao"] div[class*="col-md"]{
    padding: 5px 5px 0px 5px !important;
}


[id*="formListarInspecoes"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
[id*="formInspecaoItens"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar, 
[id*="formAdicionarInspecao"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar {
    background-color:#FFF !important;
    border-bottom-color: #CCC !important;
}

[id*="formInspecaoItens"] .ui-dialog .ui-dialog-content{
    overflow:hidden !important;
}

html, body{
    max-height:100% !important;
    overflow:hidden !important;
}
#divCorporativo{
    bottom:23px !important;
}

#corporativo {
    max-width: 18vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#corporativo label[ref="lblCheck"]{
    font-size:11px !important;
    min-width:75px !important;
    font-weight:500 !important;
}

.ui-datatable-scrollable-header-box{
    width: 100% !important;
}

.ui-dialog .ui-panel-content {
    height: auto !important;
}

#body {
    height: calc(100% - 40px);
    max-height: 100% !important;
    position: relative;
    display: flex;
    flex-direction: column;
}

#main {
    flex-grow: 1;
}

#formPesquisar .ui-radiobutton {
    background: transparent !important;
}

.ui-datatable-scrollable-body,
.FundoPagina > .ui-panel-content {
    height: 100% !important;
}

.FundoPagina {
    border: thin solid #CCC !important;
    border-top:4px solid #3C8DBC !important;
}

.verticalSpace {
    margin-bottom: 10px;
}









[id*="formBoletimTrabalho"] .ui-state-focus{
    box-shadow: none !important;
    outline: none !important;
}

[id*="TopoPonto"], .Teste{
    border-bottom: thin solid #DDD !important;
}

.EntradaSaida{
    font-size:8pt;
    font-weight:bold !important;
    color:#FFF !important;
    background-color:#666;
    display:block !important;
    min-width:90px;
    padding:2px 6px 2px 6px;
    text-align: center;
    border-radius:3px;
    box-shadow:2px 2px 3px #CCC;
    max-width:calc(100% - 35px) !important;
    white-space:nowrap;
    overflow:hidden !important;
    text-overflow:ellipsis !important;
    text-transform: uppercase !important;
}

.EntradaSaida[ref1="1"],
.EntradaSaida[ref1="1"],
.EntradaSaida[ref1="11"]{
    background-color: forestgreen;
    clear:both !important;
    display: block !important;
    border:thin solid #1D761D;
}

.EntradaSaida[ref1="1"][ref2="2"],
.EntradaSaida[ref1="1"][ref2="4"],
.EntradaSaida[ref1="12"]{
    background-color: red;
    clear:both !important;
    display:block !important;
    border:thin solid #D20000;
}

div[ref="nada"]{
    display:none !important;
}

@media only screen and (max-width: 764px) and (min-width: 10px) {
    div[ref^="botaoRelatorio"]{
        height: calc(50% - 10px) !important;
    }

    div[ref="botaoRelatorioContatos"]{
        margin-top:10px !important;
    }

    .btSalvar{
        max-width:100% !important;
    }

    #PaiMap, #map{
        height: 250px !important;
    }

    #PaidivItensHistorico{
        height: calc(100vh - 600px) !important;
    }
}

@media only screen and (max-width: 700px) and (min-width: 10px) {
    .ui-paginator-current{
        font-size: 8pt !important;
        margin-right:0px !important;
        margin-left:12px !important;
        padding-left:0px !important;
        padding-right:0px !important;
    }
}

div[id*="pnlHTML"] table{
    padding:0px !important;
    border-spacing:0px !important;
}

div[id*="pnlHTML"] table tbody tr td img{
    margin-right:12px;
}

div[id*="pnlHTML"] table tbody tr td span,
div[id*="pnlHTML"] table tbody tr td span strong{
    font-size:8pt !important;
    padding:0px !important;
}

div[id*="pnlHTML"] table tbody tr{
    /*min-height:5px !important;
    height:5px !important;
    max-height:5px !important;
    line-height:5px !important;*/
}

div[id*="pnlHTML"] table tbody tr td{
    padding:0px !important;
    min-height:5px !important;
    height:5px !important;
    max-height:5px !important;
    line-height:5px !important;
    vertical-align: middle !important;
}

div[id*="pnlHTML"] table tbody tr{
    padding:0px !important;
    line-height:5px !important;
    height:5px !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td,
div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr{
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    height:auto !important;
}

div[id*="pnlHTML"] table tbody tr td table tbody tr td span{
    /*width:100px !important;
    white-space:nowrap;
    overflow:hidden !important;
    text-overflow:ellipsis !important;*/
}

div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:first-child td{
    background-color:#3C8DBC !important;
    color:#FFF !important;
    padding-bottom:0px !important;
    height:auto !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td{
    padding-top: 6px !important;
    padding-left: 6px !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(3) td table{
    border:thin solid #BBB !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:nth-child(odd):not(:first-child) td{
    background-color:whitesmoke !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td:first-child{
    text-align:right !important;
    padding-right: 6px !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr td span{
    margin-top: 8px !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(1) td table,
div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr,
div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td{
    vertical-align:middle !important;
}

div[id*="pnlHTML"] table tbody tr:nth-child(1) td table tbody tr td{
    height:10px !important;
    line-height:10px !important;
    max-height:10px !important;
}

div[id*="pnlHTML"][class*="INSP"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td,
div[id*="pnlHTML"][class*="RO"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td,
div[id*="pnlHTML"][class*="SUP"] table tbody tr:nth-child(1) td table tbody tr:nth-child(3) td{
    display:none !important;
}

div[id*="pnlHTML"]{
    height:100% !important;
    overflow:hidden !important;
    overflow-y:auto !important;
}

div[id*="pnlHTMLpai"]{
    height: 436px !important;
}

#divConteudo{
    height:436px !important;
}

.ui-datatable:not([id*="tabelaClienteEW"]) thead th {
    background-color: #3C8DBC !important;
    color: #FFF;
}

div[id*="panelInspecoesDetalhes"]{

}


@media only screen and (max-width: 700px) and (min-width: 10px) {
    div[id*="pnlHTMLpai"][class*="INSP"]{
        height:350px !important;
    }

    div[id*="pnlHTMLpai"][class*="INSP"] div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:not(:first-child) td span,
    div[id*="pnlHTMLpai"][class*="SUP"] div[id*="pnlHTML"] table tbody tr:nth-child(3) td table tbody tr:not(:first-child) td span{
        height:auto !important;
        line-height:initial !important;
        display:inline-block;
        margin:0px !important;

        /*max-height:15px !important;*/
    }

    #divConteudo[class*="INSP"]{
        height:2900px !important;
    }

    div[id*="pnlHTML"]{
        overflow-y:hidden !important;
    }

    [id*="gmap"]{
        height: 295px !important;
    }

    #pnlHTMLpai{
        min-height: 520px !important;
        height: 520px !important;
        max-height: 520px !important;
    }

    [id*="btExpandirMapa"]{
        top: 4px !important;
        right: 13px !important;
        width: 32px !important;
        height: 32px !important;
    }

    [id*="btExpandirMapa"] img{
        margin-top: 4px !important;
        margin-left: 0px !important;
    }

    [id*="cadastrar"]{
        height: calc(100vh - 180px) !important;
    }

    [id*="panelInspecoesDetalhes"]{
        overflow: hidden !important;
        height: auto !important;
    }
}

.quadroResumo{
    padding:0px 8px 0px 0px !important;
    margin-bottom:8px !important;
    height:53px;
}

.quadroResumo div{
    border-radius:0px;
    height:100% !important;
    padding:0px !important;
    position:relative;
    background-color: #FFF;
    box-shadow:1px 0px 4px #BBB;
}

.quadroResumo div[ref="entrada"]{
    color: forestgreen !important;
}

.quadroResumo div[ref="entrada"] label[ref="icon"]{
    background-color:forestgreen;
    color:#FFF;
}

.quadroResumo div[ref="saida"]{
    color:red !important;
}

.quadroResumo div[ref="saida"] label[ref="icon"]{
    background-color:red;
    color:#FFF;
}

.quadroResumo div[ref="inspecao"]{
    color:#b4af00 !important;
}

.quadroResumo div[ref="inspecao"] label[ref="icon"]{
    background-color:#b4af00;
    color:#FFF;
}

.quadroResumo div[ref="relatoriosX"]{
    color:#f29100 !important;
}

.quadroResumo div[ref="relatoriosX"] label[ref="icon"]{
    background-color:#F90;
    color:#FFF;
}

.quadroResumo div[ref="rondas"]{
    color:#8b6dc1 !important;
}

.quadroResumo div[ref="rondas"] label[ref="icon"]{
    background-color:#8b6dc1;
    color:#FFF;
}

.quadroResumo div[ref="outros"]{
    color:#505050 !important;
}

.quadroResumo div[ref="outros"] label[ref="icon"]{
    background-color:#505050;
    color:#FFF;
}

.quadroResumo label[ref="titulo"]{
    font-size:8pt !important;
    font-weight:500 !important;
    width:calc(100% - 60px);
    text-align:center;
    text-transform:uppercase !important;
    height:15px !important;
    float:left;
    margin-top:2px !important;
    max-width:100%;
    overflow:hidden;
    white-space:nowrap;
    text-overflow:ellipsis;
    padding-left:4px !important;
    padding-right:4px !important;
}

.quadroResumo label[ref="icon"]{
    float:left;
    height:53px;
    width:60px;
    margin:0px !important;
}

.quadroResumo label[ref="valor"]{
    font-size:20pt !important;
    font-weight:500 !important;
    width:calc(100% - 60px);
    text-align:center;
    left:0px;
    height:55px;
    margin-top: -5px !important;
    float:right !important;
    max-width:100%;
    overflow:hidden;
    white-space:nowrap;
    text-overflow:ellipsis;

}
.quadroResumo div[ref="relatoriosX"] label[ref="valor"],
.quadroResumo div[ref="relatoriosX"] label[ref="titulo"]{
    width:calc(100% - 85px) !important;
    max-width:calc(100% - 85px) !important;
    left:0px !important;
}

.quadroResumo div[ref="relatoriosX"] label[ref="valor"]{
    margin-right:25px !important;
}

.quadroResumo label[ref="icon"]{
    font-size:20pt;
    text-align:center;
    padding-top:6px !important;
}

.ui-selectonemenu,
.ui-selectonemenu label{
    cursor:pointer !important;
}

.lblRotulo{
    font-size:10pt !important;
    color:#505050 !important;
    font-weight: bold !important;
    text-shadow:1px 1px #FFF;
    cursor: default !important;
    font-family:'Open Sans', sans-serif !important;
    margin:0px !important;
}

.FotoPendente{
    width:100% !important;
    min-height:350px !important;
    height:100% !important;
    max-height:100% !important;
    border:3px solid #BBB;
    background-color:#DDD;
    border-radius:4px;
    color:#BBB;
    cursor:pointer;
    box-shadow:2px 2px 3px #CCC;
    background-size: cover;
    background-position: center center;
    position:relative !important;
}

.FotoPendente:hover{
    background-color:#CCC;
    border:3px solid #AAA;
    transition:0.3s ease;
    color:#999 !important;
}

.FotoPendente:hover label,
.FotoPendente:hover i{
    color:#999 !important;
}

.FotoPendente label{
    font-size: 20pt;
}

.FotoPendente i{
    font-size: 32pt;
}

#divMapaFull{
    position:absolute;
    width:100%;
    height:100%;
    top:0px;
    left:0px;
    background-color:#FFF;
    z-index:9999 !important;
    padding:0px !important;
}

[id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
    background-color: #EEE !important;
    border-bottom-color: #CCC !important;
}

[id*="formBoletimTrabalho"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
    background-color: #FFF !important;
}

[id*="relatorio"] .ui-dialog.ui-widget-content .ui-dialog-titlebar{
    background-color: #EEE !important;
    border-bottom-color: #CCC !important;
    height: 70px !important;
}

[id*="relatorio"] .ui-dialog.ui-widget-content .ui-dialog-content, body .ui-dialog .ui-dialog-content{
    background-color: #FFF !important;
}

[id*="dlgRelatorio_content"]{
    background-color: transparent !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {
    .FundoPagina {
        max-height: calc(100% - 90px) !important;
    }
}
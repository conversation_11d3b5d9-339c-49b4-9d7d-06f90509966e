<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:composite = "http://java.sun.com/jsf/composite"
      >
    <composite:interface>
        <composite:attribute name="titulo" required="true" />
        <composite:attribute name="imagem" required="true" />
        <composite:attribute name="dataInicio" required="true" />
        <composite:attribute name="dataFim" required="true" />
    </composite:interface>

    <composite:implementation>
        <div
            id="#{cc.id}"
            class="col-md-4 col-sm-12 col-xs-12"
            style="align-self: center; padding-left: 4px !important; padding-top:4px !important;"
            >
            <img src="${cc.attrs.imagem}" height="40" style="margin-top:-6px !important" />

            <label class="TituloPagina" style="margin-top:4px !important;">#{cc.attrs.titulo}</label>

            <label class="TituloDataHora">
                <h:outputText value="#{localemsgs.Periodo}: "/>
                <span>
                    <h:outputText value="#{cc.attrs.dataInicio}" converter="conversorData" />
                    <h:outputText value=" - "/>
                    <h:outputText value="#{cc.attrs.dataFim}" converter="conversorData"/></span>
            </label>
        </div>
    </composite:implementation>
</html>

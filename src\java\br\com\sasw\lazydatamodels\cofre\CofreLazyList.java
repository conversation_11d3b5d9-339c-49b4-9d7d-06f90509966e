/*
 */
package br.com.sasw.lazydatamodels.cofre;

import Controller.CofreInteligente.CofreIntelSatMobWeb;
import Dados.Persistencia;
import SasBeansCompostas.TesCofresResClientes;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class CofreLazyList extends LazyDataModel<TesCofresResClientes> {

    private static final long serialVersionUID = 1L;
    private List<TesCofresResClientes> cofres;
    private final CofreIntelSatMobWeb cofressatmobweb;
    private final Persistencia persistencia;
    private final boolean exibirTodos;
    private Map filters;

    public CofreLazyList(Persistencia pst, boolean exibirTodos, Map filters) {
        this.cofressatmobweb = new CofreIntelSatMobWeb();
        this.persistencia = pst;
        this.exibirTodos = exibirTodos;
        this.filters = filters;
    }

    @Override
    public List<TesCofresResClientes> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.cofres = this.cofressatmobweb.ListagemPaginada(first, pageSize, this.filters, this.exibirTodos, this.persistencia);

            // set the total of players
            setRowCount(this.cofressatmobweb.Contagem(this.filters, this.exibirTodos, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.cofres;
    }

    @Override
    public Object getRowKey(TesCofresResClientes cofre) {
        return cofre.getClientes().getCodCofre();
    }

    @Override
    public TesCofresResClientes getRowData(String codcofre) {
        BigDecimal cc = new BigDecimal(codcofre);
        for (TesCofresResClientes cofre : this.cofres) {
            if (cc.equals(cofre.getClientes().getCodCofre())) {
                return cofre;
            }
        }
        return null;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

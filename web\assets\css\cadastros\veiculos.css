/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 30-Mar-2020, 14:57:28
    Author     : SASWRichard
*/


.cadastrar{
    width: 90vh;
    min-width: 100%;
}


@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
}

@media only screen and (max-width: 2000px) and (min-width: 701px) {
    .DataGrid [role="columnheader"] > span {
        top: -4px !important;
        position: relative !important;
    }
}

@media only screen and (max-width: 700px) and (min-width: 10px) {

    #divDadosFilial,
    #divDadosFilial div,
    .FilialNome,
    .FilialEndereco,
    .FilialBairroCidade{
        min-width:100% !important;
        width:100% !important;
        max-width:100% !important;
        text-align: center !important;
    }

    .ui-paginator-top {
        white-space: normal !important;
    }

    .tabela .ui-datatable-scrollable-body{
        height: calc(100% - 6.5em);
    }
}

@media only screen and (max-width: 2000px) and (min-width: 701px) {
    .DataGrid{
        width:100% !important;
        border:none !important
    }

    .gridTitulo,
    .gridValor{
        font-size:8pt !important;
    }

    .DataGrid thead tr th,
    .DataGrid tbody tr td {
        min-width: 100px;
        max-width: 100px;
        white-space: normal !important;
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        -webkit-hyphens: auto !important;
        -ms-hyphens: auto !important;
        hyphens: auto !important;
    }
}

html, body{
    max-height:100% !important;
    overflow:hidden !important;
}

#divCorporativo{
    bottom:23px !important;
}

#corporativo {
    max-width: 18vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#corporativo label[ref="lblCheck"]{
    font-size:11px !important;
    min-width:75px !important;
    font-weight:500 !important;
}

footer .ui-chkbox-box {
    max-width: 12px !important;
    max-height: 12px !important;
}

.ui-dialog .ui-panel-content {
    height: auto !important;
}

.tabela .ui-datatable-scrollable-body{
    height: calc(100% - 9em);
}

.botoesDataTable {
    width: 40px;
    margin-top: 8px;
    position: absolute;
    right: -14px; top: 50%;
    transform: translateY(-50%);
}

.veiculoCard .veiculoCardInner{
    padding:0px !important;
    width:100% !important;
    border:1px solid #CCC !important;
    background:linear-gradient(to bottom, #FAFAFA, #F6F6F6);
    margin-bottom:6px;
    margin-top:4px !important;
    border-radius:6px !important;
    box-shadow:2px 2px 3px #DDD;
    border-top:5px solid #3c8dbc !important;
}

.veiculoCardInner .ui-panel-title{
    margin-top: 0px !important;
    font-size: 16pt !important;
    font-weight: bold !important;
    color: #3c8dbc !important;
}

.gridTitulo{
    width:100px !important;
    font-size:10pt !important;
    text-align:right !important;
    float:left;
    height:15px !important;
    margin-top:2px;
    color:#666 !important;
}


.gridValor{
    width:calc(100% - 100px) !important;
    max-width:calc(100% - 100px) !important;
    font-size:10pt !important;
    text-align:left !important;
    float:left;
    height:20px !important;
    padding-left:8px !important;
    font-weight:bold;
    white-space: nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    margin-top:2px;
    color:#202020 !important;
}
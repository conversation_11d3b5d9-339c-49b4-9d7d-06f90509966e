/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
*/
/* 
    Created on : 10/01/2017, 14:57:51
    Author     : Gigliano
*/

.tabela .ui-datatable-data .ui-widget-content{ 
    background: white; 
} 
.ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important; color: black !important;
} 

.fotoPosto .ui-widget-content{
    background: transparent;
} 
.fotoPosto .ui-galleria{
    width: 512px !important;
}
.fotoPosto .ui-galleria-filmstrip-wrapper{
    bottom: 75px;
    visibility: hidden;
} 
.fotoPosto .ui-galleria-nav-next,
.fotoPosto .ui-icon,
.fotoPosto .ui-icon-circle-triangle-e{ 
    top: 0px !important; 
} 
.fotoPosto .ui-galleria-nav-next{
    right: 50px;
}
.fotoPosto .ui-galleria-nav-prev{
    left: 50px;
}
.fotoPosto .ui-galleria-panel,
.fotoPosto .ui-galleria-panel-wrapper{ 
    height: 200px !important; 
    width: 512px !important;
} 

.fotoFuncion .ui-galleria-filmstrip-wrapper{ 
    bottom: 150px !important;
    height: 150px !important;
    right: 120px !important;
    width: 40px !important;
} 
.fotoFuncion .ui-galleria-filmstrip {
    width: 40px !important;
    left:0px !important;
}
.fotoFuncion .ui-galleria-frame,
.fotoFuncion .ui-galleria-frame-content,
.fotoFuncion .ui-galleria-frame-image{
    width: 40px !important; 
    height: 40px !important; 
}
.fotoFuncion .ui-galleria-nav-next,
.fotoFuncion .ui-icon,.fotoFuncion .ui-icon-circle-triangle-e{ 
    top: 0px !important; 
} 
.fotoFuncion .ui-galleria-panel,.fotoFuncion .ui-galleria-panel-wrapper{
    height: 150px !important;
    width: 340px !important;
}
.fotoFuncion .ui-galleria{
    width: 340px !important;
    height: 150px !important;
}

.dialogo .ui-widget-content{
    background: transparent;
}
.dialogo .ui-panelgrid-cell{
    display: table-cell;
    vertical-align: top;
}

.dialogo .ui-dialog.ui-widget-content .ui-dialog-content{
    padding: 0px 15px 15px 0px;
}

.contrato .ui-autocomplete-panel {
    width: 250px !important;
}
.contrato .ui-autocomplete-input{
    width: 250px !important;
}

.contratoP .ui-autocomplete-panel {
    width: 280px !important;
}
.contratoP .ui-autocomplete-input{
    width: 280px !important;
}

.posto .ui-autocomplete-panel {
    width: 315px !important;
}
.posto .ui-autocomplete-input{
    width: 315px !important;
}

.postoP .ui-autocomplete-panel {
    width: 280px !important;
}
.postoP .ui-autocomplete-input{
    width: 280px !important;
}

.cliente .ui-autocomplete-panel {
    width: 250px !important;
}
.cliente .ui-autocomplete-input{
    width: 250px !important;
}

.clienteP .ui-autocomplete-panel {
    width: 280px !important;
}
.clienteP .ui-autocomplete-input{
    width: 280px !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}


/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Siates;
import SasBeans.XMLSiates.Tipo1;
import Xml.Xmls;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

/**
 *
 * <AUTHOR>
 */
public class SiatesDao {

    /**
     * Conta a quantidade de chamados no banco
     *
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Integer totalChamados(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) total FROM Siates "
                    + " WHERE IDChamado IS NOT NULL";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("SiatesDao.totalChamados - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de chamados
     *
     * @param primeiro
     * @param linhas
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Siates> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Siates> retorno = new ArrayList();
        try {
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY Data DESC, Hora Desc ) AS RowNum, "
                    + " * FROM Siates "
                    + " WHERE IDChamado IS NOT NULL ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Siates siates;
            Tipo1 tipo1;
            DocumentBuilder builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            InputSource src = new InputSource();
            Document doc;
            while (consulta.Proximo()) {
                siates = new Siates();
                siates.setSequencia(consulta.getString("Sequencia"));
                siates.setID(consulta.getString("ID"));
                siates.setWO(consulta.getString("WO"));
                siates.setReq(consulta.getString("Req"));
                siates.setXML_Envio(consulta.getString("XML_Envio"));
                siates.setXML_Retorno(consulta.getString("XML_Retorno"));
                siates.setCaptura(consulta.getString("Captura"));
                siates.setData(consulta.getString("Data"));
                siates.setHora(consulta.getString("Hora"));
                siates.setIDChamado(consulta.getString("IDChamado"));
                siates.setEmpresa(consulta.getString("Empresa"));
                siates.setOrganizacao(consulta.getString("Organizacao"));
                siates.setSuporte(consulta.getString("Suporte"));
                siates.setStatus(consulta.getString("Status"));
                siates.setOperador(consulta.getString("Operador"));
                siates.setDt_Alter(consulta.getString("Dt_Alter"));
                siates.setHr_Alter(consulta.getString("Hr_Alter"));
                try {
                    src.setCharacterStream(new StringReader(siates.getXML_Retorno().replace("ns1:", "").replace(":ns1", "")));
                    doc = builder.parse(src);
                    tipo1 = new Tipo1();
                    tipo1.setArquivoxml(new Tipo1.ArquivoXml(
                            new Tipo1.Info_Arquivo(
                                    Xmls.getConteudo(doc, "tipoarquivo"),
                                    Xmls.getConteudo(doc, "idarquivo"),
                                    Xmls.getConteudo(doc, "datahorageracaoarquivo"),
                                    Xmls.getConteudo(doc, "comunicacao")),
                            new Tipo1.Info_Demandante(
                                    Xmls.getConteudo(doc, "idgruposuportedemandante"),
                                    Xmls.getConteudo(doc, "gruposuportedemandante"),
                                    new Tipo1.Chamado_Caixa(
                                            Xmls.getConteudo(doc, "no_req"),
                                            Xmls.getConteudo(doc, "no_wo"),
                                            Xmls.getConteudo(doc, "no_inc"),
                                            Xmls.getConteudo(doc, "no_crq"),
                                            Xmls.getConteudo(doc, "numero_serie")),
                                    new Tipo1.TipoRequisicao(
                                            Xmls.getConteudo(doc, "idreq"),
                                            Xmls.getConteudo(doc, "nomereq"))),
                            new Tipo1.Info_Fornecedor(
                                    Xmls.getConteudo(doc, "idfornecedor"),
                                    Xmls.getConteudo(doc, "nomefornecedor"),
                                    Xmls.getConteudo(doc, "prioridade")),
                            new Tipo1.Categorizacao(
                                    Xmls.getConteudo(doc, "categ_op_n1"),
                                    Xmls.getConteudo(doc, "categ_op_n2"),
                                    Xmls.getConteudo(doc, "categ_op_n3"),
                                    Xmls.getConteudo(doc, "categ_prod_n1"),
                                    Xmls.getConteudo(doc, "categ_prod_n2"),
                                    Xmls.getConteudo(doc, "categ_prod_n3"),
                                    Xmls.getConteudo(doc, "nomeproduto"),
                                    Xmls.getConteudo(doc, "modeloproduto"),
                                    Xmls.getConteudo(doc, "fabricante")),
                            new Tipo1.Info_Solicitante(
                                    Xmls.getConteudo(doc, "codigodobanco"),
                                    Xmls.getConteudo(doc, "tipounidade"),
                                    Xmls.getConteudo(doc, "codigounidade"),
                                    Xmls.getConteudo(doc, "siglaunidade"),
                                    Xmls.getConteudo(doc, "nomeunidade"),
                                    Xmls.getConteudo(doc, "enderecounidade"),
                                    Xmls.getConteudo(doc, "cidadeunidade"),
                                    Xmls.getConteudo(doc, "ufunidade"),
                                    Xmls.getConteudo(doc, "cep"),
                                    Xmls.getConteudo(doc, "idsolicitante"),
                                    Xmls.getConteudo(doc, "contatonome"),
                                    Xmls.getConteudo(doc, "contatotelefone"),
                                    Xmls.getConteudo(doc, "contatoemail")),
                            new Tipo1.Solicitacao(
                                    Xmls.getConteudo(doc, "descricao"),
                                    new Tipo1.Detalhes(
                                            Xmls.getConteudo(doc, "detalhe1"),
                                            Xmls.getConteudo(doc, "detalhe2"),
                                            Xmls.getConteudo(doc, "detalhe3"),
                                            Xmls.getConteudo(doc, "detalhe4"),
                                            Xmls.getConteudo(doc, "detalhe5"),
                                            Xmls.getConteudo(doc, "detalhe6"),
                                            Xmls.getConteudo(doc, "detalhe7"),
                                            Xmls.getConteudo(doc, "detalhe8"),
                                            Xmls.getConteudo(doc, "detalhe9"),
                                            Xmls.getConteudo(doc, "detalhe10"),
                                            Xmls.getConteudo(doc, "detalhe11"),
                                            Xmls.getConteudo(doc, "detalhe12"),
                                            Xmls.getConteudo(doc, "detalhe13"),
                                            Xmls.getConteudo(doc, "detalhe14"),
                                            Xmls.getConteudo(doc, "detalhe15"),
                                            Xmls.getConteudo(doc, "detalhe16"),
                                            Xmls.getConteudo(doc, "detalhe17"),
                                            Xmls.getConteudo(doc, "detalhe18"),
                                            Xmls.getConteudo(doc, "detalhe19"),
                                            Xmls.getConteudo(doc, "detalhe20"),
                                            Xmls.getConteudo(doc, "detalhe21"),
                                            Xmls.getConteudo(doc, "detalhe22"),
                                            Xmls.getConteudo(doc, "detalhe23"),
                                            Xmls.getConteudo(doc, "detalhe24"),
                                            Xmls.getConteudo(doc, "detalhe25"),
                                            Xmls.getConteudo(doc, "detalhe26"),
                                            Xmls.getConteudo(doc, "detalhe27"),
                                            Xmls.getConteudo(doc, "detalhe28"),
                                            Xmls.getConteudo(doc, "detalhe29"),
                                            Xmls.getConteudo(doc, "detalhe30"))),
                            new Tipo1.Anexos(
                                    Xmls.getConteudo(doc, "nome_arquivo1"),
                                    Xmls.getConteudo(doc, "anexo"))));
                    siates.setXMLRetorno(tipo1);
                } catch (Exception e) {
                    siates.setXMLRetorno(new Tipo1());
                }

                retorno.add(siates);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("SiatesDao.listaPaginada - " + e.getMessage());
        }
    }

    /**
     * Verifica a existência de algum chamado de mesma ID.
     *
     * @param IDChamado
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeChamado(String IDChamado, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Siates WHERE IDChamado = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(IDChamado);
            consulta.select();
            return consulta.Proximo();
        } catch (Exception e) {
            throw new Exception("SiatesDao.existeChamado - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Siates WHERE IDChamado = " + IDChamado);
        }
    }

    /**
     * Insere uma nova entrada na tabela Siates
     *
     * @param siates
     * @param persistencia
     * @throws Exception
     */
    public void inserirChamado(Siates siates, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Siates (Sequencia,ID,WO,Req,XML_Envio,XML_Retorno,Captura,Data,Hora,"
                    + "IDChamado,Empresa,Organizacao,Suporte,Status,Operador,Dt_Alter,Hr_Alter) "
                    + " VALUES((SELECT isnull(max(sequencia),0)+1 from siates),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(siates.getID());
            consulta.setString(siates.getWO());
            consulta.setString(siates.getReq());
            consulta.setString(siates.getXML_Envio());
            consulta.setString(siates.getXML_Retorno());
            consulta.setString(siates.getCaptura());
            consulta.setString(siates.getData());
            consulta.setString(siates.getHora());
            consulta.setString(siates.getIDChamado());
            consulta.setString(siates.getEmpresa());
            consulta.setString(siates.getOrganizacao());
            consulta.setString(siates.getSuporte());
            consulta.setString(siates.getStatus());
            consulta.setString(siates.getOperador());
            consulta.setString(siates.getDt_Alter());
            consulta.setString(siates.getHr_Alter());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("SiatesDao.inserirChamado - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Siates (Sequencia,ID,WO,Req,XML_Envio,XML_Retorno,Captura,Data,Hora,"
                    + "IDChamado,Empresa,Organizacao,Suporte,Status,Operador,Dt_Alter,Hr_Alter) "
                    + " VALUES((SELECT isnull(max(sequencia),0)+1 from siates," + siates.getID() + "," + siates.getWO() + "," + siates.getReq() + ","
                    + siates.getXML_Envio() + "," + siates.getXML_Retorno() + "," + siates.getCaptura() + "," + siates.getData() + "," + siates.getHora() + ","
                    + siates.getIDChamado() + "," + siates.getEmpresa() + "," + siates.getOrganizacao() + "," + siates.getSuporte() + "," + siates.getStatus()
                    + "," + siates.getOperador() + "," + siates.getDt_Alter() + "," + siates.getHr_Alter() + ")");
        }
    }
}

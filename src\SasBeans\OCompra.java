package SasBeans;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class OCompra {

    private BigDecimal sequencia;
    private BigDecimal codFil;
    private String data;
    private BigDecimal CodFornec;
    private String fornecedor;
    private BigDecimal valor;
    private String dtVenc;
    private String condPgto;
    private Integer tipoTit;
    private Integer formaPgto;
    private String obs;
    private String ccusto;
    private Integer contaFin;
    private String situacao;
    private String tipo;
    private Integer qtdeParc;
    private Integer intervalo;
    private BigDecimal codTransp;
    private BigDecimal valorFrete;
    private Integer formaPgtoFrete;
    private String operAprov;
    private Date dtAprov;
    private String hrAprov;
    private String operador;
    private String dtAlter;
    private String hrAlter;

    public BigDecimal getSequencia() {
        return sequencia;
    }

    public void setSequencia(BigDecimal sequencia) {
        this.sequencia = sequencia;
    }

    public BigDecimal getCodFil() {
        return codFil;
    }

    public void setCodFil(BigDecimal codFil) {
        this.codFil = codFil;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public BigDecimal getCodFornec() {
        return CodFornec;
    }

    public void setCodFornec(BigDecimal CodFornec) {
        this.CodFornec = CodFornec;
    }

    public String getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(String fornecedor) {
        this.fornecedor = fornecedor;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public String getDtVenc() {
        return dtVenc;
    }

    public void setDtVenc(String dtVenc) {
        this.dtVenc = dtVenc;
    }

    public String getCondPgto() {
        return condPgto;
    }

    public void setCondPgto(String condPgto) {
        this.condPgto = condPgto;
    }

    public Integer getTipoTit() {
        return tipoTit;
    }

    public void setTipoTit(Integer tipoTit) {
        this.tipoTit = tipoTit;
    }

    public Integer getFormaPgto() {
        return formaPgto;
    }

    public void setFormaPgto(Integer formaPgto) {
        this.formaPgto = formaPgto;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public String getCcusto() {
        return ccusto;
    }

    public void setCcusto(String ccusto) {
        this.ccusto = ccusto;
    }

    public Integer getContaFin() {
        return contaFin;
    }

    public void setContaFin(Integer contaFin) {
        this.contaFin = contaFin;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getQtdeParc() {
        return qtdeParc;
    }

    public void setQtdeParc(Integer qtdeParc) {
        this.qtdeParc = qtdeParc;
    }

    public Integer getIntervalo() {
        return intervalo;
    }

    public void setIntervalo(Integer intervalo) {
        this.intervalo = intervalo;
    }

    public BigDecimal getCodTransp() {
        return codTransp;
    }

    public void setCodTransp(BigDecimal codTransp) {
        this.codTransp = codTransp;
    }

    public BigDecimal getValorFrete() {
        return valorFrete;
    }

    public void setValorFrete(BigDecimal valorFrete) {
        this.valorFrete = valorFrete;
    }

    public Integer getFormaPgtoFrete() {
        return formaPgtoFrete;
    }

    public void setFormaPgtoFrete(Integer formaPgtoFrete) {
        this.formaPgtoFrete = formaPgtoFrete;
    }

    public String getOperAprov() {
        return operAprov;
    }

    public void setOperAprov(String operAprov) {
        this.operAprov = operAprov;
    }

    public Date getDtAprov() {
        return dtAprov;
    }

    public void setDtAprov(Date dtAprov) {
        this.dtAprov = dtAprov;
    }

    public String getHrAprov() {
        return hrAprov;
    }

    public void setHrAprov(String hrAprov) {
        this.hrAprov = hrAprov;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDtAlter() {
        return dtAlter;
    }

    public void setDtAlter(String dtAlter) {
        this.dtAlter = dtAlter;
    }

    public String getHrAlter() {
        return hrAlter;
    }

    public void setHrAlter(String hrAlter) {
        this.hrAlter = hrAlter;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.sequencia);
        hash = 59 * hash + Objects.hashCode(this.codFil);
        hash = 59 * hash + Objects.hashCode(this.data);
        hash = 59 * hash + Objects.hashCode(this.CodFornec);
        hash = 59 * hash + Objects.hashCode(this.fornecedor);
        hash = 59 * hash + Objects.hashCode(this.valor);
        hash = 59 * hash + Objects.hashCode(this.dtVenc);
        hash = 59 * hash + Objects.hashCode(this.condPgto);
        hash = 59 * hash + Objects.hashCode(this.tipoTit);
        hash = 59 * hash + Objects.hashCode(this.formaPgto);
        hash = 59 * hash + Objects.hashCode(this.obs);
        hash = 59 * hash + Objects.hashCode(this.ccusto);
        hash = 59 * hash + Objects.hashCode(this.contaFin);
        hash = 59 * hash + Objects.hashCode(this.situacao);
        hash = 59 * hash + Objects.hashCode(this.tipo);
        hash = 59 * hash + Objects.hashCode(this.qtdeParc);
        hash = 59 * hash + Objects.hashCode(this.intervalo);
        hash = 59 * hash + Objects.hashCode(this.codTransp);
        hash = 59 * hash + Objects.hashCode(this.valorFrete);
        hash = 59 * hash + Objects.hashCode(this.formaPgtoFrete);
        hash = 59 * hash + Objects.hashCode(this.operAprov);
        hash = 59 * hash + Objects.hashCode(this.dtAprov);
        hash = 59 * hash + Objects.hashCode(this.hrAprov);
        hash = 59 * hash + Objects.hashCode(this.operador);
        hash = 59 * hash + Objects.hashCode(this.dtAlter);
        hash = 59 * hash + Objects.hashCode(this.hrAlter);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OCompra other = (OCompra) obj;
        if (!Objects.equals(this.sequencia, other.sequencia)) {
            return false;
        }
        return true;
    }

}

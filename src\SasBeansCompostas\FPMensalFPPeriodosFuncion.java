package SasBeansCompostas;

import SasBeans.FPMensal;
import SasBeans.FPPeriodos;
import SasBeans.Funcion;

/**
 *
 * <AUTHOR>
 */
public class FPMensalFPPeriodosFuncion {

    private FPMensal fpmensal;
    private FPPeriodos fpperiodos;
    private Funcion funcion;

    public FPMensalFPPeriodosFuncion() {
        this.fpmensal = new FPMensal();
        this.fpperiodos = new FPPeriodos();
        this.funcion = new Funcion();
    }

    public FPMensal getFpmensal() {
        return fpmensal;
    }

    public void setFpmensal(FPMensal fpmensal) {
        this.fpmensal = fpmensal;
    }

    public FPPeriodos getFpperiodos() {
        return fpperiodos;
    }

    public void setFpperiodos(FPPeriodos fpperiodos) {
        this.fpperiodos = fpperiodos;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }
}

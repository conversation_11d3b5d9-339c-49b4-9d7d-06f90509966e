/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1000;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1000Dao {

    public List<S1000> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S1000> retorno = new ArrayList<>();
            String sql = " Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, Filiais.RazaoSocial infoCadastro_nmRazao, "
                    + " Filiais.PorteEmp infoCadastro_classTrib, Rais.NatJur infoCadastro_natJurid, Pessoa.Nome contato_nmCtt, "
                    + " Dirf.CPFRInf contato_cpfCtt, DIRF.DDDRInf+DIRF.FoneRInf contato_foneFixo, DIRF.emailRInf contato_email, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1000' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1000' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1000' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Filiais "
                    + " Left join Rais  on Rais.CodFil = Filiais.CodFil "
                    + " Left Join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa  on Pessoa.Codigo = Dirf.CodPessoaRIn "
                    + " Where Filiais.CodFil = ? "
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.select();
            S1000 s1000;
            while (consulta.Proximo()) {
                s1000 = new S1000();
                s1000.setSucesso(consulta.getInt("sucesso"));
                s1000.setIdeEvento_procEmi("1");
                s1000.setIdeEvento_verProc("Satellite eSocial");
                s1000.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1000.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1000.setInfoCadastro_nmRazao(consulta.getString("infoCadastro_nmRazao"));
                //s1000.setInfoCadastro_classTrib(consulta.getString("infoCadastro_classTrib"));
                s1000.setInfoCadastro_classTrib("99");
                s1000.setInfoCadastro_natJurid(consulta.getString("infoCadastro_natJurid"));
                s1000.setInfoCadastro_indCoop("0");
                s1000.setInfoCadastro_indConstr("0");
                s1000.setInfoCadastro_indDesFolha("0");
                s1000.setInfoCadastro_indOptRegEletron("0");
                s1000.setInfoCadastro_indEntEd("N");
                s1000.setInfoCadastro_indEtt("N");
                s1000.setContato_nmCtt(consulta.getString("contato_nmCtt"));
                s1000.setContato_cpfCtt(consulta.getString("contato_cpfCtt"));
                s1000.setContato_foneFixo(consulta.getString("contato_foneFixo"));
                s1000.setContato_email(consulta.getString("contato_email"));
                s1000.setSoftwareHouse_cnpjSoftHouse("04057947000190");
                s1000.setSoftwareHouse_nmRazao("South American Technology Ltda");
                s1000.setSoftwareHouse_nmCont("Carlos Santos");
                s1000.setSoftwareHouse_telefone("6133440038");
                s1000.setSoftwareHouse_email("<EMAIL>");
                s1000.setSituacaoPJ_indSitPJ("0");

                s1000.setIdePeriodo_iniValid(compet);
                s1000.setIdeEvento_tpAmb(ambiente);
                s1000.setId(s1000.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001");
                //s1000.setId("ID1726199760000002018022414123500001");
                s1000.setIdeEmpregador_tpInsc(s1000.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1000.setIdeEmpregador_nrInsc(s1000.getIdeEmpregador_nrInsc().substring(0, 8));
                s1000.setInfoCadastro_classTrib(FuncoesString.PreencheEsquerda(s1000.getInfoCadastro_classTrib(), 2, "0"));

                retorno.add(s1000);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1000Dao.get - " + e.getMessage() + "\r\n"
                    + "  Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc, Filiais.RazaoSocial infoCadastro_nmRazao, "
                    + " Filiais.PorteEmp infoCadastro_classTrib, Rais.NatJur infoCadastro_natJurid, Pessoa.Nome contato_nmCtt, "
                    + " Dirf.CPFRInf contato_cpfCtt, DIRF.DDDRInf+DIRF.FoneRInf contato_foneFixo, DIRF.emailRInf contato_email, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1000' "
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1000' "
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1000' "
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Filiais "
                    + " Left join Rais  on Rais.CodFil = Filiais.CodFil "
                    + " Left Join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa  on Pessoa.Codigo = Dirf.CodPessoaRIn "
                    + " Where Filiais.CodFil = " + codFil
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ");
        }
    }
}

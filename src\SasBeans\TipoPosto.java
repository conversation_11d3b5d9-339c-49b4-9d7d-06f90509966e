package SasBeans;

/**
 * <AUTHOR>
 */
public class TipoPosto {

    private String descricao;
    private String frequencia;
    private String operador;
    private String hr_alter;
    private String dt_alter;

    private String codigo;
    private String chdiu1;
    private String chnot1;
    private String chdiu2;
    private String chnot2;
    private String chdiu3;
    private String chnot3;
    private String chdiu4;
    private String chnot4;
    private String chdiu5;
    private String chnot5;
    private String chdiu6;
    private String chnot6;
    private String chdiu7;
    private String chnot7;
    private String chdiufer;
    private String chnotfer;

    public TipoPosto() {
        descricao = "";
        frequencia = "";
        operador = "";
        hr_alter = "";
        dt_alter = "";
        codigo = "";
        chdiu1 = "";
        chnot1 = "";
        chdiu2 = "";
        chnot2 = "";
        chdiu3 = "";
        chnot3 = "";
        chdiu4 = "";
        chnot4 = "";
        chdiu5 = "";
        chnot5 = "";
        chdiu6 = "";
        chnot6 = "";
        chdiu7 = "";
        chnot7 = "";
        chdi<PERSON> = "";
        chnotfer = "";
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

    public String getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(String dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getChdiu1() {
        return chdiu1;
    }

    public void setChdiu1(String chdiu1) {
        this.chdiu1 = chdiu1;
    }

    public String getChnot1() {
        return chnot1;
    }

    public void setChnot1(String chnot1) {
        this.chnot1 = chnot1;
    }

    public String getChdiu2() {
        return chdiu2;
    }

    public void setChdiu2(String chdiu2) {
        this.chdiu2 = chdiu2;
    }

    public String getChnot2() {
        return chnot2;
    }

    public void setChnot2(String chnot2) {
        this.chnot2 = chnot2;
    }

    public String getChdiu3() {
        return chdiu3;
    }

    public void setChdiu3(String chdiu3) {
        this.chdiu3 = chdiu3;
    }

    public String getChnot3() {
        return chnot3;
    }

    public void setChnot3(String chnot3) {
        this.chnot3 = chnot3;
    }

    public String getChdiu4() {
        return chdiu4;
    }

    public void setChdiu4(String chdiu4) {
        this.chdiu4 = chdiu4;
    }

    public String getChnot4() {
        return chnot4;
    }

    public void setChnot4(String chnot4) {
        this.chnot4 = chnot4;
    }

    public String getChdiu5() {
        return chdiu5;
    }

    public void setChdiu5(String chdiu5) {
        this.chdiu5 = chdiu5;
    }

    public String getChnot5() {
        return chnot5;
    }

    public void setChnot5(String chnot5) {
        this.chnot5 = chnot5;
    }

    public String getChdiu6() {
        return chdiu6;
    }

    public void setChdiu6(String chdiu6) {
        this.chdiu6 = chdiu6;
    }

    public String getChnot6() {
        return chnot6;
    }

    public void setChnot6(String chnot6) {
        this.chnot6 = chnot6;
    }

    public String getChdiu7() {
        return chdiu7;
    }

    public void setChdiu7(String chdiu7) {
        this.chdiu7 = chdiu7;
    }

    public String getChnot7() {
        return chnot7;
    }

    public void setChnot7(String chnot7) {
        this.chnot7 = chnot7;
    }

    public String getChdiufer() {
        return chdiufer;
    }

    public void setChdiufer(String chdiufer) {
        this.chdiufer = chdiufer;
    }

    public String getChnotfer() {
        return chnotfer;
    }

    public void setChnotfer(String chnotfer) {
        this.chnotfer = chnotfer;
    }

}

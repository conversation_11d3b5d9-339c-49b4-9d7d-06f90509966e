<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/index.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/stylePage.css" rel="stylesheet" />
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h" style="overflow:hidden !important">
            <f:metadata>
                <f:viewParam name="dataRef" value="#{login.loginSatDesktopData}"/>
                <f:viewParam name="paramRef" value="#{login.loginSatDesktopBase}"/>
                <f:viewParam name="codPessoaRef" value="#{login.loginSatDesktopCodPessoa}"/>
                <f:viewParam name="seqRotaRef" value="#{login.loginSatDesktopSeqRota}"/>
            </f:metadata>

            <p:growl id="msgs" widgetVar="msgs" />


            <h:form id="main">
                <label id="imgLoad" style="width: 100%; text-align: center !important;margin-top: 30px; color: #000; font-size: 20pt;">
                    <center><i class="fa fa-refresh fa-spin fa-fw" style="font-size: 52pt; color: #000; display: block; width: 60px; margin-bottom: 10px;"></i></center>
                        #{localemsgs.ProcessandoAguarde}
                </label>

                <p:remoteCommand name="rcLogin" partialSubmit="true" 
                                 process="@form" 
                                 update="msgs" 
                                 actionListener="#{login.logarSatelliteDesktop}" />         

                <script>
                    $(document).ready(function () {
                        setTimeout(function () {
                            rcLogin();
                        }, 500);
                    });
                </script>
            </h:form>
        </h:body>
    </f:view>
</html>

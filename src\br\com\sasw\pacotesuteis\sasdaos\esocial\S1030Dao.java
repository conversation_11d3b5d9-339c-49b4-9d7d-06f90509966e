/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1030;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1030Dao {

    public List<S1030> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql1 = "Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc "
                    + " From Filiais "
                    + " Where Filiais.CodFil = ? ";
            Consulta consulta = new Consulta(sql1, persistencia);
            consulta.setString(codFil);
            consulta.select();
            String ideEmpregador_tpInsc = "", ideEmpregador_nrInsc = "";
            while (consulta.Proximo()) {
                ideEmpregador_tpInsc = consulta.getString("ideEmpregador_tpInsc");
                ideEmpregador_nrInsc = consulta.getString("ideEmpregador_nrInsc");
            }
            String sql = " Select Codigo ideCargo_codCargo, Descricao dadosCargo_nmCargo, CBO dadosCargo_codCBO, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1030' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1030' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1030' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Cargos "
                    + " WHERE cargo in (Select cargo from funcion where situacao <> 'D') "
                    + " ORDER BY sucesso asc, Cargos.Codigo asc ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.select();
            List<S1030> retorno = new ArrayList<>();
            S1030 s1030;
            while (consulta.Proximo()) {
                s1030 = new S1030();
                s1030.setIdeEvento_procEmi("1");
                s1030.setSucesso(consulta.getInt("sucesso"));
                s1030.setIdeEvento_verProc("Satellite eSocial");
                s1030.setIdeEmpregador_tpInsc(ideEmpregador_tpInsc);
                s1030.setIdeEmpregador_nrInsc(ideEmpregador_nrInsc);
                s1030.setIdeCargo_codCargo(consulta.getString("ideCargo_codCargo"));
                s1030.setDadosCargo_nmCargo(consulta.getString("dadosCargo_nmCargo"));
                s1030.setDadosCargo_codCBO(consulta.getString("dadosCargo_codCBO"));
                retorno.add(s1030);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1030Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Codigo ideCargo_codCargo, Descricao dadosCargo_nmCargo, CBO dadosCargo_codCBO, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z  "
                    + "             where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1030' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1030' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = Cargos.Codigo "
                    + "             and z.evento = 'S-1030' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From Cargos "
                    + " WHERE cargo in (Select cargo from funcion where situacao <> 'D') "
                    + " ORDER BY sucesso asc, Cargos.Codigo asc ");
        }
    }
}

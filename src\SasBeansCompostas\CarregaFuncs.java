/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Cargos;
import SasBeans.Funcion;
import SasBeans.PstServ;

/**
 *
 * <AUTHOR>
 */
public class CarregaFuncs {

    private Funcion funcion;
    private PstServ pstServ;
    private Cargos cargos;

    /**
     * @return the funcion
     */
    public Funcion getFuncion() {
        return funcion;
    }

    /**
     * @param funcion the funcion to set
     */
    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    /**
     * @return the pstServ
     */
    public PstServ getPstServ() {
        return pstServ;
    }

    /**
     * @param pstServ the pstServ to set
     */
    public void setPstServ(PstServ pstServ) {
        this.pstServ = pstServ;
    }

    /**
     * @return the cargos
     */
    public Cargos getCargos() {
        return cargos;
    }

    /**
     * @param cargos the cargos to set
     */
    public void setCargos(Cargos cargos) {
        this.cargos = cargos;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package ClassesJson.BI;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ReceberXRecebidosDescricaoJson {

    List<ReceberXRecebidosSomaValores> recebeSoma = new ArrayList<>();

    public List<ReceberXRecebidosSomaValores> getRecebeSoma() {
        return recebeSoma;
    }

    public void setRecebeSoma(List<ReceberXRecebidosSomaValores> recebeSoma) {
        this.recebeSoma = recebeSoma;
    }

}

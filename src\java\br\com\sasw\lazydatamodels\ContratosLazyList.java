/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Contratos.ContratosSatMobWeb;
import Dados.Persistencia;
import SasBeans.Contratos;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ContratosLazyList extends LazyDataModel<Contratos> {

    private static final long serialVersionUID = 1L;
    private List<Contratos> contratos;
    private Persistencia persistencia;
    private ContratosSatMobWeb contratosSatWeb;

    public ContratosLazyList(Persistencia pst) {
        this.persistencia = pst;
        contratosSatWeb = new ContratosSatMobWeb();
    }

    @Override
    public List<Contratos> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.contratos = this.contratosSatWeb.listaPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.contratosSatWeb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.contratos;
    }

    @Override
    public Object getRowKey(Contratos contrato) {
        if (null == contrato.getCodFil() || null == contrato.getContrato()) {
            return null;
        }
        return contrato.getCodFil().replace(".0", "") + ";" + contrato.getContrato();
    }

    @Override
    public Contratos getRowData(String codFilContrato) {
        String[] codFilContratoArray = codFilContrato.split(";", -1);
        if (codFilContratoArray.length != 2) {
            return null;
        }
        for (Contratos contrato : this.contratos) {
            if (codFilContratoArray[0].equals(contrato.getCodFil().replace(".0", ""))
                    && codFilContratoArray[1].equals(contrato.getContrato())) {
                return contrato;
            }
        }
        return null;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

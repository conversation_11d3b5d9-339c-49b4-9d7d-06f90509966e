/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class CPostais {

    private String Pais;
    private String CP;
    private String Assentamento;
    private String TipoAssenta;
    private String Cidade;
    private String Estado;
    private String CidadeDesc;
    private BigDecimal CodEstado;
    private BigDecimal CodMunicipio;
    private BigDecimal IDCP;

    /**
     *
     * @return Pais
     */
    public String getPais() {
        return this.Pais;
    }

    /**
     * Set Pais
     *
     * @param Pais
     */
    public void setPais(String Pais) {
        this.Pais = Pais;
    }

    /**
     *
     * @return CP
     */
    public String getCP() {
        return this.CP;
    }

    /**
     * Set CP
     *
     * @param CP
     */
    public void setCP(String CP) {
        this.CP = CP;
    }

    /**
     *
     * @return Assentamento
     */
    public String getAssentamento() {
        return this.Assentamento;
    }

    /**
     * Set Assentamento
     *
     * @param Assentamento
     */
    public void setAssentamento(String Assentamento) {
        this.Assentamento = Assentamento;
    }

    /**
     *
     * @return TipoAssenta
     */
    public String getTipoAssenta() {
        return this.TipoAssenta;
    }

    /**
     * Set TipoAssenta
     *
     * @param TipoAssenta
     */
    public void setTipoAssenta(String TipoAssenta) {
        this.TipoAssenta = TipoAssenta;
    }

    /**
     *
     * @return Cidade
     */
    public String getCidade() {
        return this.Cidade;
    }

    /**
     * Set Cidade
     *
     * @param Cidade
     */
    public void SetCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    /**
     *
     * @return Estado
     */
    public String getEstado() {
        return this.Estado;
    }

    /**
     * Set Estado
     *
     * @param Estado
     */
    public void setEstado(String Estado) {
        this.Estado = Estado;
    }

    /**
     *
     * @return CidadeDesc
     */
    public String getCidadeDesc() {
        return this.CidadeDesc;
    }

    /**
     * Set CidadeDesc
     *
     * @param CidadeDesc
     */
    public void setCidadeDesc(String CidadeDesc) {
        this.CidadeDesc = CidadeDesc;
    }

    /**
     *
     * @return CodEstado
     */
    public BigDecimal getCodEstado() {
        return this.CodEstado;
    }

    /**
     * Return CodEstado
     *
     * @param CodEstado
     */
    public void setCodEstado(String CodEstado) {
        try {
            this.CodEstado = new BigDecimal(CodEstado);
        } catch (Exception e) {
            this.CodEstado = new BigDecimal("0");
        }
    }

    /**
     *
     * @return CodMunicipio
     */
    public BigDecimal getCodMunicipio() {
        return this.CodMunicipio;
    }

    /**
     * Set CodMunicipio
     *
     * @param CodMunicipio
     */
    public void setCodMunicipio(String CodMunicipio) {
        try {
            this.CodMunicipio = new BigDecimal(CodMunicipio);
        } catch (Exception e) {
            this.CodMunicipio = new BigDecimal("0");
        }
    }

    /**
     *
     * @return IDCP
     */
    public BigDecimal getIDCP() {
        return this.IDCP;
    }

    /**
     * Set IDCP
     *
     * @param IDCP
     */
    public void setIDCP(String IDCP) {
        try {
            this.IDCP = new BigDecimal(IDCP);
        } catch (Exception e) {
            this.IDCP = new BigDecimal("0");
        }
    }
}

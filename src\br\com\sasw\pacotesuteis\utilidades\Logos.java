/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

/**
 *
 * <AUTHOR>
 */
public class Logos {

    public static String getLogo(String empresa, String codfil) {
        String url;
        if (codfil.contains(".0")) {
            codfil = codfil.replace(".0", "");
        }
        switch (empresa) {
            case "SATFIDELYS1":
            case "SATFIDELYS":
            case "FIDELYS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_Fidelys_satelliteWeb.png";
                break;
            case "SATMAXIMA":
            case "MAXIMA":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_maxima.png";
                break;
            case "SATINVLRO":
            case "INVLRO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logoinviseg.png";
                break;
            case "SATBIMBO":
            case "BIMBO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
            case "PISCINAFACIL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo-piscina.png";
                break;
            case "SATSHALOM":
            case "SHALOM":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_shalom.png";
                break;
            case "SATINTERFORT":
            case "INTERFORT":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_interfort.jpg";
                break;
            case "SATSERVITE":
            case "SERVITE":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_servite.jpg";
                break;
            case "SATPROSECUR":
            case "PROSECUR":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoProsecur.jpg";
                break;
            case "SATGLOVAL":
            case "BKSATGLOVAL":
            case "GLOVAL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/gloval_manifest.jpg";
                break;
            case "SATCOGAR":
            case "COGAR":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoCogar.jpg";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "CONFEDERALBSB":
            case "SATCONFEDERALGO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_confederal.jpg";
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "CORPVS":
            case "CORPVSPE":
            case "SATCORPVS2":
            case "SATCORPVSPE2":  
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
            case "TRANSVIP":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
            case "PRESERVE":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_preserve.jpg";
                break;
            case "SATDELTACORP":
            case "DELTACORP":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_deltacorp.jpg";
                break;                
            case "SATFEDERAL":
            case "FEDERAL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_federal.jpg";
                break;                
            case "SATFORCAALERTA":
            case "FORCAALERTA":
            case "FORCA ALERTA":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_forcaalerta.jpg";
                break;                                
            case "SATVSG":
            case "VSG":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
            case "TSEG":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_tecnoseg.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
            case "AGIL":
            case "AGILVIG":
            case "AGILCOND":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_AGIL.jpg";
                break;
            case "SATLOYAL":
            case "LOYAL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
            case "TRANSVIG":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
            case "INVLMT":
            case "INVLRS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
            case "SATCOMETRA":
            case "GSI":
            case "COMETRA":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
            case "TRANSEXCEL":
                try {
                    switch (codfil) {
                        case "1":
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logotransexcel.jpg";
                            break;
                        case "2001":
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logoDepa.jpg";
                            break;
                        case "3001":
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logoExcel.jpg";
                            break;
                        default:
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logotransexcel.jpg";
                    }
                } catch (Exception e) {
                    url = "https://mobile.sasw.com.br:9091/satellite/logos/logotransexcel.jpg";
                }
                break;
            case "SATRODOB":
            case "SATRODOBAN":
            case "RODOB":
            case "RODOBAN":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
            case "TAMEME":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_tameme.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/eagle.jpg";
                break;
            case "SAS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logosas.jpg";
                break;
            case "SASW":
            case "SATELLITE":
            case "SATSASEX":
            case "SASEX":
            case "SASWMEX":
            case "SATSASWMEX":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logosasw.jpg";
                break;
            case "SATQUALIFOCO":
            case "QUALIFOCO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_qualifoco.jpg";
                break;
            case "IBL":
            case "SATIBL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_ibl.jpg";
                break;
            case "SATTRANSPORTER":
            case "TRANSPORTER":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoTransporter.jpg";
                break;
            case "SATECOVISAO":
            case "ECOVISAO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/Logo_EcoVisao.png";
                break;
              
            case "SPM":
                url = "https://mobile.sasw.com.br:9091/satmobile/logos/logo_ETV.png";
                break;
            case "SATBRASIFORT":
            case "BRASIFORT":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/Logo_Brasifort.png";
                break;
            case "SATCIT":
            case "CIT":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_citcompany.png";
                break;
            case "SATASO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_sataso.png";
                break;
            case "SATGLOBAL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_satglobal.png";
                break;
            case "SATTECBAN":
            case "TECBAN":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_sattecban.png";
                break;
            default:
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo.png";
        }
        return url;
    }

    public static String getLogoAnexo(String empresa, String codfil) {
        String url;
        if (codfil.contains(".0")) {
            codfil = codfil.replace(".0", "");
        }
        switch (empresa) {
            case "SATMAXIMA":
            case "MAXIMA":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_maxima.png";
                break;
            case "SATINVLRO":
            case "INVLRO":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logoinviseg.png";
                break;
            case "SATBIMBO":
            case "BIMBO":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_Bimbo.png";
                break;
            case "SATPISCINAFACIL":
            case "PISCINAFACIL":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo-piscina.png";
                break;
            case "SATSERVITE":
            case "SERVITE":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_servite.jpg";
                break;
            case "SATPROSECUR":
            case "PROSECUR":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\LogoProsecur.jpg";
                break;
            case "SATGLOVAL":
            case "BKSATGLOVAL":
            case "GLOVAL":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\gloval_manifest.jpg";
                break;
            case "SATCOGAR":
            case "COGAR":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\LogoCogar.jpg";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "CONFEDERALBSB":
            case "SATCONFEDERALGO":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_confederal.jpg";
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "CORPVS":
            case "CORPVSPE":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
            case "TRANSVIP":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\LogoTVip.jpg";
                break;
            case "SATPRESERVE":
            case "PRESERVE":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_preserve.jpg";
                break;
            case "SATVSG":
            case "VSG":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_VSG.jpg";
                break;
            case "SATTSEG":
            case "TSEG":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_tecnoseg.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
            case "AGIL":
            case "AGILVIG":
            case "AGILCOND":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_AGIL.jpg";
                break;
            case "SATLOYAL":
            case "LOYAL":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
            case "TRANSVIG":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
            case "INVLMT":
            case "INVLRS":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_invioseg.jpg";
                break;
            case "SATGSI":
            case "SATCOMETRA":
            case "GSI":
            case "COMETRA":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
            case "TRANSEXCEL":
                try {
                    switch (codfil) {
                        case "1":
                            url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logotransexcel.jpg";
                            break;
                        case "2001":
                            url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logoDepa.jpg";
                            break;
                        case "3001":
                            url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logoExcel.jpg";
                            break;
                        default:
                            url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logotransexcel.jpg";
                    }
                } catch (Exception e) {
                    url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logotransexcel.jpg";
                }
                break;
            case "SATRODOB":
            case "SATRODOBAN":
            case "RODOB":
            case "RODOBAN":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\LogoRDB.jpg";
                break;
            case "SATTAMEME":
            case "TAMEME":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_tameme.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\eagle.jpg";
                break;
            case "SAS":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logosas.jpg";
                break;
            case "SASW":
            case "SATELLITE":
            case "SATSASEX":
            case "SASEX":
            case "SASWMEX":
            case "SATSASWMEX":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logosasw.jpg";
                break;
            case "SATFEDERAL":
            case "FEDERALO":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_federal.jpg";
                break;
            case "SATFORCAALERTA":
            case "FORCAALERTA":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_forcaalerta.jpg";
                break;    
             case "SATDELTACORP":
            case "DELTACORP":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_deltacorp.jpg";
                break;        
            case "SATQUALIFOCO":
            case "QUALIFOCO":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_qualifoco.jpg";
                break;
            case "IBL":
            case "SATIBL":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_ibl.jpg";
                break;
            case "SATTRANSPORTER":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\LogoTransporter.jpg";
                break;
            case "SATECOVISAO":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\Logo_EcoVisao.png";
                break;
            case "SPM":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_ETV.png";
                break;
            case "SATCIT":
            case "CIT":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_citcompany.png";
                break;
            case "SATBRINKS":
            case "BRINK":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\Logo_Brinks.jpg";
                break;                    
            case "SATSHALOM":
            case "SHALOM":
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo_shalom.png";
                break;
            default:
                url = "file:\\C:\\xampp\\htdocs\\satellite\\logos\\logo.png";
        }
        return url;
    }
}

package SasBeans;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ReajustesGrp {

    private String codigo;
    private String descricao;
    private String operador;
    private String dt_alter;
    private String hr_alter;

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(String dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.codigo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ReajustesGrp other = (ReajustesGrp) obj;
        if (!Objects.equals(this.codigo, other.codigo)) {
            return false;
        }
        return true;
    }
}

/*
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.PortalRH.PortalRHSatMobWeb;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Pessoa;
import SasDaos.PessoaDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import java.io.File;
import java.io.FileOutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.component.accordionpanel.AccordionPanel;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named(value = "funcoesadm")
@ViewScoped
public class FuncoesAdmMB implements Serializable {

    private Persistencia persistencia, persistenciaSat;
    private List<String> folhasp, cCheque;
    private ArrayList<String> dados;
    private PortalRHSatMobWeb portalrhsatweb;
    private BigDecimal codPessoa;
    private String codFil, caminho, banco, matricula, senhaNova, senhaAtual, matr, dataIni, dataFim, assunto, aviso, operador, log;
    private ArquivoLog logerro;

    public FuncoesAdmMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        try {
            matricula = matricula.replace(".0", "");
        } catch (Exception e) {
            matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        }
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\Contracheques\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        assunto = new String();
        aviso = new String();
        dataFim = new String();
        dataFim = new String();
        matr = new String();
        portalrhsatweb = new PortalRHSatMobWeb();
        log = new String();
    }

    public void PersistenciaSatellite(Persistencia sat) {
        this.persistenciaSat = sat;
    }

    public void enviarMensagem() {
        try {
            if (null == this.assunto || this.assunto.equals("")) {
                throw new Exception("Assunto inválido");
            }
            if (null == this.aviso || this.aviso.equals("")) {
                throw new Exception("Aviso inválido");
            }
            BigDecimal ap = this.portalrhsatweb.getMaxSequencia(this.persistencia);
            this.portalrhsatweb.insereMensagem(ap.toPlainString(), this.codFil, this.assunto, this.aviso,
                    FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgFuncoesAdm').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listaDatasCC() {
        try {
            this.dataIni = this.dataIni.equals("") ? LocalDate.now().toString() : this.dataIni;
            this.dataFim = this.dataFim.equals("") ? LocalDate.now().toString() : this.dataFim;
            this.matr = this.matr.equals("") ? this.matricula : this.matr;
            this.dados = this.portalrhsatweb.getLogsContraChequeUsuario(this.matr, DataAtual.inverteData2(this.dataIni),
                    DataAtual.inverteData2(this.dataFim), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listaDatasFP() {
        try {
            this.dataIni = this.dataIni.equals("") ? LocalDate.now().toString() : this.dataIni;
            this.dataFim = this.dataFim.equals("") ? LocalDate.now().toString() : this.dataFim;
            this.matr = this.matr.equals("") ? this.matricula : this.matr;
            this.dados = this.portalrhsatweb.getLogsFolhaPontoUsuario(this.matr, DataAtual.inverteData2(this.dataIni),
                    DataAtual.inverteData2(this.dataFim), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void logsAdm() {
        try {
            this.folhasp = this.portalrhsatweb.getLogsFolhaPonto(this.matricula, this.persistencia);
            if (this.folhasp.isEmpty()) {
                this.folhasp.add(Messages.getMessageS("SemRegistros"));
            }
            this.cCheque = this.portalrhsatweb.getLogsContraCheque(this.matricula, this.persistencia);
            if (this.cCheque.isEmpty()) {
                this.cCheque.add(Messages.getMessageS("SemRegistros"));
            }
            this.assunto = new String();
            this.aviso = new String();
            this.dataFim = new String();
            this.dataFim = new String();
            this.matr = new String();
            this.dados = new ArrayList<>();
            AccordionPanel ap = (AccordionPanel) FacesContext.getCurrentInstance().getViewRoot().findComponent("adm:accordionAdm");
            ap.setActiveIndex("mensagens");
            PrimeFaces.current().ajax().update("adm");
            PrimeFaces.current().executeScript("PF('dlgFuncoesAdm').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public String trocarSenha(String senhaAntiga) {
        if (this.senhaAtual.equals(senhaAntiga)) {
            try {
                this.portalrhsatweb.trocarSenha(this.senhaNova, this.matricula, FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
                this.portalrhsatweb.geraLogTrocaSenha(this.matricula, this.codFil, persistencia);
                PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                return this.senhaNova;
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SenhaAtualNaoConfere"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        return null;
    }

    public String trocarSenhaAdm(String senhaAntiga) {
        if (this.senhaAtual.equals(senhaAntiga)) {
            try {
                PessoaDao pessoaDao = new PessoaDao();
                Pessoa p = new Pessoa();
                Pessoa p2 = pessoaDao.getPessoaCPF(this.codPessoa.toPlainString().replace(".0", ""), this.persistencia);
                
                p.setPWWeb(this.senhaNova);
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                
                // Atualiza em BD Central
                p.setCodigo(p2.getCodPessoaWEB());
                pessoaDao.atualizaSenhaSatMob(p, this.persistenciaSat);
                
                // Atualiza em BD Local
                p.setCodigo(this.codPessoa);
                pessoaDao.atualizaSenhaSatMob(p, this.persistencia);

                //this.portalrhsatweb.trocarSenha(this.senhaNova, this.matricula, FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
                //this.portalrhsatweb.geraLogTrocaSenha(this.matricula, this.codFil, persistencia);
                PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SenhaSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                return this.senhaNova;
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SenhaAtualNaoConfere"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        return null;
    }

    public void logsFuncion() {
        try {
            this.folhasp = this.portalrhsatweb.getLogsFolhaPonto(this.matricula, this.persistencia);
            if (this.folhasp.isEmpty()) {
                this.folhasp.add(Messages.getMessageS("SemRegistros"));
            }
            this.cCheque = this.portalrhsatweb.getLogsContraCheque(this.matricula, this.persistencia);
            if (this.cCheque.isEmpty()) {
                this.cCheque.add(Messages.getMessageS("SemRegistros"));
            }
            PrimeFaces.current().ajax().update("logfuncion");
            PrimeFaces.current().executeScript("PF('dlgFuncoesFuncion').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public String getSenhaNova() {
        return senhaNova;
    }

    public void setSenhaNova(String senhaNova) {
        this.senhaNova = senhaNova;
    }

    public String getSenhaAtual() {
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) {
        this.senhaAtual = senhaAtual;
    }

    public String getMatr() {
        return matr;
    }

    public void setMatr(String matr) {
        this.matr = matr;
    }

    public String getDataIni() {
        return dataIni;
    }

    public void setDataIni(String dataIni) {
        this.dataIni = dataIni;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getAviso() {
        return aviso;
    }

    public void setAviso(String aviso) {
        this.aviso = aviso;
    }

    public ArrayList<String> getDados() {
        return dados;
    }

    public void setDados(ArrayList<String> dados) {
        this.dados = dados;
    }

    public List<String> getFolhasp() {
        return folhasp;
    }

    public void setFolhasp(List<String> folhasp) {
        this.folhasp = folhasp;
    }

    public List<String> getcCheque() {
        return cCheque;
    }

    public void setcCheque(List<String> cCheque) {
        this.cCheque = cCheque;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.SatMobEW;

import Controller.Supervisao.Supervisoes;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Inspecoes;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PstInspecao;
import SasBeans.PstServ;
import SasBeans.RHPonto;
import SasBeans.RelatorioDoctos;
import SasBeans.Rondas;
import SasBeans.TmktDet;
import SasBeansCompostas.FuncionPstServ;
import SasBeansCompostas.LogsSatMobEW;
import SasBeansCompostas.Marcador;
import SasBeansCompostas.TmktDetPstPstServClientes;
import SasDaos.ClientesDao;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasDaos.InspecoesDao;
import SasDaos.PessoaDao;
import SasDaos.PstInspecaoDao;
import SasDaos.PstServDao;
import SasDaos.RHPontoDao;
import SasDaos.RondasDao;
import SasDaos.TmktDetDao;
import SasLibrary.Supervisao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.LogsSatMobEWDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.Numeros.S2I;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SatMobEWSatWeb {

    public List<PstInspecao> getDetalhesPstInspecoes(LogsSatMobEW logSatMobEW, Persistencia persistencia) throws Exception {
        try {
//            String[] chave = logSatMobEW.getChave().split(";");
//            String secao = chave[0];
//            String codFil = chave[1];
//            String data = chave[2];
//            String codInspecao = chave[3];
//            String matr = chave[4];
            PstInspecaoDao pstInspecaoDao = new PstInspecaoDao();
            return pstInspecaoDao.listaInspecoes(logSatMobEW.getChave(), persistencia);
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public String getRelatorio(LogsSatMobEW logSatMobEW, String idioma, Persistencia persistencia) throws Exception {
        String[] chave = logSatMobEW.getChave().split(";");
        String secao = chave[0];
        String codFil = chave[1];
        String data = chave[2];
        String codInspecao = chave[3];
        String matr = chave[4];

        InspecoesDao inspecoesDao = new InspecoesDao();
        Inspecoes inspecoes = inspecoesDao.getInspecoes(codInspecao, persistencia);

        ClientesDao clientesDao = new ClientesDao();
        Clientes cliente = clientesDao.clientePosto(secao, codFil, persistencia);

        FiliaisDao filiaisDao = new FiliaisDao();
        Filiais filial = filiaisDao.getFilial(codFil, persistencia);

        String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));

        htmlEmail = htmlEmail.replace("@TituloPagina", "@RelatorioInspecao");

        htmlEmail = htmlEmail.replace("@TituloRelatorio", inspecoes.getDescricao());
        htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
        htmlEmail = htmlEmail.replace("@TituloInfo", "");
        htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
        htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));
        htmlEmail = htmlEmail.replace("@Detalhes", "@Detalhes");

        String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

        StringBuilder relatorioEmail = new StringBuilder();

        relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Posto").replace("@TextoPadrao", cliente.getContato()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###"))));

        String dataHtml;
        try {
            LocalDate datetime = LocalDate.parse(data, DateTimeFormatter.ofPattern("yyyyMMdd"));
            dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        } catch (Exception xxx) {
            dataHtml = data;
        }

        relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));

        htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

        htmlEmail = htmlEmail.replace("@Script", "");
        htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(persistencia.getEmpresa(), "0"));
        htmlEmail = htmlEmail.replace("@URL", "");
        htmlEmail = htmlEmail.replace("@MensagemUrl", "");
        return htmlEmail;
    }

    public String getRelatorioInspecao(LogsSatMobEW logSatMobEW, String idioma, Persistencia persistencia) throws Exception {
        String secao = logSatMobEW.getSecao();
        String codFil = logSatMobEW.getCodfil();
        String data = logSatMobEW.getData();
//        String codInspecao = logSatMobEW.getChave();

//        InspecoesDao inspecoesDao = new InspecoesDao();
//        Inspecoes inspecoes = inspecoesDao.getInspecoes(codInspecao, persistencia);
        ClientesDao clientesDao = new ClientesDao();
        Clientes cliente = clientesDao.clientePosto(secao, codFil, persistencia);

        FiliaisDao filiaisDao = new FiliaisDao();
        Filiais filial = filiaisDao.getFilial(codFil, persistencia);

        String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));

        htmlEmail = htmlEmail.replace("@TituloPagina", "@RelatorioInspecao");

        htmlEmail = htmlEmail.replace("@TituloRelatorio", logSatMobEW.getTitulo());
        htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
        htmlEmail = htmlEmail.replace("@TituloInfo", "");
        htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
        htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));
        htmlEmail = htmlEmail.replace("@Detalhes", "@Detalhes");

        String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

        StringBuilder relatorioEmail = new StringBuilder();

        relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Posto").replace("@TextoPadrao", cliente.getContato()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
        relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###"))));

        String dataHtml;
        try {
            LocalDate datetime = LocalDate.parse(data, DateTimeFormatter.ofPattern("yyyyMMdd"));
            dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        } catch (Exception xxx) {
            dataHtml = data;
        }

        relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));

        htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

        htmlEmail = htmlEmail.replace("@Script", "");
        htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(persistencia.getEmpresa(), "0"));
        htmlEmail = htmlEmail.replace("@URL", "");
        htmlEmail = htmlEmail.replace("@MensagemUrl", "");
        return htmlEmail;
    }

    public void atualizaFiltroWeb(LogsSatMobEW logSatMobEW, boolean filtroWeb, Persistencia persistencia) throws Exception {
        try {
            Supervisao supervisaoDao = new Supervisao();
            supervisaoDao.updateFiltroWeb(logSatMobEW.getChave().split(";")[0], filtroWeb, persistencia);
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public boolean isFiltroWeb(LogsSatMobEW logSatMobEW, Persistencia persistencia) throws Exception {
        try {
            Supervisoes tmktDetPstDao = new Supervisoes();
            TmktDetPstPstServClientes relatorio = tmktDetPstDao.obterSupervisao(logSatMobEW.getChave().split(";")[0], persistencia);
            return relatorio.getTmktdetpst().isFiltroWeb();
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rondas> obterRondas(LogsSatMobEW logSatMobEW, Persistencia persistencia) throws Exception {
        try {
            RondasDao rondasDao = new RondasDao();
            return rondasDao.getRondasHora(logSatMobEW.getChave().split(";")[1], logSatMobEW.getChave().split(";")[0],
                    logSatMobEW.getChave().split(";")[2], logSatMobEW.getChave().split(";")[3], persistencia);
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public String getRelatorioRondas(LogsSatMobEW logSatMobEW, List<Rondas> rondas, String idioma,
            boolean download, Persistencia persistencia) throws Exception {
        try {
            String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));
            String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

            String chaveRonda[] = logSatMobEW.getDetalhes().split(";");
            String hrInicio = chaveRonda[0];
            String hrFim = chaveRonda[1];
            BigDecimal rondasHora = new BigDecimal(chaveRonda[2]);
            BigDecimal rondasTotal = new BigDecimal(chaveRonda[3]);

            boolean rondaCompleta = rondasHora.compareTo(rondasTotal) == 0;

//            CONCAT(rondas.secao,';',CONVERT(varchar,rondas.data,112),';',rondas.hora,';',';',rondas.codfil,';')
            ClientesDao clientesDao = new ClientesDao();
            FiliaisDao filiaisDao = new FiliaisDao();

            Clientes cliente = clientesDao.clientePosto(logSatMobEW.getChave().split(";")[0],
                    logSatMobEW.getChave().split(";")[3], persistencia);
            Filiais filial = filiaisDao.getFilial(logSatMobEW.getChave().split(";")[3], persistencia);

            String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            htmlEmail = htmlEmail.replace("@TituloRelatorio", "@ReportRonda");
            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
            htmlEmail = htmlEmail.replace("@TituloInfo", "");
            htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
            htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                    : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));

            StringBuilder relatorioEmail = new StringBuilder();

            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Posto").replace("@TextoPadrao", cliente.getContato()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                    + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###"))));
            String dataHtml;
            try {
                LocalDate datetime = LocalDate.parse(logSatMobEW.getChave().split(";")[1], DateTimeFormatter.ofPattern("yyyyMMdd"));
                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            } catch (Exception xxx) {
                dataHtml = logSatMobEW.getChave().split(";")[1];
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));

            String hrInicioHtml, hrFimHtml;
            try {
                LocalTime localtime = LocalTime.parse(hrInicio, DateTimeFormatter.ofPattern("HH:mm"));
                hrInicioHtml = localtime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("hh:mm a") : DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception xxx) {
                hrInicioHtml = hrInicio;
            }
            try {
                LocalTime localtime = LocalTime.parse(hrFim, DateTimeFormatter.ofPattern("HH:mm"));
                hrFimHtml = localtime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("hh:mm a") : DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception xxx) {
                hrFimHtml = hrFim;
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@InicioRonda").replace("@TextoPadrao", hrInicioHtml));
            if (rondaCompleta) {
                relatorioEmail.append(padrao2colunas.replace("@Padrao", "@FimRonda").replace("@TextoPadrao", hrFimHtml));
            }

            if (download) {
                String padraoImagem = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_imagem.html"));

                List<Marcador> marcadores = new ArrayList<>();
                Marcador marcador;
                for (Rondas ronda : rondas) {
                    marcador = new Marcador();
                    marcador.setLatitude(ronda.getLatitude());
                    marcador.setLongitude(ronda.getLongitude());
                    marcador.setLocalidade(ronda.getDescricao());
                    marcadores.add(marcador);
                }

                if (!marcadores.isEmpty()) {

//                    String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    String urlEmail = "http://localhost:9080/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";

                    String caminho = "C:/xampp/htdocs/satellite/fotos/" + persistencia.getEmpresa() + "/"
                            + FuncoesString.RecortaString(logSatMobEW.getData(), 0, 10).replaceAll("-", "") + "/";
                    String nome = logSatMobEW.getChave().split(";")[3] + "_mapa.png";

                    // enviandoImagemMapa(mapaEstatico(marcadores, true), caminho, nome); Desativado em 21/03/2023

//                    relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
//                        .replace("@ImagemMensagem", "@MensagemMapa")
//                        .replace("@ImagemRelatorio", urlEmail+nome));
                    relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                            .replace("@ImagemMensagem", "@MensagemMapa")
                            .replace("@ImagemRelatorio", caminho + nome));
                }
            }

            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());
            htmlEmail = htmlEmail.replace("@Script", "");
            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
            htmlEmail = htmlEmail.replace("@URL", "");
            htmlEmail = htmlEmail.replace("@MensagemUrl", "");
            return htmlEmail;
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Obtem a rota feita pelo prestador de serviço
     *
     * @param logSatMobEW
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> obterRotaPrestador(LogsSatMobEW logSatMobEW, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDaoEWDao = new LogsSatMobEWDao();
            return logsSatMobEWDaoEWDao.obterRotaPrestador(logSatMobEW.getChave().split(";")[0], logSatMobEW.getChave().split(";")[1], persistencia);
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public String getRelatorioRotaPrestador(LogsSatMobEW logSatMobEW, List<LogsSatMobEW> paradas, String idioma,
            boolean download, Persistencia persistencia) throws Exception {
        try {
            String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));
            String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

            String chaveRonda[] = logSatMobEW.getDetalhes().split(";");
            String hrInicio = chaveRonda[0];
            String hrFim = chaveRonda[1];
//            
//            ClientesDao clientesDao = new ClientesDao();
            FiliaisDao filiaisDao = new FiliaisDao();
//            
//            Clientes cliente = clientesDao.clienteContato(logSatMobEW.getSecao(),
//                    logSatMobEW.getCodfil(), persistencia);
            Filiais filial = filiaisDao.getFilial(logSatMobEW.getCodfil(), persistencia);

            String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            htmlEmail = htmlEmail.replace("@TituloRelatorio", "@ReportRota");
            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", filial.getDescricao());
            htmlEmail = htmlEmail.replace("@TituloInfo", "");
            htmlEmail = htmlEmail.replace("@TituloEndereco", filial.getEndereco() + ", " + filial.getBairro() + ". " + filial.getCidade() + "/" + filial.getUF());
            htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                    : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));

            StringBuilder relatorioEmail = new StringBuilder();

//            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Local").replace("@TextoPadrao", cliente.getContato()));
//            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao",cliente.getEnde()+" - "+cliente.getBairro()));
//            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao",cliente.getCidade()+"/"+cliente.getEstado()+" - "
//             + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###")))); 
            String dataHtml;
            try {
                LocalDate datetime = LocalDate.parse(logSatMobEW.getChave().split(";")[1], DateTimeFormatter.ofPattern("yyyyMMdd"));
                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            } catch (Exception xxx) {
                dataHtml = logSatMobEW.getChave().split(";")[1];
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));

            String hrInicioHtml, hrFimHtml;
            try {
                LocalTime localtime = LocalTime.parse(hrInicio, DateTimeFormatter.ofPattern("HH:mm"));
                hrInicioHtml = localtime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("hh:mm a") : DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception xxx) {
                hrInicioHtml = hrInicio;
            }
            try {
                LocalTime localtime = LocalTime.parse(hrFim, DateTimeFormatter.ofPattern("HH:mm"));
                hrFimHtml = localtime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("hh:mm a") : DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception xxx) {
                hrFimHtml = hrFim;
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@InicioRota").replace("@TextoPadrao", hrInicioHtml));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@UltimoServico").replace("@TextoPadrao", hrFimHtml));

            if (download) {
                String padraoImagem = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_imagem.html"));

                List<Marcador> marcadores = new ArrayList<>();
                Marcador marcador;
                for (LogsSatMobEW parada : paradas) {
                    marcador = new Marcador();
                    marcador.setLatitude(parada.getLatitude());
                    marcador.setLongitude(parada.getLongitude());
                    marcador.setLocalidade(parada.getPosto());
//                    marcadores.add(marcador);
                }

                if (!marcadores.isEmpty()) {

//                    String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    String urlEmail = "http://localhost:9080/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";

                    String caminho = "C:/xampp/htdocs/satellite/fotos/" + persistencia.getEmpresa() + "/"
                            + FuncoesString.RecortaString(logSatMobEW.getData(), 0, 10).replaceAll("-", "") + "/";
                    String nome = logSatMobEW.getChave().split(";")[3] + "_mapa.png";

                    //enviandoImagemMapa(mapaEstatico(marcadores, true), caminho, nome); Desativado em 21/03/2023

//                    relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
//                        .replace("@ImagemMensagem", "@MensagemMapa")
//                        .replace("@ImagemRelatorio", urlEmail+nome));
                    relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                            .replace("@ImagemMensagem", "@MensagemMapa")
                            .replace("@ImagemRelatorio", caminho + nome));
                }
            }

            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());
            htmlEmail = htmlEmail.replace("@Script", "");
            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
            htmlEmail = htmlEmail.replace("@URL", "");
            htmlEmail = htmlEmail.replace("@MensagemUrl", "");
            return htmlEmail;
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public String getRelatorioReport(LogsSatMobEW logSatMobEW, String idioma, boolean download, Persistencia persistencia) throws Exception {
        try {
            String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));
            String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

            Supervisoes tmktDetPstDao = new Supervisoes();
            ClientesDao clientesDao = new ClientesDao();
            FiliaisDao filiaisDao = new FiliaisDao();

            TmktDetPstPstServClientes relatorio = tmktDetPstDao.obterSupervisao(logSatMobEW.getChave().split(";")[0], persistencia);
            Clientes cliente = clientesDao.clientePosto(relatorio.getPstserv().getSecao(),
                    relatorio.getPstserv().getCodFil().toPlainString(), persistencia);
            Filiais filial = filiaisDao.getFilial(relatorio.getPstserv().getCodFil().toPlainString(), persistencia);

            String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            if (logSatMobEW.getTipo().equals("2")
                    && (null == logSatMobEW.getPosto()
                    || logSatMobEW.getPosto().equals(""))) {
                htmlEmail = htmlEmail.replace("ref=\"SomenteComPosto\" rowspan=\"5\"", "rowspan=\"2\"");
                htmlEmail = htmlEmail.replace("ref=\"SomenteComPosto\"", "style=\"display:none\"");
            }

            htmlEmail = htmlEmail.replace("@TituloRelatorio", "@ReportEvento");
            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
            htmlEmail = htmlEmail.replace("@TituloInfo", "");
            htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
            htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                    : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));

            StringBuilder relatorioEmail = new StringBuilder();

            if (logSatMobEW.getTipo().equals("2")
                    && (null == logSatMobEW.getPosto()
                    || logSatMobEW.getPosto().equals(""))) {
                // POSTO NÃO INFORMADO
            } else {
                relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Posto").replace("@TextoPadrao", cliente.getContato()));
                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getNRed() + " - " + cliente.getNome()));
                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
                relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                        + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###"))));
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Ocorrencia").replace("@TextoPadrao", relatorio.getTmktdetpst().getHistorico()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Detalhes").replace("@TextoPadrao", relatorio.getTmktdetpst().getDetalhes().replace("\\N", "<br>")));

            String dataHtml;
            try {
                LocalDate datetime = LocalDate.parse(relatorio.getTmktdetpst().getData(), DateTimeFormatter.ofPattern("yyyyMMdd"));
                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            } catch (Exception xxx) {
                dataHtml = relatorio.getTmktdetpst().getData();
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Hora").replace("@TextoPadrao", relatorio.getTmktdetpst().getHora()));

            if (download) {
                String padraoImagem = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_imagem.html"));
                String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
//                String urlEmail = "http://localhost:9080/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";

                String foto = FuncoesString.RecortaString(logSatMobEW.getData(), 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[0].replace(".0", ""), 8, "0") + "_";

                String mapa = FuncoesString.RecortaString(logSatMobEW.getData(), 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[0].replace(".0", ""), 8, "0")
                        + "_mapa.png";

                String[] fotos = logSatMobEW.getFotos().split(";");
                for (int i = 0; i < fotos.length; i++) {
                    relatorioEmail.append(padraoImagem
                            .replace("@ImagemId", "foto" + i)
                            .replace("@ImagemRelatorio", fotos[i]));
                }

                relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                        .replace("@ImagemMensagem", "@MensagemMapa")
                        .replace("@ImagemRelatorio", urlEmail + mapa));
            }

            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());
            htmlEmail = htmlEmail.replace("@Script", "");
            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
            htmlEmail = htmlEmail.replace("@URL", "");
            htmlEmail = htmlEmail.replace("@MensagemUrl", "");
            return htmlEmail;
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Gera o relatório feito por prestadores em HTML
     *
     * @param logSatMobEW
     * @param idioma
     * @param download
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getRelatorioPrestador(LogsSatMobEW logSatMobEW, String idioma, boolean download, Persistencia persistencia) throws Exception {
        try {
            String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));
            String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

            TmktDetDao tmktDetDao = new TmktDetDao();
            ClientesDao clientesDao = new ClientesDao();
            FiliaisDao filiaisDao = new FiliaisDao();

            TmktDet relatorio = tmktDetDao.getTmktDet(logSatMobEW.getChave().split(";")[0], "0", persistencia);
            Clientes cliente = clientesDao.clienteContato(relatorio.getCodCont(), relatorio.getCodFil(), persistencia);
            Filiais filial = filiaisDao.getFilial(relatorio.getCodFil(), persistencia);

            String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            htmlEmail = htmlEmail.replace("@TituloRelatorio", "@ReportEvento");
            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", cliente.getContato());
            htmlEmail = htmlEmail.replace("@TituloInfo", "");
            htmlEmail = htmlEmail.replace("@TituloEndereco", cliente.getEnde() + ", " + cliente.getBairro() + ". " + cliente.getCidade() + "/" + cliente.getEstado());
            htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                    : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));

            StringBuilder relatorioEmail = new StringBuilder();

            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Local").replace("@TextoPadrao", cliente.getContato()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getEnde() + " - " + cliente.getBairro()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao:", "").replace("@TextoPadrao", cliente.getCidade() + "/" + cliente.getEstado() + " - "
                    + (idioma.equals("en") ? (cliente.getCEP().startsWith("000") ? cliente.getCEP().replace("000", "") : cliente.getCEP()) : formatarString(cliente.getCEP(), "#####-###"))));

            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Ocorrencia").replace("@TextoPadrao", relatorio.getHistorico()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Detalhes").replace("@TextoPadrao", relatorio.getDetalhes().replace("\\N", "<br>")));

            String dataHtml;
            try {
                LocalDate datetime = LocalDate.parse(relatorio.getData(), DateTimeFormatter.ofPattern("yyyyMMdd"));
                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            } catch (Exception xxx) {
                dataHtml = relatorio.getData();
            }
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Data").replace("@TextoPadrao", dataHtml));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Hora").replace("@TextoPadrao", relatorio.getHora()));

            if (download) {
                String padraoImagem = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_imagem.html"));
                String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
//                String urlEmail = "http://localhost:9080/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";

                String foto = FuncoesString.RecortaString(logSatMobEW.getData(), 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[0].replace(".0", ""), 8, "0") + "_";

                String mapa = FuncoesString.RecortaString(logSatMobEW.getData(), 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[0].replace(".0", ""), 8, "0")
                        + "_mapa.png";

                String[] fotos = logSatMobEW.getFotos().split(";");
                for (int i = 0; i < fotos.length; i++) {
                    relatorioEmail.append(padraoImagem
                            .replace("@ImagemId", "foto" + i)
                            .replace("@ImagemRelatorio", fotos[i]));
                }

                relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                        .replace("@ImagemMensagem", "@MensagemMapa")
                        .replace("@ImagemRelatorio", urlEmail + mapa));
            }

            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());
            htmlEmail = htmlEmail.replace("@Script", "");
            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
            htmlEmail = htmlEmail.replace("@URL", "");
            htmlEmail = htmlEmail.replace("@MensagemUrl", "");
            return htmlEmail;
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public String getRelatorioBatidaNovo(LogsSatMobEW logSatMobEW, String idioma, boolean download, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funcionDao = new FuncionDao();
            RHPontoDao rhPontoDao = new RHPontoDao();
            FiliaisDao filiaisDao = new FiliaisDao();

            String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));
            String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

            RHPonto rhPonto = rhPontoDao.buscaPonto(logSatMobEW.getChave().split(";")[0], logSatMobEW.getChave().split(";")[1],
                    logSatMobEW.getChave().split(";")[2], persistencia);
            FuncionPstServ funcion = funcionDao.listaFuncionFilialMatr(logSatMobEW.getChave().split(";")[3], logSatMobEW.getChave().split(";")[0],
                    logSatMobEW.getChave().split(";")[1], persistencia);
            Filiais filial = filiaisDao.getFilial(logSatMobEW.getChave().split(";")[3], persistencia);
            String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            String dataHtml;
            try {
                LocalDateTime datetime = LocalDateTime.parse(logSatMobEW.getData(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"));
                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            } catch (Exception xxx) {
                dataHtml = logSatMobEW.getData();
            }

//            htmlEmail = htmlEmail.replace("@TituloPagina", idioma.equals("en") ? check+" - "+logSatMobEW.getChave().split(";")[1].replace(".0", "") : "Relatório Batida de Ponto - "+logSatMobEW.getChave().split(";")[1].replace(".0", ""));
            htmlEmail = htmlEmail.replace("@TituloRelatorio", S2I(logSatMobEW.getFotos()) % 2 == 0 ? "@CheckOutReport" : "@CheckInReport");
            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", funcion.getPstserv().getLocal());
            htmlEmail = htmlEmail.replace("@TituloInfo", funcion.getPstserv().getDescContrato());
            htmlEmail = htmlEmail.replace("@TituloEndereco", filial.getRazaoSocial());
            htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                    : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));
//            htmlEmail = htmlEmail.replace("@Detalhes", idioma.equals("en") ? "Details" : "Detalhes");

            StringBuilder relatorioEmail = new StringBuilder();

            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Officer").replace("@TextoPadrao", funcion.getFuncion().getNome()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Matricula").replace("@TextoPadrao", funcion.getFuncion().getMatr().toBigInteger().toString()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@DataHora").replace("@TextoPadrao", dataHtml + " - " + rhPonto.getHora()));

            if (download) {
                String padraoImagem = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_imagem.html"));
                String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";
//                String urlEmail = "http://localhost:9080/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";
                String foto = FuncoesString.RecortaString(logSatMobEW.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[1].replace(".0", ""), 8, "0") + "_"
                        + logSatMobEW.getFotos() + ".jpg";
                String mapa = FuncoesString.RecortaString(logSatMobEW.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[1].replace(".0", ""), 8, "0")
                        + "_mapa_" + logSatMobEW.getFotos() + ".png";

                relatorioEmail.append(padraoImagem
                        .replace("@ImagemId", "foto" + logSatMobEW.getFotos())
                        .replace("@ImagemRelatorio", urlEmail + foto));

                relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                        .replace("@ImagemMensagem", "@MensagemMapa")
                        .replace("@ImagemRelatorio", urlEmail + mapa));
            }

            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

            htmlEmail = htmlEmail.replace("@Script", "");

            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
            htmlEmail = htmlEmail.replace("@URL", "");
            htmlEmail = htmlEmail.replace("@MensagemUrl", "");
            return htmlEmail;
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    public String getRelatorioBatida(LogsSatMobEW logSatMobEW, String idioma, boolean download, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funcionDao = new FuncionDao();
            RHPontoDao rhPontoDao = new RHPontoDao();
            FiliaisDao filiaisDao = new FiliaisDao();

            String htmlEmail = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/relatorioEW.html"));
            String padrao2colunas = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_duas_colunas.html"));

            RHPonto rhPonto = rhPontoDao.buscaPonto(logSatMobEW.getChave().split(";")[0], logSatMobEW.getChave().split(";")[1],
                    logSatMobEW.getChave().split(";")[2], persistencia);
            FuncionPstServ funcion = funcionDao.listaFuncionFilialMatr(logSatMobEW.getChave().split(";")[3], logSatMobEW.getChave().split(";")[0],
                    logSatMobEW.getChave().split(";")[1], persistencia);
            Filiais filial = filiaisDao.getFilial(logSatMobEW.getChave().split(";")[3], persistencia);
            String empresa = persistencia.getEmpresa().equals("SATSASEX") ? "SASW" : persistencia.getEmpresa().replace("SAT", "");

            String dataHtml;
            try {
                LocalDateTime datetime = LocalDateTime.parse(logSatMobEW.getData(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"));
                dataHtml = datetime.format(idioma.equals("en") ? DateTimeFormatter.ofPattern("MM/dd/yyyy") : DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            } catch (Exception xxx) {
                dataHtml = logSatMobEW.getData();
            }

//            htmlEmail = htmlEmail.replace("@TituloPagina", idioma.equals("en") ? check+" - "+logSatMobEW.getChave().split(";")[1].replace(".0", "") : "Relatório Batida de Ponto - "+logSatMobEW.getChave().split(";")[1].replace(".0", ""));
            htmlEmail = htmlEmail.replace("@TituloRelatorio", S2I(logSatMobEW.getFotos()) % 2 == 0 ? "@CheckOutReport" : "@CheckInReport");
            htmlEmail = htmlEmail.replace("@SubTituloRelatorio", funcion.getPstserv().getLocal());
            htmlEmail = htmlEmail.replace("@TituloInfo", funcion.getPstserv().getDescContrato());
            htmlEmail = htmlEmail.replace("@TituloEndereco", filial.getRazaoSocial());
            htmlEmail = htmlEmail.replace("@TituloTelefone", idioma.equals("en") ? FuncoesString.formatarString(filial.getFone(), "(###) ###-####")
                    : FuncoesString.formatarString(filial.getFone(), "(##) ########?"));
//            htmlEmail = htmlEmail.replace("@Detalhes", idioma.equals("en") ? "Details" : "Detalhes");

            StringBuilder relatorioEmail = new StringBuilder();

            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Officer").replace("@TextoPadrao", funcion.getFuncion().getNome()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@Matricula").replace("@TextoPadrao", funcion.getFuncion().getMatr().toBigInteger().toString()));
            relatorioEmail.append(padrao2colunas.replace("@Padrao", "@DataHora").replace("@TextoPadrao", dataHtml + " - " + rhPonto.getHora()));

            if (download) {
                String padraoImagem = LerArquivo.obterConteudo(SatMobEWSatWeb.class.getResourceAsStream("/relatorios/EW_imagem.html"));
                String urlEmail = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";
//                String urlEmail = "http://localhost:9080/satellite/fotos/" + persistencia.getEmpresa() + "/ponto/";
                String foto = FuncoesString.RecortaString(logSatMobEW.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[1].replace(".0", ""), 8, "0") + "_"
                        + logSatMobEW.getFotos() + ".jpg";
                String mapa = FuncoesString.RecortaString(logSatMobEW.getChave().split(";")[0], 0, 10).replaceAll("-", "") + "/"
                        + FuncoesString.PreencheEsquerda(logSatMobEW.getChave().split(";")[1].replace(".0", ""), 8, "0")
                        + "_mapa_" + logSatMobEW.getFotos() + ".png";

                relatorioEmail.append(padraoImagem
                        .replace("@ImagemId", "foto" + logSatMobEW.getFotos())
                        .replace("@ImagemRelatorio", urlEmail + foto));

                relatorioEmail.append(padraoImagem.replace("@ImagemId", "mapa")
                        .replace("@ImagemMensagem", "@MensagemMapa")
                        .replace("@ImagemRelatorio", urlEmail + mapa));
            }

            htmlEmail = htmlEmail.replace("@Relatorio", relatorioEmail.toString());

            htmlEmail = htmlEmail.replace("@Script", "");

            htmlEmail = htmlEmail.replace("@ImagemLogo", getLogo(empresa, "0"));
            htmlEmail = htmlEmail.replace("@URL", "");
            htmlEmail = htmlEmail.replace("@MensagemUrl", "");
            return htmlEmail;
        } catch (Exception e) {
            throw new Exception("satmobewsatweb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listar postos para filtro
     *
     * @param codPessoa
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> getPstSerList(BigDecimal codPessoa, String codfil, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            return pstServDao.listarPostosSaspw(codPessoa, codfil, persistencia);
            //return pstServDao.listarPostosPessoa(codPessoa, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Listar postos para filtro
     *
     * @param codfil
     * @param latitude
     * @param longitude
     * @return
     * @throws Exception
     */
    public List<PstServ> getPstListaCompleta(String codfil, String latitude, String longitude, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            return pstServDao.listarPostosCompleto(codfil, latitude, longitude, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Listar postos para filtro
     *
     * @param codfil
     * @param codSecao
     * @param latitude
     * @param longitude
     * @return
     * @throws Exception
     */
    public List<PessoaCliAut> getContatosListaCompleta(String codfil, String codSecao, String latitude, String longitude, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            return pstServDao.listarContatosCompleto(codfil, codSecao, latitude, longitude, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    public void inserirRelatorio(RelatorioDoctos relDocs, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            pstServDao.inserirRelatorio(relDocs, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Listar postos para filtro
     *
     * @param codPessoa
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> getPstServClientList(BigDecimal codPessoa, String codfil, Persistencia persistencia) throws Exception {
        try {
            PstServDao pstServDao = new PstServDao();
            PessoaCliAut cliente = new PessoaCliAut();
            cliente.setCodigo(codPessoa.toBigInteger().toString());
            return pstServDao.listarPostosCliente(cliente, false, persistencia);
        } catch (Exception e) {
            throw new Exception("falha - " + e.getMessage());
        }
    }

    /**
     * Contagem do cadastro de clientes
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;

            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat("yyyyMMdd").parse(filtro.get("data")));
            String tipo = "";
            switch (c.get(Calendar.DAY_OF_WEEK)) {
                case 1:
                    tipo = "Dom";
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    tipo = "SegSex";
                    break;
                case 7:
                    tipo = "Sab";
                    break;
            }

            return logsSatMobEWDao.totalLogs(filtro.get("data"), filtro.get("codfil"), filtro.get("matricula"),
                    filtro.get("codpessoa"), filtro.get("posto"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),
                    persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, BigDecimal codPessoa, String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;

            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat("yyyyMMdd").parse(data1));
            String tipo = "";
            switch (c.get(Calendar.DAY_OF_WEEK)) {
                case 1:
                    tipo = "Dom";
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    tipo = "SegSex";
                    break;
                case 7:
                    tipo = "Sab";
                    break;
            }

            return logsSatMobEWDao.totalLogs(data1, data2, filtro.get("codfil"), filtro.get("matricula"),"",
                    filtro.get("codpessoa"), filtro.get("posto"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),
                    persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }
    
    public Integer contagem(Map filtros, BigDecimal codPessoa, String data1, String data2, String Matricula, String NomeFuncionario, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;

            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat("yyyyMMdd").parse(data1));
            String tipo = "";
            switch (c.get(Calendar.DAY_OF_WEEK)) {
                case 1:
                    tipo = "Dom";
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    tipo = "SegSex";
                    break;
                case 7:
                    tipo = "Sab";
                    break;
            }

            if(null != Matricula && !Matricula.equals("")){
                Matricula = "IN(" + Matricula + ")";
            }
            
            return logsSatMobEWDao.totalLogs(data1, data2, filtro.get("codfil"), Matricula, NomeFuncionario,
                    filtro.get("codpessoa"), filtro.get("posto"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),
                    persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de clientes
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;

            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat("yyyyMMdd").parse(filtro.get("data")));
            String tipo = "";
            switch (c.get(Calendar.DAY_OF_WEEK)) {
                case 1:
                    tipo = "Dom";
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    tipo = "SegSex";
                    break;
                case 7:
                    tipo = "Sab";
                    break;
            }

            return logsSatMobEWDao.obterLogs(primeiro, linhas, filtro.get("data"), filtro.get("codfil"), filtro.get("matricula"),
                    filtro.get("codpessoa"), filtro.get("secao"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),
                    persistencia);
        } catch (Exception e) {
            throw new Exception("SatMobEWSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<LogsSatMobEW> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;

            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat("yyyyMMdd").parse(data1));
            String tipo = "";
            switch (c.get(Calendar.DAY_OF_WEEK)) {
                case 1:
                    tipo = "Dom";
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    tipo = "SegSex";
                    break;
                case 7:
                    tipo = "Sab";
                    break;
            }

            return logsSatMobEWDao.obterLogs(primeiro, linhas, data1, data2, filtro.get("codfil"), "","",
                    filtro.get("codpessoa"), filtro.get("secao"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),"",
                    persistencia);
        } catch (Exception e) {
            throw new Exception("SatMobEWSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<LogsSatMobEW> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, String matriculas, String nomeColaborador, String data1, String data2, String ordem, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;

            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat("yyyyMMdd").parse(data1));
            String tipo = "";
            switch (c.get(Calendar.DAY_OF_WEEK)) {
                case 1:
                    tipo = "Dom";
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    tipo = "SegSex";
                    break;
                case 7:
                    tipo = "Sab";
                    break;
            }

            if(null != matriculas && !matriculas.equals("")){
                matriculas = "IN(" + matriculas + ")";
            }
            
            return logsSatMobEWDao.obterLogs(primeiro, linhas, data1, data2, filtro.get("codfil"), matriculas, nomeColaborador,
                    filtro.get("codpessoa"), filtro.get("posto"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),ordem,
                    persistencia);
        } catch (Exception e) {
            throw new Exception("SatMobEWSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de clientes
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<LogsSatMobEW> listagemResumo(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;
            String tipo = "6";
            
            try {
                Calendar c = Calendar.getInstance();
                c.setTime(new SimpleDateFormat("yyyyMMdd").parse(filtro.get("data")));
                switch (c.get(Calendar.DAY_OF_WEEK)) {
                    case 1:
                        tipo = "Dom";
                        break;
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                        tipo = "SegSex";
                        break;
                    case 7:
                        tipo = "Sab";
                        break;
                }
            } catch (Exception e) {

            }
            return logsSatMobEWDao.obterLogsResumoQde(primeiro, linhas, filtro.get("data"), filtro.get("codfil"), filtro.get("matricula"),
                    filtro.get("codpessoa"), filtro.get("posto"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),
                    persistencia);
        } catch (Exception e) {
            throw new Exception("SatMobEWSatWeb.falhageral<message>" + e.getMessage());
        }
    }
    
    public List<LogsSatMobEW> listagemResumo(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, String data1, String data2, Persistencia persistencia) throws Exception {
        try {
            LogsSatMobEWDao logsSatMobEWDao = new LogsSatMobEWDao();
            Map<String, String> filtro = filtros;
            String tipo = "6";
            
            try {
                Calendar c = Calendar.getInstance();
                c.setTime(new SimpleDateFormat("yyyyMMdd").parse(data1));
                switch (c.get(Calendar.DAY_OF_WEEK)) {
                    case 1:
                        tipo = "Dom";
                        break;
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                        tipo = "SegSex";
                        break;
                    case 7:
                        tipo = "Sab";
                        break;
                }
            } catch (Exception e) {

            }
            return logsSatMobEWDao.obterLogsResumoQde(primeiro, linhas, data1, data2, filtro.get("codfil"), filtro.get("matricula"),
                    filtro.get("codpessoa"), filtro.get("posto"), tipo, filtro.get("filtroWeb"), codPessoa, filtro.get("tipo"),
                    persistencia);
        } catch (Exception e) {
            throw new Exception("SatMobEWSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void trocarSenhaCliente(BigDecimal codPessoa, String senha, String operador, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(codPessoa);
            Pessoa temp = pessoadao.BuscaPessoa(pessoa, local).get(0);
            pessoa.setOperador(operador);
            pessoa.setPWWeb(senha);
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, local);
            pessoa.setCodigo(temp.getCodPessoaWEB());
            pessoadao.atualizaSenhaSatMob(pessoa, central);
        } catch (Exception e) {
            throw new Exception("login.falhaalterarsenhacliente<message>" + e.getMessage());
        }
    }

    public Boolean verificaSenha(BigDecimal codPessoa, String senha, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            Pessoa p = pessoadao.getPessoaCodigo(codPessoa, persistencia);
            if (p.getPWWeb().equals(senha)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new Exception("login.falhalistarclientes<message>" + e.getMessage());
        }
    }

    private String mapaEstatico(List<Marcador> marcadores, boolean caminho) {
        StringBuilder url = new StringBuilder("http://maps.googleapis.com/maps/api/staticmap"
                + "?size=640x480&format=PNG&zoom=19"
                + "&key=AIzaSyA3fIxeYPRGxBm_EVlNGImhZcDx1WvYe_w");
        StringBuilder path = new StringBuilder("&path=color:0xff9900|weight:6");
        for (Marcador marcador : marcadores) {
            url.append(marcador.PADRAO_MARCADOR.replace("@Icon", marcador.getIcon())
                    .replace("@Localidade", marcador.getLocalidade())
                    .replace("@Latitude", marcador.getLatitude())
                    .replace("@Longitude", marcador.getLongitude()));
            if (caminho) {
                path.append("|").append(marcador.getLatitude()).append(",").append(marcador.getLongitude());
            }
        }
        return url.append(caminho ? path.toString() : "").toString().replace(" ", "%20");
    }

    private boolean enviandoImagemMapa(String url, String caminho, String nome) {
        boolean enviado = false;
        try {
            //Criando imagem
            URL u = new URL(url);
            InputStream in = new BufferedInputStream(u.openStream());
            ByteArrayOutputStream o = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int n = 0;
            while (-1 != (n = in.read(buf))) {
                o.write(buf, 0, n);
            }
            o.close();
            in.close();
            byte[] r = o.toByteArray();

            File diretorio = new File(caminho);
            if (!diretorio.exists()) {
                diretorio.mkdirs();
            }

            FileOutputStream fos = new FileOutputStream(caminho + nome);
            fos.write(r);
            fos.close();

            enviado = true;
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return enviado;
    }

    public void envioMensagem(BigDecimal codPessoa, BigDecimal codOperador, String mensagem, Persistencia local) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            pessoadao.envioMensagemSatMob(codPessoa, codOperador, mensagem, local);

        } catch (Exception e) {
            throw new Exception("SatMobEWSatWeb.envioMensagem<message>" + e.getMessage());
        }
    }
}

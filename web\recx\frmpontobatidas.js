//SASW Tecnologia
//SATMOB
//FrmPontoBatidas - ScriptJS
//Autor: Carlos
//Data: 02/03/2022
//
//Atualizacoes de versao: 
//
//

const video = document.getElementById('webCamera');
const editMatr = document.getElementById('editMatr');
//const btnEnviarMatr = document.getElementById('btnEnviarMatr');
const btnAuth = document.getElementById('btnautentica');
const CadBiom = document.getElementById('btncadBio1');
const vImgSaidaBox = document.getElementById('vImgSaidaBox');
const btnSair = document.getElementById('btnSair');

const descriptors = [];
var vimgfoto = "";
var vNomeFoto = "";
var vNomeFotoCap = "";
var vPathWeb = "";
var vPathLocal = "";
var vCount = 0;
var vIntervalo;

window.onload = function(){	    
	var vMatrP = sessionStorage.getItem('matrAutenticada');
	if ((vMatrP == null) || (vMatrP == "")){
	   document.getElementById("editMatr").focus();
	}else{
		document.getElementById("editMatr").value = vMatrP;
		autenticar();
		
	}
}

function autenticaBio(){
    var vMatrP = document.getElementById('editMatr').value;
    if (vMatrP != ""){	
       sessionStorage.setItem('matrSat', vMatrP);
       sessionStorage.setItem('frmbiometriaautentica', 'frmpontobatidas.html');	   
       window.location.href = "frmbiometriaautentica.html"; //"frmbiometriaautentica.html?matr="+vMatrP;
    }else{
       //document.getElementById("editMensagem").value = "Informe a sua matrícula";
       //document.getElementById("painelMensagens").style.display = "flex";				
       //tempo();		
       messageDlg("Informe a sua matrícula");
       document.getElementById("editMatr").focus();
    }		
}

async function autenticar (){
	var vHeaders = new Headers();
	vHeaders.append("Content-Type", "application/xml");
        //Atribui variaveis
	let matrID = document.querySelector('#editMatr').value;
	let tokID = "B8489FF7205E3217B4E043D70FF9848B"; //document.querySelector('#token').value;
	let vPW = document.querySelector('#editSenha').value;
	let vChaveBio = sessionStorage.getItem('chavebio');
	var vParam = `matr=${matrID}&token=${tokID}&pw=${vPW}&chavebio=${vChaveBio}&data=`;
	var vRequisicao = {
            method: 'POST',
            headers: vHeaders,
            body: vParam,
            redirect: 'follow'
	};
        var vWSSAS = "https://mobile.sasw.com.br/SatWebServicePreHomolog/api/executasvc/obterFotoPW";
	fetch(vWSSAS, vRequisicao)	
	.then(response => response.text())
	.then(result => autenticarSvc(JSON.parse(result)))
	.catch(error => console.log('error', error));
}

async function autenticarSvc(jsonObj) {
    if (jsonObj.matr == 0){			    
        //document.getElementById("editMensagem").value = jsonObj.nome;
        //document.getElementById("painelMensagens").style.display = "flex";				
        //tempo();
        messageDlg(document.getElementById("editMensagem").value = jsonObj.nome);

    }else{
        if (jsonObj.funcion[0].faceid == "NAO REGISTRADO") {
            document.getElementById("fotoBiometria").src = jsonObj.funcion[0].fotobatida1
        } else {
            document.getElementById("fotoBiometria").src = jsonObj.funcion[0].faceid;
        }	
        //   document.getElementById("lbNome").innerHTML = jsonObj.funcion.nome;
        document.getElementById("painelLogin").style.display = "none";
        document.getElementById("painelLogin").setAttribute('hidden', true);
        document.getElementById("sectionFoto").style.display = "flex";		
        document.getElementById("lbMatrF").innerHTML = jsonObj.funcion[0].matr.replace('.0', '');
        document.getElementById("lbNomeF").innerHTML = jsonObj.funcion[0].nome;
        document.getElementById("lbCargoF").innerHTML = jsonObj.funcion[0].cargodescricao;
        document.getElementById("lbFilialF").innerHTML = jsonObj.funcion[0].filialdesc;
        document.getElementById("lbPostoF").innerHTML = jsonObj.funcion[0].posto;
        document.getElementById("lbFoneF").innerHTML = jsonObj.funcion[0].fone;
        document.getElementById("lbemailF").innerHTML = jsonObj.funcion[0].email;
        document.getElementById("painelInfBatida").style.display = "flex";

        document.getElementById("btnEnviar").style.display = "none";

        var tbody = document.getElementById('gridBatidasLn');
        tbody.innerHTML = '';
        var vDt =  "";
        for (var i = 0; i < jsonObj.funcion.length; i++){
                if (vDt != jsonObj.funcion[i].data){
                        var t = `<tbody >
                                        <td>${formataData(jsonObj.funcion[i].data)}</td>
                                        <td>${jsonObj.funcion[i].hora1}</td>
                                        <td>${jsonObj.funcion[i].hora2}</td>
                                        <td>${jsonObj.funcion[i].hora3}</td>
                                        <td>${jsonObj.funcion[i].hora4}</td>	
                                </tbody>`;
                   tbody.innerHTML += t;
                   vDt = jsonObj.funcion[i].data;
                }
        }

    }
}

function formataData(vDtF){
 	var vDt2 = vDtF.substring(8-2)+
			  "/"+vDtF.substr(4,2)+
			  "/"+vDtF.substring(4,-5);
  return vDt2;
}
async function atualizar(){
	sessionStorage.setItem('matrAutenticada', "");
	sessionStorage.setItem('chavebio', "");
	document.location.reload(true);	
}

editMatr.addEventListener("keypress", function(event) {
	if (event.key === "Enter") {
		event.preventDefault();
		document.getElementById("editSenha").focus();				
	}
	//}else if (event.key === "Escape") {		
	//	document.getElementById("btnSair").click();
	//}
});

editSenha.addEventListener("keypress", function(event) {
	if (event.key === "Enter") {
		event.preventDefault();
		document.getElementById("btnEnviar").click();		
	}
});

function tempo(op) {	
	var s = 1;	
	vIntervalo = window.setInterval(function() {
		if (s == 100) { s = 0; }
		if (s > 20) document.getElementById("painelMensagens").style.display = "none";
		if (s > 20) document.getElementById("editMatr").focus();
		if (s > 22) window.clearInterval(vIntervalo);
		s++;
	},100);	
}	

function openModal(nm){
	let modal = document.getElementById(nm);
	if (typeof modal == "undefined" || modal === null){
		return;
	}
	modal.style.display = 'block';
	document.body.style.overflow = 'hidden';
}

function closeModal(nm){
	let modal = document.getElementById(nm);
	if (typeof modal == "undefined" || modal === null){
		return;
	}
	modal.style.display = 'none';
	document.body.style.overflow = 'auto';
}

function messageDlg(vMsg){
	document.getElementById('modal-textoCentral').style.textAlign = "center";
	document.getElementById('modal-textoCentral').innerHTML = vMsg;
	openModal('dv-modal');	
}
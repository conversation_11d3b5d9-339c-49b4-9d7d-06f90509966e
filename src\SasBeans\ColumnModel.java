/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ColumnModel implements Serializable {

    private String header;
    private String property;
    private int position;

    public ColumnModel(String header, String property, int position) {
        this.header = header;
        this.property = property;
        this.position = position;
    }

    public String getHeader() {
        return header;
    }

    public String getProperty() {
        return property;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/cofre_dashboard_geral.css" rel="stylesheet"/>
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>

            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.3/Chart.min.js"></script>
            <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{dashboardCofreGeral.Persistencia(login.pp)}"/>
                <f:viewAction action="#{dashboardCofreGeral.carregarGraficos()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <header>
                <div id="TopoDash" class="col-md-12">
                    <h:form id="top">
                        <p:poll interval="180" listener="#{dashboardCofreGeral.carregarGraficos()}" update="top main msgs" />

                        <div class="col-md-4 col-sm-4 col-xs-12" style="padding-left:0px !important;"> 
                            <label class="Empresa"><h:outputText value="#{dashboardCofreGeral.filiais.descricao}" rendered="#{dashboardCofreGeral.filiais != null}"/></label>
                            <label class="Filial">
                                <h:outputText value="#{dashboardCofreGeral.filiais.endereco}" rendered="#{dashboardCofreGeral.filiais != null}"/>
                                <h:outputText value=" - " rendered="#{dashboardCofreGeral.filiais != null}"/>
                                <h:outputText value="#{dashboardCofreGeral.filiais.bairro}" rendered="#{dashboardCofreGeral.filiais != null}"/>
                                <h:outputText value=" - " rendered="#{dashboardCofreGeral.filiais != null}"/>
                                <h:outputText value="#{dashboardCofreGeral.filiais.cidade}" rendered="#{dashboardCofreGeral.filiais != null}"/>
                                <h:outputText value="/" rendered="#{dashboardCofreGeral.filiais != null}"/>
                                <h:outputText value="#{dashboardCofreGeral.filiais.UF}" rendered="#{dashboardCofreGeral.filiais != null}"/>
                            </label>
                            <label class="Voltar" onclick="window.history.back();"><i class="fa fa-arrow-circle-left"></i>&nbsp;#{localemsgs.Voltar}</label>
                        </div>
                        <div class="col-md-8 col-sm-8 col-xs-12" style="padding:0px !important;">
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.CofresAtivos}</label>
                                    <label ref="Valor">#{dashboardCofreGeral.totalCofres}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.TotalDepositos}</label>
                                    <label ref="Valor">#{dashboardCofreGeral.totalDepositos}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.TotalColetas}</label>
                                    <label ref="Valor">#{dashboardCofreGeral.totalColetas}</label>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.Dia}</label>
                                    <p:selectOneMenu id="slcDia" required="true" styleClass="seletor" value="#{dashboardCofreGeral.dia}" >
                                        <f:selectItems value="#{dashboardCofreGeral.diasMes}" var="dia" itemValue="#{dia}"
                                                       itemLabel="#{dia}" noSelectionValue=""/>

                                        <p:ajax listener="#{dashboardCofreGeral.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.Mes}</label>
                                    <p:selectOneMenu id="slcMes" required="true" styleClass="seletor" value="#{dashboardCofreGeral.mes}" >
                                        <f:selectItem itemLabel="#{localemsgs.JANUARY.toUpperCase()}" itemValue="01" />
                                        <f:selectItem itemLabel="#{localemsgs.FEBRUARY.toUpperCase()}" itemValue="02" />
                                        <f:selectItem itemLabel="#{localemsgs.MARCH.toUpperCase()}" itemValue="03" />
                                        <f:selectItem itemLabel="#{localemsgs.APRIL.toUpperCase()}" itemValue="04" />
                                        <f:selectItem itemLabel="#{localemsgs.MAY.toUpperCase()}" itemValue="05" />
                                        <f:selectItem itemLabel="#{localemsgs.JUNE.toUpperCase()}" itemValue="06" />
                                        <f:selectItem itemLabel="#{localemsgs.JULY.toUpperCase()}" itemValue="07" />
                                        <f:selectItem itemLabel="#{localemsgs.AUGUST.toUpperCase()}" itemValue="08" />
                                        <f:selectItem itemLabel="#{localemsgs.SEPTEMBER.toUpperCase()}" itemValue="09" />
                                        <f:selectItem itemLabel="#{localemsgs.OCTOBER.toUpperCase()}" itemValue="10" />
                                        <f:selectItem itemLabel="#{localemsgs.NOVEMBER.toUpperCase()}" itemValue="11" />
                                        <f:selectItem itemLabel="#{localemsgs.DECEMBER.toUpperCase()}" itemValue="12" />

                                        <p:ajax listener="#{dashboardCofreGeral.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.Ano}</label>
                                    <p:selectOneMenu id="slcAno" styleClass="seletor" value="#{dashboardCofreGeral.ano}">
                                        <f:selectItem itemLabel="2017" itemValue="2017" />
                                        <f:selectItem itemLabel="2018" itemValue="2018" />
                                        <f:selectItem itemLabel="2019" itemValue="2019" />
                                        <f:selectItem itemLabel="2020" itemValue="2020" />
                                        <f:selectItem itemLabel="2021" itemValue="2021" />
                                        
                                        <p:ajax listener="#{dashboardCofreGeral.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </div>
            </header>
            <h:form id="main">
                <p:panel class="panelPrincipal">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 28px; margin-bottom: 10px; padding:0px 0px 10px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important">
                            <label class="Titulo">#{localemsgs.InformacoesGerais}<br />
                                <span>#{localemsgs.MovimentacoesDiaHistoricoCompleto}</span></label>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo">'
                                    <i class="fa fa-exchange" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Movimentacoes}<br/>
                                    <span><br/></span>
                                </label>
                                <div class="col-md-12 col-sm-12 col-xs-12 FundoChart">
                                    <canvas id="graficoDepositosColetas" class="graficoPequeno" height="150"></canvas>
                                </div>
                            </div>
                        </div>
                        <ui:fragment  rendered="#{login.pp.empresa.contains('GETLOCK')}">
                            <div class="col-md-2 col-sm-2 col-xs-6 BoxMedia">
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <label ref="Titulo">
                                        <i class="fa fa-android" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.CofresAtualizados}<br/>
                                        <span>#{localemsgs.VersaoAtual}: #{dashboardCofreGeral.versaoAtual}</span>
                                    </label>
                                    <div class="col-md-12 col-sm-12 col-xs-12 FundoChart">
                                        <canvas id="graficoCofresDesatualizados" class="graficoPequeno" height="150"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12 BoxMedia">
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <label ref="Titulo">
                                        <i class="icon-wifi" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.StatusComunicacao}<br/>
                                        <span><br/></span>
                                    </label>
                                    <div class="col-md-12 col-sm-12 col-xs-12 FundoChart">
                                        <canvas id="graficoComunicacaoCofres" class="graficoPequeno" height="150"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-12 BoxMedia">
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <label ref="Titulo"><i class="fa fa-battery-full" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.StatusBateria}<br/>
                                        <span><br/></span>
                                    </label>
                                    <div class="col-md-12 col-sm-12 col-xs-12 FundoChart">
                                        <canvas id="graficoBateriaCofres" class="graficoPequeno" height="150"></canvas>
                                    </div>
                                </div>
                            </div>
                        </ui:fragment>

                    </div>

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasEsquerda">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table style="max-height: 500px !important">
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.SaldoCofres}<br/>
                                        <span><br/></span>
                                    </td>
                                </tr>
                            </table>
                            <div class="col-md-12 col-sm-12 col-xs-12 Tabelas">
                                <h:outputText value="#{dashboardCofreGeral.tabelaSaldos}" escape="false"/>
                            </div>
                        </div>
                    </div>

                    <ui:fragment  rendered="#{login.pp.empresa.contains('GETLOCK')}">
                        <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasDireita">
                            <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                                <table style="max-height: 500px !important">
                                    <tr>
                                        <td class="Titulo">
                                            #{localemsgs.StatusCofres}<br />
                                            <span>#{localemsgs.InfoCofres}</span>
                                        </td>
                                    </tr>
                                </table>
                                <div class="col-md-12 col-sm-12 col-xs-12 Tabelas">
                                    <h:outputText value="#{dashboardCofreGeral.tabelaStatus}" escape="false"/>
                                </div>
                            </div>
                        </div>
                    </ui:fragment>

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasEsquerda">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table style="max-height: 500px !important">
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.MovimentacoesRecentesCofre}<br />
                                        <span>#{localemsgs.UltimasMovimentacoes}</span>
                                    </td>
                                </tr>
                            </table>
                            <div class="col-md-12 col-sm-12 col-xs-12 Tabelas">
                                <h:outputText value="#{dashboardCofreGeral.tabelaMovimentacoesCofres}" escape="false"/>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasEsquerda">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table style="max-height: 500px !important">
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.MovimentacoesRecentesHoje}<br />
                                        <span>#{localemsgs.UltimasMovimentacoesHoje}</span>
                                    </td>
                                </tr>
                            </table>
                            <div class="col-md-12 col-sm-12 col-xs-12 Tabelas">
                                <h:outputText value="#{dashboardCofreGeral.tabelaMovimentacoes}" escape="false"/>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseMovimentacoes}<br />
                                        <span>#{localemsgs.TotalMovimentacaoHora}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoMovimentacaoHora" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </p:panel>
                <script type="text/javascript">
                    // <![CDATA[
                    $(document).ready(function () {
                        CarregarGraficos();
                        AjustarCabecalhos();

                    }).on('keyup', '[grid-style="dark"] thead tr th input', function () {
                        $obj = $(this).parents('table');
                        var input = $obj.find('input').val().toLowerCase();
                        $obj.find('tbody tr').filter(function () {
                            $(this).toggle($(this).text().toLowerCase().indexOf(input) > -1);
                        });
                    });

                    function AjustarCabecalhos() {
                        $('[grid-style="dark"] tbody tr:nth-child(1) td').each(function () {
                            $(this).parent('tr').parent('tbody').parent('table').find('thead tr:first-child th:eq(' + $(this).index() + ')').width($(this).width() + 'px');
                            var widthTh = $(this).parent('tr').parent('tbody').parent('table').find('thead tr:first-child th:eq(' + $(this).index() + ')').width();
                            $(this).css('min-width', widthTh + 'px').css('width', widthTh + 'px').css('max-width', widthTh + 'px');
                        });

                        $('[grid-style="dark"] thead tr:nth-child(1) th:last-child').each(function () {
                            var widthTh = $(this).width() + 25;
                            $(this).css('min-width', widthTh + 'px').css('width', widthTh + 'px').css('max-width', widthTh + 'px');
                        });
                    }

                    function CarregarGraficos() {
                        // Horas, Depositos e Coletas
                        var configMovimentacoesPorHora = {
                            scaleGridLineColor: "#303030",
                            responsive: false
                        };

                        var dadosMovimentacoesPorHora = {
                            labels: #{dashboardCofreGeral.labelEstatisticaPorHora},
                            datasets: [
                                {
                                    label: "#{localemsgs.Depositos}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#4cff00",
                                    lineTension: 0.1,
                                    pointBorderColor: "#4cff00",
                                    pointBackgroundColor: "#4cff00",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboardCofreGeral.dadosDepositosPorHora},
                                    spanGaps: false
                                },
                                {
                                    label: "#{localemsgs.Coletas}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#F00000",
                                    lineTension: 0.1,
                                    pointBorderColor: "#F00000",
                                    pointBackgroundColor: "#F00000",
                                    pointStrokeColor: "transparent",
                                    data: #{dashboardCofreGeral.dadosColetasPorHora},
                                    spanGaps: false
                                }
                            ]
                        };

                        Chart.defaults.global.hover.mode = 'nearest';
                        Chart.defaults.global.scaleLineColor = "#AAAAAA";
                        Chart.defaults.global.legend.display = true;

                        var movimentacaoPorHora = $('#graficoMovimentacaoHora')[0].getContext("2d");

                        try {
                            var graficoMovimentacaoPorHora = new Chart(movimentacaoPorHora, {
                                type: "line",
                                data: dadosMovimentacoesPorHora,
                                options: configMovimentacoesPorHora
                            });

                        } catch (e) {
                            alert(e);
                        }

                        var configStatusCofres = {
                            animateRotate: true,
                            animateScale: false,
                            responsive: false,
                            maintainAspectRatio: true,
                            percentageInnerCutout: 90,
                            segmentStrokeColor: 'transparent',
                            legend: {
                                position: 'left'
                            },
                            plugins: {
                                datalabels: {
                                    display: false
                                }
                            }
                        };

                        var dadosGraficoDepositosColetas = {
                            labels: ['#{localemsgs.Movimentacoes}'],
                            datasets: [
                                {
                                    label: "#{localemsgs.Depositos}",
                                    backgroundColor: "#4cff00",
                                    borderWidth: 0.7,
                                    borderColor: "#4cff00",
                                    lineTension: 0.1,
                                    pointBorderColor: "#4cff00",
                                    pointBackgroundColor: "#4cff00",
                                    pointStrokeColor: "transparent",
                                    data: [#{dashboardCofreGeral.totalDepositos}],
                                    spanGaps: false
                                },
                                {
                                    label: "#{localemsgs.Coletas}",
                                    backgroundColor: "#F00000",
                                    borderWidth: 0.7,
                                    borderColor: "#F00000",
                                    lineTension: 0.1,
                                    pointBorderColor: "#F00000",
                                    pointBackgroundColor: "#F00000",
                                    pointStrokeColor: "transparent",
                                    data: [#{dashboardCofreGeral.totalColetas}],
                                    spanGaps: false
                                }
                            ]
                        };

                        Chart.defaults.global.legend.display = true;

                        try {
                            var depositosColetas = document.getElementById("graficoDepositosColetas").getContext("2d");
                            var graficoDepositosColetas = new Chart(depositosColetas, {
                                type: 'bar',
                                data: dadosGraficoDepositosColetas,
                                options: {
                                    animateRotate: true,
                                    animateScale: false,
                                    responsive: false,
                                    maintainAspectRatio: true,
                                    percentageInnerCutout: 90,
                                    segmentStrokeColor: 'transparent',
                                    legend: {
                                        position: 'bottom'
                                    },
                                    plugins: {
                                        datalabels: {
                                            display: false
                                        }
                                    },
                                }
                            });
                        } catch (e) {
                        }

                        var dadosCofresDesatualizados = {
                            labels: ['#{localemsgs.Atualizados}', '#{localemsgs.Desatualizados}'],
                            datasets: [
                                {
                                    data: #{dashboardCofreGeral.dadosCofresDesatualizados},
                                    backgroundColor: ['#008000', '#ff0000']
                                }]
                        };

                        Chart.defaults.global.legend.display = false;


                        try {
                            var cofresDesatualizados = document.getElementById("graficoCofresDesatualizados").getContext("2d");
                            var graficoCofresDesatualizados = new Chart(cofresDesatualizados, {
                                type: 'doughnut',
                                data: dadosCofresDesatualizados,
                                options: configStatusCofres
                            });
                        } catch (e) {
                        }

                        var dadosComunicacaoCofres = {
                            labels: ['#{localemsgs.Online}', '#{localemsgs.SemComunicacaoRecente}', '#{localemsgs.Offline}'],
                            datasets: [
                                {
                                    data: #{dashboardCofreGeral.dadosCofresOffline},
                                    backgroundColor: ['#008000', '#ffff00', '#ff0000']
                                }]
                        };

                        Chart.defaults.global.legend.display = true;


                        try {
                            var comunicacaoCofres = document.getElementById("graficoComunicacaoCofres").getContext("2d");
                            var graficoComunicacaoCofres = new Chart(comunicacaoCofres, {
                                type: 'doughnut',
                                data: dadosComunicacaoCofres,
                                options: configStatusCofres
                            });
                        } catch (e) {
                        }


                        var dadosBateriaCofres = {
                            labels: ['100%', '50% - 99%', '0% - 49%'],
                            datasets: [
                                {
                                    data: #{dashboardCofreGeral.dadosCofreBateria},
                                    backgroundColor: ['#008000', '#ffff00', '#ff0000'],
                                    icons: ['\uf240', '\uf241', '\uf243']
                                }]
                        };

                        Chart.defaults.global.legend.display = true;



                        try {
                            var bateriaCofres = document.getElementById("graficoBateriaCofres").getContext("2d");
                            var graficoBateriaCofres = new Chart(bateriaCofres, {
                                type: 'doughnut',
                                data: dadosBateriaCofres,
                                options: {

                                    layout: {
                                        padding: {
                                            top: 18,
                                            bottom: 19
                                        }
                                    },
                                    animateRotate: true,
                                    animateScale: false,
                                    responsive: false,
                                    maintainAspectRatio: true,
                                    percentageInnerCutout: 90,
                                    segmentStrokeColor: 'transparent',
                                    legend: {
                                        position: 'left',
                                    },
                                    plugins: {
                                        datalabels: {
                                            color: '#ffffff',
                                            align: 'end',
                                            anchor: 'end',
                                            offset: 3,
                                            font: {
                                                family: 'FontAwesome',
                                                size: 13
                                            },
                                            formatter: function (value, context) {
                                                return value > 0 ? context.dataset.icons[context.dataIndex] : '';
                                            }
                                        }
                                    }
                                }});
                        } catch (e) {
                        }

                        $(window).resize(function () {
                            AjustarCabecalhos();
                        });
                    }
                    // ]]>
                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading_circular.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>

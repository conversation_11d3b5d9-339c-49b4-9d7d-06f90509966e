/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RastrearEW;

/**
 *
 * <AUTHOR>
 */
public class RastrearEWDao {

    /**
     * Insere uma nova entrada na tabela RastrearEW com Sequencia automática.
     *
     * @param rastrearEW
     * @param persistencia
     * @throws Exception
     */
    public void inserirRastrear(RastrearEW rastrearEW, Persistencia persistencia) throws Exception {
        try {
            String sql = " Declare @lat Varchar(15)\n"
                    + "Declare @long Varchar(15)\n"
                    + "\n"
                    + "Select top 1  \n"
                    + "@lat = Latitude,\n"
                    + "@long = Longitude\n"
                    + "from RastrearEW (Nolock) where codpessoa = ? Order by Sequencia desc\n"
                    + "\n"
                    + "INSERT INTO RastrearEW (Sequencia, CodPessoa, CodContato, Data, Hora, Latitude,  Longitude, Acuracia, DtTransf, HrTransf, Origem, Distancia) \n"
                    + " VALUES (\n"
                    + "(SELECT ISNULL(MAX(Sequencia),0) + 1 Sequencia FROM RastrearEW (NoLock)),\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?,\n"
                    + "(dbo.fun_CalcDistancia(@lat,@long,?,?))) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rastrearEW.getCodPessoa());
            consulta.setString(rastrearEW.getCodPessoa());
            consulta.setString(rastrearEW.getCodContato());
            consulta.setString(rastrearEW.getData());
            consulta.setString(rastrearEW.getHora());
            consulta.setString(rastrearEW.getLatitude());
            consulta.setString(rastrearEW.getLongitude());
            consulta.setString(rastrearEW.getAcuracia());
            consulta.setString(rastrearEW.getDtTransf());
            consulta.setString(rastrearEW.getHrTransf());
            consulta.setString(rastrearEW.getOrigem());
            consulta.setString(rastrearEW.getLatitude());
            consulta.setString(rastrearEW.getLongitude());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RastrearEWDao.inserirRastrear - " + e.getMessage() + "\n\r"
                    + " INSERT INTO RastrearEW (Sequencia, CodPessoa, CodContato, Data, Hora, Latitude, "
                    + " Longitude, Acuracia, DtTransf, HrTransf, Origem, Distancia) "
                    + " VALUES ((SELECT ISNULL(MAX(Sequencia),0) + 1 Sequencia FROM RastrearEW), " + rastrearEW.getCodPessoa() + ", "
                    + rastrearEW.getCodContato() + ", " + rastrearEW.getData() + ", " + rastrearEW.getHora() + ", " + rastrearEW.getLatitude() + ", "
                    + rastrearEW.getLongitude() + ", " + rastrearEW.getAcuracia() + ", " + rastrearEW.getDtTransf()
                    + rastrearEW.getHrTransf() + ", " + rastrearEW.getOrigem()
                    + " (dbo.fun_CalcDistancia( "
                    + "     (select top 1 z.latitude from rastrearEW z where z.codpessoa = " + rastrearEW.getCodPessoa() + " order by z.sequencia desc), "
                    + "     (select top 1 z.longitude from rastrearEW z where z.codpessoa = " + rastrearEW.getCodPessoa() + " order by z.sequencia desc), "
                    + "      " + rastrearEW.getLatitude() + ", " + rastrearEW.getLongitude() + "))) ");
        }
    }
}

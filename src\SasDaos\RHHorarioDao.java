package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.RHHorario;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RHHorarioDao {

    /**
     * Verifica se tem o ponto
     *
     * @param matr Matrícula do funcionario
     * @param codFil Código da filial
     * @param persistencia Conexão com a base de dados
     * @return se tem ponto eletronico
     * @throws Exception
     */
    public boolean isPontoEletronico(BigDecimal matr, BigDecimal codFil, Persistencia persistencia) throws Exception {
        boolean ponto = false;
        try {
            String sql = "SELECT RHHorario.PontoElet pontoeletronico "
                    + "FROM funcion "
                    + "JOIN RHHorario "
                    + "ON funcion.Horario = RHHorario.Codigo "
                    + "WHERE funcion.CodFil = ? AND funcion.Matr = ? AND RHHorario.CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setBigDecimal(matr);
            consulta.setBigDecimal(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                if (consulta.getInt("pontoeletronico") > 0) {
                    ponto = true;
                }
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("Ocorreu um erro: " + ex.getMessage());
        }
        return ponto;
    }

    /**
     * Lista todos os RHHorarios de uma filial.
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHHorario> listaRHHorarios(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<RHHorario> retorno = new ArrayList<>();
            String sql = "SELECT * "
                    + " FROM RHHorario "
                    + " WHERE CodFil = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            RHHorario rhHorario;
            while (consulta.Proximo()) {
                rhHorario = new RHHorario();
                rhHorario.setCodFil(consulta.getBigDecimal("CodFil"));
                rhHorario.setCodigo(consulta.getBigDecimal("Codigo"));
                rhHorario.setDescricao(consulta.getString("Descricao"));
                rhHorario.setPontoElet(consulta.getBigDecimal("PontoElet"));
                rhHorario.setHorarioFixo(consulta.getBigDecimal("HorarioFixo"));
                rhHorario.setTipoComp(consulta.getString("TipoComp"));
                rhHorario.setCHComp(consulta.getBigDecimal("CHComp"));
                rhHorario.setHsMaxDiaSem(consulta.getBigDecimal("HsMaxDiaSem"));
                rhHorario.setHsMaxSabDom(consulta.getBigDecimal("HsMaxSabDom"));
                rhHorario.setHsMaxDiaSem2(consulta.getBigDecimal("HsMaxDiaSem2"));
                rhHorario.setHsMaxSabDom2(consulta.getBigDecimal("HsMaxSabDom2"));
                rhHorario.setJornadaMax(consulta.getBigDecimal("JornadaMax"));
                rhHorario.setIntervMin(consulta.getBigDecimal("IntervMin"));
                rhHorario.setD1(consulta.getBigDecimal("D1"));
                rhHorario.setHora101(consulta.getString("Hora101"));
                rhHorario.setHora102(consulta.getString("Hora102"));
                rhHorario.setHora103(consulta.getString("Hora103"));
                rhHorario.setHora104(consulta.getString("Hora104"));
                rhHorario.setD2(consulta.getBigDecimal("D2"));
                rhHorario.setHora201(consulta.getString("Hora201"));
                rhHorario.setHora202(consulta.getString("Hora202"));
                rhHorario.setHora203(consulta.getString("Hora203"));
                rhHorario.setHora204(consulta.getString("Hora204"));
                rhHorario.setD3(consulta.getBigDecimal("D3"));
                rhHorario.setHora301(consulta.getString("Hora301"));
                rhHorario.setHora302(consulta.getString("Hora302"));
                rhHorario.setHora303(consulta.getString("Hora303"));
                rhHorario.setHora304(consulta.getString("Hora304"));
                rhHorario.setD4(consulta.getBigDecimal("D4"));
                rhHorario.setHora401(consulta.getString("Hora401"));
                rhHorario.setHora402(consulta.getString("Hora402"));
                rhHorario.setHora403(consulta.getString("Hora403"));
                rhHorario.setHora404(consulta.getString("Hora404"));
                rhHorario.setD5(consulta.getBigDecimal("D5"));
                rhHorario.setHora501(consulta.getString("Hora501"));
                rhHorario.setHora502(consulta.getString("Hora502"));
                rhHorario.setHora503(consulta.getString("Hora503"));
                rhHorario.setHora504(consulta.getString("Hora504"));
                rhHorario.setD6(consulta.getBigDecimal("D6"));
                rhHorario.setHora601(consulta.getString("Hora601"));
                rhHorario.setHora602(consulta.getString("Hora602"));
                rhHorario.setHora603(consulta.getString("Hora603"));
                rhHorario.setHora604(consulta.getString("Hora604"));
                rhHorario.setD7(consulta.getBigDecimal("D7"));
                rhHorario.setHora701(consulta.getString("Hora701"));
                rhHorario.setHora702(consulta.getString("Hora702"));
                rhHorario.setHora703(consulta.getString("Hora703"));
                rhHorario.setHora704(consulta.getString("Hora704"));
                rhHorario.setD8(consulta.getBigDecimal("D8"));
                rhHorario.setHora801(consulta.getString("Hora801"));
                rhHorario.setHora802(consulta.getString("Hora802"));
                rhHorario.setHora803(consulta.getString("Hora803"));
                rhHorario.setHora804(consulta.getString("Hora804"));
                rhHorario.setHrDUDiu(consulta.getBigDecimal("HrDUDiu"));
                rhHorario.setHrDUNotDiu(consulta.getBigDecimal("HrDUNotDiu"));
                rhHorario.setHrDUNot(consulta.getBigDecimal("HrDUNot"));
                rhHorario.setHrSabDiu(consulta.getBigDecimal("HrSabDiu"));
                rhHorario.setHrSabNotDiu(consulta.getBigDecimal("HrSabNotDiu"));
                rhHorario.setHrSabNot(consulta.getBigDecimal("HrSabNot"));
                rhHorario.setHrDomDiu(consulta.getBigDecimal("HrDomDiu"));
                rhHorario.setHrDomNotDiu(consulta.getBigDecimal("HrDomNotDiu"));
                rhHorario.setHrDomNot(consulta.getBigDecimal("HrDomNot"));
                rhHorario.setHECC(consulta.getBigDecimal("HECC"));
                rhHorario.setOperador(consulta.getString("Operador"));
                rhHorario.setDt_Alter(consulta.getString("Dt_Alter"));
                rhHorario.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(rhHorario);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("RHHorarioDao.listaRHHorarios - " + ex.getMessage() + "\r\n"
                    + "SELECT * "
                    + " FROM RHHorario "
                    + " WHERE CodFil = " + codFil);
        }
    }

    /**
     * Busca um horário específico
     *
     * @param codFil
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public RHHorario getRHHorario(BigDecimal codFil, int codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT * "
                    + " FROM RHHorario "
                    + " WHERE CodFil = ? and codigo  = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setInt(codigo);
            consulta.select();
            RHHorario rhHorario = new RHHorario();
            while (consulta.Proximo()) {
                rhHorario.setCodFil(consulta.getBigDecimal("CodFil"));
                rhHorario.setCodigo(consulta.getBigDecimal("Codigo"));
                rhHorario.setDescricao(consulta.getString("Descricao"));
                rhHorario.setPontoElet(consulta.getBigDecimal("PontoElet"));
                rhHorario.setHorarioFixo(consulta.getBigDecimal("HorarioFixo"));
                rhHorario.setTipoComp(consulta.getString("TipoComp"));
                rhHorario.setCHComp(consulta.getBigDecimal("CHComp"));
                rhHorario.setHsMaxDiaSem(consulta.getBigDecimal("HsMaxDiaSem"));
                rhHorario.setHsMaxSabDom(consulta.getBigDecimal("HsMaxSabDom"));
                rhHorario.setHsMaxDiaSem2(consulta.getBigDecimal("HsMaxDiaSem2"));
                rhHorario.setHsMaxSabDom2(consulta.getBigDecimal("HsMaxSabDom2"));
                rhHorario.setJornadaMax(consulta.getBigDecimal("JornadaMax"));
                rhHorario.setIntervMin(consulta.getBigDecimal("IntervMin"));
                rhHorario.setD1(consulta.getBigDecimal("D1"));
                rhHorario.setHora101(consulta.getString("Hora101"));
                rhHorario.setHora102(consulta.getString("Hora102"));
                rhHorario.setHora103(consulta.getString("Hora103"));
                rhHorario.setHora104(consulta.getString("Hora104"));
                rhHorario.setD2(consulta.getBigDecimal("D2"));
                rhHorario.setHora201(consulta.getString("Hora201"));
                rhHorario.setHora202(consulta.getString("Hora202"));
                rhHorario.setHora203(consulta.getString("Hora203"));
                rhHorario.setHora204(consulta.getString("Hora204"));
                rhHorario.setD3(consulta.getBigDecimal("D3"));
                rhHorario.setHora301(consulta.getString("Hora301"));
                rhHorario.setHora302(consulta.getString("Hora302"));
                rhHorario.setHora303(consulta.getString("Hora303"));
                rhHorario.setHora304(consulta.getString("Hora304"));
                rhHorario.setD4(consulta.getBigDecimal("D4"));
                rhHorario.setHora401(consulta.getString("Hora401"));
                rhHorario.setHora402(consulta.getString("Hora402"));
                rhHorario.setHora403(consulta.getString("Hora403"));
                rhHorario.setHora404(consulta.getString("Hora404"));
                rhHorario.setD5(consulta.getBigDecimal("D5"));
                rhHorario.setHora501(consulta.getString("Hora501"));
                rhHorario.setHora502(consulta.getString("Hora502"));
                rhHorario.setHora503(consulta.getString("Hora503"));
                rhHorario.setHora504(consulta.getString("Hora504"));
                rhHorario.setD6(consulta.getBigDecimal("D6"));
                rhHorario.setHora601(consulta.getString("Hora601"));
                rhHorario.setHora602(consulta.getString("Hora602"));
                rhHorario.setHora603(consulta.getString("Hora603"));
                rhHorario.setHora604(consulta.getString("Hora604"));
                rhHorario.setD7(consulta.getBigDecimal("D7"));
                rhHorario.setHora701(consulta.getString("Hora701"));
                rhHorario.setHora702(consulta.getString("Hora702"));
                rhHorario.setHora703(consulta.getString("Hora703"));
                rhHorario.setHora704(consulta.getString("Hora704"));
                rhHorario.setD8(consulta.getBigDecimal("D8"));
                rhHorario.setHora801(consulta.getString("Hora801"));
                rhHorario.setHora802(consulta.getString("Hora802"));
                rhHorario.setHora803(consulta.getString("Hora803"));
                rhHorario.setHora804(consulta.getString("Hora804"));
                rhHorario.setHrDUDiu(consulta.getBigDecimal("HrDUDiu"));
                rhHorario.setHrDUNotDiu(consulta.getBigDecimal("HrDUNotDiu"));
                rhHorario.setHrDUNot(consulta.getBigDecimal("HrDUNot"));
                rhHorario.setHrSabDiu(consulta.getBigDecimal("HrSabDiu"));
                rhHorario.setHrSabNotDiu(consulta.getBigDecimal("HrSabNotDiu"));
                rhHorario.setHrSabNot(consulta.getBigDecimal("HrSabNot"));
                rhHorario.setHrDomDiu(consulta.getBigDecimal("HrDomDiu"));
                rhHorario.setHrDomNotDiu(consulta.getBigDecimal("HrDomNotDiu"));
                rhHorario.setHrDomNot(consulta.getBigDecimal("HrDomNot"));
                rhHorario.setHECC(consulta.getBigDecimal("HECC"));
                rhHorario.setOperador(consulta.getString("Operador"));
                rhHorario.setDt_Alter(consulta.getString("Dt_Alter"));
                rhHorario.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.Close();
            return rhHorario;
        } catch (Exception ex) {
            throw new Exception("RHHorarioDao.listaRHHorarios - " + ex.getMessage() + "\r\n"
                    + "SELECT * "
                    + " FROM RHHorario "
                    + " WHERE CodFil = " + codFil
                    + " and codigo  = " + codigo);
        }
    }
}

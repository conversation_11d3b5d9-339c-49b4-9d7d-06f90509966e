/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.XMLNFe;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class XMLNFeDao {
    
    public List<XMLNFe> BuscaDocumentosEmitir(Persistencia persistencia, String cnpj) throws Exception {
        List<XMLNFe> retorno = new ArrayList();
        try {
            String sql = "select * from xmlnfe (Nolock) where Status in (0,1) and CNPJ = ? ";
            XMLNFe documento;
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(cnpj);
            consult.select();
            while (consult.Proximo()) {
                documento = new XMLNFe();
                documento.setSequencia(consult.getInt("Sequencia"));
                documento.setCNPJ(consult.getString("CNPJ"));
                documento.setPraca(consult.getString("Praca"));
                documento.setSerie(consult.getString("Serie"));
                documento.setNumero(consult.getInt("Numero"));
                documento.setUF(consult.getString("UF"));
                documento.setDt_Nota(consult.getString("Dt_Nota"));
                documento.setHr_Nota(consult.getString("Hr_Nota"));                
                documento.setXML_Envio(consult.getString("Xml_Envio"));
                documento.setChaveNFE(consult.getString("ChaveNFE"));
                documento.setStatus(consult.getInt("Status"));
                documento.setTOKEN(consult.getString("Token"));
                retorno.add(documento);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFeDao.BuscaNotasEmitir - " + e.getMessage() + "\r\n"
                    + "Select * from xmlnfse where Status = 0 and CNPJ = "+cnpj);
        }
    } 
    
 public void gravaRetornoDocumento(String Token, String XML, int Status,
            String Dt_Retorno, String Hr_Retorno, String chaveNFe, String protocolo, Persistencia persistencia) throws Exception {
        String sql = "update XMLNFE set \n"
                + " XML_Retorno = ?, \n"
                + " Dt_Retorno = ?, \n"
                + " Hr_Retorno = ?, \n"
                + " Status = ?, \n "
                + " ChaveNFe = ?, \n "
                + " Protocolo = ? \n "
                + " where Token = ?;";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(XML);
            consulta.setString(Dt_Retorno);
            consulta.setString(Hr_Retorno);
            consulta.setInt(Status);
            
            consulta.setString(chaveNFe);
            consulta.setString(protocolo);
            
            consulta.setString(Token);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("XMLNFeDao.gravaRetornoNota - " + e.getMessage());
        }
    }    
    
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.RegistrosPonto;

import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.BatidaPonto;
import SasBeans.FolhaPonto;
import SasBeans.SasPWFill;
import SasDaos.FolhasPontoDao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.BatidasPontoDao;
import SasDaos.LogPortalDao;
import SasDaos.SasPwFilDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class RegistrosPontoSatMobWeb {

    private final Persistencia persistencia;
    private String orderBy;

    public RegistrosPontoSatMobWeb(Persistencia persistencia) {
        this.persistencia = persistencia;
        this.orderBy = "";
    }

    public RegistrosPontoSatMobWeb(Persistencia persistencia, String recebeOrderBy) {
        this.persistencia = persistencia;
        this.orderBy = recebeOrderBy;
    }

    public List<FolhaPonto> listaFolhasPaginadoSemFiltro(int primeiro, int linhas, Map filtro) throws Exception {
        try {
            if (orderBy.equals("")) {
                FolhasPontoDao folhaDAO = new FolhasPontoDao();
                return folhaDAO.getPaginadoSemFiltro(primeiro, linhas, filtro, persistencia);
            }
            else{
                FolhasPontoDao folhaDAO = new FolhasPontoDao(orderBy);
                return folhaDAO.getPaginadoSemFiltro(primeiro, linhas, filtro, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("registrospontos.falhageral<message>" + e.getMessage());
        }
    }

    public List<BatidaPonto> listaAllBatidas(String Matricula, String sDt_Ini, String sDt_Fim) throws Exception {
        try {
            BatidasPontoDao batidaDAO = new BatidasPontoDao(persistencia);
            return batidaDAO.getAll(Matricula, sDt_Ini, sDt_Fim, persistencia);
        } catch (Exception e) {
            throw new Exception("registrospontos.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscaFilial(String CodFil, BigDecimal CodPessoa) throws Exception {
        try {
            SasPwFilDao saspwFilDAO = new SasPwFilDao();
            return saspwFilDAO.buscaSasPWFillLogin(CodFil, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("NFiscalSatMobWeb.buscaFilial<message>" + e.getMessage());
        }
    }

    public int contagemRegistro(Map filters) throws Exception {
        try {
            FolhasPontoDao folhaDAO = new FolhasPontoDao();
            return folhaDAO.getQuantidade(filters, persistencia);
        } catch (Exception e) {
            throw new Exception("NFiscalSatMobWeb.buscaFilial<message>" + e.getMessage());
        }
    }

    public void geraLogFolhaDePonto(String matricula, String dataInicial,
            String dataFinal, String codFil, Persistencia persistencia) throws Exception {
        try {
            LogPortalDao logportaldao = new LogPortalDao();
            logportaldao.insereLog(matricula, codFil, "Folha de Ponto gerada " + dataInicial + " a " + dataFinal, persistencia);
        } catch (Exception e) {
            throw new Exception("folhadepontos.falhageral<falha salvar log> - " + e.getMessage());
        }
    }
}

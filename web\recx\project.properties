auxiliary.org-netbeans-modules-css-prep.less_2e_compiler_2e_options=
auxiliary.org-netbeans-modules-css-prep.less_2e_enabled=false
auxiliary.org-netbeans-modules-css-prep.less_2e_mappings=/less:/css
auxiliary.org-netbeans-modules-css-prep.sass_2e_compiler_2e_options=
auxiliary.org-netbeans-modules-css-prep.sass_2e_enabled=false
auxiliary.org-netbeans-modules-css-prep.sass_2e_mappings=/scss:/css
auxiliary.org-netbeans-modules-javascript-nodejs.enabled=false
auxiliary.org-netbeans-modules-javascript-nodejs.node_2e_default=true
auxiliary.org-netbeans-modules-javascript-nodejs.run_2e_enabled=false
auxiliary.org-netbeans-modules-javascript-nodejs.sync_2e_enabled=true
auxiliary.org-netbeans-modules-javascript2-requirejs.enabled=true
auxiliary.org-netbeans-modules-web-clientproject-api.js_2e_libs_2e_folder=js/libs
browser.autorefresh.Chrome=true
browser.autorefresh.Chrome.INTEGRATED=true
browser.highlightselection.Chrome=true
browser.highlightselection.Chrome.INTEGRATED=true
browser.run=true
file.reference.NetBeans-SatMobX=.
file.reference.SatMobX-nbproject=nbproject
files.encoding=UTF-8
site.root.folder=${file.reference.SatMobX-nbproject}
source.folder=${file.reference.SatMobX-nbproject}
start.file=index.html
test.folder=${file.reference.SatMobX-nbproject}
test.selenium.folder=${file.reference.SatMobX-nbproject}
web.context.root=/SatMobX/nbproject

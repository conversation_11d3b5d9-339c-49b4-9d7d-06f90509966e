<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body>
            <f:metadata>
                <f:viewAction action="#{clientescontainer.Persistencias(login.pp, login.satellite)}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>

                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>

                <ui:define name="infoFilial">
                    <div class="ui-grid-row">
                        <h:outputText value="#{clientescontainer.filiais.descricao}" class="negrito"/>
                    </div>
                    <div class="ui-grid-row">
                        #{clientescontainer.filiais.endereco}
                    </div>
                    <div class="ui-grid-row">
                        #{clientescontainer.filiais.bairro}<h:outputText value=", " rendered="#{clientescontainer.filiais.bairro ne null and container.filiais.bairro ne ''}"/>#{clientescontainer.filiais.cidade}/#{clientescontainer.filiais.UF}
                    </div>
                </ui:define>

                <ui:define name="seletorData">
                    <h:outputText value="#{localemsgs.Data}: "/>
                    <h:outputText id="dataDia" value="#{login.dataTela}" converter="conversorDia"/>
                </ui:define>

                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="main">

                            <p:panel  id="painelPesquisa">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1">
                                    <p:tab title="#{localemsgs.Pesquisar}">
                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3 pesquisa,ui-grid-col-3 pesquisa,
                                                     ui-grid-col-3 pesquisa,ui-grid-col-3 pesquisa" 
                                                     layout="grid">
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaNome" value="#{localemsgs.Nome} "/>
                                                <p:inputText id="pesquisaNome" value="#{clientescontainer.pesquisaNome}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaNome" value="#{localemsgs.Nome}"/>
                                                </p:inputText> 
                                            </p:panelGrid> 
                                        </p:panelGrid>
                                        <p:commandButton action="#{clientescontainer.pesquisar}" 
                                                         update="msgs main:tabela main:painelPesquisa" 
                                                         styleClass="botao" value="#{localemsgs.Pesquisar}"/>
                                        <p:commandButton action="#{clientescontainer.limparPesquisa}" 
                                                         update="msgs main:tabela main:painelPesquisa" 
                                                         styleClass="botao" value="#{localemsgs.LimparFiltros}"
                                                         style="white-space: nowrap !important"/>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:panel>

                            <p:panel id="cadastrar" styleClass="painelCadastro">
                                <p:dataGrid  id="tabela" value="#{clientescontainer.allClientes}" paginator="true" rows="50" lazy="true"
                                             rowsPerPageTemplate="5,10,15,20,25,50" 
                                             currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Clientes}"
                                             paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} 
                                             {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                             var="cli" columns="1"
                                             emptyMessage="#{localemsgs.SemRegistros}">
                                    <i class='fas fa-id-card'></i>
                                    <p:panel header="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{cli.NRed}" style="color:#000 !important;">  
                                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText class="negrito-normal" value="#{cli.nome}"/>

                                            <p:column>
                                                <h:outputText class="Titulo" value="#{localemsgs.Endereco}: "/>
                                                <h:outputText value="#{cli.ende}, #{cli.bairro} - #{cli.cidade}/#{cli.estado}" class="negrito-normal"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:column>
                                                <h:outputText value="#{localemsgs.CEP}: " />
                                                <h:outputText value="#{cli.CEP}" class="negrito-normal" converter="conversorCEP"/>
                                            </p:column>

                                            <p:column>
                                                <h:outputText value="#{localemsgs.Contatos} : "/>
                                                <h:outputText value="#{cli.email} " class="negrito-normal"/>
                                                <h:outputText value="#{cli.fone1}" converter="conversorFone" class="negrito-normal"/>
                                                <h:outputText value=" " />
                                                <h:outputText value="#{cli.fone2}" converter="conversorFone" class="negrito-normal"/>
                                            </p:column>
                                        </p:panelGrid>
                                        
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Orcamento}: " />
                                                <h:outputText value="#{cli.obs}" class="negrito-normal"/>
                                            </p:column>

                                        </p:panelGrid>                                        

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2, ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank"> 
                                            <p:commandLink title="#{localemsgs.Editar}" update="msgs formCadastrar" 
                                                           actionListener="#{clientescontainer.abrirCliente(cli)}">
                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                            </p:commandLink>

                                            <p:commandLink title="#{localemsgs.Usuarios}" update="msgs formUsuarios" 
                                                           actionListener="#{clientescontainer.abrirUsuarios(cli)}">
                                                <p:graphicImage url="../assets/img/icone_usuarios_redondo.png" height="30"/>
                                            </p:commandLink>
                                        </p:panelGrid>
                                    </p:panel>
                                </p:dataGrid>
                            </p:panel>

                            <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">

                                <div style="padding-bottom: 10px;">
                                    <p:commandLink title="#{localemsgs.Novo}" actionListener="#{clientescontainer.preCadastroCliente()}"
                                                   update="formCadastrar">
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                    </p:commandLink>
                                </div>

                                <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                            </p:panel>
                        </h:form>

                        <h:form id="formUsuarios">
                            <p:dialog widgetVar="dlgListarUsuarios" positionType="absolute" 
                                      draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                      showEffect="drop" hideEffect="drop" closeOnEscape="false"
                                      style=" background-image: url('../assets/img/menu_fundo.png'); background-size: 750px 430px;">
                                <f:facet name="header">
                                    <img src="../assets/img/icone_usuarios.png" height="40" width="40"/> 
                                    <p:spacer width="5px"/>
                                    <h:outputText value="#{localemsgs.Usuarios}" style="color:#022a48" /> 
                                </f:facet>

                                <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar2">
                                    <p:panelGrid columns="2" style="width: 100%">
                                        <p:scrollPanel mode="native" style="width:100%; height:300px">
                                            <p:dataGrid id="tabelaUsuarios" value="#{clientescontainer.usuarios}"
                                                        emptyMessage="#{localemsgs.SemRegistros}" var="lista" 
                                                        styleClass="tabela" style="font-size: 12px"
                                                        columns="1">
                                                <p:panel>
                                                    <f:facet name="header">
                                                        <i class="fas fa-user"></i>
                                                        <h:outputText value="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{lista.pessoa.nome}" style="color:#000 !important"/>
                                                    </f:facet>
                                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-5,ui-grid-col-7" 
                                                                 layout="grid" styleClass="ui-panelgrid-blank">     
                                                        <p:column>
                                                            <h:outputText value="#{localemsgs.CodigoLogin}: "/>
                                                            <h:outputText value="#{lista.pessoa.codigo}" title="#{lista.pessoa.codigo}" 
                                                                          converter="conversorCodFil" class="negrito-normal"/>
                                                        </p:column>
                                                        <p:column>
                                                            <h:outputText value="#{localemsgs.Email}: "/>
                                                            <h:outputText value="#{lista.pessoa.email}" title="#{lista.pessoa.email}" class="negrito-normal"/>
                                                        </p:column>
                                                        <p:column>
                                                            <h:outputText value="#{localemsgs.Nivel}: "/>
                                                            <h:outputText value="#{lista.saspw.nivelOP}" title="#{lista.saspw.nivelOP}" class="negrito-normal"/>
                                                        </p:column>
                                                        <p:column>
                                                            <h:outputText value="#{localemsgs.Grupo}: "/>
                                                            <h:outputText value="#{lista.grupo.descricao}" title="#{lista.grupo.descricao}" class="negrito-normal"/>
                                                        </p:column>
                                                        <p:column>
                                                            <h:outputText value="#{localemsgs.Situacao}: "/>
                                                            <h:outputText value="#{lista.saspw.situacao}" title="#{lista.saspw.situacao}" class="negrito-normal"/>
                                                        </p:column>
                                                        <p:column>
                                                            <h:outputText value="#{localemsgs.Descricao}: "/>
                                                            <h:outputText value="#{lista.saspw.descricao}" title="#{lista.saspw.descricao}" class="negrito-normal"/>
                                                        </p:column>
                                                    </p:panelGrid>

                                                    <p:panelGrid columns="2" columnClasses="ui-g-6 ui-md-3 ui-lg-2,ui-g-6 ui-md-9 ui-lg-10" 
                                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                                        <p:commandLink title="#{localemsgs.Editar}" actionListener="#{clientescontainer.abrirUsuario(lista)}"
                                                                       update="formCadastrarUsuario msgs">
                                                            <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                                        </p:commandLink>

                                                        <p:commandLink title="#{localemsgs.Remover}" actionListener="#{clientescontainer.excluir(lista)}"
                                                                       update="tabelaUsuarios msgs">
                                                            <p:confirm header="#{localemsgs.Atencao}" 
                                                                       message="#{localemsgs.ConfirmaExclusao}"
                                                                       icon="pi pi-exclamation-triangle" />
                                                            <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                                        </p:commandLink>
                                                    </p:panelGrid>
                                                </p:panel>
                                            </p:dataGrid>
                                        </p:scrollPanel>
                                        <p:commandLink title="#{localemsgs.Adicionar}" id="adicionarFilial"
                                                       oncomplete="PF('dlgCadastrarUsuario').show()" actionListener="#{clientescontainer.novoUsuario}"
                                                       update="msgs formCadastrarUsuario">
                                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                    </p:panelGrid>
                                </p:panel>
                            </p:dialog>
                        </h:form>

                        <h:form class="form-inline" id="formCadastrarUsuario">
                            <p:dialog widgetVar="dlgCadastrarUsuario" positionType="absolute" responsive="true"
                                      draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                      showEffect="drop" hideEffect="drop" closeOnEscape="false" 
                                      style="background-image: url('assets/img/menu_fundo.png');
                                      background-size: 750px 430px; background-repeat: no-repeat; ">
                                <script>
                                    $(document).ready(function () {
                                        //first unbind the original click event
                                        PF('dlgCadastrarUsuario').closeIcon.unbind('click');

                                        //register your own
                                        PF('dlgCadastrarUsuario').closeIcon.click(function (e) {
                                            $("#formCadastrarUsuario\\:botaoFechar").click();
                                            //should be always called
                                            e.preventDefault();
                                        });


                                    })
                                </script>
                                <f:facet name="header">
                                    <img src="../assets/img/icone_usuarios.png" height="40" width="40"/> 
                                    #{localemsgs.CadastrarUsuario}
                                </f:facet>
                                <p:panel id="editar" styleClass="painelCadastro">
                                    <p:commandButton widgetVar="botaoFechar" style="display: none"
                                                     oncomplete="PF('dlgCadastrarUsuario').hide()" id="botaoFechar">
                                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                                    </p:commandButton>
                                    <p:confirmDialog global="true">
                                        <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                        <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                    </p:confirmDialog>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">

                                        <p:outputLabel for="pessoa" value="#{localemsgs.Nome}:" rendered="#{!clientescontainer.cadastroNovaPessoa and clientescontainer.flagUsuario eq 1}"/>
                                        <p:autoComplete id="pessoa" value="#{clientescontainer.usuario.pessoa}" completeMethod="#{clientescontainer.listarQueryValida}"
                                                        required="true" label="#{localemsgs.Pessoa}"
                                                        rendered="#{!clientescontainer.cadastroNovaPessoa and clientescontainer.flagUsuario eq 1}"
                                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Pessoa}"  scrollHeight="200"
                                                        inputStyle="width: 100%" placeholder="#{localemsgs.Nome}" forceSelection="true"
                                                        var="ppl" itemLabel="#{ppl.nome}" itemValue="#{ppl}">
                                            <p:column>
                                                <i class="fas fa-plus" style="#{ppl.codigo eq '-1' ? 'display: inline; font-size: 10px;' : 'display: none'}"></i>
                                                <h:outputText value=" #{ppl.nome}" style="#{ppl.codigo eq '-1' ? 'font-weight: bold' : ''}"/>
                                            </p:column>
                                            <o:converter converterId="omnifaces.ListIndexConverter" list="#{clientescontainer.listaPessoa}" />
                                            <p:ajax event="itemSelect" listener="#{clientescontainer.selecionarPessoa}" update="msgs formCadastrarUsuario:editar"/>
                                        </p:autoComplete>

                                        <p:outputLabel for="nome" value="#{localemsgs.Nome}:" rendered="#{clientescontainer.cadastroNovaPessoa or clientescontainer.flagUsuario eq 2}"/>
                                        <p:inputText value="#{clientescontainer.usuario.pessoa.nome}" rendered="#{clientescontainer.cadastroNovaPessoa or clientescontainer.flagUsuario eq 2}" id="nome" style="width: 100%">
                                            <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                        </p:inputText>

                                        <p:outputLabel for="email" value="#{localemsgs.Email}:"/>
                                        <p:inputText value="#{clientescontainer.usuario.pessoa.email}" id="email" style="width: 100%">
                                            <p:watermark for="email" value="#{localemsgs.Email}"/>
                                        </p:inputText>

                                        <p:outputLabel for="cpf" value="#{localemsgs.CPF}:"/>
                                        <p:inputMask id="cpf"  value="#{clientescontainer.usuario.pessoa.CPF}" disabled="#{!clientescontainer.cadastroNovaPessoa}"
                                                     mask="#{mascaras.mascaraCPF}">
                                            <p:watermark for="cpf" value="#{localemsgs.CPF}"/>
                                        </p:inputMask>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">    

                                        <p:outputLabel for="rg" value="#{localemsgs.RG}:"/>
                                        <p:inputText value="#{clientescontainer.usuario.pessoa.RG}" id="rg" style="width: 100%">
                                            <p:watermark for="rg" value="#{localemsgs.RG}"/>
                                        </p:inputText>

                                        <p:outputLabel for="RGOrg" value="#{localemsgs.RGOrg}:"/>
                                        <p:inputText value="#{clientescontainer.usuario.pessoa.RGOrgEmis}" id="RGOrg" style="width: 100%">
                                            <p:watermark for="RGOrg" value="#{localemsgs.RGOrg}"/>
                                        </p:inputText>

                                        <p:outputLabel for="situacaoPessoa" value="#{localemsgs.Tipo}:"/>
                                        <p:selectOneMenu value="#{clientescontainer.usuario.pessoa.situacao}" style="width: 100%" 
                                                         id="situacaoPessoa" >
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Candidato}" itemValue="C"/>
                                            <f:selectItem itemLabel="#{localemsgs.Prestador}" itemValue="P"/>
                                            <f:selectItem itemLabel="#{localemsgs.Autonomo}" itemValue="A"/>
                                            <f:selectItem itemLabel="#{localemsgs.Funcionario}" itemValue="F" itemDisabled="true"/>
                                            <f:selectItem itemLabel="#{localemsgs.Diretor}" itemValue="D"/>
                                            <f:selectItem itemLabel="#{localemsgs.Socio}" itemValue="S"/>
                                            <f:selectItem itemLabel="#{localemsgs.Visitante}" itemValue="V"/>
                                            <f:selectItem itemLabel="#{localemsgs.BBloqueado}" itemValue="B"/>
                                            <f:selectItem itemLabel="#{localemsgs.Visitanteweb}" itemValue="W" />
                                            <f:selectItem itemLabel="#{localemsgs.OOutros}" itemValue="O"/>
                                        </p:selectOneMenu>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">    
                                        <p:outputLabel for="nivel" value="#{localemsgs.Nivel}:"/>
                                        <p:selectOneMenu value="#{clientescontainer.usuario.saspw.nivelx}" id="nivel"
                                                         required="true" label="#{localemsgs.Nivel}" style="width: 100%"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nivel}">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                            <f:selectItems value="#{clientescontainer.niveis}" />
                                        </p:selectOneMenu>

                                        <p:outputLabel for="grupo" value="#{localemsgs.Grupo}:"/>
                                        <p:selectOneMenu value="#{clientescontainer.usuario.grupo.codigo}" id="grupo" style="width: 100%"
                                                         required="true" label="#{localemsgs.Grupo}" filter="true" filterMatchMode="contains"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Grupo}">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItems value="#{clientescontainer.grupos}" var="grupo" itemValue="#{grupo.codigo}"
                                                           itemLabel="#{grupo.descricao}" noSelectionValue="Selecione"/>
                                        </p:selectOneMenu>

                                        <p:outputLabel for="situacao" value="#{localemsgs.Situacao}:" />
                                        <p:selectOneMenu value="#{clientescontainer.usuario.saspw.situacao}" id="situacao"
                                                         required="true" label="#{localemsgs.Situacao}" style="width: 100%"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Situacao}">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                            <f:selectItem itemLabel="#{localemsgs.Bloqueado}" itemValue="B"/>
                                        </p:selectOneMenu>

                                        <p:outputLabel for="descricao" value="#{localemsgs.Descricao}:"/>
                                        <p:inputText id="descricao" value="#{clientescontainer.usuario.saspw.descricao}"
                                                     label="#{localemsgs.Descricao} " style="width: 100%">
                                            <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                        </p:inputText>

                                        <p:outputLabel for="senha" value="#{localemsgs.Senha}:"/>
                                        <p:password id="senha" value="#{clientescontainer.usuario.pessoa.PWWeb}" required="true" transient="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}" autocomplete="off"
                                                    label="#{localemsgs.Senha}" feedback="true" redisplay="true" match="confirmacao"
                                                    promptLabel="#{localemsgs.DigiteSenha}" weakLabel="#{localemsgs.SenhaFraca}"
                                                    goodLabel="#{localemsgs.SenhaBoa}" strongLabel="#{localemsgs.SenhaForte}"
                                                    style="width: 100%">
                                            <f:validateRegex pattern="^[0-9]{5,20}$" for="senha"/>
                                            <p:watermark for="senha" value="#{localemsgs.Senha}"/>
                                        </p:password>

                                        <p:outputLabel for="confirmacao" value="#{localemsgs.Confirmacao}:" />
                                        <p:password id="confirmacao" value="#{clientescontainer.usuario.pessoa.PWWeb}" redisplay="true"
                                                    label="#{localemsgs.Senha}" style="width: 100%">
                                            <p:watermark for="confirmacao" value="#{localemsgs.Senha}"/>
                                        </p:password>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="motivo" value="#{localemsgs.Motivo}:"/>
                                        <p:inputText id="motivo" value="#{clientescontainer.usuario.saspw.motivo}"
                                                     label="#{localemsgs.Motivo}" style="width: 100%">
                                            <p:watermark for="motivo" value="#{localemsgs.Motivo}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:commandButton action="#{clientescontainer.cadastrar}" update="msgs formUsuarios:tabelaUsuarios" styleClass="botao"
                                                     value="#{localemsgs.Cadastrar}" rendered="#{clientescontainer.flagUsuario eq 1}">
                                    </p:commandButton> 

                                    <p:commandButton action="#{clientescontainer.editar}" update="msgs formUsuarios:tabelaUsuarios" styleClass="botao"
                                                     value="#{localemsgs.Editar}" rendered="#{clientescontainer.flagUsuario eq 2}">
                                    </p:commandButton>  
                                </p:panel>
                            </p:dialog>
                        </h:form>
                    </div>

                    <!-- Cadastrar novo cliente -->
                    <h:form id="formCadastrar" >
                        <p:dialog styleClass="box-primary" widgetVar="dlgCadastrar" minHeight="500" modal="true" 
                                  onShow="PF('dlgCadastrar').initPosition()"
                                  id="dlgCadastrar" responsive="true" dynamic="true" style="background-size: 750px 430px;">
                            <script>
                                $(document).ready(function () {
                                    //first unbind the original click event
                                    PF('dlgCadastrar').closeIcon.unbind('click');

                                    //register your own
                                    PF('dlgCadastrar').closeIcon.click(function (e) {
                                        $("#formCadastrar\\:botaoFechar").click();
                                        //should be always called
                                        e.preventDefault();
                                    });
                                })
                            </script>
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <f:facet name="header">
                                <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Clientes}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="cadastrar">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>
                                
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="tpCli" value="#{localemsgs.TpCli}: "  />
                                    <p:selectOneMenu id="tpCli" value="#{clientescontainer.novo.tpCli}"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.EstabelecimentoComercialResidencia}" itemValue="0"/>
                                        <f:selectItem itemLabel="#{localemsgs.Cofre}" itemValue="4"/>
                                        <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="7"/>
                                        <f:selectItem itemLabel="#{localemsgs.TransportadoraValores}" itemValue="8"/>
                                        <f:selectItem itemLabel="#{localemsgs.ATM}" itemValue="9"/>
                                    </p:selectOneMenu>                         

                                    <p:outputLabel for="nred" value="#{localemsgs.NRed}: " />
                                    <p:inputText id="nred" value="#{clientescontainer.novo.NRed}"
                                                 required="true" label="#{localemsgs.NRed}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NRed}"
                                                 maxlength="20">
                                        <p:watermark for="nred" value="#{localemsgs.NRed}"/>
                                    </p:inputText>
                                    
                                    <p:outputLabel for="clifat" value="#{localemsgs.CliFat}: "/>
                                    <p:selectOneMenu id="clifat" value="#{clientescontainer.cliFat}" 
                                                     converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial" style="width: 100%;" var="cf"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                        <p:column>
                                            <h:outputText value="#{cf.NRed}" class="negrito-normal"/>
                                        </p:column>
                                        <p:column>
                                            <h:outputText value="#{cf.ende}, #{cf.bairro} - #{cf.cidade}/#{cf.estado}" />
                                        </p:column>
                                        <f:selectItems value="#{clientescontainer.cliFats}" var="clifat" itemValue="#{clifat}"
                                                       itemLabel="#{clifat.NRed}"/>
                                    </p:selectOneMenu>
                                </p:panelGrid>
                                <p:tabView id="tabs" activeIndex="0" dynamic="true" orientation="left"
                                           onTabShow="PF('dlgCadastrar').initPosition()">
                                    <p:tab id="tabDados" title="#{localemsgs.Identificacao}">

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">                                        
                                            <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                                            <p:inputText id="nome" value="#{clientescontainer.novo.nome}"
                                                         label="#{localemsgs.Nome}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                            </p:inputText>  

                                            <p:outputLabel for="regiao" value="#{localemsgs.Regiao}:"/>
                                            <p:selectOneMenu id="regiao" value="#{clientescontainer.regiao}" converter="omnifaces.SelectItemsConverter"
                                                             styleClass="filial" style="width: 100%;"
                                                             filter="true" filterMatchMode="contains">
                                                <f:selectItems value="#{clientescontainer.regioes}" var="regiao" itemValue="#{regiao}"
                                                               itemLabel="#{regiao.regiao} - #{regiao.descricao}" noSelectionValue=""/>
                                            </p:selectOneMenu>
                                        </p:panelGrid>


                                        <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank"> 
                                            <p:outputLabel for="cep" value="#{localemsgs.CEP}: "/>                   
                                            <p:inputText id="cep" value="#{clientescontainer.novo.CEP}"
                                                         maxlength="8" style="width: 100%">
                                                <p:watermark for="cep" value="#{localemsgs.CEP}"/>
                                            </p:inputText>

                                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                                           partialSubmit="true" process="@this formCadastrar:tabs:cep" id="cep_pesquisa"
                                                           update="formCadastrar:tabs:cep formCadastrar:tabs:ende formCadastrar:tabs:bairro formCadastrar:tabs:cidade formCadastrar:tabs:estado msgs"
                                                           actionListener="#{clientescontainer.buscarEndereco}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                                <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                                          draggable="false" closable="true" width="300">
                                                    <div class="form-inline">
                                                        <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                                        <p:spacer height="20px"/>
                                                    </div>
                                                    <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                                     onclick="PF('dlgOk').hide();" />
                                                </p:dialog>    
                                            </p:commandLink>

                                            <p:outputLabel for="bairro" value="#{localemsgs.Bairro}: "/>
                                            <p:inputText id="bairro" value="#{clientescontainer.novo.bairro}"
                                                         label="#{localemsgs.Bairro}" style="width: 100%"
                                                         maxlength="25">
                                                <f:validateLength maximum="25"/>
                                                <p:watermark for="bairro" value="#{localemsgs.Bairro}"/>
                                            </p:inputText> 
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="ende" value="#{localemsgs.Endereco}: "/>
                                            <p:inputText id="ende" value="#{clientescontainer.novo.ende}"
                                                         style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="ende" value="#{localemsgs.Endereco}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="cidade" value="#{localemsgs.Cidade}: "/>
                                            <p:autoComplete id="cidade" value="#{clientescontainer.novo.cidade}" styleClass="cidade" 
                                                            completeMethod="#{clientescontainer.buscarCidade}" scrollHeight="200"
                                                            maxlength="25" forceSelection="true" style="width: 100%">
                                                <p:ajax event="itemSelect" listener="#{clientescontainer.selecionarCidade}"
                                                        update="formCadastrar:tabs:cidade formCadastrar:tabs:estado"/>
                                                <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                            </p:autoComplete>

                                            <p:outputLabel for="estado" value="#{localemsgs.UF}: "/>
                                            <p:inputText id="estado" value="#{clientescontainer.novo.estado}" style="width: 100%"
                                                         label="#{localemsgs.UF}" disabled="true" maxlength="2">
                                                <p:watermark for="estado" value="#{localemsgs.UF}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="fone1" value="#{localemsgs.Fone1}: "/>
                                            <p:inputMask id="fone1" value="#{clientescontainer.novo.fone1}" style="width: 100%"
                                                         label="#{localemsgs.Fone1}" maxlength="11" mask="#{mascaras.mascaraFone}">
                                                <p:watermark for="fone1" value="#{localemsgs.Fone1}"/>
                                            </p:inputMask>

                                            <p:outputLabel for="fone2" value="#{localemsgs.Fone2}: "/>
                                            <p:inputMask id="fone2" value="#{clientescontainer.novo.fone2}" style="width: 100%"
                                                         label="#{localemsgs.Fone2}" maxlength="11" mask="#{mascaras.mascaraFone}">
                                                <p:watermark for="fone2" value="#{localemsgs.Fone2}"/>
                                            </p:inputMask>

                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="atividade" value="#{localemsgs.Atividade}: "/>
                                            <p:selectOneMenu id="atividade" value="#{clientescontainer.novo.ramoAtiv}" 
                                                             converter="omnifaces.SelectItemsConverter"
                                                             styleClass="filial" style="width: 100%;"
                                                             filter="true" filterMatchMode="contains">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="0" noSelectionOption="true"/>
                                                <f:selectItems value="#{clientescontainer.ramosAtiv}" var="ramosAtiv" itemValue="#{ramosAtiv.codigo}"
                                                               itemLabel="#{ramosAtiv.codigo} - #{ramosAtiv.descricao}"/>
                                            </p:selectOneMenu>

                                            <p:outputLabel for="email" value="#{localemsgs.Email}: "/>
                                            <p:inputText id="email" value="#{clientescontainer.novo.email}" style="width: 100%"
                                                         label="#{localemsgs.Email}" maxlength="80">
                                                <p:watermark for="email" value="#{localemsgs.Email}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="cnpj" value="#{localemsgs.CNPJCPF}: "/>
                                            <p:inputText id="cnpj" value="#{clientescontainer.cpfcnpj}"
                                                         label="#{localemsgs.CNPJCPF}" style="width: 100%">
                                                <p:watermark for="cnpj" value="#{localemsgs.CNPJCPF}"/>
                                                <p:ajax event="blur" listener="#{clientescontainer.mascaraCNPJCPF}"
                                                        update="formCadastrar:tabs:cnpj msgs"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">

                                            <p:outputLabel for="ierg" value="#{localemsgs.IERG}: "/>
                                            <p:inputText id="ierg" value="#{clientescontainer.ierg}"
                                                         label="#{localemsgs.IERG}" style="width: 100%">
                                                <p:watermark for="ierg" value="#{localemsgs.IERG}"/>
                                            </p:inputText>

                                            <p:outputLabel for="im" value="#{localemsgs.Insc_Munic}: "/>
                                            <p:inputText id="im" value="#{clientescontainer.novo.insc_Munic}"
                                                         label="#{localemsgs.Insc_Munic}" style="width: 100%">
                                                <p:watermark for="im" value="#{localemsgs.Insc_Munic}"/>
                                            </p:inputText>
                                        </p:panelGrid>   
                                    </p:tab>
                                    <p:tab id="tabOrcamento" title="#{localemsgs.Orcamento}">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="qtdeCacambas" value="#{localemsgs.Orcamento}: " />
                                            <p:inputText id="qtdeCacambas" value="#{clientescontainer.qtdeCacambas}"
                                                         label="#{localemsgs.Orcamento}" maxlength="2" style="width: 100%">
                                                <p:watermark for="qtdeCacambas" value="#{localemsgs.Orcamento}"/>
                                            </p:inputText>
                                        </p:panelGrid>                                         
                                    </p:tab>
                                    <p:tab id="tabInterface" title="#{localemsgs.Interface}">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="interfext" value="#{localemsgs.InterfExt}: " />
                                            <p:inputText id="interfext" value="#{clientescontainer.novo.interfExt}"
                                                         label="#{localemsgs.InterfExt}" maxlength="80" style="width: 100%">
                                                <p:watermark for="interfext" value="#{localemsgs.InterfExt}"/>
                                            </p:inputText>
                                        </p:panelGrid> 

                                        <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-3,ui-grid-col-2" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="latitude" value="#{localemsgs.Latitude}: " />
                                            <p:inputText id="latitude" value="#{clientescontainer.novo.latitude}"
                                                         label="#{localemsgs.Latitude}" style="width: 100%">
                                                <p:watermark for="latitude" value="#{localemsgs.Latitude}"/>
                                            </p:inputText>

                                            <p:outputLabel for="longitude" value="#{localemsgs.Longitude}: " />
                                            <p:inputText id="longitude" value="#{clientescontainer.novo.longitude}"
                                                         label="#{localemsgs.Longitude}" style="width: 100%">
                                                <p:watermark for="longitude" value="#{localemsgs.Longitude}"/>
                                            </p:inputText>

                                            <p:commandLink title="#{localemsgs.Mapa}"
                                                           update="msgs" action="#{clientescontainer.posicaoCliente}">
                                                <p:graphicImage url="../assets/img/icone_redondo_mapa.png" height="30"/>
                                            </p:commandLink>
                                        </p:panelGrid> 

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="codExt" value="#{localemsgs.CodExt}: " />
                                            <p:inputText id="codExt" value="#{clientescontainer.novo.codExt}"
                                                         label="#{localemsgs.CodExt}" maxlength="80" style="width: 100%">
                                                <p:watermark for="codExt" value="#{localemsgs.CodExt}"/>
                                            </p:inputText>

                                            <p:outputLabel for="codPtoCli" value="#{localemsgs.CodPtoCli}: " />
                                            <p:inputText id="codPtoCli" value="#{clientescontainer.novo.codPtoCli}"
                                                         label="#{localemsgs.CodPtoCli}" maxlength="80" style="width: 100%">
                                                <p:watermark for="codPtoCli" value="#{localemsgs.CodPtoCli}"/>
                                            </p:inputText>

                                        </p:panelGrid> 
                                    </p:tab>
                                </p:tabView>    

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-12,ui-grid-col-12" 
                                             layout="grid" styleClass="ui-panelgrid-blank" style="display: block !important">
                                    <p:commandLink id="cadastro" action="#{clientescontainer.cadastrarCliente}"
                                                   update="msgs main:tabela"
                                                   title="#{localemsgs.Cadastrar}">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </p:panelGrid>
                            </p:panel>
                        </p:dialog>

                        <p:dialog id="dlgMapaCliente" widgetVar="dlgMapaCliente" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" 
                                  style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Clientes}" style="color:black" />
                            </f:facet>
                            <p:panel style="width:calc(100vw - 100px);height:calc(100vh - 100px);background-color: transparent" styleClass="cadastrar">
                                <p:gmap id="gmap" center="#{clientescontainer.centroMapa}" zoom="16" type="TERRAIN" 
                                        style="height:85vh" model="#{clientescontainer.mapaCliente}" />
                            </p:panel>
                        </p:dialog>
                    </h:form>
                </ui:define>
            </ui:composition>
        </h:body>
    </f:view>
</html>

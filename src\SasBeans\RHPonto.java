/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class RHPonto {

    private BigDecimal Matr;
    private String DtCompet;
    private BigDecimal Batida;
    private BigDecimal Tipo;
    private String Hora;
    private String Motivo;
    private String DtBatida;
    private String NSerie;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String local;
    private String secao;
    private String funcionario;
    private String latitude;
    private String longitude;
    private String foto;
    private String distancia;
    private String URL;

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(BigDecimal Matr) {
        this.Matr = Matr;
    }

    public String getDtCompet() {
        return DtCompet;
    }

    public void setDtCompet(String DtCompet) {
        this.DtCompet = DtCompet;
    }

    public BigDecimal getBatida() {
        return Batida;
    }

    public void setBatida(BigDecimal Batida) {
        this.Batida = Batida;
    }

    public BigDecimal getTipo() {
        return Tipo;
    }

    public void setTipo(BigDecimal Tipo) {
        this.Tipo = Tipo;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getMotivo() {
        return Motivo;
    }

    public void setMotivo(String Motivo) {
        this.Motivo = Motivo;
    }

    public String getDtBatida() {
        return DtBatida;
    }

    public void setDtBatida(String DtBatida) {
        this.DtBatida = DtBatida;
    }

    public String getNSerie() {
        return NSerie;
    }

    public void setNSerie(String NSerie) {
        this.NSerie = NSerie;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }

    public String getFuncionario() {
        return funcionario;
    }

    public void setFuncionario(String funcionario) {
        this.funcionario = funcionario;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public String getSecao() {
        return secao;
    }

    public void setSecao(String secao) {
        this.secao = secao;
    }

    public String getDistancia() {
        return distancia;
    }

    public void setDistancia(String distancia) {
        this.distancia = distancia;
    }

    public String getURL() {
        return URL;
    }

    public void setURL(String URL) {
        this.URL = URL;
    }
    
    
}

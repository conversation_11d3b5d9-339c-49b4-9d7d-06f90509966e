﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtAfastTemp/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtAfastTemp/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtAfastTemp">
                    <xs:annotation>
                        <xs:documentation>Evento Afastamento Temporario</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideVinculo">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Trabalhador e do Vinculo</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nisTrab" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>NIS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:maxLength value="11"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="matricula" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Matricula</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="30"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="codCateg" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo da Categoria</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:integer">
                                                    <xs:pattern value="\d{3}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoAfastamento">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Evento</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="iniAfastamento" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do Afastamento Temporario - Inicio</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtIniAfast">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de inicio do afastamento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codMotAfast">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do motivo de afastamento temporario, conforme tabela 18</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="2"/>
                                                                <xs:pattern value="\d{2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoMesmoMtv" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>O afastamento decorre de mesmo acidente/doenca anterior?</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="tpAcidTransito" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de acidente de transito</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="observacao" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Observacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="255"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoAtestado" minOccurs="0" maxOccurs="9">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes Complementares - Atestado Médico</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="codCID" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo CID</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="4"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="qtdDiasAfast">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Quantidade de dias de afastamento</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{1,3}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="emitente" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Médico/Dentista que emitiu o atestado</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="nmEmit">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Nome do médico/dentista que emitiu o atestado</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="2"/>
                                                                                        <xs:maxLength value="70"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideOC">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Orgao de classe:
                                                                                            1 - Conselho Regional de Medicina (CRM);
                                                                                            2 - Conselho Regional de Odontologia (CRO)</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrOc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número Inscricao OC</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="2"/>
                                                                                        <xs:maxLength value="14"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ufOC" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Sigla da UF do Orgao de classe</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:enumeration value="AC"/>
                                                                                        <xs:enumeration value="AL"/>
                                                                                        <xs:enumeration value="AP"/>
                                                                                        <xs:enumeration value="AM"/>
                                                                                        <xs:enumeration value="BA"/>
                                                                                        <xs:enumeration value="CE"/>
                                                                                        <xs:enumeration value="DF"/>
                                                                                        <xs:enumeration value="ES"/>
                                                                                        <xs:enumeration value="GO"/>
                                                                                        <xs:enumeration value="MA"/>
                                                                                        <xs:enumeration value="MT"/>
                                                                                        <xs:enumeration value="MS"/>
                                                                                        <xs:enumeration value="MG"/>
                                                                                        <xs:enumeration value="PA"/>
                                                                                        <xs:enumeration value="PB"/>
                                                                                        <xs:enumeration value="PR"/>
                                                                                        <xs:enumeration value="PE"/>
                                                                                        <xs:enumeration value="PI"/>
                                                                                        <xs:enumeration value="RJ"/>
                                                                                        <xs:enumeration value="RN"/>
                                                                                        <xs:enumeration value="RS"/>
                                                                                        <xs:enumeration value="RO"/>
                                                                                        <xs:enumeration value="RR"/>
                                                                                        <xs:enumeration value="SC"/>
                                                                                        <xs:enumeration value="SP"/>
                                                                                        <xs:enumeration value="SE"/>
                                                                                        <xs:enumeration value="TO"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoCessao" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes Complementares - Cessao/requisicao de trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="cnpjCess">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>CNPJ do Cessionario</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="14"/>
                                                                            <xs:pattern value="\d{14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infOnus">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Ônus da Cessao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoMandSind" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes Complementares - afastamento para exercicio de mandato sindical</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="cnpjSind">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>CNPJ do Sindicato</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="14"/>
                                                                            <xs:pattern value="\d{14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infOnusRemun">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Ônus da Remuneracao:
                                                                                1 - Apenas do Empregador; 
                                                                                2 - Apenas do Sindicato;
                                                                                3 - Parte do Empregador, sendo a diferenca e/ou complementacao salarial paga pelo Sindicato</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoRetif" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de retificacao do Afastamento Temporario</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="origRetif">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Origem da retificacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="tpProc" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de Processo</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrProc" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Número do Processo</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="21"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="fimAfastamento" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do Término do Afastamento</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtTermAfast">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Preencher com a data do término do afastamento do trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

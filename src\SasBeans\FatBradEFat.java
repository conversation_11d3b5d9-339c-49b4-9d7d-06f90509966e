/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FatBradEFat {

    private BigDecimal SeqArq;
    private BigDecimal Sequencia;
    private String Empresa;
    private LocalDate DtIniSrv;
    private LocalDate DtFimSrv;
    private String DiaSem;
    private int DiasCst;
    private int NroPA;
    private int NroBDN;
    private String NomePA;
    private String EnderecoPA;
    private String Municipio;
    private String TipoSrv;
    private String HrCheg;
    private String HrSaida;
    private String ClassifSrv;
    private String Juncao;
    private String IDSrv;
    private BigDecimal GTVSup;
    private String GTVSupSerie;
    private BigDecimal ValorSup;
    private BigDecimal GTVRet;
    private String GTVRetSerie;
    private BigDecimal ValorRet;
    private BigDecimal ValorCh;
    private String Descricao;
    private BigDecimal AdValorem;
    private BigDecimal Embarque;
    private BigDecimal ICMS;
    private BigDecimal Custodia;
    private BigDecimal TotalFat;
    private BigDecimal Multa;
    private String Rota;
    private String Status;

    /**
     *
     * @return SetArq
     */
    public BigDecimal getSeqArq() {
        return this.SeqArq;
    }

    /**
     * Set SeqArq
     *
     * @param SeqArq
     */
    public void setSeqArq(String SeqArq) {
        try {
            this.SeqArq = new BigDecimal(SeqArq);
        } catch (Exception e) {
            this.SeqArq = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Sequencia
     */
    public BigDecimal getSequencia() {
        return this.Sequencia;
    }

    /**
     * Set Sequencia
     *
     * @param Sequencia
     */
    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Empresa
     */
    public String getEmpresa() {
        return this.Empresa;
    }

    /**
     * Set Empresa
     *
     * @param Empresa
     */
    public void setEmpresa(String Empresa) {
        this.Empresa = Empresa;
    }

    /**
     *
     * @return DtIniSrv
     */
    public LocalDate getDtIniSrv() {
        return this.DtIniSrv;
    }

    /**
     * Set DtIniSrv
     *
     * @param DtIniSrv
     */
    public void setDtIniSrv(LocalDate DtIniSrv) {
        this.DtIniSrv = DtIniSrv;
    }

    /**
     *
     * @return DtFimSrv
     */
    public LocalDate getDtFimSrv() {
        return this.DtFimSrv;
    }

    /**
     * Set DtFimSrv
     *
     * @param DtFimSrv
     */
    public void setDtFimSrv(LocalDate DtFimSrv) {
        this.DtFimSrv = DtFimSrv;
    }

    /**
     *
     * @return DiaSem
     */
    public String getDiaSem() {
        return this.DiaSem;
    }

    /**
     * Set DiaSem
     *
     * @param DiaSem
     */
    public void setDiaSem(String DiaSem) {
        this.DiaSem = DiaSem;
    }

    /**
     *
     * @return DiasCst
     */
    public int getDiasCst() {
        return this.DiasCst;
    }

    /**
     * Set DiasCst
     *
     * @param DiasCst
     */
    public void setDiasCst(int DiasCst) {
        this.DiasCst = DiasCst;
    }

    /**
     *
     * @return NroPA
     */
    public int getNroPA() {
        return this.NroPA;
    }

    /**
     * Set NroPA
     *
     * @param NroPA
     */
    public void setNroPA(int NroPA) {
        this.NroPA = NroPA;
    }

    /**
     *
     * @return NroBDN
     */
    public int getNroBDN() {
        return this.NroBDN;
    }

    /**
     * Set NroBDN
     *
     * @param NroBDN
     */
    public void setNroBDN(int NroBDN) {
        this.NroBDN = NroBDN;
    }

    /**
     *
     * @return NomePA
     */
    public String getNomePA() {
        return this.NomePA;
    }

    /**
     * Set NomePA
     *
     * @param NomePA
     */
    public void setNomePA(String NomePA) {
        this.NomePA = NomePA;
    }

    /**
     *
     * @return EnderecoPA
     */
    public String getEnderecoPA() {
        return this.EnderecoPA;
    }

    /**
     * Set EnderecoPA
     *
     * @param EnderecoPA
     */
    public void setEnderecoPA(String EnderecoPA) {
        this.EnderecoPA = EnderecoPA;
    }

    /**
     *
     * @return Municipio
     */
    public String getMunicipio() {
        return this.Municipio;
    }

    /**
     * Set Municipio
     *
     * @param Municipio
     */
    public void setMunicipio(String Municipio) {
        this.Municipio = Municipio;
    }

    /**
     *
     * @return TipoSrv
     */
    public String getTipoSrv() {
        return this.TipoSrv;
    }

    /**
     * Set TipoSrv
     *
     * @param TipoSrv
     */
    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    /**
     *
     * @return HrCheg
     */
    public String getHrCheg() {
        return this.HrCheg;
    }

    /**
     * Set HrCheg
     *
     * @param HrCheg
     */
    public void setHrCheg(String HrCheg) {
        this.HrCheg = HrCheg;
    }

    /**
     *
     * @return HrSaida
     */
    public String getHrSaida() {
        return this.HrSaida;
    }

    /**
     * Set HrSaida
     *
     * @param HrSaida
     */
    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    /**
     *
     * @return ClassifSrv
     */
    public String getClassifSrv() {
        return this.ClassifSrv;
    }

    /**
     * Set ClassifSrv
     *
     * @param ClassifSrv
     */
    public void setClassifSrv(String ClassifSrv) {
        this.ClassifSrv = ClassifSrv;
    }

    /**
     *
     * @return Juncao
     */
    public String getJuncao() {
        return this.Juncao;
    }

    /**
     * Set Juncao
     *
     * @param Juncao
     */
    public void setJuncao(String Juncao) {
        this.Juncao = Juncao;
    }

    /**
     *
     * @return getIDSrv
     */
    public String getIDSrv() {
        return this.IDSrv;
    }

    /**
     * Set getIDSrv
     *
     * @param IDSrv
     */
    public void setIDSrv(String IDSrv) {
        this.IDSrv = IDSrv;
    }

    /**
     *
     * @return GTVSup
     */
    public BigDecimal getGTVSup() {
        return this.GTVSup;
    }

    /**
     * Set GTVSup
     *
     * @param GTVSup
     */
    public void setGTVSup(String GTVSup) {
        try {
            this.GTVSup = new BigDecimal(GTVSup);
        } catch (Exception e) {
            this.GTVSup = new BigDecimal("0");
        }
    }

    /**
     *
     * @return GTVSupSerie
     */
    public String getGTVSupSerie() {
        return this.GTVSupSerie;
    }

    /**
     * Set GTVSupSerie
     *
     * @param GTVSupSerie
     */
    public void setGTVSupSerie(String GTVSupSerie) {
        this.GTVSupSerie = GTVSupSerie;
    }

    /**
     *
     * @return ValorSup
     */
    public BigDecimal getValorSup() {
        return this.ValorSup;
    }

    /**
     * Set ValorSup
     *
     * @param ValorSup
     */
    public void setValorSup(String ValorSup) {
        try {
            this.ValorSup = new BigDecimal(ValorSup);
        } catch (Exception e) {
            this.ValorSup = new BigDecimal("0");
        }
    }

    /**
     *
     * @return GTVRet
     */
    public BigDecimal getGTVRet() {
        return this.GTVRet;
    }

    /**
     * Set GTVRet
     *
     * @param GTVRet
     */
    public void setGTVRet(String GTVRet) {
        try {
            this.GTVRet = new BigDecimal(GTVRet);
        } catch (Exception e) {
            this.GTVRet = new BigDecimal("0");
        }
    }

    /**
     *
     * @return GTVRetSerie
     */
    public String getGTVRetSerie() {
        return this.GTVRetSerie;
    }

    /**
     * Set GTVRetSerie
     *
     * @param GTVRetSerie
     */
    public void setGTVRetSerie(String GTVRetSerie) {
        this.GTVRetSerie = GTVRetSerie;
    }

    /**
     *
     * @return ValorRet
     */
    public BigDecimal getValorRet() {
        return this.ValorRet;
    }

    /**
     * Set ValorRet
     *
     * @param ValorRet
     */
    public void setValorRet(String ValorRet) {
        try {
            this.ValorRet = new BigDecimal(ValorRet);
        } catch (Exception e) {
            this.ValorRet = new BigDecimal("0");
        }
    }

    /**
     *
     * @return ValorCh
     */
    public BigDecimal getValorCh() {
        return this.ValorCh;
    }

    /**
     * Set ValorCh
     *
     * @param ValorCh
     */
    public void setValorCh(String ValorCh) {
        try {
            this.ValorCh = new BigDecimal(ValorCh);
        } catch (Exception e) {
            this.ValorCh = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Descricao
     */
    public String getDescricao() {
        return this.Descricao;
    }

    /**
     * Set Descricao
     *
     * @param Descricao
     */
    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    /**
     *
     * @return AdValorem
     */
    public BigDecimal getAdValorem() {
        return this.AdValorem;
    }

    /**
     * Set AdValorem
     *
     * @param AdValorem
     */
    public void setAdValorem(String AdValorem) {
        try {
            this.AdValorem = new BigDecimal(AdValorem);
        } catch (Exception e) {
            this.AdValorem = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Embarque
     */
    public BigDecimal getEmbarque() {
        return this.Embarque;
    }

    /**
     * Set Embarque
     *
     * @param Embarque
     */
    public void setEmbarque(String Embarque) {
        try {
            this.Embarque = new BigDecimal(Embarque);
        } catch (Exception e) {
            this.Embarque = new BigDecimal("0");
        }
    }

    /**
     *
     * @return ICMS
     */
    public BigDecimal getICMS() {
        return this.ICMS;
    }

    /**
     * Set ICMS
     *
     * @param ICMS
     */
    public void setICMS(String ICMS) {
        try {
            this.ICMS = new BigDecimal(ICMS);
        } catch (Exception e) {
            this.ICMS = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Custodia
     */
    public BigDecimal getCustodia() {
        return this.Custodia;
    }

    /**
     * Set Custodia
     *
     * @param Custodia
     */
    public void setCustodia(String Custodia) {
        try {
            this.Custodia = new BigDecimal(Custodia);
        } catch (Exception e) {
            this.Custodia = new BigDecimal("0");
        }
    }

    /**
     *
     * @return TotalFat
     */
    public BigDecimal getTotalFat() {
        return this.TotalFat;
    }

    /**
     * Set TotalFat
     *
     * @param TotalFat
     */
    public void setTotalFat(String TotalFat) {
        try {
            this.TotalFat = new BigDecimal(TotalFat);
        } catch (Exception e) {
            this.TotalFat = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Multa
     */
    public BigDecimal getMulta() {
        return this.Multa;
    }

    /**
     * Set Multa
     *
     * @param Multa
     */
    public void setMulta(String Multa) {
        try {
            this.Multa = new BigDecimal(Multa);
        } catch (Exception e) {
            this.Multa = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Rota
     */
    public String getRota() {
        return this.Rota;
    }

    /**
     * Set Rota
     *
     * @param Rota
     */
    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    /**
     *
     * @return Status
     */
    public String getStatus() {
        return this.Status;
    }

    /**
     * Set Status
     *
     * @param Status
     */
    public void setStatus(String Status) {
        this.Status = Status;
    }
}

/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesAutomatiza;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TesAutomatizaDao {

    /**
     * Retorna a listagem do dia
     *
     * @param CodPessoa - código da pessoa
     * @param persistencia - conexão com o banco
     * @return - retorna lista ou nulo para
     * @throws Exception
     */
    public List<TesAutomatiza> Listagem(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<TesAutomatiza> retorno;
        String vSQL = "select tesautomatiza.*, filiais.descricao, filiais.cidade, clientes.nred desctes "
                + " from tesautomatiza"
                + " left join filiais on tesautomatiza.codfil = filiais.codfil"
                + " left join clientes on tesautomatiza.codtes = clientes.codigo"
                + "                   and tesautomatiza.codfil = clientes.codfil"
                + " where data = convert(date,getdate()) and codpessoa = ?";
        try {
            retorno = new ArrayList<>();
            Consulta consult = new Consulta(vSQL, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.select();
            TesAutomatiza tesautomatiza;
            while (consult.Proximo()) {
                tesautomatiza = new TesAutomatiza();
                tesautomatiza.setCamera(consult.getInt("camera"));
                tesautomatiza.setCodFil(consult.getBigDecimal("codfil"));
                tesautomatiza.setCodPessoa(consult.getBigDecimal("codpessoa"));
                tesautomatiza.setCodTes(consult.getString("codtes"));
                tesautomatiza.setData(consult.getLocalDate("data"));
                tesautomatiza.setDt_alter(consult.getLocalDate("dt_alter"));
                tesautomatiza.setHr_alter(consult.getString("hr_alter"));
                tesautomatiza.setMatr(consult.getBigDecimal("matr"));
                tesautomatiza.setOperador(consult.getString("operador"));
                tesautomatiza.setSequencia(consult.getBigDecimal("sequencia"));
                tesautomatiza.setTipoContadora(consult.getInt("tipocontadora"));
                tesautomatiza.setDescFil(consult.getString("descricao"));
                tesautomatiza.setDescTes(consult.getString("desctes"));
                tesautomatiza.setCidadeFil(consult.getString("cidade"));
                retorno.add(tesautomatiza);
            }
            consult.Close();
        } catch (Exception e) {
            retorno = null;
            throw new Exception("Falha ao carregar dados da Tesouraria - " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Conta o número de liberações cadastradas no banco
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalTesAutomatizaSatMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from tesautomatiza"
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "Sequencia > 0 ";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.TotalPessoaMobWeb - " + e.getMessage());
        }
    }    

    /**
     * Listagem paginada tesAutomatiza
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TesAutomatiza> ListaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<TesAutomatiza> retorno;
        String vSQL;

        vSQL = "Select * from ("
                + "select ROW_NUMBER() OVER ( ORDER BY nome ) AS RowNum, \n "
                + " tesautomatiza.camera, tesautomatiza.codfil,tesautomatiza.codpessoa, \n "
                + " tesautomatiza.codtes, tesautomatiza.data, tesautomatiza.dt_alter, tesautomatiza.hr_alter, \n "                
                + " tesautomatiza.matr,tesautomatiza.operador,tesautomatiza.sequencia,tesautomatiza.tipocontadora, \n "
                + " filiais.descricao, filiais.cidade, clientes.nred desctes, funcion.nome nomeColaborador  "                               
                + " from tesautomatiza"
                + " left join filiais on tesautomatiza.codfil = filiais.codfil "
                + " left join clientes on tesautomatiza.codtes = clientes.codigo "
                + "                   and tesautomatiza.codfil = clientes.codfil "
                + " left join funcion  on funcion.matr = tesautomatiza.matr ";
        Map<String, String> filtro = filtros;
        for (Map.Entry<String, String> entrada : filtro.entrySet()) {
            if (!entrada.getValue().equals("")) {
                vSQL = vSQL + entrada.getKey() + " AND ";
            }
        }
        vSQL = vSQL + "codigo IS NOT null AND Nome is not null AND Nome <> '') AS RowConstrainedResult "
                + "WHERE   RowNum >= ?"
                + "    AND RowNum < ? "
                + "ORDER BY RowNum";

        try {
            retorno = new ArrayList<>();
            Consulta consult = new Consulta(vSQL, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            TesAutomatiza tesautomatiza;
            while (consult.Proximo()) {
                tesautomatiza = new TesAutomatiza();
                tesautomatiza.setCamera(consult.getInt("camera"));
                tesautomatiza.setCodFil(consult.getBigDecimal("codfil"));
                tesautomatiza.setCodPessoa(consult.getBigDecimal("codpessoa"));
                tesautomatiza.setCodTes(consult.getString("codtes"));
                tesautomatiza.setData(consult.getLocalDate("data"));
                tesautomatiza.setDt_alter(consult.getLocalDate("dt_alter"));
                tesautomatiza.setHr_alter(consult.getString("hr_alter"));
                tesautomatiza.setMatr(consult.getBigDecimal("matr"));
                tesautomatiza.setOperador(consult.getString("operador"));
                tesautomatiza.setSequencia(consult.getBigDecimal("sequencia"));
                tesautomatiza.setTipoContadora(consult.getInt("tipocontadora"));
                tesautomatiza.setDescFil(consult.getString("descricao"));
                tesautomatiza.setDescTes(consult.getString("desctes"));
                tesautomatiza.setCidadeFil(consult.getString("cidade"));
                tesautomatiza.setNome(consult.getString("nomeColaborador"));
                retorno.add(tesautomatiza);
            }
            consult.Close();
        } catch (Exception e) {
            retorno = null;
            throw new Exception("TesAutomatizaDao.ListaPaginada  - " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Insere cadastro
     *
     * @param tesautomatiza
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean gravaCadastro(TesAutomatiza tesautomatiza, Persistencia persistencia) throws Exception {
        boolean retorno;
        String vSQL = "Insert into TesAutomatiza ((Select Isnull(max(Sequencia),0)+1 from TesAutomatiza), \n"
                + "Data, CodPessoa, Matr, CodFil, CodTes, \n"
                + "Camera, TipoContadora, Operador, Dt_alter, Hr_alter) \n"
                + "Values (?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(vSQL, persistencia);
            consulta.getString(tesautomatiza.getData().toString());
            consulta.setBigDecimal(tesautomatiza.getCodPessoa());
            consulta.setBigDecimal(tesautomatiza.getMatr());
            consulta.setBigDecimal(tesautomatiza.getCodFil());
            consulta.setString(tesautomatiza.getCodTes());
            consulta.setInt(tesautomatiza.getCamera());
            consulta.setInt(tesautomatiza.getTipoContadora());
            consulta.setString(tesautomatiza.getOperador());
            consulta.setString(tesautomatiza.getDt_alter().toString());
            consulta.setString(tesautomatiza.getHr_alter());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            throw new Exception("TesAutomatizaDao.gravaCadastro - " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Atualiza a tesouraria liberada para o conferente
     *
     * @param codPessoa
     * @param codTes
     * @param persistencia
     * @throws Exception
     */
    public void atualizaTesouraria(String codPessoa, String codTes, Persistencia persistencia) throws Exception {
        try {
            String sql = " update tesautomatiza set codtes = ? "
                    + " where codpessoa = ? and data = convert(date,getdate()) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codTes);
            consulta.setString(codPessoa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar tesautomatiza - " + e.getMessage());
        }
    }

    /**
     * Atualiza a camera liberada para o conferente
     *
     * @param codPessoa
     * @param camera
     * @param persistencia
     * @throws Exception
     */
    public void atualizaCamera(String codPessoa, String camera, Persistencia persistencia) throws Exception {
        try {
            String sql = " update tesautomatiza set camera = ? "
                    + " where codpessoa = ? and data = convert(date,getdate()) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(camera);
            consulta.setString(codPessoa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar tesautomatiza - " + e.getMessage());
        }

    }

}

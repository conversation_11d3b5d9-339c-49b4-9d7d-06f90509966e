package SasControleTelas;

import Satellite.Models.Formatados.Dados;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ControlaTelaLista {

    private String Servidor;
    //topo tela
    private String LogoTopo;
    private String Titulo;
    private List<String> BarraInfo;
    //periodo
    private boolean BtnAvancaPeriodo;
    private boolean BtnVoltaPeriodo;
    private boolean Calendario;
    //cabecalho listagem
    private List<String> RotulosColunas;
    private List<String> TiposColunas;
    private List<Integer> TamanhoColunas;
    //dados da listagem
    private Dados dados;
    //barra inferior de botoes
    private boolean BtnFiltro;
    private boolean BtnF2;
    private boolean BtnCancelar;
    private boolean BtnImprimir;
    private boolean BtnFrequencia;
    private boolean BtnExpExcel;
    private int Registros;
    private boolean BtnSubir;
    private boolean BtnDescer;
    //barra menu lateral
    private boolean BtnMenu;
    private boolean BtnInserir;
    private boolean BtnExcluir;
    private boolean BtnEditar;
    private boolean BtnReciclar;
    private boolean BtnMostrarExcluidos;
    private boolean BtnCalcular;

    public ControlaTelaLista() {
        this.Servidor = "";
        this.LogoTopo = "";
        this.Titulo = "";
        this.BarraInfo = new ArrayList();
        this.BtnAvancaPeriodo = false;
        this.BtnVoltaPeriodo = false;
        this.Calendario = false;
        this.RotulosColunas = new ArrayList();
        this.TiposColunas = new ArrayList();
        this.TamanhoColunas = new ArrayList();
        this.dados = new Dados();
        this.BtnFiltro = false;
        this.BtnF2 = false;
        this.BtnCancelar = false;
        this.BtnImprimir = false;
        this.BtnFrequencia = false;
        this.BtnExpExcel = false;
        this.Registros = 0;
        this.BtnSubir = false;
        this.BtnDescer = false;
        this.BtnMenu = false;
        this.BtnInserir = false;
        this.BtnExcluir = false;
        this.BtnEditar = false;
        this.BtnReciclar = false;
        this.BtnMostrarExcluidos = false;
        this.BtnCalcular = false;
    }

    public String getServidor() {
        return Servidor;
    }

    public void setServidor(String Servidor) {
        this.Servidor = Servidor;
    }

    public String getLogoTopo() {
        return LogoTopo;
    }

    public void setLogoTopo(String LogoTopo) {
        this.LogoTopo = LogoTopo;
    }

    public String getTitulo() {
        return Titulo;
    }

    public void setTitulo(String Titulo) {
        this.Titulo = Titulo;
    }

    public List<String> getBarraInfo() {
        return BarraInfo;
    }

    public void setBarraInfo(List<String> BarraInfo) {
        this.BarraInfo = BarraInfo;
    }

    public boolean isBtnAvancaPeriodo() {
        return BtnAvancaPeriodo;
    }

    public void setBtnAvancaPeriodo(boolean BtnAvancaPeriodo) {
        this.BtnAvancaPeriodo = BtnAvancaPeriodo;
    }

    public boolean isBtnVoltaPeriodo() {
        return BtnVoltaPeriodo;
    }

    public void setBtnVoltaPeriodo(boolean BtnVoltaPeriodo) {
        this.BtnVoltaPeriodo = BtnVoltaPeriodo;
    }

    public boolean isCalendario() {
        return Calendario;
    }

    public void setCalendario(boolean Calendario) {
        this.Calendario = Calendario;
    }

    public List<String> getRotulosColunas() {
        return RotulosColunas;
    }

    public void setRotulosColunas(List<String> RotulosColunas) {
        this.RotulosColunas = RotulosColunas;
    }

    public List<String> getTiposColunas() {
        return TiposColunas;
    }

    public void setTiposColunas(List<String> TiposColunas) {
        this.TiposColunas = TiposColunas;
    }

    public List<Integer> getTamanhoColunas() {
        return TamanhoColunas;
    }

    public void setTamanhoColunas(List<Integer> TamanhoColunas) {
        this.TamanhoColunas = TamanhoColunas;
    }

    public Dados getDados() {
        return dados;
    }

    public void setDados(Dados dados) {
        this.dados = dados;
    }

    public boolean isBtnFiltro() {
        return BtnFiltro;
    }

    public void setBtnFiltro(boolean BtnFiltro) {
        this.BtnFiltro = BtnFiltro;
    }

    public boolean isBtnF2() {
        return BtnF2;
    }

    public void setBtnF2(boolean BtnF2) {
        this.BtnF2 = BtnF2;
    }

    public boolean isBtnCancelar() {
        return BtnCancelar;
    }

    public void setBtnCancelar(boolean BtnCancelar) {
        this.BtnCancelar = BtnCancelar;
    }

    public boolean isBtnImprimir() {
        return BtnImprimir;
    }

    public void setBtnImprimir(boolean BtnImprimir) {
        this.BtnImprimir = BtnImprimir;
    }

    public boolean isBtnFrequencia() {
        return BtnFrequencia;
    }

    public void setBtnFrequencia(boolean BtnFrequencia) {
        this.BtnFrequencia = BtnFrequencia;
    }

    public boolean isBtnExpExcel() {
        return BtnExpExcel;
    }

    public void setBtnExpExcel(boolean BtnExpExcel) {
        this.BtnExpExcel = BtnExpExcel;
    }

    public int getRegistros() {
        return Registros;
    }

    public void setRegistros(int Registros) {
        this.Registros = Registros;
    }

    public boolean isBtnSubir() {
        return BtnSubir;
    }

    public void setBtnSubir(boolean BtnSubir) {
        this.BtnSubir = BtnSubir;
    }

    public boolean isBtnDescer() {
        return BtnDescer;
    }

    public void setBtnDescer(boolean BtnDescer) {
        this.BtnDescer = BtnDescer;
    }

    public boolean isBtnMenu() {
        return BtnMenu;
    }

    public void setBtnMenu(boolean BtnMenu) {
        this.BtnMenu = BtnMenu;
    }

    public boolean isBtnInserir() {
        return BtnInserir;
    }

    public void setBtnInserir(boolean BtnInserir) {
        this.BtnInserir = BtnInserir;
    }

    public boolean isBtnExcluir() {
        return BtnExcluir;
    }

    public void setBtnExcluir(boolean BtnExcluir) {
        this.BtnExcluir = BtnExcluir;
    }

    public boolean isBtnEditar() {
        return BtnEditar;
    }

    public void setBtnEditar(boolean BtnEditar) {
        this.BtnEditar = BtnEditar;
    }

    public boolean isBtnReciclar() {
        return BtnReciclar;
    }

    public void setBtnReciclar(boolean BtnReciclar) {
        this.BtnReciclar = BtnReciclar;
    }

    public boolean isBtnMostrarExcluidos() {
        return BtnMostrarExcluidos;
    }

    public void setBtnMostrarExcluidos(boolean BtnMostrarExcluidos) {
        this.BtnMostrarExcluidos = BtnMostrarExcluidos;
    }

    public boolean isBtnCalcular() {
        return BtnCalcular;
    }

    public void setBtnCalcular(boolean BtnCalcular) {
        this.BtnCalcular = BtnCalcular;
    }
}

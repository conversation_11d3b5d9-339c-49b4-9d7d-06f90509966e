/*
 */
package br.com.sasw.conversores;

import SasBeans.Clientes;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorCliente")
public class ConversorCliente implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Clientes c = new Clientes();
        c.setCodFil(value.substring(value.indexOf(";") + 1));
        c.setCodigo(value.substring(0, value.indexOf(";")));
        return c;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return ((Clientes) value).getCodigo() + ";" + ((Clientes) value).getCodFil().toBigInteger();
        } catch (Exception e) {
            return null;
        }
    }
}

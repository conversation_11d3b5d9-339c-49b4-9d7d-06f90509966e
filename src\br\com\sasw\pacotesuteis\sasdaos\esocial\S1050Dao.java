/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1050;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1050Dao {

    public List<S1050> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select Convert(bigint,(RhHorario.Codfil*10000)+Codigo) ideHorContratual_codHorContrat, "
                    + " Hora201, Hora301, Hora401, Hora501, Hora601, Hora701, <PERSON>ra101, "
                    + " Hora204, <PERSON>ra304, <PERSON>ra404, <PERSON>ra504, <PERSON>ra604, <PERSON>ra704, <PERSON>ra104, "
                    + " Case when ((DATEDIFF(MI, <PERSON>ra201, <PERSON>ra204))-IntervMin) <= 0 then 528 "
                    + " else ((DATEDIFF(MI, Hora201, Hora204))-IntervMin) end dadosHorContratual_durJornada, IntervMin horarioIntervalo_durInterv, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z "
                    + "         where z.Identificador = Convert(bigint,(RhHorario.Codfil*10000)+Codigo) "
                    + "             and z.evento = 'S-1050' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))"
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Convert(bigint,(RhHorario.Codfil*10000)+Codigo) "
                    + "             and z.evento = 'S-1050' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Convert(bigint,(RhHorario.Codfil*10000)+Codigo) "
                    + "             and z.evento = 'S-1050' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " from RhHorario "
                    + " Left Join Filiais on Filiais.codFil = RhHorario.CodFil"
                    + " Where RhHorario.CodFil = ? and RhHorario.codigo in "
                    + " (Select funcion.horario from funcion where funcion.situacao <> 'D' and funcion.codfil = RhHorario.codfil"
                    + " union "
                    + "  Select Horario From FPMensal where FPMensal.CodFil = RhHorario.Codfil and Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') ) "
                    + " ORDER BY sucesso asc, Convert(bigint,(RhHorario.Codfil*10000)+Codigo) asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.select();
            List<S1050> retorno = new ArrayList<>();
            S1050 s1050;
            while (consulta.Proximo()) {
                s1050 = new S1050();
                s1050.setIdeEvento_procEmi("1");
                s1050.setSucesso(consulta.getInt("sucesso"));
                s1050.setIdeEvento_verProc("Satellite eSocial");
                s1050.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1050.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s1050.setIdeHorContratual_codHorContrat(consulta.getString("ideHorContratual_codHorContrat"));
                s1050.setDadosHorContratual_hrEntr(
                        !consulta.getString("Hora101").equals("") ? consulta.getString("Hora101")
                        : (!consulta.getString("Hora201").equals("") ? consulta.getString("Hora201")
                        : (!consulta.getString("Hora301").equals("") ? consulta.getString("Hora301")
                        : (!consulta.getString("Hora401").equals("") ? consulta.getString("Hora401")
                        : (!consulta.getString("Hora501").equals("") ? consulta.getString("Hora501")
                        : (!consulta.getString("Hora601").equals("") ? consulta.getString("Hora601")
                        : (!consulta.getString("Hora701").equals("") ? consulta.getString("Hora701") : "")))))));
                s1050.setDadosHorContratual_hrSaida(
                        !consulta.getString("Hora104").equals("") ? consulta.getString("Hora104")
                        : (!consulta.getString("Hora204").equals("") ? consulta.getString("Hora204")
                        : (!consulta.getString("Hora304").equals("") ? consulta.getString("Hora304")
                        : (!consulta.getString("Hora404").equals("") ? consulta.getString("Hora404")
                        : (!consulta.getString("Hora504").equals("") ? consulta.getString("Hora504")
                        : (!consulta.getString("Hora604").equals("") ? consulta.getString("Hora604")
                        : (!consulta.getString("Hora704").equals("") ? consulta.getString("Hora704") : "")))))));

                s1050.setDadosHorContratual_durJornada(consulta.getString("dadosHorContratual_durJornada"));

                s1050.setDadosHorContratual_perHorFlexivel("N");
                s1050.setHorarioIntervalo_tpInterv("2");
                s1050.setHorarioIntervalo_durInterv(consulta.getString("horarioIntervalo_durInterv"));
                retorno.add(s1050);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1050Dao.get - " + e.getMessage() + "\r\n"
                    + " Select Convert(bigint,(RhHorario.Codfil*10000)+Codigo) ideHorContratual_codHorContrat, "
                    + " Hora201, Hora301, Hora401, Hora501, Hora601, Hora701, Hora101, "
                    + " Hora204, Hora304, Hora404, Hora504, Hora604, Hora704, Hora104, "
                    + " Case when ((DATEDIFF(MI, Hora201, Hora204))-IntervMin) <= 0 then 528 "
                    + " else ((DATEDIFF(MI, Hora201, Hora204))-IntervMin) end dadosHorContratual_durJornada, IntervMin horarioIntervalo_durInterv, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z "
                    + "         where z.Identificador = Convert(bigint,(RhHorario.Codfil*10000)+Codigo) "
                    + "         and z.evento = 'S-1050' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "         and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Convert(bigint,(RhHorario.Codfil*10000)+Codigo) "
                    + "         and z.evento = 'S-1050' "
                    + "         and z.CodFil = " + codFil
                    + "         and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = Convert(bigint,(RhHorario.Codfil*10000)+Codigo) "
                    + "         and z.evento = 'S-1050' "
                    + "             and z.CodFil = " + codFil
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " from RhHorario "
                    + " Left Join Filiais on Filiais.codFil = RhHorario.CodFil"
                    + " Where RhHorario.CodFil = ? and RhHorario.codigo in "
                    + " (select funcion.horario from funcion where funcion.situacao <> 'D'and funcion.codfil = rhHorario.codfil) "
                    + " ORDER BY sucesso asc, Convert(bigint,(RhHorario.Codfil*10000)+Codigo) asc ");
        }
    }
}

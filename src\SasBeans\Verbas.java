/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class Verbas {

    private String Verba;
    private String Descricao;
    private String Tipo;
    private String TipoCalc;
    private String TipoTribut;
    private String TipoPgto;
    private String INSS;
    private String INSSEmpresa;
    private String IR;
    private String FGTS;
    private String DSR;
    private String Incid13;
    private String IncidFer;
    private String IncidFerTer;
    private String IncidRAIS;
    private String IncidINFREDND;
    private String IncidVT;
    private String IncidSalFamilia;
    private String IncidPensao;
    private String IncidARVVar;
    private String NRed;
    private String Formula;
    private BigDecimal Limite;
    private String Conta_CtbDesp;
    private String Conta_CtbCusto;
    private String Conta_CtbAtivo;
    private String Conta_CtbPassivo;
    private String PlanoSaude;
    private String TipoDep;
    private BigDecimal CodForn;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Verba
     */
    public String getVerba() {
        return Verba;
    }

    /**
     * @param Verba the Verba to set
     */
    public void setVerba(String Verba) {
        this.Verba = Verba;
    }

    /**
     * @return the Descricao
     */
    public String getDescricao() {
        return Descricao;
    }

    /**
     * @param Descricao the Descricao to set
     */
    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    /**
     * @return the Tipo
     */
    public String getTipo() {
        return Tipo;
    }

    /**
     * @param Tipo the Tipo to set
     */
    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    /**
     * @return the TipoCalc
     */
    public String getTipoCalc() {
        return TipoCalc;
    }

    /**
     * @param TipoCalc the TipoCalc to set
     */
    public void setTipoCalc(String TipoCalc) {
        this.TipoCalc = TipoCalc;
    }

    /**
     * @return the TipoTribut
     */
    public String getTipoTribut() {
        return TipoTribut;
    }

    /**
     * @param TipoTribut the TipoTribut to set
     */
    public void setTipoTribut(String TipoTribut) {
        this.TipoTribut = TipoTribut;
    }

    /**
     * @return the TipoPgto
     */
    public String getTipoPgto() {
        return TipoPgto;
    }

    /**
     * @param TipoPgto the TipoPgto to set
     */
    public void setTipoPgto(String TipoPgto) {
        this.TipoPgto = TipoPgto;
    }

    /**
     * @return the INSS
     */
    public String getINSS() {
        return INSS;
    }

    /**
     * @param INSS the INSS to set
     */
    public void setINSS(String INSS) {
        this.INSS = INSS;
    }

    /**
     * @return the INSSEmpresa
     */
    public String getINSSEmpresa() {
        return INSSEmpresa;
    }

    /**
     * @param INSSEmpresa the INSSEmpresa to set
     */
    public void setINSSEmpresa(String INSSEmpresa) {
        this.INSSEmpresa = INSSEmpresa;
    }

    /**
     * @return the IR
     */
    public String getIR() {
        return IR;
    }

    /**
     * @param IR the IR to set
     */
    public void setIR(String IR) {
        this.IR = IR;
    }

    /**
     * @return the FGTS
     */
    public String getFGTS() {
        return FGTS;
    }

    /**
     * @param FGTS the FGTS to set
     */
    public void setFGTS(String FGTS) {
        this.FGTS = FGTS;
    }

    /**
     * @return the DSR
     */
    public String getDSR() {
        return DSR;
    }

    /**
     * @param DSR the DSR to set
     */
    public void setDSR(String DSR) {
        this.DSR = DSR;
    }

    /**
     * @return the Incid13
     */
    public String getIncid13() {
        return Incid13;
    }

    /**
     * @param Incid13 the Incid13 to set
     */
    public void setIncid13(String Incid13) {
        this.Incid13 = Incid13;
    }

    /**
     * @return the IncidFer
     */
    public String getIncidFer() {
        return IncidFer;
    }

    /**
     * @param IncidFer the IncidFer to set
     */
    public void setIncidFer(String IncidFer) {
        this.IncidFer = IncidFer;
    }

    /**
     * @return the IncidFerTer
     */
    public String getIncidFerTer() {
        return IncidFerTer;
    }

    /**
     * @param IncidFerTer the IncidFerTer to set
     */
    public void setIncidFerTer(String IncidFerTer) {
        this.IncidFerTer = IncidFerTer;
    }

    /**
     * @return the IncidRAIS
     */
    public String getIncidRAIS() {
        return IncidRAIS;
    }

    /**
     * @param IncidRAIS the IncidRAIS to set
     */
    public void setIncidRAIS(String IncidRAIS) {
        this.IncidRAIS = IncidRAIS;
    }

    /**
     * @return the IncidINFREDND
     */
    public String getIncidINFREDND() {
        return IncidINFREDND;
    }

    /**
     * @param IncidINFREDND the IncidINFREDND to set
     */
    public void setIncidINFREDND(String IncidINFREDND) {
        this.IncidINFREDND = IncidINFREDND;
    }

    /**
     * @return the IncidVT
     */
    public String getIncidVT() {
        return IncidVT;
    }

    /**
     * @param IncidVT the IncidVT to set
     */
    public void setIncidVT(String IncidVT) {
        this.IncidVT = IncidVT;
    }

    /**
     * @return the IncidSalFamilia
     */
    public String getIncidSalFamilia() {
        return IncidSalFamilia;
    }

    /**
     * @param IncidSalFamilia the IncidSalFamilia to set
     */
    public void setIncidSalFamilia(String IncidSalFamilia) {
        this.IncidSalFamilia = IncidSalFamilia;
    }

    /**
     * @return the IncidPensao
     */
    public String getIncidPensao() {
        return IncidPensao;
    }

    /**
     * @param IncidPensao the IncidPensao to set
     */
    public void setIncidPensao(String IncidPensao) {
        this.IncidPensao = IncidPensao;
    }

    /**
     * @return the IncidARVVar
     */
    public String getIncidARVVar() {
        return IncidARVVar;
    }

    /**
     * @param IncidARVVar the IncidARVVar to set
     */
    public void setIncidARVVar(String IncidARVVar) {
        this.IncidARVVar = IncidARVVar;
    }

    /**
     * @return the NRed
     */
    public String getNRed() {
        return NRed;
    }

    /**
     * @param NRed the NRed to set
     */
    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    /**
     * @return the Formula
     */
    public String getFormula() {
        return Formula;
    }

    /**
     * @param Formula the Formula to set
     */
    public void setFormula(String Formula) {
        this.Formula = Formula;
    }

    /**
     * @return the Limite
     */
    public BigDecimal getLimite() {
        return Limite;
    }

    /**
     * @param Limite the Limite to set
     */
    public void setLimite(String Limite) {
        try {
            this.Limite = new BigDecimal(Limite);
        } catch (Exception e) {
            this.Limite = new BigDecimal("0");
        }
    }

    /**
     * @return the Conta_CtbDesp
     */
    public String getConta_CtbDesp() {
        return Conta_CtbDesp;
    }

    /**
     * @param Conta_CtbDesp the Conta_CtbDesp to set
     */
    public void setConta_CtbDesp(String Conta_CtbDesp) {
        this.Conta_CtbDesp = Conta_CtbDesp;
    }

    /**
     * @return the Conta_CtbCusto
     */
    public String getConta_CtbCusto() {
        return Conta_CtbCusto;
    }

    /**
     * @param Conta_CtbCusto the Conta_CtbCusto to set
     */
    public void setConta_CtbCusto(String Conta_CtbCusto) {
        this.Conta_CtbCusto = Conta_CtbCusto;
    }

    /**
     * @return the Conta_CtbAtivo
     */
    public String getConta_CtbAtivo() {
        return Conta_CtbAtivo;
    }

    /**
     * @param Conta_CtbAtivo the Conta_CtbAtivo to set
     */
    public void setConta_CtbAtivo(String Conta_CtbAtivo) {
        this.Conta_CtbAtivo = Conta_CtbAtivo;
    }

    /**
     * @return the Conta_CtbPassivo
     */
    public String getConta_CtbPassivo() {
        return Conta_CtbPassivo;
    }

    /**
     * @param Conta_CtbPassivo the Conta_CtbPassivo to set
     */
    public void setConta_CtbPassivo(String Conta_CtbPassivo) {
        this.Conta_CtbPassivo = Conta_CtbPassivo;
    }

    /**
     * @return the PlanoSaude
     */
    public String getPlanoSaude() {
        return PlanoSaude;
    }

    /**
     * @param PlanoSaude the PlanoSaude to set
     */
    public void setPlanoSaude(String PlanoSaude) {
        this.PlanoSaude = PlanoSaude;
    }

    /**
     * @return the TipoDep
     */
    public String getTipoDep() {
        return TipoDep;
    }

    /**
     * @param TipoDep the TipoDep to set
     */
    public void setTipoDep(String TipoDep) {
        this.TipoDep = TipoDep;
    }

    /**
     * @return the CodForn
     */
    public BigDecimal getCodForn() {
        return CodForn;
    }

    /**
     * @param CodForn the CodForn to set
     */
    public void setCodForn(String CodForn) {
        try {
            this.CodForn = new BigDecimal(CodForn);
        } catch (Exception e) {
            this.CodForn = new BigDecimal("0");
        }
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

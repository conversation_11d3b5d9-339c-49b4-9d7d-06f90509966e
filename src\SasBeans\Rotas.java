package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Rotas {

    private BigDecimal Sequencia;
    private String Nome;
    private String Rota;
    private String Data;
    private BigDecimal CodFil;
    private String TpVeic;
    private String Viagem;
    private String ATM;
    private String BACEN;
    private String Aeroporto;
    private String HrLargada;
    private String HrChegada;
    private String HrIntIni;
    private String HrIntFim;
    private BigDecimal HsTotal;
    private String Observacao;
    private BigDecimal KmSaida;
    private BigDecimal KmChegada;
    private BigDecimal KmTotal;
    private BigDecimal HrUrb;
    private BigDecimal HrInter;
    private BigDecimal PrdUrb;
    private BigDecimal PrdInter;
    private BigDecimal GuiasUrb;
    private BigDecimal GuiasInter;
    private BigDecimal KmTotConj;
    private BigDecimal KmTotConj2;
    private String DtFim;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private String OperFech;
    private LocalDate Dt_Fech;
    private String Hr_Fech;
    private String Flag_Excl;

    private BigDecimal valor;
    private List<Rt_Perc> trajetos;
    private BigDecimal qtdVolumes;
    private BigDecimal qtdTrajetos;

    private String Placa;
    private String Veiculo;
    private String ModeloVeiculo;
    private String NomeMotorista;

    private String recOk;
    private String recPd;
    private String entOk;
    private String entPd;

    private String recGuias;
    private String recLacres;
    private String recValor;
    private String recPdGuias;
    private String recPdLacres;
    private String recPdValor;
    private String entGuias;
    private String entLacres;
    private String entValor;
    private String entPdGuias;
    private String entPdLacres;
    private String entPdValor;

    public String getPlaca() {
        return Placa;
    }

    public void setPlaca(String Placa) {
        this.Placa = Placa;
    }

    public String getVeiculo() {
        return Veiculo;
    }

    public void setVeiculo(String Veiculo) {
        this.Veiculo = Veiculo;
    }

    public String getModeloVeiculo() {
        return ModeloVeiculo;
    }

    public void setModeloVeiculo(String ModeloVeiculo) {
        this.ModeloVeiculo = ModeloVeiculo;
    }

    public String getNomeMotorista() {
        return NomeMotorista;
    }

    public void setNomeMotorista(String NomeMotorista) {
        this.NomeMotorista = NomeMotorista;
    }

    public Rotas() {
        this.Sequencia = new BigDecimal("0");
        this.Nome = "";
        this.Data = "";
        this.Rota = "";
        this.Data = null;
        this.CodFil = new BigDecimal("0");
        //this.OS = new BigDecimal("0");
        //this.osfat = new BigDecimal("0");
        this.TpVeic = "";
        this.Viagem = "";
        this.ATM = "";
        this.BACEN = "";
        this.Aeroporto = "";
        this.HrLargada = "";
        this.HrChegada = "";
        this.HrIntIni = "";
        this.HrIntFim = "";
        this.HsTotal = new BigDecimal("0");
        this.Observacao = "";
        this.KmSaida = new BigDecimal("0");
        this.KmChegada = new BigDecimal("0");
        this.KmTotal = new BigDecimal("0");
        this.HrUrb = new BigDecimal("0");
        this.HrInter = new BigDecimal("0");
        this.PrdUrb = new BigDecimal("0");
        this.PrdInter = new BigDecimal("0");
        this.GuiasUrb = new BigDecimal("0");
        this.GuiasInter = new BigDecimal("0");
        this.KmTotConj = new BigDecimal("0");
        this.KmTotConj2 = new BigDecimal("0");
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
        this.OperFech = "";
        this.Dt_Fech = null;
        this.Hr_Fech = "";
        this.Flag_Excl = "";
    }

    public Rotas(Rotas original) {
        Sequencia = original.getSequencia();
        Nome = original.getNome();
        Rota = original.getRota();
        Data = original.getData();
        CodFil = original.getCodFil();
        TpVeic = original.getTpVeic();
        Viagem = original.getViagem();
        ATM = original.getATM();
        BACEN = original.getBACEN();
        Aeroporto = original.getAeroporto();
        HrLargada = original.getHrLargada();
        HrChegada = original.getHrChegada();
        HrIntIni = original.getHrIntIni();
        HrIntFim = original.getHrIntFim();
        HsTotal = original.getHsTotal();
        Observacao = original.getObservacao();
        KmSaida = original.getKmSaida();
        KmChegada = original.getKmChegada();
        KmTotal = original.getKmTotal();
        HrUrb = original.getHrUrb();
        HrInter = original.getHrInter();
        PrdUrb = original.getPrdUrb();
        PrdInter = original.getPrdInter();
        GuiasUrb = original.getGuiasUrb();
        GuiasInter = original.getGuiasInter();
        KmTotConj = original.getKmTotConj();
        KmTotConj2 = original.getKmTotConj2();
        DtFim = original.getDtFim();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
        OperFech = original.getOperFech();
        Dt_Fech = original.getDt_Fech();
        Hr_Fech = original.getHr_Fech();
        Flag_Excl = original.getFlag_Excl();
        valor = original.getValor();
        trajetos = original.getTrajetos();
        qtdVolumes = original.getQtdVolumes();
        qtdTrajetos = original.getQtdTrajetos();
        Placa = original.getPlaca();
        Veiculo = original.getVeiculo();
        ModeloVeiculo = original.getModeloVeiculo();
        NomeMotorista = original.getNomeMotorista();
        recOk = original.getRecOk();
        recPd = original.getRecPd();
        entOk = original.getEntOk();
        entPd = original.getEntPd();
        recGuias = original.getRecGuias();
        recLacres = original.getRecLacres();
        recValor = original.getRecValor();
        recPdGuias = original.getRecPdGuias();
        recPdLacres = original.getRecPdLacres();
        recPdValor = original.getRecPdValor();
        entGuias = original.getEntGuias();
        entLacres = original.getEntLacres();
        entValor = original.getEntValor();
        entPdGuias = original.getEntPdGuias();
        entPdLacres = original.getEntPdLacres();
        entPdValor = original.getEntPdValor();
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getTpVeic() {
        return TpVeic;
    }

    public void setTpVeic(String TpVeic) {
        this.TpVeic = TpVeic;
    }

    public String getViagem() {
        return Viagem;
    }

    public void setViagem(String Viagem) {
        this.Viagem = Viagem;
    }

    public String getATM() {
        return ATM;
    }

    public void setATM(String ATM) {
        this.ATM = ATM;
    }

    public String getBACEN() {
        return BACEN;
    }

    public void setBACEN(String BACEN) {
        this.BACEN = BACEN;
    }

    public String getAeroporto() {
        return Aeroporto;
    }

    public void setAeroporto(String Aeroporto) {
        this.Aeroporto = Aeroporto;
    }

    public String getHrLargada() {
        return HrLargada;
    }

    public void setHrLargada(String HrLargada) {
        this.HrLargada = HrLargada;
    }

    public String getHrChegada() {
        return HrChegada;
    }

    public void setHrChegada(String HrChegada) {
        this.HrChegada = HrChegada;
    }

    public String getHrIntIni() {
        return HrIntIni;
    }

    public void setHrIntIni(String HrIntIni) {
        this.HrIntIni = HrIntIni;
    }

    public String getHrIntFim() {
        return HrIntFim;
    }

    public void setHrIntFim(String HrIntFim) {
        this.HrIntFim = HrIntFim;
    }

    public BigDecimal getHsTotal() {
        return HsTotal;
    }

    public void setHsTotal(String HsTotal) {
        try {
            this.HsTotal = new BigDecimal(HsTotal);
        } catch (Exception e) {
            this.HsTotal = new BigDecimal("0");
        }
    }

    public String getObservacao() {
        return Observacao;
    }

    public void setObservacao(String Observacao) {
        this.Observacao = Observacao;
    }

    public BigDecimal getKmSaida() {
        return KmSaida;
    }

    public void setKmSaida(String KmSaida) {
        try {
            this.KmSaida = new BigDecimal(KmSaida);
        } catch (Exception e) {
            this.KmSaida = new BigDecimal("0");
        }
    }

    public BigDecimal getKmChegada() {
        return KmChegada;
    }

    public void setKmChegada(String KmChegada) {
        try {
            this.KmChegada = new BigDecimal(KmChegada);
        } catch (Exception e) {
            this.KmChegada = new BigDecimal("0");
        }
    }

    public BigDecimal getKmTotal() {
        return KmTotal;
    }

    public void setKmTotal(String KmTotal) {
        try {
            this.KmTotal = new BigDecimal(KmTotal);
        } catch (Exception e) {
            this.KmTotal = new BigDecimal("0");
        }
    }

    public BigDecimal getHrUrb() {
        return HrUrb;
    }

    public void setHrUrb(String HrUrb) {
        try {
            this.HrUrb = new BigDecimal(HrUrb);
        } catch (Exception e) {
            this.HrUrb = new BigDecimal("0");
        }
    }

    public BigDecimal getHrInter() {
        return HrInter;
    }

    public void setHrInter(String HrInter) {
        try {
            this.HrInter = new BigDecimal(HrInter);
        } catch (Exception e) {
            this.HrInter = new BigDecimal("0");
        }
    }

    public BigDecimal getPrdUrb() {
        return PrdUrb;
    }

    public void setPrdUrb(String PrdUrb) {
        try {
            this.PrdUrb = new BigDecimal(PrdUrb);
        } catch (Exception e) {
            this.PrdUrb = new BigDecimal("0");
        }
    }

    public BigDecimal getPrdInter() {
        return PrdInter;
    }

    public void setPrdInter(String PrdInter) {
        try {
            this.PrdInter = new BigDecimal(PrdInter);
        } catch (Exception e) {
            this.PrdInter = new BigDecimal("0");
        }
    }

    public BigDecimal getGuiasUrb() {
        return GuiasUrb;
    }

    public void setGuiasUrb(String GuiasUrb) {
        try {
            this.GuiasUrb = new BigDecimal(GuiasUrb);
        } catch (Exception e) {
            this.GuiasUrb = new BigDecimal("0");
        }
    }

    public BigDecimal getGuiasInter() {
        return GuiasInter;
    }

    public void setGuiasInter(String GuiasInter) {
        try {
            this.GuiasInter = new BigDecimal(GuiasInter);
        } catch (Exception e) {
            this.GuiasInter = new BigDecimal("0");
        }
    }

    public BigDecimal getKmTotConj() {
        return KmTotConj;
    }

    public void setKmTotConj(String KmTotConj) {
        try {
            this.KmTotConj = new BigDecimal(KmTotConj);
        } catch (Exception e) {
            this.KmTotConj = new BigDecimal("0");
        }
    }

    public BigDecimal getKmTotConj2() {
        return KmTotConj2;
    }

    public void setKmTotConj2(String KmTotConj2) {
        try {
            this.KmTotConj2 = new BigDecimal(KmTotConj2);
        } catch (Exception e) {
            this.KmTotConj2 = new BigDecimal("0");
        }
    }

    public String getDtFim() {
        return DtFim;
    }

    public void setDtFim(String DtFim) {
        this.DtFim = DtFim;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getOperFech() {
        return OperFech;
    }

    public void setOperFech(String OperFech) {
        this.OperFech = OperFech;
    }

    public LocalDate getDt_Fech() {
        return Dt_Fech;
    }

    public void setDt_Fech(LocalDate Dt_Fech) {
        this.Dt_Fech = Dt_Fech;
    }

    public String getHr_Fech() {
        return Hr_Fech;
    }

    public void setHr_Fech(String Hr_Fech) {
        this.Hr_Fech = Hr_Fech;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public void setValor(String valor) {
        try {
            this.valor = new BigDecimal(valor);
        } catch (Exception e) {
            this.valor = BigDecimal.ZERO;
        }
    }

    public List<Rt_Perc> getTrajetos() {
        return trajetos;
    }

    public void setTrajetos(List<Rt_Perc> trajetos) {
        this.trajetos = trajetos;
    }

    public BigDecimal getQtdVolumes() {
        return qtdVolumes;
    }

    public void setQtdVolumes(BigDecimal qtdVolumes) {
        this.qtdVolumes = qtdVolumes;
    }

    public BigDecimal getQtdTrajetos() {
        return qtdTrajetos;
    }

    public void setQtdTrajetos(BigDecimal qtdTrajetos) {
        this.qtdTrajetos = qtdTrajetos;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 83 * hash + Objects.hashCode(this.Sequencia);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Rotas other = (Rotas) obj;
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        return true;
    }

    public String getRecOk() {
        return recOk;
    }

    public void setRecOk(String recOk) {
        this.recOk = recOk;
    }

    public String getRecPd() {
        return recPd;
    }

    public void setRecPd(String recPd) {
        this.recPd = recPd;
    }

    public String getEntOk() {
        return entOk;
    }

    public void setEntOk(String entOk) {
        this.entOk = entOk;
    }

    public String getEntPd() {
        return entPd;
    }

    public void setEntPd(String entPd) {
        this.entPd = entPd;
    }

    public String getRecGuias() {
        return recGuias;
    }

    public void setRecGuias(String recGuias) {
        this.recGuias = recGuias;
    }

    public String getRecLacres() {
        return recLacres;
    }

    public void setRecLacres(String recLacres) {
        this.recLacres = recLacres;
    }

    public String getRecValor() {
        return recValor;
    }

    public void setRecValor(String recValor) {
        this.recValor = recValor;
    }

    public String getRecPdGuias() {
        return recPdGuias;
    }

    public void setRecPdGuias(String recPdGuias) {
        this.recPdGuias = recPdGuias;
    }

    public String getRecPdLacres() {
        return recPdLacres;
    }

    public void setRecPdLacres(String recPdLacres) {
        this.recPdLacres = recPdLacres;
    }

    public String getRecPdValor() {
        return recPdValor;
    }

    public void setRecPdValor(String recPdValor) {
        this.recPdValor = recPdValor;
    }

    public String getEntGuias() {
        return entGuias;
    }

    public void setEntGuias(String entGuias) {
        this.entGuias = entGuias;
    }

    public String getEntLacres() {
        return entLacres;
    }

    public void setEntLacres(String entLacres) {
        this.entLacres = entLacres;
    }

    public String getEntValor() {
        return entValor;
    }

    public void setEntValor(String entValor) {
        this.entValor = entValor;
    }

    public String getEntPdGuias() {
        return entPdGuias;
    }

    public void setEntPdGuias(String entPdGuias) {
        this.entPdGuias = entPdGuias;
    }

    public String getEntPdLacres() {
        return entPdLacres;
    }

    public void setEntPdLacres(String entPdLacres) {
        this.entPdLacres = entPdLacres;
    }

    public String getEntPdValor() {
        return entPdValor;
    }

    public void setEntPdValor(String entPdValor) {
        this.entPdValor = entPdValor;
    }
}

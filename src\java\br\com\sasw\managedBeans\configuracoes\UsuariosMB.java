/*
 */
package br.com.sasw.managedBeans.configuracoes;

import Arquivo.ArquivoLog;
import Controller.Clientes.ClientesSatMobWeb;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaPortalSrv;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.Sysdef;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.lazydatamodels.configuracoes.UsuarioLazyList;
import br.com.sasw.managedBeans.recursoshumanos.PessoasMB;
import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import br.com.sasw.utils.Logger;
import static br.com.sasw.utils.Mascaras.removeMascara;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "usuarios")
@ViewScoped
public class UsuariosMB implements Serializable {

    private final String banco;
    private final BigDecimal codPessoa;
    private String operador, sistema, subsistema, nome, grupoSelecionado, nomeFilial, caminho, log, codGrupo, nivel, nomeAux;
    private ArquivoLog logerro;
    private final AcessosSatMobWeb acessossatmobweb;
    private final ClientesSatMobWeb clientessatmobweb;
    private String codFil;
    private Persistencia persistencia, central;
    private Filiais filiais;
    private List<Saspw> lista, usuariosGrupo;
    private UsuarioSatMobWeb novo, buscaUsuario;
    private List<SASGrupos> grupos, gruposClientes;
    private SASGrupos novoGrupo, grupoEdicao;
    private List<SasPWFill> listaFiliais;
    private SasPWFill filialSelecionada, filialAdicionar, filial;
    private List<Sysdef> todasPermissoes;
    private List<PessoaCliAut> listaClientes, listaClientesBusca;
    private List<SaspwacSysdef> listaPermissoes, listaPermissoesBusca;
    private SaspwacSysdef permissaoAdicionar;
    private List<Clientes> todosClientes, clientesGrupo;
    private List<PessoaPortalSrv> servicos;
    private PessoaPortalSrv servico;
    private Clientes todosClientesSelecao, clienteGrupoSelecionado;
    private PessoaCliAut novoCliente, clienteSelecionado, clienteAdicionar;
    private Sysdef todasPermissoesSelecionada;
    private boolean alteracao, inclusao, exclusao, mostrarFiliais, somenteAtivos, limparFiltros, flag_exclPessoaCliAut, satMobEW,
            clienteAdministrador, cadastroNovaPessoa, clientesExcluidos;
    private PessoasMB pessoas;
    private Pessoa pessoa, novaPessoa;
    private List<Pessoa> listaPessoa;
    private int flag;
    private LazyDataModel<UsuarioSatMobWeb> usuarios = null;
    private Map filters, niveis;

    public UsuariosMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        codGrupo = (String) fc.getExternalContext().getSessionMap().get("codgrupo");
        nivel = (String) fc.getExternalContext().getSessionMap().get("nivel");
//        clienteAdministrador = new BigDecimal(this.codGrupo).intValue() >= 1000;
        clienteAdministrador = false;
        acessossatmobweb = new AcessosSatMobWeb();
        clientessatmobweb = new ClientesSatMobWeb();
        lista = new ArrayList<>();
        novo = new UsuarioSatMobWeb();
        listaFiliais = new ArrayList<>();
        filialSelecionada = new SasPWFill();
        todasPermissoesSelecionada = new Sysdef();
        nome = new String();
        sistema = new String();
        subsistema = new String();
        pessoa = new Pessoa();
        inclusao = false;
        exclusao = false;
        alteracao = false;
        flag = 1;
        somenteAtivos = true;
        mostrarFiliais = false;
        log = new String();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog(this.getClass().getSimpleName());
    }

    public void Persistencias(Persistencia pp, Persistencia central) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception(getMessageS("ImpossivelConectarBanco") + " " + this.banco);
            }
            this.central = central;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.clienteAdicionar = new PessoaCliAut();
            this.listaClientesBusca = new ArrayList<>();
            this.listaClientesBusca.add(this.clienteAdicionar);

            this.permissaoAdicionar = new SaspwacSysdef();
            this.listaPermissoesBusca = new ArrayList<>();
            this.listaPermissoesBusca.add(this.permissaoAdicionar);

            this.codFil = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial");

            this.niveis = gerarNiveis();
            this.grupos = this.acessossatmobweb.listarGrupos(this.persistencia);

            for (SASGrupos sasGrupos : this.grupos) {
                sasGrupos.setDescricao(getMessageS(sasGrupos.getDescricao()));
            }

            this.filiais = this.acessossatmobweb.buscaInfoFilial(this.codFil, this.persistencia);

            this.filters = new HashMap();
            this.filters.put(" s.situacao = ? ", "A");
            this.filters.put(" s.codfil = ? ", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.put(" p.nome like ? ", "");
            this.filters.put(" s.nivelx like ? ", "");
            this.filters.put(" p.codigo in (Select PessoaCliAut.Codigo"
                    + "                     from PessoaCliAut "
                    + "                     Left Join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli"
                    + "                                        and Clientes.CodFil = PessoaCliAut.CodFil"
                    + "                     where pessoacliaut.flag_excl <> '*'"
                    + "                     and Clientes.Nred like ?) ", "");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
            log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    private Map gerarNiveis() {
        Map map = new HashMap<>();
        // Se o nível não for algum nível especial, adiciona todos os niveis
        if (this.nivel.equals("1")
                || this.nivel.equals("2")
                || this.nivel.equals("3")
                || this.nivel.equals("4")
                || this.nivel.equals("8")
                || this.nivel.equals("9")) {
            map.put(Messages.getMessageS("Administrador"), "9");
            map.put(Messages.getMessageS("Operacao"), "1");
            map.put(Messages.getMessageS("Manutencao"), "2");
            map.put(Messages.getMessageS("Gerencia"), "3");
            map.put(Messages.getMessageS("PortalRH"), "4");
            map.put(Messages.getMessageS("GTV"), "5");
            map.put(Messages.getMessageS("AssinarGTV"), "7");
            map.put(Messages.getMessageS("SatMobEW"), "8");
            map.put(Messages.getMessageS("CofreInteligente"), "6");
        }

        /**
         * Se o nível do usuário for algum nível especial, adiciona somente o
         * nível à lista
         */
        // GTV
        if (this.nivel.equals("5") || this.nivel.equals("7")) {
            map.put(Messages.getMessageS("GTV"), "5");
            map.put(Messages.getMessageS("AssinarGTV"), "7");
        }

        // COFRE     
        if (this.nivel.equals("6")) {
            map.put(Messages.getMessageS("CofreInteligente"), "6");
        }

        // EW     
        if (this.nivel.equals("8")) {
            map.put(Messages.getMessageS("SatMobEW"), "8");
        }
        return map;
    }

    public void cadastrar() throws Exception {
        this.novo.getSaspw().setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.novo.getSaspw().setCodFil(this.filial.getCodfilAc());

        switch (this.novo.getSaspw().getNivelx()) {
            case "1":
                this.novo.getSaspw().setNivelOP(getMessageS("Operacao"));
                break;
            case "2":
                this.novo.getSaspw().setNivelOP(getMessageS("Manutencao"));
                break;
            case "3":
                this.novo.getSaspw().setNivelOP(getMessageS("Gerencia"));
                break;
            case "4":
                this.novo.getSaspw().setNivelOP(getMessageS("PortalRH"));
                break;
            case "5":
                this.novo.getSaspw().setNivelOP(getMessageS("GTV"));
                break;
            case "6":
                this.novo.getSaspw().setNivelOP(getMessageS("CofreInteligente"));
                break;
            case "7":
                this.novo.getSaspw().setNivelOP(getMessageS("AssinarGTV"));
                break;
            case "8":
                this.novo.getSaspw().setNivelOP(getMessageS("SatMobEW"));
                break;
            case "9":
                this.novo.getSaspw().setNivelOP(getMessageS("Administrador"));
                break;
            case "10":
                this.novo.getSaspw().setNivelOP(getMessageS("Chamados"));
                break;
            default:
                this.novo.getSaspw().setNivelOP(getMessageS(""));
                break;
        }
        try {

            if (this.cadastroNovaPessoa) {
                this.novaPessoa = new Pessoa();
                this.novaPessoa.setNome(this.novo.getPessoa().getNome().toUpperCase());
                this.novaPessoa.setEmail(this.novo.getPessoa().getEmail());
                this.novaPessoa.setPWWeb(this.novo.getPessoa().getPWWeb());
                this.novaPessoa.setCPF(removeMascara(this.novo.getPessoa().getCPF()));
                this.novaPessoa.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                this.novaPessoa.setDt_Alter(getDataAtual("SQL"));
                this.novaPessoa.setHr_Alter(getDataAtual("HORA"));
                this.novaPessoa = this.acessossatmobweb.inserirNovaPessoa(this.novaPessoa, this.persistencia, this.central);

                this.novo.setPessoa(this.novaPessoa);

                this.novo.getSaspw().setNome(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.novo.getSaspw().setCodPessoa(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.novo.getSaspw().setCodPessoaWeb(this.novo.getPessoa().getCodPessoaWEB().toPlainString());
                this.novo.getSaspw().setNomeCompleto(this.novo.getPessoa().getNome());
                this.novo.getSaspw().setCodigo(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.novo.getPessoalogin().setCodigo(this.novo.getPessoa().getCodPessoaWEB());
                this.novo.getPessoalogin().setCodPessoaBD(this.novo.getPessoa().getCodigo());
            }

            this.novo.getSaspw().setNivelOP(this.novo.getSaspw().getNivelOP().toUpperCase());
            this.novo.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.novo.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());
            this.novo.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());
            this.novo.getPessoalogin().setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.novo.getPessoalogin().setDt_alter(getDataAtual("SQL"));
            this.novo.getPessoalogin().setHr_Alter(getDataAtual("HORA"));
            this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));

            this.acessossatmobweb.criarAcesso(this.novo, this.persistencia, this.central);

            this.servico = new PessoaPortalSrv();
            this.servico.setCodigo(this.novo.getPessoa().getCodPessoaWEB().toString());
            this.servico.setServico("401");
            this.servico.setOper_Incl(RecortaAteEspaço(this.operador, 0, 10));
            this.servico.setDt_Incl(getDataAtual("SQL"));
            this.servico.setHr_Incl(getDataAtual("HORA"));
            this.servico.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.servico.setDt_Alter(getDataAtual("SQL"));
            this.servico.setHr_Alter(getDataAtual("HORA"));
            this.acessossatmobweb.inserirServicoAutomatico(this.servico, this.central);

            this.filial.setCodFil(this.novo.getSaspw().getCodFil());
            this.filial.setNome(this.novo.getPessoa().getCodigo().toBigInteger().toString());
            this.filial.setCodigo(this.novo.getPessoa().getCodigo().toBigInteger().toString());
            this.filial.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.acessossatmobweb.inserirFilial(this.filial, this.persistencia);

            for (PessoaCliAut c : this.listaClientes) {
                c.setCodigo(this.novo.getSaspw().getCodPessoa().toPlainString());
                this.acessossatmobweb.inserirCliente(c, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novo) + "\r\n"
                    + "\r\n" + Logger.listaObjetosCompostos2String(this.listaPermissoes) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.listaFiliais) + "\r\n"
                    + "\r\n" + Logger.listaObjeto2String(this.listaClientes) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editar() {
        try {
            this.novo.getSaspw().setOperador(RecortaAteEspaço(this.operador, 0, 10));
            switch (this.novo.getSaspw().getNivelx()) {
                case "1":
                    this.novo.getSaspw().setNivelOP(getMessageS("Operacao"));
                    break;
                case "2":
                    this.novo.getSaspw().setNivelOP(getMessageS("Manutencao"));
                    break;
                case "3":
                    this.novo.getSaspw().setNivelOP(getMessageS("Gerencia"));
                    break;
                case "4":
                    this.novo.getSaspw().setNivelOP(getMessageS("PortalRH"));
                    break;
                case "5":
                    this.novo.getSaspw().setNivelOP(getMessageS("GTV"));
                    break;
                case "6":
                    this.novo.getSaspw().setNivelOP(getMessageS("CofreInteligente"));
                    break;
                case "7":
                    this.novo.getSaspw().setNivelOP(getMessageS("AssinarGTV"));
                    break;
                case "8":
                    this.novo.getSaspw().setNivelOP(getMessageS("SatMobEW"));
                    break;
                case "9":
                    this.novo.getSaspw().setNivelOP(getMessageS("Administrador"));
                    break;
                case "10":
                    this.novo.getSaspw().setNivelOP(getMessageS("Chamados"));
                    break;
                default:
                    this.novo.getSaspw().setNivelOP(getMessageS(""));
                    break;
            }
            this.novo.getSaspw().setNivelOP(RecortaString(this.novo.getSaspw().getNivelOP().toUpperCase(), 0, 15));
            this.novo.getSaspw().setDescricao(this.novo.getSaspw().getDescricao().toUpperCase());
            this.novo.getSaspw().setMotivo(this.novo.getSaspw().getMotivo().toUpperCase());
            this.novo.getSaspw().setCodGrupo(Integer.valueOf(this.novo.getGrupo().getCodigo()));
            this.novo.getSaspw().setCodPessoa(this.novo.getPessoa().getCodigo().toBigInteger().toString());
            this.novo.getSaspw().setCodPessoaWeb(this.novo.getPessoa().getCodPessoaWEB().toPlainString());
            this.novo.getSaspw().setNomeCompleto(this.novo.getPessoa().getNome());
            this.novo.getSaspw().setCodigo(this.novo.getPessoa().getCodigo().toBigInteger().toString());
            this.novo.getPessoalogin().setCodigo(this.novo.getPessoa().getCodPessoaWEB());
            this.novo.getPessoalogin().setCodPessoaBD(this.novo.getPessoa().getCodigo());
            this.novo.getPessoalogin().setNivel(this.novo.getSaspw().getNivelx());

            this.acessossatmobweb.criarAcesso(this.novo, this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgEditar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoUsuario() {
        try {
            this.pessoa = new Pessoa();
            this.listaPessoa = new ArrayList<>();
            this.listaPessoa.add(this.pessoa);

            this.novaPessoa = new Pessoa();
            this.novaPessoa.setCodigo("-1");
            this.novaPessoa.setNome(getMessageS("CadastrarNovaPessoa"));

            this.cadastroNovaPessoa = false;

            this.novo = new UsuarioSatMobWeb();
            this.novo.setPessoa(new Pessoa());

            this.filialAdicionar = this.acessossatmobweb.buscarFilial(this.codFil, this.persistencia);
            this.filialAdicionar.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.filialAdicionar.setDt_Alter(getDataAtual("SQL"));
            this.filialAdicionar.setHr_Alter(getDataAtual("HORA"));

            this.listaFiliais = new ArrayList<>();
            this.listaClientes = new ArrayList<>();
            this.listaPermissoes = new ArrayList<>();

            this.listaFiliais.add(this.filialAdicionar);

//            this.filiais = this.usuariosSPM.buscaInfoFilial(this.codFil, this.persistencia);
            this.flag = 1;

            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Pessoa> listarQueryValida(String query) {
        try {
            this.nomeAux = query;
            this.listaPessoa = this.acessossatmobweb.listaPessoaQuery(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        this.listaPessoa.add(this.novaPessoa);
        return this.listaPessoa;
    }

    public void selecionarPessoa(SelectEvent event) {
        try {
            this.novo = new UsuarioSatMobWeb();
            // Buscar pessoa:
            this.pessoa = ((Pessoa) event.getObject());

            // Pessoa existe?
            if (this.pessoa.getCodigo().toBigInteger().toString().equals("-1")) {
                // Pessoa não existe.
                this.cadastroNovaPessoa = true;
                this.novo.getPessoa().setNome(this.nomeAux);
            } else {
                // Pessoa existe.
                this.cadastroNovaPessoa = false;
                this.novo.setPessoa(this.pessoa);

                // Pessoa possui codPessoaWeb?
                if (null == this.novo.getPessoa().getCodPessoaWEB()) {
                    // Pessoa não possui codPessoaWeb.
                    this.pessoa = this.acessossatmobweb.inserirCodPessoaWeb(this.pessoa, this.persistencia, this.central);
                    this.novo.setPessoa(this.pessoa);
                }

                // Pessoa possui codPessoaWeb.
                // Buscar usuário:
                this.buscaUsuario = this.acessossatmobweb.buscarUsuario(this.novo.getPessoa().getCodPessoaWEB(), this.persistencia, this.central);
                if (null != this.buscaUsuario) {
                    this.novo = this.buscaUsuario;

                    this.listaFiliais = this.acessossatmobweb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
                    this.listaClientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), true, this.persistencia);
                    this.listaPermissoes = this.acessossatmobweb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);

                    this.filial = this.acessossatmobweb.buscarFilial(this.novo.getSaspw().getCodFil(), this.persistencia);

                    this.flag = 2;
                }

                this.novo.getSaspw().setNome(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.novo.getSaspw().setCodPessoa(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.novo.getSaspw().setCodPessoaWeb(this.novo.getPessoa().getCodPessoaWEB().toPlainString());
                this.novo.getSaspw().setNomeCompleto(this.novo.getPessoa().getNome());
                this.novo.getSaspw().setCodigo(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.novo.getPessoalogin().setCodigo(this.novo.getPessoa().getCodPessoaWEB());
                this.novo.getPessoalogin().setCodPessoaBD(this.novo.getPessoa().getCodigo());

            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoCliente() {
        this.todosClientesSelecao = null;
        PrimeFaces.current().executeScript("PF('dlgAdicionarClientes').show()");
    }

    public List<PessoaCliAut> listarClientes(String query) {
        try {
            this.listaClientesBusca = this.acessossatmobweb.buscarClientesPermissao(this.listaFiliais, query,
                    this.codPessoa.toString(), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.listaClientesBusca;
    }

    public void adicionarCliente(SelectEvent event) {
        this.clienteAdicionar.setFlag_Excl("");
        this.clienteAdicionar.setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.clienteAdicionar.setDt_Alter(getDataAtual("SQL"));
        this.clienteAdicionar.setHr_Alter(getDataAtual("HORA"));

        if (this.flag == 1) {
            Boolean add = true;
            for (PessoaCliAut f : this.listaClientes) {
                if (f.getCodCli().equals(this.clienteAdicionar.getCodCli())) {
                    add = false;
                }
            }
            if (add) {
                this.listaClientes.add(this.clienteAdicionar);
            }
        } else if (this.flag == 2) {
            try {
                this.clienteAdicionar.setCodigo(this.novo.getPessoa().getCodigo().toBigInteger().toString());
                this.acessossatmobweb.inserirCliente(this.clienteAdicionar, this.persistencia);
                this.listaClientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.clientesExcluidos, this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
                this.logerro.Grava(log, caminho);
            }
        }

        this.clienteAdicionar = new PessoaCliAut();
        this.listaClientesBusca = new ArrayList<>();
        this.listaClientesBusca.add(this.clienteAdicionar);
    }

    public List<String> listarClientesPesquisa(String query) {
        List<String> retorno = new ArrayList<>();
        try {
            this.todosClientes = this.acessossatmobweb.buscarClientes(this.codPessoa, query, this.persistencia);
            for (Clientes c : this.todosClientes) {
                retorno.add(c.getNRed());
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return retorno;
    }

    public void selecionarCliente(SelectEvent event) {
        try {
            Clientes c = ((Clientes) event.getObject());
            this.todosClientesSelecao = this.clientessatmobweb.ListaClientes(c.getCodFil().toString(), c.getCodigo(), "", "", "", this.persistencia).get(0);
            this.novoCliente.setCodCli(c.getCodigo());
            this.novoCliente.setCodFil(c.getCodFil().toPlainString());
            this.novoCliente.setNomeCli(this.todosClientesSelecao.getNRed());
            PrimeFaces.current().executeScript("PF('dlgAdicionarClientes').initPosition();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarListaClientes() {
        try {
            this.listaClientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.clientesExcluidos, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirUsuario(UsuarioSatMobWeb usuario) {
        try {
            this.novo = usuario;

            this.cadastroNovaPessoa = false;

            this.listaFiliais = this.acessossatmobweb.listarFiliaisPermissao(this.novo.getSaspw().getNome(), this.persistencia);
            this.listaClientes = this.acessossatmobweb.listarClientes(this.novo.getPessoa().getCodigo(), this.clientesExcluidos, this.persistencia);
            this.listaPermissoes = this.acessossatmobweb.listarPermissoesUsuario(this.novo.getSaspw().getNome(), this.persistencia);

            this.filiais = this.acessossatmobweb.buscaInfoFilial(this.novo.getSaspw().getCodFil(), this.persistencia);
            this.flag = 2;

            PrimeFaces.current().resetInputs("formEditar:editar");
            PrimeFaces.current().executeScript("PF('dlgEditar').show()");

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<UsuarioSatMobWeb> getAllAcessos() {
        if (this.usuarios == null) {
            this.usuarios = new UsuarioLazyList(this.codPessoa.toString(), this.persistencia, this.central, this.filters);
        } else {
            ((UsuarioLazyList) this.usuarios).setFilters(this.filters);
        }
        return this.usuarios;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public List<Saspw> getLista() {
        return lista;
    }

    public void setLista(List<Saspw> lista) {
        this.lista = lista;
    }

    public UsuarioSatMobWeb getNovo() {
        return novo;
    }

    public void setNovo(UsuarioSatMobWeb novo) {
        this.novo = novo;
    }

    public List<SASGrupos> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SASGrupos> grupos) {
        this.grupos = grupos;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public SasPWFill getFilialSelecionada() {
        return filialSelecionada;
    }

    public void setFilialSelecionada(SasPWFill filialSelecionada) {
        this.filialSelecionada = filialSelecionada;
    }

    public List<Sysdef> getTodasPermissoes() {
        return todasPermissoes;
    }

    public void setTodasPermissoes(List<Sysdef> todasPermissoes) {
        this.todasPermissoes = todasPermissoes;
    }

    public Sysdef getTodasPermissoesSelecionada() {
        return todasPermissoesSelecionada;
    }

    public void setTodasPermissoesSelecionada(Sysdef todasPermissoesSelecionada) {
        this.todasPermissoesSelecionada = todasPermissoesSelecionada;
    }

    public Boolean getAlteracao() {
        return alteracao;
    }

    public void setAlteracao(Boolean alteracao) {
        this.alteracao = alteracao;
    }

    public Boolean getInclusao() {
        return inclusao;
    }

    public void setInclusao(Boolean inclusao) {
        this.inclusao = inclusao;
    }

    public Boolean getExclusao() {
        return exclusao;
    }

    public void setExclusao(Boolean exclusao) {
        this.exclusao = exclusao;
    }

    public PessoasMB getPessoas() {
        return pessoas;
    }

    public void setPessoas(PessoasMB pessoas) {
        this.pessoas = pessoas;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getSistema() {
        return sistema;
    }

    public void setSistema(String sistema) {
        this.sistema = sistema;
    }

    public String getSubsistema() {
        return subsistema;
    }

    public void setSubsistema(String subsistema) {
        this.subsistema = subsistema;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public Pessoa getPessoa() {
        return this.pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Boolean getMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(Boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }

    public Boolean getSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(Boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public String getGrupoSelecionado() {
        return grupoSelecionado;
    }

    public void setGrupoSelecionado(String grupoSelecionado) {
        this.grupoSelecionado = grupoSelecionado;
    }

    public List<Clientes> getTodosClientes() {
        return todosClientes;
    }

    public void setTodosClientes(List<Clientes> todosClientes) {
        this.todosClientes = todosClientes;
    }

    public PessoaCliAut getNovoCliente() {
        return novoCliente;
    }

    public void setNovoCliente(PessoaCliAut novoCliente) {
        this.novoCliente = novoCliente;
    }

    public PessoaCliAut getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(PessoaCliAut clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public Clientes getTodosClientesSelecao() {
        return todosClientesSelecao;
    }

    public void setTodosClientesSelecao(Clientes todosClientesSelecao) {
        this.todosClientesSelecao = todosClientesSelecao;
    }

    public Boolean getFlag_exclPessoaCliAut() {
        return flag_exclPessoaCliAut;
    }

    public void setFlag_exclPessoaCliAut(Boolean flag_exclPessoaCliAut) {
        this.flag_exclPessoaCliAut = flag_exclPessoaCliAut;
    }

    public Boolean getSatMobEW() {
        return satMobEW;
    }

    public void setSatMobEW(Boolean satMobEW) {
        this.satMobEW = satMobEW;
    }

    public Map getNiveis() {
        return niveis;
    }

    public void setNiveis(Map niveis) {
        this.niveis = niveis;
    }

    public Boolean getClienteAdministrador() {
        return clienteAdministrador;
    }

    public void setClienteAdministrador(Boolean clienteAdministrador) {
        this.clienteAdministrador = clienteAdministrador;
    }

    public void gerarLog(String texto) {
        this.logerro.GravaMetodos(texto, this.caminho);
    }

    public List<Saspw> getUsuariosGrupo() {
        return usuariosGrupo;
    }

    public void setUsuariosGrupo(List<Saspw> usuariosGrupo) {
        this.usuariosGrupo = usuariosGrupo;
    }

    public List<SASGrupos> getGruposClientes() {
        return gruposClientes;
    }

    public void setGruposClientes(List<SASGrupos> gruposClientes) {
        this.gruposClientes = gruposClientes;
    }

    public SASGrupos getNovoGrupo() {
        return novoGrupo;
    }

    public void setNovoGrupo(SASGrupos novoGrupo) {
        this.novoGrupo = novoGrupo;
    }

    public SASGrupos getGrupoEdicao() {
        return grupoEdicao;
    }

    public void setGrupoEdicao(SASGrupos grupoEdicao) {
        this.grupoEdicao = grupoEdicao;
    }

    public List<Clientes> getClientesGrupo() {
        return clientesGrupo;
    }

    public void setClientesGrupo(List<Clientes> clientesGrupo) {
        this.clientesGrupo = clientesGrupo;
    }

    public Clientes getClienteGrupoSelecionado() {
        return clienteGrupoSelecionado;
    }

    public void setClienteGrupoSelecionado(Clientes clienteGrupoSelecionado) {
        this.clienteGrupoSelecionado = clienteGrupoSelecionado;
    }

    public List<PessoaPortalSrv> getServicos() {
        return servicos;
    }

    public void setServicos(List<PessoaPortalSrv> servicos) {
        this.servicos = servicos;
    }

    public PessoaPortalSrv getServico() {
        return servico;
    }

    public void setServico(PessoaPortalSrv servico) {
        this.servico = servico;
    }

    public boolean isCadastroNovaPessoa() {
        return cadastroNovaPessoa;
    }

    public void setCadastroNovaPessoa(boolean cadastroNovaPessoa) {
        this.cadastroNovaPessoa = cadastroNovaPessoa;
    }

    public SasPWFill getFilialAdicionar() {
        return filialAdicionar;
    }

    public void setFilialAdicionar(SasPWFill filialAdicionar) {
        this.filialAdicionar = filialAdicionar;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public List<SasPWFill> getListaFiliais() {
        return listaFiliais;
    }

    public void setListaFiliais(List<SasPWFill> listaFiliais) {
        this.listaFiliais = listaFiliais;
    }

    public List<PessoaCliAut> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<PessoaCliAut> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<PessoaCliAut> getListaClientesBusca() {
        return listaClientesBusca;
    }

    public void setListaClientesBusca(List<PessoaCliAut> listaClientesBusca) {
        this.listaClientesBusca = listaClientesBusca;
    }

    public List<SaspwacSysdef> getListaPermissoes() {
        return listaPermissoes;
    }

    public void setListaPermissoes(List<SaspwacSysdef> listaPermissoes) {
        this.listaPermissoes = listaPermissoes;
    }

    public List<SaspwacSysdef> getListaPermissoesBusca() {
        return listaPermissoesBusca;
    }

    public void setListaPermissoesBusca(List<SaspwacSysdef> listaPermissoesBusca) {
        this.listaPermissoesBusca = listaPermissoesBusca;
    }

    public List<Pessoa> getListaPessoa() {
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }

    public PessoaCliAut getClienteAdicionar() {
        return clienteAdicionar;
    }

    public void setClienteAdicionar(PessoaCliAut clienteAdicionar) {
        this.clienteAdicionar = clienteAdicionar;
    }

    public boolean isClientesExcluidos() {
        return clientesExcluidos;
    }

    public void setClientesExcluidos(boolean clientesExcluidos) {
        this.clientesExcluidos = clientesExcluidos;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css"  href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .ui-inputtext{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="range_input"]{
                    min-width: 220px !Important;
                }

                .FundoPagina{
                    height: calc(100% - 8px) !important;
                }
                .DataGrid[id*="tabela"] thead tr th,
                .DataGrid[id*="tabela"] tbody tr td{
                    text-align: center !important;
                    padding-left: 6px !important; 
                    padding-right: 6px !important;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;   
                }

                .DataGrid[id*="tabela"] thead tr th,
                .DataGrid[id*="tabela"] tbody tr td{
                    min-width: 150px !important;
                    width: 150px !important;
                    max-width: 150px !important;
                }

                .DataGrid[id*="tabela"] thead tr th:nth-child(5),
                .DataGrid[id*="tabela"] tbody tr td:nth-child(5),
                .DataGrid[id*="tabela"] thead tr th:nth-child(7),
                .DataGrid[id*="tabela"] tbody tr td:nth-child(7),
                .DataGrid[id*="tabela"] thead tr th:nth-child(10),
                .DataGrid[id*="tabela"] tbody tr td:nth-child(10),
                .DataGrid[id*="tabela"] thead tr th:last-child,
                .DataGrid[id*="tabela"] tbody tr td:last-child{
                    min-width: 300px !important;   
                    width: 300px !important;   
                    max-width: 300px !important;   
                }

                .DataGrid[id*="tabela"] thead tr th:nth-child(1),
                .DataGrid[id*="tabela"] tbody tr td:nth-child(1),
                .DataGrid[id*="tabela"] thead tr th:nth-child(2),
                .DataGrid[id*="tabela"] tbody tr td:nth-child(2),
                .DataGrid[id*="tabela"] thead tr th:nth-child(8),
                .DataGrid[id*="tabela"] tbody tr td:nth-child(8){
                    min-width: 80px !important;   
                    width: 80px !important; 
                    max-width: 80px !important; 
                }
                
                @media only screen and (max-width: 764px) and (min-width :0px){
                    #divCalendario{
                        position: absolute !important;
                        top: 50px !important;
                    }

                    [id*="range_input"]{
                        min-width: 180px !Important;
                    }
                    
                    .FundoPagina{
                        height: calc(100% - 88px) !important;
                    }
                }
            </style>
        </h:head>
        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{operacoesServicos.Persistencia(login.pp)}" />
            </f:metadata>

            <p:growl id="msgs" />

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_satmob_operacoesdeservico.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.MovimentacaoOperacional}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText id="descricaoDataDe" value="#{operacoesServicos.data1}" converter="conversorData" />
                                            -
                                            <h:outputText id="descricaoDataAte" value="#{operacoesServicos.data2}" converter="conversorData" />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">
                                        #{operacoesServicos.nomeFilial}
                                        <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label>
                                    </label>
                                    <label class="FilialEndereco">#{operacoesServicos.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{operacoesServicos.filiais.bairro}, #{operacoesServicos.filiais.cidade}/#{operacoesServicos.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-12 col-xs-12">
                                    <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                  value="#{operacoesServicos.datasSelecionadas}"
                                                  monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax event="dateSelect" listener="#{operacoesServicos.selecionarDatas}" update="msgs main descricaoDataDe descricaoDataAte corporativo" />
                                    </p:datePicker>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Centro-->
                <h:form id="main">
                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:dataTable id="tabela" 
                                     value="#{operacoesServicos.listaOperacoesServicos}" 
                                     selection="#{operacoesServicos.operacoesServicosSelecionado}" 
                                     lazy="false"
                                     paginator="false"
                                     var="lista" 
                                     styleClass="tabela DataGrid" 
                                     selectionMode="single" 
                                     emptyMessage="#{localemsgs.SemRegistros}"
                                     rowKey="#{lista.numero}-#{lista.codFil}"
                                     class="tabela DataGrid" 
                                     scrollable="true"
                                     scrollHeight="100%"
                                     style="background: white"
                                     rowStyleClass="#{lista.flag_Excl eq '*' ? 'ciano'
                                                      : null}">
                            <p:column headerText="#{localemsgs.Numero}" >
                                <h:outputText value="#{lista.numero}"  converter="conversor0" />
                            </p:column>
                            <p:column headerText="#{localemsgs.CodFil}" >
                                <h:outputText value="#{lista.codFil}"  converter="conversor0" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}" >
                                <h:outputText value="#{lista.data}"  converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Ocorrencia}" >
                                <h:outputText value="#{lista.motivo_Subs}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Local}" >
                                <h:outputText value="#{lista.localPosto}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.FuncionarioEscalado}" >
                                <h:outputText value="#{lista.funcSubs}" converter="conversor0"   />
                            </p:column>
                            <p:column headerText="#{localemsgs.NomeSubs}" >
                                <h:outputText value="#{lista.nomeSubs}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Motivo}" >
                                <h:outputText value="#{lista.motivo_Aus}"  converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.FuncionarioAusente}" >
                                <h:outputText value="#{lista.funcAus}"  converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.NomeAusente}" >
                                <h:outputText value="#{lista.nomeAus}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.IndicadorHorasExtras}" >
                                <h:outputText value="#{lista.hora_Extra}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.HorasDiurnas}" >
                                <h:outputText value="#{lista.HEDiurna}" converter="conversormoedasemsimbolo"  />
                            </p:column>
                            <p:column headerText="#{localemsgs.HorasNoturnas}" >
                                <h:outputText value="#{lista.HENoturna}" converter="conversormoedasemsimbolo" />
                            </p:column>
                            <p:column headerText="#{localemsgs.NumeroHE}" >
                                <h:outputText value="#{lista.nro_HE}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Refeicoes}" >
                                <h:outputText value="#{lista.VR}" converter="conversormoeda" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Transporte}" >
                                <h:outputText value="#{lista.VT}" converter="conversormoeda" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hospedagem}" >
                                <h:outputText value="#{lista.hospedagem}" converter="conversormoeda" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Mesario}" >
                                <h:outputText value="#{lista.mesario}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.CodigoServico}" >
                                <h:outputText value="#{lista.codSrv}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Pedido}" >
                                <h:outputText value="#{lista.pedido}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Regional}" >
                                <h:outputText value="#{lista.regional}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.DescricaoRegiao}" >
                                <h:outputText value="#{lista.descRegiao}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Operador}" >
                                <h:outputText value="#{lista.operador}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}" >
                                <h:outputText value="#{lista.dt_Alter}"  converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}" >
                                <h:outputText value="#{lista.hr_Alter}"  converter="conversorHora" />
                            </p:column>
                            <p:column headerText="#{localemsgs.OBS}" >
                                <h:outputText value="#{lista.OBS}" />
                            </p:column>
                            <p:ajax event="rowDblselect" listener="#{operacoesServicos.preEdicao}" update="formCadastrar msgs" />
                        </p:dataTable>
                    </p:panel>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 30px !important; background: transparent; height:180px !important;" id="botoes">
                        <p:remoteCommand name="rcExcluir" partialSubmit="true" 
                                         process="@this" 
                                         update="msgs tabela" 
                                         actionListener="#{operacoesServicos.excluir}" />     
                        
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Novo}"
                                           actionListener="#{operacoesServicos.preCadastro}"
                                           update="formCadastrar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}"
                                           update="formCadastrar msgs"
                                           actionListener="#{operacoesServicos.preEdicao}">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px;">
                            <p:commandLink title="#{localemsgs.Excluir}"
                                           update="msgs"
                                           actionListener="#{operacoesServicos.preExcluir}">
                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                    <script type="text/javascript">
                        // <![CDATA[
                        function ConfirmarExclusao(Numero) {
                            $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}',
                                    '#{localemsgs.ConfirmaExclusao}',
                                    '#{localemsgs.Sim}',
                                    '#{localemsgs.Nao}',
                                    function () {
                                        rcExcluir();
                                    },
                                    null);
                        }
                        // ]]>
                    </script>
                </h:form>

                <h:form id="formCadastrar">
                    <p:dialog widgetVar="dlgCadastrar"  positionType="absolute" responsive="true" draggable="false"
                              styleClass="dialogo" modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="true" class="dialogoPagina" style="padding-bottom: 0px !important">

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_operacoesdeservico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.MovimentacaoOperacional}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important; margin-top:0px !important; z-index:999 !important; height:calc(100% - 20px) !important" class="cadastrar">
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="data" value="#{localemsgs.Data}">
                                </p:outputLabel>
                                <p:datePicker id="data" value="#{operacoesServicos.operacoesServicos.data}"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario2" showIcon="true"
                                              required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                              monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                              style="width: 100%" converter="conversorData" >
                                </p:datePicker>   

                            </div>
                            
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="ocorrencia" value="#{localemsgs.Ocorrencia}">
                                </p:outputLabel>
                                <p:selectOneMenu id="ocorrencia" value="#{operacoesServicos.operacoesServicos.ocorrencia}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Ocorrencia}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" />
                                    <f:selectItem itemValue="X" itemLabel="#{localemsgs.HoraExtra}"/>
                                    <f:selectItem itemValue="R" itemLabel="#{localemsgs.ReforcoNormal}"/>
                                    <f:selectItem itemValue="O" itemLabel="#{localemsgs.ReforcoObras}"/>
                                    <f:selectItem itemValue="E" itemLabel="#{localemsgs.ReforcoEspecial}"/>
                                    <f:selectItem itemValue="C" itemLabel="#{localemsgs.Cobertura}"/>
                                    <f:selectItem itemValue="M" itemLabel="#{localemsgs.ComplementoReforco}"/>
                                    <f:selectItem itemValue="N" itemLabel="#{localemsgs.NaoTemEfetivo}"/>

                                    <f:selectItem itemValue="A" itemLabel="#{localemsgs.Ausencia}"/>
                                    <f:selectItem itemValue="P" itemLabel="#{localemsgs.PlantaoReserva}"/>
                                    <f:selectItem itemValue="S" itemLabel="#{localemsgs.Cortesia}"/>
                                    <f:selectItem itemValue="1" itemLabel="#{localemsgs.AusenciaParcial}"/>

                                    <p:ajax event="change" listener="#{operacoesServicos.limparSelecaoPostoFuncionario}" update="cadastrar"></p:ajax>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="periodo" value="#{localemsgs.Periodo}">
                                </p:outputLabel>
                                <p:selectOneMenu id="periodo" value="#{operacoesServicos.operacoesServicos.periodo}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Periodo}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" />
                                    <f:selectItem itemValue="D" itemLabel="#{localemsgs.Diurno}"/>
                                    <f:selectItem itemValue="N" itemLabel="#{localemsgs.Noturno}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="motivo" value="#{localemsgs.Motivo}">
                                </p:outputLabel>
                                <p:selectOneMenu id="motivo" value="#{operacoesServicos.operacoesServicos.motivo_Aus}"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Motivo}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" />
                                    <f:selectItem itemValue="1" itemLabel="#{localemsgs.FaltaJustificada}"/>
                                    <f:selectItem itemValue="2" itemLabel="#{localemsgs.FaltaInjustificada}"/>
                                    <f:selectItem itemValue="3" itemLabel="#{localemsgs.Folga}"/>
                                    <f:selectItem itemValue="4" itemLabel="#{localemsgs.Ferias}"/>
                                    <f:selectItem itemValue="5" itemLabel="#{localemsgs.Administrativa}"/>
                                    <f:selectItem itemValue="6" itemLabel="#{localemsgs.Suspensao}"/>
                                    <f:selectItem itemValue="7" itemLabel="#{localemsgs.Reciclagem}"/>
                                    <f:selectItem itemValue="8" itemLabel="#{localemsgs.AlmocoEfetivo}"/>
                                    <f:selectItem itemValue="9" itemLabel="#{localemsgs.Escolta}"/>
                                    <f:selectItem itemValue="T" itemLabel="#{localemsgs.TrocaPlantao}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne 'C' and operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'none':''}">
                                <p:outputLabel for="func_ausente" value="#{localemsgs.FuncionarioAusente}">
                                </p:outputLabel>
                                <p:selectOneMenu id="func_ausente" value="#{operacoesServicos.operacoesServicos.funcAus}" converter="omnifaces.SelectItemsConverter"
                                                 styleClass="filial"
                                                 required="true" 
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.FuncionarioAusente}"
                                                 filter="true" filterMatchMode="contains"
                                                 rendered="#{!(operacoesServicos.operacoesServicos.ocorrencia ne 'C' and operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A')}"
                                                 style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                    <f:selectItems value="#{operacoesServicos.funcionarios}" var="funcionarios" itemValue="#{funcionarios.matr.toString()}"
                                                   itemLabel="#{funcionarios.matr} - #{funcionarios.nome}" noSelectionValue=""/>
                                    
                                    <p:ajax event="change" listener="#{operacoesServicos.selecionarPostoFuncionarioAusente}" update="pstServ"></p:ajax>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <p:outputLabel for="func_escalado" value="#{localemsgs.FuncionarioEscalado}"></p:outputLabel>
                                <p:selectOneMenu id="func_escalado" value="#{operacoesServicos.operacoesServicos.funcSubs}" converter="omnifaces.SelectItemsConverter"
                                                 styleClass="filial"
                                                 required="true" 
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.FuncionarioEscalado}"
                                                 rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                    <f:selectItems value="#{operacoesServicos.funcionarios}" var="funcionarios2" itemValue="#{funcionarios2.matr.toString()}"
                                                   itemLabel="#{funcionarios2.matr} - #{funcionarios2.nome}" noSelectionValue=""/>
                                    
                                    <p:ajax event="change" listener="#{operacoesServicos.selecionarPostoFuncionarioEscalado}" update="pstServ"></p:ajax>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-#{operacoesServicos.operacoesServicos.ocorrencia ne 'C'?'6':'12'} col-sm-#{operacoesServicos.operacoesServicos.ocorrencia ne 'C'?'6':'12'} col-xs-12">
                                <p:outputLabel for="pstServ" value="#{localemsgs.PstServ}">
                                </p:outputLabel>
                                <p:selectOneMenu id="pstServ" value="#{operacoesServicos.operacoesServicos.posto}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" 
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PstServ}"
                                                 filter="true" filterMatchMode="contains"
                                                 style="width: 100%">
                                    <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Selecione}" />
                                    <f:selectItems value="#{operacoesServicos.listaPostos}" var="pstserv" itemValue="#{pstserv.secao}"
                                                   itemLabel="#{pstserv.secao}, #{pstserv.local}: #{pstserv.descContrato}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <p:outputLabel for="indicador_horas_extras" value="#{localemsgs.IndicadorHorasExtras}"></p:outputLabel>
                                <p:selectOneMenu id="indicador_horas_extras" value="#{operacoesServicos.operacoesServicos.hora_Extra}"
                                                 rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" />
                                    <f:selectItem itemValue="S" itemLabel="#{localemsgs.Sim}"/>
                                    <f:selectItem itemValue="N" itemLabel="#{localemsgs.Nao}"/>
                                    <f:selectItem itemValue="R" itemLabel="#{localemsgs.CoberturaReserva}"/>
                                    <f:selectItem itemValue="C" itemLabel="#{localemsgs.Compensar}"/>
                                    <f:selectItem itemValue="B" itemLabel="#{localemsgs.BancoHoras}"/>
                                    <f:selectItem itemValue="T" itemLabel="#{localemsgs.PgTrocaPlantao}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <p:outputLabel for="horas_diurnas" value="#{localemsgs.HorasDiurnas}"></p:outputLabel>
                                <p:inputNumber id="horas_diurnas" value="#{operacoesServicos.operacoesServicos.HEDiurna}"
                                               rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                               style="width: 100%" decimalPlaces="2">
                                </p:inputNumber>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <p:outputLabel for="horas_noturnas" value="#{localemsgs.HorasNoturnas}"></p:outputLabel>
                                <p:inputNumber id="horas_noturnas" value="#{operacoesServicos.operacoesServicos.HENoturna}"
                                               rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                               style="width: 100%" decimalPlaces="2">
                                </p:inputNumber>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:7px 5px 3px 5px !important; background-color:#FFF; border: thin solid #DDD; border-radius: 3px; margin-top:21px; text-align: center !important">
                                    <p:selectBooleanCheckbox id="refTio2" style="float: right; margin-right: 6px !important;" value="#{operacoesServicos.operacoesServicos.pagarTipo2}" rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}">

                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel for="refTio2" value="#{localemsgs.PagarHorasTipo2}" style="font-size:8pt !important" />
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <p:outputLabel for="total_horas" value="#{localemsgs.TotaHorasDecimais}">
                                </p:outputLabel>
                                <p:inputNumber id="total_horas" value="#{operacoesServicos.operacoesServicos.totalHorasDecimais}"
                                               rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                               style="width: 100%; background-color: whitesmoke !important; background: whitesmoke !important;" decimalPlaces="0" disabled="disabled" readonly="false">
                                </p:inputNumber>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                <p:outputLabel for="refeicoes" value="#{localemsgs.RefeicoesSimbolo}">
                                </p:outputLabel>
                                <p:inputNumber id="refeicoes" value="#{operacoesServicos.operacoesServicos.VR}"
                                               rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                               style="width: 100%" decimalPlaces="2">
                                </p:inputNumber>
                            </div>
                            
                                <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                    <p:outputLabel for="transporte" value="#{localemsgs.TransporteSimbolo}">
                                    </p:outputLabel>
                                    <p:inputNumber id="transporte" value="#{operacoesServicos.operacoesServicos.VT}"
                                                   rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                   style="width: 100%" decimalPlaces="2">
                                    </p:inputNumber>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                    <p:outputLabel for="hospedagem" value="#{localemsgs.HospedagemSimbolo}">
                                    </p:outputLabel>
                                    <p:inputNumber id="hospedagem" value="#{operacoesServicos.operacoesServicos.hospedagem}"
                                                   rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                   style="width: 100%" decimalPlaces="2">
                                    </p:inputNumber>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding-left: 0px !important; padding-right: 0px !important;display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <p:outputLabel for="horario_de" value="#{localemsgs.Horario}">
                                        </p:outputLabel>
                                    </div>    
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <p:selectOneMenu id="horario_de" value="#{operacoesServicos.operacoesServicos.hora1}" editable="true" 
                                                         converter="conversorHora" validator="ValidadorHora"
                                                         rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%">
                                            <f:selectItems value="#{horas.obterHorario()}" />
                                        </p:selectOneMenu>   
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                        <p:selectOneMenu id="horario_ate" value="#{operacoesServicos.operacoesServicos.hora4}" editable="true" 
                                                         converter="conversorHora" validator="ValidadorHora"
                                                         rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%">
                                            <f:selectItems value="#{horas.obterHorario()}" />
                                        </p:selectOneMenu>     
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                    <p:outputLabel for="pedido" value="#{localemsgs.Pedido}">
                                    </p:outputLabel>
                                    <p:inputText id="pedido" value="#{operacoesServicos.operacoesServicos.pedido}" style="width: 100%" maxlength="15" rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="display: #{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'?'':'none'}">
                                    <p:outputLabel for="intrajornada" value="#{localemsgs.Intrajornada}">
                                    </p:outputLabel>
                                    <p:inputNumber id="intrajornada" value="#{operacoesServicos.operacoesServicos.intraj}"
                                                   rendered="#{operacoesServicos.operacoesServicos.ocorrencia ne '1' and operacoesServicos.operacoesServicos.ocorrencia ne 'A'}"
                                                   style="width: 100%">
                                    </p:inputNumber>
                                </div>
                            
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <p:outputLabel for="observacao" value="#{localemsgs.OBS}">
                                </p:outputLabel>
                                <p:inputTextarea id="observacao" value="#{operacoesServicos.operacoesServicos.OBS}" style="width: 100%" rows="4" />
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; padding-top: 8px !important;">
                                <p:commandLink title="#{localemsgs.Salve}" id="cadastrarDieta" action="#{operacoesServicos.gravarMovimentacao}"
                                               update="main msgs"
                                               style="width:100%">
                                    <label class="btn btn-lg btn-primary"><i class="fa fa-save"></i>&nbsp;#{localemsgs.Salve}</label>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <!--Rodapé-->
            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div style="margin-top: 2px">
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{operacoesServicos.mostrarFiliais}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{operacoesServicos.carregarGrideOperacoesServicos}" />
                                </p:selectBooleanCheckbox>
                                <label style="font-size: 9pt !important; text-align: center !important; display: block; color: #FFF !important; border: thin solid #FFF; font-weight: 500 !important; margin-top: 2px; padding: 4px 6px 4px 6px !important; background-color: rgba(255,255,255,0); border-radius: 3px;">#{operacoesServicos.listaOperacoesServicos.size()} #{localemsgs.Registros}</label>
                            </div>
                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.ExibirExcluidos}: " /></label>
                                <p:selectBooleanCheckbox value="#{operacoesServicos.exibirExcluidos}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{operacoesServicos.exibirExcluidos}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

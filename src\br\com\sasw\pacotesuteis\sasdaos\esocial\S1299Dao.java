/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1299;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1299Dao {

    public List<S1299> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select "
                    + " '1' ideEvento_indApuracao, "
                    + " '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) ideEvento_perApur, "
                    + " Filiais.TipoPessoa ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,   "
                    + " Pessoa.Nome ideRespInf_nmResp, "
                    + " Pessoa.CPF ideRespInf_cpfResp, "
                    + " Pessoa.Fone1 ideRespInf_telefone, "
                    + " DIRF.emailRInf ideRespInf_email, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1200','S-2299','S-2399')  "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and (Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%')"
                    + "         and XML_Envio like '%<infoPerApur>%') infoFech_evtRemun, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1210')  "
                    + "     and Substring(XML_Envio,CHARINDEX('<perApur>',XML_Envio)+09,(CHARINDEX('</perApur>',XML_Envio)-(CHARINDEX('<perApur>',XML_Envio)+09))) = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and (Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%' or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%')  ) infoFech_evtPgtos, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1250')  "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%') infoFech_evtAqProd, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1260')  "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%') infoFech_evtComProd, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1270')  "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%') infoFech_evtContratAvNP, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1280')  "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and ((Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%') or (Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%'))) infoFech_evtInfoComplPer, "
                    + " (Select  "
                    + "   Case when Count(*) > 0 then '' "
                    + "        else (Select top 1 x.Compet "
                    + "               From XmlEsocial x "
                    + "               where x.Compet < '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "       	   and x.CodFil = Filiais.CodFil "
                    + "                 and x.Evento = 'S-1210' "
                    + "                 and x.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%') end  "
                    + "   from XmlEsocial  "
                    + "   where XmlEsocial.Evento in ('S-1200','S-2299','S-2399', 'S-1210','S-1250','S-1260','S-1270','S-1280')  "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " 	and XmlEsocial.CodFil = Filiais.CodFil "
                    + " 	and ((Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%') or (Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%'))) infoFech_evtInfoComplPer, "
                    + " (select max(sucesso) from  (  "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2)  "
                    + "             and z.evento = 'S-1299'  "
                    + "                and z.CodFil = ?\n"
                    + "                and z.Compet = ?\n"
                    + "                and z.Ambiente = ?\n"
                    + "             and (z.Xml_Retorno like '%aguardando%'  "
                    + "                     or z.Xml_Retorno = '' "
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2)  "
                    + "             and z.evento = 'S-1299'  "
                    + "                and z.CodFil = ?\n"
                    + "                and z.Compet = ?\n"
                    + "                and z.Ambiente = ?\n"
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%'  "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') )  "
                    + " union  "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso  "
                    + "         From XmleSocial z   "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2)  "
                    + "             and z.evento = 'S-1299'  "
                    + "                and z.CodFil = ?\n"
                    + "                and z.Compet = ?\n"
                    + "                and z.Ambiente = ?\n"
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso                 "
                    + " From FPPeriodos  "
                    + " Left join FPMensal  on FPPeriodos.CodMovFP = FPMEnsal.CodMovFP "
                    + " Left join Filiais  on Filiais.CodFil = FPMensal.CodFil "
                    + " Left join Dirf  on Dirf.CodFil = FPMensal.CodFil "
                    + " Left join Pessoa on Pessoa.Codigo = DIRF.CodPessoaRIn ";
            if (compet.contains("-13")) {
                //sql = sql + " where FPPeriodos.CodMovFP = "+compet.substring(0,4);
                sql = sql + " where FPPeriodos.CodMovFP = 2212";
            } else {
                sql = sql + " where Convert(Varchar,FPPeriodos.CodMovFP) = REPLACE(RIGHT(?,5),'-','') ";
            }

            sql = sql + "   and FPMensal.CodFil = ? "
                    + " Group by FPPeriodos.CodMovFP, Filiais.TipoPessoa,Filiais.CNPJ,Pessoa.Nome,Pessoa.CPF,Pessoa.Fone1,DIRF.emailRInf, Filiais.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            if (!compet.contains("-13")) {
                consulta.setString(compet);
            }
            consulta.setString(codFil);
            consulta.select();
            List<S1299> retorno = new ArrayList<>();
            S1299 s1299;
            while (consulta.Proximo()) {
                s1299 = new S1299();
                if (compet.contains("-13")) {
                    s1299.setIdeEvento_indApuracao("2");
                    s1299.setIdeEvento_perApur(consulta.getString("ideEvento_perApur").substring(0, 4));
                } else {
                    s1299.setIdeEvento_indApuracao(consulta.getString("ideEvento_indApuracao"));
                    s1299.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));
                }

                s1299.setIdeEvento_tpAmb(ambiente);
                s1299.setIdeEvento_procEmi("1");
                s1299.setIdeEvento_verProc("Satellite eSocial");

                s1299.setSucesso(consulta.getString("sucesso"));

                s1299.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1299.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));

                s1299.setIdeRespInf_nmResp(consulta.getString("ideRespInf_nmResp"));
                s1299.setIdeRespInf_cpfResp(consulta.getString("ideRespInf_cpfResp"));
                s1299.setIdeRespInf_telefone(consulta.getString("ideRespInf_telefone"));
                s1299.setIdeRespInf_email(consulta.getString("ideRespInf_email"));

                s1299.setInfoFech_evtRemun(consulta.getString("infoFech_evtRemun"));
                s1299.setInfoFech_evtPgtos(consulta.getString("infoFech_evtPgtos"));
                s1299.setInfoFech_evtAqProd(consulta.getString("infoFech_evtAqProd"));
                //s1299.setInfoFech_evtComProd(consulta.getString("infoFech_evtComProd"));
                s1299.setInfoFech_evtComProd("N");
                s1299.setInfoFech_evtContratAvNP(consulta.getString("infoFech_evtContratAvNP"));
                s1299.setInfoFech_evtInfoComplPer(consulta.getString("infoFech_evtInfoComplPer"));
//                s1299.setInfoFech_compSemMovto(consulta.getString("infoFech_compSemMovto"));

                retorno.add(s1299);
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("S1299Dao.get - " + e.getMessage() + "\r\n");
        }
    }
}

package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.Rh_Ctrl;
import SasBeans.Sindicatos;
import SasBeansCompostas.GeraPdfFolhaPonto;

/**
 *
 * <AUTHOR>
 */
public class GeraPdfFolhaPontoDao {

    String sql;

    /**
     * Retorna dados da função
     *
     * @param sMatricula - código da matrícula
     * @param sDt_Ini - Data início
     * @param sDt_Fim - Data fim
     * @param sCodFil - Código filial
     * @param persistencia - conexão com o banco
     * @return - retorna dados da função
     * @throws java.lang.Exception - pode gerar exception
     */
    public GeraPdfFolhaPonto getDadosFunc(String sMatricula, String sDt_Ini, String sDt_Fim, String sCodFil, Persistencia persistencia) throws Exception {
        GeraPdfFolhaPonto oGeraPdfFolhaPonto = null;
        sql = "Select top 1 "
                + "Rh_Ctrl.Matr, Convert(VarChar, Dt_Ini, 103) as Dt_Ini, Convert(VarChar, Dt_Fim, 103) as Dt_Fim, "
                + "Funcion.Nome, PstServ.Local LocalPosto, Funcion.CTPS_Nro, PstServ.Secao, Funcion.CTPS_Serie, "
                + "Sindicatos.MsgExtrato, Sindicatos.MsgExtrato2, Cargos.Descricao DescCargo, "
                + "Filiais.Descricao as DescFil, Filiais.CodFil, Filiais.RazaoSocial, Filiais.Endereco, Filiais.Bairro, "
                + "Filiais.Cidade, Filiais.Cep, Filiais.Cnpj, Filiais.UF "
                + "from Rh_Ctrl "
                + "Left join Funcion as Funcion on Funcion.Matr = Rh_Ctrl.Matr and Funcion.CodFil = Rh_Ctrl.CodFil "
                + "Left join FPPeriodos  on FPPEriodos.DtInicioP = Rh_Ctrl.Dt_Ini\n"
                + "                     and FPPEriodos.DtFinalP = Rh_Ctrl.Dt_Fim\n"
                + "Left join FPMensal  on FPMEnsal.CodMovFP = FPPeriodos.CodMovFP\n"
                + //                "                   and FPMEnsal.TipoFP = 'MEN'\n" +
                "				   and FPMensal.Matr = Rh_Ctrl.Matr "
                + "Left join PstServ as PstServ on PstServ.Secao = FPMensal.Secao and PstServ.CodFil = FPMensal.CodFil "
                + "Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                + "Left Join Filiais as Filiais on Filiais.CodFil = Rh_Ctrl.CodFil "
                + "Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                + "where "
                + "Rh_Ctrl.Matr = ? and "
                + "Rh_Ctrl.Dt_Ini = ? and "
                + "Rh_Ctrl.Dt_Fim = ? and "
                + "Rh_Ctrl.CodFil = ?";
        try {
            Consulta cGeraPdfFolhaPonto = new Consulta(sql, persistencia);
            cGeraPdfFolhaPonto.setString(sMatricula);
            cGeraPdfFolhaPonto.setString(sDt_Ini);
            cGeraPdfFolhaPonto.setString(sDt_Fim);
            cGeraPdfFolhaPonto.setString(sCodFil);
            cGeraPdfFolhaPonto.select();

            while (cGeraPdfFolhaPonto.Proximo()) {
                oGeraPdfFolhaPonto = new GeraPdfFolhaPonto();
                Rh_Ctrl oRh_Ctrl = new Rh_Ctrl();
                Funcion oFuncion = new Funcion();
                PstServ oPstServ = new PstServ();
                Sindicatos oSindicatos = new Sindicatos();
                Filiais oFiliais = new Filiais();
                Cargos oCargos = new Cargos();

                oRh_Ctrl.setMatr(cGeraPdfFolhaPonto.getString("Matr"));
                oRh_Ctrl.setDt_Ini(cGeraPdfFolhaPonto.getString("Dt_Ini"));
                oRh_Ctrl.setDt_Fim(cGeraPdfFolhaPonto.getString("Dt_Fim"));
                oFuncion.setNome_Guer(cGeraPdfFolhaPonto.getString("Nome"));
                oFuncion.setCTPS_Nro(cGeraPdfFolhaPonto.getString("CTPS_Nro"));
                oFuncion.setCTPS_Serie(cGeraPdfFolhaPonto.getString("CTPS_Serie"));
                oPstServ.setLocal(cGeraPdfFolhaPonto.getString("LocalPosto"));
                oPstServ.setSecao(cGeraPdfFolhaPonto.getString("Secao"));
                oSindicatos.setMsgExtrato(cGeraPdfFolhaPonto.getString("MsgExtrato"));
                oSindicatos.setMsgExtrato2(cGeraPdfFolhaPonto.getString("MsgExtrato2"));
                oCargos.setDescricao(cGeraPdfFolhaPonto.getString("DescCargo"));
                oFiliais.setDescricao(cGeraPdfFolhaPonto.getString("DescFil"));
                oFiliais.setCodFil(cGeraPdfFolhaPonto.getString("CodFil"));
                oFiliais.setRazaoSocial(cGeraPdfFolhaPonto.getString("RazaoSocial"));
                oFiliais.setEndereco(cGeraPdfFolhaPonto.getString("Endereco"));
                oFiliais.setBairro(cGeraPdfFolhaPonto.getString("Bairro"));
                oFiliais.setCidade(cGeraPdfFolhaPonto.getString("Cidade"));
                oFiliais.setCEP(cGeraPdfFolhaPonto.getString("Cep"));
                oFiliais.setCNPJ(cGeraPdfFolhaPonto.getString("Cnpj"));
                oFiliais.setUF(cGeraPdfFolhaPonto.getString("UF"));

                oGeraPdfFolhaPonto.setRh_Ctrl(oRh_Ctrl);
                oGeraPdfFolhaPonto.setFuncion(oFuncion);
                oGeraPdfFolhaPonto.setFuncion(oFuncion);
                oGeraPdfFolhaPonto.setPstServ(oPstServ);
                oGeraPdfFolhaPonto.setSindicatos(oSindicatos);
                oGeraPdfFolhaPonto.setFiliais(oFiliais);
                oGeraPdfFolhaPonto.setCargos(oCargos);
            }
            cGeraPdfFolhaPonto.Close();
            return oGeraPdfFolhaPonto;
        } catch (Exception e) {
            throw new Exception("Falha ao retorna dados da função - " + e.getMessage());
        }

    }
}

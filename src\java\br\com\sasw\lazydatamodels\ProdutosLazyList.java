/*
 */
package br.com.sasw.lazydatamodels;

import Controller.Produtos.ProdutosSatMobWeb;
import Dados.Persistencia;
import SasBeans.Produtos;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class ProdutosLazyList extends LazyDataModel<Produtos> {

    private static final long serialVersionUID = 1L;
    private List<Produtos> produtos;
    private final ProdutosSatMobWeb produtosmobweb;
    private Persistencia persistencia;

    public ProdutosLazyList(Persistencia pst) {
        this.produtosmobweb = new ProdutosSatMobWeb();
        this.persistencia = pst;
    }

    @Override
    public List<Produtos> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.produtos = this.produtosmobweb.listagemPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.produtosmobweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.produtos;
    }

    @Override
    public Object getRowKey(Produtos produto) {
        return produto.getCodigo();
    }

    @Override
    public Produtos getRowData(String codigo) {
        for (Produtos produto : this.produtos) {
            if (produto.getCodigo().equals(new BigDecimal(codigo))) {
                return produto;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

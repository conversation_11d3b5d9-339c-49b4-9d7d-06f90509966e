package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Rt_PercDet;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercDetDao {

    /**
     * Faz inserção em Rt_PercDet inserindo KM
     *
     * @param rt_percdet
     * @param persistencia
     * @throws Exception
     */
    public void InserirDet(Rt_PercDet rt_percdet, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into Rt_PercDet "
                    + " (Sequencia, Parada, Codfil, KM) "
                    + " values (?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rt_percdet.getSequencia());
            consulta.setInt(rt_percdet.getParada());
            consulta.setBigDecimal(rt_percdet.getCodFil());
            consulta.setBigDecimal(rt_percdet.getKM());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDetDao.InserirDet - " + e.getMessage() + "\r\n"
                    + "insert into Rt_PercDet "
                    + " (Sequencia, Parada, Codfil, KM) "
                    + " values (" + rt_percdet.getSequencia() + "," + rt_percdet.getParada() + "," + rt_percdet.getCodFil() + "," + rt_percdet.getKM() + ")");
        }
    }

    /**
     * Faz atualização em Rt_PercDet inserindo KM
     *
     * @param rt_percdet
     * @param persistencia
     * @throws Exception
     */
    public void AtualizaKMDet(Rt_PercDet rt_percdet, Persistencia persistencia) throws Exception {
        try {
            String sql = "update Rt_PercDet "
                    + " set KM = ?"
                    + " where Sequencia = ? "
                    + " and Parada = ?"
                    + " and Codfil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rt_percdet.getKM());
            consulta.setBigDecimal(rt_percdet.getSequencia());
            consulta.setInt(rt_percdet.getParada());
            consulta.setBigDecimal(rt_percdet.getCodFil());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Rt_PercDetDao.AtualizaKMDet - " + e.getMessage() + "\r\n"
                    + "update Rt_PercDet "
                    + " set KM = " + rt_percdet.getKM()
                    + " where Sequencia = " + rt_percdet.getSequencia()
                    + " and Parada = " + rt_percdet.getParada()
                    + " and Codfil = " + rt_percdet.getCodFil());
        }
    }

    /**
     * Verifica a existencia de registro de detalhe para a parada passada
     *
     * @param rt_percdet - campos obrigatórios: codfil, sequencia, parada
     * @param persistencia - conexão ao banco de dados
     * @return - KM gravado na tabela
     * @throws Exception
     */
    public boolean existeDet(Rt_PercDet rt_percdet, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = "select KM from Rt_PercDet "
                    + " where Codfil = ?"
                    + " and Sequencia = ?"
                    + " and Parada = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(rt_percdet.getCodFil());
            consult.setBigDecimal(rt_percdet.getSequencia());
            consult.setInt(rt_percdet.getParada());
            consult.select();
            while (consult.Proximo()) {
                retorno = true;
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Rt_PercDetDao.existeDet - " + e.getMessage() + "\r\n"
                    + "select KM from Rt_PercDet "
                    + " where Codfil = " + rt_percdet.getCodFil()
                    + " and Sequencia = " + rt_percdet.getSequencia()
                    + " and Parada = " + rt_percdet.getParada());
        }
    }
}

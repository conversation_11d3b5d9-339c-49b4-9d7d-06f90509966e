/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TipoSrvCli {

    private String Codigo;
    private String Descricao;
    private String Banco;
    private String TAtend;
    private String TCob;
    private String TCar;
    private String TipoSrv;
    private String ER;
    private String CodInterf;
    private String Aditivo;
    private String Exportar;
    private String SubCentro;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the Codigo
     */
    public String getCodigo() {
        return Codigo;
    }

    /**
     * @param Codigo the Codigo to set
     */
    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    /**
     * @return the Descricao
     */
    public String getDescricao() {
        return Descricao;
    }

    /**
     * @param Descricao the Descricao to set
     */
    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    /**
     * @return the Banco
     */
    public String getBanco() {
        return Banco;
    }

    /**
     * @param Banco the Banco to set
     */
    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    /**
     * @return the TAtend
     */
    public String getTAtend() {
        return TAtend;
    }

    /**
     * @param TAtend the TAtend to set
     */
    public void setTAtend(String TAtend) {
        this.TAtend = TAtend;
    }

    /**
     * @return the TCob
     */
    public String getTCob() {
        return TCob;
    }

    /**
     * @param TCob the TCob to set
     */
    public void setTCob(String TCob) {
        this.TCob = TCob;
    }

    /**
     * @return the TCar
     */
    public String getTCar() {
        return TCar;
    }

    /**
     * @param TCar the TCar to set
     */
    public void setTCar(String TCar) {
        this.TCar = TCar;
    }

    /**
     * @return the TipoSrv
     */
    public String getTipoSrv() {
        return TipoSrv;
    }

    /**
     * @param TipoSrv the TipoSrv to set
     */
    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    /**
     * @return the ER
     */
    public String getER() {
        return ER;
    }

    /**
     * @param ER the ER to set
     */
    public void setER(String ER) {
        this.ER = ER;
    }

    /**
     * @return the CodInterf
     */
    public String getCodInterf() {
        return CodInterf;
    }

    /**
     * @param CodInterf the CodInterf to set
     */
    public void setCodInterf(String CodInterf) {
        this.CodInterf = CodInterf;
    }

    /**
     * @return the Aditivo
     */
    public String getAditivo() {
        return Aditivo;
    }

    /**
     * @param Aditivo the Aditivo to set
     */
    public void setAditivo(String Aditivo) {
        this.Aditivo = Aditivo;
    }

    /**
     * @return the Exportar
     */
    public String getExportar() {
        return Exportar;
    }

    /**
     * @param Exportar the Exportar to set
     */
    public void setExportar(String Exportar) {
        this.Exportar = Exportar;
    }

    /**
     * @return the SubCentro
     */
    public String getSubCentro() {
        return SubCentro;
    }

    /**
     * @param SubCentro the SubCentro to set
     */
    public void setSubCentro(String SubCentro) {
        this.SubCentro = SubCentro;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 41 * hash + Objects.hashCode(this.Codigo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TipoSrvCli other = (TipoSrvCli) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }
}

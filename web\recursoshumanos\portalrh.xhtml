<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/recursoshumanos/portalrh.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>        
                @font-face {
                    font-family: 'TwinMarker';
                    src: url('../assets/fonts/TwinMarker.ttf') format('truetype');
                    font-weight: normal;
                    font-style: normal;
                }

                .TituloPasta{
                    margin-top: 20px !important;
                    font-size: 12pt;
                    color: #3C8DBC;
                    width: 100%;
                    text-align: left;
                    padding-top: 8px;
                    padding-bottom: 8px;
                    border-bottom: thin solid #3C8DBC;
                    border-top: thin solid #3C8DBC;
                    margin-bottom: 10px;
                    background-color: azure;
                }

                .jconfirm-bg {
                    opacity: 0.9 !important;
                }

                .fundoPasta.select{
                    background-color: #FFF;
                    border: thin solid #DDD;
                }

                .fundoPasta, .btAdicionarArquivo, .fundoArquivo{
                    float: left; 
                    width: calc(20% - 5px); 
                    text-align: center;
                    height: 92px;
                    border-radius: 5px;
                    margin-top: 4px;
                    padding-top: 8px;
                    margin-left: 4px;
                    display: inline-block !important;
                    border: thin solid #EEE;
                    cursor: pointer;
                }

                .fundoArquivo{
                    border: none;
                }

                .fundoPasta i, .btAdicionarArquivo i, .fundoArquivo i{
                    font-size: 40pt;
                    color: #145B9B;
                    display: block;
                    cursor: pointer;
                }

                .fundoPasta label, .btAdicionarArquivo label, .fundoArquivo label{
                    font-size: 8pt;
                    cursor: pointer;
                }

                .fundoArquivo label{
                    background-color: #145B9B;
                    color: #FFF;
                    padding: 2px 6px 2px 6px;
                    border-radius: 10px;
                }

                #fundoPastaPai{
                    padding: 0px 0px 3px 0px;
                }

                .btAdicionarArquivo{
                    padding-top: 6px;
                }

                .btAdicionarArquivo{
                    border:3px solid #BBB;
                    background-color:#DDD;
                    border-radius:4px;
                    color:#BBB;
                    box-shadow:2px 2px 3px #CCC;
                }

                .btAdicionarArquivo i,
                .btAdicionarArquivo label{
                    color: #999;
                }

                .btAdicionarArquivo, .fundoArquivo{
                    margin-right: 4px;
                    margin-left: 0px;
                }

                .fundoArquivo{
                    width: auto !important;
                    padding-right: 30px !important;
                    padding-left: 30px !important;
                }

                @media only screen and (max-width: 5000px) and (min-width: 1600px) {
                    .fundoPasta{
                        width: calc(16.66% - 5px); 
                    }

                    #fundoPastaPai{
                        padding: 0px !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 0px) {
                    .fundoPasta, .btAdicionarArquivo, .fundoArquivo{
                        width: calc(50% - 5px) !important; 
                    }

                    #main{
                        top: 0px !Important;
                        height: 100vh !important
                    }

                    #PaiApresentacao{
                        top: 60px !important
                    }

                    #PaiBotoes{
                        top: -100px !important
                    }

                    #imgSatMob{
                        bottom: -80px !important;
                        height: 200px !important;    
                    }
                }

                @media only screen and (max-width: 509px) and (min-width: 0px) {
                    #PaiBotoes{
                        max-width: 351px !important;
                        top: -80px !important
                    }

                }


                [id*="formCadastrar"] .ui-inputtext,
                [id*="formCadastrar"] input{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                [id*="formCadastrar"] .calendario .ui-inputfield{
                    margin-top: 3px !important
                }

                [id*="formCadastrar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }


                [id*="cadastrar"] div[class*="col-md"]{
                    padding: 5px !important;
                }

                body{
                    height: 100vh !Important
                }

                .lightBox{
                    background-color: rgba(0,0,0,0.93);
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top:0px;
                    left: 0px;
                    z-index:9999;
                }

                .lightBox2{
                    background-color: rgba(0,0,0,0);
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top:0px;
                    left: 0px;
                    z-index:999991;
                }

                .Explorador {
                    position: absolute;
                    font-family: 'TwinMarker';
                    font-size: 35pt;
                    color: #ffd800;
                    line-height: 40px;
                    transform: rotate(-10deg);
                    animation: fadeIn 0.5s;
                    z-index:99999;
                }

                .Olhaque {
                    position: absolute;
                    font-family: 'TwinMarker';
                    font-size: 25pt;
                    color: #0ff;
                    line-height: 30px;
                    transform: rotate(0deg);
                    animation: fadeIn 0.5s;
                    z-index:999992;
                }

                .Olhaque span{
                    font-family: 'TwinMarker';
                }

                .btApresentacaoEntendi{
                    margin-top: 20px;
                    width: 100%;
                    z-index:999992;
                }

                [ref="MsgAviso1"]{
                    z-index:999992;
                }

                [id*="pnlDados"]{
                    height: calc(100vh - 210px) !Important;
                }

                @media only screen and (max-width: 700px) and (min-width: 0px) {
                    [id*="pnlDados"]{
                        height: calc(100vh - 250px) !Important;
                    }

                }

                .textoMensagem{
                    font-size: 14pt !important;
                    font-weight: 500 !important;
                    font-family: Arial !important;
                    text-shadow: 1px 1px 1px #000;
                }


                .botaoVerde{
                    background-color: #00A65A !important;
                }

                .botaoVerde:hover{
                    background-color: #008D4C !important;
                }

                [class*="jconfirm-box"]{
                    padding-left: 3px;
                    padding-right: 3px;
                }

            </style>

            <script type="text/javascript">
                // <![CDATA[
                let ArrayDoctos = new Array();

                function Apresentacao(Etapa) {
                    $('.lightBox,.lightBox2').css('display', '');

                    $('[ref^="MsgAviso"], .Olhaque, .Explorador').remove();

                    switch (Etapa) {
                        case 1:
                            $('#btAtualizarDados').css('z-index', '99999');
                            $('[id*="lblTextoAtualizar"]').css('color', '#FFF');

                            $('body').append('<img ref="MsgAviso1" src="../assets/images/AvisoArquivos/seta_arq1.png" style="position: absolute; top: 230px; right: 0; left: 0; margin: auto; animation: fadeIn 0.5s; width: 50px; height: 145px;" />');
                            $('body').append('<div class="Olhaque" style="top: 80px; font-size: 18pt; right: 0; left: 0; margin: auto; line-height: 28px; width: 290px;">Agora <span style="color: #FFF;">você pode manter seus dados sempre</span> atualizados<br><a href="javascript:void(0);" ref="2" class="btn btn-success btn-lg btApresentacaoEntendi"><i class="fa fa-thumbs-o-up"></i>&nbsp;&nbsp;Entendi</a></div>');

                            break;
                        case 2:
                            $('[id*="lblTextoAtualizar"]').css('color', '#145B9B');
                            $('#btAtualizarDados').css('z-index', 'initial');

                            $('#btDocumentacao').css('z-index', '99999');
                            $('[id*="lblTextoDocumentacao"]').css('color', '#FFF');

                            if ($('body').height() <= 700) {
                                $('body').append('<img ref="MsgAviso1" src="../assets/images/AvisoArquivos/seta_arq4.png" style="position: absolute; bottom: 190px; right: 150px; left: 0; margin: auto; animation: fadeIn 0.5s; width: 139px;transform: rotate(-10deg);" />');
                                $('body').append('<div class="Olhaque" style="bottom: 10px; font-size: 18pt; right: 0; left: 0; margin: auto; line-height: 28px; width: 290px;"><span style="color: #FFF;">Tire fotos dos seus </span>documentos <span style="color: #FFF;">e</span> atestados médico<br><a href="javascript:void(0);" ref="3" class="btn btn-success btn-lg btApresentacaoEntendi"><i class="fa fa-thumbs-o-up"></i>&nbsp;&nbsp;Entendi</a></div>');
                            } else {
                                $('body').append('<img ref="MsgAviso1" src="../assets/images/AvisoArquivos/seta_arq4.png" style="position: absolute; bottom: 230px; right: 150px; left: 0; margin: auto; animation: fadeIn 0.5s; width: 139px;transform: rotate(-40deg);" />');
                                $('body').append('<div class="Olhaque" style="bottom: 20px; font-size: 18pt; right: 0; left: 0; margin: auto; line-height: 28px; width: 290px;"><span style="color: #FFF;">Tire fotos dos seus </span>documentos <span style="color: #FFF;">e</span> atestados médico<br><a href="javascript:void(0);" ref="3" class="btn btn-success btn-lg btApresentacaoEntendi"><i class="fa fa-thumbs-o-up"></i>&nbsp;&nbsp;Entendi</a></div>');
                            }


                            break;
                        default:
                            $('#btDocumentacao').css('z-index', 'initial');
                            $('[id*="lblTextoDocumentacao"]').css('color', '#145B9B');
                            $('.lightBox,.lightBox2').css('display', 'none');
                            break;
                    }
                }

                $(document).ready(function () {
                    const Msg = '#{login.avisoportal}';
                    if (null != Msg &&
                            Msg != '' &&
                            Msg != 'null') {
                        $.alert({
                            title: '<img src="#{login.getLogo(login.pp.empresa)}" width="100px"/><br><br><font color="#00A65A"><i class="fa fa-paper-plane"></i>&nbsp;&nbsp;#{localemsgs.NovaMensagem}</font><br><br>-&nbsp;&nbsp;#{login.avisoportal.assunto}&nbsp;&nbsp;-<br><br><label class="textoMensagem">#{login.avisoportal.mensagem}</label>',
                            theme: 'supervan',
                            type: 'green',
                            content: '',
                            closeIcon: false,
                            buttons: {
                                ok: {
                                    btnClass: 'btn-green botaoVerde',
                                    text: '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-thumbs-o-up"></i>&nbsp;&nbsp;Entendi&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',
                                    action: function () {
                                        //Apresentacao(1);
                                    }
                                }
                            }
                        });
                    } else {
                        //Apresentacao(1);
                    }

                    $(window).resize();
                })
                        .on('click', '.fundoPasta', function () {
                            $('.fundoPasta.select').removeClass('select');
                            $(this).addClass('select');
                            $('[id*="txtTipoArq"]').val($(this).attr('hash'));
                            rc();
                            CarregarArquivos();
                        })
                        .on('click', '.btAdicionarArquivo', function () {
                            $('[id*="uploadFotosRelatorio_input"]').click();
                        })
                        .on('click', '.fundoArquivo i', function () {
                            $this = $(this).parent('.fundoArquivo');

                            if (!$this.attr('carregando') ||
                                    $this.attr('carregando') == 'N') {
                                $this.attr('carregando', 'S').css('text-align', 'center').find('i:eq(0)').attr('class', 'fa fa-refresh fa-spin fa-fw').css('text-align', 'center');

                                const linkFoto = 'https://mobile.sasw.com.br:9091/satellite/documentos/#{login.pp.empresa}/Pe_Doctos/' + parseInt($this.attr('cod-pessoa')).toString() + '/' + $this.attr('link-data');

                                setTimeout(function () {
                                    try {
                                        AbrirFoto(linkFoto, function () {
                                            $('.fundoArquivo').removeAttr('carregando').find('i:eq(0)').attr('class', 'fa fa-file-image-o');
                                        });
                                    } catch (e) {
                                        $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.FotoNaoEncontrada}');
                                        $('.fundoArquivo').removeAttr('carregando').find('i:eq(0)').attr('class', 'fa fa-file-image-o');
                                    }
                                }, 800);
                            }
                        })
                        .on('click', '[ref="excluirArquivo"]', function () {
                            $thisExcl = $(this);

                            $.MsgBoxVerdeSimNao('#{localemsgs.Atencao}', '#{localemsgs.ExcluirDocumento}', '#{localemsgs.Sim}', '#{localemsgs.Nao}', function () {
                                rcExclusao([{name: 'codPessoa', value: parseInt($thisExcl.attr('cod-pessoa'))}, {name: 'ordem', value: parseInt($thisExcl.attr('ordem'))}]);
                            });
                        })
                        .on('click', '.btApresentacaoEntendi', function () {
                            Apresentacao(parseInt($(this).attr('ref')));
                        });
                ;

                $(window).resize(function () {
                    if ($('div[south-type="js-confirm-content"]').length > 0) {
                        $('div[south-type="js-confirm-content"]').each(function () {
                            $(this).css('height', eval(($('body').height() - eval($(this).attr('ref-size')))) + 'px');
                        });
                    }

                    if ($('body').height() <= 800 &&
                            $('body').width() <= 500) {
                        $('#PaiBotoes, #imgSatMob').css('zoom', '0.7');
                    }
                });

                function RecarregarArray(Dados) {
                    ArrayDoctos = new Array();

                    if (null != Dados && Dados != '') {
                        Dados.split('|**|').forEach(function (Doc) {
                            ArrayDoctos.push({
                                Codigo: Doc.split('|__|')[0],
                                Ordem: Doc.split('|__|')[1],
                                Descricao: Doc.split('|__|')[2],
                                Tipo: Doc.split('|__|')[2].split('-')[0],
                                Minutos: Doc.split('|__|')[3]
                            });
                        });
                    }
                }

                function MascarasJS() {
                    setTimeout(function () {
                        $('[id*="divCPF"]').find('input').attr('south-type', 'cpf');
                        $('[id*="fone1"]').attr('south-type', 'telefone');
                        $('[id*="fone2"]').attr('south-type', 'telefone');
                        $('[id*="cep"]').attr('south-type', 'cep');

                        CriarAtributos('#{localeController.number}');
                    }, 600);
                }

                function CarregarArquivos(PosUpload = false) {
                    if (PosUpload) {
                        $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.FotoCarregadaSucesso}');
                    }

                    const TipoArq = $('.fundoPasta.select').attr('hash');
                    let ArrayArqs = new Array();

                    for (I = 0; I < ArrayDoctos.length; I++) {
                        if (ArrayDoctos[I].Tipo == TipoArq) {
                            ArrayArqs.push({
                                Codigo: ArrayDoctos[I].Codigo,
                                Ordem: ArrayDoctos[I].Ordem,
                                Descricao: ArrayDoctos[I].Descricao,
                                Tipo: ArrayDoctos[I].Tipo,
                                Minutos: ArrayDoctos[I].Minutos
                            });
                        }
                    }

                    let HTML = ' ';

                    if (ArrayArqs.length > 0) {
                        if (ArrayArqs.length > 1)
                            HTML += '     <h3 class="TituloPasta">[&nbsp;&nbsp;<font ref="QtdeArquivos">' + ArrayArqs.length + '</font>&nbsp;&nbsp;] #{localemsgs.ArquivosPasta}</h3>';
                        else
                            HTML += '     <h3 class="TituloPasta">[&nbsp;&nbsp;<font ref="QtdeArquivos">' + ArrayArqs.length + '</font>&nbsp;&nbsp;] #{localemsgs.ArquivoPasta}</h3>';

                        for (I = 0; I < ArrayArqs.length; I++) {
                            HTML += '     <div class="fundoArquivo" cod-pessoa="' + ArrayArqs[I].Codigo + '" link-data="PES' + ArrayArqs[I].Codigo + '-' + ArrayArqs[I].Ordem + ArrayArqs[I].Descricao.split('-')[1] + '">';
                            HTML += '       <i class="fa fa-file-image-o"></i>';
                            HTML += '       <label>' + ArrayArqs[I].Descricao.split('-')[0] + '-' + ArrayArqs[I].Ordem + ArrayArqs[I].Descricao.split('-')[1] + '</label>';
                            try {
                                if (parseInt(ArrayArqs[I].Minutos) < 20) {
                                    HTML += '       <a href="javascript:void(0);" ref="excluirArquivo" cod-pessoa="' + ArrayArqs[I].Codigo + '" ordem="' + ArrayArqs[I].Ordem + '" class="btn btn-danger" style="display: block; white-space: nowrap !important;"><i class="fa fa-trash" style="font-size: 11pt !important; color: #FFF !important"></i>&nbsp;&nbsp;Excluir</a>';
                                }
                            } catch (e) {
                            }
                            HTML += '     </div>';
                        }
                    } else {
                        HTML += '     <h3 class="TituloPasta">#{localemsgs.NenhumArquivoEncontrado}</h3>';
                    }

                    HTML += '     <div class="btAdicionarArquivo">';
                    HTML += '       <i class="fa fa-plus-square-o" aria-hidden="true"></i>';
                    HTML += '       <label>#{localemsgs.Adicionar}</label>';
                    HTML += '     </div>';

                    $('#divArquivos').html(HTML);
                }

                function AbrirDocumentacao() {
                    let HTML = '';
                    HTML += '<div south-type="js-confirm-content" ref-size="148" style="height:' + eval(($('body').height() - 148)) + 'px">';
                    HTML += '   <div id="fundoPastaPai" class="col-md-12 col-sm-12 col-xs-12" style="background-color: #EEE; border: thin solid #DDD; height: 200px; border-radius: 5px; white-space: nowrap; overflow: auto">';
                    HTML += '     <div class="fundoPasta select" hash="RG">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.RG}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CNH">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CNH}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="COMPROVANTE_RESIDENCIA">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.ComprovanteResidencia}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CTPS">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CTPS}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CERTIDAO_NASCIMENTO">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CertidaoNascimento}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CERTIDAO_CASAMENTO">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CertidaoCasamento}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="RESERVISTA">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.Reservista}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="PIS">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.PIS}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CPF">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CPF}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="PASSAPORTE">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.Passaporte}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="ATESTADO">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.Atestado}</label>';
                    HTML += '     </div>';
                    HTML += '     <div class="fundoPasta" hash="CARTEIRA_VIGILANTE">';
                    HTML += '       <i class="fa fa-folder"></i>';
                    HTML += '       <label>#{localemsgs.CarteiraVigilante}</label>';
                    HTML += '     </div>';
                    HTML += '   </div>';
                    HTML += '   <div id="divArquivos" class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important; height: calc(100% - 200px)">';

                    HTML += '   </div>';
                    HTML += '</div>';

                    $JanelaForm = $.confirm({
                        icon: 'fa fa-files-o',
                        type: 'blue',
                        closeIcon: true,
                        columnClass: 'xlarge',
                        containerFluid: true,
                        title: '#{localemsgs.Documentacao}',
                        boxWidth: '98%',
                        content: HTML,
                        useBootstrap: true,
                        offsetTop: 10,
                        offsetBottom: 10,
                        scrollToPreviousElement: true,
                        alignMiddle: false,
                        buttons: {
                            cancel: {
                                text: '#{localemsgs.Fechar}',
                                isHidden: false
                            }
                        },
                        onContentReady: function () {
                            $('.fundoPasta.select').click();
                        }
                    });
                }

                // ]]>
            </script>
        </h:head>
        <h:body id="body">
            <f:metadata>
                <f:viewParam name="Email" value="#{login.portalEmail}"/>
                <f:viewAction action="#{funcionario.Persistencias(login.pp, login.satellite)}" />
                <f:viewAction action="#{funcionario.carregarDadosEdicao(login.funcion.pessoa.matr)}" />
                <f:viewAction action="#{funcionario.carregarArrayPeDoctos}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <header style="display: #{login.portalLogin ne null and login.portalLogin ne ''?'none':''} !important">
                <div class="ui-grid ui-grid-responsive" style="background: #145b9b; height: 45px !important">
                    <div class="ui-grid-row cabecalho" style="height: 45px !important">
                        <div id="divTopoTela" class="col-md-12 col-sm-12 col-xs-12" style="background: #145b9b; height: 45px !important">
                            <img src="../assets/img/fundo_topo_logo.png" height="40" style="margin-top:-6px !important;" />
                            <label class="TituloPagina" style="left: 130px; color: white !important">#{localemsgs.PortalRH}</label>
                            <label class="TituloDataHora" style="left: 130px; color: white !important">
                                <h:outputText value="#{localemsgs.Data}: "/>
                                <span><h:outputText id="dataDia" value="#{localeController.mostraData}" converter="conversorDia"/></span>
                            </label>
                        </div>
                    </div>
                </div>
            </header>


            <h:form id="main">
                <p:remoteCommand name="rcExclusao" partialSubmit="true" 
                                 process="@this" 
                                 update="msgs" 
                                 actionListener="#{funcionario.excluirDocumento}" />  

                <div class="lightBox" style="display: none"></div>
                <div class="lightBox2" style="display: none"></div>
                <img id="imgSatMob" src="../assets/img/st_back#{login.origem}.png" style="position: absolute; bottom: -220px; left: 0; right: 0; margin: auto; height: 500px;"/>
                <p:panel class="mensagem-topo" toggleable="true"  toggleSpeed="500" toggleOrientation="horizontal" collapsed="true" rendered="#{login.avisoportal ne null}"  style="display: #{login.portalLogin ne null and login.portalLogin ne ''?'none':''} !important">
                    <f:facet name="header">
                        <h:outputText value="#{login.avisoportal.assunto}" escape="false"/>
                    </f:facet>

                    <h:outputText value="#{login.avisoportal.mensagem}" escape="false"/>
                </p:panel>


                <div class="row align-items-center h-100">
                    <div class="col-md-4 col-sm-12 col-xs-12 mx-auto mensagem" style="text-align: center !important;">
                        <p:panel toggleable="true"  toggleSpeed="500" rendered="false">
                            <f:facet name="header">
                                <h:outputText value="#{login.avisoportal.assunto}" escape="false"/>
                            </f:facet>
                            <h:outputText value="#{login.avisoportal.mensagem}" escape="false"/>
                        </p:panel>
                    </div>

                    <div id="PaiApresentacao" class="col-md-12 col-sm-8 col-xs-8" style="text-align: center !important; color: black; position: absolute; top: 100px; right: 0; left: 0; margin: auto">
                        <div>
                            <h2>
                                <h:outputText value="#{login.funcion.pessoa.sexo eq 'F' ? localemsgs.BemVinda : localemsgs.BemVindo}"/>
                            </h2>
                            <h:outputText value="#{login.funcion.pessoa.nome}"/>
                        </div>
                    </div>

                    <div id="PaiBotoes" class="col-md-12 col-sm-12 col-xs-12 mx-auto" style="text-align: center !important; color: black; max-width: 511px; height: 430px; position: absolute; top: 100px; right: 0; bottom: 0; left: 0; margin: auto;">

                        <div class="col-md-6 col-sm-6 col-xs-6" style="margin-top: 20px;width: 160px; height: 140px; float: left">
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{pontos.setPersistencia(login.pp)}"
                                               action="#{pontos.buscaPeriodos}"
                                               update="folhadeponto msgs">
                                    <p:graphicImage url="../assets/img/icone_satmob_fopag_G.png" width="100px" height="100px" 
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7"/>
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{pontos.setPersistencia(login.pp)}"
                                               action="#{pontos.buscaPeriodos}"
                                               update="folhadeponto msgs">
                                    <h:outputText style="color: #145B9B" value="#{localemsgs.FolhaPonto}"/>
                                </p:commandLink>
                            </div>
                        </div>

                        <div class="col-md-6 col-sm-6 col-xs-6" style="margin-top: 20px;width: 160px; height: 140px; float: left">
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{contracheque.setPersistencia(login.pp,login.pool)}"
                                               action="#{contracheque.buscaPeriodos}"
                                               update="contracheques msgs">
                                    <p:graphicImage url="../assets/img/icone_satmob_contracheque_G.png" width="100px" height="100px" 
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{contracheque.setPersistencia(login.pp,login.pool)}"
                                               action="#{contracheque.buscaPeriodos}"
                                               update="contracheques msgs">
                                    <h:outputText style="color: #145B9B" value="#{localemsgs.Contracheque}"/>
                                </p:commandLink>
                            </div>
                        </div>

                        <div class="col-md-6 col-sm-6 col-xs-6" style="margin-top: 20px;width: 160px; height: 140px;float: left">
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               action="#{funcoesadm.logsFuncion()}"
                                               update="adm msgs">
                                    <p:graphicImage url="../assets/img/icone_satmob_contracheque_funcaoadm_G.png" width="100px" height="100px" 
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               action="#{funcoesadm.logsFuncion()}"
                                               update="adm msgs">
                                    <h:outputText style="color: #145B9B" value="#{localemsgs.FuncoesAdm}"/>
                                </p:commandLink>
                            </div>
                        </div>

                        <div class="col-md-6 col-sm-6 col-xs-6" style="margin-top: 20px;width: 160px; height: 140px;float: left">
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               oncomplete="PF('dlgTrocarSenha').show()" update="msgs trocarsenha">
                                    <p:graphicImage url="../assets/img/icone_satmob_trocarsenhaG.png" width="100px" height="100px" 
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               oncomplete="PF('dlgTrocarSenha').show()" update="msgs trocarsenha">
                                    <h:outputText style="color: #145B9B" value="#{localemsgs.TrocarSenha}"/>
                                </p:commandLink>
                            </div>
                        </div>



                        <div id="btAtualizarDados" class="col-md-6 col-sm-6 col-xs-6" style="position: relative; margin-top: 20px; width: 160px; height: 140px;float: left; display: #{login.pp.empresa eq 'SATINVLMT' or login.pp.empresa eq 'SATINVLRO' or login.pp.empresa eq 'SATBRASIFORT' or login.pp.empresa eq 'SATSEFIX'?'none': 'initial'};">
                            <span class="badge badge-danger" style="background-color: red; float: right; width: 50px; position: absolute; top: -6px; right: 7px; height: 19px;">#{localemsgs.Novo}</span>

                            <div class="ui-grid-row">
                                <p:commandLink 
                                    oncomplete="PF('dlgCadastrar').show(); MascarasJS();" update="msgs trocarsenha">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="100px" height="100px"
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7; padding: 15px" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink 
                                    oncomplete="PF('dlgCadastrar').show(); MascarasJS();" update="msgs trocarsenha">
                                    <h:outputText id="lblTextoAtualizar" style="color: #145B9B" value="#{localemsgs.AtualizarMeusDados}"/>
                                </p:commandLink>
                            </div>
                        </div>


                        <div id="btDocumentacao" class="col-md-6 col-sm-6 col-xs-6" style="position: relative; margin-top: 20px; width: 160px; height: 140px;float: left">
                            <span class="badge badge-danger" style="background-color: red; float: right; width: 50px; position: absolute; top: -6px; right: 7px; height: 19px;">#{localemsgs.Novo}</span>
                            <div class="ui-grid-row">
                                <p:commandLink onclick="AbrirDocumentacao();" action="return false">
                                    <p:graphicImage url="../assets/img/icone_notas_fiscais.png" width="100px" height="100px"
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7; padding: 15px" />
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink onclick="AbrirDocumentacao();"  action="return false">
                                    <h:outputText id="lblTextoDocumentacao" style="color: #145B9B" value="#{localemsgs.Documentacao}"/>
                                </p:commandLink>
                            </div>
                        </div>


                        <div class="col-md-6 col-sm-6 col-xs-6" style="margin-top: 20px;width: 160px;float: left; height: 140px;display: #{login.ano ne null?'':'none'}">
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{rendimentos.setPersistencia(login.pp)}"
                                               action="#{rendimentos.imprimirRendimentos}" update="msgs"
                                               ajax="false" rendered="#{login.ano ne null}">
                                    <p:graphicImage url="../assets/img/icone_satmob_rendimentos_G.png" width="100px" height="100px" 
                                                    style="background: white; border-radius: 5px; border: 1px solid #c7c7c7"/>
                                </p:commandLink>
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink actionListener="#{rendimentos.setPersistencia(login.pp)}"
                                               action="#{rendimentos.imprimirRendimentos}" update="msgs"
                                               ajax="false" rendered="#{login.ano ne null}">
                                    <p:outputLabel style="color: #145B9B" value="#{localemsgs.InformeRendimentos} #{login.ano}"/>
                                </p:commandLink> 
                            </div>
                        </div>

                        <div class="#{login.ano ne null? 'col-md-6 col-sm-6 col-xs-6': 'col-md-12 col-sm-12 col-xs-12'}" style="margin-top: 20px;float: left; height: 140px; display: #{login.portalLogin ne null and login.portalLogin ne ''?'none':''} !important">
                            <div class="ui-grid-row">
                                <p:commandLink action="#{login.logOutRH}" update="msgs">
                                    <p:graphicImage url="../assets/img/icone_sair.png" height="80" width="80"/>
                                </p:commandLink> 
                            </div>
                            <div class="ui-grid-row">
                                <p:commandLink action="#{login.logOutRH}" update="msgs">
                                    <p:outputLabel style="color: #145B9B" value="#{localemsgs.Sair}"/>
                                </p:commandLink> 
                            </div>
                        </div>

                    </div>
                </div>

                <p:panel id="pnlUpload" style="display: none">
                    <p:remoteCommand name="rc" 
                                     process="txtTipoArq" />

                    <p:inputText id="txtTipoArq" value="#{funcionario.tipoArq}"></p:inputText>

                    <p:fileUpload id="uploadFotosRelatorio"
                                  fileUploadListener="#{funcionario.HandleFileUpload}"
                                  allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|PNG|JPE?G|GIF|BMP)$/"
                                  auto="true" multiple="false"
                                  process="@this txtTipoArq"
                                  invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                  dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                  update="msgs" skinSimple="true" previewWidth="10"
                                  style="width:10px; height:10px;"></p:fileUpload>
                </p:panel>
            </h:form>




            <h:form class="form-inline" id="formCadastrar">
                <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                          style="min-height:95% !important;height:95% !important;max-height:95% !important;max-width:95% !important;min-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="../assets/img/icone_funcionarios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.AtualizarMeusDados}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                    </f:facet>
                    <p:panel id="cadastrar" class="cadastrar" style="background-color:#EEE; overflow:hidden !important; padding:0px !important;">
                        <p:panel class="col-md-12 col-sm-12 col-xs-12" id="pnlDados" style="background-color:transparent; padding: 0px !important; margin:0px !important; overflow-y: auto !important; overflow-x: hidden !important;">
                            <div class="col-md-12 col-sm-12 col-xs-12 row" style="padding:0px !important; margin:0px !important">

                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <label><p:outputLabel for="nome" value="#{localemsgs.Nome}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                    <p:inputText id="nome" value="#{funcionario.novo.nome}" style="width: 100%"
                                                 required="true" 
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                                 maxlength="50">
                                    </p:inputText>
                                </div>
                            </div>
                            <div id="divCPF" class="col-md-3 col-sm-3 col-xs-12" style="display:#{localeController.number eq 1?'':'none'}">
                                <p:outputLabel for="cpf" value="#{localemsgs.CPF}"/>

                                <p:inputText id="cpf" value="#{funcionario.novo.CPF}" disabled="#{funcionario.novo.CPF ne null and funcionario.novo.CPF != ''}"
                                             style="width: 100%;"
                                             maxlength="14">
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="rgorg" value="#{localemsgs.RGOrg}"/>
                                <p:inputText id="rgorg" value="#{funcionario.pessoaMB.novaPessoa.RGOrgEmis}" 
                                             style="width: 100%"
                                             maxlength="15">
                                    <p:ajax event="change" process="@this" listener="#{funcionario.GerarRG}" update="rg"></p:ajax>
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="rg" value="#{localemsgs.RG}"/>
                                <p:inputText id="rg" value="#{funcionario.novo.RG}"
                                             style="width: 100%"
                                             maxlength="20">
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="fone1" value="#{localemsgs.Fone1}" />
                                <p:inputText id="fone1" value="#{funcionario.pessoaMB.novaPessoa.fone1}"
                                             maxlength="11" style="width: 100%">
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="fone2" value="#{localemsgs.Fone2}"/>
                                <p:inputText id="fone2" value="#{funcionario.pessoaMB.novaPessoa.fone2}"
                                             maxlength="11" style="width: 100%">
                                </p:inputText>
                            </div>
                            <div class="col-md-#{localeController.number eq 1?'9':'12'} col-sm-#{localeController.number eq 1?'9':'12'} col-xs-12">
                                <p:outputLabel for="email" value="#{localemsgs.Email}" />
                                <p:inputText id="email" value="#{funcionario.novo.email}"
                                             style="width: 100%"
                                             maxlength="50" >
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="cep" value="#{localemsgs.CEP}"/>

                                <p:inputText id="cep" value="#{funcionario.novo.CEP}" 
                                             style="width: 100%"
                                             maxlength="8">
                                </p:inputText>

                                <p:commandLink title="#{localemsgs.Pesquisar}"
                                               partialSubmit="true" process="@this formCadastrar:cep" id="cep_pesquisa" 
                                               update="formCadastrar:cep formCadastrar:ende formCadastrar:bairro formCadastrar:cidade formCadastrar:uf msgs"
                                               actionListener="#{funcionario.Endereco}" rendered="#{localeController.number eq 1}">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="24" style="position: absolute; right: 12px; margin-top: 4px;"/>
                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                    <p:confirmDialog global="true" >
                                        <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                        <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                    </p:confirmDialog>
                                    <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                              draggable="false" closable="true" width="300">
                                        <div class="form-inline">
                                            <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                            <p:spacer height="20px"/> 
                                        </div>
                                        <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                         onclick="PF('dlgOk').hide();" />
                                    </p:dialog>
                                </p:commandLink>
                            </div>
                            <div class="col-md-9 col-sm-9 col-xs-12">
                                <label style="margin: 0px !Important"><p:outputLabel for="bairro" value="#{localemsgs.Bairro}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:inputText id="bairro" value="#{funcionario.novo.bairro}" 
                                             style="width: 100%"
                                             required="true"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Bairro}"
                                             maxlength="20" />
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label><p:outputLabel for="ende" value="#{localemsgs.Endereco}" />  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:inputText id="ende" value="#{funcionario.novo.endereco}" 
                                             required="true" 
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Endereco}"
                                             style="width: 100%"
                                             maxlength="50">
                                </p:inputText>
                            </div>
                            <div class="col-md-9 col-sm-9 col-xs-12">
                                <label><p:outputLabel for="cidade" value="#{localemsgs.Cidade}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:autoComplete id="cidade" value="#{funcionario.novo.cidade}" styleClass="cidade" 
                                                required="true" completeMethod="#{funcionario.pessoaMB.BuscarCidade}"
                                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}" scrollHeight="200"
                                                style="width: 100% !important" maxlength="25" forceSelection="true" >
                                    <p:ajax event="itemSelect" listener="#{funcionario.selecionarCidade}"
                                            update="formCadastrar:cidade formCadastrar:uf"/>
                                </p:autoComplete>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <label><p:outputLabel for="uf" value="#{localemsgs.UF}"/>  <font style="font-weight: bold; color: red">(*)</font></label>
                                <p:inputText id="uf" value="#{funcionario.novo.UF}" disabled="true"
                                             required="true" style="width: 100%"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}"  maxlength="2">
                                </p:inputText>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="display: none">
                                <p:outputLabel for="obs" value="#{localemsgs.Obs}" />
                                <p:inputText id="obs" value="#{funcionario.pessoaMB.novaPessoa.obs}"  style="width: 100%"
                                             maxlength="60">
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="sexo" value="#{localemsgs.Sexo}"/>
                                <p:selectOneMenu value="#{funcionario.novo.sexo}"
                                                 id="sexo"

                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Masculino}" itemValue="M"/>
                                    <f:selectItem itemLabel="#{localemsgs.Feminino}" itemValue="F"/>
                                    <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="altura" value="#{localemsgs.Altura}" />
                                <p:inputText id="altura" value="#{funcionario.pessoaMB.novaPessoa.altura}" 
                                             style="width: 100%" maxlength="4">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="peso" value="#{localemsgs.Peso}"/>
                                <p:inputText id="peso" value="#{funcionario.pessoaMB.novaPessoa.peso}" 
                                             style="width: 100%" maxlength="3">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="dt_nasc" value="#{localemsgs.Dt_Nasc}" />  <font style="font-weight: bold; color: red">(*)</font></label>

                                <p:calendar id="dt_nasc" value="#{funcionario.novo.dt_Nasc}" yearRange="1950:2021"
                                            converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                            locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                            style="width: 100%;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dt_Nasc}" />
                                <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="nacionalidade" value="#{localemsgs.Nacionalidade}"/>
                                <p:selectOneMenu value="#{funcionario.novo.nacionalid}"
                                                 id="nacionalidade"
                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Brasileiro}" itemValue="10"/>
                                    <f:selectItem itemLabel="#{localemsgs.Naturalizado}" itemValue="20"/>
                                    <f:selectItem itemLabel="#{localemsgs.Argentino}" itemValue="21"/>
                                    <f:selectItem itemLabel="#{localemsgs.Boliviano}" itemValue="22"/>
                                    <f:selectItem itemLabel="#{localemsgs.Suico}" itemValue="38"/>
                                    <f:selectItem itemLabel="#{localemsgs.Italiano}" itemValue="39"/>
                                    <f:selectItem itemLabel="#{localemsgs.Hatiano}" itemValue="40"/>
                                    <f:selectItem itemLabel="#{localemsgs.Japones}" itemValue="41"/>
                                    <f:selectItem itemLabel="#{localemsgs.Chileno}" itemValue="23"/>
                                    <f:selectItem itemLabel="#{localemsgs.Chines}" itemValue="42"/>
                                    <f:selectItem itemLabel="#{localemsgs.Paraguaio}" itemValue="24"/>
                                    <f:selectItem itemLabel="#{localemsgs.Coeano}" itemValue="43"/>
                                    <f:selectItem itemLabel="#{localemsgs.Uruguaio}" itemValue="25"/>
                                    <f:selectItem itemLabel="#{localemsgs.Russo}" itemValue="44"/>
                                    <f:selectItem itemLabel="#{localemsgs.Venezuelano}" itemValue="26"/>
                                    <f:selectItem itemLabel="#{localemsgs.Portugues}" itemValue="45"/>
                                    <f:selectItem itemLabel="#{localemsgs.Colombiano}" itemValue="27"/>
                                    <f:selectItem itemLabel="#{localemsgs.Paquistanes}" itemValue="46"/>
                                    <f:selectItem itemLabel="#{localemsgs.Peruano}" itemValue="28"/>
                                    <f:selectItem itemLabel="#{localemsgs.Indiano}" itemValue="47"/>
                                    <f:selectItem itemLabel="#{localemsgs.Equatoriano}" itemValue="29"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrosLatino}" itemValue="48"/>
                                    <f:selectItem itemLabel="#{localemsgs.Alemao}" itemValue="30"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrosAsiatico}" itemValue="49"/>
                                    <f:selectItem itemLabel="#{localemsgs.Belga}" itemValue="31"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrosEuropeu}" itemValue="51"/>
                                    <f:selectItem itemLabel="#{localemsgs.Britanico}" itemValue="32"/>
                                    <f:selectItem itemLabel="#{localemsgs.Angolano}" itemValue="60"/>
                                    <f:selectItem itemLabel="#{localemsgs.Canadense}" itemValue="34"/>
                                    <f:selectItem itemLabel="#{localemsgs.Congoles}" itemValue="61"/>
                                    <f:selectItem itemLabel="#{localemsgs.Espanhol}" itemValue="35"/>
                                    <f:selectItem itemLabel="#{localemsgs.SulAfricano}" itemValue="62"/>
                                    <f:selectItem itemLabel="#{localemsgs.NorteAmericano}" itemValue="36"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrosAfricano}" itemValue="70"/>
                                    <f:selectItem itemLabel="#{localemsgs.Frances}" itemValue="37"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrosDesc}" itemValue="80"/>
                                    <p:ajax event="itemSelect" update="formCadastrar:anoCheg formCadastrar:ingresso"></p:ajax>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="anoCheg" value="#{localemsgs.AnoCheg}" /></label>

                                <p:inputText id="anoCheg" value="#{funcionario.novo.anoCheg}" 
                                             style="width: 100%" maxlength="4" disabled="#{funcionario.novo.nacionalid eq null or funcionario.novo.nacionalid eq '' or funcionario.novo.nacionalid eq '10'}">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="ingresso" value="#{localemsgs.Ingresso}"/>
                                <p:selectOneMenu value="#{funcionario.novo.interfExt}"
                                                 id="ingresso"
                                                 style="width: 100%;"  disabled="#{funcionario.novo.nacionalid eq null or funcionario.novo.nacionalid eq '' or funcionario.novo.nacionalid eq '10'}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.VistoPermanente}" itemValue="1"/>
                                    <f:selectItem itemLabel="#{localemsgs.VistoTemporario}" itemValue="2"/>
                                    <f:selectItem itemLabel="#{localemsgs.Asilado}" itemValue="3"/>
                                    <f:selectItem itemLabel="#{localemsgs.Refugiado}" itemValue="4"/>
                                    <f:selectItem itemLabel="#{localemsgs.SolicitanteRefugio}" itemValue="5"/>
                                    <f:selectItem itemLabel="#{localemsgs.ResidForaBrasil}" itemValue="6"/>
                                    <f:selectItem itemLabel="#{localemsgs.DefFisico}" itemValue="7"/>
                                    <f:selectItem itemLabel="#{localemsgs.ResidProv}" itemValue="8"/>
                                    <f:selectItem itemLabel="#{localemsgs.PermanBrasil}" itemValue="9"/>
                                    <f:selectItem itemLabel="#{localemsgs.BenefAcordo}" itemValue="10"/>
                                    <f:selectItem itemLabel="#{localemsgs.DepAgente}" itemValue="11"/>
                                    <f:selectItem itemLabel="#{localemsgs.BenefTrat}" itemValue="12"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="naturalidade" value="#{localemsgs.Naturalidade}"/>
                                <p:autoComplete id="naturalidade" value="#{funcionario.novo.naturalid}" styleClass="cidade" 
                                                completeMethod="#{funcionario.pessoaMB.BuscarCidade}"
                                                scrollHeight="200"
                                                style="width: 100% !important" maxlength="25" forceSelection="true" >
                                    <p:ajax event="itemSelect" listener="#{funcionario.selecionarCidade}" />
                                </p:autoComplete>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="raca" value="#{localemsgs.Raca}"/>
                                <p:selectOneMenu value="#{funcionario.novo.raca}"
                                                 id="raca"
                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Branca}" itemValue="2"/>
                                    <f:selectItem itemLabel="#{localemsgs.Negra}" itemValue="4"/>
                                    <f:selectItem itemLabel="#{localemsgs.Amarela}" itemValue="6"/>
                                    <f:selectItem itemLabel="#{localemsgs.Parda}" itemValue="8"/>
                                    <f:selectItem itemLabel="#{localemsgs.Indigena}" itemValue="0"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12">
                                <p:outputLabel for="instrucao" value="#{localemsgs.Instrucao}"/>
                                <p:selectOneMenu value="#{funcionario.novo.instrucao}"
                                                 id="instrucao"
                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItems value="#{funcionario.listTbVal}" var="listaInstrucao" itemValue="#{listaInstrucao.codigo}" itemLabel="#{listaInstrucao.descricao}"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12">
                                <p:outputLabel for="estadoCivil" value="#{localemsgs.EstadoCivil}"/>
                                <p:selectOneMenu value="#{funcionario.novo.estCivil}"
                                                 id="estadoCivil"
                                                 style="width: 100%;">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Solteiro}" itemValue="S"/>
                                    <f:selectItem itemLabel="#{localemsgs.Casado}" itemValue="C"/>
                                    <f:selectItem itemLabel="#{localemsgs.Desquitado}" itemValue="Q"/>
                                    <f:selectItem itemLabel="#{localemsgs.Divorciado}" itemValue="D"/>
                                    <f:selectItem itemLabel="#{localemsgs.Viuvo}" itemValue="V"/>
                                    <f:selectItem itemLabel="#{localemsgs.Marital}" itemValue="M"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="conjuge" value="#{localemsgs.Conjuge}" /></label>

                                <p:inputText id="conjuge" value="#{funcionario.novo.conjuge}" 
                                             style="width: 100%">
                                </p:inputText>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="pai" value="#{localemsgs.Pai}" /></label>

                                <p:inputText id="pai" value="#{funcionario.novo.pai}" 
                                             style="width: 100%">
                                </p:inputText>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="mae" value="#{localemsgs.Mae}" /></label>

                                <p:inputText id="mae" value="#{funcionario.novo.mae}" 
                                             style="width: 100%">
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="pis" value="#{localemsgs.PIS}" /></label>

                                <p:inputText id="pis" value="#{funcionario.novo.PIS}" 
                                             style="width: 100%" disabled="#{funcionario.novo.PIS ne null and funcionario.novo.PIS ne ''}" >
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                <label style="margin:0px !important"><p:outputLabel for="ctpsNro" value="#{localemsgs.CTPS_Nro}" /></label>

                                <p:inputText id="ctpsNro" value="#{funcionario.novo.CTPS_Nro}"
                                             style="width: 100%">
                                </p:inputText>
                            </div>
                            <div class="row" style="padding: 0px; margin: 0px">
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="ctpsSerie" value="#{localemsgs.CTPS_Serie}" /></label>

                                    <p:inputText id="ctpsSerie" value="#{funcionario.novo.CTPS_Serie}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="ctpsUF" value="#{localemsgs.CTPS_UF}" /></label>

                                    <p:inputText id="ctpsUF" value="#{funcionario.novo.CTPS_UF}"
                                                 style="width: 100%" maxlength="2">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="ctpsEmiss" value="#{localemsgs.Emissao}" /></label>

                                    <p:calendar id="ctpsEmiss" value="#{funcionario.novo.CTPS_Emis}" yearRange="1950:2021"
                                                converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                style="width: 100%;"  />
                                    <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="cnh" value="#{localemsgs.CNH}" /></label>

                                    <p:inputText id="cnh" value="#{funcionario.novo.CNH}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                            </div>
                            <div class="row" style="padding: 0px; margin: 0px">
                                <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="cnhVenc" value="#{localemsgs.Vencimento}" /></label>

                                    <p:calendar id="cnhVenc" value="#{funcionario.novo.dt_VenCNH}" yearRange="1950:2026"
                                                converter="conversorData" mask="99/99/9999" navigator="true" pattern="#{mascaras.padraoData}"
                                                locale="#{localeController.getCurrentLocale()}" styleClass="calendario"
                                                style="width: 100%;"  />
                                    <i class="fa fa-calendar" style="position:absolute; right: 14px; top: 34px; color:#666; font-size: 14pt !important;"></i>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="cnhCat" value="#{localemsgs.Categoria}" /></label>

                                    <p:inputText id="cnhCat" value="#{funcionario.novo.categoria}"
                                                 style="width: 100%" maxlength="2">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="cnhUF" value="#{localemsgs.UF}" /></label>

                                    <p:inputText id="cnhUF" value="#{funcionario.novo.UF_CNH}"
                                                 style="width: 100%" maxlength="2">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="reservista" value="#{localemsgs.Reservista}" /></label>

                                    <p:inputText id="reservista" value="#{funcionario.novo.reservista}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                            </div>
                            <div class="row" style="padding: 0px; margin: 0px">
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="reservistaCat" value="#{localemsgs.Categoria}" /></label>

                                    <p:inputText id="reservistaCat" value="#{funcionario.novo.reservCat}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="tituloEleitor" value="#{localemsgs.TituloEleitor}" /></label>

                                    <p:inputText id="tituloEleitor" value="#{funcionario.novo.titEleit}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="tituloEleitorZona" value="#{localemsgs.Zona}" /></label>

                                    <p:inputText id="tituloEleitorZona" value="#{funcionario.novo.titEZona}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="tituloEleitorSecao" value="#{localemsgs.secao}" /></label>

                                    <p:inputText id="tituloEleitorSecao" value="#{funcionario.novo.titSecao}"
                                                 style="width: 100%">
                                    </p:inputText>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12" style="position:relative;">
                                    <label style="margin:0px !important"><p:outputLabel for="grupoSang" value="#{localemsgs.GrupoSanguineo}" /></label>

                                    <p:selectOneMenu value="#{funcionario.novo.grupoSang}"
                                                     id="grupoSang"
                                                     style="width: 100%;">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                        <f:selectItem itemLabel="A+" itemValue="A+"/>
                                        <f:selectItem itemLabel="A-" itemValue="A-"/>
                                        <f:selectItem itemLabel="B+" itemValue="B+"/>
                                        <f:selectItem itemLabel="B-" itemValue="B-"/>
                                        <f:selectItem itemLabel="AB+" itemValue="AB+"/>
                                        <f:selectItem itemLabel="AB-" itemValue="AB-"/>
                                        <f:selectItem itemLabel="O-" itemValue="O+"/>
                                        <f:selectItem itemLabel="O+" itemValue="O-"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>
                        </p:panel> 
                        <div class="col-md-12 col-sm-12 col-xs-12" style="text-align: right; margin-top: 16px !important">
                            <p:commandLink id="editar" action="#{funcionario.EdicaoFuncionario}"
                                           update="msgs main formCadastrar:cadastrar"
                                           title="#{localemsgs.Editar}" styleClass="btn btn-primary">
                                <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                            </p:commandLink>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>




            <h:form id="trocarsenha"> 
                <p:dialog widgetVar="dlgTrocarSenha" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop" style="max-width: 100%">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_trocarsenhaG.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.TrocarSenha}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="atual" value="#{localemsgs.SenhaAtual}:"/>
                        <p:password id="atual" autocomplete="off" value="#{funcoesadm.senhaAtual}"/>

                        <p:outputLabel for="nova" value="#{localemsgs.NovaSenha}:"/>
                        <p:password id="nova" match="confirmar" autocomplete="off" required="true" 
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}"
                                    validatorMessage="Senhas não conferem" value="#{funcoesadm.senhaNova}"/>

                        <p:outputLabel for="confirmar" value="#{localemsgs.Confirmacao}:"/>
                        <p:password id="confirmar" autocomplete="off" value="#{funcoesadm.senhaNova}"/>
                    </p:panelGrid>
                    &nbsp;
                    <div class="pnl">
                        <p:commandButton value="#{localemsgs.TrocarSenha}" style="width: 130px !important;" 
                                         action="#{login.setPwweb(funcoesadm.trocarSenha(login.pwweb))}"
                                         update="msgs" class="custom-button"/>
                    </div>
                </p:dialog>
            </h:form>

            <h:form id="datas">
                <p:dialog widgetVar="dlgDatas" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop" style="max-width: 100%">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FuncoesAdm}" style="color:#022a48" /> 
                    </f:facet>
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="matr" value="#{localemsgs.Matr}:"/>
                        <p:inputText id="matr" value="#{funcoesadm.matr}"/>

                        <p:outputLabel for="dini" value="Data inicial:"/>
                        <p:inputMask id="dini" value="#{funcoesadm.dataIni}" mask="99/99/9999"/>

                        <p:outputLabel for="dfim" value="Data final:"/>
                        <p:inputMask id="dfim" value="#{funcoesadm.dataFim}" mask="99/99/9999"/>

                        <p:commandButton value="#{localemsgs.Contracheque}" style="width: 130px !important;" 
                                         action="#{funcoesadm.listaDatasCC}"
                                         update="msgs info" class="custom-button"/>

                        <p:commandButton value="#{localemsgs.FolhaDePonto}" style="width: 130px !important;"
                                         action="#{funcoesadm.listaDatasFP}"
                                         update="msgs info" class="custom-button"/>
                    </p:panelGrid>
                    <p:scrollPanel id="info" mode="native" style="width:100%;height:200px">
                        <table style="text-align: center; width: 100%">
                            <c:forEach var="dados" items="#{funcoesadm.dados}">
                                <tr>
                                    <td>
                                        <h:outputText value="#{dados}" styleClass="textoazul"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </table>
                    </p:scrollPanel>
                </p:dialog>
            </h:form>

            <h:form id="adm">
                <p:dialog widgetVar="dlgFuncoesAdm" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop" style="max-width: 100%">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FuncoesAdm}" style="color:#022a48" /> 
                    </f:facet>
                    <p:accordionPanel styleClass="accordion" activeIndex="null" id="accordionAdm">
                        <p:tab title="#{localemsgs.Matricula}" id="matricula">
                            <p:accordionPanel styleClass="nestedAccordion">
                                <p:tab title="#{localemsgs.FolhaDePonto}">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="fp" items="#{funcoesadm.folhasp}">
                                            <tr>
                                                <td>
                                                    <h:outputText value="#{fp}" styleClass="textoazul"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </table>
                                </p:tab>
                                <p:tab title="#{localemsgs.Contracheque}">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="cc" items="#{funcoesadm.cCheque}">
                                            <tr>
                                                <td>
                                                    <h:outputText value="#{cc}" styleClass="textoazul"/>
                                                </td> 
                                            </tr>
                                        </c:forEach>
                                    </table>
                                </p:tab>
                            </p:accordionPanel>
                        </p:tab>
                        <p:tab title="#{localemsgs.Mensagens}" id="mensagens">
                            <div style="text-align: center">
                                <h:outputText value="Assunto" styleClass="textoazul"/>
                                <p:inputText value="#{funcoesadm.assunto}" style="width: 97%"/>

                                <h:outputText value="Aviso" styleClass="textoazul"/>
                                <p:inputTextarea value="#{funcoesadm.aviso}" styleClass="textoazul" style="width: 97%"/>

                                <p:commandButton value="#{localemsgs.Enviar}" style="width: 130px !important;" 
                                                 update="main msgs" class="custom-button"
                                                 actionListener="#{funcoesadm.enviarMensagem}"
                                                 action="#{login.atualizaMensagem}"/>
                            </div>
                        </p:tab>
                        <p:tab title="#{localemsgs.ListaDatas}" id="listadatas">
                            <p:panelGrid columns="2" styleClass="pnl">
                                <p:outputLabel for="matr" value="#{localemsgs.Matricula}:"/>
                                <p:inputText id="matr" value="#{funcoesadm.matr}"/>

                                <p:outputLabel for="dini" value="Data inicial:"/>
                                <p:inputMask id="dini" value="#{funcoesadm.dataIni}" mask="99/99/9999"/>

                                <p:outputLabel for="dfim" value="Data final:"/>
                                <p:inputMask id="dfim" value="#{funcoesadm.dataFim}" mask="99/99/9999"/>

                                <p:commandButton value="#{localemsgs.Contracheque}" style="width: 130px !important;" 
                                                 action="#{funcoesadm.listaDatasCC}" oncomplete="PF('dlgDatas').show()"
                                                 update="msgs datas:info" class="custom-button"/>

                                <p:commandButton value="#{localemsgs.FolhaDePonto}" style="width: 130px !important;"
                                                 action="#{funcoesadm.listaDatasFP}" oncomplete="PF('dlgDatas').show()"
                                                 update="msgs datas:info" class="custom-button"/>
                            </p:panelGrid>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>

            <h:form id="logfuncion">
                <p:dialog widgetVar="dlgFuncoesFuncion" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop" style="max-width: 100%">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_funcaoadm_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FuncoesAdm}" style="color:#022a48" /> 
                    </f:facet>
                    <p:accordionPanel styleClass="accordion">
                        <p:tab title="#{localemsgs.FolhaDePonto}">
                            <table style="text-align: center; width: 100%">
                                <c:forEach var="fp" items="#{funcoesadm.folhasp}">
                                    <tr>
                                        <td>
                                            <h:outputText value="#{fp}" styleClass="textoazul"/>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                        <p:tab title="#{localemsgs.Contracheque}">
                            <table style="text-align: center; width: 100%">
                                <c:forEach var="cc" items="#{funcoesadm.cCheque}">
                                    <tr>
                                        <td>
                                            <h:outputText value="#{cc}" styleClass="textoazul"/>
                                        </td> 
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>

            <h:form id="folhadeponto">
                <p:dialog widgetVar="dlgFolhadePonto" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop" style="max-width: 100%">                    
                    <a href="relapuraponto.html">AQQ</a>
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_fopag_P.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.FolhaDePonto}" style="color:#022a48" /> 
                    </f:facet>
                    <table style="text-align: center; width: 100%">
                        <h:outputText class="textoazul" value="#{localemsgs.SemRegistros}" rendered="#{pontos.periodos.isEmpty()}"/>
                        <c:forEach var="periodos" items="#{pontos.periodos}">
                            <tr>
                                <td>
                                    <p:commandLink value="#{periodos.dt_Ini} a #{periodos.dt_Fim} - #{periodos.codFil.toBigInteger()}: #{periodos.matr.toBigInteger()}" target="_blank"
                                                   action="#{pontos.imprimirFolhaDePonto(periodos)}" 
                                                   update="msgs" styleClass="linkazul"
                                                   ajax="false"/>
                                   
                                </td>                                
                            </tr>                            
                        </c:forEach>
                    </table>                    
                </p:dialog>
            </h:form>

            <h:form id="contracheques">
                <p:dialog widgetVar="dlgContracheque" draggable="false" focus="contracheques"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" height="400" showEffect="drop" hideEffect="drop" style="max-width: 100%">
                    <f:facet name="header">
                        <img src="../assets/img/icone_satmob_contracheque_G.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Contracheque}" style="color:#022a48" />
                    </f:facet>


                    <p:panel class="col-md-12 col-sm-12 col-xs-12" id="panelPeriodo">
                        <div class="col-md-3 col-sm-3 col-xs-3">
                            <p:outputLabel for="periodo" value="#{localemsgs.Periodo}:" />
                        </div>
                        <div class="col-md-9 col-sm-9 col-xs-9">
                            <p:datePicker id="periodo" view="month" value="#{contracheque.periodo}" pattern="MM/yyyy" inputStyle="width: 80px;"
                                          yearNavigator="true" yearRange="2000:2030" showIcon="true" readonlyInput="true">
                                <p:ajax event="dateSelect" listener="#{contracheque.buscaPeriodo}" update="msgs"/>
                            </p:datePicker>
                        </div>                        
                    </p:panel>

                    <p:accordionPanel id="periodos" value="#{contracheque.fpperiodos}"  var="fpperiodos" dynamic="true" styleClass="accordion">
                        <p:ajax event="tabChange" listener="#{contracheque.buscarFpmensais}" update="msgs" />
                        <p:tab title="#{fpperiodos.dtInicioF}" id="periodocontracheque">
                            <h:inputHidden id="txtDataImage" value="#{contracheque.base64Assinatura}"></h:inputHidden>

                            <table style="text-align: center; width: 100%">
                                <tr>
                                    <td>
                                        <h:outputText class="textoazul" value="#{localemsgs.SemRegistros}"
                                                      rendered="#{contracheque.fpmensais.isEmpty()}"/>
                                    </td>
                                </tr>
                                <c:forEach var="fp" items="#{contracheque.fpmensais}">
                                    <tr>
                                        <td>
                                            <p:commandLink value="#{fp.tipoFpFormatado} - #{fp.codFil.toBigInteger()}: #{fp.matr.toBigInteger()}"
                                                           action="#{contracheque.imprimirContracheque(fp)}" 
                                                           update="msgs" styleClass="linkazul"/>

                                            <p:commandLink id="btImprimeContraCheque" value="#{fp.tipoFpFormatado} - #{fp.codFil.toBigInteger()}: #{fp.matr.toBigInteger()}" target="_blank"
                                                           action="#{contracheque.imprimirContrachequeDet}" 
                                                           update="msgs" styleClass="linkazul"
                                                           ajax="false" style="display: none"/>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>

                <script>
                    var frmAssina = null;

                    function AbrirFormAssinatura() {
                        frmAssina = $.alert({
                            icon: 'fa fa-pencil fa-lg',
                            theme: 'modern',
                            closeIcon: true,
                            type: 'blue',
                            title: 'Assine seu Contra Cheque',
                            content: '<iframe src="../assina_docs.html" style="width: 238px; height: 250px; border: none; padding: 0px; margin: 0px;"></iframe>',
                            buttons: {
                                ok: {
                                    text: 'Ok',
                                    btnClass: 'btn-blue',
                                    isHidden: true
                                }
                            }
                        });
                    }

                    function AssinaturaCapturada(inDataImage) {
                        frmAssina.close();
                        $('[id*="txtDataImage"]').val(inDataImage);
                        $('[id*="btImprimeContraCheque"]').click();
                    }

                    function ImprimeContraCheque() {
                        $('[id*="btImprimeContraCheque"]').click();
                    }
                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>
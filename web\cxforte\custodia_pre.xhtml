<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/animate.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <script
                src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
            </script>
            <style>
                .ui-dialog-titlebar-icon{
                    display: none !important
                }
            </style>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{importarMB.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{custodia.Persistencia(login.pp)}"/>
            </f:metadata>
            
            <!-- Caixa Forte - Custódia -->
            <h:form id="formCaixaForteCustodia">
                <p:dialog widgetVar="dlgCaixaForteCustodia" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true" draggable="false" responsive="true"
                          showEffect="drop" hideEffect="drop" height="310" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; min-width:95% !important;width:95% !important;max-width:95% !important;overflow:hidden !important;background-color:#EEE !important">

                    <f:facet name="header">
                        <img src="../assets/img/icones_custodia_CAIXAFORTE.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.CaixaForte}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                    </f:facet>

                    <p:panel style="display: inline; background-color: #EEE !important;">
                        <p:dataTable
                            id="tabelaCxForte"
                            value="#{custodia.cxForteLista}"
                            rowKey="#{listaCaixasForte.codCli}"
                            paginator="false"
                            paginatorTemplate="false"
                            lazy="true"
                            reflow="true"
                            var="listaCaixasForte"
                            styleClass="tabela"
                            selectionMode="single"
                            emptyMessage="#{localemsgs.SemRegistros}"
                            scrollable="true"
                            class="tabela DataGrid"
                            scrollWidth="100%"
                            style="display: flex; flex-direction: column; font-size: 12px; background: white; padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important; background-color:#EEE !important"
                            >
                            <p:ajax event="rowDblselect" listener="#{custodia.dblSelectPre}" />
                            <p:column headerText="#{localemsgs.CodFil}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.codFil}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.CodCli}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.codCli}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.NRed}" >
                                <h:outputText value="#{listaCaixasForte.NRed}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SaldoReal}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.saldoReal}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Saldo}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.saldo}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.QtdeRemessasCustodia}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPendQtde}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRemessasCustodia}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPendValor}" converter="conversormoeda" />
                            </p:column>
                            <p:column headerText="#{localemsgs.QtdeRemessasPreparadas}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPrepQtde}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRemessasPreparadas}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPrepValor}" converter="conversormoeda" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Operador}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.operador}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.dt_Alter}" converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}" class="text-center">
                                <h:outputText converter="conversorHora" value="#{listaCaixasForte.hr_Alter}"/>
                            </p:column>
                        </p:dataTable>
                        <label style="background-color: lightyellow;position:absolute; bottom:10px; text-shadow:1px 1px #FFF; border:thin solid darkorange !important; color: darkorange !important; font-size:16pt; width:calc(100% - 30px); padding:6px !important; text-align:center;">#{localemsgs.SelecioneCaixaForte}</label>
                    </p:panel>

                </p:dialog>
            </h:form>
            
            <script>
                $(document).ready(function(){
                    PF('dlgCaixaForteCustodia').show(); 
                });
            </script>
        </h:body>
    </f:view>
</html>
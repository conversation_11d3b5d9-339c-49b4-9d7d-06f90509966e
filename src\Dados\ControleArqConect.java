package Dados;

import Arquivo.ArquivoLog;
import Dados.OLD.DadosBancos_OLD;
import SasBeans.Paramet;
import SasDaos.ParametDao;
import java.io.File;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ControleArqConect {

    public void AtualizaArqConect(String caminho, Persistencia persistencia) throws Exception {
        try {
            String arqbk = caminho.substring(0, caminho.indexOf(".txt")) + "_bk.txt";
            File arquivo = new File(caminho);
            File arquivo_bk = new File(arqbk);
            if (arquivo_bk.exists()) {
                arquivo_bk.delete();

            }
            arquivo.renameTo(arquivo_bk);
            ParametDao pd = new ParametDao();
            DadosBancos_OLD bd;
            List<Paramet> paramets = pd.getPath(persistencia);
            for (Paramet p1 : paramets) {
                bd = new DadosBancos_OLD();
                bd.setEmpresa(p1.getPath());
                bd.setIP(p1.getHostNameWEB() + ";databaseName=" + p1.getBancoDados());
                bd.setLogin(p1.getUsuario());
                bd.setSenha(p1.getSenha());
                this.InserirConexao(bd, caminho);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar arquivo de conexão - " + e.getMessage());
        }
    }

    public void InserirConexao(DadosBancos_OLD bd, String caminho) throws Exception {
        try {
            String entrada = bd.getEmpresa() + "}"
                    + bd.getIP() + "}"
                    + bd.getLogin() + "}"
                    + bd.getSenha() + "}";
            ArquivoLog mapconect = new ArquivoLog();
            mapconect.GravaDados(entrada, caminho);
        } catch (Exception e) {
            throw new Exception("Falha ao inserir registro no arquivo de conexão - " + e.getMessage());
        }
    }

}

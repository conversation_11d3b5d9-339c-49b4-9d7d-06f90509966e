package Controller.Funcion;

import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.Clientes;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import SasBeans.PstServ;
import SasBeans.RHHorario;
import SasBeansCompostas.FuncionPstServ;
import SasDaos.CargosDao;
import SasDaos.ClientesDao;
import SasDaos.FuncionDao;
import SasDaos.PessoaDao;
import SasDaos.PstServDao;
import SasDaos.RHHorarioDao;
import SasDaos.SaspwDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class FuncionSatMobWeb {

    /**
     * Listagem de funcionarios
     *
     * @param codfil - código de filial
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> Listagem(String codfil, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.ListaFuncionFilial(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Insercção de funcionário
     *
     * @param funcion - objeto funcion Dados necessários: matr, nome, cpf,
     * secao, operador
     * @param pessoa - objeto pessoa referente ao funcionario
     * @param persistencia - conexão ao banco de dados
     * @param persistCentral - conexão ao banco central
     * @throws Exception - funcion.matriculajacadastrada quando essa matricula
     * já foi cadastrada - funcion.falhageral<message> tudo que vier após a tag
     * message descre o problema
     */
    public void Inserir(Funcion funcion, Pessoa pessoa, Persistencia persistencia, Persistencia persistCentral) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            PessoaDao pessoadao = new PessoaDao();

            //Verifica a existencia do funcionario
            if (funciondao.matriculaExiste(funcion.getMatr().toPlainString(), persistencia)) {
                throw new Exception("matriculajaexiste.");
            }

            //Verifica a existencia da pessoa
            //Se a pessoa não existe na base central, a insere.
            Pessoa p;

            if (funcion.getCodPessoaReaproveitamento() == BigDecimal.ZERO) {
                p = pessoadao.buscarPessoaCPF(pessoa.getCPF(), persistCentral);
            } else {
                p = pessoadao.buscarPessoaCodigo(funcion.getCodPessoaReaproveitamento(), persistCentral);
            }

            if (null == p.getNome()
                    || (funcion.getCodPessoaReaproveitamento() == BigDecimal.ZERO && (pessoa.getCPF().equals("") || null == pessoa.getCPF()))) {
                p = pessoa;
                p.setCodigo(pessoadao.getCodigo(persistCentral));
                p.setOperador(funcion.getOperador());
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                p.setNome(p.getNome().toUpperCase());
                p.setRG(p.getRG().toUpperCase());
                p.setRGOrgEmis(p.getRGOrgEmis().toUpperCase());
                p.setEndereco(FuncoesString.RecortaString(p.getEndereco().toUpperCase(), 0, 50));
                p.setBairro(FuncoesString.RecortaString(p.getBairro(), 0, 20));
                try {
                    p.setFuncao(p.getFuncao().toUpperCase());
                } catch (Exception e) {
                }
                p.setUF(FuncoesString.RecortaString(p.getUF().toUpperCase(), 0, 2));
                p.setSituacao("F");
                p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
                pessoadao.InserirPessoaSatMobWeb(p, persistCentral);
            } else {
                p.setOperador(funcion.getOperador());
                p.setDt_Alter(DataAtual.getDataAtual("SQL"));
                p.setHr_Alter(DataAtual.getDataAtual("HORA"));
                p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
                pessoadao.atualizaPessoaSatMob(p, persistCentral);
            }
            //Se a pessoa não existe na base local, a insere.
            Pessoa p2 = pessoadao.buscarPessoaCPF(pessoa.getCPF(), persistencia);
            if (null == p2.getNome()
                    || (funcion.getCodPessoaReaproveitamento() == BigDecimal.ZERO && (pessoa.getCPF().equals("") || null == pessoa.getCPF()))) {
                pessoa.setCodigo(pessoadao.getCodigo(persistencia));
                pessoa.setCodPessoaWEB(p.getCodigo());
                pessoa.setOperador(funcion.getOperador());
                pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
                pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
                pessoa.setSituacao("F");
                pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
                pessoadao.InserirPessoaSatMobWeb(pessoa, persistencia);
            } else {
                // update pessoa: insere a matrícula
                pessoa.setOperador(funcion.getOperador());
                pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
                pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
                pessoa.setSituacao("F");
                pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
                pessoadao.atualizaPessoaSatMob(pessoa, persistencia);
            }
            funcion.setCodPessoaWeb(pessoa.getCodigo());
            funcion = (Funcion) FuncoesString.removeAcentoObjeto(funcion);
            funciondao.InserirFuncionSatMobWeb(funcion, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Gravação de alterações de funcionário e pessoa nas bases central e local;
     *
     * @param funcion - objeto funcion
     * @param pessoa
     * @param persistencia - conexão ao banco de dados
     * @param persistSat
     * @throws Exception
     */
    public void GravaFuncion(Funcion funcion, Pessoa pessoa, Persistencia persistencia, Persistencia persistSat) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            PessoaDao pessoadao = new PessoaDao();
            funcion = (Funcion) FuncoesString.removeAcentoObjeto(funcion);
            funciondao.GravaFuncionSatMobWeb(funcion, persistencia);

            //atualiza pessoa na base central.
            Pessoa p;
            
            if(null != pessoa.getCPF() && !pessoa.getCPF().equals("")){
                p = pessoadao.buscarPessoaCPF(pessoa.getCPF(), persistSat);
            }
            else{
                p = pessoa;
            } 
            
            p.setRG(pessoa.getRG());
            p.setRGOrgEmis(pessoa.getRGOrgEmis());
            p.setNome(pessoa.getNome());
            p.setEmail(pessoa.getEmail());
            p.setFone1(pessoa.getFone1());
            p.setFone2(pessoa.getFone2());
            p.setCEP(pessoa.getCEP());
            p.setBairro(pessoa.getBairro());
            p.setUF(pessoa.getUF());
            p.setObs(pessoa.getObs());
            p.setFuncao(pessoa.getFuncao());
            p.setSituacao(pessoa.getSituacao());
            p.setAltura(pessoa.getAltura());
            p.setPeso(pessoa.getPeso());
            p.setSexo(pessoa.getSexo());
            p.setDt_Alter(DataAtual.getDataAtual("SQL"));
            p.setHr_Alter(DataAtual.getDataAtual("HORA"));
            p = (Pessoa) FuncoesString.removeAcentoObjeto(p);
            pessoadao.atualizaPessoaSatMob(p, persistSat);

            //atualiza pessoa na base local.
            pessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            pessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            pessoa = (Pessoa) FuncoesString.removeAcentoObjeto(pessoa);
            pessoadao.atualizaPessoaSatMob(pessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("funcionario.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca de funcionário por CPF
     *
     * @param cpf
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Funcion> BuscaFuncionCPF(String cpf, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.BuscaCPF(cpf, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca a próxima matr válida e sugere no cadastro de funcionário
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal sugereMatricula(Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.getMaxMatr(persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca cargos para cadastrode funcionário
     *
     * @param descricao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Cargos> buscaCargos(String descricao, Persistencia persistencia) throws Exception {
        try {
            CargosDao cargosDao = new CargosDao();
            return cargosDao.buscarCargos(descricao, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca o cargo do funcionário pelo código do cargo
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Cargos getCargoFuncion(String codigo, Persistencia persistencia) throws Exception {
        try {
            CargosDao cargosDao = new CargosDao();
            return cargosDao.getCargo(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    public List<FuncionPstServ> PesquisaFuncionarios(FuncionPstServ funcion, String codfil, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.pesquisaFuncionSatMobWeb(funcion, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de funcionários
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.TotalFuncionMobWeb(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica a existencia do nome em saspw
     *
     * @param nome
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeSASPWNome(String nome, Persistencia persistencia) throws Exception {
        try {
            SaspwDao saspwDao = new SaspwDao();
            return !saspwDao.BuscaLogin(nome, persistencia).isEmpty();
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica a existencia do nome_guer em funcion
     *
     * @param nome
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeFuncionNomeGuer(String nome, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funcionDao = new FuncionDao();
            return !funcionDao.getFuncionNomeGuer(nome, persistencia).isEmpty();
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de pessoas
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Monta a lista de horários para cadastro de funcionário
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<RHHorario> listaHorarios(String codFil, Persistencia persistencia) throws Exception {
        try {
            RHHorarioDao rhHorarioDao = new RHHorarioDao();
            return rhHorarioDao.listaRHHorarios(codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca informações do horário do funcionário
     *
     * @param codFil
     * @param horario
     * @param persistencia
     * @return
     * @throws Exception
     */
    public RHHorario buscarHorario(BigDecimal codFil, Integer horario, Persistencia persistencia) throws Exception {
        try {
            RHHorarioDao rhHorarioDao = new RHHorarioDao();
            return rhHorarioDao.getRHHorario(codFil, horario, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    public Funcion BuscaFuncisonCodpessoa(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.BuscaFuncionCodPessoa(codpessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    public List<Funcion> listaFuncion(String codfil, BigDecimal matr, String nome, String nome_guer, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno;
            FuncionDao funciondao = new FuncionDao();
            if (!"".equals(nome)) {
                retorno = funciondao.buscarFuncionNome(nome, persistencia);
            } else {
                retorno = funciondao.buscarFuncionNome(nome, persistencia);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    public List<Funcion> buscarFuncion(String query, Persistencia persistencia) throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.buscarFuncionNome(query, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarCliente(String query, String codfil, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.buscarClientes(query, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }

    public List<PstServ> buscarPstServ(String codcli, String codfil, Persistencia persistencia) throws Exception {
        try {
            PstServDao ptServDao = new PstServDao();
            return ptServDao.listarPostosCliente(codfil, codcli, persistencia);
        } catch (Exception e) {
            throw new Exception("funcion.falhageral<message>" + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.NFItens;
import SasBeans.SatWebService.IntegracaoProtheus;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoProtheusDao {

    public List<IntegracaoProtheus> getXML(String dataInicio, String dataFim, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql1 = " Select "
                    + " NFiscal.Numero, NFiscal.Praca, NFiscal.Data Emissao, NFiscal.CliFat Cliente, '' Loja, "
                    + " NFiscal.Dt_Venc Cond_Pagamento,NFiscal.Valor,  "
                    + " COALESCE((SELECT Descr_Linh AS [text()] "
                    + " 		FROM HtLinhas "
                    + " 		WHERE HtLinhas.CodFil = NFiscal.CodFil "
                    + " 		   and HtLinhas.Codigo = NFiscal.CodHist "
                    + " 		ORDER BY Linha "
                    + " 		FOR XML PATH(''), TYPE).value('.[1]', 'VARCHAR(MAX)'), '') Mensagem_Nota,NFiscal.Compet,  "
                    + " Clientes.Banco Banco_Cliente, 0 Desconto, 'CIF' Frete, '' Tes, '' Descricao, FatTvFechaNF.Agrupador "
                    + " From NFiscal "
                    + " Left Join FatTvFechaNF  on FatTvFechaNF.NF    = NFiscal.Numero "
                    + "                        and FatTvFechaNF.Praca = NFiscal.Praca "
                    + " Left Join Clientes  on Clientes.Codigo = NFiscal.CliFat "
                    + "                    and Clientes.CodFil = NFiscal.CodFil "
                    + " where NFiscal.Data between ? and ? "
                    + "   and NFiscal.Situacao = 'A' "
                    + "   and NFiscal.CodFil = ? "
                    + " Group by NFiscal.Numero, NFiscal.Praca, NFiscal.Data, NFiscal.CliFat, NFiscal.Dt_Venc, NFiscal.CodHist,NFiscal.CodFil,FatTvFechaNF.Agrupador, "
                    + "          NFiscal.Compet, Clientes.Banco,NFiscal.Valor, NFiscal.Compet "
                    + " Order by NFiscal.Numero ";

            Consulta consulta1 = new Consulta(sql1, persistencia);
            consulta1.setString(dataInicio);
            consulta1.setString(dataFim);
            consulta1.setString(codFil);
            consulta1.select();
            List<IntegracaoProtheus> lista = new ArrayList<>();
            IntegracaoProtheus integracaoProtheus;
            while (consulta1.Proximo()) {
                integracaoProtheus = new IntegracaoProtheus();
                integracaoProtheus.setNumero(consulta1.getString("numero"));
                integracaoProtheus.setCompet(consulta1.getString("Compet"));
                integracaoProtheus.setEmissao(consulta1.getString("emissao"));
                integracaoProtheus.setCliente(consulta1.getString("cliente"));
                integracaoProtheus.setLoja(consulta1.getString("loja"));
                integracaoProtheus.setCond_pagto(consulta1.getString("Cond_Pagamento"));
                integracaoProtheus.setMensagem_nota(consulta1.getString("Mensagem_Nota"));
                integracaoProtheus.setBanco_cliente(consulta1.getString("Banco_Cliente"));
                integracaoProtheus.setDesconto(consulta1.getString("desconto"));
                integracaoProtheus.setTipo_frete(consulta1.getString("Frete"));
                integracaoProtheus.setValor(consulta1.getString("valor"));
                integracaoProtheus.setDescricao(consulta1.getString("descricao"));
                integracaoProtheus.setAgrupador(consulta1.getString("agrupador"));
                integracaoProtheus.setPraca(consulta1.getString("praca"));
                lista.add(integracaoProtheus);
            }
            consulta1.Close();

            String sql2 = " Select NFItens.Numero, NFItens.TipoPosto Item, NFItens.CodPro Produto, NFitens.Qtde, NFItens.Valor_Un PRC_Unit, "
                    + "NFitens.Valor_Tot PRC_Total"
                    + " from NFItens  "
                    + " Left Join NFiscal  on NFiscal.Numero = NFItens.Numero "
                    + "                   and NFiscal.Praca  = NFitens.Praca "
                    + " where NFiscal.Data between ? and ? "
                    + "   and NFiscal.CodFil = ? "
                    + "   and NFiscal.Situacao = 'A' "
                    + " Order by NFITens.Numero, NFitens.TipoPosto ";
            Consulta consulta2 = new Consulta(sql2, persistencia);
            consulta2.setString(dataInicio);
            consulta2.setString(dataFim);
            consulta2.setString(codFil);
            consulta2.select();
            List<NFItens> produtos = new ArrayList<>();
            NFItens produto;
            while (consulta2.Proximo()) {
                produto = new NFItens();
                produto.setNumero(consulta2.getBigDecimal("numero"));
                produto.setTipoPosto(consulta2.getString("Item"));
                produto.setCodPro(consulta2.getBigDecimal("Produto"));
                produto.setQtde(consulta2.getBigDecimal("Qtde"));
                produto.setValor_Un(consulta2.getBigDecimal("PRC_Unit"));
                produto.setValor_Tot(consulta2.getBigDecimal("PRC_Total"));
                produtos.add(produto);
            }
            consulta2.Close();
            List<NFItens> p;
            for (IntegracaoProtheus i : lista) {
                i.setMensagem_nota(i.getMensagem_nota().toUpperCase()
                        .replace("@COMPET", i.getCompet())
                        .replace("@DTVENC", i.getCond_pagto()));
                p = new ArrayList<>();;
                for (NFItens it : produtos) {
                    if (i.getNumero().replace(".0", "").equals(it.getNumero().toBigInteger().toString())) {
                        p.add(it);
                    }
                }
                i.setProdutos(p);
            }

            return lista;
        } catch (Exception e) {
            throw new Exception("IntegracaoProtheusDao.getXML - " + e.getMessage());
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class PTTStatus {

    private BigDecimal Sequencia;
    private int Status;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public int getStatus() {
        return Status;
    }

    public void setStatus(int Status) {
        this.Status = Status;
    }
}

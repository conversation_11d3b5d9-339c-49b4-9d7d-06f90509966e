package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class CxFEntradasDTO {

    private String sequencia;
    private String codCli1;
    private String hora1;
    private String ER;
    private String tipoSrv;
    private String parada;
    private String NRed;
    private String flag_Excl;
    private String operExcl;
    private String observ;
    private String pedidoDst;
    private String valor;
    private String guiaMax;

    public CxFEntradasDTO() {
    }

    public CxFEntradasDTO(CxFEntradasDTO original) {
        sequencia = original.getSequencia();
        codCli1 = original.getCodCli1();
        hora1 = original.getHora1();
        ER = original.getER();
        tipoSrv = original.getTipoSrv();
        parada = original.getParada();
        NRed = original.getNRed();
        flag_Excl = original.getFlag_Excl();
        operExcl = original.getOperExcl();
        observ = original.getObserv();
        pedidoDst = original.getPedidoDst();
        valor = original.getValor();
        guiaMax = original.getGuiaMax();
    }

    public String getSequencia() {
        return sequencia;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public String getCodCli1() {
        return codCli1;
    }

    public void setCodCli1(String codCli1) {
        this.codCli1 = codCli1;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public String getTipoSrv() {
        return tipoSrv;
    }

    public void setTipoSrv(String tipoSrv) {
        this.tipoSrv = tipoSrv;
    }

    public String getParada() {
        return parada;
    }

    public void setParada(String parada) {
        this.parada = parada;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getFlag_Excl() {
        return flag_Excl;
    }

    public void setFlag_Excl(String flag_Excl) {
        this.flag_Excl = flag_Excl;
    }

    public String getOperExcl() {
        return operExcl;
    }

    public void setOperExcl(String operExcl) {
        this.operExcl = operExcl;
    }

    public String getObserv() {
        return observ;
    }

    public void setObserv(String observ) {
        this.observ = observ;
    }

    public String getPedidoDst() {
        return pedidoDst;
    }

    public void setPedidoDst(String pedidoDst) {
        this.pedidoDst = pedidoDst;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getGuiaMax() {
        return guiaMax;
    }

    public void setGuiaMax(String guiaMax) {
        this.guiaMax = guiaMax;
    }

}

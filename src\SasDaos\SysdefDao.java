/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Sysdef;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SysdefDao {

    public List<Sysdef> getSysdef(String sCriterio, Persistencia persistencia) throws Exception {
        List<Sysdef> lSysdef = new ArrayList();
        String sql;
        try {
            if ("".equals(sCriterio) || sCriterio == null) {
                sql = "select top 50 SysDefGrp.codgrupo, SysDef.codigo, SysDef.Subsistema "
                        + "from SysDef "
                        + "inner join SysDefGrp on SysDef.codigo = SysDefGrp.codigo ";
            } else {
                sql = "select SysDefGrp.codgrupo, SysDef.codigo, SysDef.Subsistema "
                        + "from SysDef "
                        + "inner join SysDefGrp on SysDef.codigo = SysDefGrp.codigo "
                        + "where " + sCriterio;
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                Sysdef pl = new Sysdef();
                pl.setCodGrupo(consult.getString("codgrupo"));
                pl.setCodigo(consult.getString("codigo").replace(".0", ""));
                pl.setSubSistema(consult.getString("Subsistema"));
                lSysdef.add(pl);
            }
            consult.Close();
            return lSysdef;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar SasPW - " + e.getMessage());
        }
    }
}

/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
*/
/* 
    Created on : 14-Aug-2020, 12:33:25
    Author     : SASWRichard
*/

.pontoIrregular{
    position: absolute;
    color: red;
}

div.resumoBatida {
    width: 100%;
    border-collapse: collapse;
    margin: 0 auto;
}
.divTable.resumoBatida .divTableBody .divTableCell {
    font-size: 13px;
}
.divTable.resumoBatida .divTableBody .divTableRow:nth-child(odd) {
    background: #D0E4F5;
}
.divTable.resumoBatida .divTableRow:nth-child(even) {
    background: #D0E4F5;
}
.divTable.resumoBatida .divTableHeading .divTableHead {
    font-size: 15px;
    color: steelblue;
}
.resumoBatida .tableFootStyle {
    font-size: 14px;
    font-weight: normal;
}
.resumoBatida .tableFootStyle {
    font-size: 14px;
}
/* DivTable.com */
.divTable{ display: table; }
.divTableRow { display: table-row; }
.divTableHeading { display: table-header-group;}
.divTableCell, .divTableHead { display: table-cell;}
.divTableHeading { display: table-header-group;}
.divTableFoot { display: table-footer-group;}
.divTableBody { display: table-row-group;}

#btnLogin {
    display:inline-block;
    color:#444;
    border:1px solid #CCC;
    background:#DDD;
    box-shadow: 0 0 5px -1px rgba(0,0,0,0.2);
    cursor:pointer;
    vertical-align:middle;
    max-width: 100px;
    padding: 5px;
    text-align: center;
}

#btnLogin:active {
    color:red;
    box-shadow: 0 0 5px -1px rgba(0,0,0,0.6);
}

.alert {
    padding: 20px;
    background-color: #f44336;
    color: white;
    opacity: 1;
    transition: opacity 0.6s;
    margin-bottom: 15px;
    position: absolute;
    display: none; 
    width: 95%;
    max-width: 80vw;
    margin: auto;
    right: 0;
    left: 0;
}

.closebtn {
    margin-left: 15px;
    color: white;
    font-weight: bold;
    float: right;
    font-size: 22px;
    line-height: 20px;
    cursor: pointer;
    transition: 0.3s;
}

.closebtn:hover {
    color: black;
}

.DadosCabecalhoPonto{
    margin: 0;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.DadosCabecalhoPonto img{
    max-height: 80px;
    margin-right: 8px;
}

.DadosCabecalhoPonto tr:first-child td,
.DadosCabecalhoPonto tr:nth-child(5) td{
    text-transform: uppercase;
    font-weight: bold !important;
}

.DadosCabecalhoPonto tr:nth-child(3) td{
    color: darkred !important;
}

.divTopoBatida{
    border-bottom: thin solid #DDD;
}

.batida-saida, .batida-entrada{
    color: #FFF !important;
    border-radius: 30px;
    text-align: center;
    font-weight: bold !important;
}

.DadosCabecalhoPonto tr td{
    padding: 2px !important;
}

.batida-saida{
    background-color: red !important;
}

.batida-entrada{
    background-color: forestgreen !important;
}

.map{
    width:100%;
    height: 250px;
    padding: 8px;
}

.DetalhesPonto {
    table-layout:fixed;
    height:100%;
}

.DetalhesPonto tr{
    height:1px;
}

.DetalhesPonto tr:last-child{
    vertical-align: baseline;
    height:auto;
}

.DetalhesPonto tr td{
    width: 50% !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 10pt;
}

.DetalhesPonto tr:first-child td{
    font-size: 14pt;
    color: steelblue
}

@media only screen and (max-width: 768px) and (min-width: 10px) {
    .DetalhesPonto tr td{
        max-width: 110px !important;
    }
}

svg{
    width: 200px;
    height: 200px;
    position: absolute; /*Can also be `fixed`*/
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    /*Solves a problem in which the content is being cut when the div is smaller than its' wrapper:*/
    max-width: 100%;
    max-height: 100%;
    overflow: hidden;
}

#content{
    position: fixed;
    top: 0;
    width: 100vw;
    background: #ecf0f5;
}

#mensageErro{
    font-size: 20px;
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
}
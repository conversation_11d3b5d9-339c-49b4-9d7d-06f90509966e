package br.com.sasw.pacotesuteis.sasbeans.formatadas;

/**
 *
 * <AUTHOR>
 */
public class TesCofresResClientesFormatada {

    private String Data;
    private String DiaSem;
    private String DiaSemT;
    private String VlrTotalCred;
    private String VlrDepProxDU;
    private String CodCofre;
    private String Nred;
    private String Endereco;
    private String Bairro;
    private String Cidade;
    private String Estado;

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getDiaSem() {
        return DiaSem;
    }

    public void setDiaSem(String DiaSem) {
        this.DiaSem = DiaSem;
    }

    public String getDiaSemT() {
        return DiaSemT;
    }

    public void setDiaSemT(String DiaSemT) {
        this.DiaSemT = DiaSemT;
    }

    public String getVlrTotalCred() {
        return VlrTotalCred;
    }

    public void setVlrTotalCred(String VlrTotalCred) {
        this.VlrTotalCred = VlrTotalCred;
    }

    public String getVlrDepProxDU() {
        return VlrDepProxDU;
    }

    public void setVlrDepProxDU(String VlrDepProxDU) {
        this.VlrDepProxDU = VlrDepProxDU;
    }

    public String getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        this.CodCofre = CodCofre;
    }

    public String getNred() {
        return Nred;
    }

    public void setNred(String Nred) {
        this.Nred = Nred;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getEstado() {
        return Estado;
    }

    public void setEstado(String Estado) {
        this.Estado = Estado;
    }
}

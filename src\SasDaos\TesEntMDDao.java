package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesEntMD;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesEntMDDao {

    public List<TesEntMD> listaTodasComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesEntMD> retorno = new ArrayList<>();
        try {
            String sql = " SELECT codigo, sum(qtde) qtde "
                    + " FROM TesEntMD "
                    + " WHERE guia = ? AND serie = ? "
                    + " group by codigo ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            TesEntMD tesEntMD;
            while (consulta.Proximo()) {
                tesEntMD = new TesEntMD();
                tesEntMD.setCodigo(consulta.getString("codigo"));
                tesEntMD.setQtde(consulta.getString("qtde"));
                retorno.add(tesEntMD);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list composições para guia - " + e.getMessage());
        }
    }

    /**
     * Troca a serie da guia em TesEntrada
     *
     * @param persistencia - conexão ao banco de dados
     * @param tesentmd - Guia em tesentrada Obrigatório - guia, série
     * @param novaserie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, TesEntMD tesentmd, String novaserie) throws Exception {
        try {
            String sql;
            sql = "update TesEntMD set Serie = ?"
                    + " where Guia = ?"
                    + " and Serie =  ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(novaserie);
            consulta.setString(tesentmd.getGuia().toPlainString());
            consulta.setString(tesentmd.getSerie());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao trocar serie da guia em TesEntMD - " + e.getMessage());
        }
    }

    /**
     * Lista composicoes
     *
     * @param guia
     * @param serie
     * @param docto
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesEntMD> ListaComposicao(String guia, String serie, String docto, Persistencia persistencia) throws Exception {
        List<TesEntMD> composicaoMD = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesEntMD"
                    + " Where Guia = ?"
                    + "  and Serie = ?"
                    + "  and docto = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.setString(docto);
            consult.select();
            TesEntMD tesentdn;
            while (consult.Proximo()) {
                tesentdn = new TesEntMD();
                tesentdn.setCodigo(consult.getString("codigo"));
                tesentdn.setQtde(consult.getString("qtde"));
                composicaoMD.add(tesentdn);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list tesentmd - " + e.getMessage());
        }
        return composicaoMD;
    }

    /**
     * Lista a sangria por guia, serie e docto
     *
     * @param guia Número da guia
     * @param serie Valor da série
     * @param docto Valor do docto
     * @param persistencia Conexão com o banco
     * @return Retorna um lista
     * @throws Exception
     */
    public List<TesEntMD> ListaTesEntMD(BigDecimal guia, String serie, String docto, Persistencia persistencia) throws Exception {
        List<TesEntMD> retorno = new ArrayList<>();
        TesEntMD tesEntMD;

        try {
            String sql = "SELECT * FROM TesEntMD WHERE guia = ? AND serie = ? AND docto = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            consulta.setString(docto);
            consulta.select();

            while (consulta.Proximo()) {
                tesEntMD = new TesEntMD();
                tesEntMD.setGuia(consulta.getString("Guia"));
                tesEntMD.setSerie(consulta.getString("Serie"));
                tesEntMD.setCodigo(consulta.getString("Codigo"));
                tesEntMD.setDocto(consulta.getString("Docto"));
                tesEntMD.setQtde(consulta.getString("Qtde"));
                tesEntMD.setValor(consulta.getString("Valor"));
                //tesEntMD.setIDK7(consulta.getString("IDK7"));
                tesEntMD.setOperador(consulta.getString("Operador"));
                tesEntMD.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                tesEntMD.setHr_Alter(consulta.getString("Hr_Alter"));

                retorno.add(tesEntMD);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao trocar serie da guia em TesEntMD - " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Exclui a composição da sangria na tabela TesEntMD
     *
     * @param guia Valor da guia
     * @param serie Valor da série
     * @param docto Número da sangria
     * @param codigo Código da moeda
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void ExcluirComposicao(String guia, String serie, String docto, String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "delete from tesentmd "
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and codigo = ?"
                    + " and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codigo);
            consulta.setString(docto);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao excluir de tesentmd - " + e.getMessage());
        }
    }

    /**
     * Insere a composição da sangria na tabela TesEntMD
     *
     * @param guia valor da guia
     * @param serie valor da série
     * @param docto número da sangria
     * @param codigo código da moeda
     * @param qtde quantidade de moedas
     * @param valor valor da moeda
     * @param operador Operador da sangria
     * @param persistencia Conexão com o banco
     * @throws Exception
     */
    public void InserirComposicao(String guia, String serie, String docto, String codigo, BigDecimal qtde, BigDecimal valor, String operador, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "insert into tesentmd (guia, serie, codigo, docto, qtde, valor, operador, dt_alter, hr_alter)"
                    + " values (?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codigo);
            consulta.setString(docto);
            consulta.setBigDecimal(qtde);
            consulta.setBigDecimal(valor);
            consulta.setString(operador);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir composicao em tesentmd - " + e.getMessage());
        }
    }

    /**
     * Lista composicoes
     *
     * @param guia Número da guiaa
     * @param serie Número da série
     * @param persistencia Conexão com o banco
     * @return Lista as composições da tabela TesEntMD
     * @throws Exception
     */
    public List<TesEntMD> ListaComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesEntMD> composicaoDN = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesEntMD"
                    + " Where Guia = ?"
                    + "  and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            TesEntMD tesentdn;
            while (consult.Proximo()) {
                tesentdn = new TesEntMD();
                tesentdn.setCodigo(consult.getString("codigo"));
                tesentdn.setQtde(consult.getString("qtde"));
                composicaoDN.add(tesentdn);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list tesentmd - " + e.getMessage());
        }
        return composicaoDN;
    }
}

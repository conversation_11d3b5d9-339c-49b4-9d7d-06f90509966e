/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import SasBeans.Rotas;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorRota")
public class ConversorRotas implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Rotas rotas = new Rotas();
        rotas.setRota(value);

        return rotas;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {

        try {
            return ((Rotas) value).getRota();
        } catch (Exception e) {
            return null;
        }
    }
}

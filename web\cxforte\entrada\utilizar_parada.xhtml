<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <p:dialog header="#{localemsgs.Desejautilizaressaparada}?"
              widgetVar="dlgParadaHorario"
              minHeight="40"
              resizable="false"
              modal="true"
              >
        <h:form id="utilizarParada">
            <f:facet name="header">
                <h:outputText value="#{localemsgs.Confirmacao}" style="color:black" />
            </f:facet>

            <p:panel
                id="panel"
                class="modalSolicitacao"
                >

                <h:outputText
                    value="#{localemsgs.Parada} #{cxForteEntrada.listaHorasRtPercIndex + 1} #{localemsgs.De} #{cxForteEntrada.listaHorasRtPerc.size()}"
                    style="width: 100%;"
                    />
                <h:outputText
                    value="#{localemsgs.Horario}: #{cxForteEntrada.listaHorasRtPercAtual.hora1}"
                    style="width: 100%;"
                    />
                <h:outputText
                    value="#{localemsgs.Deseja_utilizar_esta_parada}?"
                    style="width: 100%;"
                    />

                <p:commandButton
                    action="#{cxForteEntrada.utilizarHoraParada()}"
                    update="msgs @form"
                    styleClass="botao"
                    value="#{localemsgs.Sim}"
                    />
                <p:commandButton
                    action="#{cxForteEntrada.naoUtilizarHoraParada()}"
                    update="msgs @form"
                    styleClass="botao"
                    value="#{localemsgs.Nao}"
                    />
            </p:panel>
        </h:form>
    </p:dialog>

</ui:composition>
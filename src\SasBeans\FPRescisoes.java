package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FPRescisoes {

    private BigDecimal CodFil;
    private BigDecimal CodMovFP;
    private BigDecimal Matr;
    private String Iniciativa;
    private String Tipo;
    private LocalDate DtAvisoIni;
    private LocalDate DtAvisoFim;
    private LocalDate DtDemissao;
    private LocalDate DtContrato;
    private String Motivo;
    private BigDecimal SaldoFGTS;
    private String Mensagem;
    private String Obs;
    private String CodMovSEFIP;
    private String CodAfast;
    private String CatTrab;
    private LocalDate DtPagto;
    private String Travar;
    private String MediasAuto;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getCodMovFP() {
        return CodMovFP;
    }

    public void setCodMovFP(String CodMovFP) {
        try {
            this.CodMovFP = new BigDecimal(CodMovFP);
        } catch (Exception e) {
            this.CodMovFP = new BigDecimal("0");
        }
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getIniciativa() {
        return Iniciativa;
    }

    public void setIniciativa(String Iniciativa) {
        this.Iniciativa = Iniciativa;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public LocalDate getDtAvisoIni() {
        return DtAvisoIni;
    }

    public void setDtAvisoIni(LocalDate DtAvisoIni) {
        this.DtAvisoIni = DtAvisoIni;
    }

    public LocalDate getDtAvisoFim() {
        return DtAvisoFim;
    }

    public void setDtAvisoFim(LocalDate DtAvisoFim) {
        this.DtAvisoFim = DtAvisoFim;
    }

    public LocalDate getDtDemissao() {
        return DtDemissao;
    }

    public void setDtDemissao(LocalDate DtDemissao) {
        this.DtDemissao = DtDemissao;
    }

    public LocalDate getDtContrato() {
        return DtContrato;
    }

    public void setDtContrato(LocalDate DtContrato) {
        this.DtContrato = DtContrato;
    }

    public String getMotivo() {
        return Motivo;
    }

    public void setMotivo(String Motivo) {
        this.Motivo = Motivo;
    }

    public BigDecimal getSaldoFGTS() {
        return SaldoFGTS;
    }

    public void setSaldoFGTS(String SaldoFGTS) {
        try {
            this.SaldoFGTS = new BigDecimal(SaldoFGTS);
        } catch (Exception e) {
            this.SaldoFGTS = new BigDecimal("0");
        }
    }

    public String getMensagem() {
        return Mensagem;
    }

    public void setMensagem(String Mensagem) {
        this.Mensagem = Mensagem;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getCodMovSEFIP() {
        return CodMovSEFIP;
    }

    public void setCodMovSEFIP(String CodMovSEFIP) {
        this.CodMovSEFIP = CodMovSEFIP;
    }

    public String getCodAfast() {
        return CodAfast;
    }

    public void setCodAfast(String CodAfast) {
        this.CodAfast = CodAfast;
    }

    public String getCatTrab() {
        return CatTrab;
    }

    public void setCatTrab(String CatTrab) {
        this.CatTrab = CatTrab;
    }

    public LocalDate getDtPagto() {
        return DtPagto;
    }

    public void setDtPagto(LocalDate DtPagto) {
        this.DtPagto = DtPagto;
    }

    public String getTravar() {
        return Travar;
    }

    public void setTravar(String Travar) {
        this.Travar = Travar;
    }

    public String getMediasAuto() {
        return MediasAuto;
    }

    public void setMediasAuto(String MediasAuto) {
        this.MediasAuto = MediasAuto;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

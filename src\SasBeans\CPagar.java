package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class CPagar {

    private BigDecimal Sequencia;
    private BigDecimal CodFil;
    private BigDecimal OP;
    private BigDecimal CodFornec;
    private String Fornecedor;
    private BigDecimal NF;
    private BigDecimal Pedido;
    private int TipoTit;
    private int FormaPgto;
    private String DtVenc;
    private String DtPrevPg;
    private String Compet;
    private String CompetPg;
    private String DtPagto;
    private BigDecimal Valor;
    private String CodBarras;
    private String Obs;
    private int ContaFin;
    private String CCusto;
    private BigDecimal ValorPago;
    private BigDecimal Juros;
    private BigDecimal Desconto;
    private BigDecimal CodConta;
    private BigDecimal Cheque;
    private String Banco;
    private String Agencia;
    private String ContaC;
    private String Situacao;
    private BigDecimal SeqExp;
    private BigDecimal CodPessoaAutPg;
    private LocalDate Dt_AutPg;
    private String Hr_AutPg;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    
    private Double TotalValor;
    private String DiasAtraso;

    public CPagar() {
        this.Sequencia = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.OP = new BigDecimal("0");
        this.CodFornec = new BigDecimal("0");
        this.Fornecedor = "";
        this.NF = new BigDecimal("0");
        this.Pedido = new BigDecimal("0");
        this.TipoTit = 0;
        this.FormaPgto = 0;
        this.DtVenc = "";
        this.DtPrevPg = "";
        this.Compet = "";
        this.CompetPg = "";
        this.DtPagto = "";
        this.Valor = new BigDecimal("0");
        this.CodBarras = "";
        this.Obs = "";
        this.ContaFin = 0;
        this.CCusto = "";
        this.ValorPago = new BigDecimal("0");
        this.Juros = new BigDecimal("0");
        this.Desconto = new BigDecimal("0");
        this.CodConta = new BigDecimal("0");
        this.Cheque = new BigDecimal("0");
        this.Banco = "";
        this.Agencia = "";
        this.ContaC = "";
        this.Situacao = "";
        this.SeqExp = new BigDecimal("0");
        this.CodPessoaAutPg = new BigDecimal("0");
        this.Dt_AutPg = LocalDate.now();
        this.Hr_AutPg = "";
        this.Operador = "";
        this.Dt_Alter = "";
        this.Hr_Alter = "";
        this.TotalValor = 0.0;
        this.DiasAtraso = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("1");
        }
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("1");
        }
    }

    public BigDecimal getOP() {
        return OP;
    }

    public void setOP(String OP) {
        try {
            this.OP = new BigDecimal(OP);
        } catch (Exception e) {
            this.OP = new BigDecimal("1");
        }
    }

    public BigDecimal getCodFornec() {
        return CodFornec;
    }

    public void setCodFornec(String CodFornec) {
        try {
            this.CodFornec = new BigDecimal(CodFornec);
        } catch (Exception e) {
            this.CodFornec = new BigDecimal("1");
        }
    }

    public String getFornecedor() {
        return Fornecedor;
    }

    public void setFornecedor(String Fornecedor) {
        this.Fornecedor = Fornecedor;
    }

    public BigDecimal getNF() {
        return NF;
    }

    public void setNF(String NF) {
        try {
            this.NF = new BigDecimal(NF);
        } catch (Exception e) {
            this.NF = new BigDecimal("0");
        }
    }

    public BigDecimal getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        try {
            this.Pedido = new BigDecimal(Pedido);
        } catch (Exception e) {
            this.Pedido = new BigDecimal("1");
        }
    }

    public int getTipoTit() {
        return TipoTit;
    }

    public void setTipoTit(int TipoTit) {
        this.TipoTit = TipoTit;
    }

    public int getFormaPgto() {
        return FormaPgto;
    }

    public void setFormaPgto(int FormaPgto) {
        this.FormaPgto = FormaPgto;
    }

    public String getDtVenc() {
        return DtVenc;
    }

    public void setDtVenc(String DtVenc) {
        this.DtVenc = DtVenc;
    }

    public String getDtPrevPg() {
        return DtPrevPg;
    }

    public void setDtPrevPg(String DtPrevPg) {
        this.DtPrevPg = DtPrevPg;
    }

    public String getCompet() {
        return Compet;
    }

    public void setCompet(String Compet) {
        this.Compet = Compet;
    }

    public String getCompetPg() {
        return CompetPg;
    }

    public void setCompetPg(String CompetPg) {
        this.CompetPg = CompetPg;
    }

    public String getDtPagto() {
        return DtPagto;
    }

    public void setDtPagto(String DtPagto) {
        this.DtPagto = DtPagto;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public String getCodBarras() {
        return CodBarras;
    }

    public void setCodBarras(String CodBarras) {
        this.CodBarras = CodBarras;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public int getContaFin() {
        return ContaFin;
    }

    public void setContaFin(int ContaFin) {
        this.ContaFin = ContaFin;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public BigDecimal getValorPago() {
        return ValorPago;
    }

    public void setValorPago(String ValorPago) {
        try {
            this.ValorPago = new BigDecimal(ValorPago);
        } catch (Exception e) {
            this.ValorPago = new BigDecimal("0");
        }
    }

    public BigDecimal getJuros() {
        return Juros;
    }

    public void setJuros(String Juros) {
        try {
            this.Juros = new BigDecimal(Juros);
        } catch (Exception e) {
            this.Juros = new BigDecimal("0");
        }
    }

    public BigDecimal getDesconto() {
        return Desconto;
    }

    public void setDesconto(String Desconto) {
        try {
            this.Desconto = new BigDecimal(Desconto);
        } catch (Exception e) {
            this.Desconto = new BigDecimal("1");
        }
    }

    public BigDecimal getCodConta() {
        return CodConta;
    }

    public void setCodConta(String CodConta) {
        try {
            this.CodConta = new BigDecimal(CodConta);
        } catch (Exception e) {
            this.CodConta = new BigDecimal("0");
        }
    }

    public BigDecimal getCheque() {
        return Cheque;
    }

    public void setCheque(String Cheque) {
        try {
            this.Cheque = new BigDecimal(Cheque);
        } catch (Exception e) {
            this.Cheque = new BigDecimal("0");
        }
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getContaC() {
        return ContaC;
    }

    public void setContaC(String ContaC) {
        this.ContaC = ContaC;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public BigDecimal getSeqExp() {
        return SeqExp;
    }

    public void setSeqExp(String SeqExp) {
        try {
            this.SeqExp = new BigDecimal(SeqExp);
        } catch (Exception e) {
            this.SeqExp = new BigDecimal("0");
        }
    }

    public BigDecimal getCodPessoaAutPg() {
        return CodPessoaAutPg;
    }

    public void setCodPessoaAutPg(String CodPessoaAutPg) {
        try {
            this.CodPessoaAutPg = new BigDecimal(CodPessoaAutPg);
        } catch (Exception e) {
            this.CodPessoaAutPg = new BigDecimal(0);
        }
    }

    public LocalDate getDt_AutPg() {
        return Dt_AutPg;
    }

    public void setDt_AutPg(LocalDate Dt_AutPg) {
        this.Dt_AutPg = Dt_AutPg;
    }

    public String getHr_AutPg() {
        return Hr_AutPg;
    }

    public void setHr_AutPg(String Hr_AutPg) {
        this.Hr_AutPg = Hr_AutPg;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public Double getTotalValor() {
        return TotalValor;
    }

    public void setTotalValor(Double TotalValor) {
        this.TotalValor = TotalValor;
    }

    public String getDiasAtraso() {
        return DiasAtraso;
    }

    public void setDiasAtraso(String DiasAtraso) {
        this.DiasAtraso = DiasAtraso;
    }

}

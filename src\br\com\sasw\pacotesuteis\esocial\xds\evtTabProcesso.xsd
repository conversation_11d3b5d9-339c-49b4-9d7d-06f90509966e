﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTabProcesso/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTabProcesso/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTabProcesso">
                    <xs:annotation>
                        <xs:documentation>Evento Tabela de Processos</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoProcesso">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Processo</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideProcesso" type="TIdeProcesso">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do Processo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosProc" type="TDadosProc">
                                                            <xs:annotation>
                                                                <xs:documentation>Dados do processo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideProcesso" type="TIdeProcesso">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao do Processo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosProc" type="TDadosProc">
                                                            <xs:annotation>
                                                                <xs:documentation>Dados do processo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideProcesso" type="TIdeProcesso">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do Processo que sera excluido</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeProcesso">
        <xs:annotation>
            <xs:documentation>Informacoes de Identificacao do Processo e Validade das Informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Processo</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do Processo</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="21"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosProc">
        <xs:annotation>
            <xs:documentation>Dados do processo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indAutoria" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Autoria da Acao Judicial</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indMatProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo da matéria do processo</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d{1,2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="observacao" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Observacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="255"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dadosProcJud" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacoes Complementares do Processo Judicial</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="ufVara">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Identificacao da UF da Secao Judiciaria</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="AC"/>
                                    <xs:enumeration value="AL"/>
                                    <xs:enumeration value="AP"/>
                                    <xs:enumeration value="AM"/>
                                    <xs:enumeration value="BA"/>
                                    <xs:enumeration value="CE"/>
                                    <xs:enumeration value="DF"/>
                                    <xs:enumeration value="ES"/>
                                    <xs:enumeration value="GO"/>
                                    <xs:enumeration value="MA"/>
                                    <xs:enumeration value="MT"/>
                                    <xs:enumeration value="MS"/>
                                    <xs:enumeration value="MG"/>
                                    <xs:enumeration value="PA"/>
                                    <xs:enumeration value="PB"/>
                                    <xs:enumeration value="PR"/>
                                    <xs:enumeration value="PE"/>
                                    <xs:enumeration value="PI"/>
                                    <xs:enumeration value="RJ"/>
                                    <xs:enumeration value="RN"/>
                                    <xs:enumeration value="RS"/>
                                    <xs:enumeration value="RO"/>
                                    <xs:enumeration value="RR"/>
                                    <xs:enumeration value="SC"/>
                                    <xs:enumeration value="SP"/>
                                    <xs:enumeration value="SE"/>
                                    <xs:enumeration value="TO"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codMunic">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Municipio</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{7}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="idVara">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo de Identificacao da Vara</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{1,4}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoSusp" minOccurs="0" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Informacoes de suspensao de exigibilidade de tributos</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="codSusp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{1,14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="indSusp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Indicativo de suspensao da exigibilidade</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="\d{2}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="dtDecisao">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Data da decisao, sentenca ou despacho</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:date">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="indDeposito">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>DepOsito do Montante Integral</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="[N|S]"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CataEquip {

    private String Serial;
    private String IDEquip;
    private String Geo;
    private String Address;
    private String Status;
    private String Data;
    private String Hora;
    private String Envio;
    private String Dt_Envio;
    private String Hr_Envio;

    private String Email;
    private String CodFil;
    private String Nome;
    private String CodCli;

    public String getSerial() {
        return Serial;
    }

    public void setSerial(String Serial) {
        this.Serial = Serial;
    }

    public String getIDEquip() {
        return IDEquip;
    }

    public void setIDEquip(String IDEquip) {
        this.IDEquip = IDEquip;
    }

    public String getGeo() {
        return Geo;
    }

    public void setGeo(String Geo) {
        this.Geo = Geo;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String Address) {
        this.Address = Address;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getEnvio() {
        return Envio;
    }

    public String getDt_Envio() {
        return Dt_Envio;
    }

    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    public String getHr_Envio() {
        return Hr_Envio;
    }

    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    public void setEnvio(String Envio) {
        this.Envio = Envio;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.Serial);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CataEquip other = (CataEquip) obj;
        if (!Objects.equals(this.Serial, other.Serial)) {
            return false;
        }
        return true;
    }
}

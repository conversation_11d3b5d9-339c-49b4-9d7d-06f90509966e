Portugues=Espa\u00f1ol
Ingles=Espa\u00f1ol
Salve=Guardar
Nnacionalidade=Nacionalidad
Titulo=JSF
Senha=Contrase\u00f1a:
Saudacao=Bienvenido a SatMob
Manual=Manual del usuario
PrimeiroAcesso=Primer acceso
ValidarCC=Validar contraheque
CandidateSe=Candidato
EsqueciSenha=<PERSON><PERSON><PERSON>\u00e9 la contrase\u00f1a
AcessoSistema=Acceso al sistema
Espanhol=Espa\u00f1ol
Entrar=Entrar
Voltar=Volver al inicio
Empresa=Empresa
SelecioneEmpresa=Seleccione una empresa
Usuario=Usuario
FolhaPonto=Hoja de punto
ContraCheque=Contracheque
TrocarSenha=Cambiar contrase\u00f1a
SelecionePeriodo=Seleccione un Per\u00edodo
SelecioneData=Seleccione una fecha
DigiteSenha=Introduzca una contrase\u00f1a
Inicio=Inicio
PropostaEmprego=Propuesta de empleo
SenhaAtual=contrase\u00f1a actual
NovaSenha=Nueva contrase\u00f1a
confirmarNovaSenha=Confirmar nueva contrase\u00f1a
RecuperarSenha=Recuperar contrase\u00f1a
FalhaCarregarEmpresas=Falla al cargar empresas
UsuarioPermissaoAcessarFiliais=Usuario sin permiso para acceder a las Sucursales
UsuarioPermissaoContateResponsavelSistema=Usuario sin permiso, p\u00f3ngase en contacto con el responsable del sistema
UsuarioInativo=Usuario inactivo
SenhaIncorreta=Contrase\u00f1a incorrecta
EmailInvalido=Correo electr\u00f3nico no v\u00e1lido
dadosNaoConfereTenteNovamente=Los datos no confieren, vuelva a intentarlo
emaiInexistenteUsuarioInativo=Correo electr\u00f3nico inexistente, o usuario inactivo
SenhaAlteradaSucesso=Contrase\u00f1a alterada con \u00e9xito
SenhaAtualNaoConfere=Contrase\u00f1a actual no confiere
camposSenhaConfirmarSenhaPrecisamIguais=Campos: Contrase\u00f1a y Confirmar Contrase\u00f1a, deben ser iguales
SenhaCadastradaSucesso=Contrase\u00f1a registrada con \u00e9xito, haga clic en Inicio.
UsuarioPossuiAcessoFavorRecuperarSenha=El usuario ya tiene acceso, por favor recuperar contrase\u00f1a
CodigoPreenchidoErro=C\u00f3digo cumplimentado con error
Filial=Sucursal
SelecioneFilial=Seleccione una Sucursal
SemSenha=Usuario sin contrase\u00f1a, haga clic en Primer acceso.
Cagedonline=CAGED Online
Caged=CAGED
RecursosHumanos=Recursos humanos
FGTS=FGTS
SeguroDesemprego=Seguro de desempleo
Tesouraria=Tesorer\u00eda
Cofre=Caja fuerte
CadastrarPessoa=Registrar Persona
Idioma=Idioma
UsuarioEmpresa=Usuario / Empresas
Fechar=Cerrar
Adicionar=A\u00f1adir
Gerar=Generar
Novo=Nuevo
AlterarSenha=Cambiar contrase\u00f1a
ExcluidoSucesso=Exclusi\u00f3n hecha con \u00e9xito
UsuarioSenha=Usuarios / Contrase\u00f1as
UsuarioFiliais=Usuario / Sucursales
Filtrar=Filtrar
RemoveFiltro=Quitar
login.falhageral<message>login.usuarioerrado=Usuario no encontrado
login.usuariosemacessocadastrado=No hay nuevos mensajes
Operacoes=Operaciones
Configuracoes=Configuraci\u00f3n
Cadastros=Registros
Relatorios=Informes
Sair=Salir
EscalaDia=Escala del d\u00eda
Supervisao=Supervisi\u00f3n
Pessoas=Personas
Funcionarios=Empleados
Clientes=Clientes
PostoServicos=Puestos de servicio
Filiais=Sucursales
Usuarios=Usuarios
Importador=Importador
Exportador=Exportador
CodFil=Sucursal
Secao=Secci\u00f3n
CodCli=CodCli
Local=Puesto de Servicio
Situacao=Situaci\u00f3n
Dt_Situacao=Fecha Situaci\u00f3n
InterfExt=InterfExt
Operador=Operador
Dt_Alter=Fecha de Cambio
Hr_Alter=Hora de Cambio
Cadastrar=Registrar
Editar=Editar
EditarPessoa=Editar Personas
PstServ=Puestos de servicio
Funcion=Empleados
Matr=Id. Empleado
Nome=Nombre
CPF=CURP
RG=Documento de identificaci\u00f3n
Dt_Nasc=Fecha de nacimiento
Dt_Admis=Fecha de admisi\u00f3n
CodPessoaWeb=CodPessoaWeb
Insc_Munic=Registro Municipal
Excluir=Excluir
SelecionarCliente=Seleccione el tipo de cliente
Obrigatorio=Campo obligatorio no insertado
ValorAtual=Valor actual
NovoValor=Nuevo valor
Nome_Guer=Nombre identificaci\u00f3n
Posto=Puesto
Dt_Situac=Fecha de la situaci\u00f3n
NRed=ID Rapida
Ende=Ende
Bairro=Colonia
Cidade=Ciudad
Estado=Estado
CEP=C\u00f3digo postal
Fone1=Tel\u00e9fono1
Fone2=Tel\u00e9fono2
CGC=CNPJ
IE=Inscripci\u00f3n Estadual
Latitude=Latitud
Codigo=C\u00f3digo
Longitude=Longitud
Pesquisar=B\u00fasqueda
Cliente=Cliente
Data=Fecha
Hora=Hora
Supervisoes=Supervisi\u00f3n
Detalhes=Detalles
Distancia=Distancia
Historico=Historia
TipoPosto=Tipo
TipoPostoDesc=Descripci\u00f3n Tipo
Resp=Respuesta
Qst=Cuesti\u00f3n
Descricao=Descripci\u00f3n
NomeCliente=Nombre Cliente
RazaoSocial=Raz\u00f3n social
Cadastro=Registro
UF=Entidad Federativa
QtdEntrevistas=Cantidad de Entrevistas
Qtd=Q.Ent
SupervisoesRecentes=Supervisi\u00f3n reciente
Entrevistas=Entrevistas
Checklist=Lista de verificaci\u00f3n
Galeria=Galer\u00eda
Funcion=Empleado
Supervisor=Supervisor
Horario=Horario
Agencia=Sucursal
NivelOP=NivelOP
NomeCompleto=Nombre completo
Motivo=Motivo
Grupo=Grupo
Confirmacao=Confirmaci\u00f3n
Pessoa=Personas
Grupos=Grupos de Usuarios
Permissoes=Permisos
Nivel=Nivel
Login=Inicio
Operacao=Operaci\u00f3n
Manutencao=Mantenimiento
Gerencia=Gerencia
Administrador=Administrador
Ativo=Activo
Bloqueado=Bloqueado
RGOrg=\u00d3rgano emisor
Obs=Observaciones
Funcao=Funci\u00f3n
Sexo=Sexo
Sistema=Sistema
Inclusao=Inclusi\u00f3n
Alteracao=Enmienda
Exclusao=Exclusi\u00f3n
Altura=Altura (cm)
Candidato=C - Candidato
Prestador=P - Prestador de servicios
Autonomo=A - Aut\u00f3nomo
Funcionario=F - Empleado
Diretor=D - Director
Socio=S - Socio
Visitante=V - Visitante
BBloqueado=B - Bloqueado
Visitanteweb=W - Visitante Web
Selecione=Seleccione
Peso=Peso
Masculino=Hombre
Feminino=Mujer
OOutros=O-No declarado
EditarFuncion=Editar Empleado
CadastrarFuncion=Registrar Empleado
CadastrarCliente=Registrar Cliente
EditarCliente=Editar cliente
CadastrarFilial=Registrar Sucursal
EditarFilial=Editar Sucursal
CadastrarUsuario=Registrar Usuario
EditarUsuario=Editar usuario
PessoaJuridica=Jur\u00eddica
PessoaFisica=F\u00edsica
QtdFotos=Qtd de Fotos
Endereco=Direcci\u00f3n
RotasSup=Rutas de Supervisi\u00f3n
Rota=Ruta
Hr_Largada=Hora de Largada
Hr_Chegada=Hora de llegada
Hr_IntIni=HrIntIni
Hr_IntFim=HrIntFim
Hr_Total=Total de horas
Viagem=Viajes
ATM=ATM
Bacen=BACEN
Aeroporto=Aeropuerto
Sequencia=Secuencia
Flag_Excl=Flag_Excl
TpVeic=Tipo Veh\u00edculo
Intervalo=Intervalo
Selecionar=Seleccionar
Parada=Parada
Hora1=Hora1
ER=ER
CodCli1=CodCli1
Regiao=Regi\u00f3n
CodCli2=CodCli2
DPar=DPAR
Hora1d=Hora1D
Valor=Monto
Pedido=Solicitud
OperIncl=OperIncl
Dt_Incl=Fecha de inclusi\u00f3n
Hr_Incl=Hr_Incl
OperExcl=OperExcl
Dt_Excl=Fecha de exclusi\u00f3n
Hr_Excl=Hr_Excl
Trajetos=Trayectos
Calcular=Calcular
CadastrarRota=Registrar Ruta
FotosLocal=Fotos del local
Tipo=Tipo
Mapa=Mapa
Contrato=Contrato
#Trajeto#Trajeto
CadastrarTrajeto=Registrar Trayecto
AtualizarCEP=\u00bfDesea actualizar la direcci\u00f3n?
Sim=S\u00ed
Nao=No
QtdPostos=Cantidad de puestos
CadastrarPstServ=Registrar Puesto de Servicio
PesquisarPstServ=Buscar Puestos
QtdSupervisoes=Cantidad de Supervisiones
SelecionePstServ=Seleccione un puesto
SemSupervisaoRecente=Sin supervisi\u00f3n m\u00e1s reciente
SemSupervisaoAntiga=Sin supervisi\u00f3n anterior
SelecioneSupervisao=Seleccione una Supervisi\u00f3n
SupervisaoAnterior=Supervisi\u00f3n Anterior
SupervisaoPosterior=Supervisi\u00f3n posterior
QtdUsuarios=Cantidad de usuarios
QtdPessoas=Cantidad de personas
QtdClientes=Cantidad de clientes
QtdFuncion=Cantidad de empleados
QtdRotas=Cantidad de Rutas
AdicionarFilial=A\u00f1adir a la lista
Email=Correo electr\u00f3nico
DigiteSenhaNovamente=Introduzca la contrase\u00f1a de nuevo
SenhaFraca=Contrase\u00f1a d\u00e9bil
SenhaBoa=Contrase\u00f1a:
SenhaForte=Contrase\u00f1a fuerte
SenhaSomenteNumerosMinimoSeis=La contrase\u00f1a debe contener s\u00f3lo n\u00fameros y tener al menos 6 caracteres
Corporativo=Corporativo
PesquisarFuncion=Buscar Empleado
TbVal=Cuestiones
QtdTbVal=Cantidad de Cuestiones
QtdFiliais=Cantidad de Sucursales
MostrarTodasFiliais=Mostrar todas las sucursales
SomenteAtivos=S\u00f3lo Activos
PesquisarCliente=B\u00fasqueda de clientes
PesquisarPessoa=B\u00fasqueda de Personas
PesquisarUsuario=Buscar usuario
OK=Aceptar
CompletarEndereco=Direcci\u00f3n autom\u00e1tica, por favor complete con informaci\u00f3n adicional.
Aviso=Advertencia
desejaSeguir=\u00bfDesea seguir con esa acci\u00f3n?
SemEnderecoCadastrado=Sin Direcci\u00f3n
EdicaoSucesso=Edici\u00f3n grabada con \u00e9xito
CadastroSucesso=Registro hecho con \u00e9xito
SelecioneCliente=Seleccione un cliente
SelecioneFuncion=Seleccione un empleado
SelecioneSituacao=Seleccione una situaci\u00f3n
SelecionePessoa=Seleccione una persona
SelecionePermissao=Seleccione un permiso
SelecioneUsuario=Seleccione un usuario
CPFInvalido=CPF inv\u00e1lido
FuncionarioCadastrado=Empleado ya registrado
MatriculaInvalida=Matr\u00edcula Inv\u00e1lida
EnderecoNaoEncontrado=Direcci\u00f3n no encontrada
CNPJInvalido=CNPJ inv\u00e1lido
ClienteNaoEncontrado=Cliente no encontrado
PessoaInvalida=Persona Inv\u00e1lida
tipoServ=Tipo de servicio
filialRota=Sucursal de la ruta
ImpossivelConectarSatellite=No se pudo conectar a la base de datos Satellite
LimparFiltros=Limpieza de filtros
login.falhageral<message>login.senhaerrada=Contrase\u00f1a incorrecta
ImpossivelConectarBanco=No se pudo conectar al banco
AlturaSemVirgula=Altura no puede tener coma
PesoSemVirgula=Peso no puede tener coma
filialRota=Sucursal de la ruta
secao=Secci\u00f3n
AdicionarPermissoesGrupo=Desea agregar todos los permisos del grupo
javax.faces.validator.RegexValidator.NOT_MATCHED=La contrase\u00f1a debe contener s\u00f3lo n\u00fameros y tener al menos 5 caracteres
primefaces.password.INVALID_MATCH=Las contrase\u00f1as actuales no confieren
QtdPermissoes=Cantidad de permisos
SemRegistros=No hay Registros
ExibirSenha=Ver la contrase\u00f1a
Placa=Tarjeta
Hora2=Hora2
Hora3=Hora3
Hora4=Hora4
hs_interv=Hs_Interv
QtdEscalas=Cantidad de escalas
CadastrarEscala=Registrar Escala
Periodo=Per\u00edodo
PesquisarFilial=Buscar en Sucursal
FilialInvalida=Sucursal inv\u00e1lida
senhaMobile=Contrase\u00f1a m\u00f3vil
permissaoRotas=Permiso de rutas
ProximaFoto=Siguiente Foto
Foto=Foto
De=de
FotoAnterior=Foto Siguiente
SemMaisFotosInicio=No hay fotos m\u00e1s recientes
SemMaisFotosFim=No hay m\u00e1s fotos
NaoSeAplica=No se aplica
Concluido=Concluido
Pendente=Pendiente
EmAndamento=En proceso
Demitido=Despedido
Inativo=Inactivo
PesquisarSupervisao=Buscar Supervisi\u00f3n
DataInvalida=Fecha Inv\u00e1lida
a=a la
numero=N\u00famero
SelecioneTrajeto=Seleccione una ruta
SelecioneRota=Seleccione una ruta
ExclusaoSucesso=Exclusi\u00f3n hecha con \u00e9xito
ExcluirRota=\u00bfDesea realmente excluir esta ruta?
ExcluirTrajeto=\u00bfDesea realmente excluir esta trayectoria?
VerifiqueHorario=Compruebe el horario
SemRotas=No hay rutas disponibles
numero=N\u00famero
Escala=Escala
excluirEscala=Eliminar la escala ocasionar\u00e1 la eliminaci\u00f3n total de su informaci\u00f3n
naoListar=No se pudo enumerar
Assinado=Firmado por
Guia=Gu\u00eda
listaguaistitulo=Lista de Gu\u00edas
Volume=Volumen
TempoEspera=Tiempo de espera
Serie=Serie
QtdGuias=Cantidad de Gu\u00edas
ExcluirEscala=\u00bfDesea realmente excluir esta escala?
SelecioneEscala=Seleccione una escala
EscalaExistente=Escala existente
RotaInexistente=Esta ruta no existe para esta fecha
CadastrarQuestoes=Registrar Cuestiones
CadastrarQuestao=Registrar Cuesti\u00f3n
Questoes=Cuestiones
SelecioneQuestao=Seleccione una cuesti\u00f3n
ExcluirQuestao=Excluir esta cuesti\u00f3n?
ExisteServico=Ya existe servicio en ese horario
HoraInvalida=La hora insertada no es v\u00e1lida
IntervaloInvalido=El intervalo insertado no es v\u00e1lido
GTV=GTV Electr\u00f3nica
DiaSemana=Dia Semana
$=$
Segunda=Lunes
Ter\u00e7a=Martes
Quarta=Mi\u00e9rcoles
Quinta=Jueves
Sexta=Viernes
Sabado=Sabado
Domingo=Domingo
DetalhesGuia=Detalles de la gu\u00eda
Lacre=Plomo
Guias=Gu\u00edas
SelecioneGuia=Seleccione una gu\u00eda
AdicionarCliente=Agregar cliente
trajetoExcluido=Trayecto ya excluido
exclusaoVisivel=Exclusiones visibles
escalaExcluida=Escala eliminada
VerTodos=Ver todos
DataInicial=La fecha de inicio
DataFinal=Fecha final
Hr_Saida=Hora de salida
EscolherOpcao=Seleccione una opci\u00f3n
TrocarCliente=Cambiar Cliente
Cedulas=Billetes
Cheques=Cheques
Moedas=Monedas
MetaisPreciosos=Metales Preciosos
MoedaExtrangeira=Moneda Extranjera
Outros=No declarado
Exportar=Exportar
AssistenciaTecnica=Asistencia t\u00e9cnica
Entrega=Entrega
Recolhimento=Recogida
ExibirExcluidos=Mostrar excluidos
escolhaTipoArquivo=Seleccione el tipo de archivo
pdf=Espa\u00f1ol
xls=XLS
html=HTML
xml=XML
nomeArquivo=Nombre de archivo
erroExportar=Error al hacer la exportaci\u00f3n
pagina=P\u00e1gina
de=de
Origem=Origen
Destino=Destino
QtdVolumes=Cantidad de vol\u00famenes
ValorGuias=Valor total de las gu\u00edas
SATELLITE=SATELLITE
SASW=SASW
SAS=SAS
SATLOYAL=LOYAL
SATTRANSVIG=TRANSVIG
SATINVLMT=INVIOLAVEL MT
CONFEDERAL=TRANSFEDERAL
CONFEDERALGO=TRANSFEDERAL GO
SATCONFEDERALBSB=CONFEDERAL
SATCONFEDERALGO=CONFEDERAL GO
SATCORPVSPE=CORPVS PE
SATTRANSVIP=TRANSVIP
SATPRESERVE=PRESERVE
VSG=VSG
SATTSEG=TECNOSEG
SATAGIL=AGILA
AGILSERV=AGIL SERV
SATAGILVIG=AGIL VIG
SATAGILCOND=AGIL COND
SATINVLRS=INVIOLAVEL RS
SASEX=SASEX
SATGSI=GSI
SATTRANSEXCEL=TRANSEXCEL
SATRODOB=RODOBAN
SATRODOBAN=RODOBAN
SATTAMEME=TAMEME
SATCOMETRA=COMETRA
SATSASEX=SASEX
EAGSATI=EAGLE
EAGSAS=EAGLE
CofreInteligente=Caja fuerte inteligente
TotalCreditos=Total de Cr\u00e9dito D\u00eda
CreditoDia=Cr\u00e9dito del d\u00eda
CreditoProxDU=Cr\u00e9dito Pr\u00f3ximo DU
ChefeEquipe=Jefe de Equipo
ValorRecolhido=Valor recopilado
SaldoCustoDia=Saldo Costo D\u00eda
SaldoCofre=Saldo de Caja
TotalVlrRecolhido=Total valor recopilado
TotalSalCustDia=Total Saldo Costo D\u00eda
TotalSaldoCofre=Total Saldo de Caja
SelecioneCofre=Seleccione una Caja
Feriado=Vacaciones
ValorRecD0=Valor Rec D0
HoraRecD0=Hora Rec D0
DepDiaAntAposCorte=Depto Ant Despu\u00e9s de Corte
ValorCorteD0=Valor Corte D0
TotalCredDia=Total de creditos
HrRecDia=Hr Rec D\u00eda
ValorRecDia=Valor Rec D\u00eda
ValorRecJaCreditado=Valor Rec ya acreditado
ValorRecACreditar=Valor Rec a Creditar
SaldoCofreTotal=Saldo de caja fuerte
DepositoJaCreditado=Dep\u00f3sito ya acreditado
DepositoProxDU=Dep\u00f3sito Prox DU
SaldoFisCst=Saldo Fis Cst
SenhaDia=Introduzca la contrase\u00f1a del d\u00eda
Mostrando=Noticias
SenhaSucesso=Contrase\u00f1a alterada con \u00e9xito.
Arquivo=Archivo
Cancelar=Cancelar
SATCORPVS=CORPVS
Buscar=Buscar
ConfirmarSaida=\u00bfDesea salir sin guardar los cambios?
QtdArquivosInvalida=Cantidad de archivos superados
Enviar=Enviar Enviar
ArrasteArquivo=Arrastre el archivo o busque
ApenasXLS=S\u00f3lo el archivo de plantilla
SelecioneArquivo=Seleccione un archivo
PossuoCodigo=Tengo un c\u00f3digo
SatMOB=SatMOB
Template=Plantilla
UsuarioSemAutorizacao=Usuario sin autorizaci\u00f3n. Para cambiar la contrase\u00f1a, p\u00f3ngase en contacto con el administrador del sistema.
ViaImportacao=Registro a trav\u00e9s de la importaci\u00f3n de archivos
DicaDownload=Descargue el archivo template, rellene el archivo y cargue el mismo para importar los nuevos valores.
DadosOrigem=Datos del origen
Remetente=Remitente
Veiculo=Veh\u00edculo
Chegada=Llegada
Saida=Salida
DadosDestino=Datos del destino
Destinatario=Destinatarios
DiscriminacaoValorIdentificacao=Discriminaci\u00f3n, valor e identificaci\u00f3n de la carga
ValorDeclarado=El valor declarado
IdentificacaoMalote=Identificaci\u00f3n de Malet\u00edn
Lacres=Plomos
Composicoes=Composiciones
TermoAssGuia=Recibimos los vol\u00famenes citados en esa Gu\u00eda, declaramos y reconocemos como entregos sin rastro de violaci\u00f3n, en perfectas condiciones, y en especial intactos los respectivos sellos de seguridad - plomos numerados descritos.
AssRemetente=Ass Remitente
AssDestinatario=As destinatario
InscEstadual=Inscripci\u00f3n Estadual
OS=OS
GETV=GU\u00cdA ELECTR\u00d3NICA DE TRANSPORTE DE VALORES
Telefone=Tel\u00e9fono
QtdParadas=Cantidad de Paradas
CamposExportacao=Seleccione los campos que desea exportar
CodPessoa=CodPersona
Postos=Puestos
Imprimir=Imprimir
FuncaoIndisponivel=Funci\u00f3n no disponible en el momento
Opcoes=Opciones
ImportacaoCompleta=Importaci\u00f3n Completa
ArquivoInvalido=Archivo no v\u00e1lido
ImportacaoCompletaComErros=Importaci\u00f3n completa con errores
ImportadosSucesso=importados con \u00e9xito
ImportadasSucesso=importadas con \u00e9xito
ErrosImportacao=Errores de importaci\u00f3n
Erros=Errores
ArrasteAqui=Arrastrar y soltar el archivo aqu\u00ed
Escalas=Escalas
BuscarPeriodo=Buscar por Per\u00edodo
Cofres=Cajas fuertes
BemVinda=Bienvenida
FolhaDePonto=Hoja De Punto
Contracheque=Contracheque
FuncoesAdm=Funciones Administrativas
AssinarGTV=Suscribirse a GTV-e
PortalRH=Portal RH
Matricula=Id. Empleado
Mensagens=Mensajes
ListaDatas=Lista de Fechas
BemVindo=Bienvenido
RGn=RG (s\u00f3lo n\u00fameros)
CPFn=CPF (s\u00f3lo n\u00fameros)
Acesso1=1er Acceso
Dt_Nascn=Fecha de nacimiento (dd / MM / aaaa)
CidadeRes=Ciudad de residencia
Contatos=Contactos
QtdContatos=Cantidad de contactos
NomeFantasia=Nombre de fantas\u00eda
PesquisarContato=Buscar Contacto
CadastrarContato=Registrar Contacto
IndicacaoCliente=Indicaci\u00f3n de cliente
IndicacaoFornecedor=Indicaci\u00f3n de proveedor
Propaganda=Propaganda
JornaisRevistas=Peri\u00f3dicos y Revistas
Internet=Internet
ProspeccaoComercial=Prospecci\u00f3n Comercial
ContatoViaSac=Contacto v\u00eda SAC
CapturaMobile=Captura m\u00f3vil
TpCli=Tipo de cliente
PAB=PAB
TA=TA
TAC=TAC
OutrasTransp=Otras Transp
Prospect=Prospect
AguardandoContato=Aguardando Contacto
ApresentacaoServicos=En Presentaci\u00f3n de Servicios
Negociacao=En negociacion
PilotoServicos=En Piloto de Servicios
FormalizacaoContratual=En Formalizaci\u00f3n Contractual
ClienteAtivo=Cliente Activo
Contato=Contacto
login.senhaerrada=Contrase\u00f1a incorrecta
SemFolhasDePonto=Sin hojas de punto
RH=Recursos humanos
InformeRendimentos=Informe de rendimientos
DadosPessoais=Datos personales
Documentos=Documentos
Dados=Datos
CodigoInvalido=C\u00f3digo no v\u00e1lido
Comercial=Comercial
Arquivos=Archivos
ModificadoEm=Modificado en
ExcluirDocumento=\u00bfDesea realmente excluir este documento? No se puede deshacer este cambio.
CadastrarProdutos=Registrar Producto
Aplicacao=Aplicaci\u00f3n
Preco=Precios
Produtos=Productos
QtdProdutos=Cantidad de Productos
PesquisarProduto=Buscar Producto
SelecioneProduto=Seleccione un producto
Numero=N\u00famero
Propostas=Propuestas
QtdPropostas=Cantidad de propuestas
Referencia=Referencia
Consultor=Consultor
Validade=Validez
DtValidade=Fecha de vencimiento
ProdutosSelecionados=Productos seleccionados
Status=Estado
Tickets=Entradas
Responsavel=Responsable
Fazer=Hacer
FilaPTeste=Fila P / Prueba
Teste=Prueba
Implantar=Implantar
Feito=Hecho
Desenvolver=Desarrollar
CadastrarTicket=Registrar Boleto
QtdTickets=Cantidad de Tickets
Ordem=Orden
PesquisarTickets=B\u00fasqueda de Tickets
MarcarDesmarcar=Marcar / Desmarcar Todos
Ticket=Ticket
CadastrarProposta=Registrar Propuesta
ValorParcial=Valor Parcial
Produto=Producto
Quantidade=Cantidad
ArrasteOuAdicione=Arrastre o haga clic en el bot\u00f3n + para agregar un producto
FormasPagamento=Formas de pago
Comentarios=Comentarios
AdicionarComentario=A\u00f1adir comentario
SalveAlteracoes=Guarde sus cambios antes de continuar.
Descontos=Descuentos
ResumoProposta=Resumen de la propuesta
Total=Total
Subtotal=Subtotal
CustosAdicionaisDescontos=Costos adicionales / Descuentos
SemFoneContato=Sin n\u00famero de tel\u00e9fono registrado.
PrecoAjustado=Precio ajustado
MAY=MAYO
APRIL=ABRIL
JANUARY=ENERO
FEBRUARY=FEBRERO
MARCH=MARZO
JUNE=JUNIO
JULY=JULIO
AUGUST=AGOSTO
SEPTEMBER=SEPTIEMBRE
OCTOBER=OCTUBRE
NOVEMBER=NOVIEMBRE
DECEMBER=DICIEMBRE
SATQUALIFOCO=QUALIFOCO
PrazoEntregaImplantacao=Plazo de Entrega / Implantaci\u00f3n
Garantia=Garant\u00eda
Outro=Otros
Opcao=Opciones
CondicoesPagamento=Condiciones de pago
DeveConter=debe contener
Digitos=d\u00edgitos
SenhaJaCadastrada=Correo electr\u00f3nico con contrase\u00f1a ya registrada
SenhaInvalida=Contrase\u00f1a no v\u00e1lida. Pruebe otra contrase\u00f1a.
SenhaNumerica6Digitos=La nueva contrase\u00f1a debe contener s\u00f3lo n\u00fameros y tener 6 d\u00edgitos.
DigiteASenha=Introduzca su contrase\u00f1a
SelecioneTicket=Seleccione un Ticket
ExportarProposta=Exportar propuesta
Modelo=Modelo
NomeArquivo=Nombre de archivo
ArquivoNaoEncontrado=Archivo no encontrado
SenhaEnviadaPara=Contrase\u00f1a enviada al correo electr\u00f3nico
NaoPossuoSenhaDia=No tengo contrase\u00f1a del d\u00eda
EmpresaMatricula=empresa matricula
UsuarioEmailInvalio=El correo electr\u00f3nico registrado es inv\u00e1lido. P\u00f3ngase en contacto con el administrador del sistema.
MatriculaAcessouSistema=Esta matr\u00edcula ya ha accedido al sistema. Entre con empresa @ matricula y su contrase\u00f1a habitual.
ErroValidacao=Error de validaci\u00f3n. Int\u00e9ntalo de nuevo.
Frequencia=Frecuencia
Faturamento=Facturaci\u00f3n
DataCompetencia=Fecha de competencia
ValorDeposito=Valor del Dep\u00f3sito
TipoDeposito=Tipo de Dep\u00f3sito
Entradas=Entradas
ConfirmarEdicaoTicket=\u00bfDesea realmente cambiar el estado del ticket?
SATINTERFORT=INTERFORT
SATGLOVAL=GLOVAL
ClienteP=Cliente
RotasValores=Rutas de Transporte de Valores
Autenticacao=Autenticaci\u00f3n
ValorTotal=Valor total
HistoricoObrigatorio=Cada ticket debe tener un historial detallando la descripci\u00f3n!
AdicionarContato=A\u00f1adir contacto
MeusTickets=Meus Tickets
Notas=Facturas
Hora1D=Hr1D
SATIBL=IBL
Ambiente=Ambiente
ExportarESocial=Exportar eSocial
Evento=Evento
Homologacao=Homologa\u00e7\u00e3o
Producao=Produ\u00e7\u00e3o
ESocial=ESocial / Reinf
CadastrarESocial=Registrar eSocial
CadastroInicial=Cadastro Inicial
XMLEnvio=XML de Envio
XMLRetorno=XML de Retorno
ProtocoloEnvio=Protocolo de Envio
InicioValidade=In\u00edcio da Validade
Dt_Envio=Fecha de env\u00edo
Hr_Envio=Hora de env\u00edo
Dt_Retorno=Fecha de regreso
Hr_Retorno=Hora de regreso
Eventos=Eventos
Certificado=Certificado
QtdEventos=Quatidade de Eventos
Resposta=Respuesta
GerarXML=Gerar XML
ConsultarRetorno=Consultar Retorno
ConsultaPendentes=Consultar Pend\u00eancias
VerificarProcessamento=Verificar Processamento
Dt_UltimoEnvio=Fecha ultimo envio
Prosseguir=Prosseguir
Emails=Emails
Para=Para
Assunto=Assunto
DeRemetente=De
Mensagem=Mensagem
ReenviarEmail=Reenviar Email
SelecioneEmail=Selecione Email
EnviarEmail=Enviar Email
ManifestoPara=Enviar manifesto para
EmailFilaEnvio=Email na fila para envio
Batidas=Puntos
Cargo=Cargo
SenhasNaoConferem=Senhas n\u00e3o conferem
Fotos=Fotos
SelecioneRelatorio=Selecione um Relat\u00f3rio
SATPROSECUR=PROSECUR
SatMobEW=SatMobEW
MatrAut=Registro autom\u00e1tico
SATSERVITE=SERVITE
NomeExisteSaspw=Username already in use
NomeExisteFuncion=Employee name already exists
PstDepen=Depend\u00eancias
SATCOGAR=COGAR
Tag=Tag
Batida=Punto
Dependencia=Depend\u00eancia
Rondas=Tours
Relatorio=Informe
Vigilante=Vigilante
Ocorrencia=Acontecimento
BuscarData=Buscar por Fecha
UsuarioNaoAutorizado=Usu\u00e1rio N\u00e3o Autorizado
ValorTotalRotas=Valor total de todas rotas:
Paradas=Paradas
Volumes=Volumes
HrCheg=HrCheg
HrSaida=HrSaida
CheckIn=Llegada Supervisor
Officer=Guarda
DataHora=Fecha - Tiempo
InicioRonda=Inicio de los Tours
FimRonda=Fin de los Tours
ReportEvento=Informe del Evento
ReportRonda=Informe del Tour
CheckInReport=Informe de Llegada
CheckOutReport=Informe de Salida
QtdRelatorios=Cantidad de Informes
CheckOut=Salida Supervisor
UsuarioSemClientes=Usuario sin clientes autorizados
InicioAfastamento=In\u00edcio Afastamento
Identificador=Identificador
Validacao=Valida\u00e7\u00e3o
MuitosEventosValidar=Muitos eventos a validar, enviando automaticamente.
RelatorioPublico=Informe P\u00fablico
RelatorioTornadoPublico=Informe hecho p\u00fablico
RelatorioTornadoPrivado=Informe hecho privado
Ronda=Tour
Conformidade=Conformidad
Completas=Completas
NomeTag=Nombre TAG
OrdemLeitura=Orden de Lectura
TagTour=Nro Tag en Tour
Indicacao=Indica\u00e7\u00e3o
Dt_FormIni=Fecha Form Ini
Dt_FormFim=Fecha Form Final
LocalForm=Local Forma\u00e7\u00e3o
NCertificado=N\u00ba Certificado
Dt_Recicl=Fecha de reciclaje
Dt_VenCurs=Fecha Venc Curso
Reg_PF=Registro PF
Reg_PFUF=UF
Reg_PFDt=Fecha Reg
CNH=CNH
CNHDtVenc=Fecha de validez CNH
ExtensoesTV=Extens\u00f5es TV
ExtTV=Transporte
ExtSPP=Extens\u00e3o Seg. Pes
ExtEscolta=Escolta
DadosFormacao=Datos Formaci\u00f3n
Deletar=Borrar
AdicionarEmail=A\u00f1adir Correo Electr\u00f3nico
EmailCadastrado=Correo Electr\u00f3nico ya registrado
ClienteNaoCadastrado=Cliente no registrado!
ClockIn=Clock In
ClockOut=Clock Out
ClockOutReport=Clock Out Report
ClockInReport=Clock In Report
Pergunta=Pregunta
SimNao=S\u00ed/No
Texto=Texto
CapturaVideo=Captura de Video
CapturaFoto=Captura de Foto
AdicionarInspecao=A\u00f1adir Inspecci\u00f3n
Inspecoes=Inspecciones
SelecioneInspecao=Seleccione una Inspecci\u00f3n
PstInspecao=Inspecciones de Puestos
QtdPstInspecao=Cantidad de Inspecciones
RelatorioInspecao=Informe Inspecci\u00f3n
Inspecao=Inspecci\u00f3n
Inspecionado=Inspeccionado
Assinatura=Firma
ClockInClockOut=Clock In/Clock Out
RelatorioSupervisor=Informe de Supervisor
CheckInCheckOut=Salida/Llegada Supervisor
Remover=Borrar
A3=A3
SenhaIndisponivelProcureRH=Fun\u00e7\u00e3o indispon\u00edvel no momento. Entre em contato com seu departamente de RH para alterar a senha.
Chamado=Llamado
Recusar=Recusar
Aceitar=Aceitar
descricao=Descripci\u00f3n
detalhe1=Detalhe 1
detalhe2=Detalhe 2
detalhe4=Detalhe 4
detalhe3=Detalhe 3
detalhe5=Detalhe 5
detalhe6=Detalhe 6
detalhe7=Detalhe 7
detalhe8=Detalhe 8
detalhe9=Detalhe 9
detalhe10=Detalhe 10
detalhe11=Detalhe 11
detalhe12=Detalhe 12
detalhe13=Detalhe 13
detalhe14=Detalhe 14
detalhe15=Detalhe 15
detalhe16=Detalhe 16
detalhe17=Detalhe 17
detalhe18=Detalhe 18
detalhe19=Detalhe 19
detalhe20=Detalhe 20
detalhe21=Detalhe 21
detalhe22=Detalhe 22
detalhe23=Detalhe 23
detalhe24=Detalhe 24
detalhe25=Detalhe 25
detalhe26=Detalhe 26
detalhe27=Detalhe 27
detalhe28=Detalhe 28
detalhe29=Detalhe 29
detalhe30=Detalhe 30
tipoarquivo=Tipo de Archivo
idarquivo=ID Arquivo
datahorageracaoarquivo=Fecha/hora de generaci\u00f3n de archivos
comunicacao=Comunica\u00e7\u00e3o
idgruposuportedemandante=ID Grupo Suporte Demandante
gruposuportedemandante=Grupo Suporte Demandante
no_req=No Req
no_wo=No WO
no_inc=No Inc
no_crq=No Crq
numero_serie=N\u00famero S\u00e9rie
idreq=ID Req
nomereq=Nome Req
idfornecedor=Id Fornecedor
prioridade=Prioridade
nomefornecedor=Nome Fornecedor
categ_op_n1=Categor\u00eda Op n1
categ_op_n2=Categor\u00eda Op n2
categ_op_n3=Categor\u00eda Op n3
categ_prod_n1=Categor\u00eda Prod n1
categ_prod_n2=Categor\u00eda Prod n2
categ_prod_n3=Categor\u00eda Prod n3
nomeproduto=Nome Produto
modeloproduto=Modelo Produto
fabricante=Fabricante
codigodobanco=C\u00f3digo Banco
tipounidade=Tipo Unidade
codigounidade=C\u00f3digo Unidade
siglaunidade=Sigla Unidade
nomeunidade=Nome Unidade
enderecounidade=Endere\u00e7o Unidade
cidadeunidade=Cidade Unidade
ufunidade=UF Unidade
cep=CP
idsolicitante=ID Solicitante
contatotelefone=Contato Tel\u00e9fono
contatonome=Contato Nome
contatoemail=Contato Email
RecusarChamado=Recusar Chamado
AceitarChamado=Aceitar Chamado
previsaoatendimento=Previs\u00e3o de Atendimento
Confirmar=Confirmar
tiporetorno=Tipo Retorno
chamadofornecedor=Llamada de Proveedor
responsavelatendimento=Respons\u00e1vel Atendimento
Grafico=Artes gr\u00e1ficas
HorasExtras=Horas Extras
Faltas=Faltas
Suspensao=Suspensiones
Atestado=Certificados M\u00e9dicos
EvoHoras=Evoluci\u00f3n Horas Extras
Horas50=Horas Extras 50% 
Horas100=Horas Extras 100%
Compet=Competencia
FaltasUltimoMes=Faltas 
EvoFaltas=Evoluci\u00f3n de las Faltas
SuspUltimoMes=Suspensiones 
EvoSusp=Evoluci\u00f3n de Suspensiones
AtestadoUltimoMes=Certificados M\u00e9dicos
EvoAtestados=Evoluci\u00f3n de los Certificados m\u00e9dicos
Competencia=Competencia
RelatorioAuditoria=Informe de auditor\u00eda del usuario
ControleUsuarios=Controle de Usu\u00e1rios
CadastrarGrupos=Registrar Grupos
DetalhesGrupo=Detalhes do Grupo
SATTRANSPORTER=SATTRANSPORTER
Said=Salida
NRedFat=Cliente a Faturar
LocalParada=Local Parada
AssinaturaDestino=Assinatura no Destino
UploadPedido=Upload Pedido
ArquivosRecentes=Arquivos Recentes
Tamanho=Tamanho
OrigemPedido=Origem do Pedido
PedidosRecentes=Recently Imported Orders
SubAgencia=Sub Sucursal
ProcessandoAguarde=PROCESSING...PLEASE WAIT
SelecioneUnicoCliente=Selecione um \u00fanico cliente para realizar a importa\u00e7\u00e3o por arquivo.
DtColeta=Date Collection
DtEntrega=Fecha de entrega
HoraEntrega=Hora de Entrega
Solicitante=Solicitante
Pedidos=Solicitudes
QtdPedidos=Cantidad de Pedidos
classificacao=Clasificaci\u00f3n
DataEntrega=Fecha de entrega
Servico=Servicio
Suprimento=Suministro
MensagemImportacaoPreOrder=Foram encontrados %s1 plomos para %s2 pedidos. Deseja prosseguir com a importa\u00e7\u00e3o?
MensagemConfirmacaoPreOrder=Se encontraron pedidos para la fecha %s1. \u00bfDesea eliminarlos y realizar una nueva importaci\u00f3n?
ReportRota=Informe Resumen de la Ruta
InicioRota=Inicio de la Ruta
UltimoServico=\u00daltimo Servicio
RotaPausada=Ruta Pausada
RotaRetomada=Ruta Reanudada
RelatorioPrestador=Informe del Prestador
RotaPrestador=Ruta del Prestador
SATECOVISAO=ECOVIS\u00c3O
TipoVeiculoInvalido=Tipo de veh\u00edculo no v\u00e1lido
Forte=Fuerte
Leve=Leve
Moto=Moto
Pesado=Pesado
Aeronave=Aviones
Nenhum=Ninguno
EntregaRecolhimento=Entrega / Recogida
Transbordo=Transbordo
Rotineiro=Rutina
Eventual=Eventual
Especial=Especial
AssistTecnica=Asistencia T\u00e9cnica
Intermediaria=Intermedio
Preliminar=Preliminar
ProjetoEspecial=Proyecto Especial
ExisteTrajetoMesmoHorarioEntrega=Hay otra parada al mismo tiempo de entrega.
SelecioneCliOri=Seleccione el cliente fuente
SelecioneCliDst=Seleccione el cliente objetivo
ExisteTrajetoMesmoHorario=Hay otra parada al mismo tiempo
HorarioDentroIntervalo=Horario no v\u00e1lido: dentro del rango de ruta
HorarioForaRota=Horario no v\u00e1lido: tiempo fuera de ruta
RecolhimentoAnteriorEntrega=Horario no v\u00e1lido: recogida previa a la entrega
SemInfoCliFat=Sin informaci\u00f3n de facturaci\u00f3n del cliente
SemGuiasParaManifest=No hay informaci\u00f3n de gu\u00eda para enviar el manifiesto
MotoristaDiferenteVeiculo=01 - Controlador diferente del est\u00e1ndar.
Pref=Pref
NSOP=N\u00ba SOP
RelatorioUsuarios=Informe de control de usuario
AtualizarGrupos=Actualizar Grupos
Motorista=Motorista
Vigilante1=Vigilante1
Vigilante2=Vigilante2
Vigilante3=Vigilante3
FuncionariosFolga=Funcion\u00e1rios de Folga
AceitarFuncionariosFolga=Los empleados a continuaci\u00f3n est\u00e1n fuera de servicio. Quieres proceder?
AceitarFuncionarioFolga=O funcion\u00e1rio abaixo se encontra de folga. Deseja prosseguir?
SenhaMobile=Contrase\u00f1a m\u00f3vil
PermissaoRotas=Permiso rutas
PessoaJaEscalada=Persona ya subi\u00f3
DataAnteriorEdicaoBloqueada=Escala fechada antes de la fecha actual. Editar bloqueado.
MotoristaJaEscalado=Motorista j\u00e1 escalado
ChEquipeJaEscalado=Jefe de Equipo ya escalado
Vigilante1JaEscalado=Vigilante 1 j\u00e1 escalado
Vigilante2JaEscalado=Vigilante 2 j\u00e1 escalado
Vigilante3JaEscalado=Vigilante 3 j\u00e1 escalado
HrMotInvalida=HrMot inv\u00e1lida
PessoaNaoMotorista=Essa pessoa n\u00e3o \u00e9 Motorista
PessoaNaoChEquipe=Essa pessoa n\u00e3o \u00e9 ChEquipe
VeiculoJaEscalado=Ve\u00edculo j\u00e1 escalado
HrCheInvalida=HrChe inv\u00e1lida
HrVig1Invalida=HrVig1 inv\u00e1lida
HrVig2Invalida=HrVig2 \u00ednvalida
HrVig3Invalida=HrVig3 inv\u00e1lida
MovimentacaoContainer=Movimenta\u00e7\u00e3o Container
Container=Container
HrServico=HrServico
Containers=Containers
decimalSeparator=,
thousandSeparator=.
DSEIndividual=DSE Individual
Colaborador=Colaborador
DtInicio=Fecha de Inic\u00edo
DtFim=Fecha de Final
DestinoEntrega=Destino Entrega
OrigemColeta=Origem Coleta
SelecioneParada=Selecione a parada para impress\u00e3o do manifesto
ManifestosDisponiveis=Manifestos Dispon\u00edveis
SB=Ag/SB
Vol=Vol
HrColeta=HrColeta
HrEntrega=HrEntrega
SBDst=Ag/SB Dst
QtdGuiasPreOrder=Qtd Guias
Graficos=Graficos
RPV=RPV
Remessa=Remessa
EditarLacre=Editar Plomo
NovoLacre=Nuevo Plomo
NovaImportacao=Nueva Importa\u00e7\u00e3o
Atualizar=Atualizar
MensagemConfirmacaoPreOrders=Se encontraron pedidos para la fecha %s1 en %s2 lotes. \u00bfDesea actualizar o realizar una nueva importaci\u00f3n?
OSRtg=OSRtg
HrPrg=HrPrg
DtEnt=FechaEnt
GuiaIndisponivel=Guia n\u00e3o dispon\u00edvel para impress\u00e3o
ErroPrimeiroAcesso=N\u00e3o foi poss\u00edvel finalizar o cadastro. Tente acessar com email e senha.
Identif=Identificaci\u00f3n
Dt_Inicio=Fecha In\u00edcio
Dt_Termino=Fecha T\u00e9rmino
CliFat=Cliente Facturar
ContratoCli=ContratoCli
RefArq=RefArq
GrpReajuste=GrpReajuste
GrpPagamento=GrpPagamento
OBS=OBS
Processo=Processo
Contratos=Contratos
QtdContratos=Quantidade de Contratos
IdentificacaoCliente=Identificaci\u00f3n Cliente
TipoContrato=Tipo de Contrato
ClienteContratante=Cliente Contratante
Identificacao=Identificaci\u00f3n
ContratoAssinado=Contrato Assinado
Determinado=Determinado
Indeterminado=Indeterminado
Transporte=Transporte
Geral=Datos Generales
Cancelado=Cancelado
MostrarSomenteAtivos=Mostrar solo activos
ContratosAVencer=Contratos a Vencer
login.falhageral<message>login.metodoerrado=Forma de login errada. Tente acessar utilizando email e senha.
EventoExcluir=Evento a Excluir
login.falhageral<message>Falha\ ao\ carregar\ filiais\ -\ UsuarioSemPermissao=Usuario sin permiso
NomeCli=Nome Cliente
SelecioneContrato=Selecione um Contrato
OutrasBases=Outras Bases
TrocarFilial=Cambiar Sucursal
SessaoExpirada=Sesi\u00f3n Expirada
CliqueParaRedirecionar=Haga clic aqu\u00ed para volver a la p\u00e1gina de inicio.
DadosGerais=Datos Generales
CarNacVig=Cartera Nacional de Vigilante
DtValCNV=Fecha de validez CNV
Mae=Madre
ExclusaoCargoSucesso=Exclus\u00e3o de cargo com sucesso
CargoSucesso=Posici\u00f3n agregada exitosamente
CargoPretendido=Cargo Pretendido
PIS=PIS
Historicos=Hist\u00f3ricos
ColetaAntesCorte=Coleta Antes Corte
Credito=Cr\u00e9dito
RecolhimentoDepoisCorte=Recolhimento Depois do Corte
NoCofre=No Cofre
ProximoDia=Pr\u00f3ximo Dia
Custodia=Cust\u00f3dia
PesquisarNFiscal=Buscar facturas
Praca=Plaza
Ativa=Activa
Cancelada=Cancelada
NFiscais=Facturas
QtdNFiscais=Cantidad de facturas
UploadNFiscal=Upload Arquivo XML
NRedPraca=Plaza Nombre
SelecioneNFiscal=Seleccione factura
ErroOcorrido=Se produjo un error al procesar la solicitud.
CliqueParaRecarregar=Haga clic para volver a cargar la p\u00e1gina
Movimentacoes=Movimenta\u00e7\u00f5es
Rotas=Rutas
SATGETLOCK=GETLOCK
SATVANTEC=VANTEC
SATNORSERV=NORSERV
DadosCliente=Dados do Cliente
IdentificacaoContainer=Identificaci\u00f3n do Container
Download=Download
PortaSuperior=Porta Superior
PortaCofre=Porta do Cofre
Cassete=Casete
Fechadura=Fechadura
Validadora=Validadora
Impressao=Impressora
Fechada=Fechada
Fechado=Fechado
Aberta=Aberta
Aberto=Aberto
Conectada=Conectada
Desconectada=Desconectada
Marca=Marca
Serial=Serial
IMEI=IMEI
InfoCofre=Informa\u00e7\u00f5es do Cofre
DepositosAte16=Depositos At\u00e9 16:00
DepositosApos16=Depositos Ap\u00f3s 16:00
Depositos=Dep\u00f3sitos
Coletas=Coletas
CofresGerenciados=Administraci\u00f3n de Cofres
DataLocacao=Fecha de arrendamiento
HoraLocacao=Tiempo de alquiler
TempoDias=Tiempo
TipoMovimentacao=Tipo de Movimiento
Atividade=Actividad
CNPJCPF=CNPJ/CPF
IERG=IE/RG
Retencoes=Retenciones
RetemImpostos=S - Retenciones de impuestos seg\u00fan los par\u00e1metros
NaoFazRetencoes=N - No tiene deducciones
AgendarColeta=Agendar Coleta
Retorno=Retorno
Envelope=Envelope
LimiteSeguro=Limite Seguro
Cheque=Cheque
CodCofre=C\u00f3digo Cofre
Patrimonio=N\u00famero Patrim\u00f4nio
DadosATM=Datos ATM
Interface=Interfaz
CodExt=Cod. Ref Cliente 2
CodPtoCli=C\u00f3d. Punto
Normal=Normal
TransportadoraValores=Portadora de Valor
MapaIndisponivel=Mapa no Disponible
CodigoRegiao=C\u00f3digo da Regi\u00e3o
Abrangencia=Cobertura
TipoLocalizacao=Tipo da Ubicaci\u00f3n
Regioes=Regiones
Urbano=Urbano
Interurbano=Interurbano
PrazoColeta=Prazo de Coleta
ColetasVencidas=Coletas Vencidas
ColetasHoje=Coletas de Hoje
ColetasVencer=Coletas a Vencer
SemPrazoColeta=Sem Prazo de Coleta
TempoCliente=Tempo no Cliente
Servicos=Servicios
PedidoCliente=Pedido Cliente
HoraDe=De
Ate=At\u00e9
SomenteDataSelecionada=Fecha seleccionada solamente
Todos=Todos
Dias=Days
Mais15Dias=M\u00e1s de 15 D\u00edas
ClienteServico=Cliente - Servi\u00e7o
MovimentacaoDiaria=Movimenta\u00e7\u00e3o Di\u00e1ria
buscaMovimentacao=Movimenta\u00e7\u00e3o
TrajetoAndamentoExecutado = Curso en progreso - Ejecutado
TrajetoAndamentoRestante = Curso en progreso - Restante
TrajetoExecutado = Curso Ejecutado
TrajetoProgramado = Camino Programado
OrigemMapa=Origen del mapa
AtualizarMapa=Actualizar mapa
TodasRotas=Lista de Rutas
Rastreador=Rastreador
Manha=Ma\u00f1ana
Tarde=Tarde
HorarioPosicoes=Tiempo de las posiciones
SATBRASIFORT=BRASIFORT
ProgramacaoGeral=Horario General
QtdeCacambas=Cantidad de buckets
Orcamento=Presupuesto
InicioHorario=Hora de inicio
FinalHorario=Hora final
ExclusaoPedidoNaoPermitida=Exclusi\u00f3n permitida solo para pedidos pendientes
ServicoNaoDisponivel=Servi\u00e7o N\u00e3o Dispon\u00edvel
Atencao=Atencion
ConfirmaExclusao=\u00bfConfirmar eliminaci\u00f3n?
ClienteNaoEncontradoOuSemCacambas=Cliente no encontrado o sin containers
EstabelecimentoComercialResidencia=Establecimiento comercial o residencia
Gravar=Grabar
QtdTrajetos=Cantidad de Tickets
FecharAtualizar=Cerrar y Actualizar
ValorDepositadoCXF=Valor Depositado en B\u00f3veda
VlrEntDir=Valor Entregas Directas
HorarioPosicao=Geolocalizaci\u00f3n
Transferencias=Transferencias
ChEquipe=Jefe de Equipo
TotalemRota=Total en Ruta
R=Recolecci\u00f3n
E=Entrega
Acessar=Acceder
EmpresaServico=Empresa/Servi\u00e7o
SelecioneEmpresaServico=Seleccione la Empresa y el Servicio
ClientesCadastrados=Clientes Cadastrados
TodosUsuarios=Lista de Usu\u00e1rios
AdicionarServico=A\u00f1adir servicio
BancoDados=Banco de Datos
Bancos=Bancos
QtdBancos=Qtd de Bancos
AdicionarBanco=Adicionar Banco de Dados
CodigoLogin=C\u00f3digo Login
JanelaHorario=Ventana de Tiempo
CliqueParaDetalhes=Haga clic para obtener detalles
Desconectado=Desconectado
SomenteAlertas=Somente alertas
NomeCofre=Nombre de la B\u00f3veda
acesso.falhageral<message>EmailEmUso=Email j\u00e1 vinculado a outra pessoa.
NumeroCofre=Numero Seguro
RelatorioMovimentacaoGeralFuncionario=Relat\u00f3rio de Movimenta\u00e7\u00e3o Geral por Funcion\u00e1rio
Noite=Noche
Atrasados=Fuera de Horario
COLETA=Coleta
DEPOSITO=Dep\u00f3sito
MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO=Movimenta\u00e7\u00e3o Individual de Funcion\u00e1rio
MOVIMENTACAO_DIARIA=Movimenta\u00e7\u00e3o Di\u00e1ria
MOVIMENTACAO_GERAL_FUNCIONARIO=Movimenta\u00e7\u00e3o Geral por Funcion\u00e1rio
GerarRelatorio=Gerar Relat\u00f3rio
Coleta=Coleta
Deposito=Dep\u00f3sito
RelatorioDeposito=Relat\u00f3rio de Dep\u00f3sito
RelatorioMovimentacaoIndividualFuncionario=Relat\u00f3rio de Movimenta\u00e7\u00e3o de Funcion\u00e1rio
RelatorioMovimentacaoDiaria=Relat\u00f3rio de Movimenta\u00e7\u00e3o Di\u00e1ria
RelatorioColeta=Relat\u00f3rio de Coleta
TotalDeposito=Total em Dep\u00f3sitos
TotalColeta=Total em Coletas
SaldoAnterior=Saldo Anterior
SaldoDia=Saldo do Dia
SemDepositosData=No hay dep\u00f3sitos para la fecha seleccionada.
SemColetasData=No hay colecciones para la fecha seleccionada.
SemMovimentacoesData=No hay operadores movi\u00e9ndose a la fecha seleccionada.
SelecioneOperador=Selecione um Operador
SelecioneDeposito=Selecione um Dep\u00f3sito
SelecioneColeta=Selecione uma Coleta
SATCIT=CIT
TotalParadas=Rutas Totales
TotalGuias=Gu\u00edas Totales
TotalKm=Total KM
Mes=M\u00eas
Ano=A\u00f1o
ParadasGuias=Paradas y Gu\u00edas
AnaliseProd=An\u00e1lisis de Productividad por D\u00eda
AnalseValores=An\u00e1lisis de valores Transportados
TotalTransportado=Total Llevado por D\u00eda
Dashboard=Dashboard
Horas=Horas
AnaliseValoresTransp=An\u00e1lisis de Valores Transportados
TotalValoresTransp=Montos Totales Transportados por Ruta
AnaliseTrajetoExec=Ejecutar An\u00e1lisis de Ruta
TotalKmPerc=Km Totales Recorridos por D\u00eda
AnaliseParada=An\u00e1lisis de Paradas
TotalParadasRota=Paradas Totales por Ruta
MediaProdDia=Productividad Promedio por D\u00eda
ConsiderandoDados=Considerando Datos del Primer al \u00daltimo D\u00eda Del Mes
MaximoRotas=Rutas M\u00e1ximas
OutrosDesc=Otros
Saidas=Salidas
EmServico=En Servicio
ForaServico=Fuera de Servicio
SATSHALOM=SHALOM
TipoOS=Tipo de OS
SelecioneOS=Seleccione una OS
CliDst=Cliente de destino
ClienteFaturar=Cliente factura
DiaFechaFat=D\u00eda cierre factura
Agrupador=Agrupaci\u00f3n
MsgExtrato=Mensaje Extracto
CodSrv=C\u00f3digo de servicio
KM=Distancia (km)
KMTerra=KM Tierra
Aditivo=Aditivo
CCusto=Centro de Costos
OSGrp=Agrupaci\u00f3n II
GTVQtde=GTVQtde 
GTVEstMin=GTVEstMin
SitFiscal=Situaci\u00f3n fiscal
CofresAtivos=Cofres Ativos
TotalDepositos=Total de Dep\u00f3sitos
TotalColetas=Total de Coletas
Dia=Dia
CofresAtualizados=Cofres Atualizados
StatusComunicacao=Status de Comunica\u00e7\u00e3o
StatusBateria=Status de Bateria
Saldo=Saldo
UltimaMovimentacao=\u00daltima Movimenta\u00e7\u00e3o
Bateria=Bater\u00eda
Versao=Vers\u00e3o
Movimentacao=Movimenta\u00e7\u00e3o
MovimentacoesRecentes=Movimenta\u00e7\u00f5es Recentes
UltimasMovimentacoes=\u00daltimas Movimenta\u00e7\u00f5es
AnaliseMovimentacoes=An\u00e1lise de Movimenta\u00e7\u00f5es
TotalMovimentacaoHora=Total de Movimenta\u00e7\u00e3o por Hora
Online=Online
Offline=Offline
SemComunicacaoRecente=Sem Comunica\u00e7\u00e3o Recente
Atualizados=Atualizados
Desatualizados=Desatualizados
SaldoCofres=Saldo dos Cofres
InfoCofres=Informa\u00e7\u00f5es dos cofres: bateria, vers\u00e3o, sensores
StatusCofres=Status dos Cofres
VersaoAtual=Vers\u00e3o Atual
InformacoesGerais=Informa\u00e7\u00f5es Gerais
MovimentacoesDiaHistoricoCompleto=Movimenta\u00e7\u00f5es do Dia e Resumo Geral dos Cofres
RelatorioColetas=Relat\u00f3rio Coletas
GerarRelatorioAntes=Gere o Relat\u00f3rio Antes de Export\u00e1-lo
HISTORICO_COLETAS=Hist\u00f3rico de Coletas
HISTORICO_DEPOSITOS=Hist\u00f3rico de Dep\u00f3sitos
HISTORICO_CREDITOS=Hist\u00f3rico de Cr\u00e9ditos
RelatorioDepositos=Relat\u00f3rio Depositos
DownloadPDF=Download PDF
DownloadCSV=Download CSV
SemGuias=Cliente no Guiado
Lote=Env\u00edo
CTPS_Nro=Seguridad Social Nro
CTPS_Serie=Seguridad Social Serie
RegistroPresenca=Registro de asistencia 
PostoRegistro=Puesto registro
HoraInicio=Hora Inicio
HoraFim=Hora Fin
PreOrder=Solicitud Previa
UploadPreOrder=Upload Solicitud Previa
EmTransito=En Tr\u00e1nsito
Finalizado=Terminado
Recolhimentos=Recogidas
Entregas=Entregas
Tempo=Tiempo
TrajetoAndamento = Seguir en Progreso
UltimaComunicacao=\u00daltima Comunicaci\u00f3n
AtualizarMapaAuto=Actualizar el mapa en
Segundos=segundos
SequenciaInvalida=Secuencia de Ruta Indefinida
RelatorioMovimentacoes=Relat\u00f3rio Movimenta\u00e7\u00f5es
HISTORICO_MOVIMENTACOES=Hist\u00f3rico de Movimenta\u00e7\u00f5es
SA\u00cdDA=Salida
ENTRADA=Entrada
RELAT\u00d3RIO\ DE\ SUPERVISOR=Informe de Supervisor
RELAT\u00d3RIO=Informe
RONDA=Ronda
ROTA=Ruta
CHEGADA\ DE\ SUPERVISOR=Llegada del Supervisor
SAIDA\ DE\ SUPERVISOR=Salida del Supervisor
INSPE\u00c7\u00c3O=Inspeccion
ItensFaturamento=Art\u00edculos de Facturaci\u00f3n
VigenciaInicio=Comienzo Del Plazo
VigenciaFim=Fin Del Plazo
MensagemExtrato=Mensaje En La Declaraci\u00f3n
TipoCalculo=Tipo de Calculo
Vigencia=Plazo
A=a
TransporteUrbano=Transporte Urbano
TransporteInterurbano=Transporte Interurbano
OutrosServicos=Otros Servicios
TransporteInterestadual=Transporte Interestatal
CodigoServico=C\u00f3digo de Servicio
Cobranca=Facturaci\u00f3n
AgrupadorNF=Agrupador
Agrupador2NF=Agrupador II
KMAsfalto=KM Asfalto
ViaCxForte=Via B\u00f3veda
EntregaSab=Entrega Sabado
EntregaDom=Entrega Domingo
EntregaFer=Entrega Vacaciones
ItensFaturar=Elementos a Facturar
TipoCalc=Tipo Calc
Qtde=Cantidad
DiasCst=D\u00edas de Custodia
Extraordinaria=Extraordinaria
Inativa=Inactiva
EmailNaoInformado=Correo electr\u00f3nico no informado
SemRetencao=Sin Retenci\u00f3n
ComRetencao=Con Retenci\u00f3n
CadastrarOS=Registrar Orden de Servicio
FrequenciaServicos=Frecuencia de los Servicios
DiaSem=Dia de la Semana
ISS=ISS
RotaC=RutaC
ItemFatPorMes=Por Mes
ItemFatPorDia=Por Dia
ItemFatPorHora=Por Hora
ItemFatPorQtde=Por Cantidad
ItemFatPorIndice=Indice sin Valor
CadastrarCodigoServico=Registrar C\u00f3digo de Servicio
Banco=Banco
TAtend=Tipo Atendimento
TCob=Forma Cobran\u00e7a
TCar=Tipo de Ve\u00edculo
TipoSrv=Tipo de Servicio
TRota=Tipo de Rota
CodInterf=C\u00f3digo Inter Cliente
Texportar=Tipo de Exporta\u00e7\u00e3o
ColetaBau=Coleta de Ba\u00fa
Compartilhado=Compartilhado
PorEquipamento=Por Equipamento
Eventual1=Eventual 1
Eventual2=Eventual 2
PorPonto=Por Ponto
CarroDedicado=Carro Dedicado
DefinidoContrato=Definido em Contrato
Embarque=Embarque
Mensal=Mensal
EmbarqueMensalizado=Embarque Mensalizado
Interbancario=Interbanc\u00e1rio
AeronaveFretada=Aeronave Fretada
AeronaveRegular=Aeronave Regular
CarroForte=Carro Fuerte
CarroLeve=Veh\u00edculo Ligero
AdValorem=Ad Valorem
Extraordinario=Extraordin\u00e1rio
ProcessamentoEnvelopesDestino=Processamento Envelopes Destino
Monitoramento=Monitoramento
NaoExportar=N\u00e3o Exportar
ExportarDBFCliente=Exportar em DBF Cliente
CadastrarAgrupadorNF=Registrar Agrupador NF
PerfilFiscal=Perfil Fiscal
Municipio=Ciudad
HistoricoNotaFiscal=Historial de Facturas
HistFixo=Hist. Fixo
HistVariavel=Hist. Vari\u00e1vel
CFOP=CFOP
Retido=Retido
TipoCobranca=Tipo de Cobranza
IRRF=IRRF
BaseINSS=Base INSS
INSS=INSS
COFINS=COFINS
CSL=CSL
IRPJ=IRPJ
Repasse=Repasse
IncluirCustodia=Incluir Cust\u00f3dia
RepasseLiquido=Repasses/L\u00edquido (%)
AliqISS=ISS (%)
ValorRot=Valor de Rutina
ValorEve=Valor Eventual
ValorEsp=Valor Especial
ValorAst=Valor Assistencia
CHDiaria=CH Diariamente
CHSemana=CH Semana
ValorServico=Valor del Servicio
DescrCalc=Tipo C\u00e1lculo
ValorRef=Valor Referencia
HEDiurna=Hora Extra Diurna
HRNoturna=Hora Extra Noturna
HEDiurna2=Hora Extra Diurna 2
HENoturna2=Hora Extra Noturna 2
CHMensal=CH Mensualmente
ValorDia=D\u00eda de Valor
ValorHora=Valor de La Hora
#IdentificacaoBanco
ItensContrato=Art\u00edculos de Contrato
mostrarValores=Mostrar Valores
Precos=Precios
Franquia=Franquicia
TiposEspecificos=Tipos Espec\u00edficos
CodigoOperacional=C\u00f3digo Operacional
ValorReforco=Valor de Impulso
InibirReajuste=Inhibir Reajuste
ValorHEDiurna=Valor HE Durante el Dia
ValorHENoturna=Valor HE Nocturna
Salario=Salario
TotalPosto=Total Posto
QtdeFunc=Qtde Func
CHSeman=CHSeman
CadastrarMovimentacao=Registrar Movimiento
QtdeFunc=Cant. Empleados
CHSeman=CH Semana
PorMes=Por Mes
PorDia=Por Dia
PorHora=Por Hora
PorQtde=Por Cantidad
IndiceSValor=Indice S/ Valor
Embarques=Embarques
EmbarquesFranquia=Embarques c/franquicia 1
EmbarquesFranquia=Embarques c/franquicia 2
EmbNumerarioConcomitDiaria=Emb efectivo concomit dia
EmbEnvelopeConcomitDiaria=Emb envelope dia
EmbSuprimentoDifenciado=Emb suministro diferenciado
EmbRecolhimentoDifenciado=Emb recolecci\u00f3n diferenciado
SuprimentoConcomitante=Suministro concomitante
CargaDescarga=Carga y descarga
EmbarquesCobrancaEntrega=Emb cobranza de entrega
AdValorem=Ad Valorem
TempoEsperaMinutos=Tiempo de espera (minutos)
TempoEsperaHora=Tiempo de espera (hora)
Envelopes=Envelopes
Bananinha=Sangria (MCD)
Sangria=Sangria
KitTroco=Kit cambio
ChequesUnitario=Cheques (Unitario)
UnidadesProdServ=Unidades (productos/servicios)
Milheiros=Miles de dinero
MilheirosBom=Miles de dinero bueno
MilheirosDilacerado=Miles de dinero da\u00f1ado
MilheirosMoedas=Miles de monedas
MilheirosCheques=Miles de cheques
MilheirosCheques=Miles de cheques 2
MilheirosTicket=Miles de ticket
MilheirosCheques=Miles de cheques 3
MilheirosSuprimento=Miles suministro
MilheirosSuprimentoMoedas=Miles suministro monedas
Custodia=Custodia
CustodiaMoedas=Custodia monedas
CustodiaProdutosServi=Custodia (productos/servicios)
CustodiaPermanenicaDi=Custodia estancia diaria
CustodiaPassagem=Custodia por pasaje
KMRodados=KM recorrido
KMTerra=KM camino de tierra
FixoMensal=Transporte mensual fijo
FixoMensalAdValorem=Ad Valorem mensual fijo
FixoMensalTesouraria=Proceso efectivo mensual fijo
TempoCoberturaPagamento=Tiempo cobertura de pago
Malotes=Envases
EntregaRecolhimentoMalotes=Suministro/Recollec envases
EnvelopeSangria=Envelope sangria
CadastrarItensContrato=Servicios a Contratar
SomenteAtivas=S\u00f3lo Activas
RotasXEscalas=Rutas x Escalas
Anexo=Adjunto
Apagar=Borrar
arquivoCliente=Arquivo Cliente
CodMovimentacao=Codigo Movimiento
NomeSubs=Nombre Sustituto
PesquisarMovimentacao=Buscar Movimiento
Reforco=Refuerzo
RotasXModelos=Rutas x Modelos
GeracaoRotasXModelos=Generaci\u00f3n de Rutas x Modelos
PesquisarOS=Buscar Orden de Trabajo
TransporteValores=Transporte de Valores
GestaoCaixasEletronicos=Gesti\u00f3n de Cajeros Autom\u00e1ticos
AtendimentoClientes=Servicio al Cliente
OperacoesServicos=Operaciones de Servicio
EscalaFuncionarios=Escala de Empleados
PontoEletronico=Escala de Empleados
Check_In=Check-In
FechamentoRotas=Rutas de Cierre
FechamentoFaturamentoTransportes=Cierre de Facturaci\u00f3n de Transporte
FechamentoFaturamentoServicos=Servicio de Cierre de Facturaci\u00f3n
FaturarVendas=Ventas de Facturas
NotasFiscais=Facturas
GerarRotasUsandoModelo=Generar Rutas utilizando Modelo
GerarModeloUsandoRotas=Generar Modelo utilizando Rutas
GerarPedidosUsandoFrequencia=Generar pedidos utilizando frecuencia
RoteirizarPedidos=Rutear pedidos
FrequenciaRota=Frecuencia/Ruta
ModeloUtilizar=Modelo para usar
Tolerancia=Tolerancia (min)
RoteirizaAuto=Rutear Servicio
ProcurarPor=Buscar por
FolhaPontoIndividual=Hoja de Punto Individual
HrTrabalho=Hrs. de Trabajo
CTPS=RFC
Extra=Extra
LocalAtuacao=Puesto de Actuaci\u00f3n
Obs.=Obs
Chave=llave
CadastrarContrato=Registrar Contrato
PesquisarContrato=Buscar Contrato
DeveSerNumerico=El campo debe ser num\u00e9rico
CampoNaoExiste=Error: el campo no existe!
SubContratosAnexos=Contratos Adjuntos
PreFatura=Prefactura
HrChegVei=Llegada veh\u00edculo
HrSaidaVei=Salida veh\u00edculo
Montante=Cantidad
ValorEmbarque=Valor de env\u00edo
ValorADV=Valor Advalorem
ValorTE=Valor de tiempo de espera
TotalRota=Ruta total
PreFaturaCliente=Prefactura del cliente
QtdeParadas=Cantidad de paradas
Reajustes=Ajustes
itensAnteriores=Art\u00edculos anteriores
Indice=Indice
AnaliseFaturamentoServico=An\u00e1lisis de Facturaci\u00f3n por Servicio
AnaliseFaturamentoRamoAtividade=An\u00e1lisis de Facturaci\u00f3n de Actividad
AnaliseFaturamentoTipoCliente=An\u00e1lisis de Facturaci\u00f3n
Milheiro=Miles
DtBase=Fecha base
FormulaDesc=Descripci\u00f3n f\u00f3rmula
IndiceDF=Tipo de indice
IndiceSolic=Indice solicitado
IndCombAprov=Indice de combustible aprobado
TE=Tiempo de espera
IndMOSolic=\u00cdndice de fuerza laboral solicitado
IndCombSolic=Indice de combustible solicitado
IndDFAprov=Tipo de indice aprobado
Formula=Formula
CodCarta=Codigo carta
DtCarta=Fecha de la carta
OperReaj=Operador de reajuste
DtReaj=Fecha de reajuste
HrReaj=Hora de reajuste
IndDFSolic=Tipo de indice solicitado
IInterno=I - Interno
Bloco=Bloque
AlaCela=Ala/Cela
Prontuario=Registro
Dieta=Dieta
Blindado=Blindado
Particular=Particular
Veiculos=Veh\u00edculos
EditarVeiculo=Editar Veh\u00edculo
CadastrarVeiculo=Registrarse Veh\u00edculo
NumeroVeiculoExiste=El n\u00famero de veh\u00edculo ya existe
Atividades=Actividades
Complemento=Complemento
CadastrarDieta=Registro de Dieta
Especificacao=Especificaci\u00f3n
SelecioneDieta=Seleccione una Dieta
NaoPodeAlterarGeral=No se puede editar la dieta general
ManutencaoItens=Mantenimiento del Art\u00edculo
CardapioDia=Men\u00fa del d\u00eda
CodigoCardapio=C\u00f3digo de Men\u00fa
Cardapio=Men\u00fa
Cardapios=Men\u00fas
SelecioneCardapio=Selecciona un Men\u00fa
PesquisarCardapio=Men\u00fa de B\u00fasqueda
DatasIncoerentes=La fecha de inicio no debe ser mayor que la fecha final
PesquisarCardapioDia=Buscar Men\u00fa del D\u00eda
ContratosGrpPagamento=Contratos (Grupo de Pago)
Tabela=Tabla
UltimoCadastrado=\u00daltimo cadastrado
Almoco=Almuerzo
AtualizarTabelaInterna=Actualizar tabla interna
TabelaPropostaComercial=Propuesta comercial (Descripci\u00f3n Servicios)
Janta=Cenar
BlocoAla=Bloco/Ala
CafeManha=Desayuno
Ceia=Merienda
CaixaForte=B\u00f3veda
SaldoReal=Equilibrio Real
QtdeRemessasCustodia=Env\u00edo de Custodia de Cantidad
ValorRemessasCustodia=Monto de Remesa de Custodia
QtdeRemessasPreparadas=Cantidad de Env\u00edos Preparados
ValorRemessasPreparadas=Valor de Los Env\u00edos Preparados
SelecioneCaixaForte=Selecciona un B\u00f3veda
Automatizada=Automatizado
TesourariaEntradas=Entradas de Efectivo
Numerarios=Efectivos
TipoMov=Tipo de Movimiento
QtdeVol=Cantidad de vol\u00famenes
CadastrarNumerario=Cadastrar numer\u00e1rio
Diurno=Diurno
Noturno=Noturno
Vespertino=Vespertino
SomentePendentes=Solamente pendientes
ContaTes=Cuenta de Tesorer\u00eda
ContaDesc=Descripci\u00f3n de cuenta
NRedOri=ID rapida origen
NRedDst=ID rapida de destino
Entrada=Entrada
Trajeto=Ruta
Faturar=Facturar
NRedDst=Destino
NRedOS=Cliente OS
RotaEnt=Ruta Entrada
OperEnt=Operador Entrada
HrEnt=Hora Entrada
RotaSai=Ruta Salida
OperSai=Operador Salida
DtSai=Fecha Salida
HrSai=Hora Salida
VolDh=VolDh
ValorDh=ValorDh
VolCh=VolCh
ValorCh=ValorCh
VolMd=VolMd
ValorMd=ValorMd
VolMet=VolMet
ValorMet=ValorMet
VolMEstr=VolMEstr
ValorMEstr=ValorMEstr
VolOutr=VolOutr
ValorOutr=ValorOutr
NRedCli=Cliente
CxForte=Boveda
MostrarHistoricoGeral=Historia General
CliOri=Origen
SelecioneModelo=Seleccione modelo
RotaJaGerada=Rutas ya generadas para esta fecha
Roteirizar=Enrutamiento
ColetarEntre=Recoger Entre
EntregarEntre=Entregar Entre
TesourariaDestino=Tesorer\u00eda de destino
Conta=Cuenta
MostrarRemessas=Mostrar Env\u00edos
RemessaEfetivada=Env\u00edo Efectivo
ClassifSrv=Clasificaci\u00f3n
Hora1O=Hora1O
Hora2O=Hora2O
Regiao1=Regiao1
Regiao2=Regiao2
Hora2D=Hora2D
QtdeGTV=QtdeGTV
SeqRota=SeqRota
Cancelamento=Cancelamento
Material=Material
AssitTecnica=Assist\u00eancia T\u00e9cnica
SelecioneRotaMapa=Seleccione una RUTA para ver el MAPA
OrigemRota=Origen de Ruta
LocalizacaoRota=Ubicaci\u00f3n de la Ruta
DestinoRota=Destino de Ruta
LocalizacaoPedido=Ubicaci\u00f3n del Pedido
QualReferenciaRotas=Qu\u00e9 referencia para cargar la sugerencia de RUTA?
QtdEnvelopes=Cantidad de envelopes
ReferenciaCarregamento=Refer\u00eancia de Rutas
MensagemImportacaoPedido=Se encontraron %s1 solicitudes en el archivo. \u00bfDesea continuar con la importaci\u00f3n?
TipoCedula=Tipo de Billete
PesquisarNumerario=Buscar efectivo
SelecioneNumerario=Seleccione un efectivo
SelecioneCodFilGuiaSerie=Seleccione Sucursal, Gu\u00eda e Serie
PesquisarNumerario=Buscar efectivo
Al\u00edvio=Alivio
Refor\u00e7o=Refuerzo
PesquisarNumerario=Buscar efectivo
CedulaMoeda=Billete/Moneda
PesquisarNumerario=Buscar efectivo
ValorProcDN=Money thousand
ValorProcMD=Coins thousand
ValorCustodia=Custody
Cedula=Billete
Moeda=Moneda
SelecioneItem=Seleccionar Elemento
Extrato=Extracto
ICMS=ICMS
NumeroVeiculo=N\u00famero de Veh\u00edculo
ConfirmarRecebimento=Confirmar Recibo
BotaoPanico=Bot\u00f3n de P\u00e1nico
RESUMO_ROTAS=Resumen de Rutas
EditarContrato=Editar contrato
IdentificacaoBanco=identificaci\u00f3n banco
PermissaoNegada=Permiso denegado
DiasMes=Dias del mes
DiasUteis=D\u00edas h\u00e1biles
HoraEntregaMenorColeta=Hora de entrega menor que la colecta
HorarioCadastrado=Hora ya registrada
SelecioneFrequencia=Seleccione una frecuencia
PesquisarCliente=B\u00fasqueda de Clientes
CoafComunic=COAF - Comunicaciones
Ordenacao=Ordenaci\u00f3n
Protocolo=Protocolo
CadastrarFrequencia=Registrar frecuencia
RotaModeloInexistente=No hay rutas en esa fecha
ModeloJaGerado=Modelo existente
HorarioColeta=Hora de Recogida
HorarioEntrega=Tiempo de Entrega
NaoExisteOS=No hay orden de trabajo con los datos proporcionados
ModeloNome=Nombre del Modelo
InformeNomeModelo=Ingrese el Nombre del Modelo
OmitirSolicitacoes=Omitir solicitudes no confirmadas
SolicitacaoSenhaMobile=Solicitud de contrase\u00f1a m\u00f3vil
FechaduraDesconectada=Cerradura desconectada
InformeSenhaGerada=Ingrese la contrase\u00f1a generada, Ej.: 9.888.888
CoordenadasNaoExistem=Las coordenadas no existen.
SenhaAceitaSucesso=Contrase\u00f1a enviada con \u00e9xito
SenhaRecusadaSucesso=Contrase\u00f1a rechazada con \u00e9xito
SenhaJaEnviada=Contrase\u00f1a ya enviada! Operaci\u00f3n no permitida
ErroSalvarBD=Error al guardar en la base de datos
ErroLerBD=Error al leer la informaci\u00f3n de la base de datos
CancelarFechadura=\u00bfDesea cancelar el env\u00edo de la contrase\u00f1a de bloqueo desconectado?
TipoOperacao=Tipo de operaci\u00f3n
LocalSolic=Local solicitado
HrSolic=Tiempo de solicitud
ChaveSrv=Clave servidor
FechIdentif=Identificador de cerradura
Comando_Ref=Comando Ref
SrvOK=Servidor OK
PW=Contrase\u00f1a
MatrChe=Matr\u00edcula jefe
PWVig1=Contrase\u00f1a de vigilante 1
Comando_Crt=Comando Crt
MatrVig1=Matr\u00edcula vigilante 1
Comando_Aceito=Comando_Aceito
TipoFechVei=TipoFechVei
TipoFechCli=TipoFechCli
StatusFechCli=Estado de cerradura del cliente
StatusFechVei=Estado del veh\u00edculo del cliente
CodigoFechCli=C\u00f3digo de cerradura del cliente
CodigoFechVei=C\u00f3digo del veh\u00edculo del cliente
SenhaUsr1FechCli=??Contrase\u00f1a Usr1 cerradura cliente
SenhaUsr1FechVei=??Contrase\u00f1a Usr1 cerradura veh\u00edculo
MACFechCli=MAC cerradura del cliente
MACFechVei=MAC cerradura del veh\u00edculo
InformeNomeModelo=Ingrese el Nombre del Modelo
MovimentacoesRecentesHoje=Movimenta\u00e7\u00f5es Recentes de Hoje
UltimasMovimentacoesHoje=\u00daltimas movimentacoes de hoje
MovimentacoesRecentesCofre=Movimenta\u00e7\u00f5es Recentes por Cofre
CoordenadasClienteNaoExistem=Las coordenadas del cliente no existen.
AprovarSenha=Aprobar contrase\u00f1a para esta ruta
ReprovarSenha=Desaprobar contrase\u00f1a para esta ruta
ExcecaoCodFechaduraCodFilInteiro=El c\u00f3digo de cerradura y/o codFil no se pueden convertir a un entero
SelecioneSolicitacao=Selecciona una solicitud
RecarregarTabela=Recargar tabla
InformarComposicoes=\u00bfQuieres continuar sin informar a las composiciones?
InsiraComposicao=Insertar Composiciones
login.falhageral<message>login.usuariobloqueado=Usuario Bloqueado 
ComposicoesCedulas=Composicion Billetes
ComposicoesMoedas=Composicion Monedas
DuploClickRoteirizar=Haga Doble Clic para Enrutar
NumeroPedido=Num\u00e9ro del Pedido
SelecionePrimeiroHorario=Selecciona la Primera Vez
SelecioneTodasRotas=Seleccionar Todas las Rutas
NomeChefe=Nombre jefe
HrChe=Tiempo jefe
MatrMot=Matr\u00edcula conductor
NomeMot=Nombre conductor
HrMot=Tiempo conductor
NomeVig1=Nombre vigilante 1
HrVig1=Tiempo vigilante 1
NomeVig2=Nombre vigilante 2
MatrVig2=Matr\u00edcula vigilante 2
HrVig2=Tiempo vigilante 2
NomeVig3=Nombre vigilante 3
HrVig3=Tiempo vigilante 3
MatrVig3=Matr\u00edcula vigilante 3
HorarioVazio=Hor\u00e1rio vazio
SomenteDestinoCxForte=Solo destino b\u00f3veda
GuiasEmCxForte=Gu\u00edas en b\u00f3veda
EntradaCxForte=Entrada de B\u00f3veda
CapturaCliente=Captura Cliente
GuiasMobile=Gu\u00edas m\u00f3vil
CapturaGTV=Captura GTV
CapturaAuto=Captura autom\u00e1tica
LocalizacaoAtual=Ubicaci\u00f3n Actual
InformeHora=Informar la Hora
DadosSalvosSucesso=Datos Guardados Exitosamente
Dinheiro=Dinero
PrevisaoHoraSaida=Previsi\u00f3n de hora de salida
ConfirmarExclusao=Realmente quieres eliminar esta ruta?
TrajetoExcluido=Route successfully Deleted
TrajetoExcutado=Eliminar no est\u00e1 permitido. La ruta fue ejecutada.
TrajetoJaExcluido=La ruta ha sido eliminada previamente
NaoExistemRotas=No hay Rutas
Ferramentas=Herramientas
BaixarServico=Servicio de Descarga
Atraso=Atraso
AtendimentoNaRota=Asistencia de Ruta
ErroObterCaixaForte=Incapaz de obtener b\u00f3vedas
ErroSemRotasNaData=No hay rutas en la fecha actual en esta sucursal.
DiaInvalido=Dia invalido
SelecioneEntrada=Selecciona una entrada
ImplementandoFeature=Esta caracter\u00edstica a\u00fan no est\u00e1 lista ...
GuiaPendenteTesouraria=Gu\u00eda pendiente en tesorer\u00eda. \u00a1Operaci\u00f3n no permitida!
GuiaEntrouEmCxForte=Guia ya ha entrado en la b\u00f3veda
GuiasCustodia=Gu\u00edas de cust\u00f3ria
VolumesDaGTV=Vol\u00famenes del GTV
TrajetoPrevistoGTV=Ruta prevista en GTV
ParadaNaoLocalizada=Parada no ubicada en esta ruta
ClienteGuiaRedefinido=El cliente de origen para esta gu\u00eda se restablecer\u00e1.
CancelarOperacao=\u00bfCancelar la operaci\u00f3n?
ClienteSelecionado=Cliente seleccionado
DesejaUtilizarParada=\u00bfQuieres usar esta parada?
HoraProg=Tiempo programado
ChegadaRota=Llegada de Ruta
MoedaReal=Real Brasile\u00f1o
MoedaDolar=Dollar
MoedaMexico=Peso Mexicano
MoedaEuro=Euro
MoedaBritaniva=Libra Brit\u00e1nica
GuiaEletronica=Gu\u00eda Electr\u00f3nica
GuiaManual=Gu\u00eda Manual
GuiaSaiuEmCxForte=Gu\u00eda ya est\u00e1 fuera de la b\u00f3veda
ExclusaoNaoPermitida=Exclusi\u00f3n no permitida
GuiaDeletadaSucesso=Gu\u00eda eliminada correctamente
KmTexto=KM
RotaSemParadaDisponivel=Ruta sin escalas disponible
ExcluirVolume=Eliminar Volumen
ExcluirGuia=Eliminar Gu\u00eda
AcaoEfetuadaSucesso=Datos Guardados con \u00c9xito!
PrenchaCamposObrigaatorios=Complete los campos requeridos
InformeGuias=Informar a Las Gu\u00edas
DuploClickCliente=Haga Doble Clic para Seleccionar Cliente
SelecioneCtrItem=Selecciona un art\u00edculo
EditarContratoAnexo=Editar Adjunto de Contrato
CadastrarContratoAnexo=Registrar Adjunto de Contrato
Patrimonial=Patrimonial
PesquisarPessoa=Buscar Persona
SelecionePessoa=Seleccionar Persona
DuploClickPessoa=Haga Doble Clic para Seleccionar Persona
SelecioneDataFinal=Seleccione la fecha final
DataSituacao=Fecha de Situaci\u00f3n
DesejaGerarMatrAuto=Quiere Generar ID Empleado Autom\u00e1ticamente?
AnoModelo=A\u00f1o modelo
Carroceria=Carroceria
Combustivel=Combustible
Gasolina=Gasolina
Alcool=Alcohol
Flex=Flex Gasolina/Alcohol
GasNatural=Gas Natural
Diesel=Di\u00e9sel
Querosene=Queroseno
Desativado=Discapacitados
Triflex=Triflex Gasolina/Alcohol/GNC
SelecionePedido=Selecciona un Pedido
RecarregarSenhas=Recargar contrase\u00f1as
PerguntaPedido=Quieres eliminar el Pedido:
MoedaPesoChileno=Peso Chileno
MoedaPesoColombiano=Peso Colombiano
TipoMoeda=Tipo de Moneda
Chassis=Chasis
dt_Compra=Fecha de Compra
RENAVAN=RENAVAN
dt_Ipva=Madurez IPVA
Seguro=Seguro
Seguradora=Aseguradora
Apolice=Pol\u00edtica
PRXRadio=PRX radio
ESN=ESN
Blindagens=Escudos
Cabine=Caba\u00f1a
Teto=Techo
Assoalho=Piso
Vidro=Vaso
Habilitacoes=Calificaciones
VistoriaPF=Inspecci\u00f3n PF
Vencimento=Vencimiento
VistoriaFabricante=Inspecci\u00f3n Fabricante
VistoriaQualidade=Inspecci\u00f3n Calidad
VistoriaQualidadeII=Vistoria Calidad II
VistoriaConformidade=Inspecci\u00f3n Conformidad
VistoriaConformidadeII=Inspecci\u00f3n Conformidad II
Tacografo=Tac\u00f3grafo
Alvara=Licencia
VistoriaQualidadeII=Inspecci\u00f3n Calidad II
VeicTerceiros=Veh\u00edculo de Terceros
NaoExisteCaixaForte=Caja Fuerte no encontrada en Branch
NaoHaDados=Sin Datos!
SaidasAutomaticas=Salidas autom\u00e1ticas
PerguntaClone=Est\u00e1 seguro de que desea clonar el Orden de servicio: 
OsClonadaSucesso=Orden de servicio clonada con \u00e9xito!
Clonar=Clonar Orden de Servicio
SPM=SPM
Ordenar=Ordenar
OrdenarPor=Ordenar Por
PosicoesRota=Posiciones de Ruta
PesquisarHorario=Horario de B\u00fasqueda
VisualizarMapa=Ver en el Mapa
SelecioneParaVisualizar=Seleccione una Posici\u00f3n de la Lista
ServicoCancelado=Servicio Cancelado
DistanciaBaixa=Distancia al Descarga
SemVolumes=Sin Vol\u00famenes
TipoGuia=Tipo de Comprobante
ServicoBaixado=Servicio Descargado
Excluido=Eliminado
TotalGeral=Total General
TotalRefeicao=Total por Comida
PercursoNaoEncontrado=Parada no encontrada para esta serie/gu\u00eda
RESUMO_ROTAS_PERIODO=Resumen de Rutas Por Per\u00edodo
SelecioneClienteSecaoCadastrado=Seleccione un cliente con Bloques/Alas registrado
TotalCafe=Caf\u00e9 Total
TotalAlmoco=Almuerzo Total
TotalJanta=Cenar Total
TotalCeia=Merienda Total
SerieNaoCadastrada=Series no registradas
CxForteNaoEncontrado=B\u00f3veda no encontrada
ResumoEfetividadeRota=Resumen de Efectividad Ruta
Fone=Tel\u00e9fono
Efetividade=Efectividad
Efetivos=Efectivos
Percentual=Porcentaje
Adiantados=Antes de Horario
Pendentes=Pendiente
ExcluirCardapio=Quieres eliminar el men\u00fa
DadosOperacionais=Datos Operacionales
DataAdmissao=Fecha de Admisi\u00f3n
RegistroDRI=Registro DRI
ApresentadoPor=Presentado Por
Regional=Regional
PrimeiroPeriodo=Primer Per\u00edodo
SegundoPeriodo=Segundo Per\u00edodo
dtRgEmissao=Emisi\u00f3n
Sindicato=Union
dtVencimentoExameMedico=Expiraci\u00f3n de Examen M\u00e9dico
dtVencimentoPsicotecnico=Madurez Psicot\u00e9cnica
Nacionalidade=Nacionalidad
Experiencia=Experiencia
Raca=Raza
Instrucao=Instrucci\u00f3n
EstadoCivil=Estado Civil
Emissao=Emisi\u00f3n
Reservista=Reservista
Categoria=Categor\u00eda
ExameCnhNum=N\u00famero de Examen CNH
CRM=CRM
TituloEleitor=T\u00edtulo del Votante
Zona=Zona
Naturalidade=Naturalidad
Conjuge=Esposa
Pai=Padre
Categoria=Categor\u00eda
DtExameCnhNum=Fecha de Examen CNH
CNPJLab=Laboratorio CNPJ
GrupoSanguineo=Grupo Sangu\u00edneo
DadosFolha=Ficha de Datos
InformacoesAdicionais=Informaciones Adicionales
Documentacao=Documentaci\u00f3n
TipoAdm=Tipo Adm
DefFis=Def Fis
CodigoOperacao=C\u00f3digo de Operaci\u00f3n
ContSind=Contribuci\u00f3n Sindical
Vinculo=Enlace
Jornada=Jornada
InstituicaoEnsino=Instituci\u00f3n Docente
AgenteIntegracao=Agente de Integraci\u00f3n
NaturezaEstagio=Pasant\u00eda en la Naturaleza
NivelEstagio=Nivel de Etapa
PrevisaoTermino=Fin del Pron\u00f3stico
TipoReintegracao=Tipo de Reintegraci\u00f3n
InfoPagtoJuizo=Informaci\u00f3n de Pago
DataReintegracao=Fecha de Reintegraci\u00f3n
DataFormacaoIni=Fecha de Formaci\u00f3n "comienzo"
DataFormacaoFim=Fecha de Formaci\u00f3n "Fin"
RegPoliciaFederal=Reg de la Polic\u00eda Federal
DataReciclagem=Fecha de Reciclaje
DataVencimentoCurso=Curso de Fecha de Vencimiento
ExportacaoGesp=Exportar Gesp
EscoltaArmada=Escort Armada
GrandesEventos=Grandes Eventos
ArmasNaoLetais=Armas no Letales
CNV=CNV
CadastroAfis=Registrarse Afis
RegMinTrabalho=Reg Ministerio Trabajo
DataEmissao=Emisi\u00f3n
SegurancaPessoal=Seguridad Personal
EdicaoPedidoNaoPermitida=Edici\u00f3n permitida solo para \u00f3rdenes pendientes
TotalDN=Total Dinero Nuevo
TotalMoeda=Total moneda
ChequesQtde=Cheques (cantidad)
ChequesValor=Cheques (valor)
TicketsValor=Tickets (valor)
KitTrocoQtde=Kit cambio cantidad
Diferenca=Diferencia
TotalDD=Total dinero da\u00f1ado
DataPedido=Fecha de la solicitud
Refeicoes=Las Comidas
GeradorQrCode=Generador de c\u00f3digo QR
NaoHaClietesQrCode=No hay clientes activos con CodPtoCli lleno
SaidaNumerario=Salida de efectivo
QtdCacambas=Cantidad de Cubos
FormaPagamento=Forma de Pago
SaidaTesouraria=Salida de Caja
GeracaoSuprimentos=Generaci\u00f3n de Suministros
GuiasJaGeradasSequencia=Ya hay gu\u00edas generadas para esta secuencia
OperacaoSucesso=Operaci\u00f3n realizada con \u00e9xito
CliquePesquisar=Haga clic en "Buscar" para ver los datos
CodCli3=CodCli3
MarcaATM=Marca ATM
TicketsQtde=Cantidad Tickets
Camera=Camera
TipoSrvRota=Tipo de Servicio Ruta
TpCliBrad=TpCliBrad
MatrConf=Matr\u00edcula Conferente
Conferente=Conferenciante
GuiaEmitidaCxForte=Gu\u00eda emitida y transferida a Caixa Forte
GuiaPendente=Emisi\u00f3n de la gu\u00eda pendiente
GuiaEmitidaNaoCxForte=Gu\u00eda emitida y no transferida a Caixa Forte
SelecionePeloMenosUmaTesouraria=Seleccione al menos un tesorer\u00eda
GuiasGeradas=Gu\u00edas generadas
ExistemGuiaPendentesSaida1=Existen
ExistemGuiaPendentesSaida2=Gu\u00edas pendientes para B\u00f3veda
NaoExistemGuiasProcessar=No hay gu\u00edas para procesar
ProcessamentoEfetuado=Procesamiento Exitoso
SomenteGuiasLacre=Solo Gu\u00edas Selladas
PessoaSemCodPessoaWeb=La persona no tiene cod Persona web
ResumoTrabalho=Resumen del Trabajo de Campo
Visitas=Visitas
Item=Articulo
RespostasPermitidas=Respuestas Permitidas
Inspetor=Inspector
Titulo2=T\u00edtulo
LocaisVisitados=Lugares Visitados
PreenchimentoObrigatorio=Respuesta Obligatoria
Fiscal=Supervisor
Anterior=Anterior
Proximo=Proximo
CandidatoTitulo=Candidato
SATMAXIMA=MAXIMA
SATFENIXX=FENIXX
PreenchimentoObrigatorio=Respuesta Obligatoria
PontoEletronicoSelecionado=Ponto Eletr\u00f4nico - Seleccionado
PontoEletronicoAnteriores=Punto Electr\u00f3nico - Anterior
Localizacao=Ubicaci\u00f3n
CliqueVisualizarFoto=Haga clic para ver la Foto
SemRegistrosAnteriores=No hay registros Anteriores
SATMAXIMA=MAXIMA
PostoSemCoordenadas=Puesto sin coordenadas
PontoEletronicoMensal=Ponto Eletr\u00f4nico - Mensualmente
SATPISCINAFACIL=PISCINA F\u00c1CIL
SATBIMBO=BIMBO
SATDELTACORP=DELTACORP
SATFORCAALERTA=FORCAALERTA
SATPETGOLDEN=PET GOLDEN
SATFORTEARARUAMA=FORTE ARARUAMA
SATBRINKSDEMO=BRINKS DEMO
SATBRINKS=BRINKS
SATFEDERAL=FEDERAL
SATSOSP=SOS ALARMES
Ferias=Vacaciones
Atrasos=Retrasos
HoraRegistrada=Tiempo Registrado
HoraPrevista=Hora Prevista
Manuais=Manuais
MensagemRecebida=Mensaje Recibida
CentralAlerta=Centro de alertas
PathSrv=Path
TipoMensagem=Tipo de mensaje
StatusMensagem=Estado del mensaje
QueueFech=Queue
FuncionarioEscalado=Empleado Escalado
FuncionarioAusente=Empleado Ausente
NomeAusente=Nombre Ausente
IndicadorHorasExtras=Indicador Tiempo Extra
HorasDiurnas=Horario Diurno
HorasNoturnas=Horas de Noche
NumeroHE=N\u00famero Horas Extra
Hospedagem=Alojamiento
Mesario=Mesa
DescricaoRegiao=Descripci\u00f3n Regi\u00f3n
Registros=Registros
MovimentacaoOperacional=Movimiento Operacional
TotaHorasDecimais=Horas Decimales Totales
RefeicoesSimbolo=Comidas($)
TransporteSimbolo=Transporte($)
HospedagemSimbolo=Alojamiento($)
Intrajornada=D\u00eda Libre
PagarHorasTipo2=Tipo de Pago 2 Horas
HoraExtra=Hora Extra
ReforcoNormal=Refuerzo Normal
ReforcoObras=Obras de Refuerzo
ReforcoEspecial=Refuerzo Especial
Cobertura=Techo
ComplementoReforco=Complemento de Refuerzo
NaoTemEfetivo=No Tiene Personal
Ausencia=Ausencia
PlantaoReserva=Deber de Reserva
Cortesia=Cortes\u00eda
AusenciaParcial=Ausencia Parcial
FaltaJustificada=Ausencia Justificada
FaltaInjustificada=Ausencia Injustificada
Folga=D\u00eda libre
Administrativa=Administrativo
Reciclagem=Reciclaje
AlmocoEfetivo=Almuerzo Efectivo
Escolta=Escolta
CoberturaReserva=Cobertura de Reserva
Compensar=Compensar
BancoHoras=Banco de Horas
SelecioneQueue=Seleccione queue
SATINVLRO=INVISEG
BaterPonto=Punto
InformeMatriculaSenha=Ingrese su registro y contrase\u00f1a
UsuarioSenhaIncorreta=Usuario o Contrase\u00f1a Incorrectos
MensagemLigarGPS=Para <b>registrar el punto</b> es necesario <b>activar la ubicaci\u00f3n</b> del dispositivo
PontoRegistrado=Punto Registrado con \u00c9xito
EmailIncorretoParaCPF=Correo electr\u00f3nico incorrecto para este CPF
PessoaNaoEhCandidata=Registro de persona que ya no es candidato
Parametro=Par\u00e1metro
InformeParametro=Ingrese el Par\u00e1metro
VerifiqueParam=Error al consultar datos. Verifique el par\u00e1metro configurado!
ErroAcessarArquivo=Error al acceder al archivo
ErroCriarDir=Error al crear el directorio
ErroDeletarArquivo=Error al eliminar el archivo
CadastroSucessoCandidato=Sus datos han sido registrados con \u00e9xito.
EdicaoSucessoCandidato=Datos actualizados exitosamente
ImoveisporSituacaoAgp=Imov\u00e9is por situa\u00e7\u00e3o
DescImoveisporSituacaoAgp=Quantidade de im\u00f3veis por situa\u00e7\u00e3o
ImoveisDiaTrab=Im\u00f3veis por dia de trabalho
DescImoveisDiaTrab=M\u00e9dia de im\u00f3veis por dia trabalhado
ImoveisporSituacaoDia=Evolu\u00e7ao im\u00f3veis por situa\u00e7\u00e3o
DescImoveisporSituacaoDia=Evolu\u00e7\u00e3o di\u00e1ria im\u00f3veis por situa\u00e7\u00e3o
InspecaoeTratamentoAgp=Inspe\u00e7\u00e3o e tratamento
DescInspecaoeTratamentoAgp=Inspe\u00e7\u00e3o e tratamento por tipo de im\u00f3vel
InspecaoTratamentoTipo=Evolu\u00e7\u00e3o inspe\u00e7\u00e3o e tratamento
DescInspecaoTratamentoTipo=Evolu\u00e7\u00e3o di\u00e1ria de inspe\u00e7\u00f5es e tratamentos
DepositosTipoAgp=Dep\u00f3sitos por tipo
DescDepositosTipoAgp=Dep\u00f3sitos identificados por tipo
DepositoAcaoAgp=A\u00e7\u00f5es tomadas
DescDepositoAcaoAgp=A\u00e7\u00f5es tomadas em dep\u00f3sitos identificados
DepositosEvolucao=Evolu\u00e7\u00e3o dep\u00f3sitos
DescDepositosEvolucao=Evolu\u00e7\u00e3o di\u00e1ria depositos identificados
EvolucaoDepositosTipo=Evolu\u00e7\u00e3o dep\u00f3sitos por tipo
DescEvolucaoDepositosTipo=Evolu\u00e7\u00e3o di\u00e1ria depositos identificados por tipo
AmostrasRoedoresAmostras=Im\u00f3veis inspecionados x Amostras, roedores e dengue
DescAmostrasRoedoresAmostras=Comparativo entre Inspe\u00e7\u00f5es e amostas, tocas e dengue
InspecionadosxDengue=Im\u00f3veis inspecionados x Dengue
DescInspecionadosxDengue=Im\u00f3veis inspecionados x Dengue nos ultimos tr\u00eas meses
ToqueAquiResponder=Toca aqu\u00ed para Responder
ToqueAquiAssinar=Toca aqu\u00ed para Suscribirte
ToqueTirarFoto=Toca para tomar una Foto
TipoBD=Tipo Base de Datos
CodEmpresa=Cod. Compa\u00f1ia
HostNameLocal=Host Name Local
HostNameWeb=Host Name WEB
ArquivoLogotipo=Archivo de Logotipo
ArquivoLogotipoNfe=Archivo de Logotipo NFe
EmailAdm=Correo Administrativo
Fotografias=Fotos
FusoHorario=Huso Horario
FusoSefaz=Huso Horario Sefaz
ToleranciaEscala=Tolerancia Escala (min)
ToleranciaPonto=Tolerancia Punto (min)
ToleranciaAcesso=Tolerancia Acceso (min)
NomeFilial=Nombre de la Sucursal
TrocarSenhaMobile=Cambiar la Contrase\u00f1a M\u00f3vil Diariamente
FonteDados=Fuente de Datos
ParamCadastrado=Par\u00e1metro ya registrado para esta Empresa
SATFIDELYS1=FIDELYS
SATFIDELYS=FIDELYS
ContasReceber=Cuentas a Recibir
DataNF=Fecha de la Factura
DtVencimento=Fecha de Vencimiento
DtPrevisaoPagto=Previsi\u00f3n de Pago
DiasAtraso=D\u00edas Retrasados
MensagemSucesso=Mensagem enviada com sucesso!
DtPagto=D\u00eda de Pago
ContasPagar=Cuentas a Pagar
Fornecedor=Proveedor
UltimaLocalizacaoSupervisor=Ubicaci\u00f3n del Supervisor
Fechaduras=Fechaduras
FechaduraLagardCrypto=01 - Lagard CryptoGard
FechaduraLagardSmart=02 - Lagard SmartGard
FechaduraLagardCombo=03 - Lagard ComboGard
FechaduraLagardCrypto04=04 - Lagard CryptoGard-BR
FechaduraLagardDynamic=05 - Lagard Dynamic
FechaduraMasHamington=11 - Mas Hamington
FechaduraTamborMecanico=21 - Tambor Mec\u00e2nico
FechaduraTamper=22 - Tamper
FechaduraHslPerto=30 - HSL Perto
FechaduraOutras=99 - Outras
TipoInstalacao=Tipo de instalaci\u00f3n
TipoInstalacaoAtm=01 - ATM
TipoInstalacaoAtm2=11 - ATM Fechadura 2
TipoInstalacaoAtm21=21 - ATM Fechadura 2
TipoInstalacaoVeiculo=2 - Ve\u00edculo
TipoInstalacaoVeiculoFechadura=12 - Ve\u00edculo Fechadura 2
TipoInstalacaoVeiculoFechadura22=22 - Ve\u00edculo Fechadura 3
SenhaManager=Senha Manager
PrazoRestante=Plazo Restante
ID=Identificaci\u00f3n
SemLatLon=Sin Lat/Lon
Dt_Demis=Fecha de Renuncia
AcessoNegado=Acceso no autorizado<br>P\u00f3ngase en contacto con su administrador del sistema.
AutorizacaoAcesso=Autorizaci\u00f3n de Acceso
EnviarAutorizacao=Enviar Autorizaci\u00f3n
Caracteres=caracteres
PessoaSemTelefone=Persona sin Tel\u00e9fono
DataAutMenor=La fecha debe ser mayor o igual que Hoy
HoraAutMenor=La Hora de Finalizaci\u00f3n Debe ser Mayor que la Hora de Inicio
HoraAutMenor2=La hora de inicio debe ser mayor que la hora actual
SPMEW=SPM EW
GuiaAutorizada=Gu\u00eda Autorizado
LatLonCaixaForte=Boveda sin latitud/longitud registrada
AtualizarMeusDados=Actualizar Datos
ComprovanteResidencia=Comprobante de Domicilio
AnoCheg=A\u00f1o de llegada
Ingresso=Billete
CTPS_UF=Seguridad Social Entidad Federativa
NovaMensagem=Nuevo Mensaje
UltimaEntrada=\u00daltima Entrada
UltimaSaida=\u00daltima Salida
ExclusaoRotaDia=Permitido eliminar solo la ruta del d\u00eda
SATASO=ASO
SATTECBAN=TECBAN
SATCEFOR=CEFOR
SATG5ESTRELAS=G5 ESTRELAS
SATGRIFFO=GRIFFO
SATROMMA=ROMMA
SATSEFIX=SEFIX
SATTAGUA=TAGU\u00c1 ENTULHOS
NaoEncontradoCentral=Persona no encontrada en la base de datos central
ChaveNaoEncontrada=Clave no Encontrada
StatusGuia=Estado de la pesta\u00f1a
Emitida=Emitido
ChaveExcluida=La clave informada est\u00e1 Deshabilitada
ChaveExpirada=La clave ingresada Caduc\u00f3
GeradoPor=Generado por
RPVdescr=RECIBO DE TRANSPORTE DE EFECTIVO PROVISIONAL
EdicaoNaoPermitida=Edici\u00f3n no permitida.<br>Servicio realizado.
ParadaEntrega=No se pueden editar las gu\u00edas: <b>Parada de Entrega</b>
SelecioneGuiaEmitida=Seleccione una pesta\u00f1a emitida
EmissaoDisponivel=Dispon\u00edvel para Emiss\u00e3o
EdicaoDisponivel=Edici\u00f3n Permitida
ResumoCaixa=Resumen de Efectivo
Todas=Todas
AnalisePorQtde=An\u00e1lisis de Recuento: Por Cantidad
AnalisePorValor=An\u00e1lisis de Recuento: Por Valor Recibido
AnalisePorQtdeHora=Cantidad Promedio por Hora
AnalisePorValorHora=Valor Recibido Medio por Hora
AnalisePorFilial=An\u00e1lisis de Recuento: por Sucursal
AnaliseQtdeDia=An\u00e1lisis de Cantidad Siaria
AnaliseValorDia=An\u00e1lisis de la Cantidad Recibida por D\u00eda
AnaliseContagemDet=An\u00e1lisis de Recuento Detallado
QtdeDH=Cant. Billetes
QtdeMD=Cant. Monedas
QuantidadeTotal=Cant. Total
ProdDH_Hr=Producci\u00f3n de Billetes - Hora
ProdMD_Hr=Producci\u00f3n de Moneda - Hora
ValorRecebido=Cantidad Recibida
InformeDataParaAcesso=Ingrese la Fecha de Acceso
DataInvalidaParaAcesso=Fecha de Acceso Inv\u00e1lida
InformeBaseDadosAcesso=Base de Datos de acceso a informes
InformeCodPessoaAcesso=Informar al Cod. Persona de Acceso
InformeSeqRotaAcesso=Informe de Secuencia de Ruta de Acceso
SemPermissaoAcesso=Sin Permiso de Acceso
DesbloquearUsuario=Desbloquear Usuario
ConfirmaDesbloquearUsuario=\u00bfEst\u00e1 seguro de que desea desbloquear el usuario?
UsuarioDesbloqueado=Usuario Desbloqueado
SATGLOBAL=GLOBAL
Produtividade=Productividad
MilheirosHr=Miles de Horas
PeriodoFechado=Periodo Cerrado
FichaFuncional=Hoja Funcional
SelecioneTransacao=Seleccione una Transacci\u00f3n
SelecioneParada=Seleccione una Parada
TransacoesSemVinculo=Transacciones sin Enlace
SemRegistrosTransacao=No hay rutas en el per\u00edodo seleccionado con transacciones con tarjeta
TotalOnePay=Total 1Pay
TaxaOnePay=Taxa 1Pay
LiquidoOnePay=L\u00edquido 1Pay
Vincular=Vincular
Crescente=Creciente
Decrescente=Descendente
LogContraCheque=Registro de Cheque de Pago
Contracheques=Cheques de Pago
PosicoesSupervisor=Posiciones de Supervisor
CliqueParaPosicaoDetalhada=Haga Clic para Obtener Posiciones Detalladas
EditarResposta=Editar Respuesta
PesquisarGuia=Gu\u00eda de B\u00fasqueda
SATNSF=SATNSF
UploadArquivo=Subir Archivo
SelecionarArquivo=Seleccione Archivo
TokenNaoInformado=Token no Informado
TokenNaoEncontrado=Token no Encontrado
InformeNomeArquivo=Ingrese el nombre del archivo
ArquivoCarregadoComSucesso=Documento cargado Exitosamente
GuiaAssistencia=GU\u00cdA DE ASISTENCIA T\u00c9CNICA
PedidoCli=Pedido del Cliente
NaoInformado=No Informado
ImgPadrao=Imagen Est\u00e1ndar
NovaFoto=Nueva Imagen
FotoPadrao=Foto Predeterminada
TornarPadrao=Hacer Predeterminada
GuiasEgtv=Guias (GTVe)
PortalCliente=Portal del Cliente
UnidadesCliente=Unidades de Cliente
MapaTesouraria=Mapa del Tesoro
IdUsuario=ID Usuario
CertidaoNascimento=Acta de Nacimiento
CertidaoCasamento=Acta de Matrimonio
Passaporte=Pasaporte
CarteiraVigilante=Documento de Vigilante
NenhumArquivoEncontrado=Ning\u00fan archivo encontrado
Seguranca=La Seguridad
AutorizacaoAcesso=Autorizaci\u00f3n de Acceso
SegSex=LunVie
Sab=Sab
Dom=Dom
Fer=Fiesta
Finalidade=Objetivo
OperAut=Oper Aut
Dt_Aut=Fecha Aut
Hr_Aut=Hora Aut
Autorizar=Autorizar
DeptoDestino=Departamento de Destino
AreaSeguranca=Zona de Seguridad
Area=Zona
ConfirmaAutorizacao=\u00bfConfirmar autorizaci\u00f3n?
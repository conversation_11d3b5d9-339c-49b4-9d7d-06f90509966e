/*
 */
package br.com.sasw.arquivos;

import SasBeans.Filiais;
import SasBeans.PropCml;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import com.itextpdf.text.BadElementException;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.pdf.draw.LineSeparator;
import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public final class CabecalhoProposta extends PdfPageEventHelper {

    protected PdfPTable cabecalho, rodape, data;
    protected float cabecalhoHeight, rodapeHeight;
    protected boolean primeiraPagina = true;

    public CabecalhoProposta(Filiais filial, PropCml proposta, String caminho) {
        try {
            Cabecalho(filial, proposta, caminho);
            Rodape(filial);
        } catch (Exception e) {

        }
    }

    public void Cabecalho(Filiais filial, PropCml proposta, String caminho) throws IOException, BadElementException {
        float[] columnWidths = {1, 1, 10};
        cabecalho = new PdfPTable(columnWidths);
        cabecalho.setTotalWidth(523);
        cabecalho.setLockedWidth(true);
        cabecalho.setSplitLate(false);
        Image img = Image.getInstance(caminho);
        Font f1, f2, f3;
        f1 = new Font(Font.FontFamily.COURIER, 15, Font.BOLD, BaseColor.GRAY);
        f2 = new Font(Font.FontFamily.HELVETICA, 8, Font.ITALIC);
        f3 = new Font(Font.FontFamily.HELVETICA, 8);
        Paragraph p1, p2, p3, p4;
        p1 = new Paragraph(filial.getRazaoSocial().toUpperCase(), f1);
        p1.setSpacingAfter(8);
        p1.setAlignment(Element.PARAGRAPH);
        LineSeparator line = new LineSeparator();
        line.setLineColor(BaseColor.GRAY);
        line.setOffset(-6);
        p1.add(line);

        //LINHA 1
        PdfPCell cell = new PdfPCell(img);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setColspan(2);
        cabecalho.addCell(cell);
        cell = new PdfPCell(p1);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setBorder(Rectangle.NO_BORDER);
        cabecalho.addCell(cell);

        //LINHA 2
        cell = new PdfPCell(new Paragraph("", f2));
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setColspan(3);
        cabecalho.addCell(cell);

        //LINHA 3
        p2 = new Paragraph("Proposta:", f2);
        p2.setAlignment(Element.PARAGRAPH);
        cell = new PdfPCell(p2);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setBorder(Rectangle.NO_BORDER);
        cabecalho.addCell(cell);
        p3 = new Paragraph(proposta.getNumero().toBigInteger() + " / " + proposta.getData().getYear(), f3);
        p3.setAlignment(Element.PARAGRAPH);
        cell = new PdfPCell(p3);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setColspan(2);
        cabecalho.addCell(cell);

        //LINHA 4
        line = new LineSeparator();
        line.setLineColor(BaseColor.BLACK);
        line.setOffset(10);
        p4 = new Paragraph();
        p4.add(line);
        p4.setSpacingAfter(-10);
        cell = new PdfPCell(p4);
        cell.setColspan(3);
        cell.setBorder(Rectangle.NO_BORDER);
        cabecalho.addCell(cell);

        data = new PdfPTable(1);
        data.setTotalWidth(523);
        data.setLockedWidth(true);
        data.setSplitLate(false);
        Paragraph p = new Paragraph(filial.getCidade() + "-" + filial.getUF() + ", "
                + proposta.getData().getDayOfMonth() + " de " + Messages.getMessageS(proposta.getData().getMonth().toString()) + " de " + proposta.getData().getYear(), f3);
        p.setIndentationRight(30);
        p.setSpacingAfter(10);
        p.setAlignment(Element.ALIGN_RIGHT);
        cell = new PdfPCell(p);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        data.addCell(cell);

        cabecalhoHeight = cabecalho.getTotalHeight() + data.getTotalHeight() + 10;

    }

    public void Rodape(Filiais filial) throws Exception {
        rodape = new PdfPTable(2);
        rodape.setTotalWidth(523);
        rodape.setLockedWidth(true);
        rodape.setSplitLate(false);
        Font f = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL, BaseColor.GRAY);
        Paragraph p1, p2, p3, p4;
        p1 = new Paragraph(filial.getEndereco(), f);
        PdfPCell cell = new PdfPCell(p1);
        cell.setBorder(Rectangle.NO_BORDER);
        rodape.addCell(cell);
        cell = new PdfPCell(new Paragraph("", f));
        cell.setBorder(Rectangle.NO_BORDER);
        rodape.addCell(cell);

        p2 = new Paragraph(filial.getBairro() + " - " + filial.getCidade() + " - " + filial.getUF(), f);
        cell = new PdfPCell(p2);
        cell.setBorder(Rectangle.NO_BORDER);
        rodape.addCell(cell);
        cell = new PdfPCell(new Paragraph("", f));
        cell.setBorder(Rectangle.NO_BORDER);
        rodape.addCell(cell);

        p3 = new Paragraph(((null != filial.getCEP() || !filial.getCEP().equals(""))
                ? "CEP: " + FuncoesString.formatarString(filial.getCEP(), "##.### - ###") : "")
                + " - Tel: " + ((null != filial.getFone() || !filial.getFone().equals(""))
                ? FuncoesString.formatarString(filial.getFone(), "(##) ########?#") : filial.getFone()), f);
        cell = new PdfPCell(p3);
        cell.setBorder(Rectangle.NO_BORDER);
        rodape.addCell(cell);
        p4 = new Paragraph("", f);
        LineSeparator line = new LineSeparator();
        line.setLineColor(BaseColor.GRAY);
        line.setOffset(4);
        p4.add(line);
        cell = new PdfPCell(p4);
        cell.setBorder(Rectangle.NO_BORDER);
        rodape.addCell(cell);

        rodapeHeight = rodape.getTotalHeight();
    }

    public float getCabecalhoHeight() {
        return cabecalhoHeight;
    }

    public float getRodapeHeight() {
        return rodapeHeight;
    }

    @Override
    public void onEndPage(PdfWriter writer, Document document) {
        cabecalho.writeSelectedRows(0, -1,
                document.left(),
                document.top() + ((document.topMargin() - 30 + cabecalhoHeight) / 2),
                writer.getDirectContent());
        rodape.writeSelectedRows(0, -1,
                document.left(),
                document.bottom() + ((document.bottomMargin()) / 2),
                writer.getDirectContent());
        if (primeiraPagina) {
            data.writeSelectedRows(0, -1,
                    document.left(),
                    document.top() + 15,
                    writer.getDirectContent());
        }
        primeiraPagina = false;
    }
}

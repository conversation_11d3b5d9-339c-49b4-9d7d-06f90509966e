/*
 */
package br.com.sasw.conversores;

import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversor0")
public class ConversorZero implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            return new BigDecimal(value.toString()).toBigInteger().toString();
        } catch (Exception e) {
            return value.toString();
        }
    }
}

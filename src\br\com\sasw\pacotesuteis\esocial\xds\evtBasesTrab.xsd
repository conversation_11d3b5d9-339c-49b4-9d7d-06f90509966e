﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtBasesTrab/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtBasesTrab/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtBasesTrab">
                    <xs:annotation>
                        <xs:documentation>Evento Bases por Trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do evento de retorno</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="nrRecArqBase">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Recibo do arquivo de origem</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indApuracao">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="perApur">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideTrabalhador">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do Trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="procJudTrab" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Processos judiciais do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="nrProcJud">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="20"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codSusp">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                                <xs:pattern value="\d{1,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoCpCalc" minOccurs="0" maxOccurs="9">
                                <xs:annotation>
                                    <xs:documentation>Calculo da contribuicao previdenciaria do segurado</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="tpCR">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo de Receita - CR</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:integer">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrCpSeg">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Valor da contribuicao do segurado calculada</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrDescSeg">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Valor efetivamente descontado do segurado</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoCp" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Informacoes sobre bases e valores das contribuicoes sociais</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ideEstabLot" maxOccurs="unbounded">
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do estabelecimento ou obra e da lotacao tributaria</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpInsc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrInsc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Número de Inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{8,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codLotacao">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoCategIncid" maxOccurs="10">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes relativas à matricula e categoria do trabalhador e tipos de incidencias</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="matricula" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Matricula</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codCateg">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Categoria</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{3}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="indSimples" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicador de Contribuicao Substituida</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoBaseCS" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes sobre bases de calculo, descontos e deducoes de CS</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="ind13">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Indicativo 13°</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="tpValor">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de valor</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d{1,2}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="valor">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="calcTerc" minOccurs="0" maxOccurs="2">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Calculo das contribuicoes sociais devidas a Outras Entidades e Fundos</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpCR">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo de Receita - CR</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrCsSegTerc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor da contribuicao social devida aos Terceiros</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrDescTerc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor efetivamente descontado</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

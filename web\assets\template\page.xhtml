<ui:composition template="page-base.xhtml"
                xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <ui:define name="template-header">
        <a href="../index.xhtml" class="logo">
            <span class="logo-mini">
                <img src="../assets/images/logo-mini.png" width="50" height="50"/>
            </span>
            <span class="logo-lg">
                <img src="../assets/images/logo-lg.png" width="200" height="50"/>
            </span>
            <span class="logo">
                <ui:insert name="logo-lg"/>
            </span>
        </a>
        <nav class="navbar navbar-static-top">
            <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
                <span class="sr-only"></span>
            </a>
            <div class="navbar-custom-menu">
                <ui:insert name="top-menu"/>
            </div>

            <div class="center-navbar">
                <ui:insert name="center-header"/>
            </div>
            <div class="mobile-header">
                <h4>TESTE</h4>
            </div>
        </nav>
    </ui:define>

    <ui:define name="logo-lg2">
        <img src="../assets/images/logo-lg.png" width="225"/>
    </ui:define>

    <ui:define name="template-menu">
        <!-- side menu -->
        <aside id="sidebar" class="main-sidebar slideout-menu">
            <div>
                <ui:insert name="menu-begin"/>
                <ui:fragment >
                    <div class="sidebar-form">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" placeholder="#{adm['menu.search.placeholder']}" autocomplete="off"
                                   onclick="this.value = '';searchLeftMenu('')"
                                   onkeyup="searchLeftMenu(this.value)" onblur="this.value = '';hideMenuResults()"/>
                            <span class="input-group-btn">
                                <button type="submit" name="search" id="search-btn" class="btn btn-flat"><i
                                        class="fa fa-search"></i>
                                </button>
                            </span>

                        </div>

                        <ul id="menu-search" class="nav navbar-nav" role="menu">
                            <li class="dropdown" >
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="true"/>

                                <ul class="dropdown-menu" role="menu">

                                </ul>
                            </li>
                        </ul>
                    </div>
                </ui:fragment>
                <section class="sidebar">
                    <ui:insert name="menu"/>
                </section>
                <ui:insert name="menu-end"/>
            </div>
        </aside>
    </ui:define>

    <ui:define name="body-end">
        <ui:fragment>
            <script type="text/javascript">
                $(document).ready(function () {
                    $('.slideout-menu').removeClass('slideout-menu');
                    $('.slideout-panel').removeClass('slideout-panel');
                });
                
                window.onscroll = function() {scrollFunction();};

                function scrollFunction() {
                  if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    document.getElementById("scrollTop").style.display = "block";
                  } else {
                    document.getElementById("scrollTop").style.display = "none";
                  }
                }
            </script>
        </ui:fragment>
        <ui:insert name="body-end"/>
    </ui:define>   
</ui:composition>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("ambienteESocial")
public class ConversorAmbienteeSocial implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            switch (value.toString().replace(".0", "")) {
                case "1":
                    return "Produção";
                case "2":
                    return "Homologação";
                default:
                    return value.toString();
            }
        } catch (Exception e) {
            return value.toString();
        }
    }
}

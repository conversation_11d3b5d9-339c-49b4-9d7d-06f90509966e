﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTabCargo/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTabCargo/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTabCargo">
                    <xs:annotation>
                        <xs:documentation>Evento Tabela <PERSON> Cargos</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoCargo">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do cargo</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideCargo" type="TideCargo">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do Cargo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosCargo" type="TDadosCargo">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes do Cargo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideCargo" type="TideCargo">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao do cargo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosCargo" type="TDadosCargo">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes do cargo</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideCargo" type="TideCargo">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do registro que sera excluido</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TideCargo">
        <xs:annotation>
            <xs:documentation>Identificacao do cargo e periodo de validade</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codCargo">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Cargo</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosCargo">
        <xs:annotation>
            <xs:documentation>Informacoes do Cargo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nmCargo">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do Cargo</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codCBO">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Classificacao Brasileira de Ocupacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{4,6}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="cargoPublico" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Detalhamento de informacoes exclusivas para Cargos e Empregos Publicos</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="acumCargo">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Preencher com o cOdigo correspondente à possibilidade de acumulacao de cargos:
                                        1 - Nao acumulavel;
                                        2 - Profissional de Saude;
                                        3 - Professor;
                                        4 - Técnico/Cientifico</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="contagemEsp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Preencher com o cOdigo correspondente a possibilidade de contagem de tempo especial:
                                        1 - Nao;
                                        2 - Professor (Infantil, Fundamental e Médio);
                                        3 - Professor de Ensino Superior, Magistrado, Membro de Ministério Publico, Membro do Tributnal de Contas (com ingresso anterior a 16/12/1998 EC nr 20/98)</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="dedicExcl">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Indicar se é cargo de dedicacao exclusiva:
                                        S - Sim;
                                        N - Nao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="[N|S]"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="leiCargo">
                            <xs:annotation>
                                <xs:documentation>Lei que criou/extinguiu/reestruturou o cargo</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="nrLei">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Numero da Lei</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="1"/>
                                                <xs:maxLength value="12"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="dtLei">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Data da Lei</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:date">
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="sitCargo">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Situacao gerada pela Lei Preencher com uma das opcoes:
                                                    1 - Criacao;
                                                    2 - Extincao;
                                                    3 - Reestruturacao</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

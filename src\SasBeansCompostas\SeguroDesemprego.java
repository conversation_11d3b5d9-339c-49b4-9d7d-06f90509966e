package SasBeansCompostas;

import SasBeans.Cargos;
import SasBeans.FPMensal;
import SasBeans.FPRescisoes;
import SasBeans.Filiais;
import SasBeans.Funcion;

/**
 *
 * <AUTHOR>
 */
public class SeguroDesemprego {

    private FPMensal fpmensal1;
    private FPMensal fpmensal2;
    private FPMensal fpmensal3;
    private FPRescisoes fprescisoes;
    private Cargos cargos;
    private Funcion funcion;
    private Filiais filiais;

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public FPMensal getFpmensal1() {
        return fpmensal1;
    }

    public void setFpmensal1(FPMensal fpmensal) {
        this.fpmensal1 = fpmensal;
    }

    public FPMensal getFpmensal2() {
        return fpmensal2;
    }

    public void setFpmensal2(FPMensal fpmensal) {
        this.fpmensal2 = fpmensal;
    }

    public FPMensal getFpmensal3() {
        return fpmensal3;
    }

    public void setFpmensal3(FPMensal fpmensal) {
        this.fpmensal3 = fpmensal;
    }

    public FPRescisoes getFprescisoes() {
        return fprescisoes;
    }

    public void setFprescisoes(FPRescisoes fprescisoes) {
        this.fprescisoes = fprescisoes;
    }

    public Cargos getCargos() {
        return cargos;
    }

    public void setCargos(Cargos cargos) {
        this.cargos = cargos;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

}

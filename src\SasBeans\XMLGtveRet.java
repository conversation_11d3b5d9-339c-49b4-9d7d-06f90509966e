/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class XMLGtveRet {
    public String Protocolo;
    public String ChaveGTVE;
    public String XML_Envio;
    public String XML_Retorno;
    public String Link;
    public XMLGtveRetDadosAdic Dados_adic;

    public String getProtocolo() {
        return Protocolo;
    }

    public void setProtocolo(String Protocolo) {
        this.Protocolo = Protocolo;
    }

    public String getChaveGTVE() {
        return ChaveGTVE;
    }

    public void setChaveGTVE(String ChaveGTVE) {
        this.ChaveGTVE = ChaveGTVE;
    }

    public String getXML_Envio() {
        return XML_Envio;
    }

    public void setXML_Envio(String XML_Envio) {
        this.XML_Envio = XML_Envio;
    }

    public String getXML_Retorno() {
        return XML_Retorno;
    }

    public void setXML_Retorno(String XML_Retorno) {
        this.XML_Retorno = XML_Retorno;
    }

    public String getLink() {
        return Link;
    }

    public void setLink(String Link) {
        this.Link = Link;
    }

    public XMLGtveRetDadosAdic getDados_adic() {
        return Dados_adic;
    }

    public void setDados_adic(XMLGtveRetDadosAdic Dados_adic) {
        this.Dados_adic = Dados_adic;
    }
}

/*
*/
/* 
    Created on : Jan 2, 2017, 4:47:45 PM
    Author     : Richard
*/

.dlgquestoes .ui-dialog, .dlgquestoes .ui-dialog-content{
    padding: 5px !important;
}

.botao .ui-panelgrid .ui-panelgrid-cell, .botao .ui-panelgrid-cell{
    padding: 0px !important;
}

.ui-lightbox-nav-left, .ui-lightbox-nav-right{
    display: none !important;
}
.ui-lightbox-caption-text{
    color:white;
}

.ui-icon-calendar {
    background-image:  url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px;
    height: 40px;
    left: -1px !important;
    margin-left: 0px !important;
    top: -1px !important;
    margin-top: 0px !important;
}


.calendario .ui-state-active, .calendario .ui-state-highlight{
    background-color: #022a48 !important;
    color: white !important;
}

.calendario .ui-datepicker-trigger{
    background: transparent !important;
}

.calendario .ui-inputfield{
    display: none !important;
}

.calendarios .ui-inputfield{
    width: 90px;
}

.calendarioDia .ui-inputfield{
    width: 190px;
}


.fotoPosto .ui-widget-content{
    background: transparent;
} 
.fotoPosto .ui-galleria{
    width: 512px !important;
}
.fotoPosto .ui-galleria-filmstrip-wrapper{
    bottom: 75px;
    visibility: hidden;
} 
.fotoPosto .ui-galleria-nav-next,
.fotoPosto .ui-icon,
.fotoPosto .ui-icon-circle-triangle-e{ 
    top: 0px !important; 
} 
.fotoPosto .ui-galleria-nav-next{
    right: 50px;
}
.fotoPosto .ui-galleria-nav-prev{
    left: 50px;
}
.fotoPosto .ui-galleria-panel,
.fotoPosto .ui-galleria-panel-wrapper{ 
    height: 200px !important; 
    width: 512px !important;
} 

.fotoFuncion .ui-galleria-filmstrip-wrapper{ 
    bottom: 150px !important;
    height: 170px !important;
    right: 120px !important;
    width: 40px !important;
} 
.fotoFuncion .ui-galleria-filmstrip {
    width: 40px !important;
    left:0px !important;
}
.fotoFuncion .ui-galleria-frame,
.fotoFuncion .ui-galleria-frame-content,
.fotoFuncion .ui-galleria-frame-image{
    width: 40px !important; 
    height: 40px !important; 
}
.fotoFuncion .ui-galleria-nav-next,
.fotoFuncion .ui-icon-circle-triangle-e{ 
    top: 0px !important; 
} 
.fotoFuncion .ui-galleria-panel,.fotoFuncion .ui-galleria-panel-wrapper{
    height: 170px !important;
    width: 340px !important;
}
.fotoFuncion .ui-galleria-panel{
    top: 20px !important;
}

.fotoFuncion .ui-galleria{
    width: 340px !important;
    height: 170px !important;
}

.dialogo .ui-widget-content{
    background: transparent;
    padding: 0px;
}
.dialogo .ui-panelgrid-cell{
    display: table-cell;
    vertical-align: top;
}
.dialogo .ui-dialog-content{
    padding: 0px 15px 15px 0px !important;
    height: 620px !important;
}

.contrato .ui-autocomplete-panel {
    width: 250px !important;
}
.contrato .ui-autocomplete-input{
    width: 250px !important;
}

.contratoP .ui-autocomplete-panel {
    width: 280px !important;
}
.contratoP .ui-autocomplete-input{
    width: 280px !important;
}

.posto .ui-autocomplete-panel {
    width: 315px !important;
}
.posto .ui-autocomplete-input{
    width: 315px !important;
}

.postoP .ui-autocomplete-panel {
    width: 280px !important;
}
.postoP .ui-autocomplete-input{
    width: 280px !important;
}

.cliente .ui-autocomplete-panel {
    width: 250px !important;
}
.cliente .ui-autocomplete-input{
    width: 250px !important;
}

.clienteP .ui-autocomplete-panel {
    width: 280px !important;
}
.clienteP .ui-autocomplete-input{
    width: 280px !important;
}

.ui-state-disabled, .ui-widget-content .ui-state-disabled{
    opacity: 0.7;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.ui-icon-calendarios{
    background-image: url('../img/icone_escaladodia_40.png') !important;
    background-position: center center !important;
    width: 40px !important;
    height: 40px !important;
    left: -1px !important;
    margin-left: 0px !important;
    top: -1px !important;
    margin-top: 0px !important;
}

.botao{
    width: 40px !important;
    height: 40px !important;
    background: transparent !important;
    border: none;
    align-self: center;
    position: inherit;
    padding: 0;
    margin-right: 0px;
    margin-top: -10px;
    box-shadow: none;
}

.botao .ui-button-text{
    display: none;
}

.botao .ui-icon{
    position: relative;
    left: -1px !important;
    top: 12px !important;
    margin-left: 0px;
    margin-top: 0px;
}

.ui-button-icon-left{
    left: auto;
}

.dialogosupervisao .ui-dialog-content{
    padding: 0px 0px 0px 0px !important;
}
.dialogosupervisao .ui-widget-content{
    background: transparent;
}

.panelTelaSupervisao{
    width: 100%;
}

.cabecalhoSupervisao{
    text-align: center;
}

.detalhesSupervisao{
    padding: 5px 5px 5px 5px;
}
@media all and (max-width: 768px) {
    .overlay{
        top: 10px !important;
    }
}
@media all and (min-width: 768px) {
    .panelTelaSupervisao{
        width: 1024px;
    }
    .cabecalhoSupervisao{
        text-align: left;
    }
    .overlay{
        right: 100px !important;
        left: auto !important;
    }
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}
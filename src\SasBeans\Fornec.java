package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Fornec {

    private BigDecimal codigo;
    private BigDecimal codFil;
    private BigDecimal grupo;
    private String situacao;
    private String empresa;
    private String fantasia;
    private String CEP;
    private String endereco;
    private String bairro;
    private String cidade;
    private BigDecimal codCidade;
    private String UF;
    private String fone;
    private String fone2;
    private String fax;
    private String contato;
    private String cargo;
    private String email;
    private String atividade;
    private String dtNasc;
    private Integer prazoPgto;
    private Integer prazoEntr;
    private Integer Ttribut;
    private String foraEst;
    private String CNPJ;
    private String InscEst;
    private String IM;
    private String CPF;
    private String Ident;
    private String RGEmiss;
    private String banco;
    private String agencia;
    private String agenciaDV;
    private String contaC;
    private String contaCDv;
    private String conta_Ctb;
    private String placa;
    private String Obs;
    private String Operador;
    private String dtAlter;
    private String hrAlter;

    public Fornec() {
        this.codigo = new BigDecimal("0");
        this.codFil = new BigDecimal("0");
        this.grupo = new BigDecimal("0");
        this.situacao = "";
        this.empresa = "";
        this.fantasia = "";
        this.CEP = "";
        this.endereco = "";
        this.bairro = "";
        this.cidade = "";
        this.codCidade = new BigDecimal("0");
        this.UF = "";
        this.fone = "";
        this.fone2 = "";
        this.fax = "";
        this.contato = "";
        this.cargo = "";
        this.email = "";
        this.atividade = "";
        this.dtNasc = "";
        this.prazoPgto = 0;
        this.prazoEntr = 0;
        this.Ttribut = 0;
        this.foraEst = "";
        this.CNPJ = "";
        this.InscEst = "";
        this.IM = "";
        this.CPF = "";
        this.Ident = "";
        this.RGEmiss = "";
        this.banco = "";
        this.agencia = "";
        this.agenciaDV = "";
        this.contaC = "";
        this.contaCDv = "";
        this.conta_Ctb = "";
        this.placa = "";
        this.Obs = "";
        this.Operador = "";
        this.dtAlter = "";
        this.hrAlter = "";
    }

    public BigDecimal getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        try {
            this.codigo = new BigDecimal(codigo);
        } catch (Exception e) {
            this.codigo = new BigDecimal("0");
        }
    }

    public BigDecimal getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        try {
            this.codFil = new BigDecimal(codFil);
        } catch (Exception e) {
            this.codFil = new BigDecimal("0");
        }
    }

    public BigDecimal getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        try {
            this.grupo = new BigDecimal(grupo);
        } catch (Exception e) {
            this.grupo = new BigDecimal("0");
        }
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getFantasia() {
        return fantasia;
    }

    public void setFantasia(String fantasia) {
        this.fantasia = fantasia;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public BigDecimal getCodCidade() {
        return codCidade;
    }

    public void setCodCidade(String codCidade) {
        try {
            this.codCidade = new BigDecimal(codCidade);
        } catch (Exception e) {
            this.codCidade = new BigDecimal("0");
        }
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getFone() {
        return fone;
    }

    public void setFone(String fone) {
        this.fone = fone;
    }

    public String getFone2() {
        return fone2;
    }

    public void setFone2(String fone2) {
        this.fone2 = fone2;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAtividade() {
        return atividade;
    }

    public void setAtividade(String atividade) {
        this.atividade = atividade;
    }

    public String getDtNasc() {
        return dtNasc;
    }

    public void setDtNasc(String dtNasc) {
        this.dtNasc = dtNasc;
    }

    public Integer getPrazoPgto() {
        return prazoPgto;
    }

    public void setPrazoPgto(Integer prazoPgto) {
        this.prazoPgto = prazoPgto;
    }

    public Integer getPrazoEntr() {
        return prazoEntr;
    }

    public void setPrazoEntr(Integer prazoEntr) {
        this.prazoEntr = prazoEntr;
    }

    public Integer getTtribut() {
        return Ttribut;
    }

    public void setTtribut(Integer Ttribut) {
        this.Ttribut = Ttribut;
    }

    public String getForaEst() {
        return foraEst;
    }

    public void setForaEst(String foraEst) {
        this.foraEst = foraEst;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getInscEst() {
        return InscEst;
    }

    public void setInscEst(String InscEst) {
        this.InscEst = InscEst;
    }

    public String getIM() {
        return IM;
    }

    public void setIM(String IM) {
        this.IM = IM;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getIdent() {
        return Ident;
    }

    public void setIdent(String Ident) {
        this.Ident = Ident;
    }

    public String getRGEmiss() {
        return RGEmiss;
    }

    public void setRGEmiss(String RGEmiss) {
        this.RGEmiss = RGEmiss;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getAgenciaDV() {
        return agenciaDV;
    }

    public void setAgenciaDV(String agenciaDV) {
        this.agenciaDV = agenciaDV;
    }

    public String getContaC() {
        return contaC;
    }

    public void setContaC(String contaC) {
        this.contaC = contaC;
    }

    public String getContaCDv() {
        return contaCDv;
    }

    public void setContaCDv(String contaCDv) {
        this.contaCDv = contaCDv;
    }

    public String getConta_Ctb() {
        return conta_Ctb;
    }

    public void setConta_Ctb(String conta_Ctb) {
        this.conta_Ctb = conta_Ctb;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDtAlter() {
        return dtAlter;
    }

    public void setDtAlter(String dtAlter) {
        this.dtAlter = dtAlter;
    }

    public String getHrAlter() {
        return hrAlter;
    }

    public void setHrAlter(String hrAlter) {
        this.hrAlter = hrAlter;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PstServ {

    private String Secao;
    private BigDecimal CodFil;
    private Integer Regional;
    private String Local;
    private String Situacao;
    private LocalDate Dt_Situacao;
    private String Contrato;
    private String TipoPosto;
    private String TipoPostoDesc;
    private String CodCli;
    private BigDecimal SupervDiu;
    private BigDecimal SupervNot;
    private BigDecimal Distancia;
    private String Posto;
    private BigDecimal OS;
    private BigDecimal HSPosto;
    private BigDecimal HsFuncion;
    private String Reforco;
    private String Ronda;
    private String Pericul;
    private String Insalub;
    private Integer Regiao;
    private Integer GRT;
    private String GrpGerencial;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private String InterfExt;
    private String DescContrato;
    private String HrPadraoIni;
    private String HrPadraoFim;

    private String cliente;
    private String endereco;
    private String latitude;
    private String longitude;
    private BigDecimal dist;
    private String codGrupo;
    private String descricao;

    public PstServ() {
        this.Secao = "";
        this.CodFil = new BigDecimal("0");
        this.Regional = 0;
        this.Local = "";
        this.Situacao = "";
        this.Dt_Situacao = null;
        this.Contrato = "";
        this.TipoPosto = "";
        this.TipoPostoDesc = "";
        this.CodCli = "";
        this.SupervDiu = new BigDecimal("0");
        this.SupervNot = new BigDecimal("0");
        this.Distancia = new BigDecimal("0");
        this.Posto = "";
        this.OS = new BigDecimal("0");
        this.HSPosto = new BigDecimal("0");
        this.HsFuncion = new BigDecimal("0");
        this.Reforco = "";
        this.Ronda = "";
        this.Pericul = "";
        this.Insalub = "";
        this.Regiao = 0;
        this.GRT = 0;
        this.GrpGerencial = "";
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
        this.InterfExt = "";
        this.DescContrato = "";
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        try {
            this.CodFil = CodFil;
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public int getRegional() {
        return Regional;
    }

    public void setRegional(Integer Regional) {
        if (Regional == null) {
            Regional = 0;
        } else {
            this.Regional = Regional;
        }
    }

    public String getLocal() {
        return Local;
    }

    public void setLocal(String Local) {
        this.Local = Local;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public LocalDate getDt_Situacao() {
        return Dt_Situacao;
    }

    public void setDt_Situacao(LocalDate Dt_Situacao) {
        this.Dt_Situacao = Dt_Situacao;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getTipoPosto() {
        return TipoPosto;
    }

    public void setTipoPosto(String TipoPosto) {
        this.TipoPosto = TipoPosto;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public BigDecimal getSupervDiu() {
        return SupervDiu;
    }

    public void setSupervDiu(String SupervDiu) {
        try {
            this.SupervDiu = new BigDecimal(SupervDiu);
        } catch (Exception e) {
            this.SupervDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getSupervNot() {
        return SupervNot;
    }

    public void setSupervNot(String SupervNot) {
        try {
            this.SupervNot = new BigDecimal(SupervNot);
        } catch (Exception e) {
            this.SupervNot = new BigDecimal("0");
        }
    }

    public BigDecimal getDistancia() {
        return Distancia;
    }

    public void setDistancia(String Distancia) {
        try {
            this.Distancia = new BigDecimal(Distancia);
        } catch (Exception e) {
            this.Distancia = new BigDecimal("0");
        }
    }

    public String getTipoPostoDesc() {
        return TipoPostoDesc;
    }

    public void setTipoPostoDesc(String TipoPostoDesc) {
        this.TipoPostoDesc = TipoPostoDesc;
    }

    public String getPosto() {
        return Posto;
    }

    public void setPosto(String Posto) {
        this.Posto = Posto;
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    public BigDecimal getHSPosto() {
        return HSPosto;
    }

    public void setHSPosto(String HSPosto) {
        try {
            this.HSPosto = new BigDecimal(HSPosto);
        } catch (Exception e) {
            this.HSPosto = new BigDecimal("0");
        }
    }

    public BigDecimal getHsFuncion() {
        return HsFuncion;
    }

    public void setHsFuncion(String HsFuncion) {
        try {
            this.HsFuncion = new BigDecimal(HsFuncion);
        } catch (Exception e) {
            this.HsFuncion = new BigDecimal("0");
        }
    }

    public String getReforco() {
        return Reforco;
    }

    public void setReforco(String Reforco) {
        this.Reforco = Reforco;
    }

    public String getRonda() {
        return Ronda;
    }

    public void setRonda(String Ronda) {
        this.Ronda = Ronda;
    }

    public String getPericul() {
        return Pericul;
    }

    public void setPericul(String Pericul) {
        this.Pericul = Pericul;
    }

    public String getInsalub() {
        return Insalub;
    }

    public void setInsalub(String Insalub) {
        this.Insalub = Insalub;
    }

    public int getRegiao() {
        return Regiao;
    }

    public void setRegiao(Integer Regiao) {
        if (Regiao == null) {
            Regiao = 0;
        } else {
            this.Regiao = Regiao;
        }
    }

    public int getGRT() {
        return GRT;
    }

    public void setGRT(Integer GRT) {
        if (GRT == null) {
            GRT = 0;
        } else {
            this.GRT = GRT;
        }
    }

    public String getGrpGerencial() {
        return GrpGerencial;
    }

    public void setGrpGerencial(String GrpGerencial) {
        this.GrpGerencial = GrpGerencial;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getInterfExt() {
        return InterfExt;
    }

    public void setInterfExt(String InterfExt) {
        this.InterfExt = InterfExt;
    }

    public String getDescContrato() {
        return DescContrato;
    }

    public void setDescContrato(String DescContrato) {
        this.DescContrato = DescContrato;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getHrPadraoIni() {
        return HrPadraoIni;
    }

    public void setHrPadraoIni(String HrPadraoIni) {
        this.HrPadraoIni = HrPadraoIni;
    }

    public String getHrPadraoFim() {
        return HrPadraoFim;
    }

    public void setHrPadraoFim(String HrPadraoFim) {
        this.HrPadraoFim = HrPadraoFim;
    }

    public BigDecimal getDist() {
        return dist;
    }

    public void setDist(BigDecimal dist) {
        this.dist = dist;
    }

    public String getCodGrupo() {
        return codGrupo;
    }

    public void setCodGrupo(String codGrupo) {
        this.codGrupo = codGrupo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

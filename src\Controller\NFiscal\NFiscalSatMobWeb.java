package Controller.NFiscal;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.NFiscal;
import SasBeans.SasPWFill;
import SasDaos.ClientesDao;
import SasDaos.NFiscalDao;
import SasDaos.SasPwFilDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class NFiscalSatMobWeb {

    public List<Clientes> buscarClientes(BigDecimal CodPessoa, String query, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.QueryCliente(CodPessoa, query, persistencia);
        } catch (Exception e) {
            throw new Exception("NFiscalSatMobWeb.buscarClientes<message>" + e.getMessage());
        }
    }

    public SasPWFill buscaFilial(String CodFil, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(CodFil, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("NFiscalSatMobWeb.buscaFilial<message>" + e.getMessage());
        }
    }

    public List<NFiscal> listaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            NFiscalDao nFiscalDao = new NFiscalDao();
            return nFiscalDao.listaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("nfiscal.falhageral<message>" + e.getMessage());
        }
    }

    public Integer total(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            NFiscalDao nFiscalDao = new NFiscalDao();
            return nFiscalDao.totalNFiscalMobWeb(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("nfiscal.falhageral<message>" + e.getMessage());
        }
    }
}

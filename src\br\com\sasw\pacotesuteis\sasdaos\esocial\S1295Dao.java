/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S1295;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S1295Dao {

    public List<S1295> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S1295> retorno = new ArrayList<>();
            String sql = " Select  "
                    + " '1' ideEvento_indApuracao,  "
                    //+ " Case When FPMensal.TipoFP = '132' then 2 else 1 end ideEvento_indApuracao, "
                    //+ " Case When FPMensal.TipoFP = '132' then '20'+Substring(Convert(Varchar,FPMensal.CodMovFP),1,2) "
                    //+ " else '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) end ideEvento_perApur,  "
                    + " '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) ideEvento_perApur,  "
                    + " Filiais.TipoPessoa ideEmpregador_tpInsc,  "
                    + " Filiais.CNPJ ideEmpregador_nrInsc,    "
                    + " Pessoa.Nome ideRespInf_nmResp,  "
                    + " Pessoa.CPF ideRespInf_cpfResp,  "
                    + " Pessoa.Fone1 ideRespInf_telefone,  "
                    + " DIRF.emailRInf ideRespInf_email,  "
                    + " (select max(sucesso) from  (   "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso   "
                    + "         From XmleSocial z    "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1295' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%'   "
                    + "                 or z.Xml_Retorno = ''  "
                    + "                 or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))   "
                    + " union   "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso   "
                    + "         From XmleSocial z    "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1295' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ocorrencia>%'   "
                    + "                 or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union   "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso   "
                    + "         From XmleSocial z    "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'S-1295' "
                    + "             and z.CodFil = ? "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso "
                    + " From FPPeriodos "
                    + " Left join FPMensal  on FPPeriodos.CodMovFP = FPMEnsal.CodMovFP "
                    + " Left join Filiais  on Filiais.CodFil = FPMensal.CodFil  "
                    + " Left join Dirf  on Dirf.CodFil = FPMensal.CodFil  "
                    + " Left join Pessoa on Pessoa.Codigo = DIRF.CodPessoaRIn  ";
            if (compet.contains("-13")) {
                sql = sql + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','')  "
                        + "    and FPMensal.tipoFP = '132' ";
            } else {
                sql = sql + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','')  "
                        + "    and FPMensal.tipoFP = 'MEN' ";
            }
            sql = sql + "     and FPMensal.CodFil = ?  "
                    + " Group by FPPeriodos.CodMovFP, Filiais.TipoPessoa,Filiais.CNPJ,Pessoa.Nome,Pessoa.CPF,Pessoa.Fone1,DIRF.emailRInf ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(codFil);
            consulta.select();
            S1295 s1295;
            while (consulta.Proximo()) {
                s1295 = new S1295();
                s1295.setSucesso(consulta.getInt("sucesso"));

                s1295.setIdeEvento_procEmi("1");
                s1295.setIdeEvento_verProc("Satellite eSocial");

                if (compet.contains("-13")) {
                    s1295.setIdeEvento_indApuracao("2");
                    s1295.setIdeEvento_perApur(consulta.getString("ideEvento_perApur").substring(0, 4));
                } else {
                    s1295.setIdeEvento_indApuracao(consulta.getString("ideEvento_indApuracao"));
                    s1295.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));
                }

                s1295.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s1295.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));

                s1295.setIdeRespInf_nmResp(consulta.getString("ideRespInf_nmResp"));
                s1295.setIdeRespInf_cpfResp(consulta.getString("ideRespInf_cpfResp"));
                s1295.setIdeRespInf_telefone(consulta.getString("ideRespInf_telefone"));
                s1295.setIdeRespInf_email(consulta.getString("ideRespInf_email"));

                retorno.add(s1295);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S1295Dao.get - " + e.getMessage());
        }
    }
}

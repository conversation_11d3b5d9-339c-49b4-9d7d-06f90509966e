﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTabLotacao/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTabLotacao/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTabLotacao">
                    <xs:annotation>
                        <xs:documentation>Evento Tabela de Lotacoes</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoLotacao">
                                <xs:annotation>
                                    <xs:documentation>Informacoes da Lotacao</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideLotacao" type="TIdeLotacao">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao da Lotacao</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosLotacao" type="TDadosLotacao">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes da Lotacao</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideLotacao" type="TIdeLotacao">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao da lotacao</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosLotacao" type="TDadosLotacao">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes da lotacao</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideLotacao" type="TIdeLotacao">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao da lotacao que sera excluida</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeLotacao">
        <xs:annotation>
            <xs:documentation>Identificacao da Lotacao e periodo de validade</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codLotacao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Lotacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosLotacao">
        <xs:annotation>
            <xs:documentation>Informacoes da Lotacao</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpLotacao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Lotacao, conforme tabela 10</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpInsc" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fpasLotacao">
                <xs:annotation>
                    <xs:documentation>Informacoes de FPAS e Terceiros relativas à lotacao tributaria</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="fpas">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do FPAS</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codTercs">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo de Terceiros</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="4"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codTercsSusp" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo de Terceiros Suspenso</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="4"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="infoProcJudTerceiros" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Informacoes de processos judiciais relativos às contribuicoes destinadas a outras Entidades</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="procJudTerceiro" maxOccurs="99">
                                        <xs:annotation>
                                            <xs:documentation>Identificacao do Processo Judicial</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="codTerc">
                                                    <xs:simpleType>
                                                        <xs:annotation>
                                                            <xs:documentation>COdigo de Terceiro</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:restriction base="xs:string">
                                                            <xs:length value="4"/>
                                                        </xs:restriction>
                                                    </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="nrProcJud">
                                                    <xs:simpleType>
                                                        <xs:annotation>
                                                            <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:restriction base="xs:string">
                                                            <xs:length value="20"/>
                                                        </xs:restriction>
                                                    </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="codSusp">
                                                    <xs:simpleType>
                                                        <xs:annotation>
                                                            <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:restriction base="xs:integer">
                                                            <xs:pattern value="\d{1,14}"/>
                                                        </xs:restriction>
                                                    </xs:simpleType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoEmprParcial" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacao complementar de obra de construcao civil</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="tpInscContrat">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Tipo de Inscricao do contratante</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrInscContrat">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número de Inscricao do Contratante</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="\d{8,14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="tpInscProp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Tipo de Inscricao do proprietario do CNO</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrInscProp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número de Inscricao do proprietario do CNO</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="\d{8,14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.MobileHW;

/**
 *
 * <AUTHOR>
 */
public class ClientesMobileHW {

    private Clientes cliente;
    private MobileHW mobileHW;

    public ClientesMobileHW() {
        this.cliente = new Clientes();
        this.mobileHW = new MobileHW();
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public MobileHW getMobileHW() {
        return mobileHW;
    }

    public void setMobileHW(MobileHW mobileHW) {
        this.mobileHW = mobileHW;
    }

}

package SasBeans;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class GuiasList {

    private String Guia;
    private String Serie;
    private String Valor;
    private String OS;
    private String Cliori;
    private String nRedOri;
    private String nomeOri;
    private String endeOri;
    private String estadoOri;
    private String bairroOri;
    private String cidadeOri;
    private String cepOri;
    private String Situacao;
    private boolean RtGuias;
    private boolean CxfGuias;
    private boolean TesSaidas;
    private List<CxFGuiasVol> Volumes;
    private BigDecimal SequenciaOri;
    private Integer ParadaOri;
    private String moeda;

    public GuiasList() {
        this.Guia = "";
        this.Serie = "";
        this.Valor = "";
        this.OS = "";
        this.Cliori = "";
        this.Situacao = "";
        this.RtGuias = false;
        this.CxfGuias = false;
        this.TesSaidas = false;
        this.Volumes = new ArrayList<>();
        this.SequenciaOri = new BigDecimal("0");
        this.ParadaOri = 0;
    }

    public void Guia() {
        this.RtGuias = false;
        this.CxfGuias = false;
        this.TesSaidas = false;
    }

    public void setGuia(String value) {
        Guia = value;
    }

    public void setSerie(String value) {
        Serie = value;
    }

    public void setValor(String value) {
        Valor = value;
    }

    public String getGuia() {
        return Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public String getValor() {
        return Valor;
    }

    public String getEstadoOri() {
        return estadoOri;
    }

    public void setEstadoOri(String estadoOri) {
        this.estadoOri = estadoOri;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public boolean isRtGuias() {
        return RtGuias;
    }

    public void setRtGuias(boolean RtGuias) {
        this.RtGuias = RtGuias;
    }

    public String getCliori() {
        return Cliori;
    }

    public void setCliori(String Cliori) {
        this.Cliori = Cliori;
    }

    public boolean isCxfGuias() {
        return CxfGuias;
    }

    public void setCxfGuias(boolean CxfGuias) {
        this.CxfGuias = CxfGuias;
    }

    public boolean isTesSaidas() {
        return TesSaidas;
    }

    public void setTesSaidas(boolean TesSaidas) {
        this.TesSaidas = TesSaidas;
    }

    public List<CxFGuiasVol> getVolumes() {
        return Volumes;
    }

    public void setVolumes(List<CxFGuiasVol> Volumes) {
        this.Volumes = Volumes;
    }

    public BigDecimal getSequenciaOri() {
        return SequenciaOri;
    }

    public void setSequenciaOri(BigDecimal SequenciaOri) {
        this.SequenciaOri = SequenciaOri;
    }

    public Integer getParadaOri() {
        return ParadaOri;
    }

    public void setParadaOri(Integer ParadaOri) {
        this.ParadaOri = ParadaOri;
    }

    public String getnRedOri() {
        return nRedOri;
    }

    public void setnRedOri(String nRedOri) {
        this.nRedOri = nRedOri;
    }

    public String getNomeOri() {
        return nomeOri;
    }

    public void setNomeOri(String nomeOri) {
        this.nomeOri = nomeOri;
    }

    public String getEndeOri() {
        return endeOri;
    }

    public void setEndeOri(String endeOri) {
        this.endeOri = endeOri;
    }

    public String getBairroOri() {
        return bairroOri;
    }

    public void setBairroOri(String bairroOri) {
        this.bairroOri = bairroOri;
    }

    public String getCidadeOri() {
        return cidadeOri;
    }

    public void setCidadeOri(String cidadeOri) {
        this.cidadeOri = cidadeOri;
    }

    public String getCepOri() {
        return cepOri;
    }

    public void setCepOri(String cepOri) {
        this.cepOri = cepOri;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 31 * hash + Objects.hashCode(this.Guia);
        hash = 31 * hash + Objects.hashCode(this.Serie);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final GuiasList other = (GuiasList) obj;
        if (!Objects.equals(this.Guia, other.Guia)) {
            return false;
        }
        if (!Objects.equals(this.Serie, other.Serie)) {
            return false;
        }
        return true;
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TesWEBRelat;
import java.util.ArrayList;
import java.util.List;

public class TesWEBRelatDao {
    public List<TesWEBRelat> listarRelatorios(String CodCli, float CodFil, Persistencia persistencia) throws Exception {
        try {
            List<TesWEBRelat> retorno = new ArrayList<>();
            String sql = " SELECT teswebrelat.Sequencia, teswebrelat.CodFIl, teswebrelat.CodTes, teswebrelat.CodCli, \n"
                    + " teswebrelat.Data, teswebrelat.Descricao, teswebrelat.URL, teswebrelat.Operador, \n"
                    + " teswebrelat.Dt_Alter, teswebrelat.Hr_Alter \n"
                    + " FROM teswebrelat \n"
                    + " WHERE teswebrelat.CodCli = ? AND CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodCli);
            consulta.setFloat(CodFil);
            consulta.select();
            TesWEBRelat novoRelatorio;
            while (consulta.Proximo()) {
                novoRelatorio = new TesWEBRelat();
                novoRelatorio.setSequencia(consulta.getBigDecimal("Sequencia"));
                novoRelatorio.setCodFil(consulta.getFloat("CodFil"));
                novoRelatorio.setCodTes(consulta.getString("CodTes"));
                novoRelatorio.setCodCli(consulta.getString("CodCli"));
                novoRelatorio.setData(consulta.getDate("Data"));
                novoRelatorio.setDescricao(consulta.getString("Descricao"));
                novoRelatorio.setURL(consulta.getString("URL"));
                novoRelatorio.setOperador(consulta.getString("operador"));
                novoRelatorio.setDt_Alter(consulta.getDate("Dt_Alter"));
                novoRelatorio.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(novoRelatorio);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarUsuarios - " + e.getMessage() + "\r\n"
                    + " SELECT teswebrelat.Sequencia, teswebrelat.CodFIl, teswebrelat.CodTes, teswebrelat.CodCli, \n"
                    + " teswebrelat.Data, teswebrelat.Descricao, teswebrelat.URL, teswebrelat.Operador, \n"
                    + " teswebrelat.Dt_Alter, teswebrelat.Hr_Alter \n"
                    + " FROM teswebrelat \n"
                    + " WHERE PessoaCliAut.CodCli = " + CodCli + " AND CodFil = " + Float.toString(CodFil));
        }
    }
    
}

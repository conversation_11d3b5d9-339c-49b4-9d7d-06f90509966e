/*
 * To change this license header,\n choose License Headers in Project Properties.
 * To change this template file,\n choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class CataMov {

    private String ID;
    private String IDEquip;
    private String Data;
    private String Hora;
    private String Operacao;
    private String Fluxo;
    private String Valor;
    private String Dt_incl;
    private String Hr_incl;
    private String Lido;
    private String DtConferencia;

    private String Cadastro;

    public String getDtConferencia() {
        return DtConferencia;
    }

    public void setDtConferencia(String DtConferencia) {
        this.DtConferencia = DtConferencia;
    }

    public String getID() {
        return ID;
    }

    public void setID(String ID) {
        this.ID = ID;
    }

    public String getIDEquip() {
        return IDEquip;
    }

    public void setIDEquip(String IDEquip) {
        this.IDEquip = IDEquip;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getOperacao() {
        return Operacao;
    }

    public void setOperacao(String Operacao) {
        this.Operacao = Operacao;
    }

    public String getFluxo() {
        return Fluxo;
    }

    public void setFluxo(String Fluxo) {
        this.Fluxo = Fluxo;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getDt_incl() {
        return Dt_incl;
    }

    public void setDt_incl(String Dt_incl) {
        this.Dt_incl = Dt_incl;
    }

    public String getHr_incl() {
        return Hr_incl;
    }

    public void setHr_incl(String Hr_incl) {
        this.Hr_incl = Hr_incl;
    }

    public String getLido() {
        return Lido;
    }

    public void setLido(String Lido) {
        this.Lido = Lido;
    }

    public String getCadastro() {
        return Cadastro;
    }

    public void setCadastro(String Cadastro) {
        this.Cadastro = Cadastro;
    }

    @Override
    public String toString() {
        return "CataMov{\n" + "ID=" + ID + ",\n IDEquip=" + IDEquip + ",\n Data=" + Data + ",\n Hora=" + Hora + ",\n Operacao=" + Operacao + ",\n Fluxo=" + Fluxo + ",\n Valor=" + Valor + ",\n Dt_incl=" + Dt_incl + ",\n Hr_incl=" + Hr_incl + ",\n Lido=" + Lido + "\n}";
    }
}

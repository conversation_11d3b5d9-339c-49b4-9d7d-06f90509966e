package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FPMensal {

    private BigDecimal CodFil;
    private BigDecimal CodMovFP;
    private String TipoFP;
    private String tipoFpFormatado;
    private BigDecimal Matr;
    private String Secao;
    private String CCusto;
    private int Regional;
    private String Cargo;
    private BigDecimal CodCargo;
    private String Situacao;
    private LocalDate Dt_Situac;
    private String Escala;
    private int Horario;
    private String Sindicato;
    private int DepSF;
    private int DepIR;
    private BigDecimal Salario;
    private BigDecimal BaseINSS;
    private BigDecimal BaseINSS13;
    private BigDecimal BaseINSSEmpSal;
    private BigDecimal BaseINSSEmpPro;
    private BigDecimal BaseINSSEmpSal13;
    private BigDecimal BaseINSSEmpPro13;
    private BigDecimal INSS;
    private BigDecimal INSS13;
    private BigDecimal INSSFerias;
    private BigDecimal BaseRT;
    private BigDecimal BaseRI;
    private BigDecimal BaseRIResc;
    private BigDecimal BaseIR;
    private BigDecimal IRDedDep;
    private BigDecimal IRRF;
    private BigDecimal IRRFFerias;
    private BigDecimal BaseIR13;
    private BigDecimal IRRF13;
    private BigDecimal BaseFGTS;
    private BigDecimal BaseFGTS13;
    private BigDecimal FGTS;
    private BigDecimal FGTS13;
    private BigDecimal MediaFerias;
    private BigDecimal Media13;
    private BigDecimal Proventos;
    private BigDecimal Descontos;
    private BigDecimal Liquido;
    private BigDecimal Pensao;
    private BigDecimal AjCusto;
    private BigDecimal PLR;
    private BigDecimal Abono;
    private String RIDesc;
    private BigDecimal HE;
    private BigDecimal AvPrevioInd;
    private BigDecimal FeriasInd;
    private BigDecimal LoteCalc;
    private int MesCaixa;
    private BigDecimal INSSCaixa;
    private LocalDate DtPagto;
    private String SitCalc;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    
    private String competenciaDescr;
    private String caminhoAssinatura;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getCodMovFP() {
        return CodMovFP;
    }

    public void setCodMovFP(String CodMovFP) {
        try {
            this.CodMovFP = new BigDecimal(CodMovFP);
        } catch (Exception e) {
            this.CodMovFP = new BigDecimal("0");
        }
    }

    public String getTipoFP() {
        return TipoFP;
    }

    public void setTipoFP(String TipoFP) {
        this.TipoFP = TipoFP;
    }

    public BigDecimal getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public String getSecao() {
        return Secao;
    }

    public void setSecao(String Secao) {
        this.Secao = Secao;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public int getRegional() {
        return Regional;
    }

    public void setRegional(int Regional) {
        this.Regional = Regional;
    }

    public String getCargo() {
        return Cargo;
    }

    public void setCargo(String Cargo) {
        this.Cargo = Cargo;
    }

    public BigDecimal getCodCargo() {
        return CodCargo;
    }

    public void setCodCargo(String CodCargo) {
        try {
            this.CodCargo = new BigDecimal(CodCargo);
        } catch (Exception e) {
            this.CodCargo = new BigDecimal("0");
        }
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public LocalDate getDt_Situac() {
        return Dt_Situac;
    }

    public void setDt_Situac(LocalDate Dt_Situac) {
        this.Dt_Situac = Dt_Situac;
    }

    public String getEscala() {
        return Escala;
    }

    public void setEscala(String Escala) {
        this.Escala = Escala;
    }

    public int getHorario() {
        return Horario;
    }

    public void setHorario(int Horario) {
        this.Horario = Horario;
    }

    public String getSindicato() {
        return Sindicato;
    }

    public void setSindicato(String Sindicato) {
        this.Sindicato = Sindicato;
    }

    public int getDepSF() {
        return DepSF;
    }

    public void setDepSF(int DepSF) {
        this.DepSF = DepSF;
    }

    public int getDepIR() {
        return DepIR;
    }

    public void setDepIR(int DepIR) {
        this.DepIR = DepIR;
    }

    public BigDecimal getSalario() {
        return Salario;
    }

    public void setSalario(String Salario) {
        try {
            this.Salario = new BigDecimal(Salario);
        } catch (Exception e) {
            this.Salario = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSS() {
        return BaseINSS;
    }

    public void setBaseINSS(String BaseINSS) {
        try {
            this.BaseINSS = new BigDecimal(BaseINSS);
        } catch (Exception e) {
            this.BaseINSS = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSS13() {
        return BaseINSS13;
    }

    public void setBaseINSS13(String BaseINSS13) {
        try {
            this.BaseINSS13 = new BigDecimal(BaseINSS13);
        } catch (Exception e) {
            this.BaseINSS13 = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSSEmpSal() {
        return BaseINSSEmpSal;
    }

    public void setBaseINSSEmpSal(String BaseINSSEmpSal) {
        try {
            this.BaseINSSEmpSal = new BigDecimal(BaseINSSEmpSal);
        } catch (Exception e) {
            this.BaseINSSEmpSal = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSSEmpPro() {
        return BaseINSSEmpPro;
    }

    public void setBaseINSSEmpPro(String BaseINSSEmpPro) {
        try {
            this.BaseINSSEmpPro = new BigDecimal(BaseINSSEmpPro);
        } catch (Exception e) {
            this.BaseINSSEmpPro = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSSEmpSal13() {
        return BaseINSSEmpSal13;
    }

    public void setBaseINSSEmpSal13(String BaseINSSEmpSal13) {
        try {
            this.BaseINSSEmpSal13 = new BigDecimal(BaseINSSEmpSal13);
        } catch (Exception e) {
            this.BaseINSSEmpSal13 = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseINSSEmpPro13() {
        return BaseINSSEmpPro13;
    }

    public void setBaseINSSEmpPro13(String BaseINSSEmpPro13) {
        try {
            this.BaseINSSEmpPro13 = new BigDecimal(BaseINSSEmpPro13);
        } catch (Exception e) {
            this.BaseINSSEmpPro13 = new BigDecimal("0");
        }
    }

    public BigDecimal getINSS() {
        return INSS;
    }

    public void setINSS(String INSS) {
        try {
            this.INSS = new BigDecimal(INSS);
        } catch (Exception e) {
            this.INSS = new BigDecimal("0");
        }
    }

    public BigDecimal getINSS13() {
        return INSS13;
    }

    public void setINSS13(String INSS13) {
        try {
            this.INSS13 = new BigDecimal(INSS13);
        } catch (Exception e) {
            this.INSS13 = new BigDecimal("0");
        }
    }

    public BigDecimal getINSSFerias() {
        return INSSFerias;
    }

    public void setINSSFerias(String INSSFerias) {
        try {
            this.INSSFerias = new BigDecimal(INSSFerias);
        } catch (Exception e) {
            this.INSSFerias = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseRT() {
        return BaseRT;
    }

    public void setBaseRT(String BaseRT) {
        try {
            this.BaseRT = new BigDecimal(BaseRT);
        } catch (Exception e) {
            this.BaseRT = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseRI() {
        return BaseRI;
    }

    public void setBaseRI(String BaseRI) {
        try {
            this.BaseRI = new BigDecimal(BaseRI);
        } catch (Exception e) {
            this.BaseRI = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseRIResc() {
        return BaseRIResc;
    }

    public void setBaseRIResc(String BaseRIResc) {
        try {
            this.BaseRIResc = new BigDecimal(BaseRIResc);
        } catch (Exception e) {
            this.BaseRIResc = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseIR() {
        return BaseIR;
    }

    public void setBaseIR(String BaseIR) {
        try {
            this.BaseIR = new BigDecimal(BaseIR);
        } catch (Exception e) {
            this.BaseIR = new BigDecimal("0");
        }
    }

    public BigDecimal getIRDedDep() {
        return IRDedDep;
    }

    public void setIRDedDep(String IRDedDep) {
        try {
            this.IRDedDep = new BigDecimal(IRDedDep);
        } catch (Exception e) {
            this.IRDedDep = new BigDecimal("0");
        }
    }

    public BigDecimal getIRRF() {
        return IRRF;
    }

    public void setIRRF(String IRRF) {
        try {
            this.IRRF = new BigDecimal(IRRF);
        } catch (Exception e) {
            this.IRRF = new BigDecimal("0");
        }
    }

    public BigDecimal getIRRFFerias() {
        return IRRFFerias;
    }

    public void setIRRFFerias(String IRRFFerias) {
        try {
            this.IRRFFerias = new BigDecimal(IRRFFerias);
        } catch (Exception e) {
            this.IRRFFerias = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseIR13() {
        return BaseIR13;
    }

    public void setBaseIR13(String BaseIR13) {
        try {
            this.BaseIR13 = new BigDecimal(BaseIR13);
        } catch (Exception e) {
            this.BaseIR13 = new BigDecimal("0");
        }
    }

    public BigDecimal getIRRF13() {
        return IRRF13;
    }

    public void setIRRF13(String IRRF13) {
        try {
            this.IRRF13 = new BigDecimal(IRRF13);
        } catch (Exception e) {
            this.IRRF13 = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseFGTS() {
        return BaseFGTS;
    }

    public void setBaseFGTS(String BaseFGTS) {
        try {
            this.BaseFGTS = new BigDecimal(BaseFGTS);
        } catch (Exception e) {
            this.BaseFGTS = new BigDecimal("0");
        }
    }

    public BigDecimal getBaseFGTS13() {
        return BaseFGTS13;
    }

    public void setBaseFGTS13(String BaseFGTS13) {
        try {
            this.BaseFGTS13 = new BigDecimal(BaseFGTS13);
        } catch (Exception e) {
            this.BaseFGTS13 = new BigDecimal("0");
        }
    }

    public BigDecimal getFGTS() {
        return FGTS;
    }

    public void setFGTS(String FGTS) {
        try {
            this.FGTS = new BigDecimal(FGTS);
        } catch (Exception e) {
            this.FGTS = new BigDecimal("0");
        }
    }

    public BigDecimal getFGTS13() {
        return FGTS13;
    }

    public void setFGTS13(String FGTS13) {
        try {
            this.FGTS13 = new BigDecimal(FGTS13);
        } catch (Exception e) {
            this.FGTS13 = new BigDecimal("0");
        }
    }

    public BigDecimal getMediaFerias() {
        return MediaFerias;
    }

    public void setMediaFerias(String MediaFerias) {
        try {
            this.MediaFerias = new BigDecimal(MediaFerias);
        } catch (Exception e) {
            this.MediaFerias = new BigDecimal("0");
        }
    }

    public BigDecimal getMedia13() {
        return Media13;
    }

    public void setMedia13(String Media13) {
        try {
            this.Media13 = new BigDecimal(Media13);
        } catch (Exception e) {
            this.Media13 = new BigDecimal("0");
        }
    }

    public BigDecimal getProventos() {
        return Proventos;
    }

    public void setProventos(String Proventos) {
        try {
            this.Proventos = new BigDecimal(Proventos);
        } catch (Exception e) {
            this.Proventos = new BigDecimal("0");
        }
    }

    public BigDecimal getDescontos() {
        return Descontos;
    }

    public void setDescontos(String Descontos) {
        try {
            this.Descontos = new BigDecimal(Descontos);
        } catch (Exception e) {
            this.Descontos = new BigDecimal("0");
        }
    }

    public BigDecimal getLiquido() {
        return Liquido;
    }

    public void setLiquido(String Liquido) {
        try {
            this.Liquido = new BigDecimal(Liquido);
        } catch (Exception e) {
            this.Liquido = new BigDecimal("0");
        }
    }

    public BigDecimal getPensao() {
        return Pensao;
    }

    public void setPensao(String Pensao) {
        try {
            this.Pensao = new BigDecimal(Pensao);
        } catch (Exception e) {
            this.Pensao = new BigDecimal("0");
        }
    }

    public BigDecimal getAjCusto() {
        return AjCusto;
    }

    public void setAjCusto(String AjCusto) {
        try {
            this.AjCusto = new BigDecimal(AjCusto);
        } catch (Exception e) {
            this.AjCusto = new BigDecimal("0");
        }
    }

    public BigDecimal getPLR() {
        return PLR;
    }

    public void setPLR(String PLR) {
        try {
            this.PLR = new BigDecimal(PLR);
        } catch (Exception e) {
            this.PLR = new BigDecimal("0");
        }
    }

    public BigDecimal getAbono() {
        return Abono;
    }

    public void setAbono(String Abono) {
        try {
            this.Abono = new BigDecimal(Abono);
        } catch (Exception e) {
            this.Abono = new BigDecimal("0");
        }
    }

    public String getRIDesc() {
        return RIDesc;
    }

    public void setRIDesc(String RIDesc) {
        this.RIDesc = RIDesc;
    }

    public BigDecimal getHE() {
        return HE;
    }

    public void setHE(String HE) {
        try {
            this.HE = new BigDecimal(HE);
        } catch (Exception e) {
            this.HE = new BigDecimal("0");
        }
    }

    public BigDecimal getAvPrevioInd() {
        return AvPrevioInd;
    }

    public void setAvPrevioInd(String AvPrevioInd) {
        try {
            this.AvPrevioInd = new BigDecimal(AvPrevioInd);
        } catch (Exception e) {
            this.AvPrevioInd = new BigDecimal("0");
        }
    }

    public BigDecimal getFeriasInd() {
        return FeriasInd;
    }

    public void setFeriasInd(String FeriasInd) {
        try {
            this.FeriasInd = new BigDecimal(FeriasInd);
        } catch (Exception e) {
            this.FeriasInd = new BigDecimal("0");
        }
    }

    public BigDecimal getLoteCalc() {
        return LoteCalc;
    }

    public void setLoteCalc(String LoteCalc) {
        try {
            this.LoteCalc = new BigDecimal(LoteCalc);
        } catch (Exception e) {
            this.LoteCalc = new BigDecimal("0");
        }
    }

    public int getMesCaixa() {
        return MesCaixa;
    }

    public void setMesCaixa(int MesCaixa) {
        this.MesCaixa = MesCaixa;
    }

    public BigDecimal getINSSCaixa() {
        return INSSCaixa;
    }

    public void setINSSCaixa(String INSSCaixa) {
        try {
            this.INSSCaixa = new BigDecimal(INSSCaixa);
        } catch (Exception e) {
            this.INSSCaixa = new BigDecimal("0");
        }
    }

    public LocalDate getDtPagto() {
        return DtPagto;
    }

    public void setDtPagto(LocalDate DtPagto) {
        this.DtPagto = DtPagto;
    }

    public String getSitCalc() {
        return SitCalc;
    }

    public void setSitCalc(String SitCalc) {
        this.SitCalc = SitCalc;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the tipoFpFormatado
     */
    public String getTipoFpFormatado() {
        return tipoFpFormatado;
    }

    /**
     * @param tipoFpFormatado the tipoFpFormatado to set
     */
    public void setTipoFpFormatado(String tipoFpFormatado) {
        this.tipoFpFormatado = tipoFpFormatado;
    }

    public String getCompetenciaDescr() {
        return competenciaDescr;
    }

    public void setCompetenciaDescr(String competenciaDescr) {
        this.competenciaDescr = competenciaDescr;
    }

    public String getCaminhoAssinatura() {
        return caminhoAssinatura;
    }

    public void setCaminhoAssinatura(String caminhoAssinatura) {
        this.caminhoAssinatura = caminhoAssinatura;
    }
}

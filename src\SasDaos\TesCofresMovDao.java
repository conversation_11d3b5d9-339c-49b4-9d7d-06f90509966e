/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.OLD.Persistencia_OLD;
import Dados.Persistencia;
import SasBeans.TesCofresMov;
import SasBeansCompostas.MovimentacaoGeralFuncionario;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TesCofresMovDao {

    public TesCofresMov ultimaMovimentacaoPorCofre(String CodCofre, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT MAX(CodigoBarras) CodigoBarras "
                    + " FROM TesCofresMov "
                    + " WHERE CodCofre = ? and Dt_Incl >= ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodCofre);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.select();
            TesCofresMov tescofremov = new TesCofresMov();
            while (consulta.Proximo()) {
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
            }
            consulta.Close();
            return tescofremov;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes da última movimentação de cofre - " + e.getMessage());
        }
    }

    public TesCofresMov ultimaMovimentacao(BigDecimal id, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select * From TesCofresMov"
                    + " Where id = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(id);
            consulta.select();
            TesCofresMov tescofremov = new TesCofresMov();
            while (consulta.Proximo()) {
                tescofremov.setCodigoBarras(consulta.getString("codigobarras"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
            }
            consulta.Close();
            return tescofremov;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes da última movimentação de cofre - " + e.getMessage());
        }
    }

    /**
     * Verifica antes de inserir
     *
     * @param movimentacao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int inserirMovimentacao(TesCofresMov movimentacao, Persistencia persistencia) throws Exception {
        StringBuilder str = new StringBuilder();

        try {
            str.append("INSERT INTO TesCofresMov (id, codcofre, data, hora, codcliente, idusuario, nomeusuario,\n"
                    + " valordeposito, tipomoeda, TipoDeposito, codigobarras, status, operador, dt_incl, hr_incl) \n");
            str.append(" SELECT \n");
            str.append(" (ISNULL(UltimoID.id,0) + 1), \n");
            str.append(" Clientes.CodCofre, \n");
            str.append(" ?,?,?,?,?,?,?,?,?,?,?,?,? \n");
            str.append(" FROM Clientes (NoLock) \n");
            str.append(" LEFT JOIN TesCofresMov (NoLock) \n");
            str.append("   ON Clientes.CodCofre = TesCofresMov.CodCofre \n");
            str.append("  AND TesCofresMov.Data = ? \n");
            str.append("  AND TesCofresMov.Hora = ? \n");
            str.append("  AND TesCofresMov.TipoDeposito = ? \n");
            str.append(" LEFT JOIN (SELECT ? AS CodCofre, \n");
            str.append("            MAX(id) AS id \n");
            str.append("            FROM TesCofresMov (NoLock) ) AS UltimoID \n");
            str.append("   ON Clientes.CodCofre = UltimoID.CodCofre \n");
            str.append(" WHERE Clientes.CodCofre = ? \n");
            str.append(" AND   TesCofresMov.id IS NULL \n");
            str.append(" GROUP BY Clientes.CodCofre, ISNULL(UltimoID.id,0) \n");

            Consulta consulta = new Consulta(str.toString(), persistencia);
            consulta.setString(movimentacao.getData().toString());
            consulta.setString(movimentacao.getHora());
            consulta.setString(movimentacao.getCodCliente());
            consulta.setString(movimentacao.getIdUsuario());
            if (movimentacao.getNomeUsuario().length() > 20) {
                movimentacao.setNomeUsuario(movimentacao.getNomeUsuario().substring(0, 19));
            }
            consulta.setString(movimentacao.getNomeUsuario());
            consulta.setBigDecimal(movimentacao.getValorDeposito());
            consulta.setString(movimentacao.getTipoMoeda());
            consulta.setString(movimentacao.getTipoDeposito());
            //consulta.setString(movimentacao.getCodigoBarras() + movimentacao.getHora());
            consulta.setString(movimentacao.getCodigoBarras());
            consulta.setString(movimentacao.getStatus());
            consulta.setString(movimentacao.getOperador());
            consulta.setString(movimentacao.getDt_Incl().toString());
            consulta.setString(movimentacao.getHr_Incl());

            consulta.setString(movimentacao.getData().toString());
            consulta.setString(movimentacao.getHora());
            consulta.setString(movimentacao.getTipoDeposito());
            consulta.setBigDecimal(movimentacao.getCodCofre());
            consulta.setBigDecimal(movimentacao.getCodCofre());
            int retorno = consulta.insert();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.inserirMovimentacao - " + e.getMessage() + "\n\n" + str.toString() + "\n\nValores: " + movimentacao.getData().toString() + ", "
                    + movimentacao.getHora() + ", "
                    + movimentacao.getCodCliente() + ", "
                    + movimentacao.getIdUsuario() + ", "
                    + movimentacao.getNomeUsuario() + ", "
                    + movimentacao.getValorDeposito().toPlainString() + ", "
                    + movimentacao.getTipoMoeda() + ", "
                    + movimentacao.getTipoDeposito() + ", "
                    + movimentacao.getCodigoBarras() + movimentacao.getHora() + ", "
                    + movimentacao.getStatus() + ", "
                    + movimentacao.getOperador() + ", "
                    + movimentacao.getDt_Incl().toString() + ", "
                    + movimentacao.getHr_Incl()
            );
        }
    }

    /**
     * Verifica antes de inserir
     *
     * @param movimentacao
     * @param persistencia
     * @return código do cofre
     * @throws Exception
     */
    public void inserirMovimentacaoKisan(TesCofresMov movimentacao, String machineId, Persistencia persistencia) throws Exception {
        try {
            String sql = "-- Inserindo a movimentacao\n"
                    + "INSERT INTO \n"
                    + "    TesCofresMov (id, codcofre, data, hora, codcliente, idusuario, nomeusuario, \n"
                    + "    valordeposito, tipomoeda, TipoDeposito, codigobarras, status, operador, dt_incl, hr_incl) \n"
                    + "SELECT \n"
                    + "    (ISNULL(UltimoID.id,0) + 1),?,?,?,?,?,?,?,?,?,?,?,?,?,?\n"
                    + "FROM \n"
                    + "    Clientes (NoLock) \n"
                    + "LEFT JOIN \n"
                    + "    TesCofresMov (NoLock) ON Clientes.CodCofre = TesCofresMov.CodCofre AND TesCofresMov.codigobarras = ? AND TesCofresMov.TipoDeposito = ?\n"
                    + "LEFT JOIN \n"
                    + "    (SELECT \n"
                    + "        ? AS CodCofre, MAX(id) AS id \n"
                    + "    FROM \n"
                    + "        TesCofresMov (NoLock) ) AS UltimoID ON Clientes.CodCofre = UltimoID.CodCofre \n"
                    + "WHERE \n"
                    + "    Clientes.CodCofre = ? AND TesCofresMov.id IS NULL \n"
                    + "GROUP BY \n"
                    + "    Clientes.CodCofre, ISNULL(UltimoID.id,0)\n";

            Consulta consulta = new Consulta(sql, persistencia);
            // Insert
            consulta.setBigDecimal(movimentacao.getCodCofre());
            consulta.setString(movimentacao.getData().toString());
            consulta.setString(movimentacao.getHora());
            consulta.setString(movimentacao.getCodCliente());
            consulta.setString(movimentacao.getIdUsuario());
            if (movimentacao.getNomeUsuario().length() > 20) {
                movimentacao.setNomeUsuario(movimentacao.getNomeUsuario().substring(0, 19));
            }
            consulta.setString(movimentacao.getNomeUsuario());
            consulta.setBigDecimal(movimentacao.getValorDeposito());
            consulta.setString(movimentacao.getTipoMoeda());
            consulta.setString(movimentacao.getTipoDeposito());
            consulta.setString(movimentacao.getCodigoBarras());
            consulta.setString(movimentacao.getStatus());
            consulta.setString(movimentacao.getOperador());
            consulta.setString(movimentacao.getDt_Incl().toString());
            consulta.setString(movimentacao.getHr_Incl());
            // left join select insert
            consulta.setString(movimentacao.getCodigoBarras());
            consulta.setString(movimentacao.getTipoDeposito());
            // left join select select
            consulta.setBigDecimal(movimentacao.getCodCofre());
            // where 
            consulta.setBigDecimal(movimentacao.getCodCofre());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.inserirMovimentacaoKisan - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaCofresUltimaComunicacao(Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "Select \n"
                    + " Clientes.CodCofre,\n"
                    + " REPLACE(FORMAT(CONVERT(date, COALESCE(TesCofresMov.Data, GETDATE())), 'd', 'pt-BR'),'/','') Data,\n"
                    + " REPLACE(ISNULL(TesCofresMov.Hora, '00:00:00'), ':','') Hora\n"
                    + " FROM Clientes\n"
                    + " LEFT JOIN (SELECT \n"
                    + "            MAX(TesCofresMov.id) id, \n"
                    + "            TesCofresMov.CodCofre\n"
                    + "            FROM TesCofresMov\n"
                    + "            WHERE TesCofresMov.NomeUsuario <> 'Aut-Satellite'"
                    + "            AND   (TesCofresMov.idUsuario IS NULL OR TesCofresMov.idUsuario <> 9999)"
                    + "            GROUP BY TesCofresMov.CodCofre) AS TesCofresMovRef\n"
                    + "   ON Clientes.CodCofre = TesCofresMovRef.CodCofre\n"
                    + " LEFT JOIN TesCofresMov\n"
                    + "   ON TesCofresMovRef.id = TesCofresMov.id\n"
                    + "  AND Clientes.CodCofre  = TesCofresMov.CodCofre\n"
                    + " WHERE Clientes.Situacao = 'A'\n"
                    + " AND   Clientes.MarcaAtm LIKE '%PRIME%'\n"
                    + " AND   Clientes.CodCofre IS NOT NULL\n"
                    + " ORDER BY Clientes.CodCofre";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            List<TesCofresMov> retorno = new ArrayList<>();
            TesCofresMov tescofremov;

            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setDataUltimaComunicacao(consulta.getString("Data"));
                tescofremov.setHora(consulta.getString("Hora"));

                retorno.add(tescofremov);
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaCofresUltimaComunicacao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void inserirResumo(TesCofresMov movimentacao, Persistencia persistencia) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();

            /* VARIAVEIS */
            sql.append("DECLARE @CodigoCofre Int\n");
            sql.append("DECLARE @DataAtual   Varchar(10)\n");
            sql.append(" SET @CodigoCofre = ? \n");
            sql.append(" SET @DataAtual   = (SELECT REPLACE(CONVERT(VARCHAR(10), getdate(), 111),'/','-'))\n");

            sql.append("Declare @UfCli Varchar(02)\n");
            sql.append("Declare @MunicCli Varchar(40)\n");
            sql.append("DECLARE @DiaSem Varchar(01)\n");
            sql.append("DECLARE @Feriado Varchar(01)\n");
            sql.append("DECLARE @VlrRecAntesCorte Float\n");
            sql.append("DECLARE @HrRecAntesCorte varchar(20)\n");
            sql.append("DECLARE @VlrRecAposCorte Float\n");
            sql.append("DECLARE @HrRecAposCorte varchar(20)\n");
            sql.append("DECLARE @CredD0Rec Float\n");
            sql.append("DECLARE @CredD0RecAposCorte Float\n");
            sql.append("DECLARE @CredD1Rec Float\n");
            sql.append("DECLARE @CredD0 Float \n");
            sql.append("DECLARE @CredD1 Float \n");
            sql.append("DECLARE @CredProxDU Float \n");
            sql.append("DECLARE @CredDiaAnt Float\n");
            sql.append("DECLARE @CredDiaD0Rec Float\n");
            sql.append("DECLARE @CredCorte Float\n");
            sql.append("DECLARE @VlrTotalCred Float \n");
            sql.append("DECLARE @VlrTotalRec Float \n");
            sql.append("DECLARE @SaldoFisTotal Float \n");
            sql.append("DECLARE @SaldoFisCred Float\n");
            sql.append(" SET @DiaSem = DATEPART(dw, @DataAtual)\n");
            sql.append(" SET @Feriado = 'N'\n");

            /*Removendo resumo */
            sql.append(" DELETE FROM TesCofresRes\n");
            sql.append(" WHERE TesCofresRes.CodCofre = @CodigoCofre\n");
            sql.append(" AND   TesCofresRes.Data     = @DataAtual\n");

            /*Buscando feriado */
            sql.append(" Select top 01 \n");
            sql.append(" @UfCli = Estado,\n");
            sql.append(" @MunicCli = Cidade\n");
            sql.append("  from Clientes (NoLock) where codCofre = @CodigoCofre\n");

            sql.append(" if(@feriado = 'N') begin\n");
            sql.append("	Select @feriado = Case when Count(*) > 0 then 'S' else 'N' end from Calendar (NoLock) where data = @DataAtual and Tipo = 'N'\n");
            sql.append(" end\n");

            sql.append(" if(@feriado = 'N') begin\n");
            sql.append("	Select @feriado = Case when Count(*) > 0 then 'S' else 'N' end from Calendar (NoLock) where data = @DataAtual and Tipo = 'E' and UF =  @UfCli\n");
            sql.append(" end\n");

            sql.append(" if(@feriado = 'N') begin\n");
            sql.append("	Select @feriado = Case when Count(*) > 0 then 'S' else 'N' end from Calendar (NoLock) where data = @DataAtual and Tipo = 'M' and UF =  @UfCli and Municipio = @MunicCli\n");
            sql.append(" end \n");

            /*Coletas do Dia */
            sql.append(" SELECT\n");
            sql.append("@VlrRecAntesCorte = Isnull(SUM(CASE WHEN CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) < @DataAtual + ' 16:00:00' THEN TesCofresMov.ValorDeposito ELSE 0 END),0),\n");
            sql.append("@HrRecAntesCorte = STUFF((SELECT ', ' + LEFT(HorasColeta.Hora,5)\n");
            sql.append("        FROM tesCofresMov (NoLock) AS HorasColeta\n");
            sql.append("        WHERE  HorasColeta.Data         = @DataAtual\n");
            sql.append("		AND HorasColeta.CodCofre        = @CodigoCofre\n");
            sql.append("        AND HorasColeta.TipoDeposito    = 'COLETA' \n");
            sql.append("        AND   HorasColeta.Hora         < '16:00:00'\n");
            sql.append("        ORDER BY HorasColeta.Hora\n");
            sql.append("        FOR XML PATH, TYPE).value('.[1]', 'NVARCHAR(max)'), 1, 2, ''),\n");
            sql.append("@VlrRecAposCorte = Isnull(SUM(CASE WHEN CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) >= @DataAtual + ' 16:00:00' THEN TesCofresMov.ValorDeposito ELSE 0 END),0),\n");
            sql.append("@HrRecAposCorte = STUFF((SELECT ', ' + LEFT(HorasPosColeta.Hora,5)\n");
            sql.append("        FROM tesCofresMov (NoLock) AS HorasPosColeta\n");
            sql.append("        WHERE  HorasPosColeta.Data         = @DataAtual\n");
            sql.append("		AND HorasPosColeta.CodCofre        = @CodigoCofre\n");
            sql.append("        AND HorasPosColeta.TipoDeposito    = 'COLETA' \n");
            sql.append("        AND   HorasPosColeta.Hora        >= '16:00:00'\n");
            sql.append("        ORDER BY HorasPosColeta.Hora\n");
            sql.append("        FOR XML PATH, TYPE).value('.[1]', 'NVARCHAR(max)'), 1, 2, '')\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'COLETA'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Coleta antes do Corte*/
            sql.append(" SELECT \n");
            sql.append("@CredD0Rec = SUM(Isnull(TesCofresMov.ValorDeposito,0))\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock) \n");
            sql.append("            WHERE TesCofresMov.Data = @DataAtual\n");
            sql.append("			AND   CodCofre          = @CodigoCofre\n");
            sql.append("            AND   TesCofresMov.Hora < '16:00:00'\n");
            sql.append("            AND   TipoDeposito      = 'COLETA'\n");
            sql.append("            GROUP BY id, CodCofre) AS UltimoRegColeta\n");
            sql.append(" ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("AND   TesCofresMov.id           < ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Coleta pos Corte*/
            sql.append(" SELECT \n");
            sql.append("@CredD1Rec = SUM(Isnull(TesCofresMov.ValorDeposito,0))\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock) \n");
            sql.append("            WHERE TesCofresMov.Data = @DataAtual\n");
            sql.append("			AND   CodCofre          = @CodigoCofre\n");
            sql.append("            AND   TesCofresMov.Hora >= '16:00:00'\n");
            sql.append("            AND   TipoDeposito      = 'COLETA'\n");
            sql.append("            GROUP BY id, CodCofre) AS UltimoRegColeta\n");
            sql.append(" ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("AND   TesCofresMov.id           < ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Credito D0*/
            sql.append("SELECT\n");
            sql.append("@CredD0 = Isnull(SUM(TesCofresMov.ValorDeposito),0)\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("WHERE CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) BETWEEN @DataAtual + ' 00:00:00' AND @DataAtual + ' 15:59:59'\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Cred D1*/
            sql.append("SELECT\n");
            sql.append("@CredD1 = Isnull(SUM(TesCofresMov.ValorDeposito),0)\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("WHERE CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) BETWEEN @DataAtual + ' 16:00:00' AND @DataAtual + ' 23:59:59'\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Resumo dia Anterior*/
            sql.append("SELECT\n");
            sql.append("@CredProxDU = CredProxDU\n");
            sql.append("FROM TesCofresRes (NoLock) \n");
            sql.append("WHERE TesCofresRes.Data = DATEADD(day, -1, @DataAtual)\n");
            sql.append("AND   TesCofresRes.CodCofre = @CodigoCofre\n");

            /*Saldo Antes Ultima Coleta Corte*/
            sql.append("SELECT \n");
            sql.append("@CredDiaAnt = Isnull(SUM(TesCofresMov.ValorDeposito),0)\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock) \n");
            sql.append("            WHERE TesCofresMov.Data = @DataAtual\n");
            sql.append("			AND   CodCofre          = @CodigoCofre            \n");
            sql.append("            AND   TesCofresMov.Hora < '16:00:00'\n");
            sql.append("            AND   TipoDeposito      = 'COLETA'\n");
            sql.append("            GROUP BY id, CodCofre) AS UltimoRegColeta\n");
            sql.append("    ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'  \n");
            sql.append("AND   TesCofresMov.id           < ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Saldo total*/
            sql.append("SELECT \n");
            sql.append("@SaldoFisTotal = SUM(TesCofresMov.ValorDeposito) \n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock)\n");
            sql.append("            WHERE CodCofre     = @CodigoCofre\n");
            sql.append("            AND   TipoDeposito = 'COLETA'\n");
            sql.append("            GROUP BY CodCofre) AS UltimoRegColeta\n");
            sql.append("    ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.id           > ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Caso Final de Semana ou Feriado zerar*/
            sql.append("if((@DiaSem in (1,7)) or (@feriado = 'S')) begin\n");
            sql.append("	Set @CredD0Rec = 0\n");
            sql.append("	Set @CredD0 = 0 \n");
            sql.append("	Set @CredD1 = 0 \n");
            sql.append("	Set @CredDiaAnt = 0\n");
            sql.append("end\n");

            sql.append("set @CredDiaD0Rec = (@CredD0 + @CredProxDU) - @CredDiaAnt\n");
            sql.append("set @CredCorte = @CredD0\n");
            sql.append("set @VlrTotalCred = (@CredProxDU + @CredD0)\n");
            sql.append("set @VlrTotalRec = (@VlrRecAntesCorte + @VlrRecAposCorte)\n");

            /*Apurando saldo fisico*/
            sql.append("set @SaldoFisCred = @VlrTotalCred - @CredDiaAnt;\n");
            sql.append("if(@VlrTotalRec > 0) begin\n");
            sql.append("	set @SaldoFisCred = @SaldoFisCred + ((@CredD0 + @CredProxDU)-@CredDiaAnt)\n");
            sql.append("	set @CredD0RecAposCorte = ((@CredD0 + @CredProxDU) - @CredDiaAnt)\n");
            sql.append("end\n");

            /*Inserindo Resumo*/
            sql.append("INSERT INTO TesCofresRes (CodCofre, Data, DiaSem, Feriado, VlrRecAntesCorte, HrRecAntesCorte, VlrRecAposCorte, HrRecAposCorte, CredD0Rec, \n");
            sql.append("CredD0RecAposCorte, CredD1Rec, CredD0, CredD1, CredProxDU, CredDiaAnt,CredDiaD0Rec, CredCorte,VlrTotalCred, VlrTotalRec, SaldoFisTotal, \n");
            sql.append("SaldoFisCred,DataStr)\n");
            sql.append("Values (@CodigoCofre, @DataAtual, @DiaSem, @feriado, @VlrRecAntesCorte, @HrRecAntesCorte, @VlrRecAposCorte, @HrRecAposCorte, @CredD0Rec, \n");
            sql.append("    	@CredD0RecAposCorte,@CredD1Rec, @CredD0, @CredD1, @CredProxDU, @CredDiaAnt, @CredDiaD0Rec, @CredCorte, @VlrTotalCred, @VlrTotalRec, \n");
            sql.append("		@SaldoFisTotal, @SaldoFisCred,Replace(@DataAtual,'-',''))\n");

            Consulta consulta = null;

            int insert = 0;
            String erro = "";

            for (int i = 0; i < 10; i++) {
                try {
                    consulta = new Consulta(sql.toString(), persistencia);
                    consulta.setBigDecimal(movimentacao.getCodCofre());
                    insert = consulta.insert();
                    if (insert > 0) {
                        break;
                    }
                    erro = erro + "\r\n" + "i = " + i
                            + ": insert não gerou exceção, apenas não conseguiu inserir nada para o cofre "
                            + movimentacao.getCodCofre();
                } catch (Exception er) {
                    insert = 0;
                    erro = "i = " + i + ": " + er.getMessage() + "\r\n" + erro;
                } finally {
                    consulta.close();
                }
            }
            if (insert == 0) {
                erro = "Tentativas excedidas para Resumo.\n" + erro;
                throw new Exception(erro);
            }
        } catch (Exception e) {
            throw new Exception("Erro ao gerar Resumo! " + e.getMessage());
        }
    }

    public void inserirResumo(String codCofre, Persistencia persistencia) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();

            /* VARIAVEIS */
            sql.append("DECLARE @CodigoCofre Int\n");
            sql.append("DECLARE @DataAtual   Varchar(10)\n");
            sql.append(" SET @CodigoCofre = ? \n");
            sql.append(" SET @DataAtual   = (SELECT REPLACE(CONVERT(VARCHAR(10), getdate(), 111),'/','-'))\n");

            sql.append("Declare @UfCli Varchar(02)\n");
            sql.append("Declare @MunicCli Varchar(40)\n");
            sql.append("DECLARE @DiaSem Varchar(01)\n");
            sql.append("DECLARE @Feriado Varchar(01)\n");
            sql.append("DECLARE @VlrRecAntesCorte Float\n");
            sql.append("DECLARE @HrRecAntesCorte varchar(20)\n");
            sql.append("DECLARE @VlrRecAposCorte Float\n");
            sql.append("DECLARE @HrRecAposCorte varchar(20)\n");
            sql.append("DECLARE @CredD0Rec Float\n");
            sql.append("DECLARE @CredD0RecAposCorte Float\n");
            sql.append("DECLARE @CredD1Rec Float\n");
            sql.append("DECLARE @CredD0 Float \n");
            sql.append("DECLARE @CredD1 Float \n");
            sql.append("DECLARE @CredProxDU Float \n");
            sql.append("DECLARE @CredDiaAnt Float\n");
            sql.append("DECLARE @CredDiaD0Rec Float\n");
            sql.append("DECLARE @CredCorte Float\n");
            sql.append("DECLARE @VlrTotalCred Float \n");
            sql.append("DECLARE @VlrTotalRec Float \n");
            sql.append("DECLARE @SaldoFisTotal Float \n");
            sql.append("DECLARE @SaldoFisCred Float\n");
            sql.append(" SET @DiaSem = DATEPART(dw, @DataAtual)\n");
            sql.append(" SET @Feriado = 'N'\n");

            /*Removendo resumo */
            sql.append(" DELETE FROM TesCofresRes\n");
            sql.append(" WHERE TesCofresRes.CodCofre = @CodigoCofre\n");
            sql.append(" AND   TesCofresRes.Data     = @DataAtual\n");

            /*Buscando feriado */
            sql.append(" Select top 01 \n");
            sql.append(" @UfCli = Estado,\n");
            sql.append(" @MunicCli = Cidade\n");
            sql.append("  from Clientes (NoLock) where codCofre = @CodigoCofre\n");

            sql.append(" if(@feriado = 'N') begin\n");
            sql.append("	Select @feriado = Case when Count(*) > 0 then 'S' else 'N' end from Calendar (NoLock) where data = @DataAtual and Tipo = 'N'\n");
            sql.append(" end\n");

            sql.append(" if(@feriado = 'N') begin\n");
            sql.append("	Select @feriado = Case when Count(*) > 0 then 'S' else 'N' end from Calendar (NoLock) where data = @DataAtual and Tipo = 'E' and UF =  @UfCli\n");
            sql.append(" end\n");

            sql.append(" if(@feriado = 'N') begin\n");
            sql.append("	Select @feriado = Case when Count(*) > 0 then 'S' else 'N' end from Calendar (NoLock) where data = @DataAtual and Tipo = 'M' and UF =  @UfCli and Municipio = @MunicCli\n");
            sql.append(" end \n");

            /*Coletas do Dia */
            sql.append(" SELECT\n");
            sql.append("@VlrRecAntesCorte = Isnull(SUM(CASE WHEN CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) < @DataAtual + ' 16:00:00' THEN TesCofresMov.ValorDeposito ELSE 0 END),0),\n");
            sql.append("@HrRecAntesCorte = STUFF((SELECT ', ' + LEFT(HorasColeta.Hora,5)\n");
            sql.append("        FROM tesCofresMov (NoLock) AS HorasColeta\n");
            sql.append("        WHERE  HorasColeta.Data         = @DataAtual\n");
            sql.append("		AND HorasColeta.CodCofre        = @CodigoCofre\n");
            sql.append("        AND HorasColeta.TipoDeposito    = 'COLETA' \n");
            sql.append("        AND   HorasColeta.Hora         < '16:00:00'\n");
            sql.append("        ORDER BY HorasColeta.Hora\n");
            sql.append("        FOR XML PATH, TYPE).value('.[1]', 'NVARCHAR(max)'), 1, 2, ''),\n");
            sql.append("@VlrRecAposCorte = Isnull(SUM(CASE WHEN CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) >= @DataAtual + ' 16:00:00' THEN TesCofresMov.ValorDeposito ELSE 0 END),0),\n");
            sql.append("@HrRecAposCorte = STUFF((SELECT ', ' + LEFT(HorasPosColeta.Hora,5)\n");
            sql.append("        FROM tesCofresMov (NoLock) AS HorasPosColeta\n");
            sql.append("        WHERE  HorasPosColeta.Data         = @DataAtual\n");
            sql.append("		AND HorasPosColeta.CodCofre        = @CodigoCofre\n");
            sql.append("        AND HorasPosColeta.TipoDeposito    = 'COLETA' \n");
            sql.append("        AND   HorasPosColeta.Hora        >= '16:00:00'\n");
            sql.append("        ORDER BY HorasPosColeta.Hora\n");
            sql.append("        FOR XML PATH, TYPE).value('.[1]', 'NVARCHAR(max)'), 1, 2, '')\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'COLETA'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Coleta antes do Corte*/
            sql.append(" SELECT \n");
            sql.append("@CredD0Rec = SUM(Isnull(TesCofresMov.ValorDeposito,0))\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock) \n");
            sql.append("            WHERE TesCofresMov.Data = @DataAtual\n");
            sql.append("			AND   CodCofre          = @CodigoCofre\n");
            sql.append("            AND   TesCofresMov.Hora < '16:00:00'\n");
            sql.append("            AND   TipoDeposito      = 'COLETA'\n");
            sql.append("            GROUP BY id, CodCofre) AS UltimoRegColeta\n");
            sql.append(" ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("AND   TesCofresMov.id           < ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Coleta pos Corte*/
            sql.append(" SELECT \n");
            sql.append("@CredD1Rec = SUM(Isnull(TesCofresMov.ValorDeposito,0))\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock) \n");
            sql.append("            WHERE TesCofresMov.Data = @DataAtual\n");
            sql.append("			AND   CodCofre          = @CodigoCofre\n");
            sql.append("            AND   TesCofresMov.Hora >= '16:00:00'\n");
            sql.append("            AND   TipoDeposito      = 'COLETA'\n");
            sql.append("            GROUP BY id, CodCofre) AS UltimoRegColeta\n");
            sql.append(" ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("AND   TesCofresMov.id           < ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Credito D0*/
            sql.append("SELECT\n");
            sql.append("@CredD0 = Isnull(SUM(TesCofresMov.ValorDeposito),0)\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("WHERE CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) BETWEEN @DataAtual + ' 00:00:00' AND @DataAtual + ' 15:59:59'\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Cred D1*/
            sql.append("SELECT\n");
            sql.append("@CredD1 = Isnull(SUM(TesCofresMov.ValorDeposito),0)\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("WHERE CONVERT(VARCHAR, (TesCofresMov.Data + ' ' + TesCofresMov.Hora), 120) BETWEEN @DataAtual + ' 16:00:00' AND @DataAtual + ' 23:59:59'\n");
            sql.append("AND   TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Resumo dia Anterior*/
            sql.append("SELECT\n");
            sql.append("@CredProxDU = CredProxDU\n");
            sql.append("FROM TesCofresRes (NoLock) \n");
            sql.append("WHERE TesCofresRes.Data = DATEADD(day, -1, @DataAtual)\n");
            sql.append("AND   TesCofresRes.CodCofre = @CodigoCofre\n");

            /*Saldo Antes Ultima Coleta Corte*/
            sql.append("SELECT \n");
            sql.append("@CredDiaAnt = Isnull(SUM(TesCofresMov.ValorDeposito),0)\n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock) \n");
            sql.append("            WHERE TesCofresMov.Data = @DataAtual\n");
            sql.append("			AND   CodCofre          = @CodigoCofre            \n");
            sql.append("            AND   TesCofresMov.Hora < '16:00:00'\n");
            sql.append("            AND   TipoDeposito      = 'COLETA'\n");
            sql.append("            GROUP BY id, CodCofre) AS UltimoRegColeta\n");
            sql.append("    ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.Data         = @DataAtual\n");
            sql.append("AND TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'  \n");
            sql.append("AND   TesCofresMov.id           < ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Saldo total*/
            sql.append("SELECT \n");
            sql.append("@SaldoFisTotal = SUM(TesCofresMov.ValorDeposito) \n");
            sql.append("FROM TesCofresMov (NoLock) \n");
            sql.append("LEFT JOIN (SELECT\n");
            sql.append("            MAX(id) AS id,\n");
            sql.append("            CodCofre\n");
            sql.append("            FROM TesCofresMov (NoLock)\n");
            sql.append("            WHERE CodCofre     = @CodigoCofre\n");
            sql.append("            AND   TipoDeposito = 'COLETA'\n");
            sql.append("            GROUP BY CodCofre) AS UltimoRegColeta\n");
            sql.append("    ON TesCofresMov.CodCofre = UltimoRegColeta.CodCofre\n");
            sql.append("WHERE TesCofresMov.CodCofre     = @CodigoCofre\n");
            sql.append("AND   TesCofresMov.id           > ISNULL(UltimoRegColeta.id,0)\n");
            sql.append("AND   TesCofresMov.TipoDeposito = 'DINHEIRO'\n");
            sql.append("GROUP BY TesCofresMov.CodCofre\n");

            /*Caso Final de Semana ou Feriado zerar*/
            sql.append("if((@DiaSem in (1,7)) or (@feriado = 'S')) begin\n");
            sql.append("	Set @CredD0Rec = 0\n");
            sql.append("	Set @CredD0 = 0 \n");
            sql.append("	Set @CredD1 = 0 \n");
            sql.append("	Set @CredDiaAnt = 0\n");
            sql.append("end\n");

            sql.append("set @CredDiaD0Rec = (@CredD0 + @CredProxDU) - @CredDiaAnt\n");
            sql.append("set @CredCorte = @CredD0\n");
            sql.append("set @VlrTotalCred = (@CredProxDU + @CredD0)\n");
            sql.append("set @VlrTotalRec = (@VlrRecAntesCorte + @VlrRecAposCorte)\n");

            /*Apurando saldo fisico*/
            sql.append("set @SaldoFisCred = @VlrTotalCred - @CredDiaAnt;\n");
            sql.append("if(@VlrTotalRec > 0) begin\n");
            sql.append("	set @SaldoFisCred = @SaldoFisCred + ((@CredD0 + @CredProxDU)-@CredDiaAnt)\n");
            sql.append("	set @CredD0RecAposCorte = ((@CredD0 + @CredProxDU) - @CredDiaAnt)\n");
            sql.append("end\n");

            /*Inserindo Resumo*/
            sql.append("INSERT INTO TesCofresRes (CodCofre, Data, DiaSem, Feriado, VlrRecAntesCorte, HrRecAntesCorte, VlrRecAposCorte, HrRecAposCorte, CredD0Rec, \n");
            sql.append("CredD0RecAposCorte, CredD1Rec, CredD0, CredD1, CredProxDU, CredDiaAnt,CredDiaD0Rec, CredCorte,VlrTotalCred, VlrTotalRec, SaldoFisTotal, \n");
            sql.append("SaldoFisCred,DataStr)\n");
            sql.append("Values (@CodigoCofre, @DataAtual, @DiaSem, @feriado, @VlrRecAntesCorte, @HrRecAntesCorte, @VlrRecAposCorte, @HrRecAposCorte, @CredD0Rec, \n");
            sql.append("    	@CredD0RecAposCorte,@CredD1Rec, @CredD0, @CredD1, @CredProxDU, @CredDiaAnt, @CredDiaD0Rec, @CredCorte, @VlrTotalCred, @VlrTotalRec, \n");
            sql.append("		@SaldoFisTotal, @SaldoFisCred,Replace(@DataAtual,'-',''))\n");

            Consulta consulta = null;

            int insert = 0;
            String erro = "";

            for (int i = 0; i < 10; i++) {
                try {
                    consulta = new Consulta(sql.toString(), persistencia);
                    consulta.setBigDecimal(codCofre);
                    insert = consulta.insert();
                    if (insert > 0) {
                        break;
                    }
                    erro = erro + "\r\n" + "i = " + i
                            + ": insert não gerou exceção, apenas não conseguiu inserir nada para o cofre "
                            + codCofre;
                } catch (Exception er) {
                    insert = 0;
                    erro = "i = " + i + ": " + er.getMessage() + "\r\n" + erro;
                } finally {
                    consulta.close();
                }
            }
            if (insert == 0) {
                erro = "Tentativas excedidas para Resumo.\n" + erro;
                throw new Exception(erro);
            }
        } catch (Exception e) {
            throw new Exception("Erro ao gerar Resumo! " + e.getMessage());
        }
    }

    public void inserirMovimentacaoSemVerificacao(TesCofresMov movimentacao, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT into TesCofresMov (id, codcofre, data, hora, codcliente, idusuario, nomeusuario,"
                    + " valordeposito, tipomoeda, TipoDeposito, codigobarras, status, operador, dt_incl, hr_incl)"
                    + " values ((select (isnull(max(id),0)+1) id from TesCofresMov),?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(movimentacao.getCodCofre());
            consulta.setString(movimentacao.getData().toString());
            consulta.setString(movimentacao.getHora());
            consulta.setBigDecimal(movimentacao.getCodCliente());
            consulta.setString(movimentacao.getIdUsuario());
            consulta.setString(movimentacao.getNomeUsuario());
            consulta.setBigDecimal(movimentacao.getValorDeposito());
            consulta.setString(movimentacao.getTipoMoeda());
            consulta.setString(movimentacao.getTipoDeposito());
            consulta.setString(movimentacao.getCodigoBarras());
            consulta.setString(movimentacao.getStatus());
            consulta.setString(movimentacao.getOperador());
            consulta.setString(movimentacao.getDt_Incl().toString());
            consulta.setString(movimentacao.getHr_Incl());

            int insert = 0;
            String erro = "Tentativas excedidas.";
            for (int i = 0; i < 20; i++) {
                try {
                    insert = consulta.insert();
                    if (insert > 0) {
                        break;
                    }
                } catch (Exception er) {
                    insert = 0;
                    erro = "Tentativas excedidas.\r\n" + er.getMessage();
                }
            }
            consulta.close();
            if (insert == 0) {
                throw new Exception(erro);
            }
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.inserirMovimentacaoSemVerificacao - " + e.getMessage() + "\r\n"
                    + " INSERT into TesCofresMov (id, codcofre, data, hora, codcliente, idusuario, nomeusuario,"
                    + " valordeposito, tipomoeda, TipoDeposito, codigobarras, status, operador, dt_incl, hr_incl)"
                    + " values ((select (isnull(max(id),0)+1) id from TesCofresMov)," + movimentacao.getCodCofre() + ", "
                    + movimentacao.getData().toString() + ", " + movimentacao.getHora() + ", " + movimentacao.getCodCliente() + ", "
                    + movimentacao.getIdUsuario() + ", " + movimentacao.getNomeUsuario() + ", " + movimentacao.getValorDeposito() + ", "
                    + movimentacao.getTipoMoeda() + ", " + movimentacao.getTipoDeposito() + ", " + movimentacao.getCodigoBarras() + ", "
                    + movimentacao.getStatus() + ", " + movimentacao.getOperador() + ", " + movimentacao.getDt_Incl().toString() + ", "
                    + movimentacao.getHr_Incl() + ")");
        }
    }

    public List<TesCofresMov> getIntegracaoUnificado(String codCofre, String nop, String prefixo, String versao, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        String sql = "";
        try {

            /*Para Todos Cofres*/
            sql = "SELECT\n"
                    + " A.Date nop,\n"
                    + " CAST(DATE_FORMAT(A.Date,'%Y-%m-%d') AS DATE) Data,\n";
            if (!versao.equals("1000")) {
                sql += " CAST(A.Time AS TIME) Hora,\n";
            } else {
                sql += " CAST(DATE_FORMAT(A.Date,'%H:%i:%s') AS TIME) Hora,\n";
            }
            sql += " B.ID idUsuario,\n"
                    + " SUBSTRING(B.Name, 1, 20) Nome,\n";
            if (!versao.equals("1000")) {
                sql += " CAST(DATE_FORMAT(CONCAT(A.Date,' ', A.Time),'%Y-%m-%d%H:%i:%s') AS CHAR) DataHora,\n";
            } else {
                sql += " CAST(DATE_FORMAT(A.Date,'%Y-%m-%d%H:%i:%s') AS CHAR) DataHora,\n";
            }
            sql += " CASE WHEN A.Type = 1 then 'COLETA' \n"
                    + "      WHEN A.Type = 0 THEN 'DINHEIRO' \n"
                    + "      ELSE '' END as Tipo,\n";

            /* Cofre - Versão 1000 */
            if (versao.equals("1000")) {
                sql += " A.details Detalhes\n";
            } else {
                /* Cofre - Outras versoes (Nao 1000) */
                sql += " C.detail Detalhes\n";
            }

            sql += " FROM " + prefixo + "_transactions  A\n"
                    + " LEFT JOIN " + prefixo + "_users B\n"
                    + "   ON A.UserId = B.id\n";

            if (!versao.equals("1000")) {
                /* Cofre - Outras versoes (Nao 1000) */
                sql += " LEFT JOIN " + prefixo + "_transdetails AS C\n"
                        + "  ON A.device = C.device\n"
                        + " AND A.nop = C.nop\n";
            }

            sql += " WHERE IF(? NOT IN(0,'0'), DATE_FORMAT(A.Date,'%Y-%m-%d %H:%i:%s') > DATE_FORMAT(CAST(CONCAT(LEFT(REPLACE(?,' ', ''),10),' ', RIGHT(REPLACE(?,' ', ''),8)) AS DATETIME),'%Y-%m-%d %H:%i:%s'), A.Date IS NOT NULL)\n"
                    + " ORDER BY A.Date \n"
                    + " LIMIT 100";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nop);
            consulta.setString(nop);
            consulta.setString(nop);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setCodCofre(new BigDecimal(codCofre));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente("1");
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("Nome"));
                tescofremov.setTipoDeposito(consulta.getString("Tipo"));
                tescofremov.setCodigoBarras(consulta.getString("DataHora"));
                tescofremov.setDetalhes(consulta.getString("Detalhes"));
                tescofremov.setStatus("NORMAL");
                tescofremov.setOperador("Imp-Auto");
                tescofremov.setDt_Incl(LocalDate.now());
                tescofremov.setHr_Incl(DataAtual.getDataAtual("HORA"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    public List<TesCofresMov> getIntegracao(String codCofre, String nop, Persistencia_OLD persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = " Select a.Date nop, Cast(a.Date as date) Data, cast(a.Date as time) Hora, b.ID idUsuario, Substring(b.Name,1,20) Nome, \n"
                    + " CAST(DATE_FORMAT(a.Date,'%Y-%m-%d%H:%i:%s') AS CHAR) DataHora, \n"
                    + " a.Total0+ a.Total1+a.Total2+a.Total3 Total, \n"
                    + " a.Currency0 TipoMoeda, \n"
                    + " Case \n"
                    + "     when a.Type = 1 then 'COLETA' \n"
                    + "     when a.Type = 0 THEN 'DINHEIRO' \n"
                    + " else '' end as Tipo, a.details \n"
                    + " from t_transactions a \n"
                    + " Left Join t_users b on b.id = a.UserId \n"
                    //+ " where a.Date > ? \n"
                    + " WHERE IF(? NOT IN(0,'0'), DATE_FORMAT(a.Date,'%Y-%m-%d %H:%i:%s') > DATE_FORMAT(CAST(CONCAT(LEFT(REPLACE(?,' ', ''),10),' ', RIGHT(REPLACE(?,' ', ''),8)) AS DATETIME),'%Y-%m-%d %H:%i:%s'),a.Date IS NOT NULL)\n"
                    + " ORDER BY a.Date \n"
                    + " LIMIT 100";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nop);
            consulta.setString(nop);
            consulta.setString(nop);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setCodCofre(new BigDecimal(codCofre));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente("1");
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("Nome"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("Total"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("Tipo"));
                tescofremov.setCodigoBarras(consulta.getString("DataHora"));
                tescofremov.setStatus("NORMAL");
                tescofremov.setOperador("Imp-Auto");
                tescofremov.setDt_Incl(LocalDate.now());
                tescofremov.setHr_Incl(DataAtual.getDataAtual("HORA"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    public List<TesCofresMov> getIntegracao(String codCofre, String nop, String prefixo, Persistencia_OLD persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "Select a.Date nop, Cast(a.Date as date) Data, cast(a.Date as time) Hora, b.ID idUsuario, Substring(b.Name,1,20) Nome,\n"
                    //+ "a.Date DateTime,\n"
                    + " CAST(DATE_FORMAT(a.Date,'%Y-%m-%d%H:%i:%s') AS CHAR) DataHora,\n"
                    + " Case \n"
                    + "	 when a.Type = 1 then 'COLETA' \n"
                    + "	 when a.Type = 0 THEN 'DINHEIRO' \n"
                    + " else '' end as Tipo  \n"
                    + " from " + prefixo + "_transactions a \n"
                    + " Left Join " + prefixo + "_users b on b.id = a.UserId \n"
                    //+ " where a.Date > ? \n"
                    + " WHERE IF(? NOT IN(0,'0'), DATE_FORMAT(a.Date,'%Y-%m-%d %H:%i:%s') > DATE_FORMAT(CAST(CONCAT(LEFT(REPLACE(?,' ', ''),10),' ', RIGHT(REPLACE(?,' ', ''),8)) AS DATETIME),'%Y-%m-%d %H:%i:%s'),a.Date IS NOT NULL)\n"
                    + " ORDER BY a.Date \n"
                    + " LIMIT 100";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nop);
            consulta.setString(nop);
            consulta.setString(nop);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setCodCofre(new BigDecimal(codCofre));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(("1"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("Nome"));
//                tescofremov.setValorDeposito(consulta.getBigDecimal("Total"));
//                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("Tipo"));
//                tescofremov.setCodigoBarras(RecortaAteEspaço(consulta.getString("nop"), 0, 19));
                tescofremov.setCodigoBarras(consulta.getString("DataHora"));
                //tescofremov.setDetalhes(consulta.getString("Detalhes"));
                tescofremov.setStatus("NORMAL");
                tescofremov.setOperador("Imp-Auto");
                tescofremov.setDt_Incl(LocalDate.now());
                tescofremov.setHr_Incl(DataAtual.getDataAtual("HORA"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    public String ultimaMovimentacaoIntegracao(String codcofre, String idUsuario, Persistencia persistencia) throws Exception {
        try {
            String sql = "select top 1 codigobarras "
                    + " from TesCofresMov "
                    + " where codcofre = ? and idusuario not like ? "
                    + " order by id desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codcofre);
            consulta.setString("%" + idUsuario + "%");
            consulta.select();
            String retorno = null;
            while (consulta.Proximo()) {
                retorno = consulta.getString("codigobarras");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Falha ao max(id) - " + e.getMessage());
        }
    }

    public String ultimaMovimentacaoIntegracaoMasterCoin(String codcofre, String idUsuario, Persistencia persistencia) throws Exception {
        try {
            String sql = "select top 1 codigobarras "
                    + " from TesCofresMov "
                    + "  join clientes on Clientes.CodCofre = TesCofresMov.CodCofre "
                    + "                    and Clientes.MarcaAtm like '%master%'"
                    + " where TesCofresMov.codcofre = ? and idusuario not like ? "
                    + " and TEsCofresmov.NomeUsuario Not Like '%GUNN%'"
                    + " order by id desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codcofre);
            consulta.setString("%" + idUsuario + "%");
            consulta.select();
            String retorno = null;
            while (consulta.Proximo()) {
                retorno = consulta.getString("codigobarras");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Falha ao max(id) - " + e.getMessage());
        }
    }

    public BigDecimal getId(Persistencia persistencia) throws Exception {
        try {
            String sql = " select isnull(max(id),0) id from TesCofresMov ";
            Consulta idSql = new Consulta(sql, persistencia);
            idSql.select();
            BigDecimal id = BigDecimal.ZERO;
            while (idSql.Proximo()) {
                id = idSql.getBigDecimal("id");
            }
            idSql.Close();
            return id;
        } catch (Exception e) {
            throw new Exception(" Falha ao buscar max(id) - " + e.getMessage());
        }
    }

    public Integer totalPaginacao(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT  count(*) total\n"
                    + "FROM TesCofresMov\n"
                    + "WHERE codCofre IS NOT NULL";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.totalPaginacao - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "SELECT  *  FROM ( SELECT    ROW_NUMBER() OVER ( ORDER BY Hora DESC ) AS RowNum,\n"
                    + "TesCofresMov.*\n"
                    + "FROM TesCofresMov\n"
                    + "WHERE codCofre IS NOT NULL\n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult\n"
                    + " WHERE RowNum >= ? AND RowNum < ?\n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaPaginadaCliente(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "SELECT  *  FROM ( SELECT    ROW_NUMBER() OVER ( ORDER BY Hora DESC ) AS RowNum,\n"
                    + "TesCofresMov.*, Clientes.NRed \n"
                    + "FROM TesCofresMov\n"
                    + "LEFT JOIN Clientes on Clientes.CodCofre = TesCofresMov.CodCofre\n"
                    + "WHERE TesCofresMov.CodCofre IS NOT NULL\n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult\n"
                    + " WHERE RowNum >= ? AND RowNum < ?\n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                tescofremov.setNRedCofre(consulta.getString("NRed"));
                retorno.add(tescofremov);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaPaginadaCliente - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaPaginadaCliente(Map filtros, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "SELECT TesCofresMov.*, Clientes.NRed \n"
                    + "FROM TesCofresMov\n"
                    + "LEFT JOIN Clientes on Clientes.CodCofre = TesCofresMov.CodCofre\n"
                    + "                   AND Clientes.TpCli = '4'\n"
                    + "WHERE TesCofresMov.CodCofre IS NOT NULL AND Clientes.CodCofre > 0 \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + " ORDER BY Hora DESC";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                tescofremov.setNRedCofre(consulta.getString("NRed"));
                retorno.add(tescofremov);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaPaginadaCliente - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaPaginadaCliente(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "SELECT TesCofresMov.*, Clientes.NRed \n"
                    + "FROM TesCofresMov\n"
                    + "LEFT JOIN Clientes on Clientes.CodCofre = TesCofresMov.CodCofre\n"
                    + "                   AND Clientes.TpCli = '4'\n";

            if (!exibirTodos) {
                sql += " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                        + " and PessoaCliAut.CodFil = Clientes.Codfil\n";
            }
            sql += "WHERE TesCofresMov.CodCofre IS NOT NULL AND Clientes.CodCofre > 0 \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + " ORDER BY Hora DESC";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                tescofremov.setNRedCofre(consulta.getString("NRed"));
                retorno.add(tescofremov);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaPaginadaCliente - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaMovimentacaoCofre(Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "Select Clientes.CodCofre, Clientes.NRed, TesCofresMov.Data, TesCofresMov.Hora, TesCofresMov.TipoDeposito, TesCofresMov.ValorDeposito\n"
                    + "-- (Select Hora from TesCofresMov \n"
                    + "-- where TesCofresMov.ID = (Select Max(ID) from TesCofresMov where TesCofresMov.Data = Cast(GetDate() as Date)\n"
                    + "--   and TesCofresMov.CodCofre = Clientes.CodCofre and SubString(Hora,1,5) <> '00:00')) HrUltMov\n"
                    + " from Clientes\n"
                    + "LEFT JOIN TesCofresMov ON TesCofresMov.CodCofre = Clientes.CodCofre\n"
                    + "         AND TesCofresMov.ID = (Select Max(ID) from TesCofresMov where TesCofresMov.Data = Cast(GetDate() as Date)\n"
                    + "         and TesCofresMov.CodCofre = Clientes.CodCofre and SubString(Hora,1,5) <> '00:00')\n"
                    + "\n"
                    + "where Clientes.Situacao =  'A'\n"
                    + "  and Clientes.TpCli = '4'\n"
                    + "  and (Select Count(*)  from TesCofresMov where TesCofresMov.CodCofre = Clientes.CodCofre and data >= '20200201' and valorDeposito > 0.001) > 0\n"
                    + "order by Data Desc, Hora Desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
//                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
//                tescofremov.setCodCliente(consulta.getString("CodCliente"));
//                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
//                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
//                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito").toUpperCase());
//                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
//                tescofremov.setStatus(consulta.getString("Status"));
//                tescofremov.setOperador(consulta.getString("Operador"));
//                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
//                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                tescofremov.setNRedCofre(consulta.getString("NRed"));
                retorno.add(tescofremov);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaMovimentacaoCofre - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listaMovimentacaoCofre(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "Select Clientes.CodCofre, Clientes.NRed, TesCofresMov.Data, TesCofresMov.Hora, TesCofresMov.TipoDeposito, TesCofresMov.ValorDeposito\n"
                    + "-- (Select Hora from TesCofresMov \n"
                    + "-- where TesCofresMov.ID = (Select Max(ID) from TesCofresMov where TesCofresMov.Data = Cast(GetDate() as Date)\n"
                    + "--   and TesCofresMov.CodCofre = Clientes.CodCofre and SubString(Hora,1,5) <> '00:00')) HrUltMov\n"
                    + " from Clientes\n"
                    + "LEFT JOIN TesCofresMov ON TesCofresMov.CodCofre = Clientes.CodCofre\n"
                    + "         AND TesCofresMov.ID = (Select Max(ID) from TesCofresMov where TesCofresMov.Data = Cast(GetDate() as Date)\n"
                    + "         and TesCofresMov.CodCofre = Clientes.CodCofre and SubString(Hora,1,5) <> '00:00')\n";
            if (!exibirTodos) {
                sql += " inner Join PessoaCliAut  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                        + " and PessoaCliAut.CodFil = Clientes.Codfil\n";
            }
            sql += "where Clientes.Situacao =  'A'\n"
                    + "  and Clientes.TpCli = '4'\n"
                    + "  and (Select Count(*)  from TesCofresMov where TesCofresMov.CodCofre = Clientes.CodCofre and data >= '20200201' and valorDeposito > 0.001) > 0\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }
            sql += "order by Data Desc, Hora Desc";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito").toUpperCase());

                tescofremov.setNRedCofre(consulta.getString("NRed"));
                retorno.add(tescofremov);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listaMovimentacaoCofre - " + e.getMessage());
        }
    }

    /**
     * Lista detahes de movimentação de um cofre por data
     *
     * @param codCofre
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesCofresMov> listarMovimentacaoCompleta(String codCofre, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = " Select * From TesCofresMov \n"
                    + " Where codCofre = ? \n"
                    + " Order by Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCofre);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listarMovimentacaoCompleta - " + e.getMessage() + "\r\n"
                    + " Select * From TesCofresMov \n"
                    + " Where codCofre = " + codCofre + " \n"
                    + " Order by Hora desc");
        }
    }

    /**
     * Lista detahes de movimentação de um cofre por data
     *
     * @param codCofre
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesCofresMov> listarMovimentacaoDiaria(BigDecimal codCofre, String data, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = " Select * From TesCofresMov"
                    + " Where codCofre = ? "
                    + "       and data = ? "
                    + " Order by Data Desc, Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codCofre);
            consulta.setString(data);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    /**
     * Lista detahes de movimentação de um cofre por data
     *
     * @param codCofre
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public MovimentacaoGeralFuncionario listarMovimentacaoDiariaCofre(String codCofre, String data, Persistencia persistencia) throws Exception {
        MovimentacaoGeralFuncionario movimentacaoGeralFuncionario = new MovimentacaoGeralFuncionario();
        TesCofresMov tescofremov;
        try {
            String sql = "Select \n"
                    + "(SELECT sum(ISNULL(ValorDeposito,0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'COLETA') ValorColetas,\n"
                    + "(SELECT sum(ISNULL(ValorDeposito,0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'DINHEIRO') ValorDepositos, * From TesCofresMov\n"
                    + "Where codCofre = ? and data = ?\n"
                    + " Order by Data Desc, Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCofre);
            consulta.setString(data);
            consulta.select();
            while (consulta.Proximo()) {

                movimentacaoGeralFuncionario.setValorColetas(consulta.getString("ValorColetas"));
                movimentacaoGeralFuncionario.setValorDepositos(consulta.getString("ValorDepositos"));

                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                movimentacaoGeralFuncionario.getMovimentacoes().add(tescofremov);
            }
            consulta.Close();
            return movimentacaoGeralFuncionario;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    public MovimentacaoGeralFuncionario listarMovimentacaoDiariaCofre(String codCofre, String dataInicio, String dataFinal,
            String horaInicio, String horaFinal,
            String movimentacao,
            Persistencia persistencia) throws Exception {
        MovimentacaoGeralFuncionario movimentacaoGeralFuncionario = new MovimentacaoGeralFuncionario();
        TesCofresMov tescofremov;
        try {
            String sql = "Select \n"
                    + "(SELECT sum(ISNULL(ValorDeposito,0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data between ? and ? \n"
                    + "     and t.Hora >= case when t.Data = ? then ? else '00:00' end \n"
                    + "     and t.Hora <= case when t.Data = ? then ? else '24:00' end \n"
                    + "     and t.codCofre = TesCofresMov.codCofre \n"
                    + "     and t.TipoDeposito = 'COLETA') ValorColetas,\n"
                    + "(SELECT sum(ISNULL(ValorDeposito,0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data between ? and ? \n"
                    + "     and t.Hora >= case when t.Data = ? then ? else '00:00' end \n"
                    + "     and t.Hora <= case when t.Data = ? then ? else '24:00' end \n"
                    + "     and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'DINHEIRO') ValorDepositos, * \n"
                    + "From TesCofresMov\n"
                    + "Where codCofre = ? and data between ? and ? \n"
                    + "     and Hora > case when Data = ? then ? else '00:00' end \n"
                    + "     and Hora < case when Data = ? then ? else '24:00' end \n";
            if (movimentacao != null) {
                sql += " AND TipoDeposito = ? ";
            }
            sql += " Order by Data Desc, Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFinal);
            consulta.setString(dataInicio);
            consulta.setString(horaInicio);
            consulta.setString(dataFinal);
            consulta.setString(horaFinal);

            consulta.setString(dataInicio);
            consulta.setString(dataFinal);
            consulta.setString(dataInicio);
            consulta.setString(horaInicio);
            consulta.setString(dataFinal);
            consulta.setString(horaFinal);

            consulta.setString(codCofre);
            consulta.setString(dataInicio);
            consulta.setString(dataFinal);
            consulta.setString(dataInicio);
            consulta.setString(horaInicio);
            consulta.setString(dataFinal);
            consulta.setString(horaFinal);
            if (movimentacao != null) {
                consulta.setString(movimentacao);
            }
            consulta.select();
            while (consulta.Proximo()) {

                movimentacaoGeralFuncionario.setValorColetas(consulta.getString("ValorColetas"));
                movimentacaoGeralFuncionario.setValorDepositos(consulta.getString("ValorDepositos"));

                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                movimentacaoGeralFuncionario.getMovimentacoes().add(tescofremov);
            }
            consulta.Close();
            return movimentacaoGeralFuncionario;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listarMovimentacoesAnteriores(BigDecimal codCofre, String data, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = " Select TOP 200 * From TesCofresMov"
                    + " Where codCofre = ? "
                    + "       and data <= ? "
                    + " Order by Data Desc, Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codCofre);
            consulta.setString(data);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    public List<TesCofresMov> listarMovimentacaoDiariaPrime(String dataInicio, String dataFim, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = " select tescofresmov.* \n"
                    + " from clientes \n"
                    + " left join tescofresmov on tescofresmov.codcofre = clientes.codcofre \n"
                    + " Where  marcaatm like '%prime%' "
                    + "       and data between ? and ?  "
                    + " Order by Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras").replace(".0", ""));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Failed to list detalhes de movimentação diária de cofre - " + e.getMessage());
        }
    }

    /**
     * Lista detahes de movimentação de um cofre por data
     *
     * @param codCofre
     * @param data1, data2
     * @param data2
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesCofresMov> listarMovimentacaoPeriodo(String codCofre, String data1, String data2, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = " Select *, ROUND(CAST (ValorDeposito AS decimal (12,2)),2) ValorDepositoArredondamento From TesCofresMov"
                    + " Where codCofre = ? "
                    + "       and data between ? and ? "
                    + " Order by Data desc, Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCofre);
            consulta.setString(data1);
            consulta.setString(data2);
            consulta.select();
            TesCofresMov tescofremov;
            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                try {
                    tescofremov.setIdUsuario(consulta.getString("idUsuario").replace(".0", ""));
                } catch (Exception ex) {
                    tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                }
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDepositoArredondamento"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listarMovimentacaoPeriodo - " + e.getMessage() + "\r\n"
                    + " Select * From TesCofresMov"
                    + " Where codCofre = " + codCofre
                    + "       and data between " + data1 + " and " + data2
                    + " Order by Data desc, Hora desc");
        }
    }

    public List<TesCofresMov> listarMovimentacaoPeriodo(String codCofre, String data1, String data2, String tipo, Persistencia persistencia) throws Exception {
        List<TesCofresMov> retorno = new ArrayList<>();
        try {
            String sql = "SELECT *, ROUND(CAST (ValorDeposito AS decimal (12,2)),2) ValorDepositoArredondamento FROM TesCofresMov\n"
                    + "  WHERE codCofre = ?\n"
                    + "  AND   data between ? and ?\n"
                    + "  AND   TipoDeposito = ?\n"
                    + "  ORDER BY data, Hr_incl DESC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCofre);
            consulta.setString(data1);
            consulta.setString(data2);
            consulta.setString(tipo);
            consulta.select();
            TesCofresMov tescofremov;

            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDepositoArredondamento"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.add(tescofremov);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.listarMovimentacaoPeriodo - " + e.getMessage() + "\r\n"
                    + " Select * From TesCofresMov"
                    + " Where codCofre = " + codCofre
                    + "       and data between " + data1 + " and " + data2
                    + " Order by Data desc, Hora desc");
        }
    }

    public void atualizaCofres(String dtIni, Persistencia persistencia) throws Exception {
        try {
            LocalDate dtIniLD = LocalDate.parse(dtIni, DateTimeFormatter.ofPattern("yyyyMMdd"));
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(Date.from(dtIniLD.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            calendar.add(Calendar.DAY_OF_WEEK, -1);

            String sql = "Select CONVERT(VarChar,TesCofresMov.Data, 112) DataC, TesCofresMov.*, \n"
                    + " Cli2.CodCofre, Cli2.NRed NRedCofre, Cli2.Cidade, Cli2.Estado UF, Cli2.Envelope, \n"
                    + "(Select Top 1 isNull(TCM.ValorDeposito,0) ValorDeposito from TesCofresMov TCM\n"
                    + " where TCM.CodCofre = TesCofresMov.CodCofre\n"
                    + "   and TCM.TipoDeposito = 'COLETA'\n"
                    + "   and TCM.Data <= ?\n"
                    + " order by TCM.id Desc) ValorUltColeta, \n"
                    + "(Select Top 1 isNull(SaldoFisCst,0) SaldoFisCST from TesCofresRes \n"
                    + " where TesCofresRes.CodCofre = TesCofresMov.CodCofre \n"
                    + "   and TesCofresRes.Data <= ?\n"
                    + " order by TesCofresRes.Data desc) SaldoFisCst, \n"
                    + "(Select Top 1 isnull(CredProxDU,0) CredProxDU from TesCofresRes \n"
                    + " where TesCofresRes.CodCofre = Cli2.CodCofre \n"
                    + "   and TesCofresRes.Data <= ?\n"
                    + " order by TesCofresRes.Data desc) CredProxDU  \n"
                    + "from TesCofresMov \n"
                    + "left join Clientes Cli2 on  Cli2.CodCofre = TesCofresMov.CodCofre \n"
                    + "where TesCofresMov.CodCofre in (Select Cli2.CodCofre from OS_Vig \n"
                    + " left join Clientes      on  Clientes.Codigo = OS_Vig.CliDst \n"
                    + "                         and Clientes.CodFil = OS_Vig.CodFil \n"
                    + " left join Clientes Cli2 on  Cli2.Codigo = OS_Vig.Cliente \n"
                    + "                         and Cli2.CodFil = OS_Vig.CodFil \n"
                    + " where SubString(OS_Vig.CliDst ,4,1) = '7'\n"
                    + "   and SubString(OS_Vig.Cliente,4,1) = '4'\n"
                    + "   and OS_Vig.Situacao = 'A'\n"
                    + "   and OS_Vig.DtFim   >= ?\n"
                    + "   and Clientes.NRed like '%TES CI%')   \n"
                    + "   and \n"
                    + "TesCofresMov.Data >= Isnull((Select Top 1 TCM.Data from TesCofresMov TCM\n"
                    + " where TCM.CodCofre = TesCofresMov.CodCofre\n"
                    + "   and TCM.TipoDeposito = 'COLETA'\n"
                    + "   and TCM.Data < ?\n"
                    + "order by TCM.Data Desc),?)\n"
                    + "  and TesCofresMov.IDUsuario <>  '999999'\n"
                    + " order by TesCofresmov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtIni);
            consulta.setString(dtIni);
            consulta.setString(dtIni);
            consulta.setString(dtIni);
            consulta.setString(dtIni);
            consulta.setString(calendar.getTime().toInstant().atZone(ZoneId.systemDefault())
                    .toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            consulta.select();
            TesCofresMov tescofremov;

            while (consulta.Proximo()) {
                tescofremov = new TesCofresMov();
                tescofremov.setEnvelope("Envelope");
                if (tescofremov.getEnvelope().equals("0")) {
                    tescofremov.setValorUltColeta("0");
                } else {
                    tescofremov.setValorUltColeta(consulta.getString("ValorUltColeta"));
                }
                tescofremov.setSaldoFisCst(consulta.getString("SaldoFisCst"));
                tescofremov.setCredProxDU(consulta.getString("CredProxDU"));
                tescofremov.setData(LocalDate.parse(consulta.getString("DataC"), DateTimeFormatter.ofPattern("yyyyMMdd")));

                if (dtIniLD.isBefore(tescofremov.getData())) {
                    System.out.println(tescofremov.toString());
                }

//      vSaldoFisico  := 0;
//      vSaldoTotDAnt := 0;
//      vSaldoD1Dep   := 0;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.dataUltimaColeta - " + e.getMessage() + "\r\n"
                    + "Select Top 1 TCM.Data from TesCofresMov \n"
                    + " where TCM.CodCofre = TesCofresMov.CodCofre \n"
                    + "   and TCM.TipoDeposito = 'COLETA' \n"
                    + "   and TCM.Data < " + dtIni + " \n"
                    + " order by TCM.Data Desc");
        }
    }

    public MovimentacaoGeralFuncionario movimentacaoIndividual(String data, String codCofre, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            MovimentacaoGeralFuncionario movimentacaoGeralFuncionario = new MovimentacaoGeralFuncionario();
            TesCofresMov tescofremov;
            String sql = "SELECT (SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'DINHEIRO'\n"
                    + "GROUP BY t.idUsuario) ValorDepositos,\n"
                    + "\n"
                    + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'COLETA'\n"
                    + "GROUP BY t.idUsuario) ValorColetas,\n"
                    + "\n"
                    + " TesCofresMov.id, TesCofresMov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora,\n"
                    + "    TesCofresMov.CodCliente, TesCofresMov.idUsuario,  Pessoa.Nome NomeUsuario, TesCofresMov.ValorDeposito,\n"
                    + "     TesCofresMov.TipoMoeda, TesCofresMov.TipoDeposito, TesCofresMov.CodigoBarras, TesCofresMov.Status,\n"
                    + "     Pessoa.Nome Operador, TesCofresMov.Dt_Incl, TesCofresMov.Hr_Incl\n"
                    + "    FROM TesCofresMov\n"
                    + "    INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                    + "   WHERE TesCofresMov.Data =  ? and TesCofresMov.codCofre = ? and TesCofresMov.idUsuario = ? \n"
                    + "    ORDER BY Pessoa.Nome ASC, Data DESC, Hora ASC;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codCofre);
            consulta.setString(codPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                movimentacaoGeralFuncionario.getPessoa().setCodigo(consulta.getString("idUsuario"));
                movimentacaoGeralFuncionario.getPessoa().setNome(consulta.getString("NomeUsuario"));

                movimentacaoGeralFuncionario.setValorColetas(consulta.getString("ValorColetas"));
                movimentacaoGeralFuncionario.setValorDepositos(consulta.getString("ValorDepositos"));

                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                movimentacaoGeralFuncionario.getMovimentacoes().add(tescofremov);

            }
            consulta.close();
            return movimentacaoGeralFuncionario;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.movimentacaoIndividual - " + e.getMessage() + "\r\n"
                    + "SELECT (SELECT ISNULL(sum(ValorDeposito),0)\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'DINHEIRO'\n"
                    + "GROUP BY t.idUsuario) ValorDeposito,\n"
                    + "\n"
                    + "(SELECT sum(ISNULL(ValorDeposito,0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'COLETA'\n"
                    + "GROUP BY t.idUsuario) ValorColeta,\n"
                    + "\n"
                    + " TesCofresMov.id, TesCofresMov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora,\n"
                    + "    TesCofresMov.CodCliente, TesCofresMov.idUsuario,  Pessoa.Nome NomeUsuario, TesCofresMov.ValorDeposito,\n"
                    + "     TesCofresMov.TipoMoeda, TesCofresMov.TipoDeposito, TesCofresMov.CodigoBarras, TesCofresMov.Status,\n"
                    + "     Pessoa.Nome Operador, TesCofresMov.Dt_Incl, TesCofresMov.Hr_Incl\n"
                    + "    FROM TesCofresMov\n"
                    + "    INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                    + "   WHERE TesCofresMov.Data =  " + data + " and TesCofresMov.codCofre = " + codCofre
                    + " and TesCofresMov.idUsuario = " + codPessoa + "\n"
                    + "    ORDER BY Pessoa.Nome ASC, Data DESC, Hora ASC;\n");
        }
    }

    public MovimentacaoGeralFuncionario movimentacaoIndividual(String data, String codCofre, String codPessoa, String nomeUsuario, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            MovimentacaoGeralFuncionario movimentacaoGeralFuncionario = new MovimentacaoGeralFuncionario();
            TesCofresMov tescofremov;

            if (null != codPessoa && !codPessoa.equals("")) {
                sql = "SELECT (SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                        + "FROM TesCofresMov t\n"
                        + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                        + "and t.idUsuario = TesCofresMov.idUsuario \n"
                        + "and t.TipoDeposito = 'DINHEIRO'\n"
                        + "GROUP BY t.idUsuario) ValorDepositos,\n"
                        + "\n"
                        + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                        + "FROM TesCofresMov t\n"
                        + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                        + "and t.idUsuario = TesCofresMov.idUsuario \n"
                        + "and t.TipoDeposito = 'COLETA'\n"
                        + "GROUP BY t.idUsuario) ValorColetas,\n"
                        + "\n"
                        + " TesCofresMov.id, TesCofresMov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora,\n"
                        + "    TesCofresMov.CodCliente, TesCofresMov.idUsuario,  Pessoa.Nome NomeUsuario, TesCofresMov.ValorDeposito,\n"
                        + "     TesCofresMov.TipoMoeda, TesCofresMov.TipoDeposito, TesCofresMov.CodigoBarras, TesCofresMov.Status,\n"
                        + "     Pessoa.Nome Operador, TesCofresMov.Dt_Incl, TesCofresMov.Hr_Incl\n"
                        + "    FROM TesCofresMov\n"
                        + "    INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                        + "   WHERE TesCofresMov.Data =  ? and TesCofresMov.codCofre = ? and TesCofresMov.idUsuario = ? \n"
                        + "    ORDER BY Pessoa.Nome ASC, Data DESC, Hora ASC;\n";
            } else {
                sql = "SELECT (SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                        + "FROM TesCofresMov t\n"
                        + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                        + "and t.NomeUsuario = TesCofresMov.NomeUsuario \n"
                        + "and t.TipoDeposito = 'DINHEIRO'\n"
                        + "GROUP BY t.NomeUsuario) ValorDepositos,\n"
                        + "\n"
                        + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                        + "FROM TesCofresMov t\n"
                        + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                        + "and t.NomeUsuario = TesCofresMov.NomeUsuario \n"
                        + "and t.TipoDeposito = 'COLETA'\n"
                        + "GROUP BY t.NomeUsuario) ValorColetas,\n"
                        + "\n"
                        + " TesCofresMov.id, TesCofresMov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora,\n"
                        + "    TesCofresMov.CodCliente, TesCofresMov.idUsuario,  TesCofresMov.NomeUsuario NomeUsuario, TesCofresMov.ValorDeposito,\n"
                        + "     TesCofresMov.TipoMoeda, TesCofresMov.TipoDeposito, TesCofresMov.CodigoBarras, TesCofresMov.Status,\n"
                        + "     TesCofresMov.NomeUsuario Operador, TesCofresMov.Dt_Incl, TesCofresMov.Hr_Incl\n"
                        + "    FROM TesCofresMov\n"
                        + "   WHERE TesCofresMov.Data =  ? and TesCofresMov.codCofre = ? and REPLACE(TesCofresMov.NomeUsuario,'_','') = ? \n"
                        + "    ORDER BY TesCofresMov.NomeUsuario ASC, Data DESC, Hora ASC;\n";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codCofre);
            if (null != codPessoa && !codPessoa.equals("")) {
                consulta.setString(codPessoa);
            } else {
                consulta.setString(nomeUsuario);
            }
            consulta.select();
            while (consulta.Proximo()) {
                movimentacaoGeralFuncionario.getPessoa().setCodigo(consulta.getString("idUsuario"));
                movimentacaoGeralFuncionario.getPessoa().setNome(consulta.getString("NomeUsuario").replace("_", ""));

                movimentacaoGeralFuncionario.setValorColetas(consulta.getString("ValorColetas"));
                movimentacaoGeralFuncionario.setValorDepositos(consulta.getString("ValorDepositos"));

                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                movimentacaoGeralFuncionario.getMovimentacoes().add(tescofremov);

            }
            consulta.close();
            return movimentacaoGeralFuncionario;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.movimentacaoIndividual - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<MovimentacaoGeralFuncionario> movimentacaoGeralFuncionarios(String data, String codCofre, Persistencia persistencia) throws Exception {
        try {
            List<MovimentacaoGeralFuncionario> retorno = new ArrayList<>();
            MovimentacaoGeralFuncionario movimentacaoGeralFuncionario;
            TesCofresMov tescofremov;
            int indicePessoa;

            String sql = "SELECT (SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'DINHEIRO'\n"
                    + "GROUP BY t.NomeUsuario) ValorDepositos,\n"
                    + "\n"
                    + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'COLETA'\n"
                    + "GROUP BY t.NomeUsuario) ValorColetas,\n"
                    + "\n"
                    + "(SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'DINHEIRO') TotalDeposito,\n"
                    + "\n"
                    + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'COLETA') TotalColeta,\n"
                    + "\n"
                    + " TesCofresMov.id, TesCofresMov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora,\n"
                    + "    TesCofresMov.CodCliente, TesCofresMov.idUsuario,  REPLACE(TesCofresMov.NomeUsuario, '_','') NomeUsuario, TesCofresMov.ValorDeposito,\n"
                    + "     TesCofresMov.TipoMoeda, TesCofresMov.TipoDeposito, TesCofresMov.CodigoBarras, TesCofresMov.Status,\n"
                    + "     REPLACE(TesCofresMov.NomeUsuario, '_','') Operador, TesCofresMov.Dt_Incl, TesCofresMov.Hr_Incl\n"
                    + "    FROM TesCofresMov\n"
                    //+ "    INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                    + "   WHERE TesCofresMov.Data =  ? and TesCofresMov.codCofre = ? AND TesCofresMov.NomeUsuario <> 'Aut-Satellite'\n"
                    + "    ORDER BY TesCofresMov.NomeUsuario ASC, Data DESC, Hora ASC;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codCofre);
            consulta.select();
            while (consulta.Proximo()) {
                movimentacaoGeralFuncionario = new MovimentacaoGeralFuncionario();
                movimentacaoGeralFuncionario.getPessoa().setCodigo(consulta.getString("idUsuario"));
                movimentacaoGeralFuncionario.getPessoa().setNome(consulta.getString("NomeUsuario"));

                movimentacaoGeralFuncionario.setValorColetas(consulta.getString("ValorColetas"));
                movimentacaoGeralFuncionario.setValorDepositos(consulta.getString("ValorDepositos"));
                movimentacaoGeralFuncionario.setTotalColetas(consulta.getString("TotalColeta"));
                movimentacaoGeralFuncionario.setTotalDepositos(consulta.getString("TotalDeposito"));

                tescofremov = new TesCofresMov();
                tescofremov.setId(consulta.getBigDecimal("id"));
                tescofremov.setCodCofre(consulta.getBigDecimal("CodCofre"));
                tescofremov.setData(consulta.getLocalDate("Data"));
                tescofremov.setHora(consulta.getString("Hora"));
                tescofremov.setCodCliente(consulta.getString("CodCliente"));
                tescofremov.setIdUsuario(consulta.getString("idUsuario"));
                tescofremov.setNomeUsuario(consulta.getString("NomeUsuario"));
                tescofremov.setValorDeposito(consulta.getBigDecimal("ValorDeposito"));
                tescofremov.setTipoMoeda(consulta.getString("TipoMoeda"));
                tescofremov.setTipoDeposito(consulta.getString("TipoDeposito"));
                tescofremov.setCodigoBarras(consulta.getString("CodigoBarras"));
                tescofremov.setStatus(consulta.getString("Status"));
                tescofremov.setOperador(consulta.getString("Operador"));
                tescofremov.setDt_Incl(consulta.getLocalDate("Dt_Incl"));
                tescofremov.setHr_Incl(consulta.getString("Hr_Incl"));

                indicePessoa = retorno.indexOf(movimentacaoGeralFuncionario);
                if (indicePessoa >= 0) {
                    retorno.get(indicePessoa).getMovimentacoes().add(tescofremov);
                } else {
                    movimentacaoGeralFuncionario.getMovimentacoes().add(tescofremov);
                    retorno.add(movimentacaoGeralFuncionario);
                }
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TesCofresMovDao.movimentacaoGeralFuncionarios - " + e.getMessage() + "\r\n"
                    + "SELECT (SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'DINHEIRO'\n"
                    + "GROUP BY t.idUsuario) ValorDeposito,\n"
                    + "\n"
                    + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.idUsuario = TesCofresMov.idUsuario \n"
                    + "and t.TipoDeposito = 'COLETA'\n"
                    + "GROUP BY t.idUsuario) ValorColeta,\n"
                    + "\n"
                    + "(SELECT ISNULL(sum(ROUND(CAST (ValorDeposito AS decimal (12,2)),2)),0)\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'DINHEIRO') TotalDeposito,\n"
                    + "\n"
                    + "(SELECT sum(ISNULL(ROUND(CAST (ValorDeposito AS decimal (12,2)),2),0))\n"
                    + "FROM TesCofresMov t\n"
                    + " WHERE t.Data = TesCofresMov.Data and t.codCofre = TesCofresMov.codCofre \n"
                    + "and t.TipoDeposito = 'COLETA') TotalColeta,\n"
                    + "\n"
                    + " TesCofresMov.id, TesCofresMov.CodCofre, TesCofresMov.Data, TesCofresMov.Hora,\n"
                    + "    TesCofresMov.CodCliente, TesCofresMov.idUsuario,  Pessoa.Nome NomeUsuario, TesCofresMov.ValorDeposito,\n"
                    + "     TesCofresMov.TipoMoeda, TesCofresMov.TipoDeposito, TesCofresMov.CodigoBarras, TesCofresMov.Status,\n"
                    + "     Pessoa.Nome Operador, TesCofresMov.Dt_Incl, TesCofresMov.Hr_Incl\n"
                    + "    FROM TesCofresMov\n"
                    + "    INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                    + "   WHERE TesCofresMov.Data =  " + data + " and TesCofresMov.codCofre = " + codCofre + "\n"
                    + "    ORDER BY Pessoa.Nome ASC, Data DESC, Hora ASC;\n");
        }
    }
}

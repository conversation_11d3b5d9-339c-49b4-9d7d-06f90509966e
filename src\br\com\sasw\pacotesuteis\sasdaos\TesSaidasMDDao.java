/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.TesSaidasMD;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TesSaidasMDDao {

    /**
     * Lista composicoes
     *
     * @param guia
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TesSaidasMD> listaComposicoes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<TesSaidasMD> composicaoMD = new ArrayList<>();
        try {
            String sql;
            sql = "Select codigo, qtde from TesSaidasMD"
                    + " Where Guia = ?"
                    + "  and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            TesSaidasMD tesentmd;
            while (consult.Proximo()) {
                tesentmd = new TesSaidasMD();
                tesentmd.setCodigo(consult.getString("codigo"));
                tesentmd.setQtde(consult.getString("qtde"));
                composicaoMD.add(tesentmd);
            }
            consult.Close();
            return composicaoMD;
        } catch (Exception e) {
            throw new Exception("TesSaidasMDDao.ListaComposicoes - " + e.getMessage() + "\r\n"
                    + "elect codigo, qtde from TesSaidasMD"
                    + " Where Guia = " + guia
                    + "  and Serie = " + serie);
        }
    }

    /**
     * Insere uma nova entrada na tabela TesSaidasMD
     *
     * @param tesSaidasMD
     * @param persistencia
     * @throws Exception
     */
    public void inserirTesSaidasMD(TesSaidasMD tesSaidasMD, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO TesSaidasMD values (?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tesSaidasMD.getGuia());
            consulta.setString(tesSaidasMD.getSerie());
            consulta.setString(tesSaidasMD.getCodigo());
            consulta.setString(tesSaidasMD.getDocto());
            consulta.setString(tesSaidasMD.getQtde());
            consulta.setString(tesSaidasMD.getValor());
            consulta.setString(tesSaidasMD.getOperador());
            consulta.setString(tesSaidasMD.getDt_Alter());
            consulta.setString(tesSaidasMD.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidasMDDao.inserirTesSaidasMD - " + e.getMessage() + "\r\n"
                    + " INSERT INTO TesSaidasMD values (" + tesSaidasMD.getGuia() + "," + tesSaidasMD.getSerie() + ", \n"
                    + tesSaidasMD.getCodigo() + "," + tesSaidasMD.getDocto() + "," + tesSaidasMD.getQtde() + "," + tesSaidasMD.getValor() + ", \n"
                    + tesSaidasMD.getOperador() + "," + tesSaidasMD.getDt_Alter() + ",\n"
                    + tesSaidasMD.getHr_Alter() + ") ");
        }
    }

    public void excluirComposicao(String guia, String serie, String docto, String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "delete from TesSaidasMD "
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and codigo = ?"
                    + " and docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codigo);
            consulta.setString(docto);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TesSaidasMDDao.excluirComposicao - " + e.getMessage() + "\r\n"
                    + "delete from TesSaidasMD "
                    + " where Guia = " + guia
                    + " and Serie =  " + serie
                    + " and codigo = " + codigo
                    + " and docto = " + docto);
        }
    }
}

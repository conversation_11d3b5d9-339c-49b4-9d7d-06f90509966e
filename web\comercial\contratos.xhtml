<?xml version="1.0" encoding="UTF-8"?>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        min-width:100% !important;
                        width:100% !important;
                        max-width:100% !important;
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body{
                        height: calc(100% - 6.5em);
                    }
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td {
                        min-width: 100px;
                        max-width: 100px;
                        white-space: normal !important;
                        overflow-wrap: break-word !important;
                        word-wrap: break-word !important;
                        -webkit-hyphens: auto !important;
                        -ms-hyphens: auto !important;
                        hyphens: auto !important;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2),
                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3){
                        min-width: 90px;
                        max-width: 90px;
                    }

                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4),
                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5),
                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7),
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8),
                    .DataGrid thead tr th:nth-child(18),
                    .DataGrid tbody tr td:nth-child(18),
                    .DataGrid thead tr th:nth-child(22),
                    .DataGrid tbody tr td:nth-child(22),
                    .DataGrid thead tr th:nth-child(24),
                    .DataGrid tbody tr td:nth-child(24) {
                        min-width: 200px;
                        max-width: 200px;
                    }

                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9),
                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10),
                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11),
                    .DataGrid thead tr th:nth-child(13),
                    .DataGrid tbody tr td:nth-child(13),
                    .DataGrid thead tr th:nth-child(17),
                    .DataGrid tbody tr td:nth-child(17),
                    .DataGrid thead tr th:nth-child(19),
                    .DataGrid tbody tr td:nth-child(19),
                    .DataGrid thead tr th:nth-child(21),
                    .DataGrid tbody tr td:nth-child(21),
                    .DataGrid thead tr th:nth-child(23),
                    .DataGrid tbody tr td:nth-child(23),
                    .foo{
                        min-width: 150px;
                        max-width: 150px;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }

                    [id*="tabelaSubContratos"] thead tr th,
                    [id*="tabelaSubContratos"] tbody tr td,
                    [id*="tabelaReajustes"] thead tr th,
                    [id*="tabelaReajustes"] tbody tr td{
                        min-width: 150px !important;
                        width: 150px !important;
                        max-width: 150px !important;
                    }

                    [id*="tabelaSubContratos"] thead tr th:nth-child(2),
                    [id*="tabelaSubContratos"] tbody tr td:nth-child(2){
                        min-width: 300px;
                        width: 300px;
                        max-width: 300px;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formContrato .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formContrato .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                .contratovencidoRow, .DataGrid tbody tr.contratovencidoRow:hover td {
                    color: red !important;
                }

                .tabela .ui-datatable-scrollable-body{
                    height: calc(100% - 9em);
                }

                .botoesDataTable {
                    width: 40px;
                    margin-top: 8px;
                    position: absolute;
                    right: -14px; top: 50%;
                    transform: translateY(-50%);
                }

                .infoSecundaria {
                    color: gray;
                }

                .semPaddingLateral {
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }

                [id*="tabGeral"],
                [id*="tabDocumentos"]{
                    background-color:#FFF !important;
                    padding-right: 20px!important;
                }

                [id*="formItensContrato"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formContrato"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formCadastroGrupoPagamento"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formSubContrato"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formPesquisar"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

            </style>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{contratos.Persistencias(login.pp)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;"
                                     >
                                    <img src="../assets/img/icone_satmob_contratos_G.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Contratos}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span>
                                            <h:outputText
                                                id="dataDia"
                                                value="#{contratos.dataTela}"
                                                converter="conversorDia"
                                                />
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial"
                                     class="col-md-5 col-sm-12 col-xs-12"
                                     style="text-align: center !important;"
                                     >
                                    <div style="float:left;">
                                        <label class="FilialNome">
                                            #{contratos.filiais.descricao}
                                            <label id="btTrocarFilial"
                                                   onclick="top.location.href = '../param.xhtml'"
                                                   >
                                                #{localemsgs.TrocarFilial}
                                            </label>
                                        </label>

                                        <label class="FilialEndereco">
                                            #{contratos.filiais.endereco}
                                        </label>

                                        <label class="FilialBairroCidade">
                                            #{contratos.filiais.bairro}, #{contratos.filiais.cidade}/#{contratos.filiais.UF}
                                        </label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar"
                                     class="col-md-2 col-sm-2 col-xs-3"
                                     style="padding:0px 10px 0px 0px !important; text-align: right !important"
                                     >
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();"
                                                   action="#"
                                                   >
                                        <p:graphicImage
                                            url="../assets/img/icone_voltar_branco.png"
                                            height="40"
                                            />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <!--Tabela contratos-->
                <h:form id="main">
                    <p:hotkey bind="a" actionListener="#{contratos.preCadastroContrato}" update="msgs formContrato"/>
                    <p:hotkey bind="p" actionListener="#{contratos.prePesquisaContrato}" update="formPesquisar msgs"/>
                    <p:hotkey bind="e" actionListener="#{contratos.preEdicaoContrato}" update="msgs formContrato"/>

                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline;">
                                    <p:dataTable
                                        id="tabela"
                                        value="#{contratos.allContratos}"
                                        selection="#{contratos.contratoSelecionado}"
                                        rowStyleClass="#{lista.vencido ? 'contratovencidoRow' : null}"
                                        currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Contratos}"
                                        var="lista"
                                        lazy="true"
                                        selectionMode="single"
                                        emptyMessage="#{localemsgs.SemRegistros}"
                                        paginator="true"
                                        rows="15"
                                        reflow="true"
                                        rowsPerPageTemplate="5,10,15, 20, 25"
                                        paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                        styleClass="tabela"
                                        scrollable="true"
                                        
                                        class="tabela DataGrid"
                                        style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                        >
                                        <p:ajax event="rowDblselect" listener="#{contratos.preEdicaoContrato}" update="formContrato msgs"/>
                                        <p:column headerText="#{localemsgs.Contrato}">
                                            <h:outputText value="#{lista.contrato}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CodFil}" class="text-center">
                                            <h:outputText value="#{lista.codFil}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Tipo}" class="text-center">
                                            <h:outputText value="#{lista.tipo}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRed}">
                                            <h:outputText value="#{lista.NRed}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Descricao}">
                                            <h:outputText value="#{lista.descricao}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Situacao}">
                                            <h:outputText value="#{lista.situacaoDesc}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Identif}">
                                            <h:outputText value="#{lista.identif}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Nome}">
                                            <h:outputText value="#{lista.nome}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.GrpReajuste}" class="text-center">
                                            <h:outputText value="#{lista.grpReajuste}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.GrpPagamento}" class="text-center">
                                            <h:outputText value="#{lista.grpPagamento}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ContratoCli}" class="text-center">
                                            <h:outputText value="#{lista.contratoCli}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RefArq}" class="text-center">
                                            <h:outputText value="#{lista.refArq}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Processo}">
                                            <h:outputText value="#{lista.processo}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Validade}" class="text-center">
                                            <h:outputText value="#{lista.validade}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Inicio}">
                                            <h:outputText value="#{lista.dt_Inicio}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Termino}">
                                            <h:outputText value="#{lista.dt_Termino}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Assinado}"  class="text-center">
                                            <h:outputText value="#{lista.assinatura}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Endereco}">
                                            <h:outputText value="#{lista.endereco}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Cidade}">
                                            <h:outputText value="#{lista.cidade}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Estado}">
                                            <h:outputText value="#{lista.estado}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Fone1}">
                                            <h:outputText value="#{lista.fone1}" converter="conversorFone"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Email}">
                                            <h:outputText value="#{lista.email}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Contato}">
                                            <h:outputText value="#{lista.contato}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CNPJCPF}">
                                            <h:outputText value="#{lista.CNPJ}" converter="conversorCNPJ"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.CliFat}" rendered="false">
                                            <h:outputText value="#{lista.cliFat}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.OBS}" rendered="false">
                                            <h:outputText value="#{lista.OBS}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.OperIncl}" rendered="false">
                                            <h:outputText value="#{lista.operIncl}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Incl}" rendered="false">
                                            <h:outputText value="#{lista.dt_Incl}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Incl}" rendered="false">>
                                            <h:outputText value="#{lista.hr_Incl}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Operador}">
                                            <h:outputText value="#{lista.operador}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Dt_Alter}">
                                            <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Hr_Alter}">
                                            <h:outputText value="#{lista.hr_Alter}" converter="conversorHora"/>
                                        </p:column>
                                    </p:dataTable>

                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Adicionar}" actionListener="#{contratos.preCadastroContrato}"
                                           update="msgs formContrato" >
                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                            </p:commandLink>
                        </div>
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Editar}" actionListener="#{contratos.preEdicaoContrato}"
                                           update="msgs formContrato">
                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{contratos.prePesquisaContrato}"
                                           update="formPesquisar msgs">
                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{contratos.limparFiltros}"
                                           update=":main:tabela :msgs :cabecalho">
                                <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Exportar}" action="#{exportarMB.setTitulo(localemsgs.Contratos)}"
                                           oncomplete="PF('dlgExportar').show();"
                                           rendered="false">
                                <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </p:panel>
                </h:form>

                <!-- Cadastrar novo / editar contrato -->
                <h:form id="formContrato" class="form-inline">
                    <p:dialog
                        widgetVar="dlgCadastrar"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formContrato\\:fecharFormContrato").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton
                            id="fecharFormContrato"
                            style="display: none"
                            oncomplete="PF('dlgCadastrar').hide()">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_contratos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarContrato}" style="color:#022a48" />
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <div class="col-md-9 col-sm-9 col-xs-12" style="padding: 0px 4px 0px 0px">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"  />
                                <p:selectOneMenu
                                    id="codfil"
                                    value="#{contratos.cadastroFilial}"
                                    converter="omnifaces.SelectItemsConverter"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                    styleClass="filial"
                                    style="width: 100%"
                                    filter="true"
                                    filterMatchMode="contains" >
                                    <p:ajax event="itemSelect" update="formContrato:cadastrar"/>
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>

                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding: 0px 0px 0px 4px">
                                <p:outputLabel for="contrato" value="#{localemsgs.Contrato}"/>
                                <p:inputText id="contrato"
                                             value="#{contratos.contratoCadastro.contrato}"
                                             disabled="true"
                                             style="width: 100%">
                                    <f:validateLength minimum="3" maximum="10" />
                                </p:inputText>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 6px 4px 0px 0px">
                                <p:outputLabel for="IdentificacaoBanco"
                                               value="#{localemsgs.IdentificacaoBanco}"/>
                                <p:selectOneMenu id="IdentificacaoBanco"
                                                 value="#{contratos.identificacaoBanco}"
                                                 converter="omnifaces.SelectItemsConverter"
                                                 styleClass="filial"
                                                 style="width: 100%"
                                                 filter="true"
                                                 filterMatchMode="contains" >
                                    <f:selectItem itemValue="#{null}"
                                                  itemLabel="#{contratos.contratoCadastro.contrato.substring(0, Math.min(3, contratos.contratoCadastro.contrato.length()))}"
                                                  noSelectionOption="true" />
                                    <f:selectItems value="#{contratos.listaBancos}"
                                                   var="identificacaoCliente"
                                                   itemValue="#{identificacaoCliente}"
                                                   itemLabel="#{identificacaoCliente.banco} - #{identificacaoCliente.NRed}"
                                                   noSelectionValue=""/>
                                    <p:ajax event="itemSelect"
                                            listener="#{contratos.calcularIdContrato()}"
                                            update="msgs formContrato:contrato"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-6" style="padding: 6px 0px 0px 4px">
                                <p:outputLabel for="tipoContrato" value="#{localemsgs.TipoContrato}"  />
                                <p:selectOneMenu
                                    value="#{contratos.contratoCadastro.tipo}"
                                    id="tipoContrato"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoContrato}"
                                    style="width: 100%">
                                    <f:selectItem
                                        itemValue="#{null}"
                                        itemLabel="#{localemsgs.Selecione}"
                                        noSelectionOption="true" />
                                    <f:selectItem itemLabel="#{localemsgs.Transporte}" itemValue="T"/>
                                    <f:selectItem itemLabel="#{localemsgs.Outros}" itemValue="O"/>
                                    <p:ajax event="itemSelect"
                                            listener="#{contratos.calcularIdContrato()}"
                                            update="msgs formContrato:contrato"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-#{contratos.contratoCadastro.validade == 'D' ? '4' : '6'} col-sm-#{contratos.contratoCadastro.validade == 'D' ? '4' : '6'} col-xs-12"  style="padding: 8px 4px 0px 0px">
                                <p:outputLabel for="validade" value="#{localemsgs.Validade}"  />
                                <p:selectOneMenu
                                    id="validade"
                                    value="#{contratos.contratoCadastro.validade}"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Validade}"
                                    style="width: 100%;">
                                    <p:ajax event="itemSelect" update="formContrato:panelDataTermino msgs"/>
                                    <f:selectItem itemLabel="#{localemsgs.Determinado}" itemValue="D"/>
                                    <f:selectItem itemLabel="#{localemsgs.Indeterminado}" itemValue="I"/>
                                </p:selectOneMenu>
                            </div>

                            <div class="col-md-#{contratos.contratoCadastro.validade == 'D' ? '4' : '6'} col-sm-#{contratos.contratoCadastro.validade == 'D' ? '4' : '6'} col-xs-12"  style="padding: 8px 0px 0px 4px">
                                <p:outputLabel for="dataInicio" value="#{localemsgs.DtInicio}" indicateRequired="false"/>
                                <p:inputMask id="dataInicio" value="#{contratos.contratoCadastro.dt_Inicio}"
                                             mask="99/99/9999" required="true" label="#{localemsgs.Data}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtInicio}"
                                             style="width: 100%" maxlength="8" placeholder="#{mascaras.padraoData}"
                                             converter="conversorData"/>
                            </div>

                            <p:panel id="panelDataTermino" class="col-md-4 col-sm-4 col-xs-12" style="display: #{contratos.contratoCadastro.validade == 'D' ? '' : 'none'};padding: 8px 0px 0px 4px">
                                <p:outputLabel
                                    id="labelDataTermino"
                                    for="dataTermino"
                                    value="#{localemsgs.Dt_Termino}"
                                    indicateRequired="true"
                                    rendered="#{contratos.contratoCadastro.validade == 'D'}"/>
                                <p:inputMask
                                    id="dataTermino"
                                    value="#{contratos.contratoCadastro.dt_Termino}"
                                    mask="99/99/9999"
                                    label="#{localemsgs.Data}"
                                    required="#{contratos.contratoCadastro.validade == 'D'}"
                                    style="width: 100%"
                                    maxlength="8"
                                    placeholder="#{mascaras.padraoData}"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Dt_Termino}"
                                    converter="conversorData"
                                    rendered="#{contratos.contratoCadastro.validade == 'D'}"/>
                            </p:panel>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px 0px 0px 0px !important; margin: 0px !important">
                                <div class="col-md-4 col-sm-4 col-xs-5" style="padding: 0px !Important">
                                    <p:outputLabel for="clienteContratante"
                                                   value="#{localemsgs.ClienteContratante}"
                                                   indicateRequired="false"/>
                                    <p:autoComplete
                                        id="clienteContratante"
                                        value="#{contratos.clienteContrato}"
                                        completeMethod="#{contratos.buscarClientes}"
                                        label="#{localemsgs.ClienteContratante}"
                                        forceSelection="true"
                                        styleClass="cliente"
                                        style="min-width: 100% !important;width: 100% !important;max-width: 100% !important;"
                                        minQueryLength="3"
                                        requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.ClienteContratante}"
                                        scrollHeight="200"
                                        var="cont"
                                        itemValue="#{cont}"
                                        itemLabel="#{cont.NRed}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{contratos.listaClientes}" />
                                        <p:ajax event="itemSelect"
                                                update="formContrato:infoCliente"/>
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-8 col-sm-8 col-xs-7" style="text-align: left !important; padding-top: 14px">
                                    <p:outputPanel id="infoCliente"
                                                   class="col-md-12 col-sm-12 col-xs-12" style="text-align: left !important;">
                                        <p:outputPanel rendered="#{contratos.clienteContrato != null}">
                                            <div class="col-md-12 col-sm-12 col-xs-12 semPaddingLateral" style="text-align: left !important;">
                                                <h:outputText
                                                    id="clienteContratanteNome"
                                                    value="#{contratos.clienteContrato.codigo} - #{contratos.clienteContrato.nome}"/>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12 semPaddingLateral infoSecundaria" style="text-align: left !important;">
                                                <h:outputText id="clienteSubContratanteEndereco"
                                                              value="#{contratos.clienteContrato.ende} #{contratos.clienteContrato.bairro} #{contratos.clienteContrato.cidade} #{contratos.clienteContrato.estado}"
                                                              />
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12 semPaddingLateral infoSecundaria" style="text-align: left !important;">
                                                <h:outputText id="clienteSubContratanteContato1"
                                                              value="#{contratos.clienteContrato.fone1}"
                                                              converter="conversorFone"/>
                                                <h:outputText id="clienteSubContratanteContato2"
                                                              rendered="#{contratos.clienteContrato.fone2}"
                                                              value=" - #{contratos.clienteContrato.fone2}"
                                                              converter="conversorFone"/>
                                            </div>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="position: relative; padding: 6px 4px 0px 0px !important">
                                <p:outputLabel for="descricao"
                                               value="#{localemsgs.Descricao}"
                                               indicateRequired="false"/>

                                <p:selectOneMenu
                                    id="descricao"
                                    value="#{contratos.contratoCadastro.descricao}"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoContrato}"
                                    styleClass="filial"
                                    style="width: 100%"
                                    editable="true"
                                    filter="true"
                                    filterMatchMode="contains" >
                                    <f:selectItems value="#{contratos.descricoes}"/>
                                </p:selectOneMenu>

                                <div class="col-md-1 col-sm-1 col-xs-1 semPaddingLateral" style="position: absolute; right: 30px; top: 26px">
                                    <p:commandLink
                                        actionListener="#{contratos.preCadastroDescricaoServicos()}"
                                        update="msgs formCadastroGrupoPagamento"
                                        partialSubmit="true"
                                        process="@this"
                                        title="#{localemsgs.CadastrarGrupos}">
                                        <p:graphicImage
                                            url="../assets/img/icone_redondo_adicionar.png"
                                            width="30" height="30" />
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding: 6px 0px 0px 4px !important">

                                <p:outputLabel
                                    for="identif"
                                    value="#{localemsgs.Identificacao}"
                                    indicateRequired="false"/>
                                <p:inputText
                                    id="identif"
                                    value="#{contratos.contratoCadastro.identif}"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Identificacao}"
                                    style="width: 100%"/>
                            </div>
                            <div class="clearfix" />
                            <p:tabView
                                id="tabs"
                                activeIndex="0"
                                onTabShow="PF('dlgCadastrar').initPosition()"
                                dynamic="true"
                                style="background-color:#EEE !important;">
                                <!--Dados gerais-->
                                <p:tab id="tabGeral" title="#{localemsgs.Geral}">
                                    <p:panelGrid
                                        columns="4"
                                        columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                        layout="grid"
                                        styleClass="ui-panelgrid-blank"
                                        >
                                        <p:outputLabel for="grpReajuste" value="#{localemsgs.GrpReajuste}: " indicateRequired="false"/>
                                        <p:selectOneMenu
                                            id="grpReajuste"
                                            value="#{contratos.grupoReajuste}"
                                            required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.GrpReajuste}"
                                            converter="omnifaces.SelectItemsConverter"
                                            styleClass="filial"
                                            style="width: 100%"
                                            filter="true"
                                            filterMatchMode="contains">
                                            <f:selectItem
                                                itemValue="#{null}"
                                                itemLabel="0"
                                                noSelectionOption="true" />
                                            <f:selectItems
                                                value="#{contratos.gruposReajuste}"
                                                var="grupoReajuste"
                                                itemValue="#{grupoReajuste}"
                                                itemLabel="#{grupoReajuste.codigo} - #{grupoReajuste.descricao}"
                                                noSelectionValue=""/>
                                        </p:selectOneMenu>

                                        <p:outputLabel for="refArq" value="#{localemsgs.RefArq}: " indicateRequired="false"/>
                                        <p:inputText id="refArq" value="#{contratos.contratoCadastro.refArq}" style="width: 100%"/>

                                        <p:outputLabel
                                            for="grpPagamento"
                                            value="#{localemsgs.GrpPagamento}: "
                                            indicateRequired="false"/>
                                        <p:selectOneMenu
                                            id="grpPagamento"
                                            value="#{contratos.grupoPagamento}"
                                            required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.GrpPagamento}"
                                            converter="omnifaces.SelectItemsConverter"
                                            styleClass="filial"
                                            style="width: 100%"
                                            filter="true"
                                            filterMatchMode="contains">
                                            <f:selectItem
                                                itemValue="#{null}"
                                                itemLabel="#{localemsgs.Selecione}"
                                                noSelectionOption="true" />
                                            <f:selectItems
                                                value="#{contratos.gruposPagamento}"
                                                var="grupoPagamento"
                                                itemValue="#{grupoPagamento}"
                                                itemLabel="#{grupoPagamento.codigo} - #{grupoPagamento.descricao}"
                                                noSelectionValue=""/>
                                        </p:selectOneMenu>

                                        <p:commandLink
                                            actionListener="#{contratos.preCadastroGrupoPagamento()}"
                                            update="msgs formCadastroGrupoPagamento"
                                            partialSubmit="true"
                                            process="@this"
                                            title="#{localemsgs.CadastrarGrupos}">
                                            <p:graphicImage
                                                url="../assets/img/icone_redondo_adicionar.png"
                                                width="30" height="30" />
                                        </p:commandLink>
                                    </p:panelGrid>

                                    <p:panelGrid columns="6"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-2"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="contratoCli" value="#{localemsgs.ContratoCli}: " indicateRequired="false"/>
                                        <p:inputText id="contratoCli" value="#{contratos.contratoCadastro.contratoCli}" style="width: 100%"/>

                                        <p:outputLabel for="assinatura" value="#{localemsgs.ContratoAssinado}: " indicateRequired="false"/>
                                        <p:inputText id="assinatura" value="#{contratos.contratoCadastro.assinatura}" style="width: 100%"/>

                                        <p:outputLabel for="processo" value="#{localemsgs.Processo}: " indicateRequired="false"/>
                                        <p:inputText id="processo" value="#{contratos.contratoCadastro.processo}" style="width: 100%"/>

                                        <p:outputLabel for="situacao" value="#{localemsgs.Situacao}: " indicateRequired="false"/>
                                        <p:selectOneMenu id="situacao" value="#{contratos.contratoCadastro.situacao}"
                                                         style="width: 100%">
                                            <f:selectItem itemLabel="#{localemsgs.Ativo}" itemValue="A"/>
                                            <f:selectItem itemLabel="#{localemsgs.Cancelado}" itemValue="C"/>
                                        </p:selectOneMenu>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="OBS" value="#{localemsgs.OBS}: " indicateRequired="false"/>
                                        <p:inputText id="OBS" value="#{contratos.contratoCadastro.OBS}" style="width: 100%"/>
                                    </p:panelGrid>
                                </p:tab>

                                <!-- Sub-contratos (anexos)-->
                                <p:tab title="#{localemsgs.SubContratosAnexos}"
                                       disabled="#{not contratos.editandoContrato}">
                                    <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important; height: 202px !important">
                                        <p:dataTable
                                            id="tabelaSubContratos"
                                            var="subcontrato"
                                            value="#{contratos.allSubContratos}"
                                            selection="#{contratos.subContratoSelecionado}"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.SubContratosAnexos}"
                                            lazy="true"
                                            selectionMode="single"
                                            emptyMessage="#{localemsgs.SemRegistros}"
                                            paginator="true"
                                            rows="15"
                                            reflow="true"
                                            rowsPerPageTemplate="5,10,15, 20, 25"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                            styleClass="tabela"
                                            scrollable="true"
                                            scrollWidth="100%"
                                            class="tabela DataGrid"
                                            style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                            >                                           
                                            <p:ajax
                                                event="rowDblselect"
                                                listener="#{contratos.preEdicaoSubContratos}"
                                                update="msgs formContrato:tabs formSubContrato"/>
                                            <p:column headerText="#{localemsgs.Contrato}">
                                                <h:outputText value="#{subcontrato.contrato}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{subcontrato.descricao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Tipo}">
                                                <h:outputText value="#{subcontrato.tipo}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Situacao}">
                                                <h:outputText value="#{subcontrato.situacao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Identif}">
                                                <h:outputText value="#{subcontrato.identif}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Inicio}">
                                                <h:outputText value="#{subcontrato.dt_Inicio}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Termino}">
                                                <h:outputText value="#{subcontrato.dt_Termino}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}">
                                                <h:outputText value="#{subcontrato.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}">
                                                <h:outputText value="#{subcontrato.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}">
                                                <h:outputText value="#{subcontrato.hr_Alter}"/>
                                            </p:column>
                                        </p:dataTable>

                                        <div class="botoesDataTable">
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                                <p:commandLink actionListener="#{contratos.preCadastroSubContrato}"
                                                               update="msgs formContrato:tabs formSubContrato"
                                                               process="@this"
                                                               title="#{localemsgs.Cadastrar}"
                                                               >
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                                <p:commandLink actionListener="#{contratos.preEdicaoSubContratos}"
                                                               update="msgs formContrato:tabs formSubContrato"
                                                               title="#{localemsgs.Editar}"
                                                               >
                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </div>
                                        </div>
                                    </p:panel>
                                </p:tab>

                                <!--Reajustes-->
                                <p:tab title="#{localemsgs.Reajustes}"
                                       disabled="#{not contratos.editandoContrato}">
                                    <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important; height: 202px !important">
                                        <p:dataTable
                                            id="tabelaReajustes"
                                            value="#{contratos.allReajustes}"
                                            selection="#{contratos.reajusteSelecionado}"
                                            selectionMode="single"
                                            paginator="true"
                                            rows="15"
                                            lazy="true"
                                            reflow="true"
                                            rowsPerPageTemplate="5,10,15, 20, 25"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Reajustes}"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                            var="reajuste"
                                            styleClass="tabela"
                                            emptyMessage="#{localemsgs.SemRegistros}"
                                            scrollable="true"
                                            scrollWidth="100%"
                                            class="tabela DataGrid"
                                            rowStyleClass="#{not reajuste.dtReaj eq null ? 'contratovencidoRow' : null}"
                                            style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                            >
                                            <p:column
                                                headerText="#{localemsgs.DtBase}">
                                                <h:outputText
                                                    value="#{reajuste.dtBase}"
                                                    converter="conversorData"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.FormulaDesc}">
                                                <h:outputText value="#{reajuste.formulaDesc}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndiceDF}">
                                                <h:outputText value="#{reajuste.indiceDF}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndiceSolic}">
                                                <h:outputText value="#{reajuste.indiceSolic}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndCombAprov}">
                                                <h:outputText value="#{reajuste.indCombAprov}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.Embarques}">
                                                <h:outputText value="#{reajuste.embarques}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.TE}">
                                                <h:outputText value="#{reajuste.TE}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.Envelopes}">
                                                <h:outputText value="#{reajuste.envelopes}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.Milheiros}">
                                                <h:outputText value="#{reajuste.milheiros}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndMOSolic}">
                                                <h:outputText value="#{reajuste.indMOSolic}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.KM}">
                                                <h:outputText value="#{reajuste.KM}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.FixoMensal}">
                                                <h:outputText value="#{reajuste.fixoMensal}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndCombSolic}">
                                                <h:outputText value="#{reajuste.indCombSolic}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndCombAprov}">
                                                <h:outputText value="#{reajuste.indCombAprov}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndDFSolic}">
                                                <h:outputText value="#{reajuste.indDFSolic}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.IndDFAprov}">
                                                <h:outputText value="#{reajuste.indDFAprov}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.Formula}">
                                                <h:outputText value="#{reajuste.formula}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.CodCarta}">
                                                <h:outputText value="#{reajuste.codCarta}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.DtCarta}">
                                                <h:outputText value="#{reajuste.dtCarta}"  converter="conversorData"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.Dt_Alter}">
                                                <h:outputText value="#{reajuste.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.Hr_Alter}">
                                                <h:outputText value="#{reajuste.hr_Alter}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.OperReaj}">
                                                <h:outputText value="#{reajuste.operReaj}"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.DtReaj}">
                                                <h:outputText value="#{reajuste.dtReaj}"  converter="conversorData"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.HrReaj}">
                                                <h:outputText value="#{reajuste.hrReaj}" converter="conversorHora"/>
                                            </p:column>
                                            <p:column
                                                headerText="#{localemsgs.AdValorem}">
                                                <h:outputText value="#{reajuste.adValorem}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </p:tab>

                                <!--Documentos-->
                                <p:tab
                                    id="tabDocumentos"
                                    title="#{localemsgs.Documentos}"
                                    disabled="#{not contratos.editandoContrato}">
                                    <div  style="text-align: justify; width: 100%; padding-bottom: 10px">
                                        <h:outputText value="#{localemsgs.ArrasteArquivo}:"/>
                                    </div>

                                    <div  style="text-align: center; width: 100%; height: 150px">
                                        <p:fileUpload id="upload" fileUploadListener="#{contratos.handleFileUpload}"
                                                      allowTypes="/(\.|\/)(pdf|jp?eg|xls|xlsx|doc|docx)$/" label="#{localemsgs.Pesquisar}" auto="true"
                                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                                      dragDropSupport="true" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                      update="msgs formContrato:tabs:arquivos" previewWidth="10" skinSimple="true">
                                            <h:outputText value="#{localemsgs.ArrasteAqui}" id="ArrasteAqui"
                                                          style="text-align: justify; color: lightgray; top: 30px; position: relative;"/>
                                        </p:fileUpload>
                                    </div>
                                    <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important">
                                        <p:dataTable
                                            id="arquivos"
                                            var="documentos"
                                            value="#{contratos.documentos}"
                                            rowKey="#{documentos.ordem} #{documentos.dtArquivo}"
                                            scrollHeight="200"
                                            scrollable="true"
                                            style="background: transparent"
                                            styleClass="tabelaArquivos"
                                            >
                                            <p:column headerText="#{localemsgs.Arquivos}" style="text-align: center">
                                                <p:commandLink actionListener="#{contratos.handleFileDownload(documentos)}" ajax="false"
                                                               value="#{documentos.descricao}" update="msgs">
                                                    <p:fileDownload value="#{contratos.download}" />
                                                </p:commandLink>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ModificadoEm}" style="width: 110px; text-align: center">
                                                <h:outputText value="#{documentos.dt_alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column style="width: 30px">
                                                <p:commandLink actionListener="#{contratos.handleFileDelete(documentos)}"
                                                               update="msgs formContrato:tabs:arquivos">
                                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirDocumento}" icon="ui-icon-alert" />
                                                    <p:graphicImage  url="../assets/img/icone_redondo_excluir.png" height="20" />
                                                </p:commandLink>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </p:tab>
                            </p:tabView>

                            <div class="form-inline">
                                <p:commandLink
                                    rendered="#{not contratos.editandoContrato}"
                                    id="cadastro"
                                    action="#{contratos.cadastrarContrato}"
                                    update=" :main :msgs :cabecalho"
                                    title="#{localemsgs.Cadastrar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink
                                    rendered="#{contratos.editandoContrato}"
                                    id="edit"
                                    action="#{contratos.verificacaoEdicaoContrato}"
                                    update=":msgs :main:tabela :cabecalho"
                                    title="#{localemsgs.Editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:blockUI block="formContrato:cadastrar" trigger="formContrato:cadastro"/>
                                <p:blockUI block="formContrato:cadastrar" trigger="formContrato:edit"/>
                            </div>
                        </p:panel>
                    </p:dialog>

                    <p:confirmDialog message="#{localemsgs.AlteracaoClienteContratante}" header="#{localemsgs.Confirmacao}"
                                     showEffect="drop" width="300" widgetVar="cdglClienteContratante"
                                     hideEffect="drop">
                        <p:commandButton value="#{localemsgs.Sim}"
                                         styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                         action="#{contratos.editarContrato}"
                                         oncomplete="PF('cdglClienteContratante').hide()"
                                         update=" :main :msgs :cabecalho"/>
                        <p:commandButton value="#{localemsgs.Nao}" action="#{acessos.cadastrar}"
                                         styleClass="ui-confirmdialog-no" icon="ui-icon-close"
                                         oncomplete="PF('cdglClienteContratante').hide()"/>
                    </p:confirmDialog>
                </h:form>

                <!--Pesquisar contratos-->
                <h:form id="formPesquisar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisar"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false" width="400"
                        style="max-height:95% !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_contratos_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.PesquisarContrato}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                        </f:facet>

                        <p:panel id="pesquisar" style="background: transparent">
                            <div class="ui-grid-row">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:selectOneMenu id="codfil" value="#{contratos.contratoPesquisa.codFil}" converter="omnifaces.SelectItemsConverter"
                                                     styleClass="filial"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true" />
                                        <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial.codfilAc}"
                                                       itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                        <p:watermark for="codfil" value="#{localemsgs.CodFil}"/>
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="contrato" value="#{localemsgs.Contrato}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="contrato" value="#{contratos.contratoPesquisa.contrato}" label="#{localemsgs.Contrato}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="contrato" value="#{localemsgs.Contrato}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="descricao" value="#{contratos.contratoPesquisa.descricao}" label="#{localemsgs.Descricao}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="identif" value="#{localemsgs.Identif}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText
                                        id="identif"
                                        value="#{contratos.contratoPesquisa.identif}"
                                        label="#{localemsgs.Identif}"
                                        style="width: 100%"
                                        maxlength="60">
                                        <p:watermark for="identif"
                                                     value="#{localemsgs.Identif}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nome" value="#{localemsgs.NomeCli}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nome" value="#{contratos.contratoPesquisa.nome}" label="#{localemsgs.NomeCli}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nome" value="#{localemsgs.NomeCli}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="padding-bottom: 3px;">
                                <div style="width: 25%; float: left">
                                    <p:outputLabel for="nred" value="#{localemsgs.NRed}: "/>
                                </div>
                                <div style="width: 75%; float: left">
                                    <p:inputText id="nred" value="#{contratos.contratoPesquisa.NRed}" label="#{localemsgs.NRed}"
                                                 style="width: 100%" maxlength="60">
                                        <p:watermark for="nred" value="#{localemsgs.NRed}"/>
                                    </p:inputText>
                                </div>
                            </div>

                            <div class="form-inline">
                                <p:commandLink id="pesquisa" action="#{contratos.pesquisarContrato}" oncomplete="PF('dlgPesquisar').hide()"
                                               update="msgs main:tabela cabecalho corporativo"
                                               title="#{localemsgs.Pesquisar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Dialog de Subcontrato-->
                <h:form id="formSubContrato" class="form-inline">
                    <p:dialog
                        widgetVar="dlgSubContrato"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        style="min-width: unset !important;"
                        styleClass="dlgCadastrar">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgSubContrato').closeIcon.unbind('click');

                                //register your own
                                PF('dlgSubContrato').closeIcon.click(function (e) {
                                    $("#formSubContrato\\:botaoFecharSubContrato").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton
                            style="display: none"
                            oncomplete="PF('dlgSubContrato').hide()"
                            id="botaoFecharSubContrato">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarContratoAnexo}" style="color:#022a48" rendered="#{not contratos.editandoSubContrato}"/>
                            <h:outputText value="#{localemsgs.EditarContratoAnexo}" style="color:#022a48" rendered="#{contratos.editandoSubContrato}"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; margin-top: 10px !Important" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px 0px 0px 0px">
                                <p:outputLabel for="tipo" value="#{localemsgs.Tipo}"/>
                                <p:selectOneMenu id="tipo"
                                                 value="#{contratos.subContratoSelecionado.tipo}"
                                                 label="#{localemsgs.Tipo}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                 required="true">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Transporte}" itemValue="T"/>
                                    <f:selectItem itemLabel="#{localemsgs.OutrosDesc}" itemValue="O"/>
                                    <p:ajax event="itemSelect" update="msgs tipo"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px 0px 0px 0px">
                                <p:outputLabel value="#{localemsgs.ClienteContratante}: "/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-5" style="padding: 6px 4px 0px 0px">
                                <p:inputText
                                    id="contratante"
                                    value="#{contratos.clienteContrato.codigo}"
                                    disabled="true"
                                    style="width: 100%;"/>
                            </div>
                            <div class="col-md-9 col-sm-9 col-xs-7" style="padding: 6px 0px 0px 8px !important">
                                <div class="col-md-12 col-sm-12 col-xs-12 semPaddingLateral">
                                    <h:outputText
                                        id="clienteContratanteNome"
                                        value="#{contratos.clienteContrato.nome}"/>
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12 semPaddingLateral infoSecundaria">
                                    <h:outputText id="clienteSubContratanteEndereco"
                                                  value="#{contratos.clienteContrato.ende} #{contratos.clienteContrato.bairro} #{contratos.clienteContrato.cidade} #{contratos.clienteContrato.estado}"
                                                  />
                                </div>
                                <div class="col-md-12 col-sm-12 col-xs-12 semPaddingLateral infoSecundaria">
                                    <h:outputText id="clienteSubContratanteContato1"
                                                  value="#{contratos.clienteContrato.fone1}"
                                                  converter="conversorFone"/>
                                    <h:outputText id="clienteSubContratanteContato2"
                                                  rendered="#{contratos.clienteContrato.fone2}"
                                                  value=" - #{contratos.clienteContrato.fone2}"
                                                  converter="conversorFone"/>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 6px 4px 0px 0px">
                                <p:outputLabel for="vigenciaDe" value="#{localemsgs.Validade}"/>
                                <p:calendar
                                    id="vigenciaDe"
                                    value="#{contratos.subContratoSelecionado.dt_Inicio}"
                                    converter="conversorLocalDate"
                                    mask="99/99/9999"
                                    navigator="true"
                                    pattern="#{mascaras.padraoData}"
                                    locale="#{localeController.getCurrentLocale()}"
                                    styleClass="calendario"
                                    style="width: 100% !important; top: 0 !important;">
                                    <p:watermark for="vigenciaDe" value="01/01/1971"/>
                                </p:calendar>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 6px 4px 0px 4px">
                                <p:outputLabel for="vigenciaAte" value="#{localemsgs.Dt_Termino}"/>
                                <p:calendar
                                    id="vigenciaAte"
                                    value="#{contratos.subContratoSelecionado.dt_Termino}"
                                    converter="conversorLocalDate"
                                    mask="99/99/9999"
                                    navigator="true"
                                    pattern="#{mascaras.padraoData}"
                                    locale="#{localeController.getCurrentLocale()}"
                                    styleClass="calendario"
                                    style="width: 100%; top: 0 !important;">
                                    <p:watermark for="vigenciaAte" value="01/01/1971"/>
                                </p:calendar>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="position: relative; padding: 6px 0px 0px 4px">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}"/>
                                <p:selectOneMenu
                                    id="descricao"
                                    value="#{contratos.subContratoSelecionado.descricao}"
                                    converter="omnifaces.SelectItemsConverter"
                                    required="true"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.GrpPagamento}"
                                    styleClass="filial"
                                    style="width: 100%"
                                    filter="true"
                                    filterMatchMode="contains">
                                    <f:selectItem
                                        itemValue="#{null}"
                                        itemLabel="#{localemsgs.Selecione}"
                                        noSelectionOption="true" />
                                    <f:selectItems
                                        value="#{contratos.descricoes}"
                                        var="descricoes"
                                        itemValue="#{descricoes}"
                                        itemLabel="#{descricoes}"
                                        noSelectionValue=""/>
                                </p:selectOneMenu>
                                
                                <div class="col-md-1 col-sm-1 col-xs-2" style="position: absolute; top: 25px; right: 40px">
                                <p:commandLink
                                    actionListener="#{contratos.preCadastroDescricaoServicos()}"
                                    update="msgs formCadastroGrupoPagamento"
                                    partialSubmit="true"
                                    process="@this"
                                    title="#{localemsgs.CadastrarGrupos}">
                                    <p:graphicImage
                                        url="../assets/img/icone_redondo_adicionar.png"
                                        width="30" height="30" />
                                </p:commandLink>
                            </div>
                            </div>
                            
                            <div style="clearfix"/>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 6px 0px 0px 0px">
                                <p:outputLabel for="identificacao" value="#{localemsgs.Identificacao}:"/>
                                <p:inputText
                                    id="identificacao"
                                    value="#{contratos.subContratoSelecionado.identif}"
                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Identificacao}"
                                    required="true"
                                    style="width: 100%;"/>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                <p:tabView
                                    id="tabs"
                                    rendered="#{contratos.editandoSubContrato}"
                                    activeIndex="0"
                                    onTabShow="PF('dlgSubContrato').initPosition()"
                                    dynamic="true">
                                    <p:tab
                                        title="#{localemsgs.ItensFaturar}"
                                        class="AlturaTab"
                                        >
                                        <p:dataTable                                            
                                            id="tabelaItensFaturar" 
                                            value="#{contratos.allSubContratosItens}"                                             
                                            var="item"
                                            selectionMode="single"
                                            selection="#{contratos.ctrItemSelecionado}"
                                            resizableColumns="true"  
                                            emptyMessage="#{localemsgs.SemRegistros}"
                                            scrollable="true" 
                                            scrollWidth="100%" 
                                            scrollHeight="200"
                                            styleClass="tabela" 
                                            paginator="true"
                                            rows="15"
                                            lazy="true"
                                            reflow="true"
                                            rowsPerPageTemplate="5,10,15, 20, 25"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.ItensContrato}"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"                                            
                                            class="tabela DataGrid"
                                            >


                                            <p:ajax
                                                event="rowDblselect"
                                                listener="#{contratos.preEdicaoItensContrato}"
                                                update="msgs formSubContrato:tabs formItensContrato"
                                                />                                            




                                            <p:column headerText="#{localemsgs.TipoPosto}">
                                                <h:outputText value="#{item.tipoPosto}" title="#{item.tipoPosto}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{item.descricao}" title="#{item.descricao}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ValorRot}" rendered="#{contratos.mostrarPrecos == true}">
                                                <h:outputText value="#{item.valorRot}" title="#{item.valorRot}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ValorEve}" rendered="#{contratos.mostrarPrecos == true}">
                                                <h:outputText value="#{item.valorEve}" title="#{item.valorEve}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ValorEsp}" rendered="#{contratos.mostrarPrecos == true}">
                                                <h:outputText value="#{item.valorEsp}" title="#{item.valorEsp}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.ValorAst}" rendered="#{contratos.mostrarPrecos == true}">
                                                <h:outputText value="#{item.valorAst}" title="#{item.valorAst}" converter="conversormoeda"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Operador}">
                                                <h:outputText value="#{item.operador}" title="#{item.operador}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Dt_Alter}">
                                                <h:outputText value="#{item.dt_Alter}" title="#{item.dt_Alter}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Hr_Alter}">
                                                <h:outputText value="#{item.hr_Alter}" title="#{item.hr_Alter}" converter="conversorHora"/>
                                            </p:column>
                                        </p:dataTable>

                                        <div style="float: right;">
                                            <p:outputLabel for="mostrarPrecos" value="#{localemsgs.mostrarValores}:"/>

                                            <p:selectBooleanCheckbox id="mostrarPrecos" value="#{contratos.mostrarPrecos}">
                                                <p:ajax update="msgs formSubContrato:tabs:tabelaItensFaturar"/>
                                            </p:selectBooleanCheckbox>
                                        </div>

                                        <div class="botoesDataTable">
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                                <p:commandLink actionListener="#{contratos.preCadastroItem}"
                                                               update="msgs formItensContrato"
                                                               title="#{localemsgs.Cadastrar}">
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                                <p:commandLink actionListener="#{contratos.preEdicaoItensContrato}"
                                                               update="msgs"
                                                               title="#{localemsgs.ItensContrato}">
                                                    <p:graphicImage url="../assets/img/icone_cadastros.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </div>
                                        </div>
                                    </p:tab>

                                    <p:tab
                                        title="#{localemsgs.itensAnteriores}"
                                        class="AlturaTab"
                                        >
                                        <p:dataTable
                                            id="tabelaItensAnteriores"
                                            value="#{contratos.allItensAnteriores}"
                                            var="item"
                                            selection="#{contratos.itemAnteriorSelecionado}"
                                            selectionMode="single"
                                            paginator="true"
                                            rows="15"
                                            lazy="true"
                                            reflow="true"
                                            rowsPerPageTemplate="5,10,15, 20, 25"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.itensAnteriores}"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                            scrollHeight="200"
                                            scrollable="true"
                                            styleClass="tabela"
                                            emptyMessage="#{localemsgs.SemRegistros}"
                                            scrollWidth="100%"
                                            class="tabela DataGrid"
                                            >
                                            <p:ajax
                                                event="rowDblselect"
                                                listener="#{contratos.preEdicaoItensContrato}"
                                                process="@this"
                                                update="msgs formSubContrato:tabs"/>
                                            <p:column headerText="#{localemsgs.Data}">
                                                <h:outputText value="#{item.data}" title="#{item.data}" converter="conversorData"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Indice}">
                                                <h:outputText value="#{item.indice}" title="#{item.indice}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.TipoPosto}">
                                                <h:outputText value="#{item.tipoPosto}" title="#{item.tipoPosto}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{item.descricao}" title="#{item.descricao}"/>
                                            </p:column>
                                        </p:dataTable>

                                        <div class="botoesDataTable">
                                            <div class="col-md-12 col-sm-12 col-xs-12"
                                                 style="padding: 0px !important">
                                                <p:commandLink actionListener="#{contratos.cadastrarSubContrato()}"
                                                               update="msgs formSubContrato:tabs"
                                                               process="@this"
                                                               title="#{localemsgs.Cadastrar}">
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12"
                                                 style="padding: 0px !important">
                                                <p:commandLink actionListener="#{contratos.preEdicaoItensContrato()}"
                                                               update="msgs formSubContrato:tabs"
                                                               process="@this"
                                                               title="#{localemsgs.ItensContrato}">
                                                    <p:graphicImage url="../assets/img/icone_cadastros.png" width="40" height="40"/>
                                                </p:commandLink>
                                            </div>
                                        </div>
                                    </p:tab>
                                </p:tabView>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                                <p:commandLink
                                    id="cadastro"
                                    rendered="#{not contratos.editandoSubContrato}"
                                    action="#{contratos.cadastrarSubContrato()}"
                                    update=" :main :msgs :cabecalho"
                                    title="#{localemsgs.Cadastrar}" >
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                                <p:commandLink
                                    id="edit"
                                    rendered="#{contratos.editandoSubContrato}"
                                    action="#{contratos.editarSubContrato()}"
                                    update=":msgs :main:tabela :cabecalho"
                                    title="#{localemsgs.Editar}">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- Dialog de Item-->
                <h:form id="formItensContrato">
                    <p:dialog
                        widgetVar="dlgItensContrato"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        style="background-color: #EEE; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important;border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgItensContrato').closeIcon.unbind('click');

                                //register your own
                                PF('dlgItensContrato').closeIcon.click(function (e) {
                                    $("#formItensContrato\\:fecharFormItensContrato").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton style="display: none"
                                         oncomplete="PF('dlgItensContrato').hide()"
                                         id="fecharFormItensContrato">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.CadastrarItensContrato}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent; padding-left: 0px !important; padding-right: 0px !important; margin: 0px !important" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>


                            <div class="col-md-9 col-sm-9 col-xs-12" style="padding-right: 0px; padding-left: 0px">
                                <p:outputLabel for="codfil" value="#{localemsgs.CodFil}"  />
                                <p:selectOneMenu id="codfil" value="#{contratos.cadastroFilial}" converter="omnifaces.SelectItemsConverter"
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 styleClass="filial" style="width: 100%" disabled="true"
                                                 filter="true" filterMatchMode="contains" >
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-12" style="padding-right: 0px;">
                                <p:outputLabel for="contrato" value="#{localemsgs.Contrato}"/>
                                <p:inputText id="contrato" value="#{contratos.ctrItemSelecionado.contrato}" style="width: 100%;" disabled="true"/>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding-right: 0px; padding-top:8px !important; padding-left: 0px">
                                <p:outputLabel for="tipoPosto" value="#{localemsgs.TipoPosto}"  style="width: 100%"  />
                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding: 0px 4px 0px 0px">
                                    <p:inputText id="tipoPosto" value="#{contratos.ctrItemSelecionado.tipoPosto}" style="width: 100% !important;" disabled="true"/>    
                                </div>
                                <div class="col-md-9 col-sm-9 col-xs-8" style="padding: 0px 0px 0px 0px">
                                    <p:selectOneMenu value="#{contratos.ctrItemSelecionado.tipoPosto}" converter="omnifaces.SelectItemsConverter"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoPosto}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains" >
                                        <p:ajax event="itemSelect" update="tipoPosto"/>
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                        <f:selectItems value="#{contratos.tipoPosto}" />
                                    </p:selectOneMenu>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding-right: 0px; padding-top:8px !important">
                                <p:outputLabel for="tipoCalc" value="#{localemsgs.TipoCalc}" style="width: 100%" />
                                <div class="col-md-3 col-sm-3 col-xs-4" style="padding: 0px 4px 0px 0px">
                                    <p:inputText id="tipoCalc" value="#{contratos.ctrItemSelecionado.tipoCalc}" style="width: 100% !important;" disabled="true"/>
                                </div>
                                <div class="col-md-9 col-sm-9 col-xs-8" style="padding: 0px 0px 0px 0px">
                                    <p:selectOneMenu value="#{contratos.ctrItemSelecionado.tipoCalc}"
                                                     converter="omnifaces.SelectItemsConverter"
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.TipoCalc}"
                                                     styleClass="filial"
                                                     style="width: 100%"
                                                     filter="true"
                                                     filterMatchMode="tipoCalc" >
                                        <p:ajax event="itemSelect" update="tipoCalc"/>
                                        <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                        <f:selectItems value="#{contratos.tipoCalc}" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0px; padding-top:4px !important; padding-left: 0px">
                                <p:outputLabel for="descricao" value="#{localemsgs.Descricao}"  />
                                <p:inputText id="descricao" value="#{contratos.ctrItemSelecionado.descricao}" style="width: 100%;"/>
                            </div>

                            <div class="col-md-4 col-sm-4 col-xs-4" style="padding-right: 0px">

                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px; text-align: center !important; padding-top:4px !important">
                                <h:outputText value="#{localemsgs.Valor}" style="font-weight: 600;"/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px; text-align: center !important; padding-top:4px !important">
                                <h:outputText value="#{localemsgs.Franquia}" style="font-weight: 600;"/>
                            </div>



                            <div class="col-md-4 col-sm-4 col-xs-4" style="padding-right: 4px; text-align: right !important;">
                                <p:outputLabel value="#{localemsgs.Rotineiro}: " style="margin-top: 6px !important"  />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="valorRot" value="#{contratos.ctrItemSelecionado.valorRot}" style="width: 100%; text-align: center !important;"/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="franquiaRot" value="#{contratos.ctrItemSelecionado.franquiaRot}" style="width: 100%; text-align: center !important;"/>
                            </div>


                            <div class="col-md-4 col-sm-4 col-xs-4" style="padding-right: 4px; text-align: right !important;">
                                <p:outputLabel value="#{localemsgs.Eventual}: "  style="margin-top: 6px !important"  />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="valorEve" value="#{contratos.ctrItemSelecionado.valorEve}" style="width: 100%; text-align: center !important;"/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="franquiaEve" value="#{contratos.ctrItemSelecionado.franquiaEve}" style="width: 100%; text-align: center !important;"/>
                            </div>


                            <div class="col-md-4 col-sm-4 col-xs-4" style="padding-right: 4px; text-align: right !important;">
                                <p:outputLabel value="#{localemsgs.Especial}: "  style="margin-top: 6px !important"  />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="valorEsp" value="#{contratos.ctrItemSelecionado.valorEsp}" style="width: 100%; text-align: center !important;"/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="franquiaEsp" value="#{contratos.ctrItemSelecionado.franquiaEsp}" style="width: 100%; text-align: center !important;"/>
                            </div>


                            <div class="col-md-4 col-sm-4 col-xs-4" style="padding-right: 4px; text-align: right !important;">
                                <p:outputLabel value="#{localemsgs.AssistenciaTecnica}: "  style="margin-top: 6px !important"  />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="valorAst" value="#{contratos.ctrItemSelecionado.valorAst}"  style="width: 100%; text-align: center !important;"/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px">
                                <p:inputText id="franquiaAst" value="#{contratos.ctrItemSelecionado.franquiaAst}" style="width: 100%; text-align: center !important;"/>
                            </div>

                        </p:panel>
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important">
                            <p:commandLink
                                id="cadastro"
                                rendered="#{!contratos.editandoItem}"
                                action="#{contratos.cadastrarItem()}"
                                update=" :main :msgs :cabecalho"
                                title="#{localemsgs.Cadastrar}" >
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                            <p:commandLink
                                id="edit"
                                rendered="#{contratos.editandoItem}"
                                action="#{contratos.editarItem()}"
                                update=":msgs :main:tabela :cabecalho"
                                title="#{localemsgs.Editar}">
                                <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                            </p:commandLink>
                        </div>                        
                    </p:dialog>
                </h:form>

                <!-- Dialog de Reajuste -->
                <h:form id="formReajuste">
                    <p:dialog
                        widgetVar="dlgReajuste"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        styleClass="dlgCadastrar">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgReajuste').closeIcon.unbind('click');

                                //register your own
                                PF('dlgReajuste').closeIcon.click(function (e) {
                                    $("#formReajuste\\:fecharReajuste").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton
                            style="display: none"
                            oncomplete="PF('dlgReajuste').hide()"
                            id="fecharReajuste">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.EditarReajuste}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>
                            <p:outputLabel value="#{localemsgs.DtBase}"/>
                            <h:outputText
                                value="#{contratos.reajusteSelecionado.dtBase}"
                                converter="conversorData"/>
                            <p:outputLabel value="#{localemsgs.FormulaDesc}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.formulaDesc}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndiceDF}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indiceDF}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndiceSolic}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indiceSolic}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndCombAprov}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indCombAprov}"/><br/>
                            <p:outputLabel value="#{localemsgs.Embarques}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.embarques}"/><br/>
                            <p:outputLabel value="#{localemsgs.TE}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.TE}"/><br/>
                            <p:outputLabel value="#{localemsgs.Envelopes}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.envelopes}"/><br/>
                            <p:outputLabel value="#{localemsgs.Milheiros}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.milheiros}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndMOSolic}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indMOSolic}"/><br/>
                            <p:outputLabel value="#{localemsgs.KM}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.KM}"/><br/>
                            <p:outputLabel value="#{localemsgs.FixoMensal}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.fixoMensal}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndCombSolic}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indCombSolic}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndCombAprov}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indCombAprov}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndDFSolic}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indDFSolic}"/><br/>
                            <p:outputLabel value="#{localemsgs.IndDFAprov}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.indDFAprov}"/><br/>
                            <p:outputLabel value="#{localemsgs.Formula}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.formula}"/><br/>
                            <p:outputLabel value="#{localemsgs.CodCarta}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.codCarta}"/><br/>
                            <p:outputLabel value="#{localemsgs.DtCarta}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.dtCarta}"/><br/>
                            <p:outputLabel value="#{localemsgs.Dt_Alter}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.dt_Alter}"/><br/>
                            <p:outputLabel value="#{localemsgs.Hr_Alter}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.hr_Alter}"/><br/>
                            <p:outputLabel value="#{localemsgs.OperReaj}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.operReaj}"/><br/>
                            <p:outputLabel value="#{localemsgs.DtReaj}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.dtReaj}"/><br/>
                            <p:outputLabel value="#{localemsgs.HrReaj}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.hrReaj}"/><br/>
                            <p:outputLabel value="#{localemsgs.AdValorem}"/>
                            <h:outputText value="#{contratos.reajusteSelecionado.adValorem}"/><br/>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <h:form id="formCadastroGrupoPagamento">
                    <p:dialog
                        widgetVar="dlgCadastroGrupoPagamento"
                        positionType="absolute"
                        responsive="true"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        style="max-width: 800px !important; min-width: unset !important;"
                        styleClass="dlgCadastrar">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastroGrupoPagamento').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastroGrupoPagamento').closeIcon.click(function (e) {
                                    $("#formCadastroGrupoPagamento\\:fecharFormCadastroGrupoPagamento").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            });
                        </script>

                        <p:commandButton style="display: none"
                                         oncomplete="PF('dlgCadastroGrupoPagamento').hide()"
                                         id="fecharFormCadastroGrupoPagamento">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>

                        <f:facet name="header">
                            <img src="../assets/img/icone_satmob_fopag_G.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.AtualizarTabelaInterna}" style="color:#022a48"/>
                        </f:facet>

                        <p:panel id="cadastroGrupoPagamento" style="background-color: transparent;margin-top: 15px !Important" styleClass="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <div class="col-md-2 col-sm-2 col-xs-3" style="margin-bottom: 10px;">
                                <p:outputLabel value="#{localemsgs.Tabela}: "/>
                            </div>
                            <div class="col-md-10 col-sm-10 col-xs-9"  style="margin-bottom: 10px;">
                                <h:outputText value="#{contratos.tituloTabelaSelecionada}"/>
                            </div>
                            <div class="clearfix"></div>

                            <div class="col-md-2 col-sm-2 col-xs-3">
                                <p:outputLabel value="#{localemsgs.Codigo}"
                                               for="grupoPagamentoCadastroCodigo"/>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-3" style="margin-bottom: 10px;">
                                <p:inputText id="grupoPagamentoCadastroCodigo"
                                             value="#{contratos.tbValCadastro.codigo}"
                                             style="width: 100%"/>
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-3">
                                <p:outputLabel value="#{localemsgs.UltimoCadastrado}"/>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-3">
                                <h:outputText value="#{contratos.ultimoCodigoTbVal}"/>
                            </div>
                            <div class="clearfix"></div>

                            <div class="col-md-2 col-sm-2 col-xs-12">
                                <p:outputLabel value="#{localemsgs.Descricao}"
                                               for="grupoPagamentoCadastroDescricao"/>
                            </div>
                            <div class="col-md-10 col-sm-10 col-xs-12">
                                <p:inputText id="grupoPagamentoCadastroDescricao"
                                             value="#{contratos.tbValCadastro.descricao}"
                                             style="width: 100%"/>
                            </div>
                            <div class="clearfix"></div>
                        </p:panel>

                        <p:commandLink
                            action="#{contratos.inserirTbVal()}"
                            update="msgs formContrato:cadastrar formSubContrato:descricao"
                            title="#{localemsgs.Cadastrar}">
                            <p:graphicImage url="../assets/img/icone_redondo_adicionar.png"
                                            width="40" height="40" />
                        </p:commandLink>
                    </p:dialog>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{contratos.mostraFiliais}">
                                    <p:ajax update="msgs main:tabela" listener="#{contratos.mostrarFiliais}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.MostrarSomenteAtivos}: " /></label>
                                <p:selectBooleanCheckbox value="#{contratos.mostraAtivos}">
                                    <p:ajax update="msgs main:tabela" listener="#{contratos.mostrarAtivos}" />
                                </p:selectBooleanCheckbox>
                            </div>

                            <div>
                                <label ref="lblCheck"><h:outputText value="#{localemsgs.ContratosAVencer}: " /></label>
                                <p:selectBooleanCheckbox value="#{contratos.mostraVencer}">
                                    <p:ajax update="msgs main:tabela" listener="#{contratos.mostrarAVencer}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>

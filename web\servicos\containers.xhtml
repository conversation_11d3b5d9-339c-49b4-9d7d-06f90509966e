<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
        </h:head>
        <h:body>
            
            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{valores.mapaContainers2()}"/>
            </f:metadata>
            
            <p:growl id="msgs"/>
            
            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>
                
                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>            

                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="main">
                            <p:panel styleClass="painelCadastro" id="painelCadastro">
                             <p:gmap id="gmap" center="#{valores.centroMapa}" zoom="10" type="TERRAIN" 
                                    style="height:85vh" model="#{valores.posicaoRotas}">

                                <p:ajax event="overlaySelect" listener="#{valores.selecionarPinoConteiner}"/>

                                <p:gmapInfoWindow id="infoWindow">
                                    <p:panelGrid columns="1" style="text-align: center; display: block; margin: auto" >
                                        <h:outputText value="#{valores.posicaoContainer.IDEquip} - #{valores.posicaoContainer.nred}" style="font-weight: bold"/>
                                        <hr/>
                                        <h:outputText value="#{localemsgs.Endereco}: #{valores.posicaoContainer.ende}, #{valores.posicaoContainer.bairro} - #{valores.posicaoContainer.cidade}/#{valores.posicaoContainer.uf}" />
                                        <p:column>
                                            <h:outputText value="#{localemsgs.DataLocacao}: "/>
                                            <h:outputText value="#{valores.posicaoContainer.dtUltMov}" converter="conversorData"/>
                                        </p:column>
                                        <h:outputText value="#{localemsgs.HoraLocacao}: #{valores.posicaoContainer.hrcheg}" />
                                        <h:outputText value="#{localemsgs.TempoDias}: #{valores.posicaoContainer.tempoDias}" />
                                        <h:outputText value="#{localemsgs.Motorista}: #{valores.posicaoContainer.motorista}" />
                                    </p:panelGrid>
                                </p:gmapInfoWindow>                                 
                                 
                                 
                            </p:gmap>
                                
                            </p:panel>
                        </h:form>
                    </div>
                </ui:define>
            </ui:composition>
        </h:body>
    </f:view>
</html>

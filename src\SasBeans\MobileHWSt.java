/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class MobileHWSt {

    private String IMEI;
    private String CodEquip;
    private String Sequencia;
    private String Latitude;
    private String Longitude;
    private String Situacao;
    private String Operador;
    private String Data;
    private String Hora;

    private int PortaSuperior;
    private int PortaCofre;
    private int Cassete;
    private int Fechadura;
    private int Validadora;
    private int Impressao;

    public String getIMEI() {
        return IMEI;
    }

    public void setIMEI(String IMEI) {
        this.IMEI = IMEI;
    }

    public String getCodEquip() {
        return CodEquip;
    }

    public void setCodEquip(String CodEquip) {
        this.CodEquip = CodEquip;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public int getPortaSuperior() {
        return PortaSuperior;
    }

    public void setPortaSuperior(int PortaSuperior) {
        this.PortaSuperior = PortaSuperior;
    }

    public int getPortaCofre() {
        return PortaCofre;
    }

    public void setPortaCofre(int PortaCofre) {
        this.PortaCofre = PortaCofre;
    }

    public int getCassete() {
        return Cassete;
    }

    public void setCassete(int Cassete) {
        this.Cassete = Cassete;
    }

    public int getFechadura() {
        return Fechadura;
    }

    public void setFechadura(int Fechadura) {
        this.Fechadura = Fechadura;
    }

    public int getValidadora() {
        return Validadora;
    }

    public void setValidadora(int Validadora) {
        this.Validadora = Validadora;
    }

    public int getImpressao() {
        return Impressao;
    }

    public void setImpressao(int Impressao) {
        this.Impressao = Impressao;
    }
}

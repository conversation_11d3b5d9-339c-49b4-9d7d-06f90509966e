package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ATMInter {

    private BigDecimal OS;
    private BigDecimal CodFil;
    private LocalDate Data;
    private String Hora;
    private String Hora2;
    private String CodCli;
    private String Regiao;
    private String Solicitante;
    private String PedidoCli;
    private String CodAST;
    private String OBS;
    private LocalDate DtAtend;
    private String HrAtend;
    private String ServExec;
    private String SitEquip;
    private BigDecimal Matr;
    private LocalDate DtFech;
    private String HrFech;
    private String CodFech;
    private String Situacao;
    private String HrBaixa;
    private BigDecimal Pedido;
    private BigDecimal OSFat;
    private String OperIncl;
    private LocalDate Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;
    private String Flag_Excl;
    private String Oper_Excl;
    private LocalDate Dt_Excl;
    private String Hr_Excl;

    public ATMInter() {
        this.OS = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.Data = LocalDate.now();
        this.Hora = "";
        this.Hora2 = "";
        this.CodCli = "";
        this.Regiao = "";
        this.Solicitante = "";
        this.PedidoCli = "";
        this.CodAST = "";
        this.OBS = "";
        this.DtAtend = LocalDate.now();
        this.HrAtend = "";
        this.ServExec = "";
        this.SitEquip = "";
        this.Matr = new BigDecimal("0");
        this.DtFech = LocalDate.now();
        this.HrFech = "";
        this.CodFech = "";
        this.Situacao = "";
        this.HrBaixa = "";
        this.Pedido = new BigDecimal("0");
        this.OSFat = new BigDecimal("0");
        this.OperIncl = "";
        this.Dt_Incl = LocalDate.now();
        this.Hr_Incl = "";
        this.Operador = "";
        this.Dt_Alter = LocalDate.now();
        this.Hr_Alter = "";
        this.Flag_Excl = "";
        this.Oper_Excl = "";
        this.Dt_Excl = LocalDate.now();
        this.Hr_Excl = "";
    }

    /**
     * @return the OS
     */
    public BigDecimal getOS() {
        return OS;
    }

    /**
     * @param OS the OS to set
     */
    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    /**
     * @return the CodFil
     */
    public BigDecimal getCodFil() {
        return CodFil;
    }

    /**
     * @param CodFil the CodFil to set
     */
    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    /**
     * @return the Data
     */
    public LocalDate getData() {
        return Data;
    }

    /**
     * @param Data the Data to set
     */
    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    /**
     * @return the Hora
     */
    public String getHora() {
        return Hora;
    }

    /**
     * @param Hora the Hora to set
     */
    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    /**
     * @return the Hora2
     */
    public String getHora2() {
        return Hora2;
    }

    /**
     * @param Hora2 the Hora2 to set
     */
    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    /**
     * @return the CodCli
     */
    public String getCodCli() {
        return CodCli;
    }

    /**
     * @param CodCli the CodCli to set
     */
    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    /**
     * @return the Regiao
     */
    public String getRegiao() {
        return Regiao;
    }

    /**
     * @param Regiao the Regiao to set
     */
    public void setRegiao(String Regiao) {
        this.Regiao = Regiao;
    }

    /**
     * @return the Solicitante
     */
    public String getSolicitante() {
        return Solicitante;
    }

    /**
     * @param Solicitante the Solicitante to set
     */
    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    /**
     * @return the PedidoCli
     */
    public String getPedidoCli() {
        return PedidoCli;
    }

    /**
     * @param PedidoCli the PedidoCli to set
     */
    public void setPedidoCli(String PedidoCli) {
        this.PedidoCli = PedidoCli;
    }

    /**
     * @return the CodAST
     */
    public String getCodAST() {
        return CodAST;
    }

    /**
     * @param CodAST the CodAST to set
     */
    public void setCodAST(String CodAST) {
        this.CodAST = CodAST;
    }

    /**
     * @return the OBS
     */
    public String getOBS() {
        return OBS;
    }

    /**
     * @param OBS the OBS to set
     */
    public void setOBS(String OBS) {
        this.OBS = OBS;
    }

    /**
     * @return the DtAtend
     */
    public LocalDate getDtAtend() {
        return DtAtend;
    }

    /**
     * @param DtAtend the DtAtend to set
     */
    public void setDtAtend(LocalDate DtAtend) {
        this.DtAtend = DtAtend;
    }

    /**
     * @return the HrAtend
     */
    public String getHrAtend() {
        return HrAtend;
    }

    /**
     * @param HrAtend the HrAtend to set
     */
    public void setHrAtend(String HrAtend) {
        this.HrAtend = HrAtend;
    }

    /**
     * @return the ServExec
     */
    public String getServExec() {
        return ServExec;
    }

    /**
     * @param ServExec the ServExec to set
     */
    public void setServExec(String ServExec) {
        this.ServExec = ServExec;
    }

    /**
     * @return the SitEquip
     */
    public String getSitEquip() {
        return SitEquip;
    }

    /**
     * @param SitEquip the SitEquip to set
     */
    public void setSitEquip(String SitEquip) {
        this.SitEquip = SitEquip;
    }

    /**
     * @return the Matr
     */
    public BigDecimal getMatr() {
        return Matr;
    }

    /**
     * @param Matr the Matr to set
     */
    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    /**
     * @return the DtFech
     */
    public LocalDate getDtFech() {
        return DtFech;
    }

    /**
     * @param DtFech the DtFech to set
     */
    public void setDtFech(LocalDate DtFech) {
        this.DtFech = DtFech;
    }

    /**
     * @return the HrFech
     */
    public String getHrFech() {
        return HrFech;
    }

    /**
     * @param HrFech the HrFech to set
     */
    public void setHrFech(String HrFech) {
        this.HrFech = HrFech;
    }

    /**
     * @return the CodFech
     */
    public String getCodFech() {
        return CodFech;
    }

    /**
     * @param CodFech the CodFech to set
     */
    public void setCodFech(String CodFech) {
        this.CodFech = CodFech;
    }

    /**
     * @return the Situacao
     */
    public String getSituacao() {
        return Situacao;
    }

    /**
     * @param Situacao the Situacao to set
     */
    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    /**
     * @return the HrBaixa
     */
    public String getHrBaixa() {
        return HrBaixa;
    }

    /**
     * @param HrBaixa the HrBaixa to set
     */
    public void setHrBaixa(String HrBaixa) {
        this.HrBaixa = HrBaixa;
    }

    /**
     * @return the Pedido
     */
    public BigDecimal getPedido() {
        return Pedido;
    }

    /**
     * @param Pedido the Pedido to set
     */
    public void setPedido(String Pedido) {
        try {
            this.Pedido = new BigDecimal(Pedido);
        } catch (Exception e) {
            this.Pedido = new BigDecimal("0");
        }
    }

    /**
     * @return the OSFat
     */
    public BigDecimal getOSFat() {
        return OSFat;
    }

    /**
     * @param OSFat the OSFat to set
     */
    public void setOSFat(String OSFat) {
        try {
            this.OSFat = new BigDecimal(OSFat);
        } catch (Exception e) {
            this.OSFat = new BigDecimal("0");
        }
    }

    /**
     * @return the OperIncl
     */
    public String getOperIncl() {
        return OperIncl;
    }

    /**
     * @param OperIncl the OperIncl to set
     */
    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    /**
     * @return the Dt_Incl
     */
    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    /**
     * @param Dt_Incl the Dt_Incl to set
     */
    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    /**
     * @return the Hr_Incl
     */
    public String getHr_Incl() {
        return Hr_Incl;
    }

    /**
     * @param Hr_Incl the Hr_Incl to set
     */
    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    /**
     * @return the Flag_Excl
     */
    public String getFlag_Excl() {
        return Flag_Excl;
    }

    /**
     * @param Flag_Excl the Flag_Excl to set
     */
    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    /**
     * @return the Oper_Excl
     */
    public String getOper_Excl() {
        return Oper_Excl;
    }

    /**
     * @param Oper_Excl the Oper_Excl to set
     */
    public void setOper_Excl(String Oper_Excl) {
        this.Oper_Excl = Oper_Excl;
    }

    /**
     * @return the Dt_Excl
     */
    public LocalDate getDt_Excl() {
        return Dt_Excl;
    }

    /**
     * @param Dt_Excl the Dt_Excl to set
     */
    public void setDt_Excl(LocalDate Dt_Excl) {
        this.Dt_Excl = Dt_Excl;
    }

    /**
     * @return the Hr_Excl
     */
    public String getHr_Excl() {
        return Hr_Excl;
    }

    /**
     * @param Hr_Excl the Hr_Excl to set
     */
    public void setHr_Excl(String Hr_Excl) {
        this.Hr_Excl = Hr_Excl;
    }
}

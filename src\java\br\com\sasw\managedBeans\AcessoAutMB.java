/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans;

import Arquivo.ArquivoLog;
import Controller.Pedidos.PedidosSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.AcessoAut;
import SasBeans.AcessoAutArea;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import SasBeans.SasPWFill;
import SasDaos.AcessoAutDao;
import SasDaos.FuncionDao;
import SasDaos.PessoaDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR> Nicolau
 */
@Named(value = "acessoAut")
@ViewScoped
public class AcessoAutMB implements Serializable {

    private Persistencia persistencia, central;
    private ArquivoLog logerro;
    private String mesTela, anoTela, log;
    private String dataTela, data1, data2, dataRef;
    private final String codfil, banco, codcli, operador, caminho;
    private final BigDecimal codpessoa;
    private Filiais filiais, filialTela;
    private SasPWFill filial;
    private List<AcessoAut> listaAutorizacoes;
    private AcessoAut autorizacaoSelecionada;
    private AcessoAutDao acessoAutDao;
    private Boolean corporativo, somentePendentes, segSex, sab, dom, fer;
    private List<Date> datasSelecionadas;
    private Date dataInicio, dataFim;
    private Pessoa pessoa;
    private List<Pessoa> listaPessoas;
    private List<AcessoAutArea> listaAcessoAreas, listaCadastroAreas;
    private AcessoAutArea area;
    private AcessoAutArea acessoAutAreaSelecionado;
    private Funcion funcion;
    private final PessoaDao pessoaDao;
    private final FuncionDao funcionDao;
    private final PedidosSatMobWeb pedidosSatMobWeb;
    private String chavePesquisa = "NOME", chaveOrdem = "NOME", valorPesquisa;

    public AcessoAutMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        codcli = (String) fc.getExternalContext().getSessionMap().get("cliente");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        if (null == anoTela
                || anoTela.equals("")) {
            anoTela = DataAtual.getDataAtual("SQL").substring(0, 4);
        }

        if (null == mesTela
                || mesTela.equals("")) {
            mesTela = DataAtual.getDataAtual("TELA").split("/")[1];
        }

        pedidosSatMobWeb = new PedidosSatMobWeb();
        logerro = new ArquivoLog();
        listaAutorizacoes = new ArrayList<>();
        acessoAutDao = new AcessoAutDao();
        corporativo = false;
        somentePendentes = false;

        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataFim = c.getTime();
        c.add(Calendar.DATE, -30);
        dataInicio = c.getTime();

        data1 = dataInicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data2 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataInicio); // data inicial
        datasSelecionadas.add(dataFim); // data final

        filial = new SasPWFill();
        filiais = new Filiais();
        funcion = new Funcion();

        pessoaDao = new PessoaDao();
        funcionDao = new FuncionDao();
    }

    public void Persistencia(Persistencia pp, Persistencia local) {
        try {
            this.persistencia = pp;

            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = local;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            RotasSatWeb rotassatweb = new RotasSatWeb();
            this.filialTela = rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
            this.filial = this.pedidosSatMobWeb.buscaFilial(this.codfil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarListaAutorizacoes() throws Exception {
        try {
            listaAutorizacoes = acessoAutDao.listaAcessos(this.data1, this.data2, !this.corporativo ? this.codfil : "", this.somentePendentes, this.chavePesquisa, this.valorPesquisa, replaceOrder(chaveOrdem), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    private String replaceOrder(String Valor) {
        try {
            switch (this.chaveOrdem) {
                case "NOME":
                    return "Pessoa.Nome";
                case "RG":
                    return "Pessoa.RG";
                case "SEQUENCIA":
                    return "AcessoAut.Sequencia";
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
        return null;
    }

    public void novaAutorizacao() throws Exception {
        this.acessoAutAreaSelecionado = null;
        this.autorizacaoSelecionada = new AcessoAut();
        this.segSex = false;
        this.sab = false;
        this.dom = false;
        this.fer = false;
        this.pessoa = null;
        this.listaPessoas = new ArrayList<>();
        this.funcion = new Funcion();
        this.listaAcessoAreas = new ArrayList<>();
        this.filial = this.pedidosSatMobWeb.buscaFilial(this.codfil, this.persistencia);
        this.listaCadastroAreas = acessoAutDao.listaCadastrosAreas(this.codfil, this.persistencia);

        PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
    }

    public void carregarEdicao(BigDecimal sequencia) {
        try {
            this.acessoAutAreaSelecionado = null;
            this.autorizacaoSelecionada = listaAutorizacoes.stream().filter(p -> p.getSequencia2().equals(sequencia)).findFirst().get();

            this.segSex = this.autorizacaoSelecionada.getSegSex().equals("1") ? true : false;
            this.sab = this.autorizacaoSelecionada.getSab().equals("1") ? true : false;
            this.dom = this.autorizacaoSelecionada.getDom().equals("1") ? true : false;
            this.fer = this.autorizacaoSelecionada.getFer().equals("1") ? true : false;

            this.pessoa = pessoaDao.buscarPessoaCodigo(BigDecimal.valueOf(Double.parseDouble(this.autorizacaoSelecionada.getCodPessoa())), this.persistencia);
            this.listaPessoas = pessoaDao.listagemPessoaQuery(this.pessoa.getNome(), this.persistencia);
            this.funcion = funcionDao.buscarFuncion(this.pessoa.getMatr().toPlainString().replace(".0", ""), this.persistencia);
            this.listaAcessoAreas = acessoAutDao.listaAcessosAreas(this.autorizacaoSelecionada.getSequencia2().toPlainString().replace(".0", ""), this.persistencia);
            this.filial = this.pedidosSatMobWeb.buscaFilial(this.autorizacaoSelecionada.getCodFil2().toPlainString().replace(".0", ""), this.persistencia);
            this.listaCadastroAreas = acessoAutDao.listaCadastrosAreas(this.autorizacaoSelecionada.getCodFil2().toPlainString().replace(".0", ""), this.persistencia);

            PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void salvarAutorizacao() {
        try {
            this.autorizacaoSelecionada.setSegSex(this.segSex ? "1" : "0");
            this.autorizacaoSelecionada.setSab(this.sab ? "1" : "0");
            this.autorizacaoSelecionada.setDom(this.dom ? "1" : "0");
            this.autorizacaoSelecionada.setFer(this.fer ? "1" : "0");

            this.autorizacaoSelecionada.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.autorizacaoSelecionada.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.autorizacaoSelecionada.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.autorizacaoSelecionada.setCodPessoa(this.pessoa.getCodigo().toPlainString().replace(".0", ""));
            this.autorizacaoSelecionada.setCodFil(this.filial.getCodfilAc());

            acessoAutDao.salvar(this.autorizacaoSelecionada, this.listaAcessoAreas, this.persistencia);

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");

            carregarListaAutorizacoes();

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("DadosSalvosSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void novaArea() {
        this.area = new AcessoAutArea();

        PrimeFaces.current().resetInputs("formAreas:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgAreas').show();");
    }

    public void limparPesquisa() throws Exception {
        this.valorPesquisa = "";
        this.chavePesquisa = "NOME";

        carregarListaAutorizacoes();
    }

    public void listarCadastroAreas() throws Exception {
        this.listaCadastroAreas = acessoAutDao.listaCadastrosAreas(this.filial.getCodFil(), this.persistencia);
    }

    public void salvarArea() {
        Boolean valid = false;

        for (AcessoAutArea item : listaAcessoAreas) {
            if (item.getCodArea().equals(this.area.getCodArea())) {
                valid = true;
            }
        }

        if (!valid) {
            AcessoAutArea item = new AcessoAutArea();

            item.setCodArea(this.area.getCodArea());
            item.setCodFil(this.filial.getCodFil());
            item.setDescricao(this.listaCadastroAreas.stream().filter(p -> p.getCodArea().equals(this.area.getCodArea())).findFirst().get().getDescricao());
            item.setDt_Alter(DataAtual.getDataAtual("SQL"));
            item.setHrEntrada(this.area.getHrEntrada());
            item.setHrSaida(this.area.getHrSaida());
            item.setHr_Alter(DataAtual.getDataAtual("HORA"));
            item.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            item.setSequencia(this.autorizacaoSelecionada.getSequencia2());

            listaAcessoAreas.add(item);
        }

        PrimeFaces.current().executeScript("PF('dlgAreas').hide();");
    }

    public void selecionarDatasProdutividade(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                carregarListaAutorizacoes();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Pessoa> buscarPessoas(String nome) throws Exception {
        try {
            this.listaPessoas = pessoaDao.listagemPessoaQuery(nome, this.persistencia);

            return listaPessoas;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            return null;
        }
    }

    public void buscarFuncion() throws Exception {
        try {
            if (null != pessoa && null != pessoa.getMatr() && pessoa.getMatr() != BigDecimal.ZERO) {
                funcion = funcionDao.buscarFuncion(pessoa.getMatr().toPlainString().replace(".0", ""), this.persistencia);
            } else {
                funcion = new Funcion();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preExcluir(BigDecimal sequencia) {
        this.autorizacaoSelecionada = listaAutorizacoes.stream().filter(p -> p.getSequencia2().equals(sequencia)).findFirst().get();
        PrimeFaces.current().executeScript("ConfirmarExclusaoAutorizacao();");
    }

    public void autorizar() throws Exception {
        this.autorizacaoSelecionada.setOperAut(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.autorizacaoSelecionada.setDt_Aut(DataAtual.getDataAtual("SQL"));
        this.autorizacaoSelecionada.setHr_Aut(DataAtual.getDataAtual("HORA"));

        acessoAutDao.autorizar(this.autorizacaoSelecionada, this.persistencia);

        carregarListaAutorizacoes();

        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("AcaoEfetuadaSucesso"), null);
        FacesContext.getCurrentInstance().addMessage(null, mensagem);
    }

    public void preAutorizar(BigDecimal sequencia) {
        this.autorizacaoSelecionada = listaAutorizacoes.stream().filter(p -> p.getSequencia2().equals(sequencia)).findFirst().get();
        PrimeFaces.current().executeScript("Autorizar();");
    }

    public void excluirAutorizacao() {
        try {
            acessoAutDao.excluir(this.autorizacaoSelecionada.getSequencia2().toPlainString().replace(".0", ""), this.persistencia);

            carregarListaAutorizacoes();

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ExcluidoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void excluirArea() {
        try {
            if (null != this.acessoAutAreaSelecionado
                    && null != this.acessoAutAreaSelecionado.CodArea
                    && !this.acessoAutAreaSelecionado.CodArea.equals("0")) {

                List<AcessoAutArea> listaAcessoAreasNova = new ArrayList<>();

                for (AcessoAutArea item : this.listaAcessoAreas) {
                    if (!item.getCodArea().equals(this.acessoAutAreaSelecionado.getCodArea())) {
                        listaAcessoAreasNova.add(item);
                    }
                }

                this.listaAcessoAreas = listaAcessoAreasNova;

            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneCtrItem"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public Persistencia getCentral() {
        return central;
    }

    public void setCentral(Persistencia central) {
        this.central = central;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public String getMesTela() {
        return mesTela;
    }

    public void setMesTela(String mesTela) {
        this.mesTela = mesTela;
    }

    public String getAnoTela() {
        return anoTela;
    }

    public void setAnoTela(String anoTela) {
        this.anoTela = anoTela;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public List<AcessoAut> getListaAutorizacoes() {
        return listaAutorizacoes;
    }

    public void setListaAutorizacoes(List<AcessoAut> listaAutorizacoes) {
        this.listaAutorizacoes = listaAutorizacoes;
    }

    public Filiais getFilialTela() {
        return filialTela;
    }

    public void setFilialTela(Filiais filialTela) {
        this.filialTela = filialTela;
    }

    public AcessoAutDao getAcessoAutDao() {
        return acessoAutDao;
    }

    public void setAcessoAutDao(AcessoAutDao acessoAutDao) {
        this.acessoAutDao = acessoAutDao;
    }

    public Boolean getCorporativo() {
        return corporativo;
    }

    public void setCorporativo(Boolean corporativo) {
        this.corporativo = corporativo;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public String getDataRef() {
        return dataRef;
    }

    public void setDataRef(String dataRef) {
        this.dataRef = dataRef;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Boolean getSomentePendentes() {
        return somentePendentes;
    }

    public void setSomentePendentes(Boolean somentePendentes) {
        this.somentePendentes = somentePendentes;
    }

    public AcessoAut getAutorizacaoSelecionada() {
        return autorizacaoSelecionada;
    }

    public void setAutorizacaoSelecionada(AcessoAut autorizacaoSelecionada) {
        this.autorizacaoSelecionada = autorizacaoSelecionada;
    }

    public SasPWFill getFilial() {
        return filial;
    }

    public void setFilial(SasPWFill filial) {
        this.filial = filial;
    }

    public Boolean getSegSex() {
        return segSex;
    }

    public void setSegSex(Boolean segSex) {
        this.segSex = segSex;
    }

    public Boolean getSab() {
        return sab;
    }

    public void setSab(Boolean sab) {
        this.sab = sab;
    }

    public Boolean getDom() {
        return dom;
    }

    public void setDom(Boolean dom) {
        this.dom = dom;
    }

    public Boolean getFer() {
        return fer;
    }

    public void setFer(Boolean fer) {
        this.fer = fer;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public List<Pessoa> getListaPessoas() {
        return listaPessoas;
    }

    public void setListaPessoas(List<Pessoa> listaPessoas) {
        this.listaPessoas = listaPessoas;
    }

    public Funcion getFuncion() {
        return funcion;
    }

    public void setFuncion(Funcion funcion) {
        this.funcion = funcion;
    }

    public List<AcessoAutArea> getListaAcessoAreas() {
        return listaAcessoAreas;
    }

    public void setListaAcessoAreas(List<AcessoAutArea> listaAcessoAreas) {
        this.listaAcessoAreas = listaAcessoAreas;
    }

    public AcessoAutArea getAcessoAutAreaSelecionado() {
        return acessoAutAreaSelecionado;
    }

    public void setAcessoAutAreaSelecionado(AcessoAutArea acessoAutAreaSelecionado) {
        this.acessoAutAreaSelecionado = acessoAutAreaSelecionado;
    }

    public AcessoAutArea getArea() {
        return area;
    }

    public void setArea(AcessoAutArea area) {
        this.area = area;
    }

    public List<AcessoAutArea> getListaCadastroAreas() {
        return listaCadastroAreas;
    }

    public void setListaCadastroAreas(List<AcessoAutArea> listaCadastroAreas) {
        this.listaCadastroAreas = listaCadastroAreas;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getChaveOrdem() {
        return chaveOrdem;
    }

    public void setChaveOrdem(String chaveOrdem) {
        this.chaveOrdem = chaveOrdem;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

}

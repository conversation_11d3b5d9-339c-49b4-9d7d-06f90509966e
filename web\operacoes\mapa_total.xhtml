<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/valores/mapa_total.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .SeparadorBlocos td{
                    background-color: #000 !important;
                    background: #000 !important;
                    color: #FFF !important;
                    font-weight: bold !important;
                    border-left: 1px solid #505050 !important;
                    border-right: 1px solid #505050 !important;
                }
            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;">

            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                <f:viewParam name="codfil" value="#{valores.codfil}" />
                <f:viewParam name="datatela" value="#{valores.dataRota}" />
                <f:viewParam name="pedido" value="#{valores.somentePedido}" />
                <f:viewAction action="#{valores.carregarMapaTodasRotas()}"/>
            </f:metadata>
            <p:growl id="msgs"/>
            <div class="LightBox" style="display:none"></div>
            <div class="divFrame" style="display:none">
                <label id="btFecharDetalhePedido" style="width:32px;height:32px;background-color:red;color:#FFF; border-radius:50%; font-size:14pt; padding-top:0px; padding-left:0px !important; text-align:center; position:absolute; right:-8px; top:-13px; border:2px solid red; cursor:pointer" title="#{localemsgs.Fechar}"><i class="fa fa-times"></i></label>
            </div>
            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icones_satmob_carroforte.png" height="40" style="margin-top:-6px !important;" />
                                    <label class="TituloPagina">#{localemsgs.RotasValores}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{valores.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{valores.filialDesc}<label id="btTrocarFilial">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{valores.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{valores.filiais.bairro}, #{valores.filiais.cidade}/#{valores.filiais.UF}</label>
                                </div>


                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6">
                                    <p:commandLink action="#{valores.MapaDiaAnteriorTodasRotas}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px" />
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{valores.dataTela}" maxdate="#{valores.ultimoDia}" locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{valores.SelecionarDataMapaTodasRotas}" update="main cabecalho" />
                                    </p:calendar>

                                    <p:commandLink action="#{valores.MapaDiaPosteriorTodasRotas}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px" />
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="display:none !important;">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{valores.filialDesc}
                                    </div>
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.QtdRotas}: #{valores.total}
                                    </div>
                                    <div class="ui-grid-col-4">

                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:remoteCommand name="rcInserirPosicoes" partialSubmit="true" 
                                             update="msgs"
                                             process="@this"
                                             actionListener="#{valores.RecarregaGeraPontosMapaTotal}" /> 
                    
                    <div class="FundoMapa">
                        <p:panel styleClass="painelCadastro" id="painelCadastro" style="overflow:hidden;">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-10,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:panel style="position:relative;">
                                    <label class="AbrirFiltros" style="left:0px; display: none;"><i class="fa fa-filter"></i>&nbsp;<h:outputText value="#{localemsgs.Filtrar.toUpperCase()}" /></label>
                                    <div id="divQuadroResumo" style="left: -260px; display:none">
                                        <h:outputText class="fa fa-times FecharFiltros" title="#{localemsgs.Fechar}" style="display:none" />

                                        <div class="ItemResumo" style="padding-bottom: 1px !important;">
                                            <label id="lblTituloFiltro">
                                                <i class="fa fa-filter"/>
                                                &nbsp;
                                                <h:outputText value="#{localemsgs.Periodo.toUpperCase()}" />
                                            </label>
                                            <div class="ItemZebrado">
                                                <label class="QdeStatus"></label>
                                                <label>
                                                    <input type='checkbox' id='chkManha' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkManha'>
                                                        <h:outputText value=" #{localemsgs.Manha}" />
                                                    </label>
                                                </label>
                                            </div>
                                            <div class="Item">
                                                <label class="QdeStatus"></label>
                                                <label>
                                                    <input type='checkbox' id='chkTarde' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkTarde'>
                                                        <h:outputText value="#{localemsgs.Tarde}"/>
                                                    </label>
                                                </label>
                                            </div>
                                            <div class="ItemZebrado">
                                                <label class="QdeStatus" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkNoite' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkNoite'>
                                                        <h:outputText value="#{localemsgs.Noite}" />
                                                    </label>

                                                </label>
                                            </div>
                                            <div class="Item">
                                                <label class="QdeStatus" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkVencidos' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkVencidos'>
                                                        <h:outputText value="#{localemsgs.Atrasados}" />
                                                    </label>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                            <label id="lblTituloFiltro">
                                                <i class="fa fa-filter"/>
                                                &nbsp;
                                                <h:outputText value="#{localemsgs.Rota.toUpperCase()}"/>
                                            </label>
                                            <div class="ItemZebrado" style="padding:1px !important;">
                                                <p:selectOneMenu id="cboFiltroRotas" value="" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                    <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="T" />
                                                </p:selectOneMenu>

                                            </div>
                                        </div>

                                        <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                            <label id="lblTituloFiltro">
                                                <i class="fa fa-filter"/>
                                                &nbsp;
                                                <h:outputText value="#{localemsgs.tipoServ.toUpperCase()}"/>
                                            </label>
                                            <div class="ItemZebrado">
                                                <label class="QdeStatus" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkPedidos' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkPedidos'>
                                                        <h:outputText value="#{localemsgs.Pedidos}" />
                                                    </label>
                                                </label>
                                            </div>
                                            <div class="Item">
                                                <label class="QdeStatus" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkRotaProgramada' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkRotaProgramada'>
                                                        <h:outputText value="#{localemsgs.Trajetos}" />
                                                    </label>
                                                </label>
                                            </div>
                                            <div class="ItemZebrado">
                                                <label class="QdeStatus" style="padding:1px !important"></label>
                                                <label>
                                                    <input type='checkbox' id='chkVeiculos' checked="checked" />
                                                    &nbsp;
                                                    <label for='chkVeiculos'>
                                                        <h:outputText value="#{localemsgs.Veiculo}" />
                                                    </label>
                                                </label>
                                            </div>
                                        </div>
                                        <input id="txtPesquisarCliente" type="text" class="form-control" style="width:293px !important; margin-top:10px; padding:15px !important; height:50px !important" placeholder="#{localemsgs.PesquisarCliente}" />
                                        <p:lightBox iframe="true" id="lightboxListaRotas">
                                            <h:link outcome="direccion.xhtml?faces-redirect=true" includeViewParams="true">
                                                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                                                <f:viewParam name="codfil" value="#{valores.codfil}" />
                                                <f:viewParam name="dataTela" value="#{valores.dataTela}" />

                                                <p:commandButton value="#{localemsgs.TodasRotas}" onclick="OcultarObjetos();" id="btAbrirModal" icon="fa fa-list"
                                                                 styleClass="botao BotaoResumoQuadro" style="width: 100% !important; text-align:center !important;"
                                                                 type="submit"/>
                                            </h:link>
                                        </p:lightBox>
                                        <p:lightBox iframe="true" id="lightboxListaPedidos">
                                            <h:link outcome="pedidos.xhtml?faces-redirect=true">
                                                <p:commandButton value="#{localemsgs.Pedidos}" actionListener="#{pedido.persistencia(login.pp)}" onclick="OcultarObjetos();" icon="fa fa-list"
                                                                 styleClass="botao BotaoResumoQuadro" style="width: 100% !important; text-align:center !important;"
                                                                 type="submit"/>
                                            </h:link>
                                        </p:lightBox>
                                    </div>

                                    <div id="mapGoogle" style="height: calc(100vh - 148px)"></div>

                                    <div style="position:absolute; z-index: 999999 !important; top:60px; float:right; padding:4px !important; right:10px; background-color: rgba(255,255,255,0.9) !important; box-shadow:2px 2px 3px #666; border-radius: 3px !important; min-height:300px !important;height:300px !important;max-height:300px !important; overflow:hidden !important; display: none">
                                        <div style="min-height:250px !important;height:250px !important;max-height:250px !important; overflow:auto !important;">
                                            <table id="tblPedidosRoteirizar" class="DataGrid" style="background-color: #FFF !important">
                                                <thead>
                                                    <tr>
                                                        <th colspan="5" style="text-align:left !important; padding-right: 8px !important; font-size:8pt !important; padding-top:4px !important;">
                                                            <span style="margin-left:8px !important">#{localemsgs.SelecionePrimeiroHorario}:&nbsp;</span>

                                                            <p:selectOneMenu id="hora1O" editable="false" 
                                                                             converter="conversorHora" validator="ValidadorHora"
                                                                             required="true"
                                                                             filter="true" filterMatchMode="contains" style="width: calc(100% - 6px); margin-left:6px;margin-bottom:6px;">
                                                                <f:selectItems value="#{horas.obterHorario()}" />
                                                            </p:selectOneMenu>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <th style="min-width:200px !important;width:200px !important;max-width:200px !important; font-size:8pt !important; padding-top:4px !important; padding-bottom: 4px !important;">&nbsp;&nbsp;#{localemsgs.Origem}</th>
                                                        <th style="min-width:200px !important;width:200px !important;max-width:200px !important; font-size:8pt !important; padding-top:4px !important; padding-bottom: 4px !important;">&nbsp;&nbsp;#{localemsgs.Destino}</th>
                                                        <th style="text-align:center !important;min-width:85px !important;width:85px !important;max-width:85px !important; font-size:8pt !important; padding-top:4px !important; padding-bottom: 4px !important;">#{localemsgs.Pedido}</th>
                                                        <th style="text-align:center !important;min-width:85px !important;width:85px !important;max-width:85px !important; font-size:8pt !important; padding-top:4px !important; padding-bottom: 4px !important;">#{localemsgs.JanelaHorario}</th>
                                                        <th rowspan="2" style="text-align:center !important;min-width:70px !important;width:70px !important;max-width:70px !important; font-size:8pt !important; padding-top:4px !important; padding-bottom: 4px !important;">#{localemsgs.Rota}</th>
                                                    </tr>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                        <div class="col-md-6" id="divBtProcessar" style="padding:8px 4px 0px 0px !important; width:50% !important;"><a id="btAutoRoteiriza" class="btn btn-large btn-success" href="javascript:;" style="width:100% !important; color:#FFF !important; text-transform: capitalize !important;"><i class="fa fa-cog"></i>&nbsp;&nbsp;#{localemsgs.RoteirizaAuto}</a></div>
                                        <div class="col-md-6" id="divBtCancelar" style="padding:8px 0px 0px 4px !important; width:50% !important;"><a id="btCancelarRoteiro" class="btn btn-large btn-warning" href="javascript:;" style="width:100% !important; color:#FFF !important; text-transform: capitalize !important;"><i class="fa fa-ban"></i>&nbsp;&nbsp;#{localemsgs.Cancelar}</a></div>
                                        <div class="col-md-12" id="divProcessando" style="display:none;">
                                            <label style="font-size:10pt !important; color:#999; width:100%; text-align: center; margin-top:12px !important;"><i class="fa fa-refresh fa-spin fa-fw" style="font-size:14pt"></i>&nbsp;&nbsp;#{localemsgs.ProcessandoAguarde}</label>
                                        </div>
                                        <p:commandButton id="btMsgPrimeiroHora" actionListener="#{valores.exibirMensagemCliente(localemsgs.SelecionePrimeiroHorario)}" process="@this" partialSubmit="true" update="msgs" style="display:none !important"></p:commandButton>
                                        <p:commandButton id="btMsgSelecioneRotas" actionListener="#{valores.exibirMensagemCliente(localemsgs.SelecioneTodasRotas)}" process="@this" partialSubmit="true" update="msgs" style="display:none !important"></p:commandButton>
                                    </div>
                                </p:panel>
                                <p:panel class="Rotas">
                                    
                                    <p:commandButton id="btAtualizarMapa" value="#{localemsgs.AtualizarMapa}" onclick="rcInserirPosicoes()" icon="fa fa-refresh"
                                                     styleClass="botao" style="width: 100% !important; height: 40px !important; padding: 0px !important; text-align:center !important; margin-top:0px; margin-bottom:6px;"
                                                     type="submit"/>
                                    
                                    <div id="legend">
                                        <h3 style="color:#FFF !important; background-color: #3479A3 !important; border-radius:3px 3px 0px 0px;
                                            margin-top: 3px !important; margin-bottom:0px !important; padding-bottom:5px !important;
                                            padding-top:3px !important;padding-left:7px; font-size:14pt !important; font-weight:500 !important;">
                                            <i class="fa fa-map" style="font-size:12pt !important;"></i>
                                            &nbsp;#{localemsgs.Rotas}
                                            <label style="background-color:#FFF; color:steelblue; font-weight: bold; font-size:10pt; text-align:center; 
                                                   width: 60px; border-radius: 40px; padding-top:3px !important; float:right !important; 
                                                   padding-bottom:2px !important; margin-right: 6px; margin-top:1px">
                                                #{valores.qtdeRotasMapa}
                                            </label>
                                        </h3>
                                    </div>
                                </p:panel>
                            </p:panelGrid>
                        </p:panel>

                        <p:dialog header="#{localemsgs.Guias}" styleClass="box-primary"
                                  widgetVar="dlgGuiasSelecao" minHeight="500" width="500" modal="true"  appendTo="@(body)"
                                  responsive="true" dynamic="true">

                            <p:dataGrid id="tabelaGtv" value="#{valores.guias}"
                                        var="gtv" columns="1">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1"
                                                  onTabShow="PF('dlgGuiasSelecao').initPosition()"
                                                  onTabClose="PF('dlgGuiasSelecao').initPosition()">
                                    <p:tab>
                                        <f:facet name="title">
                                            <h:outputText value="#{localemsgs.Guia} #{gtv.guia} - #{gtv.serie}"/>

                                            <p:commandLink title="#{localemsgs.Guias}" ajax="true" update="main:tabelaGtv" style="float: right"
                                                           actionListener="#{valores.abrirGuia(gtv)}">
                                                <h:outputText styleClass="fas fa-print" style="margin:0 auto;"/>
                                            </p:commandLink>
                                        </f:facet>
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                     layout="grid" styleClass="ui-panelgrid-blank">

                                            <h:outputText value="#{localemsgs.Valor}:"/>
                                            <h:outputText value="#{gtv.valor}"
                                                          title="#{gtv.valor}" converter="conversormoeda"/>

                                            <h:outputText value="#{localemsgs.OrigemColeta}:"/>
                                            <p:column>
                                                <h:outputText value="#{gtv.agSB}" title="#{gtv.agSB}"/>
                                                <h:outputText value="#{gtv.origem}" title="#{gtv.origem}"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DtColeta}: "/>
                                            <h:outputText value="#{gtv.dtColeta}" title="#{gtv.dtColeta}" converter="conversorData"/>

                                            <h:outputText value="#{localemsgs.HrColeta}: "/>
                                            <p:column>
                                                <h:outputText value="#{gtv.hrCheg}" title="#{gtv.hrCheg}" converter="conversorHora"/>
                                                <h:outputText value=" - "/>
                                                <h:outputText value="#{gtv.hrSaida}" title="#{gtv.hrSaida}" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DestinoEntrega}:"/>
                                            <p:column>
                                                <h:outputText value="#{gtv.agSBD}" title="#{gtv.agSBD}"/>
                                                <h:outputText value="#{gtv.destino}" title="#{gtv.destino}"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DtEntrega}: "/>
                                            <h:outputText value="#{gtv.dtEntrega}" title="#{gtv.dtEntrega}" converter="conversorData"/>

                                            <h:outputText value="#{localemsgs.HrCheg}: "/>
                                            <p:column>
                                                <h:outputText value="#{gtv.hrCheg_E}" title="#{gtv.hrCheg_E}" converter="conversorHora"/>
                                                <h:outputText value=" - " rendered="#{gtv.hrCheg_E ne '' and gtv.hrCheg_S ne '' and gtv.hrCheg_E ne null and gtv.hrCheg_S ne null}"/>
                                                <h:outputText value="#{gtv.hrCheg_S}" title="#{gtv.hrCheg_S}" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:dataGrid>
                        </p:dialog>
                    </div>

                    <script type="text/javascript">
                        // <![CDATA[
                        if ($(document).width() <= 700) {
                            $('#legend').css('min-height', '200px').css('max-height', '200px');
                            $('.painelCadastro').css('overflow-y', 'auto');
                            $('.painelCadastro').css('max-height', ($('body').height() - 347) + 'px');
                        } else {
                            $('.painelCadastro').css('overflow-y', 'hidden');
                            $('.painelCadastro').css('max-height', ($('html').height() - 147) + 'px');
                            $('#legend').css('max-height', ($('.Rotas').height() - 46) + 'px').css('height', ($('.Rotas').height() - 46) + 'px');
                        }

                        setInterval(function () {
                            if ($('iframe').length > 0)
                                $('iframe').contents().find('header').attr('style', 'display:none !important');
                        }, 10);

                        var map;
                        var ListaMarcadores = [];
                        var ListaRotas = [];
                        var OptionsRotas = '';
                        var ListaClientes = [];
                        var tmrOcultarObjetos;
                        var Operador = '#{valores.operador}';
                        var Solicitante = '#{login.pp.empresa}';
                        var CodFil = '#{valores.codfil}';
                        var Acao = false;

                        $('#divQuadroResumo').css('display', 'none');

                        #{valores.sobrePosicaoVariaveis}

                        function TableToJson($obj) {
                            $('#divBtProcessar, #divBtCancelar').css('display', 'none');
                            $('#divProcessando').css('display', '');

                            let arrayJson = new Array();

                            $obj.find('tbody tr:not([class*="SeparadorBlocos"])').each(function () {
                                arrayJson.push({
                                    'Numero': $(this).find('td:nth-child(2)').text(),
                                    'Rota': $(this).find('select').val(),
                                    'Hora1O': $('[id*="hora1O_label"]').text(),
                                    'CodFil': CodFil,
                                    'Solicitante': Solicitante,
                                    'Operador': Operador
                                });
                            });

                            const UrlService = 'https://mobile.sasw.com.br/SatWebService/api/ws-rotas/roteirizacao/roteirizar';

                            $.ajax({
                                url: UrlService,
                                method: 'POST',
                                data: JSON.stringify(arrayJson)
                            })
                                    .done(function (response) {
                                        $('#btCancelarRoteiro').click();

                                        if (response.status === 'ok') {
                                            $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.AcaoEfetuadaSucesso}', function () {
                                                location.reload();
                                            });
                                        } else {
                                            $('#divBtProcessar, #divBtCancelar').css('display', '');
                                            $('#divProcessando').css('display', 'none');
                                            alert('Error: ' + JSON.stringify(response));
                                        }
                                        s
                                    })
                                    .fail(function (fail) {
                                        $('#divBtProcessar, #divBtCancelar').css('display', '');
                                        $('#divProcessando').css('display', 'none');
                                        alert('Error: ' + JSON.stringify(fail));
                                    });
                        }

                        function GeraCor(Numero) {
                            var hexadecimais = (eval(Numero) * eval(Numero)) + '0123456789ABCDEF';
                            var cor = '#';

                            // Pega um número aleatório no array acima
                            for (var i = 0; i < 6; i++) {
                                //E concatena à variável cor
                                cor += hexadecimais[Math.floor(Math.random() * 16)];
                            }
                            return cor;
                        }

                        function initMap() {
                            map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                zoom: 10,
                                center: #{valores.centro},
                                gestureHandling: 'cooperative'
                            });

                        #{valores.sobrePosicaoDirectionsService}

                            var legend = document.getElementById('legend');

                        #{valores.markers}
                        #{valores.contentStrings}
                        #{valores.infoWindows}
                        #{valores.legend}
                        #{valores.sobrePosicaoDirectionsDisplaySet}

                            var onChangeHandler = function () {
                        #{valores.sobrePosicaoDirectionsCalculateDisplay}
                            };

                            onChangeHandler();

                            setTimeout(function () {
                                if ($(document).width() >= 600) {
                                    //Carregar filtro aberto
                                    $('.AbrirFiltros').click();
                                    setTimeout(function () {
                                        $('.AbrirFiltros').css('display', 'none');
                                    }, 800);
                                } else {
                                    //Deixar filtro fechado
                                    $('.AbrirFiltros').fadeIn();
                                }

                                CarregarFiltroRotas();
                                CarregrarFiltroClientes();
                            }, 1500);
                        }

                        function CarregarFiltroRotas() {
                            $('#legend').find('a').each(function () {
                                ListaRotas.push($(this).text().split('-')[0]);
                            });

                            ListaRotas.sort();
                            GerarOptionsRotas();

                            // Selector Objeto de Bairros
                            var sel = $('select[id*="cboFiltroRotas"]');
                            // Selector Lista Objeto de Bairros
                            var ul = $('div[id*="cboFiltroRotas"] ul');
                            // Selector 1º Item da Lista Objeto de Bairros
                            var li = ul.children('li').first();

                            ListaRotas.forEach(function (Item) {
                                // Criar Option
                                var option = $('<option/>').val(Item).text(Item);
                                option.appendTo(sel);

                                // Clonar item existente
                                var lic = li.clone(true);
                                lic.removeClass('ui-state-disabled ui-state-highlight').attr('data-label', Item).text(Item);
                                lic.appendTo(ul);
                            });
                        }

                        function CarregrarFiltroClientes() {
                            for (I = 0; I < ListaMarcadores.length; I++) {
                                try {
                                    if (ListaMarcadores[I].icon.indexOf('solicitacao') > -1) {
                                        if (!ListaClientes.includes(ListaMarcadores[I].title.split('\n')[0].trim()))
                                            ListaClientes.push(ListaMarcadores[I].title.split('\n')[0].trim());
                                    } else {
                                        if (!ListaClientes.includes(ListaMarcadores[I].title.split('\n')[1].split('-')[0].trim()))
                                            ListaClientes.push(ListaMarcadores[I].title.split('\n')[1].split('-')[0].trim());
                                    }
                                } catch (e) {
                                }
                            }

                            ListaClientes.sort();

                            // Selector Objeto de Bairros
                            var sel = $('select[id*="cboFiltroNomeCliente"]');
                            // Selector Lista Objeto de Bairros
                            var ul = $('div[id*="cboFiltroNomeCliente"] ul');
                            // Selector 1º Item da Lista Objeto de Bairros
                            var li = ul.children('li').first();

                            ListaClientes.forEach(function (Item) {
                                // Criar Option
                                var option = $('<option/>').val(Item).text(Item);
                                option.appendTo(sel);

                                // Clonar item existente
                                var lic = li.clone(true);
                                lic.removeClass('ui-state-disabled ui-state-highlight').attr('data-label', Item).text(Item);
                                lic.appendTo(ul);
                            });
                        }

                        function TratarDisplay() {
                            if ($('div[id*="cboFiltroRotas"] ul li[class*="ui-state-highlight"]').length > 1) {
                                $('div[id*="cboFiltroRotas"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                                $('div[id*="cboFiltroRotas"] label[id*="cboFiltroRotas_label"]').text($('div[id*="cboFiltroRotas"] ul li[class*="ui-state-highlight"]').html());
                            }
                        }

                        function TratarDisplayCliente() {
                            if ($('div[id*="cboFiltroNomeCliente"] ul li[class*="ui-state-highlight"]').length > 1) {
                                $('div[id*="cboFiltroNomeCliente"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                                $('div[id*="cboFiltroNomeCliente"] label[id*="cboFiltroNomeCliente_label"]').text($('div[id*="cboFiltroNomeCliente"] ul li[class*="ui-state-highlight"]').html());
                            }
                        }

                        function ValidarFiltro(inIndex, inReferencia) {
                            return ListaMarcadores[inIndex].icon.indexOf(inReferencia) > -1 ? true : false;
                        }

                        function ObterParamURL(name, url) {
                            if (!url)
                                url = window.location.href;
                            name = name.replace(/[\[\]]/g, "\\$&");
                            var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                                    results = regex.exec(url);
                            if (!results)
                                return null;
                            if (!results[2])
                                return '';
                            return decodeURIComponent(results[2].replace(/\+/g, " "));
                        }

                        function GerarOptionsRotas() {
                            if (!OptionsRotas) {
                                OptionsRotas = '<select id="cboPedidoRotas">';
                                OptionsRotas += '<option value="0">?</option>';

                                for (I = 0; I < ListaRotas.length; I++)
                                    OptionsRotas += '<option value="' + ListaRotas[I].split(' ')[1].trim() + '">' + ListaRotas[I].split(' ')[1].trim() + '</option>';

                                OptionsRotas += '</select>';
                            }

                            return OptionsRotas;
                        }

                        $(document).ready(function () {
                            if (ObterParamURL('pedidos') &&
                                    ObterParamURL('pedidos').trim() == 'S') {
                                $('#chkRotaProgramada, #chkVeiculos').prop('checked', false);
                                $('#chkRotaProgramada').change();
                            }
                        })
                                .on('click', '#btFecharDetalhePedido, .LightBox', function () {
                                    $('.LightBox, .divFrame').fadeOut(function () {
                                        $('.divFrame iframe').remove();
                                    });
                                })
                                .on('click', '[ref="btPedido"]', function () {
                                    $('.divFrame iframe').remove();
                                    $('.divFrame').append('<iframe src="roteirizar.xhtml?faces-redirect=true&amp;numero=' + $(this).attr('numero') + '&amp;codfil' + $(this).attr('codfil') + '"></iframe>');
                                    $('.LightBox, .divFrame').fadeIn(500, function () {
                                        $('.divFrame').fadeIn();
                                    });
                                })
                                .on('change', '.CheckBoxRota input', function () {
                                    switch ($(this).attr('ref')) {
                        #{valores.sobrePosicaoSwitch}
                                    }
                                })
                                .on('click', 'div[id*="cboFiltroRotas"] .ui-selectonemenu-trigger, div[id*="cboFiltroRotas"] .ui-selectonemenu-label', function () {
                                    TratarDisplay();
                                })
                                .on('click', 'div[id*="cboFiltroNomeCliente"] .ui-selectonemenu-trigger, div[id*="cboFiltroNomeCliente"] .ui-selectonemenu-label', function () {
                                    TratarDisplayCliente();
                                })
                                .on('click', 'div[id*="cboFiltroRotas"] ul li', function () {
                                    $('div[id*="cboFiltroRotas"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                                    $(this).addClass('ui-state-highlight');
                                    $('div[id*="cboFiltroRotas"] label[id*="cboFiltroRotas_label"]').text($(this).html());
                                    $('#chkManha').change();
                                })
                                .on('click', 'div[id*="cboFiltroNomeCliente"] ul li', function () {
                                    $('div[id*="cboFiltroNomeCliente"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                                    $(this).addClass('ui-state-highlight');
                                    $('div[id*="cboFiltroNomeCliente"] label[id*="cboFiltroNomeCliente_label"]').text($(this).html());
                                    $('#chkManha').change();
                                })
                                .on('click', 'div[id*="mapGoogle"]', function () {
                                    TratarDisplay();
                                    TratarDisplayCliente();
                                })
                                .on('keyup', '#divQuadroResumo', function () {
                                    TratarDisplay();
                                    TratarDisplayCliente();
                                })
                                .on('change', '#divQuadroResumo [type="checkbox"]', function () {
                                    for (var i = 0; i < ListaMarcadores.length; i++) {
                                        if (ListaMarcadores[i].icon.indexOf('_VEICULO') === -1) {
                                            if (($('#chkManha').prop('checked') &&
                                                    $('#chkTarde').prop('checked') &&
                                                    $('#chkVencidos').prop('checked') &&
                                                    $('#chkNoite').prop('checked')) ||
                                                    ($('#chkManha').prop('checked') && ValidarFiltro(i, '_manha')) ||
                                                    ($('#chkManha').prop('checked') && ValidarFiltro(i, '_M.png')) ||
                                                    ($('#chkTarde').prop('checked') && ValidarFiltro(i, '_tarde')) ||
                                                    ($('#chkTarde').prop('checked') && ValidarFiltro(i, '_T.png')) ||
                                                    ($('#chkVencidos').prop('checked') && ValidarFiltro(i, 'novo_icone_ATRASO')) ||
                                                    ($('#chkNoite').prop('checked') && ValidarFiltro(i, '_noite')) ||
                                                    ($('#chkNoite').prop('checked') && ValidarFiltro(i, '_N.png'))) {
                                                if (($('#chkPedidos').prop('checked') &&
                                                        $('#chkRotaProgramada').prop('checked')) ||
                                                        ($('#chkPedidos').prop('checked') && ValidarFiltro(i, '_solicitacao')) ||
                                                        ($('#chkRotaProgramada').prop('checked') && ValidarFiltro(i, 'novo_icone') && ListaMarcadores[i].icon.indexOf('novo_icone_ATRASO') === -1) ||
                                                        ($('#chkRotaProgramada').prop('checked') && ValidarFiltro(i, '_troca'))) {
                                                    if (ListaMarcadores[i].icon.indexOf('_solicitacao') > -1 ||
                                                            $('div[id*="cboFiltroRotas"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||
                                                            (ListaMarcadores[i].title.toString().split('\n')[0].trim() === $('label[id*="cboFiltroRotas_label"]').text().trim())) {


                                                        if ($('#txtPesquisarCliente').val().trim() == '')
                                                            ListaMarcadores[i].setMap(map);
                                                        else if (ListaMarcadores[i].title.indexOf($('#txtPesquisarCliente').val().toUpperCase()) > -1)
                                                            ListaMarcadores[i].setMap(map);
                                                        else
                                                            ListaMarcadores[i].setMap(null);

                                                    } else
                                                        ListaMarcadores[i].setMap(null);
                                                } else
                                                    ListaMarcadores[i].setMap(null);
                                            } else
                                                ListaMarcadores[i].setMap(null);
                                        } else if ($('#chkVeiculos').prop('checked'))
                                            ListaMarcadores[i].setMap(map);
                                        else
                                            ListaMarcadores[i].setMap(null);
                                    }
                                })
                                .on('click', '[id*="btAtualizarMapa"]', function () {
                                    //$(this).find('.fa-refresh').addClass('fa-spin fa-fw');
                                })
                                .on('click', '#btTrocarFilial', function () {
                                    top.location.href = '../param.xhtml';
                                })
                                .on('click', '.AbrirFiltros', function () {
                                    $(this).stop().animate({
                                        'left': '-50px'
                                    }, 300);

                                    setTimeout(function () {
                                        $('#divQuadroResumo').css('display', '').stop().animate({
                                            'left': '10px'
                                        }, 500);

                                        setTimeout(function () {
                                            $('.FecharFiltros').fadeIn(600);
                                        }, 800);

                                        $('.AbrirFiltros').css('display', 'none');
                                    }, 300);
                                })
                                .on('click', '.FecharFiltros', function () {
                                    $('#divQuadroResumo').stop().animate({
                                        'left': '-380px'
                                    }, 500);

                                    setTimeout(function () {
                                        $('.AbrirFiltros').css('display', '').stop().animate({
                                            'left': '0px'
                                        }, 300);

                                        $('.FecharFiltros').fadeOut();
                                        $('#divQuadroResumo').css('display', 'none');

                                    }, 500);
                                })
                                .on('dblclick', '#mapGoogle div[title*="Details"], #mapGoogle div[title*="Detalhes"], #mapGoogle div[title*="detalles"]', function () {
                                    if (Acao) {
                                        Acao = false;
                                    } else {
                                        Acao = true;
                                        let IndexMarker = $(this).attr('title').split('\n')[4].split(':')[1].split('-')[1].trim();
                                        let Imagem = ListaMarcadores[eval(IndexMarker)].icon.indexOf('manha.png') > -1 ? 'manha' : ListaMarcadores[eval(IndexMarker)].icon.indexOf('tarde.png') > -1 ? 'tarde' : ListaMarcadores[eval(IndexMarker)].icon.indexOf('noite.png') > -1 ? 'noite' : ListaMarcadores[eval(IndexMarker)].icon.indexOf('manha2.png') > -1 ? 'manha2' : ListaMarcadores[eval(IndexMarker)].icon.indexOf('tarde2.png') > -1 ? 'tarde2' : 'noite2';
                                        let ImagemTroca = '';
                                        let NumeroPedido = $(this).attr('title').split('\n')[4].split(':')[1].split('-')[0].trim();
                                        let TipoServico = $(this).attr('title').split('\n')[3].split(':')[1];

                                        if (Imagem === 'manha' ||
                                                Imagem === 'tarde' ||
                                                Imagem === 'noite') {

                                            // INSERIR

                                            switch (Imagem) {
                                                case 'manha':
                                                    ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_manha2.png';
                                                    break;

                                                case 'tarde':
                                                    ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_tarde2.png';
                                                    break;

                                                case 'noite':
                                                    ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_noite2.png';
                                                    break;
                                            }

                                            ListaMarcadores[eval(IndexMarker)].setIcon(ImagemTroca);
                                            let NomeRef = '';
                                            let HTML = '';
                                            let TemDiv = false;

                                            if (TipoServico.substr(0, 1) != 'E') {
                                                // Entrega
                                                NomeRef = $(this).attr('title').split('\n')[1].split(':')[1];
                                            } else {
                                                // Recolhimento
                                                NomeRef = $(this).attr('title').split('\n')[0].split(':')[1];
                                            }

                                            if ($('tr[ref_name="' + NomeRef + '"]').length > 0) {
                                                // Ja tem o DIV

                                            } else {
                                                // Nao tem o DIV
                                                HTML += '<tr class="SeparadorBlocos" ref_name="' + NomeRef + '"><td colspan="5">';
                                                HTML += NomeRef;
                                                HTML += '</td></tr>';
                                                $('#tblPedidosRoteirizar tbody').append(HTML);
                                                HTML = '';
                                            }

                                            if (!$('#tblPedidosRoteirizar tbody tr[numero="' + NumeroPedido + '"]').html()) {
                                                HTML = '<tr numero="' + NumeroPedido + '" index="' + IndexMarker + '" filho="' + NomeRef + '">';
                                                HTML += '   <td style="display: none">' + TipoServico + '</td>';
                                                HTML += '   <td style="min-width:250px !important;width:200px !important;max-width:250px !important;"><i class="fa fa-minus-square" style="cursor:pointer; color:red !important;"></i>&nbsp;&nbsp;&nbsp;' + $(this).attr('title').split('\n')[0].split(':')[1] + '</td>';
                                                HTML += '   <td style="min-width:250px !important;width:200px !important;max-width:250px !important;">' + $(this).attr('title').split('\n')[1].split(':')[1] + '</td>';
                                                HTML += '   <td style="min-width:85px !important;width:85px !important;max-width:85px !important; text-align:center">' + NumeroPedido + '</td>';
                                                HTML += '   <td style="min-width:85px !important;width:85px !important;max-width:85px !important; text-align:center">' + $(this).attr('title').split('\n')[2].split(':')[1] + ':' + $(this).attr('title').split('\n')[2].split(':')[2] + ':' + $(this).attr('title').split('\n')[2].split(':')[3] + '</td>';
                                                HTML += '   <td style="min-width:70px !important;width:70px !important;max-width:70px !important; text-align:center">' + GerarOptionsRotas() + '</td>';
                                                HTML += '</tr>';

                                                if($('#tblPedidosRoteirizar tbody tr[filho="' + NomeRef + '"]').length == 0){
                                                    $(HTML).insertAfter($('#tblPedidosRoteirizar tbody tr[ref_name="' + NomeRef + '"]'));
                                                }
                                                else{
                                                    $(HTML).insertAfter($('#tblPedidosRoteirizar tbody tr[filho="' + NomeRef + '"]').last());
                                                }
                                                        
                                            }
                                            $('#btAutoRoteiriza').find('i').attr('class', 'fa fa-cog')
                                            $('#tblPedidosRoteirizar').parent('div').parent('div').css('display', '');

                                            $('.gm-style-iw').parent('div').css('display', 'none');


                                        } else {
                                            // REMOVER
                                            let FilhoRef = $('#tblPedidosRoteirizar tbody tr[numero="' + NumeroPedido + '"]').attr('filho');
                                            $('#tblPedidosRoteirizar tbody tr[numero="' + NumeroPedido + '"]').remove();
                                            
                                            if($('#tblPedidosRoteirizar tbody tr[filho="' + FilhoRef + '"]').length == 0){
                                                $('#tblPedidosRoteirizar tbody tr[ref_name="' + FilhoRef + '"]').remove();
                                            }

                                            if ($('#tblPedidosRoteirizar tbody tr:not(.SeparadorBlocos)').length === 0) {
                                                $('#btCancelarRoteiro').click();
                                            }

                                            switch (Imagem) {
                                                case 'manha2':
                                                    ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_manha.png';
                                                    break;

                                                case 'tarde2':
                                                    ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_tarde.png';
                                                    break;

                                                case 'noite2':
                                                    ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_noite.png';
                                                    break;
                                            }

                                            ListaMarcadores[eval(IndexMarker)].setIcon(ImagemTroca);
                                        }

                                        setTimeout(function () {
                                            Acao = false;
                                        }, 1000);
                                    }
                                })
                                .on('click', '#btCancelarRoteiro', function () {
                                    $('#tblPedidosRoteirizar').parent('div').parent('div').css('display', 'none');
                                    $('#tblPedidosRoteirizar tbody tr').remove();

                                    for (I = 0; I < ListaMarcadores.length; I++) {
                                        if (ListaMarcadores[I].icon.indexOf('manha2.png') > -1 ||
                                                ListaMarcadores[I].icon.indexOf('tarde2.png') > -1 ||
                                                ListaMarcadores[I].icon.indexOf('noite2.png') > -1) {

                                            if (ListaMarcadores[I].icon.indexOf('manha2.png') > -1)
                                                ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_manha.png';
                                            else if (ListaMarcadores[I].icon.indexOf('tarde2.png') > -1)
                                                ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_tarde.png';
                                            else
                                                ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_noite.png';

                                            ListaMarcadores[I].setIcon(ImagemTroca);
                                        }
                                    }
                                })
                                .on('change', '#tblPedidosRoteirizar tbody tr td select', function () {
                                    let index = $(this).parent('td').parent('tr').index();
                                    let value = $(this).val();

                                    $('#tblPedidosRoteirizar tbody tr').each(function () {
                                        if ($(this).index() > index /*&& 
                                         $(this).find('select').val() === '0'*/)
                                            $(this).find('select').val(value);
                                    });
                                })
                                .on('click', '#tblPedidosRoteirizar tbody tr td i[class*="minus"]', function () {
                                    let IndexMarker = $(this).parent('td').parent('tr').attr('index');
                                    let Imagem = ListaMarcadores[eval(IndexMarker)].icon.indexOf('manha') ? 'manha' : ListaMarcadores[eval(IndexMarker)].icon.indexOf('tarde') ? 'tarde' : 'noite';
                                    let ImagemTroca = '';

                                    switch (Imagem) {
                                        case 'manha':
                                            ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_manha.png';
                                            break;

                                        case 'tarde':
                                            ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_tarde.png';
                                            break;

                                        case 'noite':
                                            ImagemTroca = 'https://mobile.sasw.com.br:9091/satmobile/img/icone_solicitacao_noite.png';
                                            break;
                                    }

                                    ListaMarcadores[eval(IndexMarker)].setIcon(ImagemTroca);

                                    let FilhoRef = $(this).parent('td').parent('tr').attr('filho');
                                    $(this).parent('td').parent('tr').remove();

                                    if($('#tblPedidosRoteirizar tbody tr[filho="' + FilhoRef + '"]').length == 0){
                                        $('#tblPedidosRoteirizar tbody tr[ref_name="' + FilhoRef + '"]').remove();
                                    }                                    

                                    if ($('#tblPedidosRoteirizar tbody tr').length === 0)
                                        $('#btCancelarRoteiro').click();
                                })
                                .on('click', '#btAutoRoteiriza', function () {
                                    if (Acao) {
                                        Acao = false;
                                    } else {
                                        Acao = true;
                                        if ($('#btAutoRoteiriza').find('i').attr('class').indexOf('spin') === -1) {
                                            if (!$('[id*="hora1O_label"]').text()) {
                                                $('[id*="btMsgPrimeiroHora"]').click();
                                            } else if ($('#tblPedidosRoteirizar tbody tr td select option[value="0"]:selected').length > 0) {
                                                $('[id*="btMsgSelecioneRotas"]').click();
                                            } else {
                                                TableToJson($('#tblPedidosRoteirizar'));
                                            }
                                        }

                                        setTimeout(function () {
                                            Acao = false;
                                        }, 1000);
                                    }
                                })
                                .on('keyup', '#txtPesquisarCliente', function () {
                                    $('#chkManha').change();
                                })
                                ;

                        $(window).resize(function () {
                            if ($(document).width() <= 700) {
                                $('#legend').css('min-height', '200px').css('max-height', '200px');
                                $('.painelCadastro').css('overflow-y', 'auto');
                                $('.painelCadastro').css('max-height', ($('body').height() - 317) + 'px');
                            } else {
                                $('.painelCadastro').css('overflow-y', 'hidden');
                                $('.painelCadastro').css('max-height', ($('html').height() - 147) + 'px');
                                $('#legend').css('height', ($('.Rotas').height() - 46) + 'px');
                            }
                        });



                        function OrderLinha() {
                            let col = 5;
                            $trRef = $('#tblPedidosRoteirizar th:nth-child(4)');

                            $trRef.removeClass('asc, desc');

                            $trRef.siblings().removeClass('asc selected');
                            $trRef.siblings().removeClass('desc selected');
                            var arrData = $('#tblPedidosRoteirizar').find('tbody >tr:has(td)').get();
                            arrData.sort(function (a, b) {
                                var val1 = $(a).children('td').eq(col).text().toUpperCase();
                                var val2 = $(b).children('td').eq(col).text().toUpperCase();

                                if ($.isNumeric(val1) && $.isNumeric(val2))
                                    return sortOrder == 1 ? val1 - val2 : val2 - val1;
                                else
                                    return (val1 < val2) ? -sortOrder : (val1 > val2) ? sortOrder : 0;
                            });
                            $.each(arrData, function (index, row) {
                                $('tbody').append(row);
                            });
                        }


                        // ]]>
                    </script>
                    <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>
                </h:form>

                <h:form id="impressao">
                    <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" class="box-primary">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.Guia}" style="float: left"/>

                            <p:commandLink title="#{localemsgs.Imprimir}" style="position: absolute; width:10px !important; right: 70px;">
                                <i class="fa fa-print"></i>
                                <p:printer target="guiaimpressa"/>
                            </p:commandLink>

                            <p:commandLink title="#{localemsgs.Download}" update="msgs" ajax="false"
                                           actionListener="#{valores.gerarGuiaDownload}" style="position: absolute; width:10px !important; right: 100px;">
                                <i class="fa fa-download"></i>
                                <p:fileDownload value="#{valores.arquivoDownload}"/>
                            </p:commandLink>

                        </f:facet>

                        <p:panel class="guiaimpressa" styleClass="guiaimpressa"  style="padding:12px !important; border:thin solid #DDD !important; overflow:auto !important;max-height: 500px !important">
                            <h:outputText id="guiaimpressa" value="#{valores.html}" escape="false"/>
                        </p:panel>
                    </p:dialog>
                </h:form>
            </div>
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <!--<div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>-->
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

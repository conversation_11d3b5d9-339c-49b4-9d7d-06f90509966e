/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import SasBeans.Veiculos;
import static br.com.sasw.utils.Messages.getMessageS;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(value = "conversorTipoVeiculo")
public class ConversorTipoVeiculo implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        Veiculos veiculos = new Veiculos();
        veiculos.setPlaca(value);

        return veiculos;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            String tipo = (String) value, tipoVeiculo;
            switch (tipo) {
                case "F":
                    tipoVeiculo = getMessageS("CarroForte");
                    break;
                case "L":
                    tipoVeiculo = getMessageS("CarroLeve");
                    break;
                case "M":
                    tipoVeiculo = getMessageS("Moto");
                    break;
                case "P":
                    tipoVeiculo = getMessageS("Pesado");
                    break;
                case "B":
                    tipoVeiculo = getMessageS("Blindado");
                    break;
                case "R":
                    tipoVeiculo = getMessageS("Particular");
                    break;
                default:
                    tipoVeiculo = tipo;
            }
            return tipoVeiculo;
        } catch (Exception e) {
            return null;
        }
    }

}

<ui:composition
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:f="http://java.sun.com/jsf/core"
        xmlns:p="http://primefaces.org/ui"
        xmlns:o="http://omnifaces.org/ui"
        xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
>
<style>
        [id*="formContrato"] label{
            width: 100% !important
        }

        [id*="formContrato"] .ui-inputfield{
            width: 100% !important;
        }

        [id*="formContrato"]  div{
            padding-right: 0px;
        }

        [id*="formContrato"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
            background-color:#FFF !important;
            background:#FFF !important;
            border-bottom-color: #CCC !important;
        }
        
        [id*="formContrato"] [id*="cadastrar"] .row{
            padding: 0px !important;
            width: 100% !important;
        }
        
        [id*="formContrato"] [id*="cadastrar"] .row > div{
            padding-top: 8px !important
        }
    </style>
    <h:form id="main">
        <div class="ui-grid ui-grid-responsive FundoPagina"
             style="overflow:hidden !important; padding-right:12px !important;">
            <div class="ui-grid-row">
                <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                    <p:panel style="display: inline;">
                        <p:dataTable
                                id="tabela"
                                value="#{tesEntrada.lazyTesEntradas}"
                                selection="#{tesEntrada.numerarioClick}"
                            rowStyleClass="#{numerario.situacao eq 'S' ? 'contratovencidoRow' : null}"
                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Numerarios}"
                            var="numerario"
                            lazy="true"
                            selectionMode="single"
                            emptyMessage="#{localemsgs.SemRegistros}"
                            paginator="true"
                            rows="15"
                            reflow="true"
                            rowsPerPageTemplate="5,10,15, 20, 25"
                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                            styleClass="tabela"
                            scrollable="true"
                            scrollWidth="100%"
                            paginatorPosition="top"
                            class="tabela DataGrid"
                            style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                            >
                            <p:ajax
                                event="rowDblselect"
                                listener="#{tesEntrada.ativarModalEdicao()}"
                                update="formContrato msgs"/>
                            <p:column headerText="#{localemsgs.TipoMov}">
                                <h:outputText value="#{numerario.tipoMov}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Lote}">
                                <h:outputText value="#{numerario.lote}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Situacao}">
                                <h:outputText value="#{numerario.situacao}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Guia}">
                                <h:outputText value="#{numerario.guia}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Serie}">
                                <h:outputText value="#{numerario.serie}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.NRedOri}">
                                <h:outputText value="#{numerario.NRed1}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.NRedDst}">
                                <h:outputText value="#{numerario.NRed2}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Valor}">
                                <h:outputText value="#{numerario.valor}" converter="conversormoeda"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Qtde}">
                                <h:outputText value="#{numerario.qtde}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.QtdeVol}">
                                <h:outputText value="#{numerario.qtdeVol}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ContaTes}">
                                <h:outputText value="#{numerario.contaTes}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ContaDesc}">
                                <h:outputText value="#{numerario.contaDesc}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Data}">
                                <h:outputText value="#{numerario.data}"
                                              converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Remessa}">
                                <h:outputText value="#{numerario.remessa}" converter="conversor0"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Operador}">
                                <h:outputText value="#{numerario.operador}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}">
                                <h:outputText value="#{numerario.dt_Alter}"
                                              converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}">
                                <h:outputText value="#{numerario.hr_Alter}"
                                              converter="conversorHora"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.OperIncl}">
                                <h:outputText value="#{numerario.operIncl}"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Incl}">
                                <h:outputText value="#{numerario.dt_Incl}"
                                              converter="conversorData"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Incl}">
                                <h:outputText value="#{numerario.hr_Incl}"
                                              converter="conversorHora"/>
                            </p:column>
                        </p:dataTable>

                        <script>
                            // <![CDATA[
                            $(document).ready(function () {
                                if ($(document).width() <= 700)
                                    $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                else
                                    $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                            });

                            $(window).resize(function () {
                                if ($(document).width() <= 700)
                                    $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                else
                                    $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                            });
                            // ]]>
                        </script>
                    </p:panel>
                </div>
            </div>
        </div>

        <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.Adicionar}"
                               actionListener="#{tesEntrada.ativarModalCadastro()}"
                               update="msgs formContrato" >
                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                </p:commandLink>
            </div>
            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.Editar}"
                               actionListener="#{tesEntrada.ativarModalEdicao()}"
                               update="msgs formContrato">
                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                </p:commandLink>
            </div>

            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.Pesquisar}"
                               actionListener="#{tesEntrada.ativarModalPesquisa()}"
                               update="formPesquisa msgs main">
                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                </p:commandLink>
            </div>

            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.LimparFiltros}"
                               action="#{tesEntrada.limparPesquisa()}"
                               update="main msgs">
                    <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                </p:commandLink>
            </div>
        </p:panel>
    </h:form>
    
    
    <h:form id="formContrato" class="form-inline">
        <p:dialog
            widgetVar="dlgCadastrar"
            positionType="absolute"
            responsive="true"
            draggable="false"
            modal="true"
            closable="true"
            resizable="false"
            dynamic="true"
            showEffect="drop"
            hideEffect="drop"
            closeOnEscape="false"
            style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;"
            >
            <f:facet name="header">
                <img src="../assets/img/icone_tesouraria_entradas.png" height="40" width="40"/>
                <p:spacer width="5px"/>
                <h:outputText value="#{localemsgs.CadastrarNumerario}" style="color:#022a48" />
            </f:facet>
            <script>
                $(document).ready(function () {
                    //first unbind the original click event
                    PF('dlgCadastrar').closeIcon.unbind('click');

                    //register your own
                    PF('dlgCadastrar').closeIcon.click(function (e) {
                        $("#formContrato\\:fecharFormContrato").click();
                        //should be always called
                        e.preventDefault();
                    });
                });
            </script>

            <p:commandButton
                id="fecharFormContrato"
                style="display: none"
                oncomplete="PF('dlgCadastrar').hide()">
                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
            </p:commandButton>

            <p:panel id="cadastrar" style="background-color: transparent;" styleClass="cadastrar">
                <p:confirmDialog global="true">
                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                </p:confirmDialog>

                <div class="content-fluid" style="padding-right: 15px !important">
                    <div class="row">
                        <div class="col-md-7 col-sm-7 col-xs-12">
                            <p:outputLabel for="codfil"
                                           value="#{localemsgs.CodFil}"/>

                            <p:selectOneMenu
                                id="codfil"
                                value="#{tesEntrada.filialSelecionada}"
                                converter="omnifaces.SelectItemsConverter"
                                required="true"
                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                styleClass="filial"
                                style="width: 100%"
                                filter="true"
                                filterMatchMode="contains" >
                                <f:selectItems value="#{login.filiais}"
                                               var="filial"
                                               itemValue="#{filial}"
                                               itemLabel="#{filial.descricao}"
                                               noSelectionValue=""/>
                                <p:ajax event="itemSelect"
                                        update="msgs @form:cadastrar"
                                        process="@this"
                                        partialSubmit="true"
                                        listener="#{tesEntrada.preencherCampos}"/>
                            </p:selectOneMenu>
                        </div>
                        <div class="col-md-5 col-sm-5 col-xs-12">
                            <p:outputLabel for="movimento"
                                           value="#{localemsgs.Movimentacao}"/>

                            <p:selectOneMenu
                                id="movimento"
                                value="#{tesEntrada.numerarioEdicao.tipoMov}"
                                converter="omnifaces.SelectItemsConverter"
                                required="true"
                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                styleClass="filial"
                                style="width: 100%"
                                filter="true"
                                filterMatchMode="contains" >
                                <f:selectItem itemLabel="1 - #{localemsgs.Diurno}"
                                              itemValue="1"/>
                                <f:selectItem itemLabel="2 - #{localemsgs.Noturno}"
                                              itemValue="2"/>
                                <f:selectItem itemLabel="3 - #{localemsgs.Vespertino}"
                                              itemValue="3"/>
                            </p:selectOneMenu>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-12">
                            <p:outputLabel for="situacao"
                                           value="#{localemsgs.Situacao}"/>
                            <p:inputText id="situacao"
                                         value="#{tesEntrada.numerarioEdicao.situacao}"
                                         disabled="true"
                                         maxlength="1"
                                         style="width: 100%;"
                                         >
                                <f:validateLength minimum="1" maximum="1" />
                            </p:inputText>
                        </div>

                        <div class="col-md-3 col-sm-3 col-xs-7">
                            <p:outputLabel for="guia"
                                           value="#{localemsgs.Guia}"
                                           indicateRequired="false"/>
                            <p:inputNumber id="guia"
                                           value="#{tesEntrada.numerarioEdicao.guia}"
                                           style="width: 100% !important"
                                           maxValue="999999999"
                                           decimalPlaces="0" thousandSeparator="">
                                <f:validateLength minimum="1" maximum="9" />
                                <p:ajax event="change"
                                        update="@form:cadastrar msgs"
                                        listener="#{tesEntrada.preencherCampos}"/>
                            </p:inputNumber>
                        </div>

                        <div class="col-md-2 col-sm-2 col-xs-5">
                            <p:outputLabel for="serie"
                                           value="#{localemsgs.Serie}"/>

                            <p:inputText id="serie"
                                         value="#{tesEntrada.numerarioEdicao.serie}"
                                         onblur="value = value.toUpperCase()"
                                         style="width: 100%">
                                <f:validateLength minimum="1" maximum="5" />
                                <p:ajax event="change"
                                        update="@form:cadastrar msgs"
                                        listener="#{tesEntrada.analisarSerie}"/>
                            </p:inputText>
                        </div>

                        <div class="col-md-5 col-sm-5 col-xs-7">
                            <p:outputLabel for="data"
                                           value="#{localemsgs.Data}"
                                           indicateRequired="false"/>
                            <p:inputMask id="data"
                                         value="#{tesEntrada.numerarioEdicao.data}"
                                         mask="99/99/9999"
                                         required="true"
                                         label="#{localemsgs.Data}"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                         style="width: 100%"
                                         maxlength="8"
                                         placeholder="#{mascaras.padraoData}"
                                         converter="conversorData"/>
                        </div>
                    </div>

                    <div class="row">

                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <p:outputLabel for="codCli1"
                                           value="#{localemsgs.CliOri}: "
                                           indicateRequired="false"/>

                            <p:autoComplete
                                autocomplete="off"
                                id="codCli1"
                                value="#{tesEntrada.clienteSelecionado}"
                                var="cliente"
                                itemValue="#{cliente}"
                                itemLabel="#{cliente.NRed}"
                                completeMethod="#{tesEntrada.buscarCliente}"
                                emptyMessage="#{localemsgs.SemRegistros}"
                                forceSelection="true"
                                scrollHeight="200"
                                minQueryLength="2"
                                maxlength="25"
                                style="width: 100%">
                                <p:ajax event="itemSelect"
                                        listener="#{tesEntrada.selecionarCliente()}"
                                        update="msgs @form:cadastrar"/>
                                <o:converter converterId="omnifaces.ListConverter" list="#{tesEntrada.listaClientes}"/>
                            </p:autoComplete>
                        </div>
                        <div class="col-md-5 col-sm-5 col-xs-5" style="display: none">
                            <h:outputText
                                id="clienteContratanteNome"
                                value="#{tesEntrada.numerarioEdicao.clienteOrigem.NRed}"/>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <p:outputLabel for="codCli2"
                                           value="#{localemsgs.TesourariaDestino}: "
                                           indicateRequired="false"/>

                            <p:autoComplete
                                autocomplete="off"
                                id="codCli2"
                                value="#{tesEntrada.tesourariaSelecionada}"
                                var="cliente2"
                                itemValue="#{cliente2}"
                                itemLabel="#{cliente2.NRed}"
                                completeMethod="#{tesEntrada.buscarTesouraria}"
                                emptyMessage="#{localemsgs.SemRegistros}"
                                forceSelection="true"
                                scrollHeight="200"
                                minQueryLength="2"
                                maxlength="25"
                                style="width: 100%">
                                <p:ajax event="itemSelect"
                                        listener="#{tesEntrada.selecionarTesouraria}"
                                        update="msgs @form:cadastrar"/>
                                <o:converter converterId="omnifaces.ListConverter" list="#{tesEntrada.listaClientes}" />
                            </p:autoComplete>
                        </div>
                        <div class="col-md-5 col-sm-5 col-xs-5" style="display: none">
                            <h:outputText
                                id="nomeCli2"
                                value="#{tesEntrada.numerarioEdicao.clienteDestino.codigo}"/>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-2 col-sm-2 col-xs-4">
                            <p:outputLabel for="os_caller0"
                                           value="#{localemsgs.OS}: "
                                           indicateRequired="false"/>

                            <p:inputNumber
                                id="os_caller0"
                                value="#{tesEntrada.numerarioEdicao.os_vig.OS}"
                                label="#{localemsgs.OS}"
                                class="inputNumber"
                                maxValue="99999999"
                                decimalPlaces="0"
                                />

                            <p:commandLink
                                action="#{tesEntrada.ativarModalOS}"
                                update="msgs @form:os_caller"
                                process="@this"
                                title="#{localemsgs.OS}" rendered="false">
                                <p:graphicImage
                                    url="../assets/img/icone_redondo_pesquisar.png"
                                    width="25" height="25" style="float: right; position: absolute; right:6px; top: 24px" />
                            </p:commandLink>
                        </div>
                        <div class="col-md-10 col-sm-10 col-xs-8" style="padding-top: 28px !important">
                            <p:inputText
                                id="os_caller1"
                                value="#{tesEntrada.numerarioEdicao.os_vig.descricao}"
                                disabled="true"
                                size="7"
                                style="width: 100%"/>
                        </div>
                    </div>


                    <h:panelGroup id="os_caller" layout="block">
                        <div class="row">

                            <div class="col-md-2 col-sm-2 col-xs-2" style="text-align: right">
                                <p:outputLabel for="os_caller1"
                                               value="#{localemsgs.Trajeto}: "
                                               indicateRequired="false" style="margin-top: 7px"/>
                            </div>
                            <div class="col-md-5" style="padding-left: 15px">
                                <p:inputText id="os_caller2"
                                             value="#{tesEntrada.numerarioEdicao.os_vig.NRed}"
                                             disabled="true"
                                             style="width: 100%"/>
                            </div>
                            <div class="col-md-5 col-sm-5 col-md-10">
                                <p:inputText
                                    id="os_caller3"
                                    value="#{tesEntrada.numerarioEdicao.os_vig.NRedDst}"
                                    disabled="true"
                                    style="width: 100%"/>
                            </div>
                            
                        </div>
                        <div class="row" >
                            <div class="col-md-2 col-sm-2 col-xs-2" style="text-align: right">
                                <p:outputLabel
                                    for="os_caller1"
                                    value="#{localemsgs.Faturar}: "
                                    indicateRequired="false" style="margin-top: 7px"/>
                            </div>
                            <div class="col-md-10 col-sm-10 col-xs-10" style="padding-left: 15px">
                                <p:inputText
                                    id="os_caller4"
                                    value="#{tesEntrada.numerarioEdicao.os_vig.NRedFat}"
                                    disabled="true"
                                    style="width: 100%"/>
                            </div>
                        </div>

                    </h:panelGroup>

                    <h:panelGroup id="contaInfo" layout="block" >
                        <div class="row">
                            <div class="col-md-2 col-sm-2 col-xs-2" style="text-align: right">
                                <p:outputLabel for="contas"
                                               value="#{localemsgs.Conta}: "
                                               indicateRequired="false" style="margin-top: 7px"/>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-10">
                                <p:selectOneMenu
                                id="contas"
                                value="#{tesEntrada.numerarioEdicao.conta}"
                                converter="omnifaces.SelectItemsConverter"
                                required="true"
                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Conta}"
                                styleClass="filial"
                                style="width: 100%"
                                filter="true"
                                filterMatchMode="contains" >
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{tesEntrada.listaContas}"
                                               var="contas"
                                               itemValue="#{contas}"
                                               itemLabel="#{contas.descricao}"
                                               noSelectionValue=""/>
                            </p:selectOneMenu>
                            </div>
                        </div>
                    </h:panelGroup>

                    <h:panelGroup id="loteInfo" layout="block">
                        <div class="row">
                            <div class="col-md-2 col-sm-2 col-xs-2" style="text-align: right">
                                <p:outputLabel for="lotes"
                                               value="#{localemsgs.Lote}: "
                                               indicateRequired="false" style="margin-top: 7px"/>
                            </div>
                            <div class="col-md-5 col-sm-5 col-xs-10">
                                <p:selectOneMenu
                                id="lotes"
                                value="#{tesEntrada.numerarioEdicao.tesLote}"
                                required="true"
                                requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Lote}"
                                styleClass="filial"
                                style="width: 100%"
                                filter="true"
                                filterMatchMode="contains" >
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" noSelectionOption="true"/>
                                    <f:selectItems value="#{tesEntrada.listaLotes}"
                                               var="lotes"
                                               itemValue="#{lotes}"
                                               itemLabel="#{lotes.descricao}"
                                               noSelectionValue=""/>
                            </p:selectOneMenu>
                            </div>
                        </div>
                    </h:panelGroup>

                    <div class="row">
                        <div class="col-md-2 col-sm-2 col-xs-2" style="text-align: right">
                            <p:outputLabel for="valor"
                                           value="#{localemsgs.Valor}: "
                                           indicateRequired="false" style="margin-top: 7px"/>
                        </div>
                        <div class="col-md-4">
                            <p:inputText
                                    id="valor"
                                    value="#{tesEntrada.numerarioEdicao.valor}"
                                    label="#{localemsgs.Valor}"
                                    disabled="true"
                                    style="width: 100%" converter="conversormoeda"/>
                        </div>
                        <div class="col-md-2 col-sm-2 col-xs-19">
                            <p:outputLabel for="envelopes"
                                           value="#{localemsgs.QtdEnvelopes}: "
                                           indicateRequired="false" style="margin-top: 7px"/>
                        </div>
                        <div class="col-md-4">
                            <!-- TODO -->
                            <p:inputNumber
                                id="envelopes"
                                value="0000"
                                label="#{localemsgs.Valor}"
                                class="inputNumber"
                                maxValue="9999"
                                decimalPlaces="0"
                                />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <p:outputLabel for="volumes"
                                           value="#{localemsgs.Volumes}"
                                           indicateRequired="false"/>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <p:dataTable
                                id="volumes"
                                value="#{tesEntrada.cxfGuiasVolLista}"
                                currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Volumes}"
                                var="volume"
                                emptyMessage="#{localemsgs.SemRegistros}"
                                paginator="#{tesEntrada.cxfGuiasVolLista.size() > 15}"
                                rows="15"
                                reflow="true"
                                rowsPerPageTemplate="5,10,15, 20, 25"
                                paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                styleClass="tabela"
                                scrollable="true"
                                scrollWidth="100%"
                                paginatorPosition="top"
                                class="tabela DataGrid"
                                style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                                >
                                <p:column headerText="#{localemsgs.Ordem}" class="text-center">
                                    <h:outputText value="#{volume.ordem}" converter="conversor0" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Qtde}" class="text-center">
                                    <h:outputText value="#{volume.qtde}" converter="conversor0" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Descricao}" class="text-center">
                                    <h:outputText value="#{volume.tipo}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Lacre}" class="text-center">
                                    <h:outputText value="#{volume.lacre}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                    <h:outputText value="#{volume.valor}" converter="conversormoeda" class="text-center"/>
                                </p:column>
                            </p:dataTable>
                        </div>
                    </div>

                    <div class="form-inline">
                        <p:commandLink
                            id="cadastro"
                            action="#{tesEntrada.gravarEntrada}"
                            update="msgs main"
                            title="#{tesEntrada.editandoEntrada ? localemsgs.editar : localemsgs.Cadastrar}">
                            <p:graphicImage
                                url="#{tesEntrada.editandoEntrada
                                       ? '../assets/img/icone_adicionar.png'
                                       : '../assets/img/icone_adicionar.png'}"
                                width="40" height="40" />
                        </p:commandLink>
                    </div>
                </div>
            </p:panel>

            <p:confirmDialog message="#{localemsgs.AlteracaoClienteContratante}" header="#{localemsgs.Confirmacao}"
                             showEffect="drop" width="300" widgetVar="cdglClienteContratante"
                             hideEffect="drop">
                <p:commandButton value="#{localemsgs.Sim}"
                                 styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                 action="#{tesEntrada.editarContrato}"
                                 oncomplete="PF('cdglClienteContratante').hide()"
                                 update="msgs main cabecalho"/>
                <p:commandButton value="#{localemsgs.Nao}" action="#{acessos.cadastrar}"
                                 styleClass="ui-confirmdialog-no" icon="ui-icon-close"
                                 oncomplete="PF('cdglClienteContratante').hide()"/>
            </p:confirmDialog>
        </p:dialog>
    </h:form>

</ui:composition>
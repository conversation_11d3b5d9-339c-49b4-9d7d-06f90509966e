/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;

/**
 *
 * <AUTHOR>
 */
public class EventoMsgWhatsAppDao {

    public String inserirMensagem(String event, String token, String chat_body, String contact_number, String contact_server,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "DECLARE @Sequencia Float;\n"
                    + "\n"
                    + "SET @Sequencia = (SELECT ISNULL(MAX(Sequencia),0) + 1 FROM EventoMsgWhatsapp);\n"
                    + "\n"
                    + "SET NOCOUNT ON\n"
                    + "\n"
                    + "INSERT INTO EventoMsgWhatsapp (Sequencia, Parametro,  Event, Token, Chat_Body, Contact_Number, Contact_Server, Chat_ACK, MUID)\n"
                    + "VALUES (@Sequencia, 'SASW', ?, ?, ?, ?, ?, '0', @Sequencia);\n"
                    + "\n"
                    + "SELECT EventoMsgWhatsapp.Sequencia\n"
                    + "FROM EventoMsgWhatsapp\n"
                    + "WHERE EventoMsgWhatsapp.Sequencia = @Sequencia;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(event);
            consulta.setString(token);
            consulta.setString(chat_body);
            consulta.setString(contact_number);
            consulta.setString(contact_server);
            consulta.select();
            String sequencia = null;
            if (consulta.Proximo()) {
                sequencia = consulta.getString("Sequencia").replace(".0", "");
            }
            consulta.close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("EventoMsgWhatsAppDao.inserirMensagem - " + e.getMessage() + "\r\n"
                    + "DECLARE @Sequencia Float;\n"
                    + "\n"
                    + "SET @Sequencia = (SELECT ISNULL(MAX(Sequencia),0) + 1 FROM EventoMsgWhatsapp);\n"
                    + "\n"
                    + "SET NOCOUNT ON\n"
                    + "\n"
                    + "INSERT INTO EventoMsgWhatsapp (Sequencia, Parametro,  Event, Token, Chat_Body, Contact_Number, Contact_Server, Chat_ACK, MUID)\n"
                    + "VALUES (@Sequencia, 'SASW', " + event + ", " + token + ", " + chat_body + ", " + contact_number + ", " + contact_server + ", '0', @Sequencia);\n"
                    + "\n"
                    + "SELECT EventoMsgWhatsapp.Sequencia\n"
                    + "FROM EventoMsgWhatsapp\n"
                    + "WHERE EventoMsgWhatsapp.Sequencia = @Sequencia;");
        }
    }

    public String inserirMensagem(String event, String chat_body, String linkImagem, String contact_number, String contact_server,
            String param, String codFil, String operador, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DECLARE @Sequencia Float;\n"
                    + "\n"
                    + "SET @Sequencia = (SELECT ISNULL(MAX(Sequencia),0) + 1 FROM EventoMsgEnvioWP);\n"
                    + "\n"
                    + "SET NOCOUNT ON\n"
                    + "\n"
                    + "INSERT INTO EventoMsgEnvioWP (Sequencia, Parametro, CodFil, Evento, Destino, Situacao, Operador, Dt_Alter, Hr_Alter\n";

            switch (event.toUpperCase()) {
                case "LINK":
                    sql += ",Link";
                    break;
                case "CHAT":
                    sql += ",Msg";
                    break;
                case "MEDIA":
                    sql += ",Msg, Imagem";
                    break;
            }

            sql += ") VALUES (@Sequencia, ?,?,?,?,?,?,?,?";

            switch (event.toUpperCase()) {
                case "LINK":
                    sql += ",?";

                    if (null != linkImagem && !linkImagem.equals("")) {
                        sql += ",?";
                    }

                    break;

                case "CHAT":
                    sql += ",?";

                    break;
                case "MEDIA":
                    sql += ",?,?";

                    break;
            }

            sql += ");\n";
            sql += "SELECT @Sequencia AS Sequencia;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(param);
            consulta.setString(codFil);
            //consulta.setString(chat_body);
            consulta.setString("Envio de Mensagem");
            consulta.setString(contact_number);
            consulta.setString("N");
            consulta.setString(operador);
            consulta.setString(getDataAtual("SQL"));
            consulta.setString(getDataAtual("HORA"));

            switch (event.toUpperCase()) {
                case "LINK":
                    consulta.setString(chat_body);

                    if (null != linkImagem && !linkImagem.equals("")) {
                        consulta.setString(linkImagem);
                    }

                    break;

                case "CHAT":
                    consulta.setString(chat_body);
                    break;

                case "MEDIA":
                    consulta.setString(chat_body);
                    consulta.setString(linkImagem);
                    break;
            }

            consulta.select();
            String sequencia = null;
            if (consulta.Proximo()) {
                sequencia = consulta.getString("Sequencia").replace(".0", "");
            }
            consulta.close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("EventoMsgWhatsAppDao.inserirMensagem - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public void atualizarMensagem(String sequencia, String ack, String id, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE\n"
                    + "     EventoMsgWhatsApp\n"
                    + " SET\n"
                    + "     Chat_ACK = ?, chat_uid = ?\n"
                    + "WHERE\n"
                    + "     Sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ack);
            consulta.setString(id);
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EventoMsgWhatsAppDao.atualizarMensagem - " + e.getMessage() + "\r\n"
                    + " UPDATE\n"
                    + "     EventoMsgWhatsApp\n"
                    + " SET\n"
                    + "     Chat_ACK = " + ack + ", chat_uid = " + id + "\n"
                    + "WHERE\n"
                    + "     Sequencia =" + sequencia);
        }
    }

    public void atualizarMensagem(String sequencia, String chat_dtm, String chat_uid, String chat_dir, String ack, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE\n"
                    + "     EventoMsgWhatsApp\n"
                    + " SET\n"
                    + "     chat_dtm = ?, chat_uid= ?, chat_dir = ?, Chat_ACK = ?, MUID = ? \n"
                    + "WHERE\n"
                    + "     Sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(chat_dtm);
            consulta.setString(chat_uid);
            consulta.setString(chat_dir);
            consulta.setString(ack);
            consulta.setString(sequencia);
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EventoMsgWhatsAppDao.atualizarMensagem - " + e.getMessage() + "\r\n"
                    + " UPDATE\n"
                    + "     EventoMsgWhatsApp\n"
                    + " SET\n"
                    + "     chat_dtm = " + chat_dtm + ", chat_uid= " + chat_uid + ", chat_dir = " + chat_dir + ", Chat_ACK = " + ack + ", MUID = " + sequencia + "\n"
                    + "WHERE\n"
                    + "     Sequencia =" + sequencia);
        }
    }

    public void atualizarMensagem(String sequencia, String contact_server, String chat_dtm, String chat_dir,
            String chat_body, String contact_number, String chat_uid, String ack, String event,
            String user, String chat_type, String token, String contact_name, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE\n"
                    + "     EventoMsgWhatsApp\n"
                    + " SET\n"
                    + "     contact_server = ?, chat_dtm = ?, chat_dir = ?, \n"
                    + "     chat_body = ?, contact_number = ?, chat_uid = ?, Chat_ACK = ?, event = ?, \n"
                    + "     User_Rem = ?, chat_type = ?, token = ?, contact_name = ?\n"
                    + "WHERE \n"
                    + "     Sequencia = ?;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contact_server);
            consulta.setString(chat_dtm);
            consulta.setString(chat_dir);
            consulta.setString(chat_body);
            consulta.setString(contact_number);
            consulta.setString(chat_uid);
            consulta.setString(ack);
            consulta.setString(event);
            consulta.setString(user);
            consulta.setString(chat_type);
            consulta.setString(token);
            consulta.setString(contact_name);
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EventoMsgWhatsAppDao.atualizarMensagem - " + e.getMessage() + "\r\n"
                    + "UPDATE\n"
                    + "     EventoMsgWhatsApp\n"
                    + " SET\n"
                    + "     contact_server = ?, chat_dtm = ?, chat_dir = ?, \n"
                    + "     chat_body = ?, contact_number = ?, chat_uid = ?, Chat_ACK = ?, event = ?, \n"
                    + "     User_Rem = ?, chat_type = ?, token = ?, contact_name = ?,\n"
                    + "WHERE \n"
                    + "     Sequencia = ?;\n");
        }
    }

    public void receberMensagem(String contact_server, String chat_dtm, String chat_dir,
            String chat_body, String contact_number, String chat_uid, String ack, String event,
            String user, String chat_type, String token, String contact_name, Persistencia persistencia) throws Exception {
        try {
            String sql = "DECLARE @Sequencia Float;\n"
                    + "\n"
                    + "SET @Sequencia = (SELECT ISNULL(MAX(Sequencia),0) + 1 FROM EventoMsgWhatsapp);\n"
                    + "\n"
                    + "INSERT INTO EventoMsgWhatsapp (Sequencia, Parametro, contact_server, chat_dtm, chat_dir, \n"
                    + "            chat_body, contact_number, chat_uid, Chat_ACK, event, \n"
                    + "            User_Rem, chat_type, token, contact_name)\n"
                    + "VALUES (@Sequencia, 'SASW',?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contact_server);
            consulta.setString(chat_dtm);
            consulta.setString(chat_dir);
            consulta.setString(chat_body);
            consulta.setString(contact_number);
            consulta.setString(chat_uid);
            consulta.setString(ack);
            consulta.setString(event);
            consulta.setString(user);
            consulta.setString(chat_type);
            consulta.setString(token);
            consulta.setString(contact_name);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EventoMsgWhatsAppDao.receberMensagem - " + e.getMessage() + "\r\n"
                    + "DECLARE @Sequencia Float;\n"
                    + "\n"
                    + "SET @Sequencia = (SELECT ISNULL(MAX(Sequencia),0) + 1 FROM EventoMsgWhatsapp);\n"
                    + "\n"
                    + "INSERT INTO EventoMsgWhatsapp (Sequencia, Parametro, contact_server, chat_dtm, chat_dir, \n"
                    + "            chat_body, contact_number, chat_uid, Chat_ACK, event, \n"
                    + "            User_Rem, chat_type, token, contact_name)\n"
                    + "VALUES (@Sequencia, 'SASW'," + contact_server + ",  " + chat_dtm + ",  " + chat_dir + ", " + chat_body + ",  "
                    + contact_number + ",  " + chat_uid + ",  " + ack + ",  " + event + ", " + user + ",  " + chat_type + ",  " + token + ",  " + contact_name + ");\n");
        }
    }
}

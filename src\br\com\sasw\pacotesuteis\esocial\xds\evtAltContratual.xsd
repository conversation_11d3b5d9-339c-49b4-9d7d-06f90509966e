﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtAltContratual/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtAltContratual/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtAltContratual">
                    <xs:annotation>
                        <xs:documentation>Evento Alteracao Contratual</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideVinculo" type="TIdeVinculoNisObrig">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Trabalhador e do Vinculo</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="altContratual">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Contrato de Trabalho</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="dtAlteracao">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data da Alteracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dtEf" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data dos efeitos</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dscAlt" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Descricao da alteracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="2"/>
                                                    <xs:maxLength value="150"/>
                                                    <xs:whiteSpace value="preserve"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vinculo">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do vinculo</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpRegPrev">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Regime previdenciario</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoRegimeTrab">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do regime trabalhista</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="infoCeletista" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes de Trabalhador Celetista</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpRegJor">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Regime de jornada do empregado</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="natAtividade">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Natureza da atividade</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtBase" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Mes relativo à data base da categoria profissional do trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d{1,2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="cnpjSindCategProf">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Preencher com o CNPJ do sindicato representativo da categoria (Preponderante ou Diferenciada)</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="14"/>
                                                                            <xs:pattern value="\d{14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="trabTemp" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Dados sobre trabalho temporario</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="justProrr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Justificativa da prorrogacao</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="2"/>
                                                                                        <xs:maxLength value="999"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="aprend" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes relacionadas ao aprendiz</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpInsc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrInsc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número de Inscricao</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{8,14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoEstatutario" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes de Trabalhador Estatutario</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpPlanRP">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de plano de segregacao da massa</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoContrato">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do Contrato de Trabalho</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="codCargo" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Cargo</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codFuncao" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Funcao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codCateg">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Categoria</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                                <xs:pattern value="\d{3}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codCarreira" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Carreira</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtIngrCarr" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de ingresso na carreira</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="remuneracao" type="TRemun">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes da remuneracao e periodicidade de pagamento</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="duracao">
                                                        <xs:annotation>
                                                            <xs:documentation>Duracao do Contrato de Trabalho</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpContr">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de contrato</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtTerm" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data do Término</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="objDet" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicacao do objeto determinante da contratacao por prazo determinado</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="255"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="localTrabalho">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do local de trabalho</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="localTrabGeral" type="TLocalTrab" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Estabelecimento onde o trabalhador exercera suas atividades</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                                <xs:element name="localTrabDom" type="TEnderecoBrasil" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Endereco de trabalho do trabalhador doméstico e trabalhador temporario</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="horContratual" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do Horario Contratual do Trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="qtdHrsSem" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Qtd horas semanais</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="4"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="99.99"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="tpJornada">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo da Jornada</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dscTpJorn" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Descricao do tipo de jornada</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="100"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="tmpParc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Contrato de trabalho em regime de tempo parcial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="horario" type="THorario" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes diarias do horario contratual</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="filiacaoSindical" minOccurs="0" maxOccurs="2">
                                                        <xs:annotation>
                                                            <xs:documentation>Filiacao Sindical do Trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="cnpjSindTrab">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>CNPJ do Sindicato</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="14"/>
                                                                            <xs:pattern value="\d{14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="alvaraJudicial" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Dados do Alvara Judicial</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="nrProcJud">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Identificacao do Número do Processo Judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="20"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="observacoes" minOccurs="0" maxOccurs="99">
                                                        <xs:annotation>
                                                            <xs:documentation>Observacoes do contrato de trabalho</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="observacao">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Observacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="255"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="servPubl" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Alteracoes inerentes ao servidor público</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="mtvAlter">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Motivo da alteracao do cargo ou funcao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeVinculoNisObrig">
        <xs:annotation>
            <xs:documentation>Informacoes do Vinculo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cpfTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>CPF do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="11"/>
                        <xs:pattern value="\d{11}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nisTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>NIS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="11"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="matricula">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Matricula</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRemun">
        <xs:annotation>
            <xs:documentation>Remuneracao e periodicidade de pagamento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="vrSalFx">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Salario base do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="undSalFixo">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Unidade de pagamento da parte fixa da remuneracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscSalVar" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do salario variavel e como este é calculado  ex: Comissoes pagas no percentual de 10% sobre as vendas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="255"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TLocalTrab">
        <xs:annotation>
            <xs:documentation>Informacoes do Local de Trabalho</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="descComp" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao complementar</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="80"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEnderecoBrasil">
        <xs:annotation>
            <xs:documentation>Informacoes do Endereco no Brasil</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="4"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="10"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="complemento" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Complemento do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="30"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="bairro" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="90"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="cep">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo de Enderecamento Postal - CEP</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codMunic">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Municipio</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                        <xs:pattern value="\d{7}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="uf">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Sigla da UF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="AC"/>
                        <xs:enumeration value="AL"/>
                        <xs:enumeration value="AP"/>
                        <xs:enumeration value="AM"/>
                        <xs:enumeration value="BA"/>
                        <xs:enumeration value="CE"/>
                        <xs:enumeration value="DF"/>
                        <xs:enumeration value="ES"/>
                        <xs:enumeration value="GO"/>
                        <xs:enumeration value="MA"/>
                        <xs:enumeration value="MT"/>
                        <xs:enumeration value="MS"/>
                        <xs:enumeration value="MG"/>
                        <xs:enumeration value="PA"/>
                        <xs:enumeration value="PB"/>
                        <xs:enumeration value="PR"/>
                        <xs:enumeration value="PE"/>
                        <xs:enumeration value="PI"/>
                        <xs:enumeration value="RJ"/>
                        <xs:enumeration value="RN"/>
                        <xs:enumeration value="RS"/>
                        <xs:enumeration value="RO"/>
                        <xs:enumeration value="RR"/>
                        <xs:enumeration value="SC"/>
                        <xs:enumeration value="SP"/>
                        <xs:enumeration value="SE"/>
                        <xs:enumeration value="TO"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="THorario">
        <xs:annotation>
            <xs:documentation>Informacoes de Horario Contratual</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="dia">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indica o dia do horario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codHorContrat">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Horario Contratual</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

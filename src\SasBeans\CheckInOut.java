/*
 */
package SasBeans;

import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class CheckInOut {

    String BadgeNumber;
    String Id;
    LocalDate CheckTime;
    String Reserved;
    String Name;

    public String getBadgeNumber() {
        return BadgeNumber;
    }

    public void setBadgeNumber(String BadgeNumber) {
        this.BadgeNumber = BadgeNumber;
    }

    public String getId() {
        return Id;
    }

    public void setId(String Id) {
        this.Id = Id;
    }

    public LocalDate getCheckTime() {
        return CheckTime;
    }

    public void setCheckTime(LocalDate CheckTime) {
        this.CheckTime = CheckTime;
    }

    public String getReserved() {
        return Reserved;
    }

    public void setReserved(String Reserved) {
        this.Reserved = Reserved;
    }

    public String getName() {
        return Name;
    }

    public void setName(String Name) {
        this.Name = Name;
    }
}

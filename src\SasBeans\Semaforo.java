/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class Semaforo {

    private String Tabela;
    private String Chave;
    private BigDecimal Sequencia;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    /**
     *
     * @return Tabela
     */
    public String getTabela() {
        return this.Tabela;
    }

    /**
     * Set tabela
     *
     * @param Tabela
     */
    public void setTabela(String Tabela) {
        this.Tabela = Tabela;
    }

    /**
     *
     * @return Chave
     */
    public String getChave() {
        return this.Chave;
    }

    /**
     * Set Chave
     *
     * @param Chave
     */
    public void setChave(String Chave) {
        this.Chave = Chave;
    }

    /**
     *
     * @return Sequencia
     */
    public BigDecimal getSequencia() {
        return this.Sequencia;
    }

    /**
     * Set Sequencia
     *
     * @param Sequencia
     */
    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    /**
     *
     * @return Operador
     */
    public String getOperador() {
        return this.Operador;
    }

    /**
     * Set Operdor
     *
     * @param Operador
     */
    public void setOperdor(String Operador) {
        this.Operador = Operador;
    }

    /**
     *
     * @return Dt_Alter
     */
    public LocalDate getDt_Alter() {
        return this.Dt_Alter;
    }

    /**
     * Return Dt_Alter
     *
     * @param Dt_Alter
     */
    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     *
     * @return Hr_Alter
     */
    public String getHr_Alter() {
        return this.Hr_Alter;
    }

    /**
     * Set Hr_Alter
     *
     * @param Hr_Alter
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

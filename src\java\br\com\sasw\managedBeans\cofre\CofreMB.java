/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.cofre;

import Arquivo.ArquivoLog;
import Controller.CofreInteligente.CofreIntelSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.MobileHW;
import SasBeans.MobileHWSt;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaPortalSrv;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.TesCofresMov;
import SasBeans.TesEntDN;
import SasBeans.TesEntrada;
import SasBeans.TesSaidas;
import SasBeans.TesSaidasDN;
import SasBeans.TesCofresRes;
import SasBeansCompostas.MovimentacaoGeralFuncionario;
import SasBeansCompostas.TesCofresResClientes;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.PessoaDao;
import SasDaos.TesCofresMovDao;
import br.com.sasw.lazydatamodels.cofre.CofreLazyList;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.preencherDireita;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcento;
import static br.com.sasw.utils.Mascaras.CEP;
import static br.com.sasw.utils.Mascaras.Data;
import static br.com.sasw.utils.Mascaras.Hora;
import static br.com.sasw.utils.Mascaras.Moeda;
import static br.com.sasw.utils.Mascaras.removeMascara;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import java.awt.Color;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.inject.Named;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.commons.io.FileUtils;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datagrid.DataGrid;
import org.primefaces.component.export.PDFOptions;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "cofre")
@javax.faces.view.ViewScoped
public class CofreMB implements Serializable {

    private Date calendario, calendario1, calendario2;
    private Map filters, niveis, tiposRelatorios;
    private Persistencia persistencia, central;
    private final CofreIntelSatMobWeb cofreSatMobWeb;
    private ArquivoLog logerro;
    private final String codfil, banco, operador, caminho;
    private String codcli, filialDesc, log, nomeAux, filtroNomeCofre, filtroNumeroCofre, relatorio, relatorioCSV, nomeArquivo,
            hora1, hora2;
    private LazyDataModel<TesCofresResClientes> cofres = null;
    private List<TesCofresResClientes> listaMovimentacao;
    private TesCofresResClientes cofreSelecionado, movimentacaoSelecionada;
    private List<TesCofresMov> detalhesMovimentacao;
    private final BigDecimal codpessoa;
    private BigDecimal valorDepositos, valorColetas, valorTotal;
    private int totalDepositos, totalColetas, flagUsuario;
    private List<MobileHWSt> listaStatus;
    private MobileHW infoCofre;
    private PDFOptions pdfOptMovimentacoes;
    private boolean exibirTodos, cadastroNovaPessoa, somenteAlertas, relatorioDuasDatas, mostrarFiliais;
    private List<UsuarioSatMobWeb> usuarios;
    private UsuarioSatMobWeb usuario, buscaUsuario;
    private List<Pessoa> listaPessoa;
    private Pessoa novaPessoa, pessoa;
    private List<SASGrupos> grupos;
    private PessoaPortalSrv servico;
    private SasPWFill filial;
    private PessoaCliAut pessoacliaut;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private List<TesEntrada> depositosRelatorio;
    private List<TesCofresRes> depositosRelatorioitem;
    private TesEntrada depositoRelatorio;
    private List<TesSaidas> coletasRelatorio;
    private TesSaidas coletaRelatorio;
    private StreamedContent arquivoRelatorio;

    public static enum TipoRelatorio {
        MOVIMENTACAO_DIARIA, DEPOSITO, COLETA, MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO, MOVIMENTACAO_GERAL_FUNCIONARIO,
        HISTORICO_MOVIMENTACOES, HISTORICO_COLETAS, HISTORICO_DEPOSITOS, HISTORICO_CREDITOS
    }
    private TipoRelatorio tipoRelatorio;

    public CofreMB() {
        FacesContext facesContext = FacesContext.getCurrentInstance();
        banco = (String) facesContext.getExternalContext().getSessionMap().get("banco");
        codfil = (String) facesContext.getExternalContext().getSessionMap().get("filial");
        codcli = (String) facesContext.getExternalContext().getSessionMap().get("cliente");
        operador = (String) facesContext.getExternalContext().getSessionMap().get("nome");
        codpessoa = (BigDecimal) facesContext.getExternalContext().getSessionMap().get("codpessoa");
        filialDesc = (String) facesContext.getExternalContext().getSessionMap().get("nomeFilial");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codpessoa.toBigInteger() + ".txt";
        try {
            exibirTodos = (boolean) facesContext.getExternalContext().getSessionMap().get("menu");
        } catch (Exception ex) {
        }
        calendario = Date.from(Instant.now());
        calendario1 = Date.from(Instant.now());
        hora1 = "08:00";
        hora2 = "23:00";
        cofreSatMobWeb = new CofreIntelSatMobWeb();
        rotassatweb = new RotasSatWeb();
        filtroNomeCofre = "";
        filtroNumeroCofre = "";
        logerro = new ArquivoLog();
        mostrarFiliais = false;
    }

    public void Persistencias(Persistencia pp, Persistencia local) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = local;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            this.filters = new HashMap();
            this.filters.put(" TesCofresRes.data = ? ", DataAtual.getDataAtual("SQL"));
            if (!this.exibirTodos) {
                this.filters.put(" pessoacliaut.codigo = ? ", this.codpessoa.toPlainString());
                this.filters.put(" pessoacliaut.Flag_Excl <> ? ", "*");
            }
            this.filters.put(" clientes.codigo = ? ", this.codcli);

            if (null == this.codcli) {
                this.codcli = "";
            }

            if (this.codcli.equals("")) {
                this.filters.put(" clientes.codFil = ? ", "");
            } else {
                this.filters.put(" clientes.codFil = ? ", this.codfil);
            }

            this.filters.put(" TesCofresRes.CodCofre = ? ", "");
            this.filters.put(" Clientes.NRed like ? ", "");

            this.niveis = gerarNiveis();
            this.tiposRelatorios = gerarTiposRelatorios();
            this.grupos = this.cofreSatMobWeb.listarGrupos(this.persistencia);
            if (this.codfil.equals("")) {
                this.filiais = null;
            } else {
                this.filiais = this.rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
            }

//            this.cofreSatMobWeb.atualizarTesCofresMov(DataAtual.getDataAtual("SQL"), this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void mostrarFiliais() {
        this.cofres = null;
        getCofres();
    }

    public LazyDataModel<TesCofresResClientes> getCofres() {
        try {
            if (this.cofres == null) {
                this.filters.replace(" TesCofresRes.data = ? ", DataAtual.getDataAtual("SQL"));
                if (!this.exibirTodos) {
                    this.filters.replace(" pessoacliaut.codigo = ? ", this.codpessoa.toPlainString());
                    this.filters.put(" pessoacliaut.Flag_Excl <> ? ", "*");
                }
                this.filters.replace(" clientes.codigo = ? ", this.codcli);

                if (this.codcli.equals("")) {
                    if (this.mostrarFiliais) {
                        this.filters.replace(" clientes.codFil = ? ", "");
                    } else {
                        this.filters.replace(" clientes.codFil = ? ", this.codfil);
                    }
                } else {
                    this.filters.replace(" clientes.codFil = ? ", this.codfil);
                }

                this.cofres = new CofreLazyList(this.persistencia, this.exibirTodos, this.filters);
            } else {
                ((CofreLazyList) this.cofres).setFilters(this.filters);
            }

            PrimeFaces.current().executeScript("$(window).resize()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
        return this.cofres;
    }

    public void selecionarData(SelectEvent event) {
        try {
            this.calendario = (Date) event.getObject();
            this.filters.replace(" TesCofresRes.data = ? ", this.calendario.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            getCofres();

            DataGrid dt = (DataGrid) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFirst(0);

            PrimeFaces.current().executeScript("$(window).resize()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataAnteriorCofre() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.calendario);
            calendar.add(Calendar.DAY_OF_WEEK, -1);
            this.calendario = calendar.getTime();
            this.filters.replace(" TesCofresRes.data = ? ", this.calendario.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            getCofres();

            DataGrid dt = (DataGrid) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFirst(0);

            PrimeFaces.current().executeScript("$(window).resize()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataPosteriorCofre() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.calendario);
            calendar.add(Calendar.DAY_OF_WEEK, 1);
            this.calendario = calendar.getTime();
            this.filters.replace(" TesCofresRes.data = ? ", this.calendario.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            getCofres();

            DataGrid dt = (DataGrid) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFirst(0);

            PrimeFaces.current().executeScript("$(window).resize()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void pesquisar() {
        try {
            this.filters.replace(" TesCofresRes.CodCofre = ? ", "");
            this.filters.replace(" Clientes.NRed like ? ", "");

            if (!this.filtroNomeCofre.equals("")) {
                this.filters.replace(" Clientes.NRed like ? ", "%" + this.filtroNomeCofre + "%");
            }

            if (!this.filtroNumeroCofre.equals("")) {
                this.filters.replace(" TesCofresRes.CodCofre = ? ", this.filtroNumeroCofre);
            }

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.calendario);
            calendar.add(Calendar.DAY_OF_WEEK, 0);
            this.calendario = calendar.getTime();
            this.filters.replace(" TesCofresRes.data = ? ", this.calendario.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            getCofres();

            DataGrid dt = (DataGrid) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFirst(0);

            PrimeFaces.current().executeScript("$(window).resize()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void limparPesquisa() {
        try {
            this.filters.replace(" TesCofresRes.CodCofre = ? ", "");
            this.filters.replace(" Clientes.NRed like ? ", "");

            this.filtroNomeCofre = "";
            this.filtroNumeroCofre = "";

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.calendario);
            calendar.add(Calendar.DAY_OF_WEEK, 0);
            this.calendario = calendar.getTime();
            this.filters.replace(" TesCofresRes.data = ? ", this.calendario.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            getCofres();

            DataGrid dt = (DataGrid) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirUsuarios(TesCofresResClientes cofreSelecionado) {
        try {
            this.cofreSelecionado = cofreSelecionado;
            this.usuarios = this.cofreSatMobWeb.listagemUsuarios(this.cofreSelecionado.getClientes().getCodigo(), this.persistencia, this.central);
            PrimeFaces.current().executeScript("PF('dlgListarUsuarios').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void novoUsuario() {
        try {
            this.pessoa = new Pessoa();

            this.listaPessoa = new ArrayList<>();
            this.listaPessoa.add(this.pessoa);

            this.novaPessoa = new Pessoa();
            this.novaPessoa.setCodigo("-1");
            this.novaPessoa.setNome(getMessageS("CadastrarNovaPessoa"));

            this.cadastroNovaPessoa = false;

            this.usuario = new UsuarioSatMobWeb();
            this.usuario.setPessoa(this.pessoa);

            this.flagUsuario = 1;

            PrimeFaces.current().resetInputs("formCadastrarUsuario:editar");
            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirUsuario(UsuarioSatMobWeb usuario) {
        try {
            this.usuario = usuario;

            this.cadastroNovaPessoa = false;

            this.flagUsuario = 2;

            PrimeFaces.current().resetInputs("formCadastrarUsuario:editar");
            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').show()");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Pessoa> listarQueryValida(String query) {
        try {
            this.nomeAux = query;
            this.listaPessoa = this.cofreSatMobWeb.listaPessoaQuery(query, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        this.listaPessoa.add(this.novaPessoa);
        return this.listaPessoa;
    }

    public void selecionarPessoa(SelectEvent event) {
        try {
            this.usuario = new UsuarioSatMobWeb();
            // Buscar pessoa:
            this.pessoa = ((Pessoa) event.getObject());

            // Pessoa existe?
            if (this.pessoa.getCodigo().toBigInteger().toString().equals("-1")) {
                // Pessoa não existe.
                this.cadastroNovaPessoa = true;
                this.usuario.getPessoa().setNome(this.nomeAux);
            } else {
                // Pessoa existe.
                this.cadastroNovaPessoa = false;
                this.usuario.setPessoa(this.pessoa);

                // Pessoa possui codPessoaWeb?
                if (null == this.usuario.getPessoa().getCodPessoaWEB()) {
                    // Pessoa não possui codPessoaWeb.
                    this.pessoa = this.cofreSatMobWeb.inserirCodPessoaWeb(this.pessoa, this.persistencia, this.central);
                    this.usuario.setPessoa(this.pessoa);
                }

                // Pessoa possui codPessoaWeb.
                // Buscar usuário:
                this.buscaUsuario = this.cofreSatMobWeb.buscarUsuario(this.usuario.getPessoa().getCodPessoaWEB(), this.persistencia, this.central);
                if (null != this.buscaUsuario) {
                    this.usuario = this.buscaUsuario;

                    this.flagUsuario = 2;
                }

                this.usuario.getSaspw().setNome(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoa(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoaWeb(this.usuario.getPessoa().getCodPessoaWEB().toPlainString());
                this.usuario.getSaspw().setNomeCompleto(this.usuario.getPessoa().getNome());
                this.usuario.getSaspw().setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getPessoalogin().setCodigo(this.usuario.getPessoa().getCodPessoaWEB());
                this.usuario.getPessoalogin().setCodPessoaBD(this.usuario.getPessoa().getCodigo());

            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrar() {
        this.usuario.getSaspw().setOperador(RecortaAteEspaço(this.operador, 0, 10));
        this.usuario.getSaspw().setCodFil(this.cofreSelecionado.getClientes().getCodFil().toString());

        switch (this.usuario.getSaspw().getNivelx()) {
            case "1":
                this.usuario.getSaspw().setNivelOP(getMessageS("Operacao"));
                break;
            case "2":
                this.usuario.getSaspw().setNivelOP(getMessageS("Manutencao"));
                break;
            case "3":
                this.usuario.getSaspw().setNivelOP(getMessageS("Gerencia"));
                break;
            case "4":
                this.usuario.getSaspw().setNivelOP(getMessageS("PortalRH"));
                break;
            case "5":
                this.usuario.getSaspw().setNivelOP(getMessageS("GTV"));
                break;
            case "6":
                this.usuario.getSaspw().setNivelOP(getMessageS("CofreInteligente"));
                break;
            case "7":
                this.usuario.getSaspw().setNivelOP(getMessageS("AssinarGTV"));
                break;
            case "8":
                this.usuario.getSaspw().setNivelOP(getMessageS("SatMobEW"));
                break;
            case "9":
                this.usuario.getSaspw().setNivelOP(getMessageS("Administrador"));
                break;
            case "10":
                this.usuario.getSaspw().setNivelOP(getMessageS("Chamados"));
                break;
            default:
                this.usuario.getSaspw().setNivelOP(getMessageS(""));
                break;
        }
        try {

            if (this.cadastroNovaPessoa) {
                this.novaPessoa = new Pessoa();
                this.novaPessoa.setNome(this.usuario.getPessoa().getNome().toUpperCase());
                this.novaPessoa.setEmail(this.usuario.getPessoa().getEmail());
                this.novaPessoa.setPWWeb(this.usuario.getPessoa().getPWWeb());
                this.novaPessoa.setCPF(removeMascara(this.usuario.getPessoa().getCPF()));
                this.novaPessoa.setRG(removeMascara(this.usuario.getPessoa().getRG()));
                this.novaPessoa.setRGOrgEmis(removeMascara(this.usuario.getPessoa().getRGOrgEmis()));
                this.novaPessoa.setSituacao(this.usuario.getPessoa().getSituacao());
                this.novaPessoa.setOperador(RecortaAteEspaço(this.operador, 0, 10));
                this.novaPessoa.setDt_Alter(getDataAtual("SQL"));
                this.novaPessoa.setHr_Alter(getDataAtual("HORA"));
                this.novaPessoa = this.cofreSatMobWeb.inserirNovaPessoa(this.novaPessoa, this.persistencia, this.central);

                this.usuario.setPessoa(this.novaPessoa);

                this.usuario.getSaspw().setNome(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoa(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getSaspw().setCodPessoaWeb(this.usuario.getPessoa().getCodPessoaWEB().toPlainString());
                this.usuario.getSaspw().setNomeCompleto(this.usuario.getPessoa().getNome());
                this.usuario.getSaspw().setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
                this.usuario.getPessoalogin().setCodigo(this.usuario.getPessoa().getCodPessoaWEB());
                this.usuario.getPessoalogin().setCodPessoaBD(this.usuario.getPessoa().getCodigo());
            }

            this.usuario.getSaspw().setNivelOP(this.usuario.getSaspw().getNivelOP().toUpperCase());
            this.usuario.getSaspw().setDescricao(this.usuario.getSaspw().getDescricao().toUpperCase());
            this.usuario.getSaspw().setMotivo(this.usuario.getSaspw().getMotivo().toUpperCase());
            this.usuario.getPessoalogin().setNivel(this.usuario.getSaspw().getNivelx());
            this.usuario.getPessoalogin().setBancoDados(this.persistencia.getEmpresa());
            this.usuario.getPessoalogin().setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.usuario.getPessoalogin().setDt_alter(getDataAtual("SQL"));
            this.usuario.getPessoalogin().setHr_Alter(getDataAtual("HORA"));
            this.usuario.getSaspw().setCodGrupo(Integer.valueOf(this.usuario.getGrupo().getCodigo()));

            this.cofreSatMobWeb.criarAcesso(this.usuario, this.persistencia, this.central);

            this.servico = new PessoaPortalSrv();
            this.servico.setCodigo(this.usuario.getPessoa().getCodPessoaWEB().toString());
            this.servico.setServico("301");
            this.servico.setOper_Incl(RecortaAteEspaço(this.operador, 0, 10));
            this.servico.setDt_Incl(getDataAtual("SQL"));
            this.servico.setHr_Incl(getDataAtual("HORA"));
            this.servico.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.servico.setDt_Alter(getDataAtual("SQL"));
            this.servico.setHr_Alter(getDataAtual("HORA"));
            this.cofreSatMobWeb.inserirServicoAutomatico(this.servico, this.central);

            this.filial = new SasPWFill();
            this.filial.setCodfilAc(this.usuario.getSaspw().getCodFil());
            this.filial.setCodFil(this.usuario.getSaspw().getCodFil());
            this.filial.setNome(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.filial.setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.filial.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.cofreSatMobWeb.inserirFilial(this.filial, this.persistencia);

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.usuario.getSaspw().getCodPessoa().toPlainString());
            this.pessoacliaut.setCodCli(this.cofreSelecionado.getClientes().getCodigo());
            this.pessoacliaut.setCodFil(this.cofreSelecionado.getClientes().getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.cofreSelecionado.getClientes().getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.cofreSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            this.usuarios = this.cofreSatMobWeb.listagemUsuarios(this.cofreSelecionado.getClientes().getCodigo(), this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void editar() {
        try {
            this.usuario.getSaspw().setOperador(RecortaAteEspaço(this.operador, 0, 10));
            switch (this.usuario.getSaspw().getNivelx()) {
                case "1":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Operacao"));
                    break;
                case "2":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Manutencao"));
                    break;
                case "3":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Gerencia"));
                    break;
                case "4":
                    this.usuario.getSaspw().setNivelOP(getMessageS("PortalRH"));
                    break;
                case "5":
                    this.usuario.getSaspw().setNivelOP(getMessageS("GTV"));
                    break;
                case "6":
                    this.usuario.getSaspw().setNivelOP(getMessageS("CofreInteligente"));
                    break;
                case "7":
                    this.usuario.getSaspw().setNivelOP(getMessageS("AssinarGTV"));
                    break;
                case "8":
                    this.usuario.getSaspw().setNivelOP(getMessageS("SatMobEW"));
                    break;
                case "9":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Administrador"));
                    break;
                case "10":
                    this.usuario.getSaspw().setNivelOP(getMessageS("Chamados"));
                    break;
                default:
                    this.usuario.getSaspw().setNivelOP(getMessageS(""));
                    break;
            }
            this.usuario.getSaspw().setNivelOP(RecortaString(this.usuario.getSaspw().getNivelOP().toUpperCase(), 0, 15));
            this.usuario.getSaspw().setDescricao(this.usuario.getSaspw().getDescricao().toUpperCase());
            this.usuario.getSaspw().setMotivo(this.usuario.getSaspw().getMotivo().toUpperCase());
            this.usuario.getSaspw().setCodGrupo(Integer.valueOf(this.usuario.getGrupo().getCodigo()));
            this.usuario.getSaspw().setCodPessoa(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.usuario.getSaspw().setCodPessoaWeb(this.usuario.getPessoa().getCodPessoaWEB().toPlainString());
            this.usuario.getSaspw().setNomeCompleto(this.usuario.getPessoa().getNome());
            this.usuario.getSaspw().setCodigo(this.usuario.getPessoa().getCodigo().toBigInteger().toString());
            this.usuario.getPessoalogin().setCodigo(this.usuario.getPessoa().getCodPessoaWEB());
            this.usuario.getPessoalogin().setCodPessoaBD(this.usuario.getPessoa().getCodigo());
            this.usuario.getPessoalogin().setNivel(this.usuario.getSaspw().getNivelx());

            this.cofreSatMobWeb.criarAcesso(this.usuario, this.persistencia, this.central);

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.usuario.getPessoa().getCodigo().toPlainString());
            this.pessoacliaut.setCodCli(this.cofreSelecionado.getClientes().getCodigo());
            this.pessoacliaut.setCodFil(this.cofreSelecionado.getClientes().getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.cofreSelecionado.getClientes().getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.cofreSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            this.usuarios = this.cofreSatMobWeb.listagemUsuarios(this.cofreSelecionado.getClientes().getCodigo(), this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void excluir(UsuarioSatMobWeb usuario) {
        try {
            this.usuario = usuario;

            this.pessoacliaut = new PessoaCliAut();
            this.pessoacliaut.setCodigo(this.usuario.getPessoa().getCodigo().toPlainString());
            this.pessoacliaut.setCodCli(this.cofreSelecionado.getClientes().getCodigo());
            this.pessoacliaut.setCodFil(this.cofreSelecionado.getClientes().getCodFil().toPlainString());
            this.pessoacliaut.setNomeCli(this.cofreSelecionado.getClientes().getNRed());
            this.pessoacliaut.setOperador(RecortaAteEspaço(this.operador, 0, 10));
            this.pessoacliaut.setDt_Alter(getDataAtual("SQL"));
            this.pessoacliaut.setHr_Alter(getDataAtual("HORA"));
            this.pessoacliaut.setFlag_Excl("*");
            this.cofreSatMobWeb.inserirCliente(this.pessoacliaut, this.persistencia);

            this.usuarios = this.cofreSatMobWeb.listagemUsuarios(this.cofreSelecionado.getClientes().getCodigo(), this.persistencia, this.central);

            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirMovimentacoes(TesCofresResClientes cofreSelecionado) {
        try {
            this.cofreSelecionado = cofreSelecionado;
            this.calendario1 = this.calendario;
            this.calendario2 = this.calendario;
            this.infoCofre = this.cofreSatMobWeb.buscarInfoCofre(this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);
            if (null != this.infoCofre && null == this.infoCofre.getMarcaEquip()) {
                this.infoCofre.setMarcaEquip("-");
            }
            listarMovimentacoes();
            this.movimentacaoSelecionada = null;
            PrimeFaces.current().executeScript("PF('dlgListarMovimentos').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarMovimentacoes() {
        try {
            String vCodPessoa = this.codpessoa.toString();
            this.listaMovimentacao = this.cofreSatMobWeb.MovimentoCofreCliente(this.cofreSelecionado.getClientes().getCodigo(),
                    this.cofreSelecionado.getClientes().getCodFil().toPlainString(),
                    this.getData1(), this.getData2(), vCodPessoa, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void detalharMovimentacao() {
        try {
            this.calendario1 = this.calendario;
            this.calendario2 = this.calendario;
            atualizarListaDetalhesMovmentacao();
            PrimeFaces.current().executeScript("PF('dlgListarDetalhesmovimentacao').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void detalharMovimentacao(TesCofresResClientes cofreSelecionado) {
        try {
            this.cofreSelecionado = cofreSelecionado;
            this.calendario1 = this.calendario;
            this.calendario2 = this.calendario;
            atualizarListaDetalhesMovmentacao();
            PrimeFaces.current().executeScript("PF('dlgListarDetalhesmovimentacao').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void atualizarListaDetalhesMovmentacao() {
        try {
            this.detalhesMovimentacao = this.cofreSatMobWeb.detalhesMovimentacaoCofre(this.cofreSelecionado.getClientes().getCodCofre(),
                    this.getData1(), this.getData2(), this.persistencia);

            this.valorDepositos = BigDecimal.ZERO;
            this.valorColetas = BigDecimal.ZERO;
            this.valorTotal = BigDecimal.ZERO;

            this.totalColetas = 0;
            this.totalDepositos = 0;

            this.detalhesMovimentacao.forEach((tesCofresMov) -> {
                if (tesCofresMov.getTipoDeposito().toUpperCase().equals("COLETA")) {
                    this.valorColetas = this.valorColetas.add(tesCofresMov.getValorDeposito());
                    this.totalColetas++;
                } else {
                    this.valorDepositos = this.valorDepositos.add(tesCofresMov.getValorDeposito());
                    this.totalDepositos++;
                }
            });

            this.valorTotal = this.valorDepositos.subtract(this.valorColetas);
            PrimeFaces.current().ajax().update("detalhesMovimentacao:cadastrar2");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preExportarMovimentacao() {
        this.pdfOptMovimentacoes = new PDFOptions(); //add getter and setter too
        this.pdfOptMovimentacoes.setColumnWidths(new float[]{0.08f, 0.15f, 0.12f, 0.16f, 0.14f, 0.21f, 0.14f});
        PrimeFaces.current().executeScript("PF('dlgExportarDetMov').show();");
    }

    public void exportarMovimentacao(Object document) {
        try {
            Document pdf = (Document) document;
            pdf.setPageSize(PageSize.A4.rotate());
            pdf.setMargins(0, 0, 18, 18);
            pdf.open();
            float[] columnWidths = {2, 10};
            PdfPTable table = new PdfPTable(columnWidths);
            table.setTotalWidth(pdf.right() - 18);
            table.setSplitLate(false);
            table.setSplitRows(true);
            PdfPCell cell;
            Font negrito;
            String logo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("")
                    + LoginMB.getLogoS(this.banco);
            Image image = Image.getInstance(logo);
            image.scaleToFit(75, 100);
            cell = new PdfPCell();
            cell.addElement(image);
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            negrito = new Font(Font.HELVETICA, 14f, Font.BOLD);
            cell = new PdfPCell(new Phrase(this.cofreSatMobWeb.filialCofre(this.cofreSelecionado.getClientes().getCodFil().toPlainString(),
                    this.persistencia), negrito));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(Messages.getMessageS("Operador") + ":"));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(this.operador));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(Messages.getMessageS("Data") + ":"));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(DataAtual.getDataAtual("TELA") + " " + DataAtual.getDataAtual("HORA")));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(Messages.getMessageS("Cliente") + ":"));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            negrito = new Font(Font.HELVETICA, 12f, Font.BOLD);
            cell = new PdfPCell(new Phrase(this.cofreSelecionado.getClientes().getNRed(), negrito));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase(Messages.getMessageS("Período")));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);
            cell = new PdfPCell(new Phrase(this.getData1() + " à " + this.getData2()));
            cell.setBorder(Rectangle.NO_BORDER);
            table.addCell(cell);

            pdf.add(table);

            Paragraph p = new Paragraph("Movimentação Cofre " + this.cofreSelecionado.getClientes().getCodCofre() + "\n\r",
                    new Font(Font.HELVETICA, 18, Font.BOLD, Color.BLACK));
            p.setAlignment(Element.ALIGN_CENTER);
            pdf.add(p);

            pdf.addAuthor(this.operador);
            pdf.addCreationDate();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void abrirStatus(TesCofresResClientes cofreSelecionado) {
        try {
            this.cofreSelecionado = cofreSelecionado;
            this.calendario1 = this.calendario;
            this.calendario2 = this.calendario;
            this.infoCofre = this.cofreSatMobWeb.buscarInfoCofre(this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);
            if (null == this.infoCofre.getMarcaEquip()) {
                this.infoCofre.setMarcaEquip("-");
            }
            listarStatus();
            PrimeFaces.current().executeScript("PF('dlgListarStatus').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarStatus() {
        try {
            this.listaStatus = this.cofreSatMobWeb.listaStatusCofre(this.cofreSelecionado.getClientes().getCodCofre(),
                    this.cofreSelecionado.getMobileHW().getIMEI(),
                    this.getData1(), this.getData2(), this.somenteAlertas, this.persistencia);
            PrimeFaces.current().ajax().update("formStatus:cadastrar");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void abrirInfoCofre(TesCofresResClientes cofreSelecionado) {
        try {
            this.cofreSelecionado = cofreSelecionado;
            this.infoCofre = this.cofreSatMobWeb.buscarInfoCofre(this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);
            if (null == this.infoCofre.getMarcaEquip()) {
                this.infoCofre.setMarcaEquip("-");
            }
            PrimeFaces.current().executeScript("PF('dlgInfoCofre').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private Map gerarNiveis() {
        Map map = new HashMap<>();
        map.put(getMessageS("Administrador"), "9");
        map.put(getMessageS("Operacao"), "1");
        map.put(getMessageS("Manutencao"), "2");
        map.put(getMessageS("Gerencia"), "3");
//        map.put(Messages.getMessageS("CofreInteligente"), "6");
        return map;
    }

    private Map gerarTiposRelatorios() {
        Map map = new HashMap<>();
        map.put(getMessageS("MOVIMENTACAO_DIARIA"), TipoRelatorio.MOVIMENTACAO_DIARIA);
        map.put(getMessageS("DEPOSITO"), TipoRelatorio.DEPOSITO);
        map.put(getMessageS("COLETA"), TipoRelatorio.COLETA);
        map.put(getMessageS("MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO"), TipoRelatorio.MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO);
        map.put(getMessageS("MOVIMENTACAO_GERAL_FUNCIONARIO"), TipoRelatorio.MOVIMENTACAO_GERAL_FUNCIONARIO);
        map.put(getMessageS("HISTORICO_MOVIMENTACOES"), TipoRelatorio.HISTORICO_MOVIMENTACOES);
        map.put(getMessageS("HISTORICO_COLETAS"), TipoRelatorio.HISTORICO_COLETAS);
        map.put(getMessageS("HISTORICO_DEPOSITOS"), TipoRelatorio.HISTORICO_DEPOSITOS);
        map.put(getMessageS("HISTORICO_CREDITOS"), TipoRelatorio.HISTORICO_CREDITOS);
        return map;
    }

    public void abrirRelatorios(TesCofresResClientes cofreSelecionado) {
        try {
            this.tipoRelatorio = null;
            this.cofreSelecionado = cofreSelecionado;
            this.relatorio = null;
            this.relatorioCSV = null;
            this.infoCofre = this.cofreSatMobWeb.buscarInfoCofre(this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);
            this.calendario2 = this.calendario;
            this.relatorioDuasDatas = false;
            carregarInformacoesRelatorios();
            PrimeFaces.current().ajax().update("formRelatorio");
            PrimeFaces.current().executeScript("PF('dlgRelatorio').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarInformacoesRelatorios() {
        this.relatorio = null;
        this.relatorioCSV = null;
        this.depositosRelatorio = new ArrayList<>();
        this.depositoRelatorio = null;
        this.coletasRelatorio = new ArrayList<>();
        this.coletaRelatorio = null;
        this.depositosRelatorioitem = new ArrayList();
        this.depositosRelatorioitem = null;
        this.listaPessoa = new ArrayList<>();
        this.pessoa = null;
        this.relatorioDuasDatas = false;
        this.nomeArquivo = "relatorio";
        try {
            if (this.tipoRelatorio != null) {
                switch (this.tipoRelatorio) {
                    case HISTORICO_CREDITOS:// Criado para mostrar Tipo de Relatorio
                        String vCodPessoa = this.codpessoa.toString();
                        this.listaMovimentacao = this.cofreSatMobWeb.MovimentoCofreCliente(this.cofreSelecionado.getClientes().getCodigo(),
                                this.cofreSelecionado.getClientes().getCodFil().toPlainString(),
                                this.getData1(), this.getData2(), vCodPessoa, this.persistencia);
                        List<TesCofresResClientes> RetornoCred = this.listaMovimentacao;
                        if (RetornoCred.size() == 0) {
                                throw new Exception("SemDepositosData");
                            } else {
                                TesCofresRes depositoRelatorioItem;
                                int Contador = 0;
                                for (TesCofresResClientes tesCofresres : RetornoCred) {
                                    depositoRelatorioItem = new TesCofresRes();                                    
                                    depositoRelatorioItem.setCodCofre(tesCofresres.getTescofresres().getCodCofre().toString());
                                    depositoRelatorioItem.setCredD0(tesCofresres.getTescofresres().getCredD0());
                                    depositoRelatorioItem.setCredD1(tesCofresres.getTescofresres().getCredD1());
                                    depositoRelatorioItem.setData(tesCofresres.getTescofresres().getData());                                    
                                    depositoRelatorioItem.setDataStr(tesCofresres.getTescofresres().getDataStr());
                                    depositoRelatorioItem.setDiaSem(tesCofresres.getTescofresres().getDiaSem());
                                    depositoRelatorioItem.setDiaSemT(tesCofresres.getTescofresres().getDiaSemT());
                                    depositoRelatorioItem.setFeriado(tesCofresres.getTescofresres().getFeriado());
                                    depositoRelatorioItem.setHrRecApos(tesCofresres.getTescofresres().getHrRecApos());
                                    depositoRelatorioItem.setHrRecDepD0(tesCofresres.getTescofresres().getHrRecDepD0());
                                    depositoRelatorioItem.setNRed(tesCofresres.getTescofresres().getNRed());
                                    depositoRelatorioItem.setSaldoFisCred(tesCofresres.getTescofresres().getSaldoFisCred().toString());
                                    depositoRelatorioItem.setSaldoFisCst(tesCofresres.getTescofresres().getSaldoFisCst().toString());
                                    depositoRelatorioItem.setSaldoFisTotal(tesCofresres.getTescofresres().getSaldoFisTotal().toString());
                                    depositoRelatorioItem.setVlrCredCorteD0(tesCofresres.getTescofresres().getVlrCredCorteD0().toString());
                                    depositoRelatorioItem.setVlrCredRecD0(tesCofresres.getTescofresres().getVlrCredRecD0().toString());
                                    depositoRelatorioItem.setVlrD0Apos(tesCofresres.getTescofresres().getVlrD0Apos().toString());
                                    depositoRelatorioItem.setVlrD1Apos(tesCofresres.getTescofresres().getVlrD1Apos().toString());
                                    depositoRelatorioItem.setVlrDep(tesCofresres.getTescofresres().getVlrDep().toString());
                                    depositoRelatorioItem.setVlrDepProxDU(tesCofresres.getTescofresres().getVlrDepProxDU().toString());
                                    depositoRelatorioItem.setVlrRecApos(tesCofresres.getTescofresres().getVlrRecApos().toString());
                                    depositoRelatorioItem.setVlrRecDepD0(tesCofresres.getTescofresres().getVlrRecDepD0().toString());
                                    depositoRelatorioItem.setVlrTotalCred(tesCofresres.getTescofresres().getVlrTotalCred().toString());
                                    depositoRelatorioItem.setVlrTotalRec(tesCofresres.getTescofresres().getVlrTotalRec().toString());
                                    this.depositosRelatorioitem.add(depositoRelatorioItem);                                    
                                }
                            }
                        break;

                    case HISTORICO_DEPOSITOS:
                    case HISTORICO_COLETAS:
                    case HISTORICO_MOVIMENTACOES:
                        this.relatorioDuasDatas = true;
                        break;
                    case MOVIMENTACAO_DIARIA:
                        break;
                    case DEPOSITO:
                        this.depositosRelatorio = this.cofreSatMobWeb.listarDepositos(this.cofreSelecionado.getClientes().getCodigo(),
                                this.cofreSelecionado.getClientes().getCodFil().toString(), this.getData1(), this.persistencia);
                        if (this.depositosRelatorio.isEmpty()) {
                            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();

                            List<TesCofresMov> Retorno = tesCofresMovDao.listarMovimentacaoPeriodo(this.cofreSelecionado.getClientes().getCodCofre(), this.getData1(), this.getData1(), "DINHEIRO", this.persistencia);

                            if (Retorno.size() == 0) {
                                throw new Exception("SemDepositosData");
                            } else {
                                TesEntrada depositoRelatorioItem;
                                int Contador = 0;
                                for (TesCofresMov tesCofresMov : Retorno) {
                                    depositoRelatorioItem = new TesEntrada();

                                    if (!tesCofresMov.getNomeUsuario().equals("Aut-Satellite")) {
                                        depositoRelatorioItem.setSerie("aut-sat_" + Integer.toString(Contador++));
                                        depositoRelatorioItem.setValor(tesCofresMov.getValorDeposito());
                                        depositoRelatorioItem.setHrInicio(tesCofresMov.getHora());
                                        depositoRelatorioItem.setOperador(tesCofresMov.getNomeUsuario().replace("_", ""));                                        
                                        this.depositosRelatorio.add(depositoRelatorioItem);
                                    }
                                }
                            }
                        }
                        break;
                    case COLETA:
                        this.coletasRelatorio = this.cofreSatMobWeb.listarColetas(this.cofreSelecionado.getClientes().getCodigo(),
                                this.cofreSelecionado.getClientes().getCodFil().toString(), this.getData1(), this.persistencia);
                        if (this.coletasRelatorio.isEmpty()) {
                            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();

                            List<TesCofresMov> Retorno = tesCofresMovDao.listarMovimentacaoPeriodo(this.cofreSelecionado.getClientes().getCodCofre(), this.getData1(), this.getData1(), "COLETA", this.persistencia);

                            if (Retorno.size() == 0) {
                                throw new Exception("SemColetasData");
                            } else {
                                TesSaidas coletaRelatorioItem;
                                int Contador = 0;
                                for (TesCofresMov tesCofresMov : Retorno) {
                                    coletaRelatorioItem = new TesSaidas();

                                    if (!tesCofresMov.getNomeUsuario().equals("Aut-Satellite")) {
                                        coletaRelatorioItem.setSerie("aut-sat_" + Integer.toString(Contador++));
                                        coletaRelatorioItem.setTotalDN(tesCofresMov.getValorDeposito().toPlainString());
                                        coletaRelatorioItem.setHrInicio(tesCofresMov.getHora());
                                        coletaRelatorioItem.setOperador(tesCofresMov.getNomeUsuario().replace("_", ""));
                                        coletaRelatorioItem.setData(tesCofresMov.getData().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                                    }

                                    this.coletasRelatorio.add(coletaRelatorioItem);
                                }
                            }
                        }
                        break;
                    case MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO:
                        PessoaDao pessoaDao = new PessoaDao();

                        this.listaPessoa = pessoaDao.listarOperadoresCofreRelatorios(this.getData1(),
                                this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);
                        if (this.listaPessoa.isEmpty()) {
                            throw new Exception("SemMovimentacoesData");
                        }
                        break;
                    case MOVIMENTACAO_GERAL_FUNCIONARIO:

                        break;
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarRelatorio() {
        List<TesCofresMov> tesCofresMovsAux;
        List<MovimentacaoGeralFuncionario> movimentacaoGeralFuncionarios;
        MovimentacaoGeralFuncionario movimentacaoIndividual;
        BigDecimal saldoAnterior, saldoDia;
        boolean houveColeta;

        StringBuilder relatorioBuilder = new StringBuilder(), relatorioBuilderCSV = new StringBuilder();

        /**
         * CABEÇALHO
         */
        relatorioBuilder.append("<div>");
        relatorioBuilder.append("<p style=\"font-weight: bold;\">").append(this.cofreSelecionado.getClientes().getNRed()).append("</p><p>");
        relatorioBuilder.append(this.cofreSelecionado.getClientes().getEnde()).append("</p><p>");
        relatorioBuilder.append(preencherDireita(CEP(this.cofreSelecionado.getClientes().getCEP()).replace("<br />", "").replace("<br>", "").replace("\n", ""), 25, " "))
                .append(this.cofreSelecionado.getClientes().getCidade()).append("/").append(this.cofreSelecionado.getClientes().getEstado()).append("</p>");

        try {
            switch (this.tipoRelatorio) {
                case HISTORICO_MOVIMENTACOES:
                    this.nomeArquivo = getMessageS("HISTORICO_MOVIMENTACOES") + "-" + this.getData1() + "-" + this.getData2();

                    movimentacaoIndividual = this.cofreSatMobWeb.gerarRelatorioMovimentacoes(this.getData1(), this.getData2(),
                            this.hora1, this.hora2,
                            null, this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("RelatorioMovimentacoes").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br />");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("Periodo")).append(": ")
                            .append(Data(this.getData1())).append(" - ")
                            .append(Data(this.getData2()));

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("Movimentacoes").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

                    relatorioBuilder.append(getMessageS("TotalDeposito")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorDepositos().toString()))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("TotalColeta")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorColetas().toString()))
                            .append("<br />");

                    relatorioBuilder.append("<hr />").append("<br />").append("<table style=\"width: 100%;\">");
                    relatorioBuilder.append("<thead>").append("<tr>").append("<th>").append(getMessageS("Data")).append("</th>")
                            .append("<th>").append(getMessageS("Hora")).append("</th>")
                            .append("<th>").append(getMessageS("Movimentacao")).append("</th>")
                            .append("<th>").append(getMessageS("Valor")).append("</th>")
                            .append("<th>").append(getMessageS("Operador")).append("</th>")
                            .append("</thead>").append("<tbody>");

                    relatorioBuilderCSV.append(getMessageS("Data")).append(";")
                            .append(getMessageS("Hora")).append(";")
                            .append(getMessageS("Movimentacao")).append(";")
                            .append(getMessageS("Valor")).append(";")
                            .append(getMessageS("Operador")).append(";").append("\r\n");

                    for (TesCofresMov tesCofresMov : movimentacaoIndividual.getMovimentacoes()) {
                        relatorioBuilder
                                .append("<tr>")
                                .append("<td>").append(Data(tesCofresMov.getData().toString())).append("</td>")
                                .append("<td>").append(Hora(tesCofresMov.getHora())).append("</td>")
                                .append("<td>").append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                ? (getMessageS("Deposito").toUpperCase())
                                : (tesCofresMov.getTipoDeposito())).append("</td>")
                                .append("<td>").append(Moeda(tesCofresMov.getValorDeposito().toString())).append("</td>")
                                .append("<td>").append(tesCofresMov.getNomeUsuario().toUpperCase()).append("</td>")
                                .append("</tr>");

                        relatorioBuilderCSV.append(Data(tesCofresMov.getData().toString())).append(";")
                                .append(Hora(tesCofresMov.getHora())).append(";")
                                .append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                        ? (getMessageS("Deposito").toUpperCase())
                                        : (tesCofresMov.getTipoDeposito())).append(";")
                                .append(tesCofresMov.getValorDeposito().toString()).append(";")
                                .append(tesCofresMov.getNomeUsuario().toUpperCase()).append(";").append("\r\n");

                    }

                    relatorioBuilder.append("</tbody>").append("</table>").append("<hr />").append("<br />");
                    break;
                case HISTORICO_DEPOSITOS:
                    this.nomeArquivo = getMessageS("HISTORICO_DEPOSITOS") + "-" + this.getData1() + "-" + this.getData2();

                    movimentacaoIndividual = this.cofreSatMobWeb.gerarRelatorioMovimentacoes(this.getData1(), this.getData2(),
                            this.hora1, this.hora2,
                            "DINHEIRO", this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("RelatorioDepositos").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br />");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("Periodo")).append(": ")
                            .append(Data(this.getData1())).append(" - ")
                            .append(Data(this.getData2()));

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("Depositos").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

                    relatorioBuilder.append(getMessageS("TotalDeposito")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorDepositos().toString()))
                            .append("<br />");

                    relatorioBuilder.append("<hr />").append("<br />").append("<table style=\"width: 100%;\">");
                    relatorioBuilder.append("<thead>").append("<tr>").append("<th>").append(getMessageS("Data")).append("</th>")
                            .append("<th>").append(getMessageS("Hora")).append("</th>")
                            .append("<th>").append(getMessageS("Valor")).append("</th>")
                            .append("<th>").append(getMessageS("Operador")).append("</th>")
                            .append("</thead>").append("<tbody>");

                    relatorioBuilderCSV.append(getMessageS("Data")).append(";")
                            .append(getMessageS("Hora")).append(";")
                            .append(getMessageS("Valor")).append(";")
                            .append(getMessageS("Operador")).append(";").append("\r\n");

                    for (TesCofresMov tesCofresMov : movimentacaoIndividual.getMovimentacoes()) {
                        relatorioBuilder
                                .append("<tr>")
                                .append("<td>").append(Data(tesCofresMov.getData().toString())).append("</td>")
                                .append("<td>").append(Hora(tesCofresMov.getHora())).append("</td>")
                                .append("<td>").append(Moeda(tesCofresMov.getValorDeposito().toString())).append("</td>")
                                .append("<td>").append(tesCofresMov.getNomeUsuario().toUpperCase()).append("</td>")
                                .append("</tr>");

                        relatorioBuilderCSV.append(Data(tesCofresMov.getData().toString())).append(";")
                                .append(Hora(tesCofresMov.getHora())).append(";")
                                .append(tesCofresMov.getValorDeposito().toString()).append(";")
                                .append(tesCofresMov.getNomeUsuario().toUpperCase()).append(";").append("\r\n");

                    }

                    relatorioBuilder.append("</tbody>").append("</table>").append("<hr />").append("<br />");
                    break;
                case HISTORICO_COLETAS:
                    this.nomeArquivo = getMessageS("HISTORICO_COLETAS") + "-" + this.getData1() + "-" + this.getData2();

                    movimentacaoIndividual = this.cofreSatMobWeb.gerarRelatorioMovimentacoes(this.getData1(), this.getData2(),
                            this.hora1, this.hora2,
                            "COLETA", this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("RelatorioColetas").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br />");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("Periodo")).append(": ")
                            .append(Data(this.getData1())).append(" - ")
                            .append(Data(this.getData2()));

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("Coletas").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

                    relatorioBuilder.append(getMessageS("TotalColeta")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorColetas().toString()))
                            .append("<br />");

                    relatorioBuilder.append("<hr />").append("<br />").append("<table style=\"width: 100%;\">");
                    relatorioBuilder.append("<thead>").append("<tr>").append("<th>").append(getMessageS("Data")).append("</th>")
                            .append("<th>").append(getMessageS("Hora")).append("</th>")
                            .append("<th>").append(getMessageS("Valor")).append("</th>")
                            .append("<th>").append(getMessageS("Operador")).append("</th>")
                            .append("</thead>").append("<tbody>");

                    relatorioBuilderCSV.append(getMessageS("Data")).append(";")
                            .append(getMessageS("Hora")).append(";")
                            .append(getMessageS("Valor")).append(";")
                            .append(getMessageS("Operador")).append(";").append("\r\n");

                    for (TesCofresMov tesCofresMov : movimentacaoIndividual.getMovimentacoes()) {
                        relatorioBuilder
                                .append("<tr>")
                                .append("<td>").append(Data(tesCofresMov.getData().toString())).append("</td>")
                                .append("<td>").append(Hora(tesCofresMov.getHora())).append("</td>")
                                .append("<td>").append(Moeda(tesCofresMov.getValorDeposito().toString())).append("</td>")
                                .append("<td>").append(tesCofresMov.getNomeUsuario().toUpperCase()).append("</td>")
                                .append("</tr>");

                        relatorioBuilderCSV.append(Data(tesCofresMov.getData().toString())).append(";")
                                .append(Hora(tesCofresMov.getHora())).append(";")
                                .append(tesCofresMov.getValorDeposito().toString()).append(";")
                                .append(tesCofresMov.getNomeUsuario().toUpperCase()).append(";").append("\r\n");

                    }

                    relatorioBuilder.append("</tbody>").append("</table>").append("<hr />").append("<br />");
                    break;
                case MOVIMENTACAO_GERAL_FUNCIONARIO:
                    movimentacaoGeralFuncionarios = this.cofreSatMobWeb.gerarRelatorioMovimentacaoGeralFuncionarios(this.getData1(),
                            this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);

                    this.nomeArquivo = getMessageS("MOVIMENTACAO_GERAL_FUNCIONARIO") + "-" + this.getData1();

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("RelatorioMovimentacaoGeralFuncionario").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br />");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("Data")).append(": ")
                            .append(Data(this.getData1()));

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("Movimentacoes").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

                    relatorioBuilder.append(getMessageS("TotalDeposito")).append(": ")
                            .append(Moeda(movimentacaoGeralFuncionarios.get(0).getTotalDepositos().toString()))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("TotalColeta")).append(": ")
                            .append(Moeda(movimentacaoGeralFuncionarios.get(0).getTotalColetas().toString()))
                            .append("<br />");

                    relatorioBuilderCSV.append(getMessageS("Data")).append(";")
                            .append(getMessageS("Hora")).append(";")
                            .append(getMessageS("Movimentacao")).append(";")
                            .append(getMessageS("Valor")).append(";")
                            .append(getMessageS("Operador")).append(";").append("\r\n");

                    for (MovimentacaoGeralFuncionario movimentacaoGeralFuncionario : movimentacaoGeralFuncionarios) {
                        relatorioBuilder.append("<hr />").append("<br />");

                        relatorioBuilder.append("<h3>").append(getMessageS("Operador")).append(": ")
                                .append(movimentacaoGeralFuncionario.getPessoa().getNome())
                                .append("</h3>").append("<br />");

                        for (TesCofresMov tesCofresMov : movimentacaoGeralFuncionario.getMovimentacoes()) {
                            relatorioBuilder
                                    .append(Hora(tesCofresMov.getHora())).append(" - ")
                                    .append(preencherDireita(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                            ? (getMessageS("Deposito").toUpperCase() + ": ")
                                            : (tesCofresMov.getTipoDeposito() + ": "), 10, " "))
                                    .append(Moeda(tesCofresMov.getValorDeposito().toString())).append("<br />");

                            relatorioBuilderCSV.append(Data(tesCofresMov.getData().toString())).append(";")
                                    .append(Hora(tesCofresMov.getHora())).append(";")
                                    .append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                            ? (getMessageS("Deposito").toUpperCase())
                                            : (tesCofresMov.getTipoDeposito())).append(";")
                                    .append(tesCofresMov.getValorDeposito().toString()).append(";")
                                    .append(movimentacaoGeralFuncionario.getPessoa().getNome()).append(";").append("\r\n");
                        }

                        relatorioBuilder.append("<br />").append("<br />");

                        relatorioBuilder.append(getMessageS("TotalDeposito")).append(": ")
                                .append(Moeda(movimentacaoGeralFuncionario.getValorDepositos().toString()))
                                .append("<br />");
                        relatorioBuilder.append(getMessageS("TotalColeta")).append(": ")
                                .append(Moeda(movimentacaoGeralFuncionario.getValorColetas().toString()))
                                .append("<br />");
                    }

                    relatorioBuilder.append("<hr />").append("<br />");
                    break;
                case MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO:
                    if (this.pessoa == null) {
                        throw new Exception("SelecioneOperador");
                    }
                    movimentacaoIndividual = this.cofreSatMobWeb.gerarRelatorioMovimentacaoIndividual(this.getData1(),
                            this.cofreSelecionado.getClientes().getCodCofre(), null, this.pessoa.getNome(), this.persistencia);

                    this.nomeArquivo = getMessageS("MOVIMENTACAO_INDIVIDUAL_FUNCIONARIO") + "-" + this.pessoa.getNome() + "-" + this.getData1();

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">")
                            .append("<h3>").append(getMessageS("RelatorioMovimentacaoIndividualFuncionario").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br />");

                    relatorioBuilder.append("<h3>").append(getMessageS("Operador")).append(": ").append(movimentacaoIndividual.getPessoa().getNome()).append("</h3>").append("<br />");
                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("Data")).append(": ")
                            .append(Data(this.getData1()));

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("Movimentacoes").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

                    relatorioBuilder.append(getMessageS("TotalDeposito")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorDepositos().toString()))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("TotalColeta")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorColetas().toString()))
                            .append("<br />");

                    relatorioBuilder.append("<hr />").append("<br />");

                    relatorioBuilderCSV.append(getMessageS("Data")).append(";")
                            .append(getMessageS("Hora")).append(";")
                            .append(getMessageS("Movimentacao")).append(";")
                            .append(getMessageS("Valor")).append(";")
                            .append(getMessageS("Operador")).append(";").append("\r\n");

                    for (TesCofresMov tesCofresMov : movimentacaoIndividual.getMovimentacoes()) {
                        relatorioBuilder
                                .append(Hora(tesCofresMov.getHora())).append(" - ")
                                .append(preencherDireita(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                        ? (getMessageS("Deposito").toUpperCase() + ": ")
                                        : (tesCofresMov.getTipoDeposito() + ": "), 10, " "))
                                .append(Moeda(tesCofresMov.getValorDeposito().toString())).append("<br />");

                        relatorioBuilderCSV.append(Data(tesCofresMov.getData().toString())).append(";")
                                .append(Hora(tesCofresMov.getHora())).append(";")
                                .append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                        ? (getMessageS("Deposito").toUpperCase())
                                        : (tesCofresMov.getTipoDeposito())).append(";")
                                .append(tesCofresMov.getValorDeposito().toString()).append(";")
                                .append(tesCofresMov.getNomeUsuario().toUpperCase()).append(";").append("\r\n");

                    }

                    relatorioBuilder.append("<hr />").append("<br />");
                    break;
                case MOVIMENTACAO_DIARIA:
                    movimentacaoIndividual = this.cofreSatMobWeb.gerarRelatorioMovimentacoes(
                            this.getData1(), this.cofreSelecionado.getClientes().getCodCofre(), this.persistencia);

                    this.nomeArquivo = getMessageS("MOVIMENTACAO_DIARIA") + "-" + this.getData1();

                    saldoAnterior = BigDecimal.ZERO;
                    houveColeta = false;

                    tesCofresMovsAux = this.cofreSatMobWeb.detalhesMovimentacoesAnteriores(new BigDecimal(this.cofreSelecionado.getClientes().getCodCofre()),
                            this.getData1(), this.persistencia);

                    for (TesCofresMov tesCofresMov : movimentacaoIndividual.getMovimentacoes()) {
                        if (!tesCofresMov.getTipoDeposito().equals("COLETA")) {
                            saldoAnterior = saldoAnterior.add(tesCofresMov.getValorDeposito());
                        } else {
                            houveColeta = true;
                            saldoAnterior = (tesCofresMov.getValorDeposito()).subtract(saldoAnterior);
                            break;
                        }
                    }

                    saldoDia = BigDecimal.ZERO;

                    for (TesCofresMov tesCofresMov : tesCofresMovsAux) {
                        if (!tesCofresMov.getTipoDeposito().equals("COLETA")) {
                            saldoDia = saldoDia.add(tesCofresMov.getValorDeposito());
                        } else {
                            break;
                        }
                    }

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("RelatorioMovimentacaoDiaria").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br />");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("Data")).append(": ")
                            .append(Data(this.getData1()));

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("Movimentacoes").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

                    relatorioBuilder.append(getMessageS("TotalDeposito")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorDepositos().toString()))
                            .append("<br />");
                    relatorioBuilder.append(getMessageS("TotalColeta")).append(": ")
                            .append(Moeda(movimentacaoIndividual.getValorColetas().toString()))
                            .append("<br />");

                    relatorioBuilder.append("<hr />").append("<br />");
                    if (houveColeta) {
                        relatorioBuilder.append(getMessageS("SaldoAnterior")).append(": ")
                                .append(Moeda(saldoAnterior.toString()))
                                .append("<br />");
                    }
                    relatorioBuilder.append(getMessageS("SaldoDia")).append(": ")
                            .append(Moeda(saldoDia.toString()))
                            .append("<br />");

                    relatorioBuilder.append("<hr />").append("<br />");

                    relatorioBuilderCSV.append(getMessageS("Data")).append(";")
                            .append(getMessageS("Hora")).append(";")
                            .append(getMessageS("Movimentacao")).append(";")
                            .append(getMessageS("Valor")).append(";").append("\r\n");

                    for (TesCofresMov tesCofresMov : movimentacaoIndividual.getMovimentacoes()) {
                        relatorioBuilder
                                .append(Hora(tesCofresMov.getHora())).append(" - ")
                                .append(preencherDireita(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                        ? (getMessageS("Deposito").toUpperCase() + ": ")
                                        : (tesCofresMov.getTipoDeposito() + ": "), 10, " "))
                                .append(Moeda(tesCofresMov.getValorDeposito().toString())).append("<br />");

                        relatorioBuilderCSV.append(Data(tesCofresMov.getData().toString())).append(";")
                                .append(Hora(tesCofresMov.getHora())).append(";")
                                .append(tesCofresMov.getTipoDeposito().toUpperCase().equals("DINHEIRO")
                                        ? (getMessageS("Deposito").toUpperCase())
                                        : (tesCofresMov.getTipoDeposito())).append(";")
                                .append(tesCofresMov.getValorDeposito().toString()).append(";").append("\r\n");

                    }

                    relatorioBuilder.append("<hr />").append("<br />");

                    break;
                case DEPOSITO:
                    if (this.depositoRelatorio == null) {
                        throw new Exception("SelecioneDeposito");
                    }
                    List<TesEntDN> tesEntDNs = this.cofreSatMobWeb.listarComposicoesDeposito(this.depositoRelatorio.getGuia().toString(),
                            this.depositoRelatorio.getSerie(), this.persistencia);
                    this.nomeArquivo = getMessageS("DEPOSITO") + "-" + this.getData1();

                    TesEntDN tesEntDN1 = new TesEntDN(),
                     tesEntDN2 = new TesEntDN(),
                     tesEntDN5 = new TesEntDN(),
                     tesEntDN10 = new TesEntDN(),
                     tesEntDN20 = new TesEntDN(),
                     tesEntDN50 = new TesEntDN(),
                     tesEntDN100 = new TesEntDN();

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center\">").append("<h3>")
                            .append(getMessageS("RelatorioDeposito").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<br /><p>");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("</p><p>");
                    relatorioBuilder.append(getMessageS("Operador")).append(": ").append(this.depositoRelatorio.getOperador()).append("</p><p>");
                    relatorioBuilder.append(getMessageS("Data")).append(": ")
                            .append(Data(this.depositoRelatorio.getData())).append(" ").append(Hora(this.depositoRelatorio.getHrInicio()));

                    relatorioBuilder.append("</p>").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center\">").append("<h3>").append(getMessageS("Valores").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

//                // Valores:
                    for (TesEntDN tesEntDN : tesEntDNs) {
                        if (tesEntDN.getCodigo().intValue() == 1) {
                            tesEntDN1 = tesEntDN;
                        } else if (tesEntDN.getCodigo().intValue() == 102) {
                            tesEntDN2 = tesEntDN;
                        } else if (tesEntDN.getCodigo().intValue() == 105) {
                            tesEntDN5 = tesEntDN;
                        } else if (tesEntDN.getCodigo().intValue() == 11) {
                            tesEntDN10 = tesEntDN;
                        } else if (tesEntDN.getCodigo().intValue() == 21) {
                            tesEntDN20 = tesEntDN;
                        } else if (tesEntDN.getCodigo().intValue() == 51) {
                            tesEntDN50 = tesEntDN;
                        } else if (tesEntDN.getCodigo().intValue() == 101) {
                            tesEntDN100 = tesEntDN;
                        }
                    }

                    if (tesEntDN1.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "1,00", 10, " "))
                                .append(": ").append(tesEntDN1.getQtde().intValue()).append(" ")
                                .append(tesEntDN1.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (tesEntDN2.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "2,00", 10, " "))
                                .append(": ").append(tesEntDN2.getQtde().intValue()).append(" ")
                                .append(tesEntDN2.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (tesEntDN5.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "5,00", 10, " "))
                                .append(": ").append(tesEntDN5.getQtde().intValue()).append(" ")
                                .append(tesEntDN5.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (tesEntDN10.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "10,00", 10, " "))
                                .append(": ").append(tesEntDN10.getQtde().intValue()).append(" ")
                                .append(tesEntDN10.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (tesEntDN20.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "20,00", 10, " "))
                                .append(": ").append(tesEntDN20.getQtde().intValue()).append(" ")
                                .append(tesEntDN20.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (tesEntDN50.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "50,00", 10, " "))
                                .append(": ").append(tesEntDN50.getQtde().intValue()).append(" ")
                                .append(tesEntDN50.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (tesEntDN100.getQtde().intValue() > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "100,00", 10, " "))
                                .append(": ").append(tesEntDN100.getQtde().intValue()).append(" ")
                                .append(tesEntDN100.getQtde().intValue() == 1 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />").append("<h3>")
                            .append(getMessageS("Total")).append(": ")
                            .append(Moeda(this.depositoRelatorio.getValor().toString())).append("</h3>").append("<br />");

                    break;
                case COLETA:
                    if (this.coletaRelatorio == null) {
                        throw new Exception("SelecioneColeta");
                    }
                    List<TesSaidasDN> tesSaidasDNs = this.cofreSatMobWeb.listarComposicoesColeta(this.coletaRelatorio.getGuia(),
                            this.coletaRelatorio.getSerie(), this.persistencia);
                    this.nomeArquivo = getMessageS("COLETA") + "-" + this.getData1();

                    TesSaidasDN tesSaidasDN1 = new TesSaidasDN(),
                     tesSaidasDN2 = new TesSaidasDN(),
                     tesSaidasDN5 = new TesSaidasDN(),
                     tesSaidasDN10 = new TesSaidasDN(),
                     tesSaidasDN20 = new TesSaidasDN(),
                     tesSaidasDN50 = new TesSaidasDN(),
                     tesSaidasDN100 = new TesSaidasDN();

                    relatorioBuilder.append("<br />").append("<div style=\"text-align: center;\">").append("<h3>")
                            .append(getMessageS("RelatorioColeta").toUpperCase())
                            .append("</h3>").append("</div>").append("<br />").append("<p>");

                    relatorioBuilder.append(preencherDireita(getMessageS("Equip") + ": " + this.cofreSelecionado.getClientes().getCodCofre(), 25, " "))
                            .append("</p><p>");
                    relatorioBuilder.append(getMessageS("Operador")).append(": ").append(this.coletaRelatorio.getOperador()).append("</p><p>");
                    relatorioBuilder.append(getMessageS("Data")).append(": ")
                            .append(Data(this.coletaRelatorio.getData())).append(" ").append(Hora(this.coletaRelatorio.getHrInicio()));

                    relatorioBuilder.append("</p><br />").append("<hr />").append("<br />");

                    relatorioBuilder.append("<div style=\"text-align: center;\">").append("<h3>").append(getMessageS("Valores").toUpperCase())
                            .append("</h3>").append("</div>")
                            .append("<br />");

//                // Valores:
                    for (TesSaidasDN tesSaidasDN : tesSaidasDNs) {
                        if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("1")) == 0) {
                            tesSaidasDN1 = tesSaidasDN;
                        } else if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("102")) == 0) {
                            tesSaidasDN2 = tesSaidasDN;
                        } else if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("105")) == 0) {
                            tesSaidasDN5 = tesSaidasDN;
                        } else if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("11")) == 0) {
                            tesSaidasDN10 = tesSaidasDN;
                        } else if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("21")) == 0) {
                            tesSaidasDN20 = tesSaidasDN;
                        } else if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("51")) == 0) {
                            tesSaidasDN50 = tesSaidasDN;
                        } else if (null != tesSaidasDN.getCodigo() && new BigDecimal(tesSaidasDN.getCodigo()).compareTo(new BigDecimal("101")) == 0) {
                            tesSaidasDN100 = tesSaidasDN;
                        }
                    }

                    if (null != tesSaidasDN1.getQtde() && new BigDecimal(tesSaidasDN1.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "1,00", 10, " "))
                                .append(": ").append(tesSaidasDN1.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN1.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (null != tesSaidasDN2.getQtde() && new BigDecimal(tesSaidasDN2.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "2,00", 10, " "))
                                .append(": ").append(tesSaidasDN2.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN2.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (null != tesSaidasDN5.getQtde() && new BigDecimal(tesSaidasDN5.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "5,00", 10, " "))
                                .append(": ").append(tesSaidasDN5.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN5.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (null != tesSaidasDN10.getQtde() && new BigDecimal(tesSaidasDN10.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "10,00", 10, " "))
                                .append(": ").append(tesSaidasDN10.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN10.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (null != tesSaidasDN20.getQtde() && new BigDecimal(tesSaidasDN20.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "20,00", 10, " "))
                                .append(": ").append(tesSaidasDN20.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN20.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (null != tesSaidasDN50.getQtde() && new BigDecimal(tesSaidasDN50.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "50,00", 10, " "))
                                .append(": ").append(tesSaidasDN50.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN50.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }
                    if (null != tesSaidasDN100.getQtde() && new BigDecimal(tesSaidasDN100.getQtde()).compareTo(BigDecimal.ZERO) > 0) {
                        relatorioBuilder
                                .append(preencherDireita(getMessageS("$") + "100,00", 10, " "))
                                .append(": ").append(tesSaidasDN100.getQtde().replace(".0", "")).append(" ")
                                .append(new BigDecimal(tesSaidasDN100.getQtde()).compareTo(BigDecimal.ONE) == 0 ? getMessageS("nota") : getMessageS("notas"))
                                .append("<br />");
                    }

                    relatorioBuilder.append("<br />").append("<hr />").append("<br />").append("<h3>")
                            .append(getMessageS("Total")).append(": ")
                            .append(Moeda(this.coletaRelatorio.getTotalDN())).append("</h3>").append("<br />");

                    break;
            }

            relatorioBuilder.append("</div>");
            this.relatorio = relatorioBuilder.toString();
            this.relatorioCSV = relatorioBuilderCSV.toString();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarRelatorioDownloadCSV() {
        try {
            if (this.relatorioCSV == null || this.relatorioCSV.equals("")) {
                carregarRelatorio();
            }

            File file = File.createTempFile(this.nomeArquivo, "csv");
            FileUtils.writeStringToFile(file, removeAcento(this.relatorioCSV), StandardCharsets.UTF_8);

            InputStream stream = new FileInputStream(file);

            this.arquivoRelatorio = new DefaultStreamedContent(stream, "csv", this.nomeArquivo + ".csv");

//            stream.close();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    private org.w3c.dom.Document obterDocumentDeByte(InputStream documentoXml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(documentoXml);
    }

    public void gerarRelatorioDownloadPDF() {
        try {
            if (this.relatorio == null) {
                carregarRelatorio();
            }
            InputStream stream = new ByteArrayInputStream(this.relatorio.getBytes());

            String Conteudo = this.relatorio.replace("<hr />", "").replace("<br />", "").replace("<div style=\"text-align: center\"></div>", "");

            ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            InputStream input = new ByteArrayInputStream(Conteudo.getBytes());
            //Document doc = tidy.parseDOM(input, null);
            org.w3c.dom.Document doc = obterDocumentDeByte(input);
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(osPdf);
            input.close();

            /*  ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            org.w3c.dom.Document doc = tidy.parseDOM(stream, null);
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(osPdf);*/
            InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

            this.arquivoRelatorio = new DefaultStreamedContent(inputPDF, "pdf", this.nomeArquivo + ".pdf");
            osPdf.close();
            stream.close();
            inputPDF.close();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

    }

    public Date getCalendario() {
        return calendario;
    }

    public void setCalendario(Date calendario) {
        this.calendario = calendario;
    }

    public List<TesCofresResClientes> getListaMovimentacao() {
        return listaMovimentacao;
    }

    public void setListaMovimentacao(List<TesCofresResClientes> listaMovimentacao) {
        this.listaMovimentacao = listaMovimentacao;
    }

    public TesCofresResClientes getMovimentacaoSelecionada() {
        return movimentacaoSelecionada;
    }

    public void setMovimentacaoSelecionada(TesCofresResClientes movimentacaoSelecionada) {
        this.movimentacaoSelecionada = movimentacaoSelecionada;
    }

    public TesCofresResClientes getCofreSelecionado() {
        return cofreSelecionado;
    }

    public void setCofreSelecionado(TesCofresResClientes cofreSelecionado) {
        this.cofreSelecionado = cofreSelecionado;
    }

    public List<TesCofresMov> getDetalhesMovimentacao() {
        return detalhesMovimentacao;
    }

    public void setDetalhesMovimentacao(List<TesCofresMov> detalhesMovimentacao) {
        this.detalhesMovimentacao = detalhesMovimentacao;
    }

    public List<MobileHWSt> getListaStatus() {
        return listaStatus;
    }

    public void setListaStatus(List<MobileHWSt> listaStatus) {
        this.listaStatus = listaStatus;
    }

    public MobileHW getInfoCofre() {
        return infoCofre;
    }

    public void setInfoCofre(MobileHW infoCofre) {
        this.infoCofre = infoCofre;
    }

    public String getData() {
        return this.calendario.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public String getData1() {
        return this.calendario1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public String getData2() {
        return this.calendario2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public String getBanco() {
        return banco;
    }

    public PDFOptions getPdfOptMovimentacoes() {
        return pdfOptMovimentacoes;
    }

    public void setPdfOptMovimentacoes(PDFOptions pdfOptMovimentacoes) {
        this.pdfOptMovimentacoes = pdfOptMovimentacoes;
    }

    public Date getCalendario1() {
        return calendario1;
    }

    public void setCalendario1(Date calendario1) {
        this.calendario1 = calendario1;
    }

    public Date getCalendario2() {
        return calendario2;
    }

    public void setCalendario2(Date calendario2) {
        this.calendario2 = calendario2;
    }

    public BigDecimal getValorDepositos() {
        return valorDepositos;
    }

    public void setValorDepositos(BigDecimal valorDepositos) {
        this.valorDepositos = valorDepositos;
    }

    public BigDecimal getValorColetas() {
        return valorColetas;
    }

    public void setValorColetas(BigDecimal valorColetas) {
        this.valorColetas = valorColetas;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public int getTotalDepositos() {
        return totalDepositos;
    }

    public void setTotalDepositos(int totalDepositos) {
        this.totalDepositos = totalDepositos;
    }

    public int getTotalColetas() {
        return totalColetas;
    }

    public void setTotalColetas(int totalColetas) {
        this.totalColetas = totalColetas;
    }

    public List<UsuarioSatMobWeb> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<UsuarioSatMobWeb> usuarios) {
        this.usuarios = usuarios;
    }

    public UsuarioSatMobWeb getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioSatMobWeb usuario) {
        this.usuario = usuario;
    }

    public int getFlagUsuario() {
        return flagUsuario;
    }

    public void setFlagUsuario(int flagUsuario) {
        this.flagUsuario = flagUsuario;
    }

    public boolean isCadastroNovaPessoa() {
        return cadastroNovaPessoa;
    }

    public void setCadastroNovaPessoa(boolean cadastroNovaPessoa) {
        this.cadastroNovaPessoa = cadastroNovaPessoa;
    }

    public List<Pessoa> getListaPessoa() {
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }

    public Map getNiveis() {
        return niveis;
    }

    public void setNiveis(Map niveis) {
        this.niveis = niveis;
    }

    public List<SASGrupos> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SASGrupos> grupos) {
        this.grupos = grupos;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public boolean isSomenteAlertas() {
        return somenteAlertas;
    }

    public void setSomenteAlertas(boolean somenteAlertas) {
        this.somenteAlertas = somenteAlertas;
    }

    public boolean isExibirTodos() {
        return exibirTodos;
    }

    public void setExibirTodos(boolean exibirTodos) {
        this.exibirTodos = exibirTodos;
    }

    public String getFiltroNomeCofre() {
        return filtroNomeCofre;
    }

    public void setFiltroNomeCofre(String filtroNomeCofre) {
        this.filtroNomeCofre = filtroNomeCofre;
    }

    public String getFiltroNumeroCofre() {
        return filtroNumeroCofre;
    }

    public void setFiltroNumeroCofre(String filtroNumeroCofre) {
        this.filtroNumeroCofre = filtroNumeroCofre;
    }

    public Map getTiposRelatorios() {
        return tiposRelatorios;
    }

    public void setTiposRelatorios(Map tiposRelatorios) {
        this.tiposRelatorios = tiposRelatorios;
    }

    public String getRelatorio() {
        return relatorio;
    }

    public void setRelatorio(String relatorio) {
        this.relatorio = relatorio;
    }

    public TipoRelatorio getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(TipoRelatorio tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public List<TesEntrada> getDepositosRelatorio() {
        return depositosRelatorio;
    }

    public void setDepositosRelatorio(List<TesEntrada> depositosRelatorio) {
        this.depositosRelatorio = depositosRelatorio;
    }

    public TesEntrada getDepositoRelatorio() {
        return depositoRelatorio;
    }

    public void setDepositoRelatorio(TesEntrada depositoRelatorio) {
        this.depositoRelatorio = depositoRelatorio;
    }

    public List<TesCofresRes> getDepositosRelatorioItem() {
        return depositosRelatorioitem;
    }

    public void setDepositosRelatorioItem(List<TesCofresRes> depositosRelatorioItem) {
        this.depositosRelatorioitem = depositosRelatorioItem;
    }
    
    
    public List<TesSaidas> getColetasRelatorio() {
        return coletasRelatorio;
    }

    public void setColetasRelatorio(List<TesSaidas> coletasRelatorio) {
        this.coletasRelatorio = coletasRelatorio;
    }

    public TesSaidas getColetaRelatorio() {
        return coletaRelatorio;
    }

    public void setColetaRelatorio(TesSaidas coletaRelatorio) {
        this.coletaRelatorio = coletaRelatorio;
    }

    public boolean isRelatorioDuasDatas() {
        return relatorioDuasDatas;
    }

    public void setRelatorioDuasDatas(boolean relatorioDuasDatas) {
        this.relatorioDuasDatas = relatorioDuasDatas;
    }

    public StreamedContent getArquivoRelatorio() {
        return arquivoRelatorio;
    }

    public void setArquivoRelatorio(StreamedContent arquivoRelatorio) {
        this.arquivoRelatorio = arquivoRelatorio;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getHora2() {
        return hora2;
    }

    public void setHora2(String hora2) {
        this.hora2 = hora2;
    }

    public boolean isMostrarFiliais() {
        return mostrarFiliais;
    }

    public void setMostrarFiliais(boolean mostrarFiliais) {
        this.mostrarFiliais = mostrarFiliais;
    }
}

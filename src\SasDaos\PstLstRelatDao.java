/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PstLstRelat;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PstLstRelatDao {

    public boolean getFiltroWeb(String secao, String codFil, String codigo, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = true;
            String sql = " SELECT ISNULL(FiltroWeb, '1') FiltroWeb FROM PstLstRelat"
                    + " WHERE Secao = ? AND CodFil = ? AND Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codFil);
            consulta.setString(codigo);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("FiltroWeb").contains("1");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstLstRelatDao.getFiltroWeb - " + e.getMessage() + "\r\n"
                    + " SELECT ISNULL(FiltroWeb, '1') FiltroWeb FROM PstLstRelat"
                    + " WHERE Secao = " + secao + " AND CodFil = " + codFil + " AND Codigo = " + codigo);
        }
    }

    public List<PstLstRelat> getRelatorios(String secao, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<PstLstRelat> retorno = new ArrayList<>();
            String sql = " select * from PstLstRelat "
                    + " where secao = ? and codFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codFil);
            consulta.select();
            PstLstRelat pstLstRelat;
            while (consulta.Proximo()) {
                pstLstRelat = new PstLstRelat();
                pstLstRelat.setSecao(consulta.getString("Secao"));
                pstLstRelat.setCodFil(consulta.getString("CodFil"));
                pstLstRelat.setCodigo(consulta.getString("Codigo"));
                pstLstRelat.setDescricao(consulta.getString("Descricao"));
                pstLstRelat.setFoto(consulta.getString("Foto").contains("1"));
                pstLstRelat.setVideo(consulta.getString("Video").contains("1"));
                pstLstRelat.setOperador(consulta.getString("Operador"));
                pstLstRelat.setDt_Alter(consulta.getString("Dt_Alter"));
                pstLstRelat.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(pstLstRelat);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstLstRelatDao.getRelatorios - " + e.getMessage() + "\r\n"
                    + " select * from PstLstRelat "
                    + " where secao = " + secao + " and codFil = " + codFil);
        }
    }
}

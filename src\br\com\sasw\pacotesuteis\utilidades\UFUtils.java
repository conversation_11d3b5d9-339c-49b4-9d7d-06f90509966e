/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

import java.util.HashMap;

/**
 *
 * <AUTHOR>
 */
public class UFUtils {
    
    public static String nome(String sigla){
        return init().get(sigla).getNome();
    }
    
    public static String codigo(String sigla){
        return String.valueOf(init().get(sigla).getCodigo());
    }

    private static HashMap<String, UF> init() {
        HashMap<String, UF> estados = new HashMap<>();
        estados.put("RO", new UF(11, "Rondônia", "RO"));
        estados.put("AC", new UF(12, "Acre", "AC"));
        estados.put("AM", new UF(13, "Amazonas", "AM"));
        estados.put("RR", new UF(14, "<PERSON><PERSON><PERSON>", "RR"));
        estados.put("PA", new UF(15, "Par<PERSON>", "PA"));
        estados.put("AP", new UF(16, "Amapá", "AP"));
        estados.put("TO", new UF(17, "Tocantins", "TO"));
        estados.put("MA", new UF(21, "Maranhão", "MA"));
        estados.put("PI", new UF(22, "Piauí", "PI"));
        estados.put("CE", new UF(23, "Ceará", "CE"));
        estados.put("RN", new UF(24, "Rio Grande do Norte", "RN"));
        estados.put("PB", new UF(25, "Paraíba", "PB"));
        estados.put("PE", new UF(26, "Pernambuco", "PE"));
        estados.put("AL", new UF(27, "Alagoas", "AL"));
        estados.put("SE", new UF(28, "Sergipe", "SE"));
        estados.put("BA", new UF(29, "Bahia", "BA"));
        estados.put("MG", new UF(31, "Minas Gerais", "MG"));
        estados.put("ES", new UF(32, "Espírito Santo", "ES"));
        estados.put("RJ", new UF(33, "Rio de Janeiro", "RJ"));
        estados.put("SP", new UF(35, "São Paulo", "SP"));
        estados.put("PR", new UF(41, "Paraná", "PR"));
        estados.put("SC", new UF(42, "Santa Catarina", "SC"));
        estados.put("RS", new UF(43, "Rio Grande do Sul", "RS"));
        estados.put("MS", new UF(50, "Mato Grosso do Sul", "MS"));
        estados.put("MT", new UF(51, "Mato Grosso", "MT"));
        estados.put("GO", new UF(52, "Goiás", "GO"));
        estados.put("DF", new UF(53, "Distrito Federal", "DF"));
        return estados;
    }

    private static class UF {

        private int codigo;
        private String sigla;
        private String nome;

        public UF(int codigo, String sigla, String nome) {
            this.codigo = codigo;
            this.sigla = sigla;
            this.nome = nome;
        }

        public int getCodigo() {
            return codigo;
        }

        public void setCodigo(int codigo) {
            this.codigo = codigo;
        }

        public String getSigla() {
            return sigla;
        }

        public void setSigla(String sigla) {
            this.sigla = sigla;
        }

        public String getNome() {
            return nome;
        }

        public void setNome(String nome) {
            this.nome = nome;
        }

    }
}

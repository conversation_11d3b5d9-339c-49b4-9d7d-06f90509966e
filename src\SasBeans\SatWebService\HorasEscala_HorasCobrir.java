/*
 */
package SasBeans.SatWebService;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class HorasEscala_HorasCobrir {

    public String nRed;
    public BigDecimal qtdeFunc;
    public BigDecimal qtdeEscala;
    public BigDecimal cHDia;
    public BigDecimal qtdeCobrir;
    public BigDecimal qtdePresenca;

    public HorasEscala_HorasCobrir() {
        this.nRed = "";
        this.qtdeFunc = BigDecimal.ZERO;
        this.qtdeEscala = BigDecimal.ZERO;
        this.cHDia = BigDecimal.ZERO;
        this.qtdeCobrir = BigDecimal.ZERO;
        this.qtdePresenca = BigDecimal.ZERO;
    }

    public String getnRed() {
        return nRed;
    }

    public void setnRed(String nRed) {
        this.nRed = null == nRed ? "" : nRed;
    }

    public BigDecimal getQtdeFunc() {
        return qtdeFunc;
    }

    public void setQtdeFunc(BigDecimal qtdeFunc) {
        this.qtdeFunc = null == qtdeFunc ? BigDecimal.ZERO : qtdeFunc;
    }

    public BigDecimal getQtdeEscala() {
        return qtdeEscala;
    }

    public void setQtdeEscala(BigDecimal qtdeEscala) {
        this.qtdeEscala = null == qtdeEscala ? BigDecimal.ZERO : qtdeEscala;
    }

    public BigDecimal getcHDia() {
        return cHDia;
    }

    public void setcHDia(BigDecimal cHDia) {
        this.cHDia = null == cHDia ? BigDecimal.ZERO : cHDia;
    }

    public BigDecimal getQtdeCobrir() {
        return qtdeCobrir;
    }

    public void setQtdeCobrir(BigDecimal qtdeCobrir) {
        this.qtdeCobrir = null == qtdeCobrir ? BigDecimal.ZERO : qtdeCobrir;
    }

    public BigDecimal getQtdePresenca() {
        return qtdePresenca;
    }

    public void setQtdePresenca(BigDecimal qtdePresenca) {
        this.qtdePresenca = null == qtdePresenca ? BigDecimal.ZERO : qtdePresenca;
    }
}

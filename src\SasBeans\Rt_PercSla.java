/*
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rt_PercSla {

    private BigDecimal Sequencia;
    private int Parada;
    private BigDecimal CodFil;
    private String HrCheg;
    private String HrSaida;
    private String HrChegVei;
    private String HrSaidaVei;
    private BigDecimal Atraso;
    private BigDecimal TempoEspera;
    private BigDecimal OS;
    private String OperIncl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public int getParada() {
        return Parada;
    }

    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public String getHrCheg() {
        return HrCheg;
    }

    public void setHrCheg(String HrCheg) {
        this.HrCheg = HrCheg;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public String getHrChegVei() {
        return HrChegVei;
    }

    public void setHrChegVei(String HrChegVei) {
        this.HrChegVei = HrChegVei;
    }

    public String getHrSaidaVei() {
        return HrSaidaVei;
    }

    public void setHrSaidaVei(String HrSaidaVei) {
        this.HrSaidaVei = HrSaidaVei;
    }

    public BigDecimal getAtraso() {
        return Atraso;
    }

    public void setAtraso(BigDecimal Atraso) {
        this.Atraso = Atraso;
    }

    public BigDecimal getTempoEspera() {
        return TempoEspera;
    }

    public void setTempoEspera(BigDecimal TempoEspera) {
        this.TempoEspera = TempoEspera;
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(BigDecimal OS) {
        this.OS = OS;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

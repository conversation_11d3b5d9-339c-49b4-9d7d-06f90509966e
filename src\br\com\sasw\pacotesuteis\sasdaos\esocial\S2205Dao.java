/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2205;
import SasBeans.ESocial.S2205.Dependentes;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2205Dao {

    public List<S2205> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT X.* FROM(SELECT DISTINCT\n"
                    + "Convert(BigInt,Funcion.Matr) Matr, \n"
                    + "Filiais.TipoPessoa ideEmpregador_tpInsc, \n"
                    + "Filiais.CNPJ ideEmpregador_nrInsc,\n"
                    + "Funcion.CPF ideTrabalhador_cpfTrab,\n"
                    + "Funcion.PIS dadosTrabalhador_nisTrab,\n"
                    + "Funcion.Nome dadosTrabalhador_nmTrab,\n"
                    + "Funcion.Sexo dadosTrabalhador_sexo,\n"
                    + "Funcion.Raca dadosTrabalhador_racaCor,\n"
                    + "Funcion.EstCivil dadosTrabalhador_estCiv,\n"
                    + "Funcion.Instrucao dadosTrabalhador_grauInstr,\n"
                    + "Substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto,\n"
                    + " Case when (CharIndex('/',Naturalid) > 1) then \n"
                    + "      (Select Max(CodIBGE) From Municipios where Nome = Substring(Naturalid,1,(CharIndex('/',Naturalid)-1)) \n"
                    + "                                             and UF = Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) and Len(Naturalid) > 0) \n"
                    + " else '0' end nascimento_codMunic, \n"
                    + " Case when (CharIndex('/',Naturalid) > 1) then \n"
                    + " (Select Substring(Naturalid,(CharIndex('/',Naturalid)+1),2) UF from Funcion where Matr = FPMensal.Matr) \n"
                    + " else '0' end nascimento_uf, \n"
                    + " '105' nascimento_paisNascto, \n"
                    + " '105' nascimento_paisNac, \n"
                    + " Funcion.Mae nascimento_nmMae, Funcion.Pai nascimento_nmPai, \n"
                    + " Funcion.CTPS_Nro CTPS_nrCtps, Funcion.CTPS_Serie nrCtps_serieCtps, Funcion.CTPS_UF nrCtps_ufCtps, \n"
                    + " Funcion.RG RG_nrRg, Funcion.OrgEmis RG_orgaoEmissor, \n"
                    + " substring(replace(convert(varchar,Funcion.RgDtEmis,111),'/','-'),0,11) RG_dtExped, \n"
                    + " Funcion.CNH CNH_nrRegCnh, substring(replace(convert(varchar,Funcion.Dt_VenCNH,111),'/','-'),0,11) CNH_dtValid, \n"
                    + " Funcion.UF_CNH CNH_ufCnh, Funcion.Categoria CNH_categoriaCnh, \n"
                    + " Funcion.Endereco brasil_dscLograd, Funcion.Numero brasil_nrLograd, \n"
                    + " Funcion.Complemento brasil_complemento, Funcion.Bairro brasil_bairro, Funcion.CEP brasil_cep, \n"
                    + " Municipios.CodIBGE brasil_codMunic, Funcion.UF brasil_uf, Funcion.DefFis InfoDeficiencia_defFisica,  \n"
                    + " Funcion.DefFisTipo,  Funcion.Fone1 contato_fonePrinc, Funcion.Fone2 contato_foneAlternat, \n"
                    + " Funcion.Email contato_emailPrinc, Replace(convert(Varchar,Funcion.Dt_Alter,111),'/','-') alteracao_dtAlteracao, \n"
                    + "                     (select max(sucesso) from  ( \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n"
                    + "                             From XmleSocial z \n"
                    + "                             where z.Identificador = Funcion.Matr \n"
                    + "                                 and z.evento = 'S-2205' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%aguardando%' \n"
                    + "                                         or z.Xml_Retorno = ''\n"
                    + "                                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))\n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.Matr \n"
                    + "                                 and z.evento = 'S-2205' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%<ocorrencia>%' \n"
                    + "                                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.Matr \n"
                    + "                                 and z.evento = 'S-2205' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'\n"
                    + "                                      or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso \n"
                    + "from FPMensal (nolock) \n"
                    + " Left join Funcion (nolock) on Funcion.Matr = FPMEnsal.Matr "
                    + " Left Join Filiais on Filiais.codFil = Funcion.CodFil \n"
                    + " Left Join Municipios on Municipios.nome = Funcion.Cidade \n"
                    + "                     and Municipios.UF   = Funcion.UF \n"
                    + " where Convert(Varchar,FPMensal.CodMovFP) = REPLACE(RIGHT(?,5),'-','') \n"
                    + "   and FPMensal.CodFil = ? \n"
                    //+ " order by Funcion.Dt_Alter desc ";
                    + ") AS X\n"
                    + " ORDER BY alteracao_dtAlteracao";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(compet);
            consulta.setString(codFil);

            consulta.select();
            List<S2205> retorno = new ArrayList<>();
            S2205 s2205;

            while (consulta.Proximo()) {
                s2205 = new S2205();

                s2205.setSucesso(consulta.getInt("sucesso"));

                s2205.setIdeEvento_indRetif("1");
                s2205.setIdeEvento_procEmi("1");
                s2205.setIdeEvento_tpAmb(ambiente);
                s2205.setIdeEvento_verProc("Satellite eSocial");

                s2205.setMatr(consulta.getString("Matr"));

                s2205.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2205.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2205.setIdeTrabalhador_cpfTrab(consulta.getString("ideTrabalhador_cpfTrab"));
                s2205.setAlteracao_dtAlteracao(consulta.getString("alteracao_dtAlteracao"));
                s2205.setDadosTrabalhador_nisTrab(consulta.getString("dadosTrabalhador_nisTrab"));
                s2205.setDadosTrabalhador_nmTrab(consulta.getString("dadosTrabalhador_nmTrab"));
                s2205.setDadosTrabalhador_sexo(consulta.getString("dadosTrabalhador_sexo"));
                s2205.setDadosTrabalhador_racaCor(consulta.getString("dadosTrabalhador_racaCor"));
                s2205.setDadosTrabalhador_estCiv(consulta.getString("dadosTrabalhador_estCiv"));
                s2205.setDadosTrabalhador_grauInstr(consulta.getString("dadosTrabalhador_grauInstr"));

                s2205.setNascimento_dtNascto(consulta.getString("nascimento_dtNascto"));
                s2205.setNascimento_codMunic(consulta.getString("nascimento_codMunic"));
                s2205.setNascimento_uf(consulta.getString("nascimento_uf"));
                s2205.setNascimento_paisNascto(consulta.getString("nascimento_paisNascto"));
                s2205.setNascimento_paisNac(consulta.getString("nascimento_paisNac"));
                s2205.setNascimento_nmMae(consulta.getString("nascimento_nmMae"));
                s2205.setNascimento_nmPai(consulta.getString("nascimento_nmPai"));

                s2205.setCTPS_nrCtps(consulta.getString("CTPS_nrCtps"));
                s2205.setNrCtps_serieCtps(consulta.getString("nrCtps_serieCtps"));
                s2205.setNrCtps_ufCtps(consulta.getString("nrCtps_ufCtps"));

                s2205.setRG_nrRg(consulta.getString("RG_nrRg"));
                s2205.setRG_orgaoEmissor(consulta.getString("RG_orgaoEmissor"));
                s2205.setRG_dtExped(consulta.getString("RG_dtExped"));

                s2205.setCNH_nrRegCnh(consulta.getString("CNH_nrRegCnh"));

                s2205.setCNH_ufCnh(consulta.getString("CNH_ufCnh"));
                s2205.setCNH_dtValid(consulta.getString("CNH_dtValid"));

                s2205.setBrasil_tpLograd("");
                s2205.setBrasil_dscLograd(consulta.getString("brasil_dscLograd"));
                s2205.setBrasil_nrLograd(consulta.getString("brasil_nrLograd"));
                s2205.setBrasil_complemento(consulta.getString("brasil_complemento"));
                s2205.setBrasil_bairro(consulta.getString("brasil_bairro"));
                s2205.setBrasil_cep(consulta.getString("brasil_cep"));
                s2205.setBrasil_codMunic(consulta.getString("brasil_codMunic"));
                s2205.setBrasil_uf(consulta.getString("brasil_uf"));

                s2205.setInfoDeficiencia_defFisica("N");
                s2205.setInfoDeficiencia_defVisual("N");
                s2205.setInfoDeficiencia_defAuditiva("N");
                s2205.setInfoDeficiencia_defMental("N");
                s2205.setInfoDeficiencia_defIntelectual("N");
                s2205.setInfoDeficiencia_reabReadap("N");
                s2205.setInfoDeficiencia_infoCota("N");
                s2205.setInfoDeficiencia_observacao("N");                
                
                if (consulta.getString("InfoDeficiencia_defFisica").equals("S")) {
                    s2205.setInfoDeficiencia_defFisica(consulta.getString("InfoDeficiencia_defFisica"));
                    if (consulta.getString("DefFisTipo").equals("1")){
                       s2205.setInfoDeficiencia_defFisica("S");
                       s2205.setInfoDeficiencia_defVisual("N");
                       s2205.setInfoDeficiencia_defAuditiva("N");
                       s2205.setInfoDeficiencia_defMental("N");
                       s2205.setInfoDeficiencia_defIntelectual("N");
                       s2205.setInfoDeficiencia_reabReadap("N");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                       
                     }
                    if (consulta.getString("InfoDeficiencia_defFisica").equals("2")){                                       
                    //    s2205.setInfoDeficiencia_defFisica("N");
                       s2205.setInfoDeficiencia_defVisual("N");
                       s2205.setInfoDeficiencia_defAuditiva("S");
                       s2205.setInfoDeficiencia_defMental("N");
                       s2205.setInfoDeficiencia_defIntelectual("N");
                       s2205.setInfoDeficiencia_reabReadap("N");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                                           
                    }else if (consulta.getString("InfoDeficiencia_defFisica").equals("3")){                                       
                      //  s2205.setInfoDeficiencia_defFisica("N");
                       s2205.setInfoDeficiencia_defVisual("S");
                       s2205.setInfoDeficiencia_defAuditiva("N");
                       s2205.setInfoDeficiencia_defMental("N");
                       s2205.setInfoDeficiencia_defIntelectual("N");
                       s2205.setInfoDeficiencia_reabReadap("N");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                                           
                    }else if (consulta.getString("InfoDeficiencia_defFisica").equals("4")){                                       
                      //  s2205.setInfoDeficiencia_defFisica("N");
                       s2205.setInfoDeficiencia_defVisual("N");
                       s2205.setInfoDeficiencia_defAuditiva("N");
                       s2205.setInfoDeficiencia_defMental("S");
                       s2205.setInfoDeficiencia_defIntelectual("N");
                       s2205.setInfoDeficiencia_reabReadap("N");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                                           
                    }else if (consulta.getString("InfoDeficiencia_defFisica").equals("5")){                                       
                     //   s2205.setInfoDeficiencia_defFisica("S");
                       s2205.setInfoDeficiencia_defVisual("N");
                       s2205.setInfoDeficiencia_defAuditiva("N");
                       s2205.setInfoDeficiencia_defMental("N");
                       s2205.setInfoDeficiencia_defIntelectual("N");
                       s2205.setInfoDeficiencia_reabReadap("N");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                                           
                    }else if (consulta.getString("InfoDeficiencia_defFisica").equals("6")){                                       
//                        s2205.setInfoDeficiencia_defFisica("N");
                       s2205.setInfoDeficiencia_defVisual("N");
                       s2205.setInfoDeficiencia_defAuditiva("N");
                       s2205.setInfoDeficiencia_defMental("N");
                       s2205.setInfoDeficiencia_defIntelectual("N");
                       s2205.setInfoDeficiencia_reabReadap("S");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                                           
                    }else if (consulta.getString("InfoDeficiencia_defFisica").equals("7")){                                       
  //                      s2205.setInfoDeficiencia_defFisica("N");
                       s2205.setInfoDeficiencia_defVisual("N");
                       s2205.setInfoDeficiencia_defAuditiva("N");
                       s2205.setInfoDeficiencia_defMental("N");
                       s2205.setInfoDeficiencia_defIntelectual("S");
                       s2205.setInfoDeficiencia_reabReadap("N");
                       s2205.setInfoDeficiencia_infoCota("N");
                       s2205.setInfoDeficiencia_observacao("N");                                           
                    }
                } else {
                    s2205.setInfoDeficiencia_defFisica("N");
                    s2205.setInfoDeficiencia_defVisual("N");
                    s2205.setInfoDeficiencia_defAuditiva("N");
                    s2205.setInfoDeficiencia_defMental("N");
                    s2205.setInfoDeficiencia_defIntelectual("N");
                    s2205.setInfoDeficiencia_reabReadap("N");
                    s2205.setInfoDeficiencia_infoCota("N");
                    s2205.setInfoDeficiencia_observacao("N");
                }
                s2205.setContato_fonePrinc(consulta.getString("contato_fonePrinc"));
                s2205.setContato_foneAlternat(consulta.getString("contato_foneAlternat"));
                s2205.setContato_emailPrinc(consulta.getString("contato_emailPrinc"));
                s2205.setContato_emailAlternat("");
                if (null != s2205.getIdeEmpregador_nrInsc() && !s2205.getIdeEmpregador_nrInsc().equals("")) {
                    retorno.add(s2205);
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception();
        }
    }

    public List<S2205> getDependentes(List<S2205> s2205, String codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Convert(Bigint,Funcion.Matr) Matr, F5_Dep.Tipo dependente_tpDep, F5_Dep.Nome dependente_nmDep, "
                    + " substring(replace(convert(varchar,F5_Dep.Dt_Nasc,111),'/','-'),0,11) dependente_dtNascto, "
                    + " F5_Dep.DepIR dependente_depIRRF, F5_Dep.DepSF dependente_depSF, F5_Dep.CPF dependente_cpfDep,"
                    + " Funcion.cpf Trabalhador_indPriEmpr, F5_DEep.Sexo sexoDep "
                    + " from FPMensal "
                    + " Left Join Funcion on Funcion.Matr = FPMensal.Matr "
                    + " Inner Join F5_Dep  on F5_Dep.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' and Funcion.CodFil = ?  and F5_Dep.Tipo not in ('T','S') "// and F5_Dep.Tipo <> 'S' "
                    + "         and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate()))";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();
            S2205 s2205_dependente = new S2205();
            Dependentes dependente;
            int indice;
            while (consulta.Proximo()) {
                dependente = new Dependentes();
                dependente.setDependente_tpDep(consulta.getString("dependente_tpDep"));
                dependente.setDependente_nmDep(consulta.getString("dependente_nmDep"));
                dependente.setDependente_dtNascto(consulta.getString("dependente_dtNascto"));
                dependente.setDependente_depIRRF(consulta.getString("dependente_depIRRF"));
                dependente.setDependente_depSF(consulta.getString("dependente_depSF"));
                dependente.setDependente_cpfDep(consulta.getString("dependente_cpfDep"));
                dependente.setDependente_sexoDep(consulta.getString("sexoDep"));                
                dependente.setDependente_incTrab("N");

//                s2205_dependente.setIdeTrabalhador_cpfTrab(consulta.getString("Trabalhador_indPriEmpr"));
                indice = s2205.indexOf(s2205_dependente);
                if (indice >= 0) {
                    s2205.get(indice).getDependentes().add(dependente);
                }
//                for(S2205 s : s2205){
//                    if(s.getVinculo_matricula().equals(consulta.getString("Vinculo_matricula"))){
//                        s.getDependentes().add(dependente);
//                        break;
//                    }
//                }
            }
            consulta.Close();
            return s2205;
        } catch (Exception e) {
            throw new Exception("S2205Dao.getDependentes - " + e.getMessage() + "\r\n"
                    + "Select Funcion.Matr, F5_Dep.Tipo dependente_tpDep, F5_Dep.Nome dependente_nmDep, "
                    + " substring(replace(convert(varchar,F5_Dep.Dt_Nasc,111),'/','-'),0,11) dependente_dtNascto, F5_Dep.DepIR dependente_depIRRF, "
                    + " F5_Dep.DepSF dependente_depSF, F5_DEep.Sexo sexoDep "
                    + " from FPMensal "
                    + " Left Join Funcion on Funcion.Matr = FPMensal.Matr "
                    + " Inner Join F5_Dep  on F5_Dep.Matr = Funcion.Matr "
                    + " where TipoFP = 'MEN' and Funcion.CodFil = " + codfil + " and F5_Dep.Tipo not in ('T','S') "
                    + "         and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) "
                    + "                             from FPPeriodos where DtFecha <= Convert(Date,Getdate()))");
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Pedidos.PedidosRefeicaoSatMobWeb;
import Dados.Persistencia;
import SasBeans.Pedido;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class PedidosLazyList extends LazyDataModel<Pedido> {

    private static final long serialVersionUID = 1L;
    private List<Pedido> pedidos;
    private final PedidosRefeicaoSatMobWeb pedidosSatMobWeb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public PedidosLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.pedidosSatMobWeb = new PedidosRefeicaoSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<Pedido> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.pedidos = this.pedidosSatMobWeb.listagemPaginadaSemFlag(first, pageSize, filters, this.codPessoa, this.persistencia);

            //set the  total of players
            setRowCount(this.pedidosSatMobWeb.contagemSemFlag(filters, this.codPessoa, this.persistencia));
            //set the page size
            setPageSize(pageSize);

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.pedidos;
    }

    @Override
    public Object getRowKey(Pedido pedido) {
        return pedido.getChavePrimaria();
    }

    @Override
    public Pedido getRowData(String chavePrimaria) {
        for (Pedido pedido : this.pedidos) {
            if (chavePrimaria.equals(pedido.getChavePrimaria())) {
                return pedido;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

}

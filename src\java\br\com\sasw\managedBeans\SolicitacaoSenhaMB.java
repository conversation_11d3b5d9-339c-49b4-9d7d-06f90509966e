/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans;

import Controller.QueueFech.QueueFechController;
import SasBeansCompostas.QueueFechSolicitacaoSenhaDTO;
import java.util.List;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;

/**
 *
 * <AUTHOR>
 */
@Named
@ViewScoped
public class SolicitacaoSenhaMB extends BaseBeanMB {

    private QueueFechController queueController;
    private boolean temPermissao;
    private String matricula;
    private List<QueueFechSolicitacaoSenhaDTO> solicitacoes;
    private QueueFechSolicitacaoSenhaDTO solicitacaoSelecionada, solicitacaoSalvar;
    private boolean omitirNaoConfirmado = false;
    private String senha;

    public SolicitacaoSenhaMB() throws Exception {
        super();
        temPermissao = login.isPermissaoSolicitacaoSenhaMobile(); // FIXME: 10109
        matricula = (String) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("filial");
        queueController = new QueueFechController(persistencia, logerro, caminho);
    }

    public void carregarLista() {
        // apenas atualize se não estiver no meio de uma tentativa de enviar senha
        if (solicitacaoSalvar == null) {
            try {
                solicitacoes = queueController.getAllQueueFechSolicitacaoSenha(temPermissao, this.codFil, dataTela, omitirNaoConfirmado);
            } catch (Exception e) {
                displayError(e.getMessage());
            }
        }
    }

    public void atualizar() {
        carregarLista();
        if (solicitacoes != null && !solicitacoes.isEmpty()) {
            PrimeFaces.current().executeScript("PF('dlgSolicitacaoSenha').show();");
        }
    }

    public void onRowSelected() {
        PrimeFaces.current().executeScript("limparMarkers();");
        String codigoJS = "addCoordinates([";

        if (solicitacaoSelecionada.coordenadasExistem()) {
            String lat = solicitacaoSelecionada.getLatitude();
            String lng = solicitacaoSelecionada.getLongitude();
            codigoJS += "{latitude: " + lat + ", longitude: " + lng + ", tipo: 'caminhao'}, ";
        } else {
            displayWarn("CoordenadasNaoExistem");
        }

        if (solicitacaoSelecionada.coordenadasClienteExistem()) {
            String lat = solicitacaoSelecionada.getClienteLatitude();
            String lng = solicitacaoSelecionada.getClienteLongitude();
            codigoJS += "{latitude: " + lat + ", longitude: " + lng + ", tipo: 'cliente'}, ";
        } else {
            displayWarn("CoordenadasClienteNaoExistem");
        }
        codigoJS += "]);";

        PrimeFaces.current().executeScript(codigoJS);
    }

    private void ativarModalSenha() {
        senha = "";
        PrimeFaces.current().resetInputs("senhaForm:panel");
        PrimeFaces.current().executeScript("PF('dlgPedirSenha').show();");
    }

    private void sucessoAceitar() {
        solicitacaoSalvar = null;
        carregarLista();
        PrimeFaces.current().executeScript("PF('dlgSolicitacaoSenha').hide();");
        displayInfo("SenhaAceitaSucesso");
    }

    private void sucessoReprovar() {
        carregarLista();
        displayInfo("SenhaRecusadaSucesso");
    }

    public void aprovarSelecionado() {
        if (!temPermissao) {
            displayError("PermissaoNegada");
            return;
        }
        if (solicitacaoSelecionada == null) {
            displayWarn("SelecioneSolicitacao");
            return;
        }
        aprovar();
    }

    private void aprovar() {
        try {
            solicitacaoSalvar = new QueueFechSolicitacaoSenhaDTO(solicitacaoSelecionada);

            if (queueController.isOperacaoNaoPermitida(solicitacaoSalvar)) {
                throw new Exception("SenhaJaEnviada");
            }

            if (persistencia.getEmpresa().equals("PRESERVE")) {
                // TODO
                displayError("PRESERVE not ready yet.");
                return;
            }

            queueController.adaptaTipoFech(solicitacaoSalvar);
            if (queueController.isOutrasFechaduras(solicitacaoSalvar)) {
                queueController.updateSolicitacaoSenha(solicitacaoSalvar, null);
                sucessoAceitar();
                return;
            }

            if (solicitacaoSalvar.isAtivo()) {
                updateFechaduraAtiva();
//                queueController.updateFechaduraAtiva(solicitacaoSalvar, codFil, operador, matricula);
//                sucessoAceitar();
            } else {
                ativarModalSenha();
            }
        } catch (NumberFormatException e) {
            displayError(e.getMessage());
        } catch (Exception e) {
            displayError("ErroSalvarBD");
        } finally {
            solicitacaoSalvar = null;
        }
    }

    private void updateFechaduraAtiva() {
        try {
            int tipoOperacaoLG = queueController.getTipoOperacaoLG(solicitacaoSalvar);

            if (tipoOperacaoLG != -1) {
                queueController.updateFechaduraAtiva(solicitacaoSalvar, codFil, operador, matricula, tipoOperacaoLG);
                sucessoAceitar();
            } else {
                // criar formulário
                PrimeFaces.current().executeScript("PF('dlgFechadura').show();");
            }
        } catch (Exception e) {
            displayError(e.getMessage());
        }
    }

    public void atribuirSenha() {
        if (senha == null) {
            PrimeFaces.current().executeScript("PF('dlgCancelarSenha').jq.click();");
        } else {
            if (temPermissao) {
                try {
                    queueController.updateSolicitacaoSenha(solicitacaoSalvar, senha);
                    PrimeFaces.current().executeScript("PF('dlgPedirSenha').hide();");
                    sucessoAceitar();
                } catch (Exception e) {
                    displayError("ErroSalvarBD");
                }
            } else {
                displayError("PermissaoNegada");
            }
        }
    }

    public void cancelarUpdate() {
        solicitacaoSalvar = null;
    }

    public void reprovarSelecionado() {
        if (!temPermissao) {
            displayError("PermissaoNegada");
            return;
        }
        if (solicitacaoSelecionada == null) {
            displayWarn("SelecioneSolicitacao");
            return;
        }
        reprovar();
    }

    private void reprovar() {
        try {
            String sequencia = solicitacaoSelecionada.getSequencia();
            queueController.rejeitaSolicitacaoSenha(temPermissao, sequencia);
            sucessoReprovar();
        } catch (Exception e) {
            displayError(e.getMessage());
        } finally {
            solicitacaoSalvar = null;
        }
    }

    public boolean isTemPermissao() {
        return temPermissao;
    }

    public List<QueueFechSolicitacaoSenhaDTO> getSolicitacoes() {
        return solicitacoes;
    }

    public QueueFechSolicitacaoSenhaDTO getSolicitacaoSelecionada() {
        return solicitacaoSelecionada;
    }

    public void setSolicitacaoSelecionada(QueueFechSolicitacaoSenhaDTO solicitacaoSelecionada) {
        this.solicitacaoSelecionada = solicitacaoSelecionada;
    }

    public boolean isOmitirNaoConfirmado() {
        return omitirNaoConfirmado;
    }

    public void setOmitirNaoConfirmado(boolean omitirNaoConfirmado) {
        this.omitirNaoConfirmado = omitirNaoConfirmado;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }
}

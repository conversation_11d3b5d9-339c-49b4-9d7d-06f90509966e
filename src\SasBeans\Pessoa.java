package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Pessoa {

    private BigDecimal Codigo;
    private BigDecimal CodPessoaWEB;
    private String Nome;
    private String Situacao;
    private String Dt_Situac;
    private String Endereco;
    private String Numero;
    private String Bairro;
    private String Cidade;
    private BigDecimal CodCidade;
    private String UF;
    private String CEP;
    private String Fone1;
    private String Fone2;
    private String Email;
    private String Regiao;
    private String EstCivil;
    private String Conjuge;
    private String Pai;
    private String Mae;
    private String RG;
    private String RGOrgEmis;
    private String RgDtEmis;
    private String CPF;
    private String TitEleit;
    private String TitZona;
    private String TitSecao;
    private String PIS;
    private String CNH;
    private String CNHDtVenc;
    private String CNHCat;
    private String Reservista;
    private String ReservCat;
    private String CTPS_Nro;
    private String CTPS_Serie;
    private String CTPS_UF;
    private String CTPS_Emis;
    private String Dt_nasc;
    private String Naturalid;
    private int Instrucao;
    private String Sexo;
    private String Raca;
    private String GrupoSang;
    private BigDecimal Altura;
    private BigDecimal Peso;
    private String Indicacao;
    private String Dt_FormIni;
    private String Dt_FormFim;
    private String LocalForm;
    private String Certific;
    private String Reg_PF;
    private String Reg_PFUF;
    private String Reg_PFDt;
    private String CarNacVig;
    private String DtValCNV;
    private String Reg_DRT;
    private String Dt_Recicl;
    private String Dt_VenCurs;
    private String Dt_ExameMe;
    private String Dt_Psico;
    private String ExtTV;
    private String ExtSPP;
    private String ExtEscolta;
    private String Obs;
    private String Funcao;
    private BigDecimal Matr;
    private int Dig01;
    private int Dig02;
    private int Dig03;
    private int Dig04;
    private int Dig05;
    private int Dig06;
    private int Dig07;
    private int Dig08;
    private int Dig09;
    private int Dig10;
    private String PW;
    private String Bottom;
    private String BottomII;
    private BigDecimal CodPrest;
    private BigDecimal CodVisit;
    private BigDecimal PretSalario;
    private String Apresen;
    private String Ct_Banco;
    private String Ct_Agencia;
    private String Ct_Conta;
    private int Regional;
    private String PWWeb;
    private String CargoPretend;
    private String SecaoPretend;
    private String EscalaPretend;
    private int HorarioPretend;
    private String SindicatoPdr;
    private BigDecimal CodFilPretend;
    private String PWPortal;
    private String Dt_UltAcPortal;
    private BigDecimal CodSatellite;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String Naturalid_UF;
    private String Mae_Prof;
    private String Mae_Nacion;
    private String Pai_Prof;
    private String Pai_Nacion;
    private String Conjuge_Prof;
    private String Conjuge_Nasc;
    private String Olhos;
    private String Cabelo;
    private String DefeitosFis;
    private String Tatuagem;
    private String Camisa;
    private String Sapato;
    private String Calca;
    private String Jaqueta;
    private String Tipo_Moradia;
    private String Religiao;
    private String Religiao_Prat;
    private String Reservista_RM;
    private String NomeEscola;
    private String PorteArma;

    private String CodDieta;
    private String CodCli;
    private String Complemento;

    private String Nivelx;
    private String Senha;

    private String ChaveAcesso;

    private String EnvPontoEmail;
    private String EnvPontoWhp;

    public Pessoa() {
    }

    public Pessoa(Pessoa original) {
        Codigo = original.getCodigo();
        CodPessoaWEB = original.getCodPessoaWEB();
        Nome = original.getNome();
        Situacao = original.getSituacao();
        Dt_Situac = original.getDt_Situac();
        Endereco = original.getEndereco();
        Numero = original.getNumero();
        Bairro = original.getBairro();
        Cidade = original.getCidade();
        CodCidade = original.getCodCidade();
        UF = original.getUF();
        CEP = original.getCEP();
        Fone1 = original.getFone1();
        Fone2 = original.getFone2();
        Email = original.getEmail();
        Regiao = original.getRegiao();
        EstCivil = original.getEstCivil();
        Conjuge = original.getConjuge();
        Pai = original.getPai();
        Mae = original.getMae();
        RG = original.getRG();
        RGOrgEmis = original.getRGOrgEmis();
        RgDtEmis = original.getRgDtEmis();
        CPF = original.getCPF();
        TitEleit = original.getTitEleit();
        TitZona = original.getTitZona();
        TitSecao = original.getTitSecao();
        PIS = original.getPIS();
        CNH = original.getCNH();
        CNHDtVenc = original.getCNHDtVenc();
        CNHCat = original.getCNHCat();
        Reservista = original.getReservista();
        ReservCat = original.getReservCat();
        CTPS_Nro = original.getCTPS_Nro();
        CTPS_Serie = original.getCTPS_Serie();
        CTPS_UF = original.getCTPS_UF();
        CTPS_Emis = original.getCTPS_Emis();
        Dt_nasc = original.getDt_nasc();
        Naturalid = original.getNaturalid();
        Instrucao = original.getInstrucao();
        Sexo = original.getSexo();
        Raca = original.getRaca();
        GrupoSang = original.getGrupoSang();
        Altura = original.getAltura();
        Peso = original.getPeso();
        Indicacao = original.getIndicacao();
        Dt_FormIni = original.getDt_FormIni();
        Dt_FormFim = original.getDt_FormFim();
        LocalForm = original.getLocalForm();
        Certific = original.getCertific();
        Reg_PF = original.getReg_PF();
        Reg_PFUF = original.getReg_PFUF();
        Reg_PFDt = original.getReg_PFDt();
        CarNacVig = original.getCarNacVig();
        DtValCNV = original.getDtValCNV();
        Reg_DRT = original.getReg_DRT();
        Dt_Recicl = original.getDt_Recicl();
        Dt_VenCurs = original.getDt_VenCurs();
        Dt_ExameMe = original.getDt_ExameMe();
        Dt_Psico = original.getDt_Psico();
        ExtTV = original.getExtTV();
        ExtSPP = original.getExtSPP();
        ExtEscolta = original.getExtEscolta();
        Obs = original.getObs();
        Funcao = original.getFuncao();
        Matr = original.getMatr();
        Dig01 = original.getDig01();
        Dig02 = original.getDig02();
        Dig03 = original.getDig03();
        Dig04 = original.getDig04();
        Dig05 = original.getDig05();
        Dig06 = original.getDig06();
        Dig07 = original.getDig07();
        Dig08 = original.getDig08();
        Dig09 = original.getDig09();
        Dig10 = original.getDig10();
        PW = original.getPW();
        Bottom = original.getBottom();
        BottomII = original.getBottomII();
        CodPrest = original.getCodPrest();
        CodVisit = original.getCodVisit();
        PretSalario = original.getPretSalario();
        Apresen = original.getApresen();
        Ct_Banco = original.getCt_Banco();
        Ct_Agencia = original.getCt_Agencia();
        Ct_Conta = original.getCt_Conta();
        Regional = original.getRegional();
        PWWeb = original.getPWWeb();
        CargoPretend = original.getCargoPretend();
        SecaoPretend = original.getSecaoPretend();
        EscalaPretend = original.getEscalaPretend();
        HorarioPretend = original.getHorarioPretend();
        SindicatoPdr = original.getSindicatoPdr();
        CodFilPretend = original.getCodFilPretend();
        PWPortal = original.getPWPortal();
        Dt_UltAcPortal = original.getDt_UltAcPortal();
        CodSatellite = original.getCodSatellite();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
        Naturalid_UF = original.getNaturalid_UF();
        Mae_Prof = original.getMae_Prof();
        Mae_Nacion = original.getMae_Nacion();
        Pai_Prof = original.getPai_Prof();
        Pai_Nacion = original.getPai_Nacion();
        Conjuge_Prof = original.getConjuge_Prof();
        Conjuge_Nasc = original.getConjuge_Nasc();
        Olhos = original.getOlhos();
        Cabelo = original.getCabelo();
        DefeitosFis = original.getDefeitosFis();
        Tatuagem = original.getTatuagem();
        Camisa = original.getCamisa();
        Sapato = original.getSapato();
        Calca = original.getCalca();
        Jaqueta = original.getJaqueta();
        Tipo_Moradia = original.getTipo_Moradia();
        Religiao = original.getReligiao();
        Religiao_Prat = original.getReligiao_Prat();
        Reservista_RM = original.getReservista_RM();
        NomeEscola = original.getNomeEscola();
        PorteArma = original.getPorteArma();
        CodDieta = original.getCodDieta();
        CodCli = original.getCodCli();
        Complemento = original.getComplemento();
        EnvPontoEmail = original.getEnvPontoEmail();
        EnvPontoWhp = original.getEnvPontoWhp();
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 23 * hash + Objects.hashCode(this.Codigo);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Pessoa other = (Pessoa) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

    public BigDecimal getCodigo() {
        return this.Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public BigDecimal getCodPessoaWEB() {
        return CodPessoaWEB;
    }

    public void setCodPessoaWEB(BigDecimal CodPessoaWEB) {
        this.CodPessoaWEB = CodPessoaWEB;
    }

    public void setCodPessoaWEB(String CodPessoaWEB) {
        try {
            this.CodPessoaWEB = new BigDecimal(CodPessoaWEB);
        } catch (Exception e) {
            this.CodPessoaWEB = null;
        }
    }

    public void setCodCidade(BigDecimal CodCidade) {
        this.CodCidade = CodCidade;
    }

    public void setAltura(BigDecimal Altura) {
        this.Altura = Altura;
    }

    public void setPeso(BigDecimal Peso) {
        this.Peso = Peso;
    }

    public void setMatr(BigDecimal Matr) {
        this.Matr = Matr;
    }

    public void setCodPrest(BigDecimal CodPrest) {
        this.CodPrest = CodPrest;
    }

    public void setCodVisit(BigDecimal CodVisit) {
        this.CodVisit = CodVisit;
    }

    public void setCodFilPretend(BigDecimal CodFilPretend) {
        this.CodFilPretend = CodFilPretend;
    }

    public void setCodSatellite(BigDecimal CodSatellite) {
        this.CodSatellite = CodSatellite;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = new BigDecimal("0");
        }
    }

    public String getNome() {
        return this.Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getSituacao() {
        return this.Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getDt_Situac() {
        return this.Dt_Situac;
    }

    public void setDt_Situac(String Dt_Situac) {
        this.Dt_Situac = Dt_Situac;
    }

    public String getEndereco() {
        return this.Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getNumero() {
        return this.Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getBairro() {
        return this.Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return this.Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public BigDecimal getCodCidade() {
        return this.CodCidade;
    }

    public void setCodCidade(String CodCidade) {
        try {
            this.CodCidade = new BigDecimal(CodCidade);
        } catch (Exception e) {
            this.CodCidade = new BigDecimal("0");
        }
    }

    public String getUF() {
        return this.UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCEP() {
        return this.CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone1() {
        return this.Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getFone2() {
        return this.Fone2;
    }

    public void setFone2(String Fone2) {
        this.Fone2 = Fone2;
    }

    public String getEmail() {
        return this.Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getRegiao() {
        return this.Regiao;
    }

    public void setRegiao(String Regiao) {
        this.Regiao = Regiao;
    }

    public String getEstCivil() {
        return this.EstCivil;
    }

    public void setEstCivil(String EstCivil) {
        this.EstCivil = EstCivil;
    }

    public String getConjuge() {
        return this.Conjuge;
    }

    public void setConjuge(String Conjuge) {
        this.Conjuge = Conjuge;
    }

    public String getPai() {
        return this.Pai;
    }

    public void setPai(String Pai) {
        this.Pai = Pai;
    }

    public String getMae() {
        return this.Mae;
    }

    public void setMae(String Mae) {
        this.Mae = Mae;
    }

    public String getRG() {
        return this.RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getRGOrgEmis() {
        return this.RGOrgEmis;
    }

    public void setRGOrgEmis(String RGOrgEmis) {
        this.RGOrgEmis = RGOrgEmis;
    }

    public String getRgDtEmis() {
        return this.RgDtEmis;
    }

    public void setRgDtEmis(String RgDtEmis) {
        this.RgDtEmis = RgDtEmis;
    }

    public String getCPF() {
        return this.CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getTitEleit() {
        return this.TitEleit;
    }

    public void setTitEleit(String TitEleit) {
        this.TitEleit = TitEleit;
    }

    public String getTitZona() {
        return this.TitZona;
    }

    public void setTitZona(String TitZona) {
        this.TitZona = TitZona;
    }

    public String getTitSecao() {
        return this.TitSecao;
    }

    public void setTitSecao(String TitSecao) {
        this.TitSecao = TitSecao;
    }

    public String getPIS() {
        return this.PIS;
    }

    public void setPIS(String PIS) {
        this.PIS = PIS;
    }

    public String getCNH() {
        return this.CNH;
    }

    public void setCNH(String CNH) {
        this.CNH = CNH;
    }

    public String getCNHDtVenc() {
        return this.CNHDtVenc;
    }

    public void setCNHDtVenc(String CNHDtVenc) {
        this.CNHDtVenc = CNHDtVenc;
    }

    public String getCNHCat() {
        return this.CNHCat;
    }

    public void setCNHCat(String CNHCat) {
        this.CNHCat = CNHCat;
    }

    public String getReservista() {
        return this.Reservista;
    }

    public void setReservista(String Reservista) {
        this.Reservista = Reservista;
    }

    public String getReservCat() {
        return this.ReservCat;
    }

    public void setReservCat(String ReservCat) {
        this.ReservCat = ReservCat;
    }

    public String getCTPS_Nro() {
        return this.CTPS_Nro;
    }

    public void setCTPS_Nro(String CTPS_Nro) {
        this.CTPS_Nro = CTPS_Nro;
    }

    public String getCTPS_Serie() {
        return this.CTPS_Serie;
    }

    public void setCTPS_Serie(String CTPS_Serie) {
        this.CTPS_Serie = CTPS_Serie;
    }

    public String getCTPS_UF() {
        return this.CTPS_UF;
    }

    public void setCTPS_UF(String CTPS_UF) {
        this.CTPS_UF = CTPS_UF;
    }

    public String getCTPS_Emis() {
        return this.CTPS_Emis;
    }

    public void setCTPS_Emis(String CTPS_Emis) {
        this.CTPS_Emis = CTPS_Emis;
    }

    public String getDt_nasc() {
        return this.Dt_nasc;
    }

    public void setDt_nasc(String Dt_nasc) {
        this.Dt_nasc = Dt_nasc;
    }

    public String getNaturalid() {
        return this.Naturalid;
    }

    public void setNaturalid(String Naturalid) {
        this.Naturalid = Naturalid;
    }

    public int getInstrucao() {
        return this.Instrucao;
    }

    public void setInstrucao(int Instrucao) {
        this.Instrucao = Instrucao;
    }

    public String getSexo() {
        return this.Sexo;
    }

    public void setSexo(String Sexo) {
        this.Sexo = Sexo;
    }

    public String getRaca() {
        return this.Raca;
    }

    public void setRaca(String Raca) {
        this.Raca = Raca;
    }

    public String getGrupoSang() {
        return this.GrupoSang;
    }

    public void setGrupoSang(String GrupoSang) {
        this.GrupoSang = GrupoSang;
    }

    public BigDecimal getAltura() {
        return this.Altura;
    }

    public void setAltura(String Altura) {
        try {
            this.Altura = new BigDecimal(Altura);
        } catch (Exception e) {
            this.Altura = new BigDecimal("0");
        }
    }

    public BigDecimal getPeso() {
        return this.Peso;
    }

    public void setPeso(String Peso) {
        try {
            this.Peso = new BigDecimal(Peso);
        } catch (Exception e) {
            this.Peso = new BigDecimal("0");
        }
    }

    public String getIndicacao() {
        return this.Indicacao;
    }

    public void setIndicacao(String Indicacao) {
        this.Indicacao = Indicacao;
    }

    public String getDt_FormIni() {
        return this.Dt_FormIni;
    }

    public void setDt_FormIni(String Dt_FormIni) {
        this.Dt_FormIni = Dt_FormIni;
    }

    public String getDt_FormFim() {
        return this.Dt_FormFim;
    }

    public void setDt_FormFim(String Dt_FormFim) {
        this.Dt_FormFim = Dt_FormFim;
    }

    public String getLocalForm() {
        return this.LocalForm;
    }

    public void setLocalForm(String LocalForm) {
        this.LocalForm = LocalForm;
    }

    public String getCertific() {
        return this.Certific;
    }

    public void setCertific(String Certific) {
        this.Certific = Certific;
    }

    public String getReg_PF() {
        return this.Reg_PF;
    }

    public void setReg_PF(String Reg_PF) {
        this.Reg_PF = Reg_PF;
    }

    public String getReg_PFUF() {
        return this.Reg_PFUF;
    }

    public void setReg_PFUF(String Reg_PFUF) {
        this.Reg_PFUF = Reg_PFUF;
    }

    public String getReg_PFDt() {
        return this.Reg_PFDt;
    }

    public void setReg_PFDt(String Reg_PFDt) {
        this.Reg_PFDt = Reg_PFDt;
    }

    public String getCarNacVig() {
        return this.CarNacVig;
    }

    public void setCarNacVig(String CarNacVig) {
        this.CarNacVig = CarNacVig;
    }

    public String getDtValCNV() {
        return this.DtValCNV;
    }

    public void setDtValCNV(String DtValCNV) {
        this.DtValCNV = DtValCNV;
    }

    public String getReg_DRT() {
        return this.Reg_DRT;
    }

    public void setReg_DRT(String Reg_DRT) {
        this.Reg_DRT = Reg_DRT;
    }

    public String getDt_Recicl() {
        return this.Dt_Recicl;
    }

    public void setDt_Recicl(String Dt_Recicl) {
        this.Dt_Recicl = Dt_Recicl;
    }

    public String getDt_VenCurs() {
        return this.Dt_VenCurs;
    }

    public void setDt_VenCurs(String Dt_VenCurs) {
        this.Dt_VenCurs = Dt_VenCurs;
    }

    public String getDt_ExameMe() {
        return this.Dt_ExameMe;
    }

    public void setDt_ExameMe(String Dt_ExameMe) {
        this.Dt_ExameMe = Dt_ExameMe;
    }

    public String getDt_Psico() {
        return this.Dt_Psico;
    }

    public void setDt_Psico(String Dt_Psico) {
        this.Dt_Psico = Dt_Psico;
    }

    public String getExtTV() {
        return this.ExtTV;
    }

    public void setExtTV(String ExtTV) {
        this.ExtTV = ExtTV;
    }

    public String getExtSPP() {
        return this.ExtSPP;
    }

    public void setExtSPP(String ExtSPP) {
        this.ExtSPP = ExtSPP;
    }

    public String getExtEscolta() {
        return this.ExtEscolta;
    }

    public void setExtEscolta(String ExtEscolta) {
        this.ExtEscolta = ExtEscolta;
    }

    public String getObs() {
        return this.Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getFuncao() {
        return this.Funcao;
    }

    public void setFuncao(String Funcao) {
        this.Funcao = Funcao;
    }

    public BigDecimal getMatr() {
        return this.Matr;
    }

    public void setMatr(String Matr) {
        try {
            this.Matr = new BigDecimal(Matr);
        } catch (Exception e) {
            this.Matr = new BigDecimal("0");
        }
    }

    public int getDig01() {
        return this.Dig01;
    }

    public void setDig01(int Dig01) {
        this.Dig01 = Dig01;
    }

    public int getDig02() {
        return this.Dig02;
    }

    public void setDig02(int Dig02) {
        this.Dig02 = Dig02;
    }

    public int getDig03() {
        return this.Dig03;
    }

    public void setDig03(int Dig03) {
        this.Dig03 = Dig03;
    }

    public int getDig04() {
        return this.Dig04;
    }

    public void setDig04(int Dig04) {
        this.Dig04 = Dig04;
    }

    public int getDig05() {
        return this.Dig05;
    }

    public void setDig05(int Dig05) {
        this.Dig05 = Dig05;
    }

    public int getDig06() {
        return this.Dig06;
    }

    public void setDig06(int Dig06) {
        this.Dig06 = Dig06;
    }

    public int getDig07() {
        return this.Dig07;
    }

    public void setDig07(int Dig07) {
        this.Dig07 = Dig07;
    }

    public int getDig08() {
        return this.Dig08;
    }

    public void setDig08(int Dig08) {
        this.Dig08 = Dig08;
    }

    public int getDig09() {
        return this.Dig09;
    }

    public void setDig09(int Dig09) {
        this.Dig09 = Dig09;
    }

    public int getDig10() {
        return this.Dig10;
    }

    public void setDig10(int Dig10) {
        this.Dig10 = Dig10;
    }

    public String getPW() {
        return this.PW;
    }

    public void setPW(String PW) {
        this.PW = PW;
    }

    public String getBottom() {
        return this.Bottom;
    }

    public void setBottom(String Bottom) {
        this.Bottom = Bottom;
    }

    public String getBottomII() {
        return this.BottomII;
    }

    public void setBottomII(String BottomII) {
        this.BottomII = BottomII;
    }

    public void setPretSalario(BigDecimal PretSalario) {
        this.PretSalario = PretSalario;
    }

    public BigDecimal getCodPrest() {
        return this.CodPrest;
    }

    public void setCodPrest(String CodPrest) {
        try {
            this.CodPrest = new BigDecimal(CodPrest);
        } catch (Exception e) {
            this.CodPrest = new BigDecimal("0");
        }
    }

    public BigDecimal getCodVisit() {
        return this.CodVisit;
    }

    public void setCodVisit(String CodVisit) {
        try {
            this.CodVisit = new BigDecimal(CodVisit);
        } catch (Exception e) {
            this.CodVisit = new BigDecimal("0");
        }
    }

    public BigDecimal getPretSalario() {
        return this.PretSalario;
    }

    public void setPretSalario(String PretSalario) {
        try {
            this.PretSalario = new BigDecimal(PretSalario);
        } catch (Exception e) {
            this.PretSalario = new BigDecimal("0");
        }
    }

    public String getApresen() {
        return this.Apresen;
    }

    public void setApresen(String Apresen) {
        this.Apresen = Apresen;
    }

    public String getCt_Banco() {
        return this.Ct_Banco;
    }

    public void setCt_Banco(String Ct_Banco) {
        this.Ct_Banco = Ct_Banco;
    }

    public String getCt_Agencia() {
        return this.Ct_Agencia;
    }

    public void setCt_Agencia(String Ct_Agencia) {
        this.Ct_Agencia = Ct_Agencia;
    }

    public String getCt_Conta() {
        return this.Ct_Conta;
    }

    public void setCt_Conta(String Ct_Conta) {
        this.Ct_Conta = Ct_Conta;
    }

    public int getRegional() {
        return this.Regional;
    }

    public void setRegional(int Regional) {
        this.Regional = Regional;
    }

    public String getPWWeb() {
        return this.PWWeb;
    }

    public void setPWWeb(String PWWeb) {
        this.PWWeb = PWWeb;
    }

    public String getCargoPretend() {
        return this.CargoPretend;
    }

    public void setCargoPretend(String CargoPretend) {
        this.CargoPretend = CargoPretend;
    }

    public String getSecaoPretend() {
        return this.SecaoPretend;
    }

    public void setSecaoPretend(String SecaoPretend) {
        this.SecaoPretend = SecaoPretend;
    }

    public String getEscalaPretend() {
        return this.EscalaPretend;
    }

    public void setEscalaPretend(String EscalaPretend) {
        this.EscalaPretend = EscalaPretend;
    }

    public int getHorarioPretend() {
        return this.HorarioPretend;
    }

    public void setHorarioPretend(int HorarioPretend) {
        this.HorarioPretend = HorarioPretend;
    }

    public String getSindicatoPdr() {
        return this.SindicatoPdr;
    }

    public void setSindicatoPdr(String SindicatoPdr) {
        this.SindicatoPdr = SindicatoPdr;
    }

    public BigDecimal getCodFilPretend() {
        return this.CodFilPretend;
    }

    public void setCodFilPretend(String CodFilPretend) {
        try {
            this.CodFilPretend = new BigDecimal(CodFilPretend);
        } catch (Exception e) {
            this.CodFilPretend = new BigDecimal("0");
        }
    }

    public String getPWPortal() {
        return this.PWPortal;
    }

    public void setPWPortal(String PWPortal) {
        this.PWPortal = PWPortal;
    }

    public String getDt_UltAcPortal() {
        return this.Dt_UltAcPortal;
    }

    public void setDt_UltAcPortal(String Dt_UltAcPortal) {
        this.Dt_UltAcPortal = Dt_UltAcPortal;
    }

    public BigDecimal getCodSatellite() {
        return this.CodSatellite;
    }

    public void setCodSatellite(String CodSatellite) {
        try {
            this.CodSatellite = new BigDecimal(CodSatellite);
        } catch (Exception e) {
            this.CodSatellite = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return this.Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return this.Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return this.Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNaturalid_UF() {
        return Naturalid_UF;
    }

    public void setNaturalid_UF(String Naturalid_UF) {
        this.Naturalid_UF = Naturalid_UF;
    }

    public String getMae_Prof() {
        return Mae_Prof;
    }

    public void setMae_Prof(String Mae_Prof) {
        this.Mae_Prof = Mae_Prof;
    }

    public String getMae_Nacion() {
        return Mae_Nacion;
    }

    public void setMae_Nacion(String Mae_Nacion) {
        this.Mae_Nacion = Mae_Nacion;
    }

    public String getPai_Prof() {
        return Pai_Prof;
    }

    public void setPai_Prof(String Pai_Prof) {
        this.Pai_Prof = Pai_Prof;
    }

    public String getPai_Nacion() {
        return Pai_Nacion;
    }

    public void setPai_Nacion(String Pai_Nacion) {
        this.Pai_Nacion = Pai_Nacion;
    }

    public String getConjuge_Prof() {
        return Conjuge_Prof;
    }

    public void setConjuge_Prof(String Conjuge_Prof) {
        this.Conjuge_Prof = Conjuge_Prof;
    }

    public String getConjuge_Nasc() {
        return Conjuge_Nasc;
    }

    public void setConjuge_Nasc(String Conjuge_Nasc) {
        this.Conjuge_Nasc = Conjuge_Nasc;
    }

    public String getOlhos() {
        return Olhos;
    }

    public void setOlhos(String Olhos) {
        this.Olhos = Olhos;
    }

    public String getCabelo() {
        return Cabelo;
    }

    public void setCabelo(String Cabelo) {
        this.Cabelo = Cabelo;
    }

    public String getDefeitosFis() {
        return DefeitosFis;
    }

    public void setDefeitosFis(String DefeitosFis) {
        this.DefeitosFis = DefeitosFis;
    }

    public String getTatuagem() {
        return Tatuagem;
    }

    public void setTatuagem(String Tatuagem) {
        this.Tatuagem = Tatuagem;
    }

    public String getCamisa() {
        return Camisa;
    }

    public void setCamisa(String Camisa) {
        this.Camisa = Camisa;
    }

    public String getSapato() {
        return Sapato;
    }

    public void setSapato(String Sapato) {
        this.Sapato = Sapato;
    }

    public String getCalca() {
        return Calca;
    }

    public void setCalca(String Calca) {
        this.Calca = Calca;
    }

    public String getJaqueta() {
        return Jaqueta;
    }

    public void setJaqueta(String Jaqueta) {
        this.Jaqueta = Jaqueta;
    }

    public String getTipo_Moradia() {
        return Tipo_Moradia;
    }

    public void setTipo_Moradia(String Tipo_Moradia) {
        this.Tipo_Moradia = Tipo_Moradia;
    }

    public String getReligiao() {
        return Religiao;
    }

    public void setReligiao(String Religiao) {
        this.Religiao = Religiao;
    }

    public String getReligiao_Prat() {
        return Religiao_Prat;
    }

    public void setReligiao_Prat(String Religiao_Prat) {
        this.Religiao_Prat = Religiao_Prat;
    }

    public String getReservista_RM() {
        return Reservista_RM;
    }

    public void setReservista_RM(String Reservista_RM) {
        this.Reservista_RM = Reservista_RM;
    }

    public String getNomeEscola() {
        return NomeEscola;
    }

    public void setNomeEscola(String NomeEscola) {
        this.NomeEscola = NomeEscola;
    }

    public String getPorteArma() {
        return PorteArma;
    }

    public void setPorteArma(String PorteArma) {
        this.PorteArma = PorteArma;
    }

    @Override
    public String toString() {
        return "Pessoa{" + "Codigo=" + Codigo + ", Nome=" + Nome + '}';
    }

    public String getCodDieta() {
        return CodDieta;
    }

    public void setCodDieta(String CodDieta) {
        this.CodDieta = CodDieta;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getComplemento() {
        return Complemento;
    }

    public void setComplemento(String Complemento) {
        this.Complemento = Complemento;
    }

    public String getNivelx() {
        return Nivelx;
    }

    public void setNivelx(String Nivelx) {
        this.Nivelx = Nivelx;
    }

    public String getSenha() {
        return Senha;
    }

    public void setSenha(String Senha) {
        this.Senha = Senha;
    }

    public String getChaveAcesso() {
        return ChaveAcesso;
    }

    public void setChaveAcesso(String ChaveAcesso) {
        this.ChaveAcesso = ChaveAcesso;
    }

    public String getEnvPontoEmail() {
        return EnvPontoEmail;
    }

    public void setEnvPontoEmail(String EnvPontoEmail) {
        this.EnvPontoEmail = EnvPontoEmail;
    }

    public String getEnvPontoWhp() {
        return EnvPontoWhp;
    }

    public void setEnvPontoWhp(String EnvPontoWhp) {
        this.EnvPontoWhp = EnvPontoWhp;
    }

}

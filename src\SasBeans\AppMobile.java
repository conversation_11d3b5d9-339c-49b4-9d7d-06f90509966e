package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class AppMobile {

    private BigDecimal Sequencia;
    private String Nome;
    private String Descricao;
    private String Pacote;
    private String Plataforma;
    private String Parametro;
    private String VersaoAceita;
    private String VersaoAtual;

    public AppMobile() {
        this.Sequencia = new BigDecimal("1");
        this.Nome = "";
        this.Descricao = "";
        this.Pacote = "";
        this.Plataforma = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("1");
        }
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getPacote() {
        return Pacote;
    }

    public void setPacote(String Pacote) {
        this.Pacote = Pacote;
    }

    public String getPlataforma() {
        return Plataforma;
    }

    public void setPlataforma(String Plataforma) {
        this.Plataforma = Plataforma;
    }

    public String getParametro() {
        return Parametro;
    }

    public void setParametro(String Parametro) {
        this.Parametro = Parametro;
    }

    public String getVersaoAceita() {
        return VersaoAceita;
    }

    public void setVersaoAceita(String VersaoAceita) {
        this.VersaoAceita = VersaoAceita;
    }

    public String getVersaoAtual() {
        return VersaoAtual;
    }

    public void setVersaoAtual(String VersaoAtual) {
        this.VersaoAtual = VersaoAtual;
    }
}

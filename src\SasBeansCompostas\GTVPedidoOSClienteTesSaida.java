package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.GTV;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.TesSaidas;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class GTVPedidoOSClienteTesSaida {

    private GTV gtv;
    private Pedido pedido;
    private OS_Vig osvig;
    private Clientes cliente;
    private TesSaidas tesSaida;
    private BigDecimal totalGeral;
    private BigDecimal totalDN;
    private BigDecimal totalMoeda;
    private LocalDate dataPedido;

    public GTVPedidoOSClienteTesSaida() {
    }

    public GTVPedidoOSClienteTesSaida(GTV gtv, Pedido pedido, OS_Vig osvig, Clientes cliente, TesSaidas tesSaida) {
        this.gtv = gtv;
        this.pedido = pedido;
        this.osvig = osvig;
        this.cliente = cliente;
        this.tesSaida = tesSaida;
    }

    public GTV getGtv() {
        return gtv;
    }

    public void setGtv(GTV gtv) {
        this.gtv = gtv;
    }

    public Pedido getPedido() {
        return pedido;
    }

    public void setPedido(Pedido pedido) {
        this.pedido = pedido;
    }

    public OS_Vig getOsvig() {
        return osvig;
    }

    public void setOsvig(OS_Vig osvig) {
        this.osvig = osvig;
    }

    public Clientes getCliente() {
        return cliente;
    }

    public void setCliente(Clientes cliente) {
        this.cliente = cliente;
    }

    public TesSaidas getTesSaida() {
        return tesSaida;
    }

    public void setTesSaida(TesSaidas tesSaida) {
        this.tesSaida = tesSaida;
    }

    public String getOrigem() {
        return cliente != null ? cliente.getNRed() : null;
    }

    public BigDecimal getTotalGeral() {
        return totalGeral;
    }

    public void setTotalGeral(BigDecimal totalGeral) {
        this.totalGeral = totalGeral;
    }

    public BigDecimal getTotalDN() {
        return totalDN;
    }

    public void setTotalDN(BigDecimal totalDN) {
        this.totalDN = totalDN;
    }

    public BigDecimal getTotalMoeda() {
        return totalMoeda;
    }

    public void setTotalMoeda(BigDecimal totalMoeda) {
        this.totalMoeda = totalMoeda;
    }

    public LocalDate getDataPedido() {
        return dataPedido;
    }

    public void setDataPedido(LocalDate dataPedido) {
        this.dataPedido = dataPedido;
    }

    @Override
    public String toString() {
        return "GTVPedidoOSClienteTesSaida{" + "gtv=" + gtv.toString()
                + ", pedido=" + pedido.toString()
                + ", dataPedido=" + dataPedido.toString() + '}';
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 17 * hash + Objects.hashCode(this.gtv);
        hash = 17 * hash + Objects.hashCode(this.pedido);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final GTVPedidoOSClienteTesSaida other = (GTVPedidoOSClienteTesSaida) obj;
        if (!Objects.equals(this.gtv, other.gtv)) {
            return false;
        }
        if (!Objects.equals(this.pedido, other.pedido)) {
            return false;
        }
        if (!Objects.equals(this.osvig, other.osvig)) {
            return false;
        }
        if (!Objects.equals(this.cliente, other.cliente)) {
            return false;
        }
        if (!Objects.equals(this.tesSaida, other.tesSaida)) {
            return false;
        }
        return true;
    }

}

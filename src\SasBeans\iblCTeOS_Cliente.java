/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class iblCTeOS_Cliente {
    public String CNPJ;
    public String CPF;
    public String IE;
    public String xNome;
    public String xFant;
    public iblCTeOS_ClienteEnder ender;

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getIE() {
        return IE;
    }

    public void setIE(String IE) {
        this.IE = IE;
    }

    public String getxNome() {
        return xNome;
    }

    public void setxNome(String xNome) {
        this.xNome = xNome;
    }

    public String getxFant() {
        return xFant;
    }

    public void setxFant(String xFant) {
        this.xFant = xFant;
    }

    public iblCTeOS_ClienteEnder getEnder() {
        return ender;
    }

    public void setEnder(iblCTeOS_ClienteEnder ender) {
        this.ender = ender;
    }

    
    
    
}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/index.css" rel="stylesheet" />
            <script src="../assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .jconfirm-content{
                    display: flex;
                }

                #tblMapaItens{
                    width: 100%;
                }

                #tblMapaItens thead tr th{
                    font-size: 11pt;
                    font-weight: 600;
                    color: #FFF;
                    background-color: #000;
                    padding: 6px 10px 6px 10px;
                }

                #tblMapaItens tbody tr td i{
                    font-size: 14pt !important;
                    margin-right: 10px;
                }

                #tblMapaItens tbody tr td{
                    padding: 6px 10px 6px 10px;
                    border: thin solid #DDD;
                    font-size: 10pt;
                    font-weight: 500;
                }

                #tblMapaItens tbody tr td:not(:last-child){
                    border-right: none;
                }

                #tblMapaItens tbody tr td:last-child{
                    width: 160px;
                    text-align: right;
                    border-left: none;
                }

                @media(max-width: 550px){
                    [id$="main"]{
                        zoom: 0.7;
                    }
                }
            </style>
        </h:head>
        <h:body style="background-color: #FFF">
            <f:metadata>
                <f:viewAction action="#{login.carregarMapaTesouraria}" />
            </f:metadata>

            <p:growl id="msgs" widgetVar="msgs" />

            <h:form id="main" style="background-color: transparent; padding: 0px 3px 0px 0px !important;">
                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 60px; position: relative; padding: 0px;">
                    <input type="text" id="txtPesquisar" class="form-control" placeholder="#{localemsgs.Pesquisar}" style="font-size: 11pt; padding: 10px 10px 10px 44px !important; height: 50px; background-color: #F3F3F3; box-shadow: none; color: #222 !important" />
                    <i class="fa fa-search" style="position: absolute; color: #AAA; top: 12px; left: 16px; font-size: 18pt"></i>
                </div>
                <p:panel id="pnlListaArquivos" class="col-md-12 col-sm-12 col-xs-12" style="background-color: #FFF; border: thin solid #CCC; box-shadow: 2px 2px 3px #DDD; padding: 4px 4px 3px 4px !important"></p:panel>

                <a id="lnkDownload" href="#" download="download" hidden="hidden"></a>

                <script>
// <![CDATA[
                    function CarregarArquivos(Dados) {
                        try {
                            $frmUpload.close();
                        } catch (e) {
                        }
                        let HtmlIcones = '';
                        let ExisteArquivo = false;
                        HtmlIcones += '<table id="tblMapaItens">';
                        HtmlIcones += '  <thead>';
                        HtmlIcones += '    <tr>';
                        HtmlIcones += '      <th colspan="2">#{localemsgs.NomeArquivo}</th>';
                        HtmlIcones += '    </tr>';
                        HtmlIcones += '  </thead>';
                        HtmlIcones += '  <tbody>';

                        if (null != Dados && Dados != '') {
                            Dados.split('=*=').forEach(function (item) {
                                ExisteArquivo = true;
                                let NomeArquivo = item.split('|*|')[0];
                                let Icone = '<i class="fa fa-file-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                let Link = item.split('|*|')[2];

                                switch (ReplaceAll(item.split('|*|')[1], '.', '')) {
                                    case 'xls':
                                    case 'xlsx':
                                    case 'csv':
                                        Icone = '<i class="fa fa-file-excel-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                        break;

                                    case 'doc':
                                    case 'docx':
                                        Icone = '<i class="fa fa-file-word-o" aria-hidden="true" style="font-size: 30pt;"></i>';

                                        break;

                                    case 'pdf':
                                        Icone = '<i class="fa fa-file-pdf-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                        break;

                                    case 'txt':
                                    case 'ret':
                                    case 'rem':
                                        Icone = '<i class="fa fa-file-text-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                        break;

                                    case 'zip':
                                    case 'rar':
                                        Icone = '<i class="fa fa-file-archive-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                        break

                                    case 'ppt':
                                    case 'pptx':
                                        Icone = '<i class="fa fa-file-powerpoint-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                        break

                                    case 'png':
                                    case 'jpg':
                                    case 'jpeg':
                                    case 'tiff':
                                    case 'bmp':
                                    case 'gif':
                                        Icone = '<i class="fa fa-file-image-o" aria-hidden="true" style="font-size: 30pt;"></i>';
                                        break
                                }

                                HtmlIcones += '  <tr>';
                                HtmlIcones += '      <td>' + Icone + NomeArquivo + '</td>';
                                HtmlIcones += '      <td>';
                                HtmlIcones += '        <a class="btn btn-primary" href="javascript: void(0);" ref-nome="' + NomeArquivo.toLowerCase() + '" ref-link="' + Link + '"><i class="fa fa-download"></i>&nbsp;#{localemsgs.Download}</a>';
                                HtmlIcones += '      </td>';
                                HtmlIcones += '  </tr>';
                            });
                        }

                        if (!ExisteArquivo) {
                            HtmlIcones += '  <tr>';
                            HtmlIcones += '      <td colspan="2" style="color: #AAA; font-size: 14pt; font-weight: 500; text-align: center">#{localemsgs.NaoHaDados}</td>';
                            HtmlIcones += '  </tr>';
                        }

                        HtmlIcones += '  </tbody>';
                        HtmlIcones += '</table>';

                        $('[id$="pnlListaArquivos"]').html(HtmlIcones);
                    }

                    $(document).ready(function () {

                    }).on('click', '#tblMapaItens tbody tr td a', function () {
                        downloadURI($(this).attr('ref-link'), $(this).attr('ref-nome'));
                    })
                            .on('keyup', '#txtPesquisar', function () {
                                if ($(this).val().length >= 2) {
                                    let value = $(this).val().toLowerCase();

                                    $("#tblMapaItens tbody tr").filter(function () {
                                        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                                    });
                                } else
                                    $('#tblMapaItens tbody tr').css('display', '');
                            })
                            ;

                    function downloadURI(fileURL, fileName) {
                        // for non-IE
                        if (!window.ActiveXObject) {
                            var save = document.createElement('a');
                            save.href = fileURL;
                            save.target = '_blank';
                            save.download = fileName || 'unknown';

                            var evt = new MouseEvent('click', {
                                'view': window,
                                'bubbles': true,
                                'cancelable': false
                            });
                            save.dispatchEvent(evt);

                            (window.URL || window.webkitURL).revokeObjectURL(save.href);
                        }

                        // for IE < 11
                        else if (!!window.ActiveXObject && document.execCommand) {
                            var _window = window.open(fileURL, '_blank');
                            _window.document.close();
                            _window.document.execCommand('SaveAs', true, fileName || fileURL)
                            _window.close();
                        }
                    }
                    // ]]>
                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>
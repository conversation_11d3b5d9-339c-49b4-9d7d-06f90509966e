package Controller.Movimentacoes;

import Dados.Persistencia;
import SasBeans.CtrOperV;
import SasBeans.EmailsEnviar;
import SasBeans.Funcion;
import SasDaos.CtrOpervDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FuncionDao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class MovimentacoesSatMobWeb {

    public void Gravar(CtrOperV ctrOperV, Persistencia persistencia) throws Exception {
        try {
            CtrOperV ctrOperv = ctrOperV;
            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            BigDecimal numero = ctrOperVDao.obterNumeroIncremento(ctrOperV.getCodFil(), persistencia);
            ctrOperVDao.adicionaCtrOperV(numero, ctrOperV, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>");
        }
    }

    public void gravarMovimentacao(CtrOperV ctrOperV, Persistencia persistencia) throws Exception {
        try {

            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            ctrOperV = (CtrOperV) FuncoesString.removeAcentoObjeto(ctrOperV);
            ctrOperVDao.editar(ctrOperV, persistencia);

        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>");

        }
    }

    public void InserirMovimentacao(CtrOperV ctrOperV, Persistencia persistencia) throws Exception {
        try {
            int contador = 1;
            CtrOperV ctrOperv = ctrOperV;
            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            BigDecimal numero = ctrOperVDao.obterNumeroIncremento(ctrOperV.getCodFil(), persistencia);
            ctrOperVDao.adicionaCtrOperV(numero, ctrOperV, persistencia);

        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    public void EnviarEmail(EmailsEnviar emailEnviar, Persistencia persistencia) throws Exception {
        EmailsEnviarDao emailEnviarDao = new EmailsEnviarDao();
        emailEnviarDao.InserirEmail(emailEnviar, persistencia);

    }

    public List<CtrOperV> listarMovimentacoes(String codfil, Persistencia persistencia) throws Exception {
        try {

            CtrOpervDao cdao = new CtrOpervDao();
            return cdao.listaMovimentacao(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    public List<CtrOperV> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<CtrOperV> retorno;
            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            retorno = ctrOperVDao.listaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("Movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Funcion> listarFuncionarios(String nome, Persistencia persistencia)
            throws Exception {
        try {
            FuncionDao funciondao = new FuncionDao();
            return funciondao.buscarFuncionNome(nome, persistencia);

        } catch (Exception e) {
            throw new Exception("Movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {

            CtrOpervDao ctrOperVDao = new CtrOpervDao();
            return ctrOperVDao.totalMovimentacoesMobWeb(filtros, codPessoa, persistencia);

        } catch (Exception e) {
            throw new Exception("movimentacoes.falhageral<message>" + e.getMessage());
        }
    }

}

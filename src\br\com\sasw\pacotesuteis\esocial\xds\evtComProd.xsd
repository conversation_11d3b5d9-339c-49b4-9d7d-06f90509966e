﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtComProd/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtComProd/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtComProd">
                    <xs:annotation>
                        <xs:documentation>Evento Comercializacao da Producao Rural Pessoa Fisica</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveFopagMensal">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="tpInsc">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nrInsc">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Número de Inscricao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{8,14}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoComProd">
                                <xs:annotation>
                                    <xs:documentation>Informacao da Comercializacao de Producao</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ideEstabel">
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do estabelecimento que comercializou a producao</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="nrInscEstabRural">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Número de Inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="tpComerc" maxOccurs="5">
                                                        <xs:annotation>
                                                            <xs:documentation>Registro que apresenta o valor total da comercializacao por "tipo" de comercializacao</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="indComerc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de Comercializacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrTotCom">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor Total da Comercializacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="ideAdquir" minOccurs="0" maxOccurs="9999">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Identificacao dos Adquirentes da Producao</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpInsc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrInsc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número de Inscricao</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{8,14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrComerc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor Bruto da Comercializacao</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nfs" minOccurs="0" maxOccurs="999">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Notas Fiscais da aquisicao de producao</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="serie" minOccurs="0">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Série da Nota Fiscal/Fatura</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="1"/>
                                                                                                    <xs:maxLength value="5"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="nrDocto">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Número da Nota Fiscal/Fatura</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="1"/>
                                                                                                    <xs:maxLength value="20"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="dtEmisNF">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Data de Emissao da Nota Fiscal/Fatura</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:date">
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vlrBruto">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Valor bruto</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrCPDescPR">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Vr da Contrib. Previd. descontada pelo adquirente</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrRatDescPR">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Valor da GILRAT incidente sobre a aquisicao de producao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="vrSenarDesc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Valor da contribuicao destinada ao Senar</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:decimal">
                                                                                                    <xs:totalDigits value="14"/>
                                                                                                    <xs:fractionDigits value="2"/>
                                                                                                    <xs:maxInclusive value="999999999999"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoProcJud" minOccurs="0" maxOccurs="10">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacao de Processo Judicial</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpProc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de Processo</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrProc">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número do Processo</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="2"/>
                                                                                        <xs:maxLength value="21"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="codSusp">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                        <xs:pattern value="\d{1,14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrCPSusp" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor da Contrib. Previd. com exigibilidade suspensa</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrRatSusp" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor da contribuicao para Gilrat com exigibilidade suspensa</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrSenarSusp" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor da contribuicao para o Senar com exigibilidade suspensa</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveFopagMensal">
        <xs:annotation>
            <xs:documentation>Identificacao do Evento PeriOdico</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indApuracao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="perApur">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

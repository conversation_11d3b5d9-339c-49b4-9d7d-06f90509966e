/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class PropProd {

    private BigDecimal Numero;
    private BigDecimal CodFil;
    private BigDecimal Ordem;
    private BigDecimal CodPro;
    private BigDecimal Qtde;
    private BigDecimal CustoUn;
    private BigDecimal ValorUn;
    private BigDecimal ValorTot;
    private String OperIncl;
    private LocalDate Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getNumero() {
        return Numero;
    }

    public void setNumero(BigDecimal Numero) {
        this.Numero = Numero;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getOrdem() {
        return Ordem;
    }

    public void setOrdem(BigDecimal Ordem) {
        this.Ordem = Ordem;
    }

    public BigDecimal getCodPro() {
        return CodPro;
    }

    public void setCodPro(BigDecimal CodPro) {
        this.CodPro = CodPro;
    }

    public BigDecimal getQtde() {
        return Qtde;
    }

    public void setQtde(BigDecimal Qtde) {
        this.Qtde = Qtde;
    }

    public BigDecimal getCustoUn() {
        return CustoUn;
    }

    public void setCustoUn(BigDecimal CustoUn) {
        this.CustoUn = CustoUn;
    }

    public BigDecimal getValorUn() {
        return ValorUn;
    }

    public void setValorUn(BigDecimal ValorUn) {
        this.ValorUn = ValorUn;
    }

    public BigDecimal getValorTot() {
        return ValorTot;
    }

    public void setValorTot(BigDecimal ValorTot) {
        this.ValorTot = ValorTot;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public LocalDate getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(LocalDate Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

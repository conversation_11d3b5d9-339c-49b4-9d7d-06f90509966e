﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtIrrfBenef/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtIrrfBenef/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtIrrfBenef">
                    <xs:annotation>
                        <xs:documentation>IRRF do beneficiario</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do evento de retorno</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="nrRecArqBase">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Recibo do arquivo de origem</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="perApur">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideTrabalhador">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do Trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoDep" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Informacoes relativas a existencia de dependentes</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="vrDedDep">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Deducao IRRF relativo a dependentes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoIrrf" maxOccurs="9">
                                <xs:annotation>
                                    <xs:documentation>Informacoes relativas ao IRRF</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="codCateg" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo da Categoria</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:integer">
                                                    <xs:pattern value="\d{3}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indResBr">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Tem residencia fiscal no Brasil?</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[N|S]"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="basesIrrf" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Bases do IRRF</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpValor">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de valor</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d{1,2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="valor">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Valor</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="999999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="irrf" minOccurs="0" maxOccurs="20">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes relativas ao IRRF</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpCR">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo de Receita - CR</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="vrIrrfDesc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Valor efetivamente descontado do trabalhador relativo ao IRRF</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="999999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="idePgtoExt" type="TNaoResid" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes complementares sobre pagamentos a residente fiscal no exterior</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TNaoResid">
        <xs:annotation>
            <xs:documentation>Endereco no Exterior - Fiscal</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="idePais">
                <xs:annotation>
                    <xs:documentation>Identificacao do Pais onde foi efetuado o pagamento</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="codPais">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Pais</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="3"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="indNIF">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Indicativo do preenchimento do NIF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nifBenef" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número de Identificacao Fiscal - NIF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="20"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="endExt">
                <xs:annotation>
                    <xs:documentation>Informacoes complementares de endereco do beneficiario</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="dscLograd">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Descricao do logradouro</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="100"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrLograd" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do logradouro</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="10"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="complem" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Complemento do logradouro</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="30"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="bairro" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome do bairro/distrito</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="90"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nmCid">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome da Cidade</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="50"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codPostal" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo de Enderecamento Postal</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="4"/>
                                    <xs:maxLength value="12"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

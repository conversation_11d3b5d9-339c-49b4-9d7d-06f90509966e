/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class XMLNFE {

    private String Sequencia;
    private String TOKEN;
    private String CNPJ;
    private String Praca;
    private String Serie;
    private String Numero;
    private String UF;
    private String Dt_Nota;
    private String Hr_Nota;
    private String XML_Envio;
    private String XML_Retorno;
    private String ChaveNFE;
    private String Protocolo;
    private String Status;
    private String Dt_Envio;
    private String Hr_Envio;
    private String Dt_Retorno;
    private String Hr_Retorno;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getTOKEN() {
        return TOKEN;
    }

    public void setTOKEN(String TOKEN) {
        this.TOKEN = TOKEN;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getPraca() {
        return Praca;
    }

    public void setPraca(String Praca) {
        this.Praca = Praca;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getDt_Nota() {
        return Dt_Nota;
    }

    public void setDt_Nota(String Dt_Nota) {
        this.Dt_Nota = Dt_Nota;
    }

    public String getHr_Nota() {
        return Hr_Nota;
    }

    public void setHr_Nota(String Hr_Nota) {
        this.Hr_Nota = Hr_Nota;
    }

    public String getXML_Envio() {
        return XML_Envio;
    }

    public void setXML_Envio(String XML_Envio) {
        this.XML_Envio = XML_Envio;
    }

    public String getXML_Retorno() {
        return XML_Retorno;
    }

    public void setXML_Retorno(String XML_Retorno) {
        this.XML_Retorno = XML_Retorno;
    }

    public String getChaveNFE() {
        return ChaveNFE;
    }

    public void setChaveNFE(String ChaveNFE) {
        this.ChaveNFE = ChaveNFE;
    }

    public String getProtocolo() {
        return Protocolo;
    }

    public void setProtocolo(String Protocolo) {
        this.Protocolo = Protocolo;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getDt_Envio() {
        return Dt_Envio;
    }

    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    public String getHr_Envio() {
        return Hr_Envio;
    }

    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    public String getDt_Retorno() {
        return Dt_Retorno;
    }

    public void setDt_Retorno(String Dt_Retorno) {
        this.Dt_Retorno = Dt_Retorno;
    }

    public String getHr_Retorno() {
        return Hr_Retorno;
    }

    public void setHr_Retorno(String Hr_Retorno) {
        this.Hr_Retorno = Hr_Retorno;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Municipios {

    private BigDecimal Codigo;
    private String Nome;
    private String UF;
    private BigDecimal CodIBGE;
    private BigDecimal CodSEFAZ;
    private String Pais;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public Municipios() {

        this.Codigo = new BigDecimal("0");
        this.Nome = "";
        this.UF = "";
        this.CodIBGE = new BigDecimal("0");
        this.CodSEFAZ = new BigDecimal("0");
        this.Pais = "";
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public BigDecimal getCodIBGE() {
        return CodIBGE;
    }

    public void setCodIBGE(BigDecimal CodIBGE) {
        this.CodIBGE = CodIBGE;
    }

    public BigDecimal getCodSEFAZ() {
        return CodSEFAZ;
    }

    public void setCodSEFAZ(BigDecimal CodSEFAZ) {
        this.CodSEFAZ = CodSEFAZ;
    }

    public String getPais() {
        return Pais;
    }

    public void setPais(String Pais) {
        this.Pais = Pais;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 31 * hash + Objects.hashCode(this.Codigo);
        hash = 31 * hash + Objects.hashCode(this.Nome);
        hash = 31 * hash + Objects.hashCode(this.UF);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Municipios other = (Municipios) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TesConta {

    private String Codigo;
    private String CodFil;
    private String Tipo;
    private String Descricao;
    private String CodSrv;
    private String TipoSrvDesc;
    private String Exportar;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String TipoSrvCodigo;
    private String Banco;
    private String TAtend;
    private String TCob;
    private String TCar;
    private String TipoSrv;
    private String ER;
    private String CodInterf;
    private String Aditivo;
    private String SubCentro;
    private String TipoSrvOperador;
    private String TipoSrvDt_Alter;
    private String TipoSrvHr_Alter;

    public TesConta() {
        Codigo = "";
        CodFil = "";
        Tipo = "";
        Descricao = "";
        CodSrv = "";
        TipoSrvDesc = "";
        Exportar = "";
        Operador = "";
        Dt_Alter = "";
        Hr_Alter = "";
        TipoSrvCodigo = "";
        Banco = "";
        TAtend = "";
        TCob = "";
        TCar = "";
        TipoSrv = "";
        ER = "";
        CodInterf = "";
        Aditivo = "";
        SubCentro = "";
        TipoSrvOperador = "";
        TipoSrvDt_Alter = "";
        TipoSrvHr_Alter = "";
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getCodSrv() {
        return CodSrv;
    }

    public void setCodSrv(String CodSrv) {
        this.CodSrv = CodSrv;
    }

    public String getTipoSrvDesc() {
        return TipoSrvDesc;
    }

    public void setTipoSrvDesc(String TipoSrvDesc) {
        this.TipoSrvDesc = TipoSrvDesc;
    }

    public String getExportar() {
        return Exportar;
    }

    public void setExportar(String Exportar) {
        this.Exportar = Exportar;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getTipoSrvCodigo() {
        return TipoSrvCodigo;
    }

    public void setTipoSrvCodigo(String TipoSrvCodigo) {
        this.TipoSrvCodigo = TipoSrvCodigo;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getTAtend() {
        return TAtend;
    }

    public void setTAtend(String TAtend) {
        this.TAtend = TAtend;
    }

    public String getTCob() {
        return TCob;
    }

    public void setTCob(String TCob) {
        this.TCob = TCob;
    }

    public String getTCar() {
        return TCar;
    }

    public void setTCar(String TCar) {
        this.TCar = TCar;
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String TipoSrv) {
        this.TipoSrv = TipoSrv;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public String getCodInterf() {
        return CodInterf;
    }

    public void setCodInterf(String CodInterf) {
        this.CodInterf = CodInterf;
    }

    public String getAditivo() {
        return Aditivo;
    }

    public void setAditivo(String Aditivo) {
        this.Aditivo = Aditivo;
    }

    public String getSubCentro() {
        return SubCentro;
    }

    public void setSubCentro(String SubCentro) {
        this.SubCentro = SubCentro;
    }

    public String getTipoSrvOperador() {
        return TipoSrvOperador;
    }

    public void setTipoSrvOperador(String TipoSrvOperador) {
        this.TipoSrvOperador = TipoSrvOperador;
    }

    public String getTipoSrvDt_Alter() {
        return TipoSrvDt_Alter;
    }

    public void setTipoSrvDt_Alter(String TipoSrvDt_Alter) {
        this.TipoSrvDt_Alter = TipoSrvDt_Alter;
    }

    public String getTipoSrvHr_Alter() {
        return TipoSrvHr_Alter;
    }

    public void setTipoSrvHr_Alter(String TipoSrvHr_Alter) {
        this.TipoSrvHr_Alter = TipoSrvHr_Alter;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TesConta other = (TesConta) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 37 * hash + Objects.hashCode(this.Codigo);
        return hash;
    }
    
    @Override
    public String toString() {
        return "Conta{" + "Codigo=" + Codigo + ", Descricao=" + Descricao + "}";
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S2230 {

    private int sucesso;
    private String evtAfastTemp_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String ideVinculo_cpfTrab;
    private String ideVinculo_nisTrab;
    private String ideVinculo_matricula;
    private String ideVinculo_codCateg;
    private String iniAfastamento_dtIniAfast;
    private String iniAfastamento_codMotAfast;
    private String iniAfastamento_infoMesmoMtv;
    private String iniAfastamento_tpAcidTransito;
    private String iniAfastamento_observacao;
    private String iniAfastamento_dtInicio;
    private String iniAfastamento_dtFim;
    private String infoAtestado_codCID;
    private String infoAtestado_qtdDiasAfast;
    private String emitente_nmEmit;
    private String emitente_ideOC;
    private String emitente_nrOc;
    private String emitente_ufOC;
    private String infoCessao_cnpjCess;
    private String infoCessao_infOnus;
    private String infoMandSind_cnpjSind;
    private String infoMandSind_infOnusRemun;
    private String infoRetif_origRetif;
    private String infoRetif_tpProc;
    private String infoRetif_nrProc;
    private String fimAfastamento_dtTermAfast;
    private String CodPonto;   

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtAfastTemp_Id() {
        return evtAfastTemp_Id;
    }

    public void setEvtAfastTemp_Id(String evtAfastTemp_Id) {
        this.evtAfastTemp_Id = evtAfastTemp_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getIdeVinculo_cpfTrab() {
        return ideVinculo_cpfTrab;
    }

    public void setIdeVinculo_cpfTrab(String ideVinculo_cpfTrab) {
        this.ideVinculo_cpfTrab = ideVinculo_cpfTrab;
    }

    public String getIdeVinculo_nisTrab() {
        return ideVinculo_nisTrab;
    }

    public void setIdeVinculo_nisTrab(String ideVinculo_nisTrab) {
        this.ideVinculo_nisTrab = ideVinculo_nisTrab;
    }

    public String getIdeVinculo_matricula() {
        return ideVinculo_matricula;
    }

    public void setIdeVinculo_matricula(String ideVinculo_matricula) {
        this.ideVinculo_matricula = ideVinculo_matricula;
    }

    public String getIdeVinculo_codCateg() {
        return ideVinculo_codCateg;
    }

    public void setIdeVinculo_codCateg(String ideVinculo_codCateg) {
        this.ideVinculo_codCateg = ideVinculo_codCateg;
    }

    public String getIniAfastamento_dtIniAfast() {
        return iniAfastamento_dtIniAfast;
    }

    public void setIniAfastamento_dtIniAfast(String iniAfastamento_dtIniAfast) {
        this.iniAfastamento_dtIniAfast = iniAfastamento_dtIniAfast;
    }

    public String getIniAfastamento_codMotAfast() {
        return iniAfastamento_codMotAfast;
    }

    public void setIniAfastamento_codMotAfast(String iniAfastamento_codMotAfast) {
        this.iniAfastamento_codMotAfast = iniAfastamento_codMotAfast;
    }

    public String getIniAfastamento_infoMesmoMtv() {
        return iniAfastamento_infoMesmoMtv;
    }

    public void setIniAfastamento_infoMesmoMtv(String iniAfastamento_infoMesmoMtv) {
        this.iniAfastamento_infoMesmoMtv = iniAfastamento_infoMesmoMtv;
    }

    public String getIniAfastamento_tpAcidTransito() {
        return iniAfastamento_tpAcidTransito;
    }

    public void setIniAfastamento_tpAcidTransito(String iniAfastamento_tpAcidTransito) {
        this.iniAfastamento_tpAcidTransito = iniAfastamento_tpAcidTransito;
    }

    public String getIniAfastamento_observacao() {
        return iniAfastamento_observacao;
    }

    public void setIniAfastamento_observacao(String iniAfastamento_observacao) {
        this.iniAfastamento_observacao = iniAfastamento_observacao;
    }

    public String getIniAfastamento_dtInicio() {
        return iniAfastamento_dtInicio;
    }

    public void setIniAfastamento_dtInicio(String iniAfastamento_dtInicio) {
        this.iniAfastamento_dtInicio = iniAfastamento_dtInicio;
    }
    
    public String getIniAfastamento_dtFim() {
        return iniAfastamento_dtFim;
    }

    public void setIniAfastamento_dtFim(String iniAfastamento_dtFim) {
        this.iniAfastamento_dtFim = iniAfastamento_dtFim;
    }
        
    public String getInfoAtestado_codCID() {
        return infoAtestado_codCID;
    }

    public void setInfoAtestado_codCID(String infoAtestado_codCID) {
        this.infoAtestado_codCID = infoAtestado_codCID;
    }

    public String getInfoAtestado_qtdDiasAfast() {
        return infoAtestado_qtdDiasAfast;
    }

    public void setInfoAtestado_qtdDiasAfast(String infoAtestado_qtdDiasAfast) {
        this.infoAtestado_qtdDiasAfast = infoAtestado_qtdDiasAfast;
    }

    public String getEmitente_nmEmit() {
        return emitente_nmEmit;
    }

    public void setEmitente_nmEmit(String emitente_nmEmit) {
        this.emitente_nmEmit = emitente_nmEmit;
    }

    public String getEmitente_ideOC() {
        return emitente_ideOC;
    }

    public void setEmitente_ideOC(String emitente_ideOC) {
        this.emitente_ideOC = emitente_ideOC;
    }

    public String getEmitente_nrOc() {
        return emitente_nrOc;
    }

    public void setEmitente_nrOc(String emitente_nrOc) {
        this.emitente_nrOc = emitente_nrOc;
    }

    public String getEmitente_ufOC() {
        return emitente_ufOC;
    }

    public void setEmitente_ufOC(String emitente_ufOC) {
        this.emitente_ufOC = emitente_ufOC;
    }

    public String getInfoCessao_cnpjCess() {
        return infoCessao_cnpjCess;
    }

    public void setInfoCessao_cnpjCess(String infoCessao_cnpjCess) {
        this.infoCessao_cnpjCess = infoCessao_cnpjCess;
    }

    public String getInfoCessao_infOnus() {
        return infoCessao_infOnus;
    }

    public void setInfoCessao_infOnus(String infoCessao_infOnus) {
        this.infoCessao_infOnus = infoCessao_infOnus;
    }

    public String getInfoMandSind_cnpjSind() {
        return infoMandSind_cnpjSind;
    }

    public void setInfoMandSind_cnpjSind(String infoMandSind_cnpjSind) {
        this.infoMandSind_cnpjSind = infoMandSind_cnpjSind;
    }

    public String getInfoMandSind_infOnusRemun() {
        return infoMandSind_infOnusRemun;
    }

    public void setInfoMandSind_infOnusRemun(String infoMandSind_infOnusRemun) {
        this.infoMandSind_infOnusRemun = infoMandSind_infOnusRemun;
    }

    public String getInfoRetif_origRetif() {
        return infoRetif_origRetif;
    }

    public void setInfoRetif_origRetif(String infoRetif_origRetif) {
        this.infoRetif_origRetif = infoRetif_origRetif;
    }

    public String getInfoRetif_tpProc() {
        return infoRetif_tpProc;
    }

    public void setInfoRetif_tpProc(String infoRetif_tpProc) {
        this.infoRetif_tpProc = infoRetif_tpProc;
    }

    public String getInfoRetif_nrProc() {
        return infoRetif_nrProc;
    }

    public void setInfoRetif_nrProc(String infoRetif_nrProc) {
        this.infoRetif_nrProc = infoRetif_nrProc;
    }

    public String getFimAfastamento_dtTermAfast() {
        return fimAfastamento_dtTermAfast;
    }

    public void setFimAfastamento_dtTermAfast(String fimAfastamento_dtTermAfast) {
        this.fimAfastamento_dtTermAfast = fimAfastamento_dtTermAfast;
    }
    public String getCodPonto() {
        return CodPonto;
    }

    public void setCodPonto(String CodPonto) {
        this.CodPonto = CodPonto;
    }
}

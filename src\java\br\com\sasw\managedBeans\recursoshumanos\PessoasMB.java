package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Arquivo.ArquivoLogs;
import Controller.Dietas.Dieta;
import Controller.Pessoas.PessoasSatMobWeb;
import Controller.PstServ.PstServSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.AcessoAut;
import SasBeans.Cargos;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.MobileContatos;
import SasBeans.Municipios;
import SasBeans.Pe_Cargo;
import SasBeans.Pe_Doctos;
import SasBeans.Pessoa;
import SasBeans.PessoaLogin;
import SasBeans.SasPWFill;
import SasBeans.SegAutorizaArea;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.AcessosDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import br.com.sasw.lazydatamodels.PessoaLazyList;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.managedBeans.comercial.ClientesMB;
import br.com.sasw.pacotesuteis.utilidades.BuscarEndereco;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.utils.LocaleController;
import br.com.sasw.utils.Logger;
import br.com.sasw.utils.Mascaras;
import br.com.sasw.utils.Messages;
import br.com.sasw.pacotesuteis.utilidades.winzap.Winzap;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.font.TextAttribute;
import java.awt.font.TextLayout;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIViewRoot;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.imageio.ImageIO;
import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named(value = "pessoa")
@ViewScoped
public class PessoasMB implements Serializable {

    private Persistencia persistenciaLocal, persistenciaSat;
    private List<Pessoa> listaPessoa;
    private final PstServSatMobWeb pstservsatmobweb;
    private Pessoa pessoaSelecionada, novaPessoa, pesquisaPessoa;
    private final PessoasSatMobWeb pessoasatmobweb;
    private BigDecimal codigo, matr, codPessoa;
    private String banco, operador, situacao, nomeFilial, caminho, log, dataTela, codFil;
    private String chavePesquisa = "NOME", valorPesquisa;
    private List<Municipios> cidades;
    private List<SasBeansCompostas.Dieta> listaDietas;
    private SasBeansCompostas.Dieta dietaSelecionada, dietaSelecionadaCadastro, dietaNova;
    private Dieta dieta;
    private Clientes clientes;
    private ClientesMB clientesMB;
    private List<Clientes> listaClientes;
    private Boolean edicao, editaSituacao, atualizado, limparFiltros, existeLocal, existeCentral, extTV, extSPP, extEscolta;
    private boolean eCodigo, eNome, eEmail,
            eRG, eOrgEmis, eCPF, eFone1, eFone2, eEnde, eBairro, eCidade, eUF, eCEP, eSituacao,
            eObs, eMatr, eFuncao, eSexo, eAltura, ePeso, eOperador, eDtAlter, eHrAlter, mostraExcluidos, spm;
    private int flag, total;
    private LazyDataModel<Pessoa> pessoas = null;
    private List<Pessoa> pessoasCompleto;
    private ArquivoLog logerro;
    private Map filters, niveis;
    private List<MobileContatos> contatos, buscaContatos;
    private List<Pe_Doctos> documentos;
    private Pe_Doctos documento;
    private List<Pe_Cargo> cargosPretendidos;
    private List<Cargos> cargos;
    private Cargos cargo;
    private StreamedContent download;
    private UploadedFile uploadedFile;
    private MobileContatos contato;
    private UsuarioSatMobWeb pessoaLoginUsuario;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private AcessoAut acessoAut;
    private SegAutorizaArea segAutorizaArea;
    private String Base64Qr;
    private String SequenciaAutorizacao;
    private final String NOME = "pessoa.nome LIKE ?",
            SITUACAO = "pessoa.situacao LIKE ?",
            CIDADE = "pessoa.cidade LIKE ?",
            UF = "pessoa.uf LIKE ?",
            RG = "pessoa.rg LIKE ?",
            CPF = "pessoa.cpf = ?",
            MATR = "pessoa.matr = ?",
            CODIGO = "pessoa.codigo = ?",
            CARGO = "pessoa.cargo = ?";

    public PessoasMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        novaPessoa = new Pessoa();
        log = new String();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        logerro = new ArquivoLog();
        rotassatweb = new RotasSatWeb();
        pessoasatmobweb = new PessoasSatMobWeb();
        listaClientes = new ArrayList<>();
        edicao = true;
        eCodigo = true;
        eNome = true;
        eEmail = true;
        eCPF = true;
        eMatr = true;
        eFuncao = true;
        buscaContatos = new ArrayList<>();
        dataTela = DataAtual.getDataAtual("SQL");
        dieta = new Dieta();
        pstservsatmobweb = new PstServSatMobWeb();
        pessoaLoginUsuario = new UsuarioSatMobWeb();
        acessoAut = new AcessoAut();
        segAutorizaArea = new SegAutorizaArea();

        pessoasCompleto = new ArrayList<>();

        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String caminho = request.getQueryString();

        if (null == caminho
                || (null != caminho && !caminho.contains("ref=msl"))) {
            spm = false;
        } else {
            spm = true;
        }
    }

    public void Persistencias(Persistencia pstLocal, Persistencia pstCentral) {
        try {
            persistenciaSat = pstCentral;
            if (null == persistenciaSat) {
                throw new Exception("ImpossivelConectarSatellite");
            }
            persistenciaLocal = pstLocal;
            if (null == persistenciaLocal) {
                throw new Exception(Messages.getMessageS("ImpossivelConectarBanco") + " " + banco);
            }
            filters = new HashMap<>();
            filters.put(NOME, "");
            filters.put(SITUACAO, "");
            filters.put(CIDADE, "");
            filters.put(UF, "");
            filters.put(RG, "");
            filters.put(CPF, "");
            filters.put(MATR, "");
            filters.put(CODIGO, "");
            total = pessoasatmobweb.contagemQuick(filters, persistenciaLocal);
            clientesMB = new ClientesMB(persistenciaLocal);
            cargos = pessoasatmobweb.listarCargos(persistenciaLocal);
            filiais = rotassatweb.buscaInfoFilial(codFil, persistenciaLocal);
            mostraExcluidos = false;
            HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
            String caminho = request.getQueryString();

            if (null == caminho
                    || (null != caminho && !caminho.contains("ref=msl"))) {
                spm = false;
            } else {
                spm = true;
            }

            try {
                listaDietas = dieta.listaDieta(persistenciaLocal);
            } catch (Exception e) {
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public PessoasMB(Persistencia pstLocal, Persistencia pstCentral) {
        FacesContext fc = FacesContext.getCurrentInstance();
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        novaPessoa = new Pessoa();
        persistenciaSat = pstCentral;
        persistenciaLocal = pstLocal;
        pessoasatmobweb = new PessoasSatMobWeb();
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        edicao = true;
        eCodigo = true;
        eNome = true;
        eEmail = true;
        eCPF = true;
        eMatr = true;
        eFuncao = true;
        buscaContatos = new ArrayList<>();
        rotassatweb = new RotasSatWeb();
        dataTela = DataAtual.getDataAtual("SQL");
        pstservsatmobweb = new PstServSatMobWeb();
    }

    public List<Pessoa> ListarQuery(String query) {
        try {
            this.listaPessoa = pessoasatmobweb.ListaPessoaQuery(query, this.persistenciaLocal);
            return this.listaPessoa;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public List<Pessoa> ListarQueryValida(String query) {
        try {
            this.listaPessoa = this.pessoasatmobweb.ListaPessoaQueryValida(query, this.persistenciaLocal);
            return this.listaPessoa;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void SelecionarCidade(SelectEvent event) {
        String[] parts = event.getObject().toString().split(",  ");
        this.novaPessoa.setCidade(parts[0]);
        this.novaPessoa.setUF(parts[1]);
    }

    public List<String> BuscarCidade(String query) {
        try {
            List<String> retorno = new ArrayList<>();
            this.cidades = pessoasatmobweb.ListaMunicipios(query, this.persistenciaLocal);
            for (Municipios cidade : this.cidades) {
                retorno.add(cidade.getNome() + ",  " + cidade.getUF());
            }
            return retorno;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void novaDieta() {
        StringBuilder script = new StringBuilder();
        script.append("PF('dlgCadastrarDietas').show();");
        PrimeFaces.current().executeScript(script.toString());
    }

    public void gerarChaveAcesso(){
        try{
            PessoaDao pessoaDao = new PessoaDao();

            if (null != this.novaPessoa && null != this.novaPessoa.getCodigo() && this.novaPessoa.getCodigo() != BigDecimal.ZERO) {
                String ChaveAcesso = pessoaDao.gerarChaveAcesso(this.persistenciaSat);
                this.novaPessoa.setChaveAcesso(ChaveAcesso);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }
    
    private Pessoa preencherChaveAcesso(Pessoa pessoa) {
        try {
            PessoaDao pessoaDao = new PessoaDao();

            if (null != pessoa && null != pessoa.getCodigo() && pessoa.getCodigo() != BigDecimal.ZERO) {
                String ChaveAcesso = pessoaDao.pesquisarChaveAcesso(pessoa, this.persistenciaLocal, this.persistenciaSat);
                pessoa.setChaveAcesso(ChaveAcesso);
            }

            return pessoa;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            return pessoa;
        }
    }

    public void Endereco() {
        try {
            String enderecoCompleto = new BuscarEndereco().BuscarPeloCEP(this.novaPessoa.getCEP());
            JSONObject obj = new JSONObject(enderecoCompleto);
            if (Integer.parseInt(obj.get("resultado").toString()) == 1) {
                this.novaPessoa.setBairro(FuncoesString.RecortaString(obj.get("bairro").toString(), 0, 20));
                this.novaPessoa.setEndereco(obj.get("tipo_logradouro").toString() + " " + obj.get("logradouro").toString());
                if (obj.get("uf").toString().equals("DF") || obj.get("uf").toString().equals("df")) {
                    this.novaPessoa.setCidade("BRASILIA");
                    this.novaPessoa.setUF("DF");
                } else {
                    this.cidades = pessoasatmobweb.ListaMunicipios(obj.get("uf").toString(), obj.get("cidade").toString(), this.persistenciaLocal);
                    this.novaPessoa.setCidade(this.cidades.get(0).getNome());
                    this.novaPessoa.setUF(this.cidades.get(0).getUF().substring(0, 2));
                }
                this.novaPessoa.setCEP(Mascaras.CEP(this.novaPessoa.getCEP()));
                PrimeFaces.current().executeScript("PF('dlgOk').show(); MascarasJS();");
            } else {
                PrimeFaces.current().executeScript("MascarasJS();");
                this.novaPessoa.setCEP(Mascaras.CEP(this.novaPessoa.getCEP()));
                throw new Exception(Messages.getMessageS("EnderecoNaoEncontrado"));
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            try {
                this.logerro.Grava(log, caminho);
            } catch (Exception ex) {
            }
        }
    }

    public void BuscarPessoaLocal() {
        try {
            String cpf = Mascaras.removeMascara(this.novaPessoa.getCPF());
            if (!cpf.equals("")) {
                Locale locale = LocaleController.getsCurrentLocale();
                if (locale.getLanguage().toUpperCase().equals("PT")) {
                    if (!ValidadorCPF_CNPJ.ValidarCPF(cpf)) {
                        this.edicao = true;
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                this.edicao = false;
                this.existeLocal = false;
                this.existeCentral = false;
                this.novaPessoa = this.pessoasatmobweb.buscarPessoa(cpf, this.persistenciaLocal);
                this.existeLocal = null != this.novaPessoa.getNome();
                if (!this.existeLocal) {
                    this.novaPessoa = this.pessoasatmobweb.buscarPessoa(cpf, this.persistenciaSat);
                    this.existeCentral = null != this.novaPessoa.getNome();
                } else {
                    this.situacao = this.novaPessoa.getSituacao();
                    if (this.situacao.equals("F")) {
                        this.editaSituacao = this.pessoasatmobweb.Situacao(this.novaPessoa, this.persistenciaLocal);
                    } else {
                        this.editaSituacao = false;
                    }
                }

                this.novaPessoa.setMatr("");
                this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
                this.novaPessoa.setCPF(Mascaras.CPF(this.novaPessoa.getCPF()));

                if (this.atualizado) {
                    try {
                        this.novaPessoa.setCEP(Mascaras.removeMascara(this.novaPessoa.getCEP()));
                        this.novaPessoa.setCEP(Mascaras.CEP(this.novaPessoa.getCEP()));
                    } catch (Exception e1) {
                    }
                    try {
                        this.extTV = this.novaPessoa.getExtTV().equals("S");
                    } catch (Exception e1) {
                    }
                    try {
                        this.extSPP = this.novaPessoa.getExtSPP().equals("S");
                    } catch (Exception e1) {
                    }
                    try {
                        this.extEscolta = this.novaPessoa.getExtEscolta().equals("S");
                    } catch (Exception e1) {
                    }
                    UpdateFormulario();
                }
                this.atualizado = false;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void buscarPessoaCofre() {
        try {
            String cpf = Mascaras.removeMascara(this.novaPessoa.getCPF());
            if (!cpf.equals("")) {
                Locale locale = LocaleController.getsCurrentLocale();
                if (locale.getLanguage().toUpperCase().equals("PT")) {
                    if (!ValidadorCPF_CNPJ.ValidarCPF(cpf)) {
                        this.edicao = true;
                        throw new Exception(Messages.getMessageS("CPFInvalido"));
                    }
                }
                this.edicao = false;
                this.existeLocal = false;
                this.existeCentral = false;
                this.novaPessoa = this.pessoasatmobweb.buscarPessoa(cpf, this.persistenciaLocal);
                this.existeLocal = null != this.novaPessoa.getNome();
                if (!this.existeLocal) {
                    this.novaPessoa = this.pessoasatmobweb.buscarPessoa(cpf, this.persistenciaSat);
                    this.existeCentral = null != this.novaPessoa.getNome();
                } else {
                    this.situacao = this.novaPessoa.getSituacao();
                    if (this.situacao.equals("F")) {
                        this.editaSituacao = this.pessoasatmobweb.Situacao(this.novaPessoa, this.persistenciaLocal);
                    } else {
                        this.editaSituacao = false;
                    }
                }

                if (this.existeLocal || this.existeCentral) {
                    this.flag = 2;
                }

                this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
                this.novaPessoa.setCPF(Mascaras.CPF(this.novaPessoa.getCPF()));

                if (this.atualizado) {
                    updateFormularioCofre();
                }
                this.atualizado = false;
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void updateFormularioCofre() {
        try {
            PrimeFaces.current().ajax().update("formCadastrar:cpf");
            PrimeFaces.current().ajax().update("formCadastrar:nome");
            PrimeFaces.current().ajax().update("formCadastrar:email");
            PrimeFaces.current().ajax().update("formCadastrar:cadastro");
            PrimeFaces.current().ajax().update("formCadastrar:editar");
        } catch (Exception e) {

        }
    }

    public void UpdateFormulario() {
        try {
            PrimeFaces.current().ajax().update("formCadastrar:cpf");
            PrimeFaces.current().ajax().update("formCadastrar:rg");
            PrimeFaces.current().ajax().update("formCadastrar:rgorg");
            PrimeFaces.current().ajax().update("formCadastrar:nome");
            PrimeFaces.current().ajax().update("formCadastrar:email");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:fone1");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:fone2");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:cep");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:ende");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:bairro");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:cidade");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:uf");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:obs");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:funcao");
            PrimeFaces.current().ajax().update("formCadastrar:matr");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:altura");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:peso");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:sexo");
            PrimeFaces.current().ajax().update("formCadastrar:tabs:situacao");
//            PrimeFaces.current().ajax().update("formCadastrar:tabs:tabFormacao");
            PrimeFaces.current().executeScript("MascarasJS();");
        } catch (Exception e) {
            PrimeFaces.current().ajax().update("formCadastrar:cpf");
            PrimeFaces.current().ajax().update("formCadastrar:rg");
            PrimeFaces.current().ajax().update("formCadastrar:rgorg");
            PrimeFaces.current().ajax().update("formCadastrar:nome");
            PrimeFaces.current().ajax().update("formCadastrar:email");
            PrimeFaces.current().ajax().update("formCadastrar:fone1");
            PrimeFaces.current().ajax().update("formCadastrar:fone2");
            PrimeFaces.current().ajax().update("formCadastrar:cep");
            PrimeFaces.current().ajax().update("formCadastrar:ende");
            PrimeFaces.current().ajax().update("formCadastrar:bairro");
            PrimeFaces.current().ajax().update("formCadastrar:cidade");
            PrimeFaces.current().ajax().update("formCadastrar:uf");
            PrimeFaces.current().ajax().update("formCadastrar:obs");
            PrimeFaces.current().ajax().update("formCadastrar:matr");
            PrimeFaces.current().ajax().update("formCadastrar:altura");
            PrimeFaces.current().ajax().update("formCadastrar:peso");
            PrimeFaces.current().ajax().update("formCadastrar:sexo");
            PrimeFaces.current().ajax().update("formCadastrar:situacao");
            PrimeFaces.current().executeScript("MascarasJS();");
        }
    }

    public List<Pessoa> ListarParametro(Pessoa pessoa) {
        List<Pessoa> retorno = new ArrayList<>();
        try {
            retorno = this.pessoasatmobweb.ListaPessoa(pessoa, this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return retorno;
    }

    private Map gerarNiveis(Boolean Todos) {
        Map map = new HashMap<>();
        // Se o nível não for algum nível especial, adiciona todos os niveis
        if (Todos) {
            map.put(Messages.getMessageS("Administrador"), "9");
            map.put(Messages.getMessageS("Operacao"), "1");
            map.put(Messages.getMessageS("Manutencao"), "2");
            map.put(Messages.getMessageS("Gerencia"), "3");
            map.put(Messages.getMessageS("PortalRH"), "4");
        }

        map.put(Messages.getMessageS("GTV"), "5");
        map.put(Messages.getMessageS("AssinarGTV"), "7");
        if (!spm) {
            map.put(Messages.getMessageS("SatMobEW"), "8");
        } else {
            map.put(Messages.getMessageS("SPMEW"), "8");
        }
        map.put(Messages.getMessageS("CofreInteligente"), "6");

        return map;
    }

    public boolean guardarQrCOde() {
        boolean enviado = false;
        try {
            //Criando imagem
            this.Base64Qr = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("model");
            String codPessoa = this.pessoaSelecionada.getCodigo().toPlainString().replace(".0", "");

            String Base64QrTratado = this.Base64Qr.substring(this.Base64Qr.indexOf(",") + 1);
            byte[] montar = Base64QrTratado.getBytes("UTF-8");

            String caminho = "C:/xampp/htdocs/satellite/qrCode/" + this.persistenciaLocal.getEmpresa() + "/" + DataAtual.getDataAtual("SQL");
            File diretorio = new File(caminho);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            String nome = caminho + "/" + codPessoa.replace(".0", "") + "_" + this.SequenciaAutorizacao.replace(".0", "") + ".jpg";

            FileOutputStream fos = new FileOutputStream(nome);
            fos.write(Base64.decode(Base64QrTratado));
            fos.close();

            // Enviar Msg WhatsApp
            String TelefoneEnvio = null != this.pessoaSelecionada.getFone1() && !this.pessoaSelecionada.getFone1().equals("") ? this.pessoaSelecionada.getFone1() : this.pessoaSelecionada.getFone2();
            String TextoEnvio = this.segAutorizaArea.getMensagem();

            String LinkImagem = nome.replace("C:/xampp/htdocs/satellite/", "https://mobile.sasw.com.br:9091/satellite/");

            Winzap.enviarMensagemImagem(TextoEnvio,
                    LinkImagem,
                    TelefoneEnvio,
                    this.codFil,
                    this.operador,
                    null,
                    null,
                    null,
                    this.persistenciaLocal.getEmpresa(),
                    this.persistenciaSat,
                    new ArquivoLogs("caminho"));

            PrimeFaces.current().resetInputs("formEnviarQrCode:cadastrar");
            PrimeFaces.current().ajax().update("formEnviarQrCode:cadastrar");
            PrimeFaces.current().executeScript("PF('dlgEnviarQrCode').hide()");

            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("MensagemSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);

            enviado = true;
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        return enviado;
    }

    public void abrirQrCode() throws Exception {
        if (null == this.pessoaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePessoa"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                if ((null != this.pessoaSelecionada.getFone1()
                        && !this.pessoaSelecionada.getFone1().equals(""))
                        || (null != this.pessoaSelecionada.getFone2()
                        && !this.pessoaSelecionada.getFone2().equals(""))) {

                    this.acessoAut = new AcessoAut();
                    this.segAutorizaArea = new SegAutorizaArea();

                    PrimeFaces.current().resetInputs("formEnviarQrCode:cadastrar");
                    PrimeFaces.current().ajax().update("formEnviarQrCode:cadastrar");

                    PrimeFaces.current().executeScript("PF('dlgEnviarQrCode').show();");
                    PrimeFaces.current().executeScript("gerarHtml();");
                    PrimeFaces.current().ajax().update("formEnviarQrCode:cadastrar");

                } else {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("PessoaSemTelefone"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void enviarQrCode() throws Exception {
        if (null == this.pessoaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePessoa"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                if ((null != this.pessoaSelecionada.getFone1()
                        && !this.pessoaSelecionada.getFone1().equals(""))
                        || (null != this.pessoaSelecionada.getFone2()
                        && !this.pessoaSelecionada.getFone2().equals(""))) {

                    SimpleDateFormat Formato = new SimpleDateFormat("yyyyMMdd");
                    Date DataAut = Formato.parse(this.acessoAut.getData());
                    Date DataHoje = Formato.parse(DataAtual.getDataAtual("SQL"));

                    if (DataAut.getTime() < DataHoje.getTime()) {
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("DataAutMenor"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    } else if (Integer.parseInt(this.segAutorizaArea.getHrEntrada().replace(":", "")) > Integer.parseInt(this.segAutorizaArea.getHrSaida().replace(":", ""))) {
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("HoraAutMenor"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    } else if (Integer.parseInt(this.segAutorizaArea.getHrEntrada().replace(":", "")) < Integer.parseInt(DataAtual.getDataAtual("HORA").replace(":", ""))) {
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("HoraAutMenor2"), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                    } else {

                        // Atribuição de Dados
                        this.segAutorizaArea.setCodFil(this.codFil);
                        this.segAutorizaArea.setCodArea("1");
                        this.segAutorizaArea.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                        this.segAutorizaArea.setHr_alter(DataAtual.getDataAtual("HORA"));
                        this.segAutorizaArea.setDt_alter(DataAtual.getDataAtual("SQL"));

                        this.acessoAut.setSituacao("OK");
                        this.acessoAut.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                        this.acessoAut.setHr_Alter(DataAtual.getDataAtual("HORA"));
                        this.acessoAut.setDt_Alter(DataAtual.getDataAtual("SQL"));

                        // Insert em BD
                        PessoaDao pessoaDao = new PessoaDao();
                        this.SequenciaAutorizacao = pessoaDao.gravarAutorizacaoPessoa(this.pessoaSelecionada, this.segAutorizaArea, this.acessoAut, this.persistenciaLocal).replace(".0", "");

                        String LogoQrCode = LoginMB.getLogoS(this.persistenciaLocal.getEmpresa());

                        PrimeFaces.current().executeScript("enviarQrCode('" + this.SequenciaAutorizacao + "', '" + this.acessoAut.getData() + "', '" + this.segAutorizaArea.getHrEntrada() + " - " + this.segAutorizaArea.getHrSaida() + "','" + LogoQrCode + "');");
                    }
                }
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        }
    }

    public void preUsuario() {
        if (null == this.pessoaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePessoa"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {

            if (null == this.pessoaSelecionada.getCodPessoaWEB()) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("PessoaSemCodPessoaWeb"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                if (null == this.pessoaSelecionada.getSenha() || this.pessoaSelecionada.getSenha().equals("")) {
                    this.niveis = gerarNiveis(false);
                } else {
                    this.niveis = gerarNiveis(true);
                }

                this.pessoaLoginUsuario.getSaspw().setPW(this.pessoaSelecionada.getSenha());
                this.pessoaLoginUsuario.getSaspw().setNivelx(this.pessoaSelecionada.getNivelx());

                if (null == this.pessoaSelecionada.getSenha() || this.pessoaSelecionada.getSenha().equals("")) {
                    PrimeFaces.current().resetInputs("formCadastrarUsuario:cadastrar");
                }

                PrimeFaces.current().ajax().update("formCadastrarUsuario:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').show()");
            }
        }
    }

    public void PreCadastro() {
        this.edicao = false;
        this.flag = 1;
        this.novaPessoa = new Pessoa();
        if (this.persistenciaLocal.getEmpresa().equals("SATMAXIMA")) {
            this.novaPessoa.setSituacao("I");
        } else {
            this.novaPessoa.setSituacao("P");
        }
        this.cidades = new ArrayList<>();
        /*Locale locale = LocaleController.getsCurrentLocale();
        if (locale.getLanguage().toUpperCase().equals("PT")) {
            this.edicao = true;
        } else {
            this.edicao = false;
        }*/
        this.edicao = false;
        this.existeLocal = false;
        this.existeCentral = false;
//        this.edicao = true;
        this.atualizado = true;
        this.editaSituacao = false;
        PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
        try {
            TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
            tabs.setActiveIndex(0);
        } catch (Exception e) {
        }
        PrimeFaces.current().executeScript("PF('dlgCadastrar').show(); MascarasJS();");
    }

    public void PreCadastro2() {
        this.edicao = false;
        this.flag = 1;
        this.novaPessoa = new Pessoa();
        if (this.persistenciaLocal.getEmpresa().equals("SATMAXIMA")) {
            this.novaPessoa.setSituacao("I");
        } else {
            this.novaPessoa.setSituacao("P");
        }
        this.cidades = new ArrayList<>();
        this.edicao = false;
        this.existeLocal = false;
        this.existeCentral = false;
        this.atualizado = true;
        this.editaSituacao = false;
    }

    public void GerarRG() throws Exception {
        if (null != this.novaPessoa.getRGOrgEmis()
                && this.novaPessoa.getRGOrgEmis().toUpperCase().equals("MSL")) {
            PessoaDao pessoaDao = new PessoaDao();
            this.novaPessoa.setRG(pessoaDao.gerarRG(this.persistenciaLocal));
        }
    }

    public void PrePesquisa() {
        this.flag = 1;
        this.novaPessoa = new Pessoa();
        this.cidades = new ArrayList<>();
        this.edicao = true;
        this.atualizado = true;
        this.editaSituacao = false;
    }

    public void cadastroSimples() {
        try {
            Locale locale = LocaleController.getsCurrentLocale();

            this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
            if (locale.getLanguage().toUpperCase().equals("PT")) {
                if (!ValidadorCPF_CNPJ.ValidarCPF(this.novaPessoa.getCPF())) {
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }

            this.novaPessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaPessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaPessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novaPessoa.setDt_Situac(DataAtual.getDataAtual("SQL"));
            this.novaPessoa.setNome(this.novaPessoa.getNome().toUpperCase());
            this.novaPessoa.setEmail(this.novaPessoa.getEmail().toLowerCase());
            this.novaPessoa.setSituacao("W");

            if (this.existeLocal || this.flag == 2) {
                if (this.situacao.equals("F")) {
                    this.novaPessoa.setSituacao("F");
                }
                this.pessoasatmobweb.gravar(this.novaPessoa, this.persistenciaLocal, this.persistenciaSat);
            } else {
                this.pessoasatmobweb.Inserir(this.novaPessoa, this.persistenciaLocal, this.persistenciaSat);
            }

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novaPessoa) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastroSenha() {
        try {
            AcessosDao objDao = new AcessosDao();

            if ((null == this.pessoaSelecionada.getSenha()
                    || this.pessoaSelecionada.getSenha().equals(""))
                    && (null == this.pessoaSelecionada.getNivelx()
                    || this.pessoaSelecionada.getNivelx().equals(""))) {
                // CRIAR USUARIO
                // Preencher Banco Local
                this.pessoaLoginUsuario.getSaspw().setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                switch (this.pessoaLoginUsuario.getSaspw().getNivelx()) {
                    case "1":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Operação");
                        break;
                    case "2":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Manutenção");
                        break;
                    case "3":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Gerência");
                        break;
                    case "4":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Portal RH");
                        break;
                    case "5":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("GTV");
                        break;
                    case "6":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Cofre Int.");
                        break;
                    case "7":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Assinar GTV-e");
                        break;
                    case "8":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("SatMobEW");
                        break;
                    case "9":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Diretoria");
                        break;
                    case "10":
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("Chamados");
                        break;
                    default:
                        this.pessoaLoginUsuario.getSaspw().setNivelOP("");
                        break;
                }
                this.pessoaLoginUsuario.getSaspw().setNivelOP(this.pessoaLoginUsuario.getSaspw().getNivelOP().toUpperCase());
                this.pessoaLoginUsuario.getSaspw().setCodGrupo(18);
                this.pessoaLoginUsuario.getSaspw().setCodPessoa(this.pessoaSelecionada.getCodigo().toPlainString().substring(0, this.pessoaSelecionada.getCodigo().toPlainString().lastIndexOf(".0")));
                this.pessoaLoginUsuario.getSaspw().setCodPessoaWeb(this.pessoaSelecionada.getCodPessoaWEB().toPlainString());
                this.pessoaLoginUsuario.getSaspw().setNomeCompleto(this.pessoaSelecionada.getNome());
                this.pessoaLoginUsuario.getSaspw().setNome(this.pessoaSelecionada.getCodigo().toPlainString().substring(0, this.pessoaSelecionada.getCodigo().toPlainString().lastIndexOf(".0")));
                this.pessoaLoginUsuario.getSaspw().setCodFil(this.codFil);
                this.pessoaLoginUsuario.getSaspw().setCodigo(this.pessoaSelecionada.getCodigo().toPlainString().substring(0, this.pessoaSelecionada.getCodigo().toPlainString().lastIndexOf(".0")));
                this.pessoaLoginUsuario.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.pessoaLoginUsuario.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.pessoaLoginUsuario.getSaspw().setSituacao("A");

                SasPWFill sasPwFill = new SasPWFill();

                sasPwFill.setCodFil(this.codFil);
                sasPwFill.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                sasPwFill.setDt_Alter(DataAtual.getDataAtual("SQL"));
                sasPwFill.setHr_Alter(DataAtual.getDataAtual("HORA"));
                sasPwFill.setNome(this.pessoaSelecionada.getCodigo().toPlainString().substring(0, this.pessoaSelecionada.getCodigo().toPlainString().lastIndexOf(".0")));
                sasPwFill.setCodfilAc(this.codFil);
                sasPwFill.setCodigo(this.pessoaSelecionada.getCodigo().toPlainString().substring(0, this.pessoaSelecionada.getCodigo().toPlainString().lastIndexOf(".0")));

                objDao.criarUsuario(this.pessoaLoginUsuario.getSaspw(), this.persistenciaLocal);
                objDao.inserirFilial(sasPwFill, this.persistenciaLocal);

                // Preencher Banco Central
                PessoaLogin pessoaLogin = new PessoaLogin();

                pessoaLogin.setCodigo(this.pessoaSelecionada.getCodPessoaWEB().toPlainString());
                pessoaLogin.setBancoDados(banco);
                pessoaLogin.setNivel(this.pessoaLoginUsuario.getSaspw().getNivelx());
                pessoaLogin.setCodPessoaBD(this.pessoaSelecionada.getCodigo().toPlainString().substring(0, this.pessoaSelecionada.getCodigo().toPlainString().lastIndexOf(".0")));
                pessoaLogin.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                pessoaLogin.setDt_alter(DataAtual.getDataAtual("SQL"));
                pessoaLogin.setHr_Alter(DataAtual.getDataAtual("HORA"));

                PessoaLoginDao pessoaLoginDao = new PessoaLoginDao();
                pessoaLoginDao.inserirPessoaLogin(pessoaLogin, this.persistenciaSat);

            }

            // SOMENTE ALTERAR SENHA
            objDao.alterarSenhaSimples(this.pessoaSelecionada.getCodigo(), this.pessoaLoginUsuario.getSaspw().getPW(), this.persistenciaLocal);
            objDao.alterarSenhaSimplesCentral(this.pessoaSelecionada.getCodPessoaWEB(), this.pessoaLoginUsuario.getSaspw().getPW(), this.persistenciaSat);

            getAllPessoa();
            PrimeFaces.current().executeScript("PF('dlgCadastrarUsuario').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("DadosSalvosSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\nOPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void cadastrar() {
        try {
            Locale locale = LocaleController.getsCurrentLocale();

            this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
            if (locale.getLanguage().toUpperCase().equals("PT")
                    && !this.novaPessoa.getSituacao().equals("I")
                    && !this.novaPessoa.getCPF().equals("")) {
                if (!ValidadorCPF_CNPJ.ValidarCPF(this.novaPessoa.getCPF())) {
                    throw new Exception(Messages.getMessageS("CPFInvalido"));
                }
            }

            this.novaPessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaPessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaPessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novaPessoa.setDt_Situac(DataAtual.getDataAtual("SQL"));
            this.novaPessoa.setNome(this.novaPessoa.getNome().toUpperCase());

            if (!this.novaPessoa.getSituacao().equals("I")) {
                this.novaPessoa.setRG(this.novaPessoa.getRG().toUpperCase());
                this.novaPessoa.setRGOrgEmis(this.novaPessoa.getRGOrgEmis().toUpperCase());
                this.novaPessoa.setEmail(this.novaPessoa.getEmail().toLowerCase());
            }

            this.novaPessoa.setEndereco(FuncoesString.RecortaString(this.novaPessoa.getEndereco().toUpperCase(), 0, 50));
            this.novaPessoa.setBairro(FuncoesString.RecortaString(this.novaPessoa.getBairro().toUpperCase(), 0, 20));
            this.novaPessoa.setUF(FuncoesString.RecortaString(this.novaPessoa.getUF().toUpperCase(), 0, 2));
            this.novaPessoa.setFuncao(this.novaPessoa.getFuncao().toUpperCase());

            if (this.novaPessoa.getComplemento() != null && !this.novaPessoa.getComplemento().equals("")) {
                this.novaPessoa.setComplemento(this.novaPessoa.getComplemento().toUpperCase());
            }

            if (this.novaPessoa.getSexo() != null) {
                this.novaPessoa.setSexo(this.novaPessoa.getSexo().toUpperCase());
            }

            this.novaPessoa.setCEP(Mascaras.removeMascara(this.novaPessoa.getCEP()));

            if (!this.novaPessoa.getSituacao().equals("I")) {
                this.novaPessoa.setFone1(Mascaras.removeMascara(this.novaPessoa.getFone1()));
                this.novaPessoa.setFone2(Mascaras.removeMascara(this.novaPessoa.getFone2()));
                this.novaPessoa.setDt_nasc(Mascaras.removeMascara(this.novaPessoa.getDt_nasc()));
                this.novaPessoa.setDtValCNV(Mascaras.removeMascara(this.novaPessoa.getDtValCNV()));
            }

            if (!this.novaPessoa.getSituacao().equals("I")) {
                if (this.novaPessoa.getAltura() != null || this.novaPessoa.getPeso() != null) {
                    if (verificaVirgula(this.novaPessoa.getAltura().toString())) {
                        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("AlturaSemVirgula"), null);
                        FacesContext.getCurrentInstance().addMessage(null, message);
                    } else if (verificaVirgula(this.novaPessoa.getPeso().toString())) {
                        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("PessoaSemVirgula"), null);
                        FacesContext.getCurrentInstance().addMessage(null, message);
                    }
                }
            } else {
                this.novaPessoa.setAltura("0");
                this.novaPessoa.setAltura(BigDecimal.ZERO);
                this.novaPessoa.setPeso("0");
                this.novaPessoa.setPeso(BigDecimal.ZERO);
            }

            if (this.existeLocal) {
                if (this.situacao.equals("F")) {
                    this.novaPessoa.setSituacao("F");
                }
                this.pessoasatmobweb.gravar(this.novaPessoa, this.persistenciaLocal, this.persistenciaSat);
                this.pessoasatmobweb.atualizarPessoaDieta(this.novaPessoa, this.persistenciaLocal);
            } else {
                this.pessoasatmobweb.Inserir(this.novaPessoa, this.persistenciaLocal, this.persistenciaSat);
                this.pessoasatmobweb.atualizarPessoaDieta(this.novaPessoa, this.persistenciaLocal);
            }

            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);

            if (this.novaPessoa.getSituacao().equals("C") && this.flag == 1) {
                this.situacao = this.novaPessoa.getSituacao();
                if (null != this.situacao && this.situacao.equals("F")) {
                    this.editaSituacao = this.pessoasatmobweb.Situacao(this.novaPessoa, this.persistenciaLocal);
                } else {
                    this.editaSituacao = false;
                }
                this.flag = 2;
                this.edicao = false;
                this.novaPessoa.setCEP(Mascaras.removeMascara(this.novaPessoa.getCEP()));
                this.novaPessoa.setCEP(Mascaras.CEP(this.novaPessoa.getCEP()));

                this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
                this.novaPessoa.setCPF(Mascaras.CPF(this.novaPessoa.getCPF()));

                this.contatos = this.pessoasatmobweb.ListaContatos(this.novaPessoa.getCodPessoaWEB(), this.persistenciaSat);
                PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
                TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
                tabs.setActiveIndex(1);
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage()
                    + "\r\n" + Logger.objeto2String(this.novaPessoa) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public boolean verificaVirgula(String numero) {
        char[] array = numero.toCharArray();
        boolean retorno = false;

        for (int i = 0; i < array.length; i++) {
            retorno = array[i] == ',' || array[i] == '.';
        }
        return retorno;
    }

    public void editar() {
        try {
            if (null == this.novaPessoa.getSituacao()) {
                if (this.situacao.equals("F")) {
                    this.novaPessoa.setSituacao("F");
                } else {
                    throw new Exception(Messages.getMessageS("SelecioneSituacao"));
                }
            }

            this.novaPessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.novaPessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.novaPessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
            this.novaPessoa.setDt_Situac(DataAtual.getDataAtual("SQL"));
            this.novaPessoa.setNome(this.novaPessoa.getNome().toUpperCase());

            if (!this.novaPessoa.getSituacao().equals("I")) {
                this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
                this.novaPessoa.setRG(this.novaPessoa.getRG().toUpperCase());
                this.novaPessoa.setRGOrgEmis(this.novaPessoa.getRGOrgEmis().toUpperCase());
                this.novaPessoa.setEmail(this.novaPessoa.getEmail().toLowerCase());
                this.novaPessoa.setFone1(Mascaras.removeMascara(this.novaPessoa.getFone1()));
                this.novaPessoa.setFone2(Mascaras.removeMascara(this.novaPessoa.getFone2()));
                this.novaPessoa.setDt_nasc(Mascaras.removeMascara(this.novaPessoa.getDt_nasc()));
                this.novaPessoa.setDtValCNV(Mascaras.removeMascara(this.novaPessoa.getDtValCNV()));
            }

            if (this.novaPessoa.getComplemento() != null && !this.novaPessoa.getComplemento().equals("")) {
                this.novaPessoa.setComplemento(this.novaPessoa.getComplemento().toUpperCase());
            }

            this.novaPessoa.setEndereco(FuncoesString.RecortaString(this.novaPessoa.getEndereco().toUpperCase(), 0, 50));
            this.novaPessoa.setBairro(FuncoesString.RecortaString(this.novaPessoa.getBairro(), 0, 20));
            this.novaPessoa.setFuncao(this.novaPessoa.getFuncao().toUpperCase());
            this.novaPessoa.setUF(FuncoesString.RecortaString(this.novaPessoa.getUF().toUpperCase(), 0, 2));
            this.novaPessoa.setSexo(this.novaPessoa.getSexo().toUpperCase());
            this.novaPessoa.setObs(this.novaPessoa.getObs().toUpperCase());

            this.novaPessoa.setCEP(Mascaras.removeMascara(this.novaPessoa.getCEP()));

            if (this.novaPessoa.getAltura() != null || this.novaPessoa.getPeso() != null) {
                if (verificaVirgula(this.novaPessoa.getAltura().toString()) == true) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("AlturaSemVirgula"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else if (verificaVirgula(this.novaPessoa.getPeso().toString()) == true) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("PessoaSemVirgula"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                }
            }

            this.novaPessoa.setIndicacao(this.novaPessoa.getIndicacao().toUpperCase());
            this.novaPessoa.setDt_FormIni(Mascaras.removeMascaraData(this.novaPessoa.getDt_FormIni()));
            this.novaPessoa.setDt_FormFim(Mascaras.removeMascaraData(this.novaPessoa.getDt_FormFim()));
            this.novaPessoa.setLocalForm(this.novaPessoa.getLocalForm().toUpperCase());
            this.novaPessoa.setCertific(this.novaPessoa.getCertific().toUpperCase());
            this.novaPessoa.setDt_Recicl(Mascaras.removeMascaraData(this.novaPessoa.getDt_Recicl()));
            this.novaPessoa.setDt_VenCurs(Mascaras.removeMascaraData(this.novaPessoa.getDt_VenCurs()));
            this.novaPessoa.setReg_PF(this.novaPessoa.getReg_PF().toUpperCase());
            this.novaPessoa.setReg_PFUF(this.novaPessoa.getReg_PFUF().toUpperCase());
            this.novaPessoa.setReg_PFDt(Mascaras.removeMascaraData(this.novaPessoa.getReg_PFDt()));
            this.novaPessoa.setCNH(this.novaPessoa.getCNH().toUpperCase());
            this.novaPessoa.setCNHDtVenc(Mascaras.removeMascaraData(this.novaPessoa.getCNHDtVenc()));
            this.novaPessoa.setExtTV(this.extTV ? "S" : "N");
            this.novaPessoa.setExtSPP(this.extSPP ? "S" : "N");
            this.novaPessoa.setExtEscolta(this.extEscolta ? "S" : "N");

            this.novaPessoa = (Pessoa) FuncoesString.removeAcentoObjeto(this.novaPessoa);
            this.pessoasatmobweb.gravar(this.novaPessoa, this.persistenciaLocal, this.persistenciaSat);
            this.pessoasatmobweb.atualizarPessoaDieta(this.novaPessoa, this.persistenciaLocal);
            PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("EdicaoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.novaPessoa) + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Cargos> buscarCargo(String query) {
        List<Cargos> retorno = new ArrayList<>();
        try {
            for (Cargos cargo : this.cargos) {
                if (cargo.getCodigo().toBigInteger().toString().contains(query)
                        || cargo.getCargo().contains(query)
                        || cargo.getDescricao().toUpperCase().contains(query.toUpperCase())) {
                    cargo.setDescricao(cargo.getCargo() + " - " + cargo.getDescricao());
                    retorno.add(cargo);
                }
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return retorno;
    }

    public void selecionarCargo(SelectEvent event) {
        try {
            this.cargo = (Cargos) event.getObject();
            this.cargosPretendidos = this.pessoasatmobweb.adicionarCargoPretendido(cargo,
                    this.novaPessoa.getCodigo().toString(), this.operador, this.persistenciaLocal);
            this.cargo = null;
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("CargoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void removerCargoPretendido(Pe_Cargo pe_cargo) {
        try {
            this.cargosPretendidos = this.pessoasatmobweb.removerCargoPretendido(pe_cargo, this.novaPessoa.getCodigo().toString(), this.persistenciaLocal);
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("ExclusaoCargoSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.pessoaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecionePessoa"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                if (null != this.pessoaSelecionada.getCodCli() && !this.pessoaSelecionada.getCodCli().equals("")) {
                    this.clientesMB.setCodigo(this.pessoaSelecionada.getCodCli());
                    this.clientesMB.BuscarCliente();
                    if (this.clientesMB.getLista() != null && !this.clientesMB.getLista().isEmpty()) {
                        this.clientes = this.clientesMB.getLista().get(0);
                    } else {
                        this.clientes = null;
                    }
                }

                this.novaPessoa = this.pessoaSelecionada;
                this.situacao = this.novaPessoa.getSituacao();
                if (null != this.situacao && this.situacao.equals("F")) {
                    this.editaSituacao = this.pessoasatmobweb.Situacao(this.novaPessoa, this.persistenciaLocal);
                } else {
                    this.editaSituacao = false;
                }
                this.flag = 2;
                this.edicao = false;
                this.novaPessoa.setCEP(Mascaras.removeMascara(this.novaPessoa.getCEP()));
                this.novaPessoa.setCEP(Mascaras.CEP(this.novaPessoa.getCEP()));

                this.novaPessoa.setCPF(Mascaras.removeMascara(this.novaPessoa.getCPF()));
                this.novaPessoa.setCPF(Mascaras.CPF(this.novaPessoa.getCPF()));

                this.extTV = this.novaPessoa.getExtTV().equals("S");
                this.extSPP = this.novaPessoa.getExtSPP().equals("S");
                this.extEscolta = this.novaPessoa.getExtEscolta().equals("S");

                this.documentos = this.pessoasatmobweb.listarDoctos(this.novaPessoa.getCodigo(), this.persistenciaLocal);
                this.contatos = this.pessoasatmobweb.ListaContatos(this.novaPessoa.getCodPessoaWEB(), this.persistenciaSat);
                this.cargosPretendidos = this.pessoasatmobweb.listarCargosPretendidos(this.novaPessoa.getCodigo().toString(), this.persistenciaLocal);
                
                this.novaPessoa = preencherChaveAcesso(this.novaPessoa);
                
                TabView tabs = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("formCadastrar:tabs");
                tabs.setActiveIndex(0);
                PrimeFaces.current().resetInputs("formCadastrar:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrar').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void dblSelect(SelectEvent event) {
        this.pessoaSelecionada = (Pessoa) event.getObject();
        buttonAction(null);
    }

    public void dblSelectDieta(SelectEvent event) {
        this.dietaSelecionada = (SasBeansCompostas.Dieta) event.getObject();
        buttonActionDieta(null);
    }

    public void buttonActionDietaNovo(ActionEvent actionEvent) {
        dietaNova = new SasBeansCompostas.Dieta();
        dietaNova.setDescricao("");
        dietaNova.setEspecificacao("");
        dietaNova.setSequencia("0.0");

        PrimeFaces.current().resetInputs("formDietasCadastro:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastrarDietasForm').show();");
    }

    public void salvarDadosDieta() throws Exception {
        this.dietaNova.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
        this.dietaNova.setHr_Alter(DataAtual.getDataAtual("HORA"));
        this.dietaNova.setDt_alter(DataAtual.getDataAtual("SQL"));
        this.dietaNova.setDescricao(this.dietaNova.getDescricao().toUpperCase());
        this.dietaNova.setEspecificacao(this.dietaNova.getEspecificacao().toUpperCase());

        dieta.salvarDieta(persistenciaLocal, this.dietaNova);
        listaDietas = dieta.listaDieta(persistenciaLocal);
    }

    public void buttonActionDieta(ActionEvent actionEvent) {
        if (null == this.dietaSelecionada) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneDieta"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else if (new Float(this.dietaSelecionada.getSequencia()) == 1) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("NaoPodeAlterarGeral"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                dietaNova = new SasBeansCompostas.Dieta();
                dietaNova.setDescricao(dietaSelecionada.getDescricao());
                dietaNova.setEspecificacao(dietaSelecionada.getEspecificacao());
                dietaNova.setSequencia(dietaSelecionada.getSequencia());

                PrimeFaces.current().resetInputs("formDietasCadastro:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastrarDietasForm').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void onRowSelect(SelectEvent event) {
        this.pessoaSelecionada = (Pessoa) event.getObject();
    }

    public void handleFileUpload(FileUploadEvent fileUploadEvent) {
        try {
            if (fileUploadEvent.getFile().getSize() == 0) {
                throw new Exception(Messages.getMessageS("SelecioneArquivo"));
            } else {
                this.uploadedFile = fileUploadEvent.getFile();
//                this.inputStream = new BufferedInputStream(this.uploadedFile.getInputstream());
                //String arquivo = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"+this.uploadedFile.getFileName();
                Pe_Doctos docto = new Pe_Doctos();;
                docto.setDescricao(this.uploadedFile.getFileName());
                docto.setCodigo(this.novaPessoa.getCodigo().toBigInteger().toString());
                docto.setDt_alter(DataAtual.getDataAtual("SQL"));
                docto.setHr_Alter(DataAtual.getDataAtual("HORA"));
                docto.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                docto = this.pessoasatmobweb.inserirDocumento(docto, this.persistenciaLocal);
                new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa()
                        + "\\" + this.novaPessoa.getCodigo().toBigInteger()).mkdirs();
                String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa()
                        + "\\" + this.novaPessoa.getCodigo().toBigInteger() + "\\" + docto.getDescricao();
                File file = new File(arquivo);
                FileOutputStream output = new FileOutputStream(file);
                output.write(this.uploadedFile.getContents());
                output.close();
                this.documentos = this.pessoasatmobweb.listarDoctos(this.novaPessoa.getCodigo(), this.persistenciaLocal);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void handleFileDownload(Pe_Doctos docto) {
        try {
            this.documento = docto;
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa()
                    + "\\" + this.novaPessoa.getCodigo().toBigInteger() + "\\" + this.documento.getDescricao();
            InputStream stream = new FileInputStream(arquivo);
            this.download = new DefaultStreamedContent(stream, "application/" + this.documento.getDescricao().split("\\.")[this.documento.getDescricao().split("\\.").length - 1], this.documento.getDescricao());
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ArquivoNaoEncontrado"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    /**
     * Excluí o documento no servidor O documento excluído e movido para uma
     * pasta de removidos O nome do documento entao e alterado para o formato
     * "Nome_UsuarioQueRealizouAcao_yyyy-MM-dd HH-mm-ss.extensao"
     *
     * @param docto
     */
    public void handleFileDelete(Pe_Doctos docto) {
        try {
            this.documento = docto;

            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa()
                    + "\\" + this.novaPessoa.getCodigo().toBigInteger() + "\\" + this.documento.getDescricao();
            File file = new File(arquivo);

            //cria path de removidos
            File removidos = new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistenciaLocal.getEmpresa()
                    + "\\" + this.novaPessoa.getCodigo().toBigInteger() + "\\" + "Removidos");
            removidos.mkdirs();

            if (this.documento.getDescricao().lastIndexOf(".") > 0) {
                String nome = this.documento.getDescricao().substring(0, this.documento.getDescricao().lastIndexOf("."));
                String tipo = this.documento.getDescricao().split("\\.")[this.documento.getDescricao().split("\\.").length - 1];

                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
                LocalDateTime now = LocalDateTime.now();

                file.renameTo(new File(removidos + "\\" + nome + "_" + FuncoesString.RecortaAteEspaço(this.operador, 0, 10) + "_" + dtf.format(now) + "." + tipo));
            }
            this.pessoasatmobweb.deletarDocumento(this.documento, this.persistenciaLocal);
            this.documentos = this.pessoasatmobweb.listarDoctos(this.novaPessoa.getCodigo(), this.persistenciaLocal);
            //file.delete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public LazyDataModel<Pessoa> getAllPessoa() {
        if (this.pessoas == null) {
            this.pessoas = new PessoaLazyList(this.persistenciaLocal);
        }
        return this.pessoas;
    }

    public void listarPessoas() throws Exception {
        PessoaDao pessoaDao = new PessoaDao();

        this.pessoasCompleto = pessoaDao.listaCompleta(this.filters, this.persistenciaLocal);
    }

    public void PesquisaPaginada() {
        try {
            //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            if (!novaPessoa.getNome().equals("")) {
                filters.replace(NOME, "%" + novaPessoa.getNome() + "%");
            } else {
                filters.replace(NOME, "");
            }
            if (null != novaPessoa.getSituacao()) {
                filters.replace(SITUACAO, "%" + novaPessoa.getSituacao() + "%");
            } else {
                filters.replace(SITUACAO, "");
            }
            if (null != novaPessoa.getCidade()) {
                filters.replace(CIDADE, "%" + novaPessoa.getCidade() + "%");
            } else {
                filters.replace(CIDADE, "");
            }
            if (!novaPessoa.getUF().equals("")) {
                filters.replace(UF, "%" + novaPessoa.getUF() + "%");
            } else {
                filters.replace(UF, "");
            }
            if (!novaPessoa.getRG().equals("")) {
                filters.replace(RG, novaPessoa.getRG());
            } else {
                filters.replace(RG, "");
            }
            if (!novaPessoa.getCPF().equals("")) {
                filters.replace(CPF, novaPessoa.getCPF());
            } else {
                filters.replace(CPF, "");
            }
            if (null != novaPessoa.getMatr()) {
                filters.replace(MATR, novaPessoa.getMatr().toPlainString());
            } else {
                filters.replace(MATR, "");
            }
            if (null != novaPessoa.getCodigo()) {
                filters.replace(CODIGO, novaPessoa.getCodigo().toPlainString());
            } else {
                filters.replace(CODIGO, "");
            }

            listarPessoas();

            /*total = pessoasatmobweb.contagemQuick(filters, persistenciaLocal);
            dt.setFilters(filters);
            getAllPessoa();
            dt.setFirst(0);*/
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    private void replaceFilter(String valor) {
        switch (chavePesquisa) {
            case "NOME":
                filters.replace(NOME, "%" + valor + "%");
                return;
            case "SITUACAO":
                filters.replace(SITUACAO, "%" + valor + "%");
                return;
            case "CIDADE":
                filters.replace(CIDADE, "%" + valor + "%");
                return;
            case "UF":
                filters.replace(UF, "%" + valor + "%");
                return;
            case "RG":
                filters.replace(RG, "%" + valor + "%");
                return;
            case "CPF":
                filters.replace(CPF, valor);
                return;
            case "MATR":
                filters.replace(MATR, valor);
                return;
            case "CODIGO":
                filters.replace(CODIGO, valor);
                return;
            case "CARGO":
                filters.replace(CODIGO, valor);
                return;
        }
    }

    public void pesquisaRapida() {
        try {
            /*DataTable dt = (DataTable) FacesContext.getCurrentInstance()
                    .getViewRoot()
                    .findComponent("main:tabela");*/

            this.LimparFiltros();
            this.replaceFilter(valorPesquisa);

            /*total = pessoasatmobweb.contagemQuick(filters, persistenciaLocal);
            dt.setFilters(filters);
            getAllPessoa();
            dt.setFirst(0);*/
            listarPessoas();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            logerro.Grava(log, caminho);
        }
    }

    public List<Clientes> ListarClientes(String query) {
        try {
            return this.pstservsatmobweb.ListarClientes(this.codFil.toString(), query, this.persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return null;
    }

    public void SelecionarCliente(SelectEvent event) {
        Clientes cli = ((Clientes) event.getObject());
        this.clientesMB.setCodigo(cli.getCodigo());
        this.clientesMB.BuscarCliente();
        this.clientes = this.clientesMB.getLista().get(0);
        EnderecoCliente();
        this.novaPessoa.setCodCli(this.clientes.getCodigo());
    }

    public void EnderecoCliente() {
        try {
            if (!this.clientes.getEnde().equals("")) {
                this.novaPessoa.setEndereco(this.clientes.getEnde());
            } else {
                this.novaPessoa.setEndereco("SemEnderecoCadastrado");
            }

            if (!this.clientes.getBairro().equals("")) {
                this.novaPessoa.setBairro(this.clientes.getBairro());
            } else {
                this.novaPessoa.setBairro("SemEnderecoCadastrado");
            }

            if (!this.clientes.getEstado().equals("")) {
                this.novaPessoa.setUF(this.clientes.getEstado());
            } else {
                this.novaPessoa.setUF("DF");
            }

            if (!this.clientes.getCidade().equals("")) {
                this.novaPessoa.setCidade(this.clientes.getCidade());
            } else {
                this.novaPessoa.setCidade("SemEnderecoCadastrado");
            }

            if (!this.clientes.getCEP().equals("")) {
                this.novaPessoa.setCEP(this.clientes.getCEP());
            } else {
                this.novaPessoa.setCEP("000000-000");
            }

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void LimparFiltros() {
        try {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            filters.replace(NOME, "");
            filters.replace(SITUACAO, "");
            filters.replace(CIDADE, "");
            filters.replace(UF, "");
            filters.replace(RG, "");
            filters.replace(CPF, "");
            filters.replace(MATR, "");
            filters.replace(CODIGO, "");
            filters.replace(CARGO, "");
            dt.setFilters(this.filters);
            getAllPessoa();
            this.limparFiltros = false;
            this.total = this.pessoasatmobweb.Contagem(this.filters, persistenciaLocal);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void AtualizaTabela() {
        PrimeFaces.current().ajax().update("main:tabela");
    }

    public void AtualizaTabelaMenu() {
        PrimeFaces.current().ajax().update("exportarPessoa:tabela");
    }

    public void NovoContato() {
        try {
            if (null == this.novaPessoa.getCodPessoaWEB()) {
                if (null == this.novaPessoa.getSituacao()) {
                    if (this.situacao.equals("F")) {
                        this.novaPessoa.setSituacao("F");
                    } else {
                        throw new Exception(Messages.getMessageS("SelecioneSituacao"));
                    }
                }
                UIViewRoot viewRoot = FacesContext.getCurrentInstance().getViewRoot();
                Locale locale = viewRoot.getLocale();
                if (locale.getLanguage().toUpperCase().equals("PT")) {
                    if (!this.novaPessoa.getCPF().isEmpty()) {
                        this.novaPessoa.setCPF(this.novaPessoa.getCPF().substring(0, 3)
                                + this.novaPessoa.getCPF().substring(4, 7)
                                + this.novaPessoa.getCPF().substring(8, 11)
                                + this.novaPessoa.getCPF().substring(12, 14));
                    }
                }
                this.novaPessoa.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.novaPessoa.setHr_Alter(DataAtual.getDataAtual("HORA"));
                this.novaPessoa.setDt_Alter(DataAtual.getDataAtual("SQL"));
                this.novaPessoa.setDt_Situac(DataAtual.getDataAtual("SQL"));
                this.novaPessoa.setNome(this.novaPessoa.getNome().toUpperCase());
                this.novaPessoa.setRG(this.novaPessoa.getRG().toUpperCase());
                this.novaPessoa.setRGOrgEmis(this.novaPessoa.getRGOrgEmis().toUpperCase());
                this.novaPessoa.setEndereco(FuncoesString.RecortaString(this.novaPessoa.getEndereco().toUpperCase(), 0, 50));
                this.novaPessoa.setBairro(FuncoesString.RecortaString(this.novaPessoa.getBairro(), 0, 20));
                this.novaPessoa.setFuncao(this.novaPessoa.getFuncao().toUpperCase());
                this.novaPessoa.setUF(FuncoesString.RecortaString(this.novaPessoa.getUF().toUpperCase(), 0, 2));
                this.novaPessoa.setSexo(this.novaPessoa.getSexo().toUpperCase());
                this.novaPessoa.setObs(this.novaPessoa.getObs().toUpperCase());
                this.novaPessoa.setEmail(this.novaPessoa.getEmail().toLowerCase());

                if (locale.getLanguage().toUpperCase().equals("PT")
                        && !this.novaPessoa.getCEP().isEmpty() && this.novaPessoa.getCEP().contains(".") && this.novaPessoa.getCEP().contains("-")) {
                    try {
                        this.novaPessoa.setCEP(this.novaPessoa.getCEP().substring(0, 2)
                                + this.novaPessoa.getCEP().substring(3, 6)
                                + this.novaPessoa.getCEP().substring(7, 10));
                    } catch (Exception e) {
                        throw new Exception("CEPInvalido");
                    }
                }

                if (!this.novaPessoa.getFone1().equals("")) {
                    this.novaPessoa.setFone1(this.novaPessoa.getFone1().substring(1, 3)
                            + this.novaPessoa.getFone1().substring(5));
                }

                if (!this.novaPessoa.getFone2().equals("")) {
                    this.novaPessoa.setFone2(this.novaPessoa.getFone2().substring(1, 3)
                            + this.novaPessoa.getFone2().substring(5));
                }

                if (this.novaPessoa.getAltura() != null || this.novaPessoa.getPeso() != null) {
                    if (verificaVirgula(this.novaPessoa.getAltura().toString()) == true) {
                        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("AlturaSemVirgula"), null);
                        FacesContext.getCurrentInstance().addMessage(null, message);
                    } else if (verificaVirgula(this.novaPessoa.getPeso().toString()) == true) {
                        FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("PessoaSemVirgula"), null);
                        FacesContext.getCurrentInstance().addMessage(null, message);
                    }
                }

                this.novaPessoa = (Pessoa) FuncoesString.removeAcentoObjeto(this.novaPessoa);
                this.novaPessoa.setCodPessoaWEB(this.pessoasatmobweb.gerarCodPessoaWeb(this.novaPessoa, this.persistenciaLocal, this.persistenciaSat));
            }
            this.contato = new MobileContatos();
            this.buscaContatos = new ArrayList<>();
            this.buscaContatos.add(this.contato);
            PrimeFaces.current().resetInputs("formCadastrar:addContato");
            PrimeFaces.current().executeScript("PF('dlgAdicionarContato').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n"
                    + "\r\n" + Logger.objeto2String(this.novaPessoa) + "\r\n";
            this.logerro.Grava(log, caminho);
        }

    }

    public List<MobileContatos> BuscarContatos(String query) {
        try {
            this.buscaContatos = this.pessoasatmobweb.BuscarContatos(query, this.persistenciaSat);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.buscaContatos;
    }

    public void AdiconarContato() {
        try {
            this.contato.setCodPessoa(this.novaPessoa.getCodPessoaWEB());
            this.contato.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.contato.setHora(DataAtual.getDataAtual("HORA"));
            this.contato.setData(DataAtual.getDataAtual("SQL"));
            this.contato.setSituacao(1);
            this.pessoasatmobweb.AdicionarContato(this.contato, this.persistenciaSat);
            this.contatos = this.pessoasatmobweb.ListaContatos(this.novaPessoa.getCodPessoaWEB(), this.persistenciaSat);
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void DeletarContato() {
        try {
            if (null == this.contato) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("SelecioneContato"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.contato.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
                this.contato.setHora(DataAtual.getDataAtual("HORA"));
                this.contato.setData(DataAtual.getDataAtual("SQL"));
                this.contato.setSituacao(0);
                this.pessoasatmobweb.DeletarContato(this.contato, this.persistenciaSat);
                this.contatos = this.pessoasatmobweb.ListaContatos(this.novaPessoa.getCodPessoaWEB(), this.persistenciaSat);
                PrimeFaces.current().ajax().update("formCadastrar:tabs:contatos");
                this.contato = null;
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS("ExclusaoSucesso"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public List<Pessoa> getListaPessoa() {
        return listaPessoa;
    }

    public void setListaPessoa(List<Pessoa> listaPessoa) {
        this.listaPessoa = listaPessoa;
    }

    public Pessoa getPessoaSelecionada() {
        return pessoaSelecionada;
    }

    public void setPessoaSelecionada(Pessoa pessoaSelecionada) {
        this.pessoaSelecionada = pessoaSelecionada;
    }

    public Pessoa getNovaPessoa() {
        return novaPessoa;
    }

    public void setNovaPessoa(Pessoa novaPessoa) {
        this.novaPessoa = novaPessoa;
    }

    public BigDecimal getMatr() {
        return matr;
    }

    public void setMatr(BigDecimal matr) {
        this.matr = matr;
    }

    public BigDecimal getCodigo() {
        return codigo;
    }

    public void setCodigo(BigDecimal codigo) {
        this.codigo = codigo;
    }

    public Boolean getEdicao() {
        return edicao;
    }

    public void setEdicao(Boolean edicao) {
        this.edicao = edicao;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Boolean getAtualizado() {
        return atualizado;
    }

    public void setAtualizado(Boolean atualizado) {
        this.atualizado = atualizado;
    }

    public Boolean getEditaSituacao() {
        return editaSituacao;
    }

    public void setEditaSituacao(Boolean editaSituacao) {
        this.editaSituacao = editaSituacao;
    }

    public Boolean getLimparFiltros() {
        return limparFiltros;
    }

    public void setLimparFiltros(Boolean limparFiltros) {
        this.limparFiltros = limparFiltros;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean iseCodigo() {
        return eCodigo;
    }

    public void seteCodigo(boolean eCodigo) {
        this.eCodigo = eCodigo;
    }

    public boolean iseNome() {
        return eNome;
    }

    public void seteNome(boolean eNome) {
        this.eNome = eNome;
    }

    public boolean iseEmail() {
        return eEmail;
    }

    public void seteEmail(boolean eEmail) {
        this.eEmail = eEmail;
    }

    public boolean iseRG() {
        return eRG;
    }

    public void seteRG(boolean eRG) {
        this.eRG = eRG;
    }

    public boolean iseOrgEmis() {
        return eOrgEmis;
    }

    public void seteOrgEmis(boolean eOrgEmis) {
        this.eOrgEmis = eOrgEmis;
    }

    public boolean iseCPF() {
        return eCPF;
    }

    public void seteCPF(boolean eCPF) {
        this.eCPF = eCPF;
    }

    public boolean iseFone1() {
        return eFone1;
    }

    public void seteFone1(boolean eFone1) {
        this.eFone1 = eFone1;
    }

    public boolean iseFone2() {
        return eFone2;
    }

    public void seteFone2(boolean eFone2) {
        this.eFone2 = eFone2;
    }

    public boolean iseEnde() {
        return eEnde;
    }

    public void seteEnde(boolean eEnde) {
        this.eEnde = eEnde;
    }

    public boolean iseBairro() {
        return eBairro;
    }

    public void seteBairro(boolean eBairro) {
        this.eBairro = eBairro;
    }

    public boolean iseCidade() {
        return eCidade;
    }

    public void seteCidade(boolean eCidade) {
        this.eCidade = eCidade;
    }

    public boolean iseUF() {
        return eUF;
    }

    public void seteUF(boolean eUF) {
        this.eUF = eUF;
    }

    public boolean iseCEP() {
        return eCEP;
    }

    public void seteCEP(boolean eCEP) {
        this.eCEP = eCEP;
    }

    public boolean iseSituacao() {
        return eSituacao;
    }

    public void seteSituacao(boolean eSituacao) {
        this.eSituacao = eSituacao;
    }

    public boolean iseObs() {
        return eObs;
    }

    public void seteObs(boolean eObs) {
        this.eObs = eObs;
    }

    public boolean iseMatr() {
        return eMatr;
    }

    public void seteMatr(boolean eMatr) {
        this.eMatr = eMatr;
    }

    public boolean iseFuncao() {
        return eFuncao;
    }

    public void seteFuncao(boolean eFuncao) {
        this.eFuncao = eFuncao;
    }

    public boolean iseSexo() {
        return eSexo;
    }

    public void seteSexo(boolean eSexo) {
        this.eSexo = eSexo;
    }

    public boolean iseAltura() {
        return eAltura;
    }

    public void seteAltura(boolean eAltura) {
        this.eAltura = eAltura;
    }

    public boolean isePeso() {
        return ePeso;
    }

    public void setePeso(boolean ePeso) {
        this.ePeso = ePeso;
    }

    public boolean iseOperador() {
        return eOperador;
    }

    public void seteOperador(boolean eOperador) {
        this.eOperador = eOperador;
    }

    public boolean iseDtAlter() {
        return eDtAlter;
    }

    public void seteDtAlter(boolean eDtAlter) {
        this.eDtAlter = eDtAlter;
    }

    public boolean iseHrAlter() {
        return eHrAlter;
    }

    public void seteHrAlter(boolean eHrAlter) {
        this.eHrAlter = eHrAlter;
    }

    public List<MobileContatos> getContatos() {
        return contatos;
    }

    public void setContatos(List<MobileContatos> contatos) {
        this.contatos = contatos;
    }

    public MobileContatos getContato() {
        return contato;
    }

    public void setContato(MobileContatos contato) {
        this.contato = null == contato ? this.contato : contato;
    }

    public List<MobileContatos> getBuscaContatos() {
        return buscaContatos;
    }

    public void setBuscaContatos(List<MobileContatos> buscaContatos) {
        this.buscaContatos = buscaContatos;
    }

    public Boolean getExtTV() {
        return extTV;
    }

    public void setExtTV(Boolean extTV) {
        this.extTV = extTV;
    }

    public Boolean getExtSPP() {
        return extSPP;
    }

    public void setExtSPP(Boolean extSPP) {
        this.extSPP = extSPP;
    }

    public Boolean getExtEscolta() {
        return extEscolta;
    }

    public void setExtEscolta(Boolean extEscolta) {
        this.extEscolta = extEscolta;
    }

    public List<Pe_Doctos> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(List<Pe_Doctos> documentos) {
        this.documentos = documentos;
    }

    public StreamedContent getDownload() {
        return download;
    }

    public void setDownload(StreamedContent download) {
        this.download = download;
    }

    public List<Pe_Cargo> getCargosPretendidos() {
        return cargosPretendidos;
    }

    public void setCargosPretendidos(List<Pe_Cargo> cargosPretendidos) {
        this.cargosPretendidos = cargosPretendidos;
    }

    public List<Cargos> getCargos() {
        return cargos;
    }

    public void setCargos(List<Cargos> cargos) {
        this.cargos = cargos;
    }

    public Cargos getCargo() {
        return cargo;
    }

    public void setCargo(Cargos cargo) {
        this.cargo = cargo;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getChavePesquisa() {
        return chavePesquisa;
    }

    public void setChavePesquisa(String chavePesquisa) {
        this.chavePesquisa = chavePesquisa;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public SasBeansCompostas.Dieta getDietaSelecionada() {
        return dietaSelecionada;
    }

    public void setDietaSelecionada(SasBeansCompostas.Dieta dietaSelecionada) {
        this.dietaSelecionada = dietaSelecionada;
    }

    public List<SasBeansCompostas.Dieta> getListaDietas() {
        return listaDietas;
    }

    public void setListaDietas(List<SasBeansCompostas.Dieta> listaDietas) {
        this.listaDietas = listaDietas;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public List<Clientes> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<Clientes> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public SasBeansCompostas.Dieta getDietaSelecionadaCadastro() {
        return dietaSelecionadaCadastro;
    }

    public void setDietaSelecionadaCadastro(SasBeansCompostas.Dieta dietaSelecionadaCadastro) {
        this.dietaSelecionadaCadastro = dietaSelecionadaCadastro;
    }

    public SasBeansCompostas.Dieta getDietaNova() {
        return dietaNova;
    }

    public void setDietaNova(SasBeansCompostas.Dieta dietaNova) {
        this.dietaNova = dietaNova;
    }

    public boolean isMostraExcluidos() {
        return mostraExcluidos;
    }

    public void setMostraExcluidos(boolean mostraExcluidos) {
        this.mostraExcluidos = mostraExcluidos;
    }

    public UsuarioSatMobWeb getPessoaLoginUsuario() {
        return pessoaLoginUsuario;
    }

    public void setPessoaLoginUsuario(UsuarioSatMobWeb pessoaLoginUsuario) {
        this.pessoaLoginUsuario = pessoaLoginUsuario;
    }

    public Map getNiveis() {
        return niveis;
    }

    public void setNiveis(Map niveis) {
        this.niveis = niveis;
    }

    public AcessoAut getAcessoAut() {
        return acessoAut;
    }

    public void setAcessoAut(AcessoAut acessoAut) {
        this.acessoAut = acessoAut;
    }

    public SegAutorizaArea getSegAutorizaArea() {
        return segAutorizaArea;
    }

    public void setSegAutorizaArea(SegAutorizaArea segAutorizaArea) {
        this.segAutorizaArea = segAutorizaArea;
    }

    public String getBase64Qr() {
        return Base64Qr;
    }

    public void setBase64Qr(String Base64Qr) {
        this.Base64Qr = Base64Qr;
    }

    public List<Pessoa> getPessoasCompleto() {
        return pessoasCompleto;
    }

    public void setPessoasCompleto(List<Pessoa> pessoasCompleto) {
        this.pessoasCompleto = pessoasCompleto;
    }

}

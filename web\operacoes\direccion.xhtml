<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="../assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                [id*="cadastroTrajeto"] .ui-autocomplete-input,
                [id*="cadastroTrajeto"] .ui-inputfield{
                    min-width:100% !important;
                    width:100% !important;
                    max-width:100% !important;
                }

                [id*="cadastroTrajeto"] div[class*="col-md"],
                [id*="formBaixaVolumes"] div[class*="col-md"]{
                    padding: 5px !important;
                }
                
                [id*="guias"] tbody tr td,
                [id*="guias"] thead tr td,
                [id*="guias"] thead tr th{
                    min-width: 150px !important;
                    text-align: center !important;
                }
                
                [id*="guias"] tbody tr td:nth-child(4),
                [id*="guias"] thead tr td:nth-child(4),
                [id*="guias"] thead tr th:nth-child(4){
                    min-width: 60px !important;
                    text-align: center !important;
                }
                
                [id*="guias"] tbody tr td:nth-child(5),
                [id*="guias"] thead tr td:nth-child(5),
                [id*="guias"] thead tr th:nth-child(5){
                    min-width: 100px !important;
                    text-align: center !important;
                }

                [id*="guias"] tbody tr td:nth-last-child(2),
                [id*="guias"] thead tr td:nth-last-child(2),
                [id*="guias"] thead tr th:nth-last-child(2),
                [id*="guias"] tbody tr td:last-child,
                [id*="guias"] thead tr td:last-child,
                [id*="guias"] thead tr th:last-child{
                    min-width: 300px !important;
                    text-align: center !important;
                }
                
                [id*="guias"] tbody tr td[colspan="18"]{
                    text-align: left !important;
                }
                
                .Fundo{
                    height: 100%;
                    background-color: #FFF;
                    border: thin solid #CCC;
                    padding:10px 10px 10px 15px !important;
                    border-radius: 4px !important;
                    border-top:4px solid #3C8DBC !important;
                    overflow:auto !important;
                }

                #main{
                    background-color:#ECF0F5 !important; 
                    padding:16px 14px 14px 14px !important; 
                    overflow:hidden !important; 
                    height: calc(100% - 0px) !important
                }

                header{
                    position:relative !important; 
                    box-shadow: -1px 2px 6px #AAA !important;
                    min-height: 10px !important;
                    line-height: 45px !important;
                    height: 45px !important;
                }

                footer{
                    min-height: 10px !important;
                    height: 10px !important;
                    bottom:0px !important;
                    margin-bottom:0px !important;
                    padding:0px !important;
                    background-color: red !important;
                }

                #footer-toggle i{
                    padding-top:12px !important;
                }

                .TextoRodape{
                    height: 10px !important;
                    margin:0px !important;
                    padding:0px !important;
                }

                .calendario input{
                    width:150px !important;
                    text-align: center !important;
                    padding-right:22px !important;
                }

                .calendario{
                    font-size:10pt !important;
                    top:-2px !important;
                }

                .TituloPagina{
                    font-size:13pt !important;
                    font-weight:bold !important;
                    line-height: 25px !important;
                    display:block;
                    position:absolute;
                    top:3px; 
                    left:40px;
                    font-family:'Open Sans', sans-serif !important;
                    color:#022B4A !important;
                }

                .TituloDataHora{
                    font-size:8pt !important;
                    font-weight:600 !important;
                    line-height: 10px !important;
                    display:block;
                    position:absolute;
                    top:28px; 
                    left:40px;
                    font-family:'Open Sans', sans-serif !important;
                    color:#404040 !important;
                }

                .FilialNome{
                    display:block;
                    font-size:12pt !important;
                    font-weight:bold !important;
                    line-height: 12px !important;
                    font-family:'Open Sans', sans-serif !important;
                    color:#022B4A !important;
                    width: 100% !important;
                    margin-top:5px !important;
                }

                .FilialEndereco,
                .FilialBairroCidade{
                    display:block;
                    font-size:8pt !important;
                    font-weight:500 !important;
                    line-height: 8px !important;
                    font-family:'Open Sans', sans-serif !important;
                    color:#404040 !important;
                    width: 100% !important;
                }

                thead tr {
                    background-color: #ecf0f5 !important;
                }

                body .ui-datatable tbody > tr.ui-widget-content, body .ui-treetable tbody > tr.ui-widget-content {

                    border-top: thin solid #dde3e7 !important;
                    border-bottom: thin solid #dde3e7 !important;
                    border: thin solid #dde3e7 !important;
                    border: thin solid #dde3e7 !important;

                }

                .box-primary {
                    border-top: 4px solid #3c8dbc !important;
                }

                .botao {
                    display: inline-block !important;
                    color: #fff !important;
                    width: 125px !important;
                    height: 40px !important;
                    padding: 0 20px !important;
                    background: #3479A3 !important;
                    border-radius: 5px !important;
                    outline: none !important;
                    border: none !important;
                    cursor: pointer !important;
                    text-align: center !important;
                    transition: all 0.2s linear !important;
                    letter-spacing: 0.05em !important;
                }

                .cadastrar{
                    padding:12px !important;
                    border:thin solid #DDD !important;
                }

                .modalBox {
                    overflow-x: auto !important;
                    overflow-y: auto !important;
                    max-width: calc(100vw - 8px)  !important;
                    max-height: calc(100vh - 40px) !important;
                }

                span.ui-autocomplete {
                    width: 100% !important;
                }

                a:hover,
                a:focus {
                    color:#3479A3 !important;
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    [id*="tabela"].DataGrid [role="columnheader"] > span {
                        top: -3px !important;
                        position: relative !important;
                    }
                }

                [id*="cadastroBaixaHorarios"] [class*="col-md"]{
                    padding: 4px !important;
                }


                .AddGuia,
                .Guia{
                    height:200px;
                    background-color:#82cc8c;
                    color:#deffe2;
                    border-radius: 10px;
                    font-size:10pt !important;
                    cursor:pointer;
                    margin-top:2px !important;
                    margin-bottom:2px !important;
                    border:thin solid #82cc8c;
                    transition: all 0.1s;
                    margin-left: 6px !important;
                    margin-right: 6px !important;
                }

                .AddGuia:hover,
                .Guia{
                    background-color:#c8fcce;
                    color: forestgreen;
                    border-color: #82cc8c;
                }

                .AddGuia i{
                    width:100%;
                    text-align:center;
                    font-size:24pt !important;
                    margin-bottom:8px;
                    cursor:pointer;
                    margin-top:48px
                }

                .AddGuia label{
                    font-weight:bold;
                    width:100%;
                    text-align:center;
                    margin:0px !important;
                    padding:0px !important;
                    cursor:pointer;
                }

                .Guia{
                    padding-left: 2px !important;
                    padding-right: 2px !important;
                }

                #divFormBaixa label,
                #divFormVolume label{
                    width:100% !important;
                    display:block;
                    font-size:8pt !important;
                    font-weight:bold !important;
                    color:dimgray !important;
                    margin:0px 0px -2px 0px !important;
                }

                #divFormBaixa [class*="col-md"],
                #divFormVolume [class*="col-md"]{
                    padding:4px !important;
                }

                .jconfirm-content-pane{
                    height:auto !important;
                }

                .jconfirm-box-container{
                    margin-top:-120px !important;
                }

                .jconfirm-title{
                    color:#000 !important;
                }

                #divFormBaixa input,
                #divFormBaixa select,
                #divFormVolume input,
                #divFormVolume select{
                    border-top:none !important;
                    border-left:none !important;
                    border-right:none !important;
                    box-shadow:none !important;
                    outline:none !important;
                    padding-left:4px !important;
                }

                .ExcluirGuia{
                    position:absolute;
                    right:-5px;
                    top:-8px;
                    color:red;
                    cursor:pointer;
                    font-size:16pt;
                    background-color:#FFF;
                    border-radius:50%;
                }

                .TituloGuia{
                    font-size:10pt; 
                    font-weight:bold !important;
                    width:100%;
                    text-align:center; 
                    padding:3px 0px 3px 0px !important; 
                    border-bottom: thin solid #669F6F !important;  
                    border-top: thin solid #669F6F !important;  
                    color: #FFF;
                    background-color: #82cc8c;
                    border-radius: 4px;
                    padding:0px !important;
                    margin: 2px 0px 0px 0px !important;
                }

                .Titulo{
                    font-size:8pt; 
                    font-weight:bold !important;
                    width: 100%;
                    text-align: left;
                    margin: 0px;
                }

                .Valor{
                    font-size:9pt; 
                    white-space: nowrap;
                    font-weight: 500 !important;
                    width: 100%;
                    text-align: left;
                    margin: -3px 0px 0px 0px;
                    color: #000 !important;
                }

                .FundoVolumes{
                    background-color: #82cc8c;
                    border-radius:6px;
                    height:84px;
                    border:thin solid #669F6F !important; 
                    padding:0px !important;
                }

                .btAddVolumes{
                    width: 100%;
                    background-color: #000;
                    color: #FFF;
                    font-weight: bold;
                    font-size: 8pt;
                    text-align: center;
                    padding: 1px 0px 1px 0px !important;
                    border-radius: 20px;
                    cursor:pointer;
                }

                .lblVolume{
                    padding: 2px 0px 2px 2px !important;
                    text-align: left;
                    width: 100%;
                    position: relative;
                    font-size: 9pt;
                    color:#000;
                    cursor:pointer;
                }

                .lblVolume i{
                    color: red;
                    position:absolute;
                    right: 4px;
                    cursor:pointer;
                    font-size:10pt;
                    background-color: #FFF;
                    text-align: center;
                    padding:0px !important;
                }

                .lblVolume:not(:last-child){
                    border-bottom: thin solid #77BB82 !important;
                }

                [id*="cadastroEscala"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                [id*="cadastroEscala"] div[class*="col-md"]{
                    padding: 5px 5px 0px 5px !important;
                }
            </style>
        </h:head>
        <h:body id="h" style="overflow:hidden !important;">
            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewParam name="seqRota" value="#{valores.sequencia}" />
                <f:viewParam name="codfil" value="#{valores.codfil}" />
                <f:viewParam name="dataTela" value="#{valores.dataTela}" />

                <f:viewAction action="#{valores.preparaTelaListaRotas}"/>
            </f:metadata>
            <p:growl id="msgsTrajetos"/>
            <div id="body">

                <h:form id="main">    
                    <div class="Fundo" style="overflow:hidden !important;">
                        <div class="col-md-12" style="border:thin solid #DDD; border-radius:3px; box-shadow: 2px 2px 4px #CCC; height:78px; background-color:#EEE; margin-bottom:12px; padding-right:0px !important">
                            <div class="col-md-6" style="padding:10px 0px 10px 0px !important;">
                                <p:outputLabel for="slcRota" value="#{localemsgs.Rotas}" style="color:#333; text-shadow: 1px 1px #FFF" />
                                <p:selectOneMenu id="slcRota" value="#{valores.rotasTelaListaSelecionada}"
                                                 converter="omnifaces.SelectItemsConverter" 
                                                 style="width: 100%" label="">
                                    <f:selectItem noSelectionOption="true" itemLabel="#{localemsgs.Todos}" itemValue="" />
                                    <f:selectItems value="#{valores.rotasTelaLista}"
                                                   var="rotaSelecaoLista"
                                                   itemValue="#{rotaSelecaoLista}" 
                                                   itemLabel="#{localemsgs.Rota} #{rotaSelecaoLista.rota}" noSelectionValue=""
                                                   />
                                    <p:ajax partialSubmit="true" process="@this" event="itemSelect"
                                            update="tabela msgsTrajetos"
                                            listener="#{valores.carregarListaRotasTrajetos}"/>
                                </p:selectOneMenu>

                            </div>
                            <div class="col-md-6" style="padding:0px 0px 10px 0px !important;">
                                <div class="col-md-9 col-sm-9 col-xs-9" style="width:calc(100% - 150px); padding-top:10px">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}" style="color:#333; text-shadow: 1px 1px #FFF; width:100%" />
                                    <p:datePicker id="data" value="#{valores.dataTela}" readonlyInput="true"
                                                  required="true"
                                                  requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                  pattern="#{mascaras.padraoData}" styleClass="calendar" showIcon="true"
                                                  converter="conversorData" locale="#{localeController.getCurrentLocale()}">
                                        <p:ajax partialSubmit="true" process="@this" event="dateSelect"
                                                update="tabela msgsTrajetos"
                                                listener="#{valores.carregarListaRotasTrajetos}"/>
                                    </p:datePicker>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-3" style="width:150px; padding:0px !important">
                                    <div class="col-md-12" style="background-color:#FFF; padding:0px !important; height:75px; width:100%; text-align:center">
                                        <p:outputLabel for="data" value="#{localemsgs.Ferramentas}" style="color:#333; text-shadow: 1px 1px #FFF;text-align:center !important; width:calc(100% - 20px);margin-top:5px; padding-bottom:2px;background-color:#EEE; border-radius:20px; border:thin solid #DDD; padding-left: 4px !important; padding-right: 0px !important;" />

                                        <div class="col-md-4 col-sm-4 col-xs-4" style="width:45px !important; text-align:center; padding-top:3px">
                                            <p:commandLink title="#{localemsgs.Adicionar}"
                                                           action="#{valores.novoTrajeto}" update="msgsTrajetos">
                                                <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40" style="margin-left: -4px !important"/>
                                            </p:commandLink>
                                        </div>
                                        <div class="col-md-4 col-sm-4 col-xs-4" style="width:45px !important; text-align:center; padding-top:3px">
                                            <p:commandLink title="#{localemsgs.Atualizar}"
                                                           onclick="location.reload();">
                                                <p:graphicImage url="../assets/img/icone_atualizar.png" height="40" style="margin-left: -4px !important"/>
                                            </p:commandLink>
                                        </div>
                                        <div class="col-md-1" style="width:45px !important; text-align:center; display:#{valores.isDataAtual() == true?'':'none'}; padding-top:3px;">
                                            <p:commandLink title="#{localemsgs.Escala}"
                                                           action="#{valores.abrirEscalaModal}"
                                                           rendered="#{valores.isDataAtual() == true}" update="msgsTrajetos">
                                                <p:graphicImage url="../assets/img/icon_calendario_novo.png" height="40" style="margin-left: -4px !important"/>
                                            </p:commandLink>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p:panel styleClass="painelCadastro" style="padding:0px !important; border:thin solid #DDD !important;overflow:hidden !important;">                            
                            <p:dataTable id="tabela" value="#{valores.rotasTelaListaTrajetos}" sortBy="#{listaTrajetosRota.rota},#{listaTrajetosRota.hora1}"
                                         style="font-size: 12px; height:calc(100vh - 156px) !important;" var="listaTrajetosRota" rowKey="#{listaTrajetosRota.parada}_#{listaTrajetosRota.rota}"
                                         resizableColumns="false" selectionMode="single"
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         scrollHeight="100%" styleClass="tabela" class="tabela"
                                         rowStyleClass="#{listaTrajetosRota.latitude eq '' ? 'sem-localizacao' : null} 
                                         #{listaTrajetosRota.flag_Excl eq '*' ? 'excluido' : null}
                                         #{listaTrajetosRota.hrCheg ne '' ? 'horario-ok' : (listaTrajetosRota.hora1.compareTo(valores.horaAtual) ge 0 ? null 
                                           : 'atrasado')}"
                                         selection="#{valores.trajetoSelecionado}">                                 
                                <p:ajax event="rowDblselect" update="msgsTrajetos cadastroTrajeto" partialSubmit="true" process="@this"
                                        listener="#{valores.selecionarTrajeto}"/>
                                <p:column headerText="#{localemsgs.ROTA}" style="width: 80px;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.rota}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Parada}" style="width: 80px;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.parada}"  class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora1}" style="width:80px !important;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.hora1}" converter="conversorHora" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HrCheg}" style="width:80px !important;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.hrCheg}" converter="conversorHora" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.HrSaida}" style="width:80px !important;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.hrSaida}" converter="conversorHora" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.ER}" style="width:80px !important" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.ER}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.NRed}" style="white-space:nowrap !important; width:350px" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.NRed}" class="text-center" style="white-space:nowrap !important; font-size:13pt !important;" />
                                    <div style="padding:0px 8px 2px 8px !important; background-color:transparent; margin-top:0px !important;white-space:nowrap !important;">
                                        <h:outputText value="#{listaTrajetosRota.enderecoOri} / #{listaTrajetosRota.bairroOri}" 
                                                      title="#{listaTrajetosRota.enderecoOri} / #{listaTrajetosRota.bairroOri}" 
                                                      class="text-center" 
                                                      style="overflow:hidden; text-overflow:ellipsis;margin:0px !important;white-space:nowrap !important; top:0px !important; 
                                                      bottom:0px !important; max-width:100% !important; color: #7d7d7d" />
                                    </div>
                                </p:column>
                                <p:column headerText="#{localemsgs.Regiao}" style="width: 150px; " class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.regiao}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora1D}" style="width:80px !important;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.hora1D}" converter="conversorHora" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.NRedDst}" style="width:350px" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.NRedDst}" class="text-center" style="white-space:nowrap !important; font-size:13pt !important;" />
                                    <div style="padding:0px 8px 2px 8px !important; background-color:transparent; margin-top:0px !important">
                                        <h:outputText value="#{listaTrajetosRota.enderecoDst} #{listaTrajetosRota.enderecoDst eq null?'/':''} #{listaTrajetosRota.bairroDst}"
                                                      title="#{listaTrajetosRota.enderecoDst} / #{listaTrajetosRota.bairroDst}"
                                                      class="text-center"
                                                      style="overflow:hidden; text-overflow:ellipsis;margin:0px !important; top:0px !important; 
                                                      bottom:0px !important; max-width:100% !important; color: #7d7d7d" />
                                    </div>
                                </p:column>
                                <p:column headerText="#{localemsgs.DPar}" style="width: 80px; " class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.DPar}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Valor}" style="width: 80px; " class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.valor}" converter="conversormoeda" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.tipoServ}" 
                                          style="width: 80px;" class="text-center">
                                    <h:outputText value="#{listaTrajetosRota.tipoSrv}" class="text-center"/>
                                </p:column>

                                <p:column style="width: 40px;" class="text-center">
                                    <p:commandLink title="#{localemsgs.ChegadaRota}" ajax="true" update="msgsTrajetos"
                                                   actionListener="#{valores.abrirChegadaServico(listaTrajetosRota)}" rendered="#{(listaTrajetosRota.hrSaida eq null or listaTrajetosRota.hrSaida eq '') and (listaTrajetosRota.hrCheg eq '' or listaTrajetosRota.hrCheg eq null) or (listaTrajetosRota.codCli1 eq listaTrajetosRota.codCli2)}">
                                        <h:outputText styleClass="fa fa-clock-o" />
                                    </p:commandLink>                                                                                      
                                    <p:commandLink title="#{localemsgs.BaixarServico}" ajax="true" update="msgsTrajetos"
                                                   actionListener="#{valores.abrirBaixaServico(listaTrajetosRota)}" rendered="#{(listaTrajetosRota.hrSaida eq null or listaTrajetosRota.hrSaida eq '') and listaTrajetosRota.hrCheg ne '' and listaTrajetosRota.hrCheg ne null and listaTrajetosRota.codCli1 ne listaTrajetosRota.codCli2}">
                                        <h:outputText styleClass="fa fa-hand-pointer-o" />
                                    </p:commandLink>                                                                                      
                                    <p:commandLink title="#{localemsgs.ServicoBaixado}" ajax="true" update="msgsTrajetos"
                                                   actionListener="#{valores.abrirTrajetoEdicao(listaTrajetosRota)}" rendered="#{listaTrajetosRota.hrSaida ne null and listaTrajetosRota.hrSaida ne '' and (listaTrajetosRota.hrCheg ne '' or listaTrajetosRota.hrCheg ne null) and (listaTrajetosRota.codCli1 ne listaTrajetosRota.codCli2)}">
                                        <h:outputText styleClass="fa fa-check" />
                                    </p:commandLink>                                                                                      
                                </p:column>   
                                <p:column style="width: 40px;" class="text-center">
                                    <p:commandLink title="#{localemsgs.Editar}" ajax="true"
                                                   disabled="#{valores.novaRota.flag_Excl eq '*'}"
                                                   actionListener="#{valores.abrirTrajetoEdicao(listaTrajetosRota)}">
                                        <h:outputText styleClass="fa fa-edit" style="margin:0 auto;"/>
                                    </p:commandLink>                                                                                      
                                </p:column>   
                                <p:column style="width: 40px;" class="text-center">
                                    <p:commandLink title="#{localemsgs.Guias}" ajax="true" update="main:tabelaGtv msgsTrajetos impressao"
                                                   actionListener="#{valores.abrirGuias(listaTrajetosRota)}">
                                        <h:outputText styleClass="fa fa-file-text" style="margin:0 auto;"/>
                                    </p:commandLink>                                                                                      
                                </p:column>   
                                <p:column style="width: 40px;" class="text-center">
                                    <p:commandLink title="#{localemsgs.Excluir}" onclick='PF("cdu#{listaTrajetosRota.parada}").show()'>
                                        <h:outputText styleClass="fa fa-trash-o" style="margin:0 auto;"/>
                                    </p:commandLink>    

                                    <p:confirmDialog global="true" message="#{localemsgs.ConfirmarExclusao}" closable="true"  header="#{localemsgs.Atencao}" severity="alert" widgetVar="cdu#{listaTrajetosRota.parada}" showEffect="fade" hideEffect="fade" style="font-size:12pt !important" >        
                                        <p:commandButton  value="#{localemsgs.Sim}" actionListener="#{valores.excluirTrajeto(listaTrajetosRota)}"  update="@form msgsTrajetos" onclick='PF("cdu#{listaTrajetosRota.parada}").hide()' styleClass="btn btn-lg btn-success" />  
                                        <p:commandButton  value="#{localemsgs.Nao}" onclick='PF("cdu#{listaTrajetosRota.parada}").hide()' type="button" styleClass="btn btn-lg btn-danger" />   
                                    </p:confirmDialog> 

                                </p:column>   
                                <f:facet name="footer">
                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-6,ui-grid-col-4,ui-grid-col-2"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <h:outputText id="qtdClientes" value="#{localemsgs.QtdTrajetos}: #{valores.rotasTelaListaTrajetos.size()}"
                                                      style="font-size: 12px"/>

                                        <p:outputLabel for="checkboxCliente" value="#{localemsgs.ExibirExcluidos}: "
                                                       style="font-size: 12px"/>
                                        <p:selectBooleanCheckbox id="checkboxCliente"
                                                                 value="#{valores.exclFlag}" style="font-size: 12px">
                                            <p:ajax update="tabela msgsTrajetos" listener="#{valores.carregarListaRotasTrajetos}" />
                                        </p:selectBooleanCheckbox>
                                    </p:panelGrid>
                                </f:facet> 
                            </p:dataTable>    

                            <script type="text/javascript">
                                // <![CDATA[
                                $('.Fundo').css('height', ($('html').height() - 31) + 'px').css('max-height', ($('html').height() - 31) + 'px');

                                $(window).resize(function () {
                                    $('.Fundo').css('height', ($('html').height() - 31) + 'px').css('max-height', ($('html').height() - 31) + 'px');
                                });
                                // ]]>
                            </script>
                        </p:panel>

                        <p:dialog header="#{localemsgs.Guias}" styleClass="box-primary modalBox"
                                  widgetVar="dlgGuiasSelecao" minHeight="500" width="500" modal="true" appendTo="@(body)"
                                  responsive="true" dynamic="true" resizable="false">

                            <p:dataGrid id="tabelaGtv" value="#{valores.guias}"
                                        var="gtv" columns="1">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1"
                                                  onTabShow="PF('dlgGuiasSelecao').initPosition()"
                                                  onTabClose="PF('dlgGuiasSelecao').initPosition()">
                                    <p:tab>
                                        <f:facet name="title">
                                            <h:outputText value="#{localemsgs.Guia} #{gtv.guia} - #{gtv.serie}"/>

                                            <p:commandLink title="#{localemsgs.Guias}" ajax="true" update="main:tabelaGtv impressao" style="float: right"
                                                           actionListener="#{valores.abrirGuia(gtv)}">
                                                <h:outputText styleClass="fa fa-print" style="margin:0 auto;"/>
                                            </p:commandLink>             
                                        </f:facet>
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                     layout="grid" styleClass="ui-panelgrid-blank">

                                            <h:outputText value="#{localemsgs.Valor}:"/>
                                            <h:outputText value="#{gtv.valor}"
                                                          title="#{gtv.valor}" converter="conversormoeda"/>

                                            <h:outputText value="#{localemsgs.OrigemColeta}:"/>
                                            <p:column>
                                                <h:outputText value="#{gtv.agSB}" title="#{gtv.agSB}"/>
                                                <h:outputText value="#{gtv.origem}" title="#{gtv.origem}"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                                     layout="grid" styleClass="ui-panelgrid-blank">  
                                            <h:outputText value="#{localemsgs.DtColeta}: "/>
                                            <h:outputText value="#{gtv.dtColeta}" title="#{gtv.dtColeta}" converter="conversorData"/>

                                            <h:outputText value="#{localemsgs.HrColeta}: "/>
                                            <p:column>
                                                <h:outputText value="#{gtv.hrCheg}" title="#{gtv.hrCheg}" converter="conversorHora"/>
                                                <h:outputText value=" - "/>
                                                <h:outputText value="#{gtv.hrSaida}" title="#{gtv.hrSaida}" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <h:outputText value="#{localemsgs.DestinoEntrega}:"/>
                                            <p:column>
                                                <h:outputText value="#{gtv.agSBD}" title="#{gtv.agSBD}"/>
                                                <h:outputText value="#{gtv.destino}" title="#{gtv.destino}"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                                     layout="grid" styleClass="ui-panelgrid-blank">  
                                            <h:outputText value="#{localemsgs.DtEntrega}: "/>
                                            <h:outputText value="#{gtv.dtEntrega}" title="#{gtv.dtEntrega}" converter="conversorData"/>

                                            <h:outputText value="#{localemsgs.HrCheg}: "/>
                                            <p:column>
                                                <h:outputText value="#{gtv.hrCheg_E}" title="#{gtv.hrCheg_E}" converter="conversorHora"/>
                                                <h:outputText value=" - " rendered="#{gtv.hrCheg_E ne '' and gtv.hrCheg_S ne '' and gtv.hrCheg_E ne null and gtv.hrCheg_S ne null}"/>
                                                <h:outputText value="#{gtv.hrCheg_S}" title="#{gtv.hrCheg_S}" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:dataGrid>
                        </p:dialog>
                    </div>
                </h:form>

                <!-- MODAL DE BAIXA SERVIÇO -->
                <h:form id="cadastroBaixaHorarios" class="form-inline">
                    <p:dialog header="#{localemsgs.AtendimentoNaRota}" styleClass="box-primary modalBox" 
                              widgetVar="cadastroBaixaHorarios" minHeight="500" modal="true"  appendTo="@(body)"
                              id="dlgCadastrarBaixaHorarios" responsive="true" dynamic="true" resizable="false"
                              style="background-size: 800px 430px !important;">
                        <p:panel id="cadastrarBaixaHorario" style="background-color: transparent; display:inline-block; padding:8px !important" styleClass="cadastrar">
                            <div class="col-md-3">
                                <p:outputLabel for="parada" value="#{localemsgs.Parada}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.parada}" disabled="true" id="parada"
                                             style="width: 100%; background-color: #EEE;">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="HrPrg" value="#{localemsgs.HrPrg}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.hora1}" disabled="true" id="HrPrg"
                                             style="width: 100%; background-color: #EEE;" converter="conversorHora" />
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="tiposerv" value="#{localemsgs.ER}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.ER eq 'E' ? localemsgs.Entrega : localemsgs.Recolhimento}" disabled="true" id="tiposerv"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="classificacao" value="#{localemsgs.classificacao}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.tipoSrv}" disabled="true" id="classificacao"
                                             style="width: 100%; background-color: #EEE; text-transform: capitalize !important  " converter="conversorClassificacao" />
                            </div>
                            <div class="col-md-12" style="padding-bottom:0px !important">
                                <p:outputLabel for="nRed" value="#{localemsgs.Cliente}"/>
                            </div>
                            <div class="col-md-3" style="padding-top:0px !important">
                                <p:inputText value="#{valores.trajetoSelecionado.codCli1}" disabled="true" id="codCli"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-9" style="padding-top:0px !important">
                                <p:inputText value="#{valores.trajetoSelecionado.NRed}" disabled="true" id="nRed"
                                             style="width: 100%; background-color: #EEE; color:#000; border-color:orangered; background-color:lightyellow" />
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="horaChegada" value="#{localemsgs.Chegada}" />
                                <p:inputMask id="horaChegada" mask="#{mascaras.mascaraHora}" value="#{valores.trajetoSelecionado.hrCheg}" 
                                             required="true" placeholder="00:00" converter="conversorHora"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                             style="width: 100%" maxlength="4">
                                    <p:ajax process="@this" update="atraso msgsTrajetos" partialSubmit="true" 
                                            listener="#{valores.calcularAtraso}" event="blur"/>
                                </p:inputMask>
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="atraso" value="#{localemsgs.Atraso}" />
                                <p:inputText value="#{valores.trajetoSelecionado.atraso}" disabled="true" id="atraso"
                                             style="width: 100%; background-color: #EEE;" converter="conversor0" />
                            </div>

                            <div class="col-md-3" style="display:#{valores.trajetoSelecionado.hrCheg ne '' and valores.trajetoSelecionado.codCli1 eq valores.trajetoSelecionado.codCli2?'':'none'}">
                                <p:outputLabel for="horaSaida" value="#{localemsgs.Saida}" />
                                <p:inputMask id="horaSaida" mask="#{mascaras.mascaraHora}" value="#{valores.trajetoSelecionado.hrSaida}" 
                                             required="true" placeholder="00:00" converter="conversorHora"
                                             rendered="#{valores.trajetoSelecionado.hrCheg ne '' and valores.trajetoSelecionado.codCli1 eq valores.trajetoSelecionado.codCli2}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.hrSaida}"
                                             style="width: 100%" maxlength="4">
                                    <p:ajax process="@this" update="tempo msgsTrajetos" partialSubmit="true" 
                                            listener="#{valores.calcularSaida}" event="blur"/>
                                </p:inputMask>
                            </div>
                            <div class="col-md-3" style="display:#{valores.trajetoSelecionado.hrCheg ne '' and valores.trajetoSelecionado.codCli1 eq valores.trajetoSelecionado.codCli2?'':'none'}">
                                <p:outputLabel for="tempo" value="#{localemsgs.Tempo}" />
                                <p:inputText value="#{valores.trajetoSelecionado.tempoEspera}" disabled="true" id="tempo"
                                             rendered="#{valores.trajetoSelecionado.hrCheg ne '' and valores.trajetoSelecionado.codCli1 eq valores.trajetoSelecionado.codCli2}"
                                             style="width: 100%; background-color: #EEE;" converter="conversor0" />
                            </div>

                            <div class="col-md-#{valores.trajetoSelecionado.hrCheg ne '' and valores.trajetoSelecionado.codCli1 eq valores.trajetoSelecionado.codCli2?'12':'6'}" style="padding-bottom:0px !important">
                                <p:outputLabel for="data" value="#{localemsgs.DataFinal}" />
                            </div>
                            <div class="col-md-3" style="padding-top:0px !important">
                                <p:inputMask id="data" value="#{valores.trajetoSelecionado.data}" mask="99/99/9999"
                                             required="true" label="#{localemsgs.DataFinal}"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DataFinal}"
                                             style="width: 100%"
                                             maxlength="8" placeholder="00/00/0000"
                                             converter="conversorData" />
                            </div>
                            <div class="col-md-3" style="padding-top:0px !important">
                                <p:inputText value="#{valores.trajetoSelecionado.data}" disabled="true" id="data_descricao"
                                             style="width: 100%; background-color: #EEE;" converter="conversorDiaSemana2" />
                            </div>
                            <div class="col-md-12" style="text-align: right !important; padding-top:8px !important; display:#{valores.trajetoSelecionado.hrCheg ne '' and valores.trajetoSelecionado.codCli1 eq valores.trajetoSelecionado.codCli2?'none':''}">
                                <p:commandLink id="cancelarHorario" oncomplete="PF('cadastroBaixaHorarios').hide();" partialSubmit="true" process="@this"
                                               styleClass="btn btn-danger" style="margin-right:8px !important; color:#FFF !important">
                                    <i class="fa fa-ban" style="margin-right:8px !important"></i>#{localemsgs.Cancelar}
                                </p:commandLink>

                                <p:commandLink id="salvarHorario" action="#{valores.salvarBaixaServico}" oncomplete="PF('cadastroBaixaHorarios').hide();"
                                               update="msgsTrajetos" styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </p:commandLink>
                            </div>
                        </p:panel>
                        <script type="text/javascript">
                            // <![CDATA[
                            function EnviarDadosBasicos(Operador,
                                    CodPessoa,
                                    CodFil,
                                    Parametro,
                                    Sequencia,
                                    Parada,
                                    Hora1,
                                    HoraCheg,
                                    KM)
                            {
                                let arrayJson = new Array();

                                arrayJson.push({
                                    'param': Parametro,
                                    'codpessoa': CodPessoa,
                                    'operador': Operador,
                                    'sequencia': Sequencia,
                                    'codfil': CodFil,
                                    'parada': Parada,
                                    'hora1': Hora1,
                                    'hrcheg': HoraCheg,
                                    'km': KM
                                });

                                $.ajax({
                                    url: 'https://mobile.sasw.com.br/SatWebService/api/ws-rotas/confirmarchegada',
                                    method: 'POST',
                                    data: JSON.stringify(arrayJson)
                                })
                                        .done(function (response) {
                                            if (response.resp.toString() === '1') {
                                                $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.DadosSalvosSucesso}', function () {
                                                    location.reload();
                                                });
                                            } else
                                                alert('Error: ' + JSON.stringify(response));
                                        })
                                        .fail(function (fail) {
                                            alert('Error: ' + JSON.stringify(fail));
                                        });
                            }

                            // ]]>
                        </script>
                    </p:dialog>
                </h:form>

                <h:form id="formBaixaVolumes" class="form-inline">
                    <p:dialog header="#{localemsgs.BaixarServico}" styleClass="box-primary modalBox" 
                              widgetVar="dlgBaixaVolumes" minHeight="500" modal="true"  appendTo="@(body)"
                              id="dlgBaixaVolumes" responsive="true" dynamic="true" resizable="false"
                              style="background-size: 800px 430px !important;">
                        <p:panel id="cadastrarVolumeBaixa" style="background-color: transparent; display:inline-block; padding:8px !important" styleClass="cadastrar">                            
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="rota" value="#{localemsgs.Rota}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.rota}" disabled="true" id="rota"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="parada" value="#{localemsgs.Parada}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.parada}" disabled="true" id="parada"
                                             style="width: 100%; background-color: #EEE;">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="tiposerv" value="#{localemsgs.ER}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.ER eq 'E' ? localemsgs.Entrega : localemsgs.Recolhimento}" disabled="true" id="tiposerv"
                                             style="width: 100%; background-color: #EEE;" />
                                <h:inputHidden id="txtTipoEr" value="#{valores.trajetoSelecionado.ER}"></h:inputHidden>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="data" value="#{localemsgs.Data}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.data}" disabled="true" id="data"
                                             style="width: 100%; background-color: #EEE;" converter="conversorData" />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="HrPrg" value="#{localemsgs.HrPrg}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.hora1}" disabled="true" id="HrPrg"
                                             style="width: 100%; background-color: #EEE;" converter="conversorHora" />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="HrChegada" value="#{localemsgs.HrCheg}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.hrCheg}" disabled="true" id="HrChegada"
                                             style="width: 100%; background-color: #EEE;" converter="conversorHora" />
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="HrSaida" value="#{localemsgs.HrSaida}"/>
                                <p:inputMask id="HrSaida" mask="#{mascaras.mascaraHora}" value="#{valores.trajetoSelecionado.hrSaida}" 
                                             required="true" placeholder="00:00" converter="conversorHora"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                             style="width: 100%" maxlength="4">
                                </p:inputMask>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6">  
                                <p:outputLabel for="Os" value="#{localemsgs.OS}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.OS}" disabled="true" id="Os"
                                             style="width: 100%; background-color: #EEE;" converter="conversor0" />
                            </div>
                            <div class="col-md-12" style="padding-bottom:0px !important">
                                <p:outputLabel for="nRedOri" value="#{localemsgs.TrajetoExecutado}"/>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding-top: 0px !important">  
                                <p:inputText value="#{valores.trajetoSelecionado.nomeOri}" disabled="true" id="nRedOri"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding-top: 0px !important">  
                                <p:inputText value="#{valores.trajetoSelecionado.NRedDst}" disabled="true" id="nRedDst"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-12" style="padding-bottom:0px !important">
                                <p:outputLabel for="agencia" value="#{localemsgs.Agencia}"/>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-top: 0px !important">  
                                <p:inputText value="#{valores.trajetoSelecionado.DPar}" disabled="true" id="agencia"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-9 col-sm-9 col-xs-9" style="padding-top: 0px !important">  
                                <p:inputText value="#{valores.trajetoSelecionado.NRedDst}" disabled="true" id="agencisDesc"
                                             style="width: 100%; background-color: #EEE;" />
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-9" style="padding-top:0px !important">
                                <p:outputLabel for="classificacao" value="#{localemsgs.classificacao}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.tipoSrv}" disabled="true" id="classificacao"
                                             style="width: 100%; background-color: #EEE; text-transform: capitalize !important" converter="conversorClassificacao" />
                                <h:inputHidden id="classificacaoCod" value="#{valores.trajetoSelecionado.tipoSrv}"></h:inputHidden>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-top:0px !important">
                                <p:outputLabel for="seqRota" value="#{localemsgs.SeqRota}" style="width:100%" />
                                <p:inputText value="#{valores.trajetoSelecionado.sequencia}" disabled="true" id="seqRota"
                                             style="width: 100%; background-color: #EEE;" converter="conversor0" />
                            </div>

                            <div class="col-md-3 col-sm-3 col-xs-3" style="padding-top:0px !important">
                                <p:outputLabel for="kmGuia" value="#{localemsgs.KmTexto}" style="width:100%" />
                                <p:inputNumber value="#{valores.trajetoSelecionado.km}" id="kmGuia"
                                               style="width: 100%;" decimalPlaces="0" maxlength="10" />
                            </div>
                            <div id="divGuiasVolumes" class="col-md-12 col-sm-12 col-xs-12" style="padding:6px 2px 6px 2px !important; height: 216px; overflow:auto !important;">
                                <div class="col-md-2 AddGuia">
                                    <i class="fa fa-plus-circle"></i>
                                    <label>#{localemsgs.Adicionar}</label>
                                    <label>#{localemsgs.Guia}</label>
                                </div>
                            </div>

                            <div class="col-md-12" style="text-align: right !important; padding-top:8px !important">
                                <p:commandLink id="cancelarHorario" oncomplete="PF('dlgBaixaVolumes').hide();" partialSubmit="true" process="@this"
                                               styleClass="btn btn-danger" style="margin-right:8px !important; color:#FFF !important">
                                    <i class="fa fa-ban" style="margin-right:8px !important"></i>#{localemsgs.Cancelar}
                                </p:commandLink>

                                <a href="javascript:;" id="btSalvarBaixa" class="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                </a>
                            </div>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--INÍCIO MODAL DE TRAJETOS-->
                <h:form id="cadastroTrajeto" class="form-inline">
                    <p:dialog header="#{localemsgs.CadastrarTrajeto}" styleClass="box-primary modalBox" 
                              widgetVar="dlgCadastrarTrajetos" minHeight="500" modal="true"  appendTo="@(body)"
                              id="dlgCadastrarTrajetos" responsive="true" dynamic="true" resizable="false"
                              style="background-size: 800px 430px !important;">
                        <p:panel id="cadastrarTrajeto" style="background-color: transparent; display:inline-block" styleClass="cadastrar">
                            <div class="col-md-6">
                                <p:outputLabel for="filialrota" value="#{localemsgs.Filial}"/>
                                <p:selectOneMenu id="filialrota" value="#{valores.filial}" converter="omnifaces.SelectItemsConverter" 
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                 filter="true" filterMatchMode="contains" style="width: 100%" label=""
                                                 disabled="true">
                                    <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}" 
                                                   itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="rotaTrajeto" value="#{localemsgs.Rota}"/>
                                <p:inputText value="#{valores.novaRota.rota}" disabled="true" id="rotaTrajeto"
                                             style="width: 100%; background-color: #EEE;">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="parada" value="#{localemsgs.Parada}"/>
                                <p:inputText value="#{valores.trajetoSelecionado.parada}" disabled="true" id="parada"
                                             style="width: 100%; background-color: #EEE;">
                                    <f:convertNumber pattern="000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="horaRota" value="#{localemsgs.Hora}" />
                                <p:inputMask id="horaRota" mask="#{mascaras.mascaraHora}" value="#{valores.trajetoSelecionado.hora1}" 
                                             disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}"
                                             required="true" placeholder="00:00" converter="conversorHora"
                                             requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                             style="width: 105%" maxlength="4">
                                    <p:ajax process="@this" update="msgsTrajetos" partialSubmit="true" 
                                            listener="#{valores.validaHoraEntrega}" event="blur"/>
                                </p:inputMask>
                            </div>
                            <div class="col-md-3">
                                <p:outputLabel for="ER" value="#{localemsgs.Tipo}" indicateRequired="false"/>
                                <p:selectOneMenu value="#{valores.trajetoSelecionado.ER}" 
                                                 required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                 id="ER" disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Entrega}" itemValue="E"/>
                                    <f:selectItem itemLabel="#{localemsgs.Recolhimento}" itemValue="R"/>
                                    <f:selectItem itemLabel="#{localemsgs.Transbordo}" itemValue="T"/>

                                    <p:ajax partialSubmit="true" update="cadastroTrajeto:cadastrarTrajeto"
                                            oncomplete="PF('dlgCadastrarTrajetos').initPosition()"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-6">
                                <p:outputLabel for="tipoSrv" value="#{localemsgs.tipoServ}" indicateRequired="false"/>
                                <p:selectOneMenu value="#{valores.trajetoSelecionado.tipoSrv}" 
                                                 required="true"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                 id="tipoSrv" disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                    <f:selectItem itemLabel="#{localemsgs.Rotineiro}" itemValue="R"/>
                                    <f:selectItem itemLabel="#{localemsgs.Eventual}" itemValue="V"/>
                                    <f:selectItem itemLabel="#{localemsgs.Especial}" itemValue="E"/>
                                    <f:selectItem itemLabel="#{localemsgs.AssistTecnica}" itemValue="A"/>

                                    <p:ajax partialSubmit="true" process="@this"/>
                                </p:selectOneMenu>
                            </div>
                            <div class="col-md-12" style="padding-bottom:0px !important">
                                <p:outputLabel for="origem" value="#{localemsgs.Origem}"/>
                            </div>
                            <div class="col-md-3" style="padding-top:0px !important">
                                <p:inputText value="#{valores.trajetoSelecionado.codCli1}" id="origem" style="width: 100%; background-color:#EEE; text-align:center !important;" disabled="true"/>
                            </div>
                            <div class="col-md-8" style="padding-top:0px !important">
                                <p:autoComplete id="cliente1" value="#{valores.cliOri}"
                                                inputStyle="width: 100% !important" completeMethod="#{valores.listarClientes}" 
                                                var="cli" itemLabel="#{cli.NRed}" itemValue="#{cli}"
                                                scrollHeight="250" disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}">
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{valores.listaClientes}"/>
                                    <p:ajax event="itemSelect" listener="#{valores.selecionarCliOri}" 
                                            update="origem origemCodFil origemEnde"/>
                                </p:autoComplete>
                            </div>
                            <div class="col-md-1" style="padding-top:0px !important">
                                <p:inputText value="#{valores.cliOri.codFil}" id="origemCodFil" style="width: 100%; background-color:#EEE; text-align:center !important;" disabled="true">
                                    <f:convertNumber pattern="0000"/>
                                </p:inputText>
                            </div>
                            <div class="col-md-12">
                                <p:inputText value="#{valores.cliOri.ende}, #{valores.cliOri.cidade}/#{valores.cliOri.estado} - #{valores.cliOri.CEP}" id="origemEnde" style="width: 100%; background-color:#EEE" disabled="true"/>  
                            </div>

                            <p:panel id="pnlTrajeto" class="col-md-12" style="padding:0px !important;" rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER}">
                                <div class="col-md-3">
                                    <p:outputLabel for="hora1D" value="#{localemsgs.Hora}" indicateRequired="false"
                                                   rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}"/>
                                    <p:inputMask id="hora1D" mask="#{mascaras.mascaraHora}"  value="#{valores.trajetoSelecionado.hora1D}" 
                                                 disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}"
                                                 required="#{valores.trajetoSelecionado.ER ne 'E'}" 
                                                 placeholder="00:00" converter="conversorHora"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hora}"
                                                 style="width: 105%" maxlength="4"
                                                 rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}">
                                        <p:ajax process="@this" update="msgsTrajetos" partialSubmit="true" 
                                                listener="#{valores.validaHoraRecolhimento}" event="blur" />
                                        <p:ajax process="@this" update="msgsTrajetos" partialSubmit="true" 
                                                listener="#{valores.abrirTrajetosModal}" event="focus" />
                                    </p:inputMask>
                                </div>    
                                <div class="col-md-12" style="padding-bottom:0px !important">
                                    <p:outputLabel for="destino" value="#{localemsgs.Destino}"
                                                   rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}"/>
                                </div>
                                <div class="col-md-3" style="padding-top:0px !important">
                                    <p:inputText value="#{valores.trajetoSelecionado.codCli2}" id="destino" style="width: 100%; background-color:#EEE; text-align:center !important;" disabled="true"
                                                 rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}"/>
                                </div>
                                <div class="col-md-8" style="padding-top:0px !important">
                                    <p:autoComplete id="cliente2" value="#{valores.cliDst}" inputStyle="width: 100%"
                                                    rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}"
                                                    completeMethod="#{valores.listarClientesDst}" 
                                                    var="cli2" itemLabel="#{cli2.NRed}" itemValue="#{cli2}" scrollHeight="250"
                                                    disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}">
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{valores.listaClientesDestino}"/>
                                        <p:ajax event="itemSelect" listener="#{valores.selecionarCliDst}" 
                                                update="msgsTrajetos destino destinoCodFil destinoEnde"/>
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-1" style="padding-top:0px !important">
                                    <p:inputText value="#{valores.cliDst.codFil}" id="destinoCodFil" style="width: 100%; background-color:#EEE; text-align:center !important;"
                                                 rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}" disabled="true">
                                        <f:convertNumber pattern="0000"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-12">
                                    <p:inputText rendered="#{valores.trajetoSelecionado.ER == 'R' or valores.trajetoSelecionado.ER == 'ER'}"
                                                 value="#{valores.cliDst.ende}, #{valores.cliDst.cidade}/#{valores.cliDst.estado} - #{valores.cliDst.CEP}" id="destinoEnde" style="width: 100%; background-color:#EEE;" disabled="true"/>  
                                </div>
                            </p:panel> 
                            <div class="col-md-3">
                                <p:outputLabel value="#{localemsgs.Valor}" />
                                <p:inputNumber id="input2" value="#{valores.valor}" symbol="#{localemsgs.$} "
                                               decimalSeparator="#{localemsgs.decimalSeparator}"
                                               thousandSeparator="#{localemsgs.thousandSeparator}" style="width: 100%; text-align: right" />
                            </div>
                            <div class="col-md-9">
                                <p:outputLabel for="obsTrajeto1" value="#{localemsgs.Obs}" />
                                <p:inputText id="obsTrajeto1" value="#{valores.trajetoSelecionado.observ}" 
                                             style="width:100%"
                                             label="#{localemsgs.Obs}" placeholder="#{localemsgs.Obs}" maxlength="20" size="40"
                                             disabled="#{valores.trajetoSelecionado.flag_Excl eq '*'}"/> 
                            </div>   
                            <div class="col-md-12">
                                <p:commandButton action="#{valores.cadastrarTrajeto}" update="msgsTrajetos" styleClass="btn btn-primary btn-lg"
                                                 value="#{localemsgs.Salve}" rendered="#{valores.flagTrajeto eq 1}" style="width:100% !important;">
                                </p:commandButton> 

                                <p:commandButton action="#{valores.editarTrajeto}" update="msgsTrajetos main" styleClass="btn btn-primary btn-lg"
                                                 value="#{localemsgs.Salve}" rendered="#{valores.flagTrajeto eq 2}" style="width:100% !important;">
                                </p:commandButton> 
                            </div>
                            
                            <p:panel class="panelTabTabela" style="padding-left: 6px !important; padding-right: 6px !important;">
                                <label style="color:#666; font-size: 16pt; width: 100%; background-color: #EEE; border: thin solid #DDD !important; border-radius: 2px; margin: 8px 0px 0px 0px; padding: 4px 2px 4px 10px">#{localemsgs.Guias} (#{valores.guias.size()})</label>
                                
                                <p:dataTable id="guias" value="#{valores.guias}"
                                             var="guiacliente" 
                                             rowKey="#{guiacliente.guia}"
                                             styleClass="tabela DataGrid" 
                                             emptyMessage="#{localemsgs.SemRegistros}"
                                             selectionMode="single"
                                             scrollable="true" 
                                             scrollHeight="150"
                                             style="font-size: 12px; background: transparent; padding:0px !important; margin:0px !important; white-space: nowrap !important">
                                    <p:column headerText="#{localemsgs.Data}" style="white-space: nowrap !important;">
                                        <h:outputText value="#{guiacliente.data}" title="#{guiacliente.data}" converter="conversorData" />
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Pedido}" style="white-space: nowrap !important">
                                        <h:outputText value="#{guiacliente.pedido}" title="#{guiacliente.pedido}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Guia}" style="white-space: nowrap !important">
                                        <h:outputText value="#{guiacliente.guia}" title="#{guiacliente.guia}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Serie}" style="white-space: nowrap !important">
                                        <h:outputText value="#{guiacliente.serie}" title="#{guiacliente.serie}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Volume}" styleClass="celula-center" style="white-space: nowrap !important">
                                        <h:outputText value="#{guiacliente.volumes}"
                                                      title="#{guiacliente.volumes}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Lacre}" styleClass="celula-center">
                                        <h:outputText value="#{guiacliente.volume1}"
                                                      title="#{guiacliente.volume1}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Operacao}">
                                        <h:outputText value="#{guiacliente.operacao}" title="#{guiacliente.operacao}" converter="tradutor"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.NRedFat}">
                                        <h:outputText value="#{guiacliente.NRedFat}" title="#{guiacliente.NRedFat}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.CodCli1}">
                                        <h:outputText value="#{guiacliente.codCli1}" title="#{guiacliente.codCli1}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.LocalParada}">
                                        <h:outputText value="#{guiacliente.localParada}" title="#{guiacliente.localParada}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Agencia}">
                                        <h:outputText value="#{guiacliente.agenciaParada}" title="#{guiacliente.agenciaParada}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.HrCheg}">
                                        <h:outputText value="#{guiacliente.hrCheg}" title="#{guiacliente.hrCheg}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.HrSaida}">
                                        <h:outputText value="#{guiacliente.hrSaida}" title="#{guiacliente.hrSaida}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Destino}">
                                        <h:outputText value="#{guiacliente.destino}" title="#{guiacliente.destino}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Entrega}">
                                        <h:outputText value="#{guiacliente.entrega}" title="#{guiacliente.entrega}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" styleClass="celula-center">
                                        <h:outputText value="#{guiacliente.valor}"
                                                      title="#{guiacliente.valor}" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Assinatura}">
                                        <h:outputText value="#{guiacliente.assinatura}" title="#{guiacliente.assinatura}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.AssinaturaDestino}">
                                        <h:outputText value="#{guiacliente.assinaturaDestino}" title="#{guiacliente.assinaturaDestino}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panel>
                            
                            
                        </p:panel>
                    </p:dialog>

                    <p:dialog header="#{localemsgs.Trajetos}" styleClass="box-primary modalBox" 
                              widgetVar="dlgTrajetosEntrega" minHeight="500" modal="true"  appendTo="@(body)"
                              id="dlgTrajetosEntrega" responsive="true" dynamic="true" resizable="false"
                              style="background-size: 750px 430px; max-width: 700px">
                        <p:panel id="panelTrajetosEntrega" style="background-color: transparent; display:inline-block" styleClass="cadastrar">  
                            <p:dataTable id="trajetosSugeridos" value="#{valores.trajetosSugeridos}" sortBy="#{entregas.hora1}"
                                         style="font-size: 12px;" var="entregas" rowKey="#{entregas.hora1}" 
                                         styleClass="tabela" class="tabela DataGrid" resizableColumns="true"
                                         selectionMode="single"
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         selection="#{valores.trajetoSugeridoSelecionado}"> 
                                <p:ajax event="rowDblselect" listener="#{valores.selecionarParadaEntrega}" 
                                        update="cadastroTrajeto:hora1D msgsTrajetos"/>
                                <p:column headerText="#{localemsgs.Parada}" style="width: 47px; font-size: 12px;
                                          #{entregas.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}" class="text-center">
                                    <h:outputText value="#{entregas.parada}" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora1}" 
                                          style="width: 50px; #{entregas.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}" class="text-center">
                                    <h:outputText value="#{entregas.hora1}" converter="conversorHora" class="text-center"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.NRed}" style="width: 150px; 
                                          #{entregas.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}" class="text-center">
                                    <h:outputText value="#{entregas.NRed}" class="text-center"/>
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!-- MODAL DE ESCALA -->                                                                     
                <h:form id="cadastroEscala"> 
                    <p:dialog id="dlgEscala" widgetVar="dlgEscala" positionType="absolute" responsive="true" draggable="false"
                              modal="true" closable="true" resizable="false" dynamic="true" showEffect="drop" hideEffect="drop"
                              closeOnEscape="false" class="dialogoGrande" style="padding:0px !important; overflow: hidden !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgEscala').closeIcon.unbind('click');
                                //register your own
                                PF('dlgEscala').closeIcon.click(function (e) {
                                    $("#cadastroEscala\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            }; );
                        </script>
                        <div class="col-md-12" style="padding:0px !important; overflow-y: auto; overflow-x: hidden !important; max-height: 70vh;">
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgEscala').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <f:facet name="header">
                                <img src="../assets/img/icones_satmob_carroforte.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.CadastrarEscala}" style="color:black" />
                            </f:facet>
                            <p:panel id="cadastrar" style="background-color: transparent; padding:0px !important; border:none !important; margin:0px !important" styleClass="cadastrar">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <p:outputLabel for="filial" value="#{localemsgs.Filial}"  />
                                    <p:selectOneMenu id="filial" value="#{valores.filial}"
                                                     converter="omnifaces.SelectItemsConverter" 
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     filter="true" filterMatchMode="contains"
                                                     style="width: 100%" label="">
                                        <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}" 
                                                       itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}" />
                                    <p:inputMask id="data" value="#{valores.escala.data}" mask="99/99/9999"
                                                 required="true" label="#{localemsgs.Data}"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                 style="width: 100%"
                                                 maxlength="8" placeholder="00/00/0000"
                                                 converter="conversorData" disabled="true"/>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="rota" value="#{localemsgs.Rota}"  />
                                    <p:selectOneMenu id="rota" value="#{valores.rotaSelecionada}"
                                                     converter="omnifaces.SelectItemsConverter" 
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                                     style="width: 100%" label="">
                                        <f:selectItems value="#{valores.rotasSelecao}"
                                                       var="rotaSelecao"
                                                       itemValue="#{rotaSelecao}" 
                                                       itemLabel="#{rotaSelecao.rota}" noSelectionValue=""
                                                       />
                                        <p:ajax partialSubmit="true" process="@this" event="itemSelect"
                                                update="cadastroEscala:cadastrar msgsTrajetos"
                                                listener="#{valores.selecionarRotaEscala}"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="rota" value="#{localemsgs.Sequencia}"  />
                                    <p:inputText id="rotaSeq" value="#{valores.rotaSelecionada.sequencia}"
                                                 label="#{localemsgs.Rota}" style="width: 100%"
                                                 disabled="true" converter="conversor0">
                                        <p:watermark for="rotaSeq" value="#{localemsgs.Rota}"/>
                                    </p:inputText> 
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}" escape="false"/>
                                    <p:inputText id="totalhr" value="#{valores.escala.hsTot}" 
                                                 maxlength="4" style="width: 100%"
                                                 disabled="true">
                                        <f:convertNumber maxFractionDigits="2"/>
                                    </p:inputText>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}"/>
                                    <p:inputMask id="hrLargada" value="#{valores.escala.hora1}"
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true" placeholder="00:00" 
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                                 maxlength="4" style="width: 100%" >
                                        <p:ajax process="@this" update="msgsTrajetos totalhr" partialSubmit="true" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrChegada" value="#{localemsgs.a}"
                                                   style="width: 100%"/>
                                    <p:inputMask id="hrChegada" value="#{valores.escala.hora4}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true"  placeholder="00:00"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                                 maxlength="4" style="width: 100%">
                                        <p:ajax process="@this" update="msgsTrajetos totalhr" partialSubmit="true" 
                                                listener="#{valores.calculaHorasTrabalhadasEscala}" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo}"/>
                                    <p:inputMask id="hrIntIni" value="#{valores.escala.hora2}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true" placeholder="00:00" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                                 maxlength="4" >
                                        <p:ajax process="@this" update="msgsTrajetos totalhr" partialSubmit="true" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-6">
                                    <p:outputLabel for="hrIntFim" value="#{localemsgs.a}"
                                                   style="width: 100%"/>
                                    <p:inputMask id="hrIntFim" value="#{valores.escala.hora3}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 required="true"  placeholder="00:00" 
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                                 maxlength="4" style="width: 100%">
                                        <p:ajax process="@this" update="msgsTrajetos totalhr" partialSubmit="true" 
                                                listener="#{valores.calculaHorasTrabalhadasEscala}" event="blur"/>
                                    </p:inputMask>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="motorista" value="#{localemsgs.Motorista}"/>
                                    <p:inputText id="matrMotorista"
                                                 value="#{valores.motorista.matr}"
                                                 disabled="true"
                                                 style="width: 100% !important"
                                                 converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="motorista" value="#{localemsgs.Motorista}" style="opacity:0;"/>
                                    <p:autoComplete id="motorista" value="#{valores.motorista}" 
                                                    inputStyle="width: 100% !important"
                                                    completeMethod="#{valores.buscarPessoas}" 
                                                    var="motoristaSelecao"
                                                    itemLabel="#{motoristaSelecao.nome}"
                                                    itemValue="#{motoristaSelecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{valores.listaPessoa}"/>
                                        <p:column>
                                            <h:outputText value=" #{motoristaSelecao.nome}"
                                                          style="#{motoristaSelecao.funcao eq 'M'
                                                                   or chEquipeSelecao.funcao eq 'T' 
                                                                   ? null : 'color: red'}"/>
                                        </p:column>
                                        <p:ajax event="itemSelect" listener="#{valores.selecionarMotorista}" 
                                                update="matrMotorista hrMotorista motorista msgsTrajetos"/>
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrMotorista" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrMotorista" value="#{valores.escala.hrMot}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}"/>
                                    <p:inputText id="matrChEquipe" value="#{valores.chEquipe.matr}"
                                                 disabled="true" style="width: 100%"
                                                 converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}" style="opacity:0;"/>
                                    <p:autoComplete id="chEquipe" value="#{valores.chEquipe}"
                                                    inputStyle="width: 100%"
                                                    completeMethod="#{valores.buscarPessoas}" 
                                                    var="chEquipeSelecao"
                                                    itemLabel="#{chEquipeSelecao.nome}"
                                                    itemValue="#{chEquipeSelecao}"
                                                    scrollHeight="250">
                                        <p:column>
                                            <h:outputText value=" #{chEquipeSelecao.nome}"
                                                          style="#{chEquipeSelecao.funcao eq 'C'
                                                                   or chEquipeSelecao.funcao eq 'T' 
                                                                   ? null : 'color: red'}"/>
                                        </p:column>
                                        <o:converter converterId="omnifaces.ListConverter" list="#{valores.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{valores.selecionarChEquipe}" 
                                                update="matrChEquipe hrChEquipe panelInfoChEquipe chEquipe msgsTrajetos"/>
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrChEquipe" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrChEquipe" value="#{valores.escala.hrChe}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>
                                <p:panel id="panelInfoChEquipe" class="col-md-12 col-sm col-xs-12" style="background: lightyellow; border: thin solid orangered; padding:4px 10px 10px 10px !important; margin-top: 6px; margin-bottom: 6px; margin-left: 5px; width:calc(100%  - 10px) !important; display:#{valores.infoChEquipe?'':'none'}">
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="codigoChEquipe" value="#{localemsgs.CodPessoa}" rendered="#{valores.infoChEquipe}"/>
                                        <p:inputText id="codigoChEquipe" value="#{valores.chEquipe.codigo}"
                                                     converter="conversor0"
                                                     disabled="true" style="width: 100%"
                                                     rendered="#{valores.infoChEquipe}"/>
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-12">
                                        <p:outputLabel for="senhaChEquipe" value="#{localemsgs.SenhaMobile}" rendered="#{valores.infoChEquipe}"/>
                                        <p:inputText id="senhaChEquipe"
                                                     value="#{valores.chEquipe.PWWeb}"
                                                     rendered="#{valores.infoChEquipe}"
                                                     disabled="true" style="width: 100%"/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-12">
                                        <p:outputLabel for="permissaoChEquipe" value="#{localemsgs.PermissaoRotas}" rendered="#{valores.infoChEquipe}"/>
                                        <p:inputText id="permissaoChEquipe" value="#{valores.permissaoRota ? text.OK :  text.Nao}" rendered="#{valores.infoChEquipe}"
                                                     disabled="true" style="width: 100%; #{valores.permissaoRota ? 'color:green' : 'color:red'}"/>
                                    </div>
                                </p:panel>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}"/>
                                    <p:inputText id="matrVigilante1" value="#{valores.vigilante1.matr}"
                                                 disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}" style="opacity:0;"/>
                                    <p:autoComplete id="vigilante1" value="#{valores.vigilante1}"
                                                    inputStyle="width: 100%"
                                                    completeMethod="#{valores.buscarPessoas}" 
                                                    var="vigilante1Selecao"
                                                    itemLabel="#{vigilante1Selecao.nome}"
                                                    itemValue="#{vigilante1Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{valores.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{valores.selecionarVigilante1}" 
                                                update="matrVigilante1 hrVigilante1 msgsTrajetos"/>
                                        <p:watermark for="vigilante1" value="#{localemsgs.Vigilante1}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante1" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante1" value="#{valores.escala.hrVig1}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>



                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}"/>
                                    <p:inputText id="matrVigilante2" value="#{valores.vigilante2.matr}"
                                                 disabled="true" style="width: 100%"
                                                 converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}" style="opacity:0;"/>
                                    <p:autoComplete id="vigilante2" value="#{valores.vigilante2}"
                                                    inputStyle="width: 100%"
                                                    completeMethod="#{valores.buscarPessoas}" 
                                                    var="vigilante2Selecao" itemLabel="#{vigilante2Selecao.nome}"
                                                    itemValue="#{vigilante2Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{valores.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{valores.selecionarVigilante2}" 
                                                update="matrVigilante2 hrVigilante2 msgsTrajetos"/>
                                        <p:watermark for="vigilante2" value="#{localemsgs.Vigilante2}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante2" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante2" value="#{valores.escala.hrVig2}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}"/>
                                    <p:inputText id="matrVigilante3" value="#{valores.vigilante3.matr}"
                                                 disabled="true" style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}" style="opacity:0;"/>
                                    <p:autoComplete id="vigilante3" value="#{valores.vigilante3}"
                                                    inputStyle="width: 100%"
                                                    completeMethod="#{valores.buscarPessoas}" 
                                                    var="vigilante3Selecao" itemLabel="#{vigilante3Selecao.nome}"
                                                    itemValue="#{vigilante3Selecao}"
                                                    scrollHeight="250">
                                        <o:converter converterId="omnifaces.ListConverter" list="#{valores.listaPessoa}"/>
                                        <p:ajax event="itemSelect" listener="#{valores.selecionarVigilante3}" 
                                                update="matrVigilante3 hrVigilante3 msgsTrajetos"/>
                                        <p:watermark for="vigilante3" value="#{localemsgs.Vigilante3}" />
                                    </p:autoComplete>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="hrVigilante3" value="#{localemsgs.Hora}"/>
                                    <p:inputMask id="hrVigilante3" value="#{valores.escala.hrVig3}" 
                                                 mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                 placeholder="00:00" style="width: 100%"
                                                 maxlength="4" />
                                </div>


                                <div class="col-md-3 col-sm-3 col-xs-12">
                                    <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}"/>
                                    <p:inputText id="numeroVeiculo" value="#{valores.veiculo.numero}" disabled="true" 
                                                 style="width: 100%" converter="conversor0"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}" style="opacity:0;"/>
                                    <p:selectOneMenu id="veiculo" value="#{valores.veiculo}" converter="omnifaces.SelectItemsConverter" 
                                                     filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                        <f:selectItems value="#{valores.veiculos}" var="veiculoSelecao" itemValue="#{veiculoSelecao}" 
                                                       itemLabel="#{veiculoSelecao}" noSelectionValue=""/>
                                        <p:ajax event="itemSelect" update="numeroVeiculo msgVeiculo msgsTrajetos"
                                                process="@this" partialSubmit="true"
                                                listener="#{valores.selecionarVeiculo}"/>
                                    </p:selectOneMenu>
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-12" style="padding-top: 22px !important">
                                    <h:outputText id="msgVeiculo" value="#{valores.msgVeiculo}"/>
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: left; padding-top: 8px !important;">
                                    <p:selectBooleanCheckbox value="#{valores.infoChEquipe}" id="senhaMobile"
                                                             style="width: 30px; text-align: left">
                                        <p:ajax update="panelInfoChEquipe" partialSubmit="true" process="@this"
                                                event="change" oncomplete="PF('dlgEscala').initPosition()"/>
                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel for="senhaMobile" value="#{localemsgs.senhaMobile}" style="color: orangered" />
                                </div>
                                <div class="col-md-6 col-sm-6 col-xs-12" style="text-align: right">

                                    <p:commandLink
                                        rendered="#{valores.flagEscala eq 1}"
                                        update="main:tabela msgsTrajetos"
                                        actionListener="#{valores.cadastrarEscala(true)}"
                                        title="#{localemsgs.Cadastrar}" styleClass="btn btn-primary" style="color:#FFF !important">
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>

                                    <p:commandLink
                                        rendered="#{valores.flagEscala eq 2}"
                                        id="edit"
                                        actionListener="#{valores.editarEscala(true)}"
                                        update=":msgsTrajetos main:tabela cadastrar"
                                        styleClass="btn btn-primary" style="color:#FFF !important">
                                        <f:viewAction action="#{rotasescala.ListarData}" />
                                        <i class="fa fa-save" style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </div>
                    </p:dialog>
                </h:form>


                <h:form id="impressao">
                    <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" class="box-primary modalBox">
                        <f:facet name="header">
                            <h:outputText value="#{localemsgs.Guia}" style="float: left"/>

                            <p:commandLink title="#{localemsgs.Imprimir}"
                                           style="position: absolute; width:10px !important; right: 70px;">
                                <i class="fa fa-print"></i>
                                <p:printer target="guiaimpressa"/>
                            </p:commandLink>

                            <p:commandLink title="#{localemsgs.Download}" update="msgsTrajetos" ajax="false"
                                           actionListener="#{valores.gerarGuiaDownload}"
                                           style="position: absolute; width:10px !important; right: 100px;">
                                <i class="fa fa-download"></i>
                                <p:fileDownload value="#{valores.arquivoDownload}"/>
                            </p:commandLink>

                        </f:facet>

                        <p:panel class="guiaimpressa" styleClass="guiaimpressa"
                                 style="padding:12px !important; border:thin solid #DDD !important; overflow:auto !important;max-height: 400px !important">
                            <h:outputText id="guiaimpressa"
                                          value="#{valores.html}" escape="false"/>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <script type="text/javascript">
                    // <![CDATA[
                    var $RefList, $RelVolume;
                    var RefParamento = '#{login.pp.empresa}';
                    var RefCodPessoa = '#{valores.codpessoa}';
                    var RefOperador = '#{valores.operador}';
                    var RefCodFil = '#{valores.codfil}';

                    function EnviarGuias()
                    {
                        let arrayJson = new Array();
                        let arrayJsonGuias = new Array();
                        let arrayJsonVolumes = new Array();

                        $('#divGuiasVolumes .Guia').each(function () {
                            $Guia = $(this);
                            arrayJsonVolumes = new Array();

                            $Guia.find('.lblVolume').each(function () {
                                $Volume = $(this);

                                arrayJsonVolumes.push({
                                    'lacre': $Volume.attr('lacre'),
                                    'qtvolumes': '1',
                                    'valoresLacres': TratarValorEnvio($Volume.attr('valor')),
                                    'observacaoLacres': $Volume.attr('obs'),
                                    'tipoLacres': $Volume.attr('celula_moeda')
                                });
                            });

                            arrayJsonGuias.push({
                                'guia': $Guia.find('.TituloGuia').text().split('-')[0],
                                'moeda': $Guia.find('[ref="TipoMoeda"]').text(),
                                'serie': $Guia.find('.TituloGuia').text().split('-')[1],
                                'valor': TratarValorEnvio($Guia.find('[ref="ValorTotal"]').text()),
                                'rpv': $Guia.find('[ref="Rpv"]').text(),
                                'volumes': arrayJsonVolumes
                            });
                        });

                        arrayJson.push({
                            'param': RefParamento,
                            'codpessoa': RefCodPessoa,
                            'operador': RefOperador.split(' ')[0],
                            'sequencia': $('[id*="formBaixaVolumes"]').find('[id*="seqRota"]').val(),
                            'codfil': RefCodFil,
                            'parada': $('[id*="formBaixaVolumes"]').find('[id*="parada"]').val(),
                            'er': $('[id*="formBaixaVolumes"]').find('[id*="txtTipoEr"]').val(),
                            'tiposerv': $('[id*="formBaixaVolumes"]').find('[id*="classificacaoCod"]').val(),
                            'hora1': $('[id*="formBaixaVolumes"]').find('[id*="HrPrg"]').val(),
                            'hrcheg': $('[id*="formBaixaVolumes"]').find('[id*="HrChegada"]').val(),
                            'hrsaidavei': '',
                            'hrsaida': $('[id*="formBaixaVolumes"]').find('[id*="HrSaida"]').val(),
                            'km': $('[id*="formBaixaVolumes"]').find('[id*="kmGuia_hinput"]').val(),
                            'obs': '',
                            'guias': arrayJsonGuias
                        });

                        $.ajax({
                            url: 'https://mobile.sasw.com.br/SatWebService/api/ws-rotas/baixaservico',
                            method: 'POST',
                            data: JSON.stringify(arrayJson[0])
                        })
                                .done(function (response) {
                                    if (response.resp.toString() === '1') {
                                        $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.DadosSalvosSucesso}', function () {
                                            location.reload();
                                        });
                                    } else
                                        alert('Error: ' + JSON.stringify(response));
                                })
                                .fail(function (fail) {
                                    alert('Error: ' + JSON.stringify(fail));
                                });
                    }

                    function GerarGuiaAutomatica() {
                        let SeqRota = $('[id*="formBaixaVolumes"]').find('[id*="seqRota"]').val();
                        let Parada = $('[id*="formBaixaVolumes"]').find('[id*="parada"]').val();
                        let Ordem = eval($('#divGuiasVolumes').find('.Guia').length) + 1;
                        let Lpad = 0;

                        Lpad = (6 - SeqRota.length);
                        for (I = 0; I < eval(Lpad); I++)
                            SeqRota = '0' + SeqRota;

                        Lpad = (4 - Parada.length);
                        for (I = 0; I < eval(Lpad); I++)
                            Parada = '0' + Parada;

                        Lpad = (2 - Ordem.toString().length);
                        for (I = 0; I < eval(Lpad); I++)
                            Ordem = '0' + Ordem;

                        return (SeqRota + Parada + Ordem);
                    }

                    function CalcularTotalGuia($obj) {
                        let ValorFloat = 0.0;

                        $obj.find('.lblVolume').each(function () {
                            if ($(this).attr('valor').toString().split(',')[1].length == 2)
                                ValorFloat += parseFloat(ReplaceAll(ReplaceAll($(this).attr('valor').toString(), '.', ''), ',', '.'));
                            else
                                ValorFloat += parseFloat(ReplaceAll($(this).attr('valor').toString(), ',', ''));
                        });

                        var ValorTotal = parseFloat(ValorFloat);

                        if ($obj.find('[ref="TipoMoeda"]').text() === 'BRL')
                            $obj.find('[ref="ValorTotal"]').text(ValorTotal.formatMoney(2, "", ".", ","));
                        else
                            $obj.find('[ref="ValorTotal"]').text(ValorTotal.formatMoney(2, "", ",", "."));
                    }

                    function TratarValorEnvio(Valor) {
                        let ValorFloat = 0.0;

                        if (Valor.toString().split(',')[1].length == 2)
                            ValorFloat = parseFloat(ReplaceAll(ReplaceAll(Valor.toString(), '.', ''), ',', '.'));
                        else
                            ValorFloat = parseFloat(ReplaceAll(Valor.toString(), ',', ''));

                        let Retorno = ValorFloat.formatMoney(2, "", ",", ".");

                        return ReplaceAll(Retorno, ',', '');
                    }

                    function FormularioVolumes() {
                        let HTML = '';

                        HTML += '<div id="divFormVolume" style="width:100% !important; display:block !important">';
                        HTML += '   <div class="col-md-12 col-sm-12 col-xs-12">';
                        HTML += '      <label>#{localemsgs.Lacre}</label>';
                        HTML += '      <input id="txtVolLacre" type="tel" class="form-control" south-required="S" maxlength="9" />';
                        HTML += '   </div>';
                        HTML += '   <div class="col-md-5 col-sm-5 col-xs-12">';
                        HTML += '      <label>#{localemsgs.Valor}</label>';
                        HTML += '      <input id="txtVolValor" type="tel" class="form-control" south-type="monetario" south-required="S" />';
                        HTML += '   </div>';
                        HTML += '   <div class="col-md-7 col-sm-7 col-xs-12">';
                        HTML += '      <label>#{localemsgs.TipoMoeda}</label>';
                        HTML += '      <select id="cboCelulaMoeda" class="form-control" south-required="S">';
                        HTML += '         <option value="-1">#{localemsgs.Selecione}</option>';
                        HTML += '         <option value="1" selected="selected">#{localemsgs.Cedulas}</option>';
                        HTML += '         <option value="3">#{localemsgs.Moedas}</option>';
                        HTML += '         <option value="2">#{localemsgs.Cheques}</option>';
                        HTML += '         <option value="4">#{localemsgs.MetaisPreciosos}</option>';
                        HTML += '         <option value="5">#{localemsgs.MoedaExtrangeira}</option>';
                        HTML += '         <option value="9">#{localemsgs.OutrosDesc}</option>';
                        HTML += '      </select>';
                        HTML += '   </div>';
                        HTML += '   <div class="col-md-12 col-sm-12 col-xs-12">';
                        HTML += '      <label>#{localemsgs.Obs}</label>';
                        HTML += '      <input id="txtVolObs" type="text" class="form-control" />';
                        HTML += '   </div>';
                        HTML += '</div>';

                        return HTML;
                    }

                    $(document)
                            .on('click', '.ExcluirGuia', function () {
                                $(this).parent('div').remove();
                            })
                            .on('click', '.lblVolume i', function () {
                                $obj = $(this).parent('label').parent('div').parent('.FundoVolumes').parent('.Guia');
                                $(this).parent('label').remove();
                                CalcularTotalGuia($obj);
                            })
                            .on('click', '.lblVolume', function () {
                                $RefList = $(this).parent('div');
                                $objCalc = $(this).parent('div').parent('.FundoVolumes').parent('.Guia');
                                $RelVolume = $(this);

                                setTimeout(function () {
                                    if ($RelVolume.find('i').attr('class').indexOf('minus') > -1) {
                                        let HTML = FormularioVolumes();

                                        $.ModalDialogCallBack('large',
                                                'fa fa-plus fa-lg',
                                                '#{localemsgs.Volume}',
                                                HTML,
                                                'blue',
                                                'fa fa-save',
                                                '#{localemsgs.Salve}',
                                                '#{localemsgs.Cancelar}',
                                                function () {
                                                    if ($.ValidaForm($('#divFormVolume'), true, '#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}')) {
                                                        $RelVolume.attr('lacre', $('#txtVolLacre').val());
                                                        $RelVolume.attr('celula_moeda', $('#cboCelulaMoeda').val());
                                                        $RelVolume.attr('valor', $('#txtVolValor').val());
                                                        $RelVolume.attr('obs', $('#txtVolObs').val());
                                                        $RelVolume.html($('#txtVolLacre').val() + '<i class="fa fa-minus-square" title="#{localemsgs.ExcluirVolume}"></i>');

                                                        CalcularTotalGuia($objCalc);

                                                        $JanelaForm.close();
                                                    }
                                                },
                                                function () {
                                                    $('#txtVolLacre').val($RelVolume.attr('lacre'));
                                                    $('#cboCelulaMoeda').val($RelVolume.attr('celula_moeda'));
                                                    $('#txtVolValor').val($RelVolume.attr('valor'));
                                                    $('#txtVolObs').val($RelVolume.attr('obs'));

                                                    $('#txtVolLacre').focus();
                                                },
                                                '#{localeController.number}');
                                    }
                                }, 300);
                            })
                            .on('click', '.btAddVolumes', function () {
                                $RefList = $(this).parent('div').find('[ref="List"]');
                                $RelVolume = null;

                                let HTML = FormularioVolumes();

                                $.ModalDialogCallBack('large',
                                        'fa fa-plus fa-lg',
                                        '#{localemsgs.Volume}',
                                        HTML,
                                        'blue',
                                        'fa fa-save',
                                        '#{localemsgs.Salve}',
                                        '#{localemsgs.Cancelar}',
                                        function () {
                                            if ($.ValidaForm($('#divFormVolume'), true, '#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}')) {
                                                let HTML = '';

                                                HTML += '<label class="lblVolume" lacre="' + $('#txtVolLacre').val() + '"';
                                                HTML += '                  celula_moeda="' + $('#cboCelulaMoeda').val() + '"';
                                                HTML += '                         valor="' + $('#txtVolValor').val() + '"';
                                                HTML += '                           obs="' + $('#txtVolObs').val() + '" title="#{localemsgs.CliqueParaDetalhes}" style="margin: 0px !important">' + $('#txtVolLacre').val() + '<i class="fa fa-minus-square" title="#{localemsgs.ExcluirVolume}"></i></label>';

                                                $RefList.append(HTML);

                                                CalcularTotalGuia($RefList.parent('.FundoVolumes').parent('.Guia'));

                                                $JanelaForm.close();
                                            }
                                        },
                                        function () {
                                            $('#txtVolLacre').focus();
                                        },
                                        '#{localeController.number}');
                            })
                            .on('click', '.AddGuia', function () {
                                let HTML = '';

                                HTML += '<div id="divFormBaixa" style="width:100% !important; display:block !important">';
                                HTML += '   <div class="col-md-12 col-sm-12 col-xs-12">';
                                HTML += '      <label>#{localemsgs.TipoGuia}</label>';
                                HTML += '      <select id="cboRPV" class="form-control" south-required="S">';
                                HTML += '         <option value="-1">#{localemsgs.Selecione}</option>';
                                HTML += '         <option value="1" descricao="#{localemsgs.GuiaEletronica}">#{localemsgs.GuiaEletronica}</option>';
                                HTML += '         <option value="0" descricao="#{localemsgs.GuiaManual}">#{localemsgs.GuiaManual}</option>';
                                HTML += '      </select>';
                                HTML += '   </div>';
                                HTML += '   <div class="col-md-9 col-sm-9 col-xs-8">';
                                HTML += '      <label>#{localemsgs.Guia}</label>';
                                HTML += '      <input id="txtVolGuia" type="tel" class="form-control" south-required="S" maxlength="9" />';
                                HTML += '   </div>';
                                HTML += '   <div class="col-md-3 col-sm-3 col-xs-4">';
                                HTML += '      <label>#{localemsgs.Serie}</label>';
                                HTML += '      <input id="txtVolSerie" type="tel" class="form-control" south-required="S" maxlength="2" />';
                                HTML += '   </div>';
                                HTML += '  <div class="col-md-5 col-sm-5 col-xs-12">';
                                HTML += '      <label>#{localemsgs.Valor}</label>';
                                HTML += '      <input id="txtVolValor" type="tel" class="form-control" south-type="monetario" south-required="S" />';
                                HTML += '  </div>';
                                HTML += '   <div class="col-md-7 col-sm-7 col-xs-12">';
                                HTML += '      <label>#{localemsgs.Moeda}</label>';
                                HTML += '      <select id="cboMoeda" class="form-control" south-required="S">';
                                HTML += '         <option value="-1">#{localemsgs.Selecione}</option>';
                                HTML += '         <option value="BRL" descricao="BRL">(BRL) #{localemsgs.MoedaReal}</option>';
                                HTML += '         <option value="USD" descricao="USD">(US$) #{localemsgs.MoedaDolar}</option>';
                                HTML += '         <option value="MXN" descricao="MXN">(MXN) #{localemsgs.MoedaMexico}</option>';
                                HTML += '         <option value="EUR" descricao="EUR">(EUR) #{localemsgs.MoedaEuro}</option>';
                                HTML += '         <option value="GBP" descricao="GBP">(GBP) #{localemsgs.MoedaBritaniva}</option>';
                                HTML += '         <option value="EUR" descricao="CLP">(CLP) #{localemsgs.MoedaPesoChileno}</option>';
                                HTML += '         <option value="EUR" descricao="COB">(COB) #{localemsgs.MoedaPesoColombiano}</option>';
                                HTML += '      </select>';
                                HTML += '   </div>';
                                HTML += '</div>';

                                $.ModalDialogCallBack('large',
                                        'fa fa-plus fa-lg',
                                        '#{localemsgs.Guia}',
                                        HTML,
                                        'green',
                                        'fa fa-save',
                                        '#{localemsgs.Salve}',
                                        '#{localemsgs.Cancelar}',
                                        function () {
                                            if ($.ValidaForm($('#divFormBaixa'), true, '#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}')) {
                                                let HTML = '';

                                                HTML += '<div class="col-md-3 Guia">';
                                                HTML += '  <i class="fa fa-minus-circle ExcluirGuia" title="#{localemsgs.ExcluirGuia}"></i>';
                                                HTML += '  <label class="TituloGuia">' + $('#txtVolGuia').val() + '-' + $('#txtVolSerie').val() + '</label>';
                                                HTML += '  <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; border-bottom: thin dashed #82cc8c !important;">';
                                                HTML += '      <div class="col-md-12 col-sm-12 col-xs-12" style="padding:1px !important">';
                                                HTML += '          <label class="Titulo">#{localemsgs.Valor}</label>';
                                                HTML += '          <label class="Valor" ref="ValorTotal"></label>';
                                                HTML += '      </div>';
                                                HTML += '  </div>';
                                                HTML += '  <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important;">';
                                                HTML += '      <div class="col-md-5 col-sm-5 col-xs-5" style="padding:1px !important">';
                                                HTML += '          <label class="Titulo">#{localemsgs.Moeda}</label>';
                                                HTML += '          <label class="Valor" ref="TipoMoeda">' + $('#cboMoeda').val() + '</label>';
                                                HTML += '      </div>';
                                                HTML += '      <div class="col-md-7 col-sm-7 col-xs-7" style="padding:1px !important">';
                                                HTML += '          <label class="Titulo">#{localemsgs.RPV}</label>';
                                                HTML += '          <label class="Valor" ref="Rpv">' + $('#cboRPV').val() + '</label>';
                                                HTML += '      </div>';
                                                HTML += '  </div>';
                                                HTML += '  <div class="col-md-12 FundoVolumes" style="padding:1px 2px 1px 2px !important;">';
                                                HTML += '      <label class="btAddVolumes"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;#{localemsgs.Volumes}</label>';
                                                HTML += '      <div ref="List" class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important; overflow: auto !important; height: 57px; position:relative; margin-top:-2px !important;"></div>';
                                                HTML += '  </div>';
                                                HTML += '</div>';

                                                $('#divGuiasVolumes').append(HTML);
                                                CalcularTotalGuia($('.Guia:last-child'));
                                                $('.Guia:last-child').find('[ref="ValorTotal"]').text($('#txtVolValor').val());
                                                $JanelaForm.close();
                                            }
                                        },
                                        function () {
                                            $('#txtVolGuia').focus();
                                        },
                                        '#{localeController.number}');
                            })
                            .on('click', '#btSalvarBaixa', function () {
                                let ValidarDados = true;

                                if ($('input[id*="HrSaida"]').val().trim() === '') {
                                    $('input[id*="HrSaida"]').addClass('form-control-required');
                                    ValidarDados = false;
                                }

                                if ($('input[id*="kmGuia"]').val().trim() === '') {
                                    $('input[id*="kmGuia"]').addClass('form-control-required');
                                    ValidarDados = false;
                                }

                                if (ValidarDados) {
                                    /*if ($('.Guia').length == 0)
                                        $.MsgBoxVermelhoOk('{localemsgs.Aviso}', '{localemsgs.InformeGuias}');
                                    else {*/
                                        EnviarGuias();
                                    //}
                                } else
                                    $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.PrenchaCamposObrigaatorios}');
                            })
                            .on('change', '#cboRPV', function () {
                                $('#txtVolGuia, #txtVolSerie').val('');

                                if ($(this).val() === '1') {
                                    $('#txtVolGuia').val(GerarGuiaAutomatica()).attr('disabled', 'disabled');
                                    $('#txtVolSerie').val('53').attr('disabled', 'disabled');
                                } else
                                    $('#txtVolGuia, #txtVolSerie').removeAttr('disabled');
                            })
                            ;
                    // ]]>
                </script>
            </div>
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>             
    </f:view>
</html>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Escala;
import SasBeans.Pessoa;
import SasBeans.Rotas;
import SasBeans.Veiculos;

/**
 *
 * <AUTHOR>
 */
public class CarregaEscala {

    private Escala escala;
    private Rotas rotas;
    private Pessoa pessoa;
    private Veiculos veiculos;

    public Escala getEscala() {
        return escala;
    }

    public void setEscala(Escala escala) {
        this.escala = escala;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Veiculos getVeiculos() {
        return veiculos;
    }

    public void setVeiculos(<PERSON>eiculos veiculos) {
        this.veiculos = veiculos;
    }

    public Rotas getRotas() {
        return rotas;
    }

    public void setRotas(Rotas rotas) {
        this.rotas = rotas;
    }

}

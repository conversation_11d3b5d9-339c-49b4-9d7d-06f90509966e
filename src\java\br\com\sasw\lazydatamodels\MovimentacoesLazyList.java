package br.com.sasw.lazydatamodels;

import Controller.Movimentacoes.MovimentacoesSatMobWeb;
import Dados.Persistencia;
import SasBeans.CtrOperV;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

public class MovimentacoesLazyList extends LazyDataModel<CtrOperV> {

    private static final long serialVersionUID = 1L;
    private List<CtrOperV> movimentacoes;
    private final MovimentacoesSatMobWeb movimentacoesSatMobWeb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;

    public MovimentacoesLazyList(Persistencia pst, BigDecimal codPessoa) {
        this.movimentacoesSatMobWeb = new MovimentacoesSatMobWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
    }

    @Override
    public List<CtrOperV> load(int primeiro, int linhas, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.movimentacoes = this.movimentacoesSatMobWeb.listagemPaginada(primeiro, linhas, filters, this.codPessoa, this.persistencia);

            setRowCount(this.movimentacoesSatMobWeb.contagem(filters, this.codPessoa, this.persistencia));

            setPageSize(linhas);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
            System.out.println("br.com.sasw.lazydatamodels.MovimentacoesLazyList.load()" + e.getMessage());
        }
        return this.movimentacoes;
    }

    @Override
    public Object getRowKey(CtrOperV ctrOperV) {
        return ctrOperV.getOperador();
    }

    @Override
    public CtrOperV getRowData(String operador) {
        for (CtrOperV ctrOperv : this.movimentacoes) {
            if (operador.equals(ctrOperv.getOperador())) {
                return ctrOperv;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class PedidoDN {

    private String Numero;
    private String CodFil;
    private String Codigo;
    private String Docto;
    private String Qtde;

    private String Tipo; // Se Moeda ou Cédula (para usar apenas um Model)
    private String TipoDesc; // Se Moeda ou Cédula (para usar apenas um Model)
    private String Valor;

    public void PedidoDN() {
        this.Numero = "";
        this.CodFil = "";
        this.Codigo = "";
        this.Docto = "";
        this.Qtde = "";
        this.Tipo = "";
        this.TipoDesc = "";
        this.Valor = "";
    }

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getDocto() {
        return Docto;
    }

    public void setDocto(String Docto) {
        this.Docto = Docto;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getTipoDesc() {
        return TipoDesc;
    }

    public void setTipoDesc(String TipoDesc) {
        this.TipoDesc = TipoDesc;
    }
}

<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:composite = "http://java.sun.com/jsf/composite"
      >
    <composite:interface>
        <composite:attribute name="titulo" required="true" />
        <composite:attribute name="imagem" required="true" />
        <composite:attribute name="data" required="true" />
    </composite:interface>

    <composite:implementation>
        <div
            id="#{cc.id}"
            class="col-md-4 col-sm-12 col-xs-12"
            style="align-self: center; padding-left: 4px !important; padding-top:4px !important; padding-bottom:4px !important;"
            >
            <img src="${cc.attrs.imagem}" height="40" width="40"/>

            <label class="TituloPagina" style="margin-top:4px !important;">#{cc.attrs.titulo}</label>

            <label class="TituloDataHora" style="margin-top:4px !important;">
                <h:outputText value="#{localemsgs.Dia}: "/>
                <span>
                    <h:outputText value="#{cc.attrs.data}" converter="conversorData" />
                </span>
            </label>
        </div>
    </composite:implementation>
</html>

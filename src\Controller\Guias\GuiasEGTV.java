/*
 */
package Controller.Guias;

import Dados.Persistencia;
import SasBeans.CxFGuiasVol;
import SasBeans.Rt_Guias;
import SasDaos.CxFGuiasVolDao;
import SasDaos.Rt_GuiasDao;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GuiasEGTV implements Serializable {

    public Rt_Guias obterInfoGuia(String sequencia, String parada, String guia, String serie, Persistencia persistencia) throws Exception {
        try {
//            Rt_PercDao rt_PercDao = new Rt_PercDao();
            Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
            return rt_guiasDao.infoGuia(guia, serie, persistencia);
//            boolean pedido = rt_PercDao.existePedido(sequencia, parada, persistencia);
//
//            List<Rt_Guias> guias = rt_guiasDao.listaGuias(sequencia, parada, pedido, persistencia);
//            if (guias.isEmpty()) {
//                guias = rt_guiasDao.listaGuiasEntrega(sequencia, parada, pedido, persistencia);
//            }
//
//            if (guias.isEmpty()) {
//                return null;
//            } else {
//                for (Rt_Guias g : guias) {
//                    if (g.getGuia().toBigInteger().toString().equals(guia) && g.getSerie().equals(serie)) {
//                        return g;
//                    }
//                }
//                return null;
//            }
        } catch (Exception e) {
            throw new Exception("GuiasEGTV.falhageral<message>" + e.getMessage());
        }
    }

    public Rt_Guias obterInfoGuia(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
            return rt_guiasDao.infoGuia(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasEGTV.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> listarLacres(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvoldao = new CxFGuiasVolDao();
            return cxfguiasvoldao.listaDeVolumesPreOrder(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasEGTV.falhageral<message>" + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.R2099;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class R2099Dao {

    public List<R2099> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select "
                    + " '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) ideEvento_perApur, "
                    + " Case when Filiais.TipoPessoa = 'J' then 1 else 2 end ideContri_tpInsc, "
                    + " Substring(Filiais.CNPJ,1,8) ideContri_nrInsc,   "
                    + " Pessoa.Nome ideRespInf_nmResp, "
                    + " Pessoa.CPF ideRespInf_cpfResp, "
                    + " Pessoa.Fone1 ideRespInf_telefone, "
                    + " DIRF.emailRInf ideRespInf_email, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2010'"
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtServTm,"
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2020' "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtServPr, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2030' "
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtAssDespRec, "
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2040'"
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtAssDespRep,"
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2050'"
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtComProd,"
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2060'"
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtCPRB,"
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2060'"
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtCPRB,"
                    + " (Select Case when Count(*) > 0 then 'S' else 'N' end  "
                    + " from XmlEsocial  "
                    + " where XmlEsocial.Evento = 'R-2070'"
                    + "     and XmlEsocial.compet = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + " and XmlEsocial.CodFil = Filiais.CodFil "
                    + " and Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%') infoFech_evtPgtos,"
                    + " '' infoFech_compSemMovto, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "             and z.evento = 'R-2099' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%<descRetorno>EM PROCESSAMENTO</descRetorno>%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "             and z.evento = 'R-2099' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>1%' "
                    + "                     or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) "
                    + "             and z.evento = 'R-2099' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<ideRecRetorno><ideStatus><cdRetorno>0</cdRetorno><descRetorno>SUCESSO%')) a) sucesso "
                    + " From FPPeriodos  "
                    + " Left join Filiais  on Filiais.CodFil = ?"
                    + " Left join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa on Pessoa.Codigo = DIRF.CodPessoaRIn "
                    + " Where '20'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),1,2)+'-'+Substring(Convert(Varchar,FPPeriodos.CodMovFP),3,2) = ?"
                    + " Group by FPPeriodos.CodMovFP, Filiais.TipoPessoa,Filiais.CNPJ,Pessoa.Nome,Pessoa.CPF,Pessoa.Fone1,DIRF.emailRInf, Filiais.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.select();
            List<R2099> retorno = new ArrayList<>();
            R2099 r2099;
            while (consulta.Proximo()) {
                r2099 = new R2099();

                //r2099.setEvtFechaEvPer_Id(consulta.getString("evtFechaEvPer_Id"));
                //r2099.setIdeEvento_tpAmb(consulta.getString("ideEvento_tpAmb"));
                r2099.setSucesso(consulta.getInt("sucesso"));
                r2099.setIdeEvento_procEmi("1");
                r2099.setIdeEvento_verProc("Satellite Reinf");

                r2099.setIdeEvento_perApur(consulta.getString("ideEvento_perApur"));

                r2099.setIdeContri_tpInsc(consulta.getString("ideContri_tpInsc"));
                r2099.setIdeContri_nrInsc(consulta.getString("ideContri_nrInsc"));

                r2099.setIdeRespInf_nmResp(consulta.getString("ideRespInf_nmResp"));
                r2099.setIdeRespInf_cpfResp(consulta.getString("ideRespInf_cpfResp"));
                r2099.setIdeRespInf_telefone(consulta.getString("ideRespInf_telefone"));
                r2099.setIdeRespInf_email(consulta.getString("ideRespInf_email"));

                r2099.setInfoFech_evtServTm(consulta.getString("infoFech_evtServTm"));
                r2099.setInfoFech_evtServPr(consulta.getString("infoFech_evtServPr"));
                r2099.setInfoFech_evtAssDespRec(consulta.getString("infoFech_evtAssDespRec"));
                r2099.setInfoFech_evtAssDespRep(consulta.getString("infoFech_evtAssDespRep"));
                r2099.setInfoFech_evtComProd(consulta.getString("infoFech_evtComProd"));
                r2099.setInfoFech_evtCPRB(consulta.getString("infoFech_evtCPRB"));
                r2099.setInfoFech_evtPgtos(consulta.getString("infoFech_evtPgtos"));
                r2099.setInfoFech_compSemMovto(consulta.getString("infoFech_compSemMovto"));

                retorno.add(r2099);
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("R2099Dao.get - " + e.getMessage() + "\r\n");
        }
    }
}

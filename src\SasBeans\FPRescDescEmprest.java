package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class FPRescDescEmprest {
    private BigDecimal CodFil;
    private BigDecimal CodMovFP;
    private String Matr;
    private String Verba;
    private String Banco;
    private String Contrato;
    private String Obs;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(BigDecimal CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getCodMovFP() {
        return CodMovFP;
    }

    public void setCodMovFP(BigDecimal CodMovFP) {
        this.CodMovFP = CodMovFP;
    }

    public String getVerba() {
        return Verba;
    }

    public void setVerba(String Verba) {
        this.Verba = Verba;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getMatr() {
        return Matr;
    }

    public void setMatr(String Matr) {
        this.Matr = Matr;
    }
    
}

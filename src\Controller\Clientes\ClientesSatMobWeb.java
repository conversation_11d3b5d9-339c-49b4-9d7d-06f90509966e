package Controller.Clientes;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.ClientesEmail;
import SasBeans.EmailsEnviar;
import SasBeans.Filiais;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.MovimentacaoContainer;
import SasBeans.Municipios;
import SasBeans.OS_Vig;
import SasBeans.Paramet;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaPortalSrv;
import SasBeans.RamosAtiv;
import SasBeans.Regiao;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.AcessosDao;
import SasDaos.ClientesDao;
import SasDaos.ClientesEmailDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FiliaisDao;
import SasDaos.MunicipiosDao;
import SasDaos.OS_VigDao;
import SasDaos.ParametDao;
import SasDaos.PessoaCliAutDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.PessoaPortalSrvDao;
import SasDaos.RamosAtivDao;
import SasDaos.RegiaoDao;
import SasDaos.SASGruposDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ClientesSatMobWeb {

    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    /**
     * Lista todos os grupos da empresa
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listarGrupos(Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            return sasgruposdao.listaSASGrupos(persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> listaPessoaQuery(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();
        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.listagemPessoaQuery(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    public Pessoa inserirCodPessoaWeb(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            if (local.getEmpresa().equals(central.getEmpresa())) {
                pessoa.setCodPessoaWEB(pessoa.getCodigo());
                pessoaDao.updateCodPessoaWeb(pessoa, central);
            } else {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoaExpressaCentral(pessoa, central));
                pessoaDao.updateCodPessoaWeb(pessoa, local);
            }
            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public UsuarioSatMobWeb buscarUsuario(BigDecimal codPessoaWeb, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            UsuarioSatMobWeb retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.buscarUsuario(codPessoaWeb, persistencia, satellite);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public void criarAcesso(UsuarioSatMobWeb usuario, Persistencia local, Persistencia central) throws Exception {
        try {
            usuario = (UsuarioSatMobWeb) FuncoesString.removeAcentoObjeto(usuario);
            usuario.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));

            usuario.getPessoa().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getPessoa().setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaDao pessoadao = new PessoaDao();

            if (acessosDAO.existeUsuario(usuario.getSaspw(), local)) {
                acessosDAO.editarUsuario(usuario.getSaspw(), local);
            } else {
                usuario.getSaspw().setPW(usuario.getPessoa().getPWWeb());
                acessosDAO.criarUsuario(usuario.getSaspw(), local);
            }

            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(usuario.getSaspw().getCodPessoaWeb());
            pessoa.setPWWeb(usuario.getPessoa().getPWWeb());
            pessoa.setOperador(usuario.getSaspw().getOperador());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setCodigo(usuario.getSaspw().getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);

            if (pessoalogindao.existePessoaLogin(usuario.getPessoalogin(), central)) {
                pessoalogindao.atualizaPessoaLogin(usuario.getPessoalogin(), central);
            } else {
                pessoalogindao.gravaPessoaLogin(usuario.getPessoalogin(), central);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirServicoAutomatico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            PessoaPortalSrvDao pessoaPortalSrvDao = new PessoaPortalSrvDao();
            pessoaPortalSrvDao.inserirServicoAutomatico(pessoaPortalSrv, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirFilial(SasPWFill filial, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            filial = (SasPWFill) FuncoesString.removeAcentoObjeto(filial);
            filial.setDt_Alter(getDataAtual("SQL"));
            filial.setHr_Alter(getDataAtual("HORA"));
            if (!acessosDAO.existeFilialUsuario(filial.getNome(), filial.getCodFil(), filial.getCodfilAc(), persistencia)) {
                acessosDAO.inserirFilial(filial, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            Boolean existeCliente = acessosDAO.existeCliente(cliente, persistencia);
            if (existeCliente) {
                acessosDAO.atualizaCliente(cliente, persistencia);
            } else {
                acessosDAO.inserirCliente(cliente, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<RamosAtiv> listarRamosAtiv(Persistencia persistencia) throws Exception {
        try {
            RamosAtivDao ramosAtivDao = new RamosAtivDao();
            return ramosAtivDao.listarRamosAtiv(persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Regiao> listarRegioes(String codfil, Persistencia persistencia) throws Exception {
        try {
            RegiaoDao regiaoDao = new RegiaoDao();
            return regiaoDao.listarRegioes(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa inserirNovaPessoa(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            List<Pessoa> pessoasCentral = pessoaDao.buscarPessoaCPFEmail(pessoa.getEmail(), pessoa.getCPF(), central);
            List<Pessoa> pessoasLocal = pessoaDao.buscarPessoaCPFEmail(pessoa.getEmail(), pessoa.getCPF(), local);

            String cpf = pessoa.getCPF();
            String email = pessoa.getEmail();

            // Se não achar nada nas duas bases, insere normalmente.           
            if (pessoasCentral.isEmpty() && pessoasLocal.isEmpty()) {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoa(pessoa, central));
                pessoa.setCodigo(pessoaDao.inserirPessoa(pessoa, local));
            } else {

                // Se achar email mas não achar CPF, email em uso
                for (Pessoa p : pessoasCentral) {
                    if (email.equals(p.getEmail()) && !cpf.equals(p.getCPF())) {
                        throw new Exception("EmailEmUso");
                    }
                }
                for (Pessoa p : pessoasLocal) {
                    if (email.equals(p.getEmail()) && !cpf.equals(p.getCPF())) {
                        throw new Exception("EmailEmUso");
                    }
                }

                // Se achar o CPF,
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoa(pessoa, central));
                pessoa.setCodigo(pessoaDao.inserirPessoa(pessoa, local));
            }

            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de usuarios
     *
     * @param codCli
     * @param persistencia conexão ao banco de dados
     * @param satellite
     * @return
     * @throws Exception
     */
    public List<UsuarioSatMobWeb> listagemUsuarios(String codCli, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listaUsuarios(codCli, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listaMunicipios(String query, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.BuscarMunicipio(query, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base no estado e cidade
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listaMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Grava as alterações no cadastro de clientes
     *
     * @param cliente
     * @param persistencia
     * @throws Exception
     */
    public void gravaCliente(Clientes cliente, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
            clientesdao.GravaClienteMobile(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>");

        }
    }

    public void apagarCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroCliente(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscaClientesBanco(String nome, String query, String banco, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.buscaClientesBanco(nome, query, banco, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscaClientesBanco(List<SasPWFill> filiais, String query, String banco, Persistencia persistencia)
            throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.buscaClientesBanco(filiais, query, banco, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Efetua a inserção de cliente, inseri um código automaticamente iniciando
     * em 601 0 001
     *
     * @param clientes - objeto cliente
     * @param codPessoa
     * @param arquivoLog
     * @param persistencia - conexão ao banco de dados
     * @param caminho
     * @return - retorna o mesmo objeto já com o código do cliente que foi
     * atribuido
     * @throws Exception - pode gerar exception contendo Concorrencia, quando
     * essa exception for gerada, deve-se apresentar um erro em tela e chamar o
     * comando novamente
     */
    public Clientes GravaNovoCliente(Clientes clientes, BigDecimal codPessoa, Persistencia persistencia, ArquivoLog arquivoLog, String caminho) throws Exception {
        try {
            int contador = 1;
            Clientes cliente = clientes;
            ClientesDao clientesdao = new ClientesDao();
            OS_Vig osvig;
            OS_VigDao osvigdao = new OS_VigDao();
            String codcli;

            if(null != cliente.getCEPCob() && !cliente.getCEPCob().equals("")){
                cliente.setCEPCob(cliente.getCEPCob().replace(".", "").replace("-", "").trim());
            }
            
            while (contador <= 30) {
                try {
                    codcli = clientesdao.getCodCliBancoTpCli(cliente.getCodFil(), "777", cliente.getTpCli(), persistencia);
                    cliente.setBanco(codcli.substring(0, 3));
                    cliente.setCodCli(codcli.substring(4, 7));
                    cliente.setCodigo(codcli);
                    cliente.setAgencia(codcli.substring(3, 7));

                    if (cliente.getBairro().length() > 25) {
                        cliente.setBairro(cliente.getBairro().substring(0, 25));
                    }
                    cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
                    clientesdao.inserir(cliente, persistencia);

                    if (persistencia.getEmpresa().equals("SATGETLOCK")) {
                        OS_VigDao.inserirOsReduzida(
                                cliente.getCodFil().toString(),
                                cliente.getNRed(),
                                "1",
                                "001",
                                cliente.getCodigo(), //Origem
                                cliente.getNRed(), //Origem
                                "9997001", //Destino
                                "TES CI GETLOCK", //Destino
                                cliente.getCodigo(), //Faturar 
                                cliente.getNRed(), //Faturar
                                "N",
                                "602.T001.1", //Contrato
                                "500001", //Agrupador
                                "500001",
                                "A",
                                cliente.getOper_Inc(),
                                cliente.getDt_Alter().toString(),
                                cliente.getHr_Alter(),
                                cliente.getOper_Alt(),
                                cliente.getDt_Alter().toString(),
                                cliente.getHr_Alter(),
                                "PD",
                                "",
                                "999",
                                "0001",
                                "0",
                                persistencia);
                    } else if (isTranspCacamba(persistencia.getEmpresa())) {

                        osvig = osvigdao.dadosFatCacambas(codPessoa.toString().replace(".0", ""), persistencia);

                        OS_VigDao.inserirOsReduzida(
                                cliente.getCodFil().toString(),
                                cliente.getNRed(),
                                "1",
                                "001",
                                cliente.getCodigo(), //Origem
                                cliente.getNRed(), //Origem
                                osvig.getCliDst(), //Destino
                                osvig.getNRedDst(), //Destino
                                osvig.getCliFat(), //Faturar
                                osvig.getNRedFat(), //Faturar
                                osvig.getViaCxF(),
                                osvig.getContrato(), //Contrato
                                osvig.getAgrupador(), //Agrupador
                                osvig.getOSGrp(),
                                "A",
                                cliente.getOper_Inc(),
                                cliente.getDt_Alter().toString(),
                                cliente.getHr_Alter(),
                                cliente.getOper_Alt(),
                                cliente.getDt_Alter().toString(),
                                cliente.getHr_Alter(),
                                osvig.getSitFiscal(),
                                "",
                                "999",
                                "0001",
                                "0", //Orcamento
                                persistencia);
                    }

                    return cliente;

                } catch (Exception ex) {
                    arquivoLog.Grava(this.getClass().getSimpleName() + "\r\n"
                            + Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "\r\n" + ex.getMessage() + "\r\n", caminho);
                } finally {
                    contador++;
                }
            }

            throw new Exception("clientes.concorrencia");
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Efetua a inserção de cliente, inseri um código automaticamente iniciando
     * em 601 0 001
     *
     * @param clientes - objeto cliente
     * @param persistencia - conexão ao banco de dados
     * @return - retorna o mesmo objeto já com o código do cliente que foi
     * atribuido
     * @throws Exception - pode gerar exception contendo Concorrencia, quando
     * essa exception for gerada, deve-se apresentar um erro em tela e chamar o
     * comando novamente
     */
    public Clientes GravaNovoCliente(Clientes clientes, Persistencia persistencia) throws Exception {
        try {
            int contador = 1;
            Clientes cliente = clientes;
            ClientesDao clientesdao = new ClientesDao();
            String codcli;

            while (contador <= 30) {
                try {
                    codcli = clientesdao.getCodCliMobile(cliente.getCodFil(), cliente.getTpCli(), persistencia);
                    cliente.setBanco(codcli.substring(0, 3));
                    cliente.setCodCli(codcli.substring(4, 7));
                    cliente.setCodigo(codcli);
                    cliente.setAgencia(codcli.substring(3, 7));
                    cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
                    clientesdao.inserir(cliente, persistencia);
                    return cliente;
                } catch (Exception ex) {
                } finally {
                    contador++;
                }
            }
            throw new Exception("clientes.concorrencia");
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Efetua a Listagem do cliente pelo parametro passado, deve-se informar um
     * de cada vez usado para pesquisa
     *
     * @param codfil - Código da filial
     * @param codigo - Código do cliente
     * @param nome - Nome do cliente
     * @param nred - NRed do cliente
     * @param agencia - Agência do cliente
     * @param persistencia - Conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaClientes(String codfil, String codigo, String nome, String nred, String agencia, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno;
            ClientesDao clientesdao = new ClientesDao();
            if (!"".equals(codigo)) { //codigo
                retorno = clientesdao.ListaClienteCodigo(codfil, codigo, persistencia);
            } else if (!"".equals(nome)) {//nome (razao social)
                retorno = clientesdao.ListaClienteNome(codfil, nome, persistencia);
            } else if (!"".equals(nred)) { //nred
                retorno = clientesdao.ListaClienteNred(codfil, nred, persistencia);
            } else if (!"".equals(agencia)) {//agencia
                retorno = clientesdao.ListaClienteAgencia(codfil, agencia, persistencia);
            } else { //todos do banco
                retorno = clientesdao.ListaCliente(codfil, persistencia);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Efetua a inserção de cliente, inseri um código automaticamente iniciando
     * em 601 0 001
     *
     * @param clientes - objeto cliente
     * @param persistencia - conexão ao banco de dados
     * @return - retorna o mesmo objeto já com o código do cliente que foi
     * atribuido
     * @throws Exception - pode gerar exception contendo Concorrencia, quando
     * essa exception for gerada, deve-se apresentar um erro em tela e chamar o
     * comando novamente
     */
    public Clientes gravaNovoCliente(Clientes clientes, Persistencia persistencia) throws Exception {
        try {
            int contador = 1;
            Clientes cliente = clientes;
            ClientesDao clientesdao = new ClientesDao();
            String codcli;

            while (contador <= 30) {
                try {
                    codcli = clientesdao.getCodCliMobile(cliente.getCodFil(), persistencia);
                    cliente.setBanco(codcli.substring(0, 3));
                    cliente.setTpCli(codcli.substring(3, 4));
                    cliente.setCodCli(codcli.substring(4, 7));
                    cliente.setCodigo(codcli);
                    cliente.setAgencia(codcli.substring(3, 7));
                    cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
                    clientesdao.inserir(cliente, persistencia);
                    return cliente;
                } catch (Exception ex) {
                } finally {
                    contador++;
                }
            }
            throw new Exception("ClientesSPM.concorrencia");
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Grava as alterações no cadastro de clientes
     *
     * @param cliente
     * @param persistencia
     * @throws Exception
     */
    public void GravaCliente(Clientes cliente, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
            clientesdao.atualizar(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>");

        }
    }

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> ListaMunicipios(String query, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.BuscarMunicipio(query, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base no estado e cidade
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> ListaMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> PesquisaClientes(String codfil, Clientes cliente, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.PesquisaClientesSatMob(codfil, cliente, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de clientes
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            ClientesDao clientesdao = new ClientesDao();
            retorno = clientesdao.TotalClientesMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.totalClientesMobWeb(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemPessoaCliAut(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.totalClientesMobWeb(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de clientes
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<Clientes> retorno;
            ClientesDao clientesdao = new ClientesDao();
            retorno = clientesdao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> ListagemPaginadaComOrdenacao(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, String CampoOrdenacao, Persistencia persistencia) throws Exception {

        try {
            List<Clientes> retorno;
            ClientesDao clientesdao = new ClientesDao();
            retorno = clientesdao.ListaPaginada(primeiro, linhas, filtros, codPessoa, CampoOrdenacao, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.listaPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> listagemPaginadaCliAut(BigDecimal codPessoa, int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.listaPaginadaCliAut(codPessoa, primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> listaCliFat(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.listaCliAutCliFat(codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<EmailsEnviar> listarEmails(String codFil, String codCli, String data1, String data2, String parametro, Persistencia persistencia) throws Exception {
        try {
            EmailsEnviarDao emailsEnviarDao = new EmailsEnviarDao();
            List<EmailsEnviar> retorno = emailsEnviarDao.emailsCliente(codCli, codFil, data1, data2, parametro, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public void enviarEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            EmailsEnviarDao emailsEnviarDao = new EmailsEnviarDao();
            emailsEnviarDao.reEnviarEmail(email, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public void CadastrarEmail(ClientesEmail cliEmail, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao cliEmailDao = new ClientesEmailDao();
            cliEmailDao.InserirEmail(cliEmail, persistencia);

        } catch (Exception e) {
            throw new Exception("email.falhageral<message> " + e.getMessage());
        }
    }

    public Boolean ExisteEmail(ClientesEmail cliEmail, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao cliEmailDao = new ClientesEmailDao();
            if (!cliEmailDao.existeEmail(cliEmail, persistencia)) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }

        } catch (Exception e) {
            throw new Exception("email.falhageral<message> " + e.getMessage());
        }
    }

    public List<ClientesEmail> BuscarEmail(String query, Persistencia persistencia) throws Exception {
        List<ClientesEmail> retornoEmails = new ArrayList<>();
        try {
            ClientesEmailDao clientesEmailDao = new ClientesEmailDao();
            retornoEmails = clientesEmailDao.selecionarEmail(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return retornoEmails;
    }

    public List<PessoaCliAut> listarUsuariosCadastrados(String codigo, String codfil, Persistencia persistencia) throws Exception {
        try {
            PessoaCliAutDao pessoaCliAutDao = new PessoaCliAutDao();
            return pessoaCliAutDao.listarUsuarios(codigo, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> buscarUsuarios(String query, String codigo, String codfil, Persistencia persistencia) throws Exception {
        try {
            PessoaCliAutDao pessoaCliAutDao = new PessoaCliAutDao();
            return pessoaCliAutDao.buscarUsuarios(query, codigo, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> buscarUsuariosPessoa(String query, String codigo, String codfil, Persistencia persistencia) throws Exception {
        try {
            PessoaCliAutDao pessoaCliAutDao = new PessoaCliAutDao();
            return pessoaCliAutDao.buscarUsuariosPessoa(query, codigo, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }
    
    public void inserirUsuario(PessoaCliAut usuario, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            Boolean existeCliente = acessosDAO.existeCliente(usuario, persistencia);
            if (existeCliente) {
                acessosDAO.atualizaCliente(usuario, persistencia);
            } else {
                acessosDAO.inserirCliente(usuario, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public Clientes inserirCliente(Clientes clientes, Clientes cliFat, BigDecimal codPessoa, String orcamento, Persistencia persistencia) throws Exception {
        try {
            int contador = 1;
            Clientes cliente = clientes;
            ClientesDao clientesdao = new ClientesDao();
            OS_Vig osvig;
            OS_VigDao osvigdao = new OS_VigDao();
            String codcli;

            while (contador <= 30) {
                try {
                    codcli = clientesdao.getCodCliBancoTpCli(cliente.getCodFil(), "777", cliente.getTpCli(), persistencia);
                    osvig = osvigdao.dadosFatCacambas(codPessoa.toString().replace(".0", ""), persistencia);

                    cliente.setObs(null);
                    cliente.setBanco(codcli.substring(0, 3));
                    cliente.setCodCli(codcli.substring(4, 7));
                    cliente.setCodigo(codcli);
                    cliente.setAgencia(codcli.substring(3, 7));
                    cliente.setNRed(RecortaString((RecortaAteEspaço(osvig.getNRedFat(), 0, 10) + " " + cliente.getNRed()), 0, 20));
                    cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);

                    clientesdao.inserir(cliente, persistencia);

                    OS_VigDao.inserirOsReduzida(
                            cliente.getCodFil().toString(),
                            cliente.getNRed(),
                            "1",
                            "001",
                            cliente.getCodigo(), //Origem
                            cliente.getNRed(), //Origem
                            osvig.getCliDst(), //Destino
                            osvig.getNRedDst(), //Destino
                            cliFat != null ? cliFat.getCodigo() : cliente.getCodigo(), //osvig.getCliFat(), //Faturar
                            cliFat != null ? cliFat.getNRed() : cliente.getNRed(), //osvig.getNRedFat(), //Faturar
                            osvig.getViaCxF(),
                            cliFat != null ? cliFat.getContato() : "999.O001.1", //osvig.getContrato(), //Contrato
                            cliFat != null ? cliFat.getAgencia() : "9999", //osvig.getAgrupador().toString(), //Agrupador
                            cliFat != null ? cliFat.getObs() : "9999", //osvig.getOSGrp().toString(),
                            "A",
                            cliente.getOper_Inc(),
                            cliente.getDt_Alter().toString(),
                            cliente.getHr_Alter(),
                            cliente.getOper_Alt(),
                            cliente.getDt_Alter().toString(),
                            cliente.getHr_Alter(),
                            osvig.getSitFiscal(),
                            "",
                            "999",
                            "0001",
                            orcamento,
                            persistencia);

                    return cliente;

                } catch (Exception ex) {
                } finally {
                    contador++;
                }
            }

            throw new Exception("clientes.concorrencia");
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<Municipios> listarMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    public boolean inserirRamosAtiv(RamosAtiv ramosAtiv, Persistencia persistencia) throws Exception {
        try {
            RamosAtivDao ramosAtivDao = new RamosAtivDao();
            if (ramosAtivDao.existeRamoAtiv(ramosAtiv.getCodigo(), persistencia)) {
                return false;
            }
            return ramosAtivDao.inserirRamosAtiv(ramosAtiv, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public boolean inserirRegiao(Regiao regiao, Persistencia persistencia) throws Exception {
        try {
            RegiaoDao regiaoDao = new RegiaoDao();
            if (regiaoDao.existeRegiao(regiao.getRegiao(), regiao.getCodFil(), persistencia)) {
                return false;
            }
            return regiaoDao.inserirRegiao(regiao, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    public void DeletarEmail(ClientesEmail clientesEmail, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao clientesEmailDao = new ClientesEmailDao();
            clientesEmailDao.deletarEmail(clientesEmail, persistencia);
        } catch (Exception e) {
            throw new Exception("clientes.falhageral<message>" + e.getMessage());
        }
    }

    public List<MovimentacaoContainer> listarMovimentacaoContainer(String codcli, String codFil,
            String dtInicio, String dtFim, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvolDao = new CxFGuiasVolDao();
            return cxfguiasvolDao.listarMovimentacaoContainerCliente(codcli, codFil, dtInicio, dtFim, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }
}

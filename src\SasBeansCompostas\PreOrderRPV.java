/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

/**
 *
 * <AUTHOR>
 */
public class PreOrderRPV {

    private String Lote;
    private String Sequencia;
    private String Parada;
    private String PreOrderSeq;
    private String NRedFat;
    private String EndeFat;
    private String BaiFat;
    private String CidFat;
    private String UFFat;
    private String CGCFat;
    private String NRedOri;
    private String EndeOri;
    private String BaiOri;
    private String CidOri;
    private String UFOri;
    private String NRedDst;
    private String EndeDst;
    private String BaiDst;
    private String CidDst;
    private String UFDst;
    private String Obs;
    private String OS;
    private String Qtde;
    private String Lacre;
    private String Tipo;
    private String Valor;
    private String RPV;

    public String getLote() {
        return Lote;
    }

    public void setLote(String Lote) {
        this.Lote = Lote;
    }

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getPreOrderSeq() {
        return PreOrderSeq;
    }

    public void setPreOrderSeq(String PreOrderSeq) {
        this.PreOrderSeq = PreOrderSeq;
    }

    public String getNRedFat() {
        return NRedFat;
    }

    public void setNRedFat(String NRedFat) {
        this.NRedFat = NRedFat;
    }

    public String getEndeFat() {
        return EndeFat;
    }

    public void setEndeFat(String EndeFat) {
        this.EndeFat = EndeFat;
    }

    public String getBaiFat() {
        return BaiFat;
    }

    public void setBaiFat(String BaiFat) {
        this.BaiFat = BaiFat;
    }

    public String getCidFat() {
        return CidFat;
    }

    public void setCidFat(String CidFat) {
        this.CidFat = CidFat;
    }

    public String getUFFat() {
        return UFFat;
    }

    public void setUFFat(String UFFat) {
        this.UFFat = UFFat;
    }

    public String getNRedOri() {
        return NRedOri;
    }

    public void setNRedOri(String NRedOri) {
        this.NRedOri = NRedOri;
    }

    public String getEndeOri() {
        return EndeOri;
    }

    public void setEndeOri(String EndeOri) {
        this.EndeOri = EndeOri;
    }

    public String getBaiOri() {
        return BaiOri;
    }

    public void setBaiOri(String BaiOri) {
        this.BaiOri = BaiOri;
    }

    public String getCidOri() {
        return CidOri;
    }

    public void setCidOri(String CidOri) {
        this.CidOri = CidOri;
    }

    public String getUFOri() {
        return UFOri;
    }

    public void setUFOri(String UFOri) {
        this.UFOri = UFOri;
    }

    public String getNRedDst() {
        return NRedDst;
    }

    public void setNRedDst(String NRedDst) {
        this.NRedDst = NRedDst;
    }

    public String getEndeDst() {
        return EndeDst;
    }

    public void setEndeDst(String EndeDst) {
        this.EndeDst = EndeDst;
    }

    public String getBaiDst() {
        return BaiDst;
    }

    public void setBaiDst(String BaiDst) {
        this.BaiDst = BaiDst;
    }

    public String getCidDst() {
        return CidDst;
    }

    public void setCidDst(String CidDst) {
        this.CidDst = CidDst;
    }

    public String getUFDst() {
        return UFDst;
    }

    public void setUFDst(String UFDst) {
        this.UFDst = UFDst;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getRPV() {
        return RPV;
    }

    public void setRPV(String RPV) {
        this.RPV = RPV;
    }

    public String getCGCFat() {
        return CGCFat;
    }

    public void setCGCFat(String CGCFat) {
        this.CGCFat = CGCFat;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }
}

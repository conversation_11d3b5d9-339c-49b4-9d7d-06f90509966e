package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.MobileHW;
import SasBeans.TesCofresRes;

/**
 *
 * <AUTHOR>
 */
public class TesCofresResClientes {

    private Clientes clientes;
    private TesCofresRes tescofresres;
    private MobileHW mobileHW;

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public TesCofresRes getTescofresres() {
        return tescofresres;
    }

    public void setTescofresres(TesCofresRes tescofresres) {
        this.tescofresres = tescofresres;
    }

    public MobileHW getMobileHW() {
        return mobileHW;
    }

    public void setMobileHW(MobileHW mobileHW) {
        this.mobileHW = mobileHW;
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.PessoaCliAut;
import SasBeans.PstServ;
import SasBeans.Psthstqst;
import SasBeans.RelatorioDoctos;
import SasBeans.TipoPosto;
import SasBeansCompostas.Login;
import SasBeansCompostas.PstServTipoPosto;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 */
public class PstServDao {

    public List<PstServ> listarPostosPessoa(BigDecimal codPessoa, String codFil, Persistencia persistencia) throws Exception {
        List<PstServ> clientes = new ArrayList<>();
        try {
            String vSQLLeft = "";
            
               vSQLLeft = " Left Join Pessoa on Pessoa.Codigo = "+codPessoa.toString()+" \n"
                         +" Left Join Funcion on Funcion.Matr = Pessoa.Matr \n";            
            String sql = " SELECT PstServ.* \n"
                    + " FROM PstServ \n"
                    + vSQLLeft
                    + " WHERE PstServ.Situacao = 'A' AND PstServ.codfil in (select filiais.codfil \n"
                    + "                          from saspw \n"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome \n"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac \n"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil \n"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) \n";
            if (null != codFil && !codFil.equals("0")) {
                sql = sql + " and PstServ.Codfil = ? \n";
            }
            if (!vSQLLeft.equals("")){
                sql = sql+ " and PstServ.Secao = Funcion.Secao \n"
                        + " and PstServ.CodFil = Funcion.CodFil \n";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            if (null != codFil && !codFil.equals("0")) {
                consulta.setString(codFil);
            }
            consulta.select();
            PstServ c;
            while (consulta.Proximo()) {
                c = new PstServ();
                c.setCodCli(consulta.getString("codcli"));
                c.setSecao(consulta.getString("secao"));
                c.setLocal(consulta.getString("Local"));
                c.setCodFil(consulta.getString("codfil"));
                clientes.add(c);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                    + "SELECT * "
                    + " FROM PstServ "
                    + " WHERE PstInspecao.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) ");
        }
        return clientes;
    }

    public List<PstServ> listarPostosPessoaCli(BigDecimal codPessoa, String codFil, Persistencia persistencia) throws Exception {
        List<PstServ> clientes = new ArrayList<>();
        try {                        
            String sql = "SELECT PstServ.* \n" +
                         " FROM PstServ \n" +
                         " WHERE PstServ.Situacao = 'A' \n" +
                         "   AND PstServ.CodCli in (select Codcli from PessoaCliAut where Codigo = "+codPessoa+") \n" ;
            if (null != codFil && !codFil.equals("0")) {
                sql = sql + " and PstServ.Codfil = "+codFil+" \n";
            }            
            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.select();
            PstServ c;
            while (consulta.Proximo()) {
                c = new PstServ();
                c.setCodCli(consulta.getString("codcli"));
                c.setSecao(consulta.getString("secao"));
                c.setLocal(consulta.getString("Local"));
                c.setCodFil(consulta.getString("codfil"));
                clientes.add(c);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                    + "SELECT * "
                    + " FROM PstServ "
                    + " WHERE PstInspecao.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) ");
        }
        return clientes;
    }
    
    public List<PstServ> listarPostosSaspw(BigDecimal codPessoa, String codFil, Persistencia persistencia) throws Exception {
        
        List<PstServ> clientes = new ArrayList<>();
        try {                        
            String sql = "SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END AS temAcesso "
                    + "FROM SaspwAc saspwac WHERE saspwac.Nome = (SELECT TOP 1 saspw.nome FROM Saspw saspw "
                    + "WHERE CodPessoa = " + codPessoa 
                    + " ORDER by saspw.Codigo asc) and Sistema = 40112 " ;
//            if (null != codFil && !codFil.equals("0")) {
//                sql = sql + " and saspwac.CodFil = "+codFil+" \n";
//            }            
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            String temAcesso = "";
            while (consulta.Proximo()) {
                temAcesso = consulta.getString("temAcesso");
            }
            consulta.Close();
            if(!temAcesso.isEmpty() && temAcesso.equals("1")) {
            
                String sqlPstSrv = "SELECT PstServ.* \n" +
                             " FROM PstServ \n" +
                             " WHERE PstServ.Situacao = 'A' \n";
                if (null != codFil && !codFil.equals("0")) {
                    sqlPstSrv = sqlPstSrv + " and PstServ.Codfil = "+codFil+" \n";
                }            
                consulta = new Consulta(sqlPstSrv, persistencia);
                consulta.select();

                PstServ c;
                while (consulta.Proximo()) {
                    c = new PstServ();
                    c.setCodCli(consulta.getString("codcli"));
                    c.setSecao(consulta.getString("secao"));
                    c.setLocal(consulta.getString("Local"));
                    c.setCodFil(consulta.getString("codfil"));
                    clientes.add(c);
                }
                consulta.Close();
                
                return clientes;
            } else {
                
                return listarPostosPessoaCli(codPessoa, codFil, persistencia);
            }

        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                    + "SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END AS temAcesso "
                    + "FROM SaspwAc saspwac WHERE saspwac.Nome = (SELECT TOP 1 saspw.nome FROM Saspw saspw "
                    + "WHERE CodPessoa = ?"  
                    + " ORDER by saspw.Codigo asc) and Sistema = 40112 ");
        }
    }
    
    public List<PstServ> listarPostosCompleto(String codfil, String latitude, String longitude, Persistencia persistencia) throws Exception {
        List<PstServ> clientes = new ArrayList<>();
        String sql = "";
        try {
            sql = "DECLARE @LatitudeUsuario  AS varchar(10)\n"
                    + " DECLARE @LongitudeUsuario AS varchar(10)\n"
                    + " DECLARE @CodigoFilial     AS int\n"
                    + " \n"
                    + " SET @LatitudeUsuario  = ? \n"
                    + " SET @LongitudeUsuario = ? \n"
                    + " SET @CodigoFilial     = ? \n"
                    + " \n"
                    + " SELECT \n"
                    + " CASE WHEN TAB.distanciaCalc IS NULL THEN TAB.nred + ' [ ' + ISNULL(TAB.local,'-') + ' - Posto: ' + ISNULL(TAB.posto,'-') + ' ]' ELSE TAB.distanciaCalc + ' Km >> ' + TAB.nred + ' [ ' + ISNULL(TAB.local,'-') + ' - Posto: ' + ISNULL(TAB.posto,'-') + ' ]' END AS nred,\n"
                    + " TAB.secao\n"
                    + " FROM(SELECT\n"
                    + "     clientes.latitude,\n"
                    + "     clientes.longitude,\n"
                    + "     clientes.nred,\n"
                    + "     CONVERT(VARCHAR,dbo.fun_calcdistancia(CASE WHEN @LatitudeUsuario != '0'  THEN @LatitudeUsuario  ELSE NULL END, \n"
                    + "                                           CASE WHEN @LongitudeUsuario != '0' THEN @LongitudeUsuario ELSE NULL END,\n"
                    + "                                             REPLACE(clientes.Latitude,' ',''),\n"
                    + "                                             REPLACE(clientes.Longitude,' ',''))) distanciaCalc,\n"
                    + "      pstserv.*\n"
                    + "      FROM pstserv\n"
                    + "      JOIN clientes\n"
                    + "        ON pstserv.codCli = clientes.codigo\n"
                    + "      WHERE clientes.codFil = @CodigoFilial AND pstserv.Situacao = 'A') AS TAB\n"
                    + " GROUP BY TAB.posto,\n"
                    + "          TAB.nred,\n"
                    + "          TAB.secao,\n"
                    + "          TAB.distanciaCalc,\n"
                    + "          TAB.local\n"
                    + " ORDER BY COALESCE(TAB.distanciaCalc, 9999999999999999), TAB.nred, TAB.posto";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(codfil);

            consulta.select();
            PstServ c;

            while (consulta.Proximo()) {
                c = new PstServ();
                c.setPosto(consulta.getString("nred"));
                c.setSecao(consulta.getString("secao"));
                clientes.add(c);
            }

            consulta.Close();

        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosCompleto - " + e.getMessage() + "\r\n"
                    + sql);
        }

        return clientes;
    }

    public List<PessoaCliAut> listarContatosCompleto(String codfil, String CodSecao, String latitude, String longitude, Persistencia persistencia) throws Exception {
        List<PessoaCliAut> pessoas = new ArrayList<>();
        String sql = "";
        try {
            sql = "DECLARE @LatitudeUsuario  AS varchar(10)\n"
                    + " DECLARE @LongitudeUsuario AS varchar(10)\n"
                    + " DECLARE @CodigoFilial     AS int\n"
                    + " \n"
                    + " SET @LatitudeUsuario  = ? \n"
                    + " SET @LongitudeUsuario = ? \n"
                    + " SET @CodigoFilial     = ? \n"
                    + " \n"
                    + " SELECT \n"
                    + " CASE WHEN TAB.distanciaCalc IS NULL THEN TAB.nred ELSE TAB.nred + ' [ ' + TAB.distanciaCalc + ' km ]' END AS nred,\n"
                    + " TAB.codigo\n"
                    + " FROM(SELECT\n"
                    + "      clientes.latitude,\n"
                    + "      clientes.longitude,\n"
                    + "      clientes.nred,\n"
                    + "      CONVERT(VARCHAR,dbo.fun_calcdistancia(CASE WHEN @LatitudeUsuario != '0'  THEN @LatitudeUsuario  ELSE NULL END,\n"
                    + "                                            CASE WHEN @LongitudeUsuario != '0' THEN @LongitudeUsuario ELSE NULL END,\n"
                    + "                                            clientes.latitude,\n"
                    + "                                            clientes.longitude)) distanciaCalc,\n"
                    + "      contatos.fantasia,\n"
                    + "      contatos.codigo\n"
                    + "      FROM contatos\n"
                    + "      JOIN clientes\n"
                    + "        ON contatos.codCli = clientes.codigo\n";

            if (null != CodSecao && !CodSecao.equals("0") && !CodSecao.equals("")) {
                sql += "      JOIN pstserv\n"
                        + "        ON clientes.codigo = pstserv.codCli AND pstserv.Situacao = 'A'\n";
            }

            sql += "      WHERE clientes.codFil = @CodigoFilial\n";
            if (null != CodSecao && !CodSecao.equals("0") && !CodSecao.equals("")) {
                sql += " AND pstserv.secao = ?";
            }

            sql += " ) AS TAB\n"
                    + " GROUP BY TAB.fantasia,\n"
                    + "          TAB.nred,\n"
                    + "         TAB.codigo,\n"
                    + "          TAB.distanciaCalc\n"
                    + " ORDER BY COALESCE(TAB.distanciaCalc, 9999999999999999), TAB.nred, TAB.fantasia";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(codfil);

            if (null != CodSecao && !CodSecao.equals("0") && !CodSecao.equals("")) {
                consulta.setString(CodSecao);
            }

            consulta.select();
            PessoaCliAut c;

            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setNome(consulta.getString("nred"));
                c.setCodigo(consulta.getString("codigo"));
                pessoas.add(c);
            }

            consulta.Close();

        } catch (Exception e) {
            throw new Exception("PstServDao.listarContatosCompleto - " + e.getMessage() + "\r\n"
                    + sql);
        }

        return pessoas;
    }

    public void inserirRelatorio(RelatorioDoctos relDocs, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DECLARE @QdeFotos AS int\n"
                    + " \n"
                    + " SET @QdeFotos = (SELECT (LEN(?) - LEN(REPLACE(?,';','')) + 1))"
                    + " INSERT INTO";

            if (null != relDocs.getCodigo()
                    && !relDocs.getCodigo().equals("")) {
                // CONTATO SELECIONADO >> tmktdet
                sql += " tmktdet";
            } else {
                // POSTO SELECIONADO >> tmktdetpst
                sql += " tmktdetpst";
            }
            sql += " (Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa,";
            if (null != relDocs.getCodigo()
                    && !relDocs.getCodigo().equals("")) {
                // CONTATO SELECIONADO >> tmktdet
                sql += " CodCont,";
            } else {
                // POSTO SELECIONADO >> tmktdetpst
                sql += " Secao,";
            }
            sql += " CodFil, Historico, Detalhes, QtdeFotos, Latitude, Longitude, Precisao, Operador, Dt_Alter, Hr_alter, Situacao, Fotos)";
            sql += " SELECT\n"
                    + " (ISNULL(MAX(TAB.Sequencia),0) + 1),\n"
                    + " ?,?,?,?,?,?,?,?,?,@QdeFotos,?,?,?,?,?,?,?,?";
            if (null != relDocs.getCodigo()
                    && !relDocs.getCodigo().equals("")) {
                // CONTATO SELECIONADO >> tmktdet
                sql += " FROM tmktdet AS TAB";
            } else {
                // POSTO SELECIONADO >> tmktdetpst
                sql += " FROM tmktdetpst AS TAB";
            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(relDocs.getFotos());
            consulta.setString(relDocs.getFotos());
            consulta.setString(relDocs.getAndamento());
            consulta.setString(relDocs.getData());
            consulta.setString(relDocs.getHora());
            consulta.setString(relDocs.getTipoCont());
            consulta.setBigDecimal(relDocs.getCodPessoa());
            if (null != relDocs.getCodigo()
                    && !relDocs.getCodigo().equals("")) {
                // CONTATO SELECIONADO >> tmktdet
                consulta.setString(relDocs.getCodigo());
            } else {
                // POSTO SELECIONADO >> tmktdetpst
                consulta.setString(relDocs.getSecao());
            }
            consulta.setString(relDocs.getCodFil());
            consulta.setString(relDocs.getHistorico());
            consulta.setString(relDocs.getDetalhes());
            consulta.setString(relDocs.getLatitude());
            consulta.setString(relDocs.getLongitude());
            consulta.setString(relDocs.getPrecisao());
            consulta.setString(relDocs.getOperador().split(" ")[0]);
            consulta.setString(relDocs.getDt_Alter());
            consulta.setString(relDocs.getHr_Alter());
            consulta.setString(relDocs.getSituacao());
            consulta.setString(relDocs.getFotos());

            consulta.insert();
            consulta.close();
            persistencia.FechaConexao();

        } catch (Exception e) {
            throw new Exception("PstServDao.inserirRelatorio - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<PstServ> listarPostosCliente(PessoaCliAut cliente, Boolean excluido, Persistencia persistencia) throws Exception {
        List<PstServ> clientes = new ArrayList<>();
        try {
            String sql = "Select Filiais.Descricao Empresa, PstServ.Local, PstServ.CodFil, PstServ.Secao, "
                    + " PessoaCliAut.CodCli, SasPW.CodGrupo "
                    + " from PessoaCliAut "
                    + " Left join PstServ  on PstServ.CodCli = PessoaCliAut.CodCli "
                    + "                   and PstServ.CodFil = PessoaCliAut.CodFil "
                    + " Left join Filiais   on Filiais.CodFil  = PessoaCliAut.CodFil "
                    + " Left join SasPW on SasPW.CodPessoa = PessoaCliAut.Codigo "
                    + " where PessoaCliAut.Codigo = ? ";
            if (!excluido) {
                sql = sql + " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*') ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(cliente.getCodigo());
            consulta.select();
            PstServ c;
            while (consulta.Proximo()) {
                c = new PstServ();
                c.setCodCli(consulta.getString("codcli"));
                c.setSecao(consulta.getString("secao"));
                c.setLocal(consulta.getString("Local"));
                c.setOperador(consulta.getString("empresa"));
                c.setCodFil(consulta.getString("codfil"));
                c.setCodGrupo(consulta.getString("CodGrupo"));
                clientes.add(c);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarClientes - " + e.getMessage() + "\r\n"
                    + "Select Filiais.Descricao Empresa, Clientes.Nred Cliente, Clientes.CodFil, "
                    + " PessoaCliAut.CodCli from PessoaCliAut "
                    + " Left join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli "
                    + "                   and Clientes.CodFil = PessoaCliAut.CodFil "
                    + " Left join Filiais   on Filiais.CodFil  = PessoaCliAut.CodFil "
                    + " where PessoaCliAut.Codigo = " + cliente.getCodigo()
                    + (!excluido ? " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*')" : ""));
        }
        return clientes;
    }

    /**
     * Busca informações do posto de trabalho de um funcionário pela matrícula e
     * código da filial dele.
     *
     * @param matricula
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public PstServ getPostoFuncion(String matricula, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select pstserv.* from pstserv "
                    + " left join funcion on funcion.secao = pstserv.secao "
                    + "                  and funcion.codfil = pstserv.codfil "
                    + " where funcion.matr = ? and funcion.codfil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.setString(codFil);
            consulta.select();
            PstServ pstServ = new PstServ();
            while (consulta.Proximo()) {
                pstServ.setLocal(consulta.getString("local"));
            }
            consulta.Close();
            return pstServ;
        } catch (Exception e) {
            throw new Exception("PstServDao.getPostoFuncion - " + e.getMessage() + "\r\n"
                    + " Select pstserv.* from pstserv "
                    + " lef join funcion on funcion.secao = pstserv.secao "
                    + "                  and funcion.codfil = pstserv.codfil "
                    + " where funcion.matr = " + matricula + " and funcion.codfil = " + codFil);
        }
    }

    /**
     * Busca informações do posto de servico
     *
     * @param secao
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public PstServ getPstServ(String secao, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT PstServ.*, ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, "
                    + " clientes.email, clientes.contato "
                    + " FROM PstServ "
                    + " LEFT JOIN ctritens ON ctritens.codfil = PstServ.codfil "
                    + "                 AND ctritens.contrato = PstServ.contrato "
                    + "                AND ctritens.tipoposto = PstServ.tipoposto "
                    + " LEFT JOIN clientes on clientes.codigo = pstserv.codcli  "
                    + "                   AND clientes.codfil = pstserv.codfil "
                    + " WHERE PstServ.Secao = ? AND PstServ.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codFil);
            consulta.select();
            PstServ pstserv = new PstServ();
            while (consulta.Proximo()) {
                pstserv = new PstServ();
                pstserv.setLocal(consulta.getString("local"));
                pstserv.setCodCli(consulta.getString("codcli"));
                pstserv.setDescContrato(consulta.getString("descricao"));
                pstserv.setSecao(consulta.getString("secao"));
                pstserv.setCodFil(consulta.getString("codfil"));
                /**
                 * Cliente.Contato
                 */
                pstserv.setOperador(consulta.getString("contato"));
                /**
                 * Cliente.email
                 */
                pstserv.setInterfExt(consulta.getString("email"));
            }
            consulta.Close();
            return pstserv;
        } catch (Exception e) {
            throw new Exception("PstServDao.getPstServ - " + e.getMessage() + "\r\n"
                    + " SELECT PstServ.*, ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " FROM PstServ "
                    + " LEFT JOIN ctritens ON ctritens.codfil = PstServ.codfil "
                    + "                 AND ctritens.contrato = PstServ.contrato "
                    + "                AND ctritens.tipoposto = PstServ.tipoposto "
                    + " WHERE  PstServ.Secao = " + secao + " AND PstServ.CodFil = " + codFil);
        }
    }

    /**
     *
     * @param pstServ - estrutura de dados da classe CtrOperV
     * @param persistencia - Conexão ao Banco
     */
    public void adicionaCtrOperV(PstServ pstServ, Persistencia persistencia) {
        String sql = "INSERT INTO PstServ (Secao, CodFil, Regional, Local, Situacao, Dt_Situacao, Contrato, "
                + " TipoPosto, CodCli, SupervDiu, SupervNot, Distancia, Posto, OS, HSPosto, HsFuncion, "
                + " Reforco, Ronda, Pericul, Insalub, Regiao, GRT, GrpGerencial, Operador, Dt_Alter, Hr_Alter "
                + " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstServ.getSecao());
            consulta.setBigDecimal(pstServ.getCodFil());
            consulta.setInt(pstServ.getRegional());
            consulta.setString(pstServ.getLocal());
            consulta.setString(pstServ.getSituacao());
            consulta.setString(pstServ.getDt_Situacao().toString());
            consulta.setString(pstServ.getContrato());
            consulta.setString(pstServ.getTipoPosto());
            consulta.setString(pstServ.getCodCli());
            consulta.setBigDecimal(pstServ.getSupervDiu());
            consulta.setBigDecimal(pstServ.getSupervNot());
            consulta.setBigDecimal(pstServ.getDistancia());
            consulta.setString(pstServ.getPosto());
            consulta.setBigDecimal(pstServ.getOS());
            consulta.setBigDecimal(pstServ.getHSPosto());
            consulta.setBigDecimal(pstServ.getHsFuncion());
            consulta.setString(pstServ.getReforco());
            consulta.setString(pstServ.getRonda());
            consulta.setString(pstServ.getPericul());
            consulta.setString(pstServ.getInsalub());
            consulta.setInt(pstServ.getRegiao());
            consulta.setInt(pstServ.getGRT());
            consulta.setString(pstServ.getGrpGerencial());
            consulta.setString(pstServ.getOperador());
            consulta.setString(pstServ.getDt_Alter().toString());
            consulta.setString(pstServ.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * Busca registros da tabela PstServ
     *
     * @param persistencia - Conexão ao Banco
     * @param sCodFil - Código da filial
     * @param sCodCli
     * @param sTurno - Turno desejado D- Diurno N- Noturno
     * @return - Lista de postos de serviços
     * @throws Exception
     */
    public List<PstServ> getPostos(Persistencia persistencia, String sCodFil, String sCodCli, String sTurno) throws Exception {
        String sql = "SELECT Secao, CodFil, Regional, Local, Contrato,  "
                + " TipoPosto, CodCli, Posto, OS "
                + " FROM PstServ "
                + " WHERE situacao='A' and codfil=? and CodCli=?"
                + " and tipoposto like ? ";
        PstServ pstServ;
        List<PstServ> lPstServs = new ArrayList();
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodFil);
            consult.setString(sCodCli);
            String tipoposto;
            if ("D".equals(sTurno.toUpperCase())) {
                tipoposto = "A" + '%';
            } else {
                tipoposto = "B" + '%';
            }
            consult.setString(tipoposto);
            consult.select();
            while (consult.Proximo()) {
                pstServ = new PstServ();
                pstServ.setSecao(consult.getString("Secao"));
                pstServ.setCodFil(consult.getString("CodFil"));
                pstServ.setRegional(consult.getInt("Regional"));
                pstServ.setLocal(consult.getString("Local"));
                pstServ.setContrato(consult.getString("Contrato"));
                pstServ.setTipoPosto(consult.getString("TipoPosto"));
                pstServ.setCodCli(consult.getString("CodCli"));
                pstServ.setPosto(consult.getString("Posto"));
                pstServ.setOS(consult.getString("OS"));
                lPstServs.add(pstServ);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar postos de serviços - " + e.getMessage());
        }
        return lPstServs;
    }

    /**
     * Busca registros da tabela PstServ
     *
     * @param codpessoa
     * @param filtros
     * @param persistencia - Conexão ao Banco
     * @return - Lista de postos de serviços
     * @throws Exception
     */
    public List<PstServ> getPostosCliente(String codpessoa, Map filtros, Persistencia persistencia) throws Exception {
        String sql = " SELECT PstServ.*, ctritens.TipoPosto+' - '+ctritens.Descricao vDescContrato, "
                + " clientes.nred vTipoPostoDesc, clientes.ende vInterfExt "
                + " FROM PstServ "
                + " inner Join PessoaCliAut on PessoaCliAut.CodCli = PstServ.CodCli "
                + "                       and PessoaCliAut.CodFil = PstServ.Codfil "
                + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                + "         and ctritens.contrato = pstserv.contrato "
                + "         and ctritens.tipoposto = pstserv.tipoposto "
                + " inner Join Clientes on Clientes.Codigo = PstServ.CodCli "
                + "                       and Clientes.CodFil = PstServ.Codfil "
                + " WHERE PstServ.situacao='A' and PessoaCliAut.codigo = ? ";
        //+ " and pstserv.codfil = 15 and pstserv.CodCli = 9996001";
        PstServ pstServ;
        List<PstServ> lPstServs = new ArrayList();
        try {
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codpessoa);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            while (consult.Proximo()) {
                pstServ = new PstServ();
                pstServ.setSecao(consult.getString("Secao"));
                pstServ.setCodFil(consult.getString("CodFil"));
                pstServ.setRegional(consult.getInt("Regional"));
                pstServ.setLocal(consult.getString("Local"));
                pstServ.setContrato(consult.getString("Contrato"));
                pstServ.setTipoPosto(consult.getString("TipoPosto"));

                // Salvando CLIENTE.NRED em TipoPostoDesc
                pstServ.setTipoPostoDesc(consult.getString("vTipoPostoDesc"));
                // Salvando CLIENTE.ENDE em InterfExt
                pstServ.setInterfExt(consult.getString("vInterfExt"));

                pstServ.setCodCli(consult.getString("CodCli"));
                pstServ.setPosto(consult.getString("Posto"));
                pstServ.setOS(consult.getString("OS"));
                pstServ.setDescContrato(consult.getString("vDescContrato"));
                lPstServs.add(pstServ);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PstServDao.getPostosCliente - " + e.getMessage() + "\r\n"
                    + " SELECT PstServ.* "
                    + " FROM PstServ "
                    + " inner Join PessoaCliAut on PessoaCliAut.CodCli = PstServ.CodCli "
                    + "                       and PessoaCliAut.CodFil = PstServ.Codfil "
                    + " WHERE PstServ.situacao='A' and PessoaCliAut.codigo = " + codpessoa);
        }
        return lPstServs;
    }

    public String getSecao(Persistencia persistencia, String sCodFil, String sCodCli) throws Exception {
        String sql = "SELECT Secao "
                + " FROM PstServ "
                + " WHERE situacao='A' and codfil=? and CodCli=? ";
        String secao = null;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodFil);
            consult.setString(sCodCli);
            consult.select();
            while (consult.Proximo()) {
                secao = consult.getString("Secao");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar postos de serviços - " + e.getMessage());
        }
        return secao;
    }

    public List<Psthstqst> getQuestionario(String secao, String data, Persistencia persistencia) {
        String sql2 = "select psthstqst.matr from psthstqst"
                + " left join tmktdetpst on tmktdetpst.sequencia = psthstqst.sequencia"
                + " where tmktdetpst.data = ?"
                + " and tmktdetpst.secao = ?"
                + " group by psthstqst.matr";
        Psthstqst oTmktDetPst;
        List<Psthstqst> lTmktDetPst = new ArrayList<Psthstqst>();
        try {

            Consulta pesq = new Consulta(sql2, persistencia);
            pesq.setString(data);
            pesq.setString(secao);
//            try {
            pesq.select();

            //Para evitar exceção caso o valor seja nulo
            while (pesq.Proximo()) {
                oTmktDetPst = new Psthstqst();
                oTmktDetPst.setMatr(pesq.getString("matr"));
                lTmktDetPst.add(oTmktDetPst);
            }
            pesq.Close();
        } catch (Exception e) {
//            throw new Exception("Falha ao atualizar qtde fotos - " + e.getMessage());
            lTmktDetPst = new ArrayList<Psthstqst>();
            oTmktDetPst = new Psthstqst();
            oTmktDetPst.setMatr("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        if (lTmktDetPst != null) {
        } else {
            lTmktDetPst = new ArrayList<Psthstqst>();
            oTmktDetPst = new Psthstqst();
            oTmktDetPst.setMatr("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        return lTmktDetPst;
    }

    public Boolean getSecao(String secao, String data_sql, Persistencia persistencia) throws Exception {
        Boolean bSecao = false;
        String sql = "Select tmk.secao From tmktdetpst as tmk "
                + " left join psthstqst as pst on pst.sequencia = tmk.sequencia "
                + " Where Data = ? and tmk.secao= ? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data_sql);
            consult.setString(secao);
            consult.select();
            while (consult.Proximo()) {
                bSecao = true;
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao verificar seção - " + e.getMessage());
        }
        return bSecao;
    }

    public Boolean getFuncSuperv(String turno, String secao, String codfil, String data_sql, Persistencia persistencia) throws Exception {

        Integer func1 = 0;
        Integer func2 = 0;
        Boolean ret = true;

        try {
            String sql = "Select count(RH_Horas.Matr) as total from RH_Horas "
                    + " left join RHPonto on  RHPonto.Matr = RH_Horas.Matr "
                    + " and RHPonto.DtCompet = ? "
                    + " and RHPonto.Batida   = 1 "
                    + " left join Funcion on Funcion.Matr = RH_Horas.Matr "
                    + " left join Cargos  on Cargos.Cargo = Funcion.Cargo "
                    + " left join Rastrear on  Rastrear.Matr   = RH_Horas.Matr "
                    + " and Rastrear.Data   = RH_Horas.Data "
                    + " and Rastrear.Batida = 1 "
                    + " left join PstServ  on  PstServ.Secao   = RH_Horas.Secao  "
                    + " and PstServ.CodFil  = RH_Horas.CodFil "
                    + " left join Clientes on  Clientes.Codigo = PstServ.CodCli  "
                    + " and Clientes.CodFil = PstServ.CodFil  "
                    + " left join CtrOperV on  CtrOperV.FuncSubs   = RH_Horas.Matr "
                    + " and CtrOperV.Data = RH_Horas.Data "
                    + " and CtrOperV.Hora_Extra = 'B' "
                    + " where RH_Horas.Data   = ? "
                    + " and RH_Horas.Secao  = ? "
                    + " and RH_Horas.CodFil = ? ";

            if ("D".equals(turno.toUpperCase())) {
                sql += " and (HsNoturnas = 0 or (HsDiurnas > 2*HsNoturnas)) "; //quando diurno
            } else {
                sql += " and (HsNoturnas > 0 or (HsNoturnas + HsDiurnas = 0)) "; //quando noturno
            }

            sql += " and (((RH_Horas.HsDiurnas+RH_Horas.HsNoturnas) <> 0) or ((CtrOperV.HEDiurna +CtrOperV.HENoturna ) <> 0)) ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data_sql);
            consult.setString(data_sql);
            consult.setString(secao);
            consult.setString(codfil);
            consult.select();
            while (consult.Proximo()) {
                func1 = Integer.parseInt(consult.getString("total"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        try {
            String sql2 = "select psthstqst.matr matr from psthstqst"
                    + " left join tmktdetpst on tmktdetpst.sequencia = psthstqst.sequencia"
                    + " where tmktdetpst.data = ?"
                    + " and tmktdetpst.secao = ?"
                    + " group by psthstqst.matr";

            Consulta consult2 = new Consulta(sql2, persistencia);
            consult2.setString(data_sql);
            consult2.setString(secao);
            consult2.select();
            List<String> lfunc2 = new ArrayList();
            String matr;
            while (consult2.Proximo()) {
                matr = consult2.getString("matr");
                lfunc2.add(matr);
            }
            consult2.Close();
            func2 = lfunc2.size();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            //valor = func1 - func2;
            if (func2 < func1) {
                ret = false;
            }
        } catch (Exception e) {

        }
        return ret;
    }

    public Boolean verificaFunc(String secao, Persistencia persistencia) {
        String data_sql = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        Boolean ret = false;
        Integer val = 0;
        try {
            String sql2 = " select count (distinct matr) as total from psthstqst where sequencia in "
                    + "(select sequencia from tmktdetpst where tmktdetpst.data = ? and tmktdetpst.secao = ?) ";

            Consulta consult2 = new Consulta(sql2, persistencia);
            consult2.setString(data_sql);
            consult2.setString(secao);
            consult2.select();
            while (consult2.Proximo()) {
                val = Integer.parseInt(consult2.getString("total"));
            }
            if (val >= 1) {
                ret = true;
            }
            consult2.Close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    public Boolean getPostoSuperv(Persistencia persistencia, String CodFil, String CodCli, String data_sql) {
        Integer posto1 = 0;
        Integer posto2 = 0;
        Integer valor = 0;
        Boolean ret = true;

        try {
            String sql = "SELECT count(Secao) as total FROM PstServ  "
                    + " WHERE situacao='A' and codfil= ? and CodCli= ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setString(CodCli);
            consult.select();
            while (consult.Proximo()) {
                posto1 = Integer.parseInt(consult.getString("total"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            String sql2 = "SELECT count(pst.Secao) as total FROM PstServ as pst "
                    + " inner join tmktdetpst as tmk on pst.Secao = tmk.Secao "
                    + " WHERE pst.situacao='A' and pst.codfil= ? and pst.CodCli= ? and Data = ? ";
            Consulta consult = new Consulta(sql2, persistencia);
            consult.setString(CodFil);
            consult.setString(CodCli);
            consult.setString(data_sql);
            consult.select();
            while (consult.Proximo()) {
                posto2 = Integer.parseInt(consult.getString("total"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        try {
            //valor = posto1 - posto2;

            if (posto1 <= posto2) {
                ret = false;
            }
        } catch (Exception e) {

        }
        return ret;
    }

    public Boolean verificaPostoSuperv(Persistencia persistencia, String sCodFil, String sCodCli) throws Exception {
        Boolean bSecao = false;
        String sql = "SELECT pst.Secao FROM PstServ as pst "
                + " left join tmktdetpst as tmk on pst.Secao = tmk.Secao "
                + " WHERE pst.situacao='A' and pst.codfil = ? and pst.CodCli = ? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodFil);
            consult.setString(sCodCli);
            consult.select();
            while (consult.Proximo()) {
                bSecao = true;
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao verificar postos de serviços - " + e.getMessage());
        }
        return bSecao;
    }

    /**
     * Incluir posto de serviço
     *
     * @param pstServ - objeto posto de serviço (pstserv)
     * @param persistencia - conexão ao banco de dados
     * @throws java.lang.Exception
     */
    public void adicionaPstServ(PstServ pstServ, Persistencia persistencia) throws Exception {
        String sql = "INSERT INTO PstServ (Secao, CodFil, Local, Situacao, Dt_Situacao,"
                + " CodCli, Operador, Dt_Alter, Hr_Alter, "
                + "TipoPosto, Contrato, Reforco, Ronda, "
                + "Pericul, Insalub) "
                + " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstServ.getSecao());
            consulta.setBigDecimal(pstServ.getCodFil());
            consulta.setString(pstServ.getLocal());
            consulta.setString(pstServ.getSituacao());
            consulta.setDate(DataAtual.LC2Date(pstServ.getDt_Situacao()));
            consulta.setString(pstServ.getCodCli());
            consulta.setString(pstServ.getOperador());
            consulta.setDate(DataAtual.LC2Date(pstServ.getDt_Alter()));
            consulta.setString(pstServ.getHr_Alter());
            consulta.setString(pstServ.getTipoPosto());
            consulta.setString(pstServ.getContrato());
            consulta.setString(pstServ.getReforco());
            consulta.setString(pstServ.getRonda());
            consulta.setString(pstServ.getPericul());
            consulta.setString(pstServ.getInsalub());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir posto - \r\n" + e.getMessage());
        }
    }

    /**
     * Devolve o próximo registro de posto de serviço
     *
     * @param codfil - codigo da filial
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal getMaxSecao(BigDecimal codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = "select max(convert(float,secao)) codigo from pstserv where codfil = ?";
            BigDecimal retorno = new BigDecimal("1");
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = retorno.add(new BigDecimal(consult.getString("codigo")));
                } catch (Exception e) {
                    retorno = new BigDecimal("1");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar o maior código ativo - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de postos
     *
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> listagemPostosSatMobWeb(BigDecimal codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = "select pstserv.codfil, pstserv.secao, pstserv.codcli, pstserv.local,"
                    + " pstserv.situacao, pstserv.dt_situacao, pstserv.interfext, pstserv.operador, "
                    + " pstserv.dt_alter, pstserv.hr_alter, pstserv.tipoposto, substring(ctritens.descricao,1,25) "
                    + " as tipopostodesc, clientes.ende+' - '+clientes.bairro+' - '+clientes.cidade+'/'+clientes.estado endereco,"
                    + " ctritens.contrato, contratos.descricao desccontrato"
                    + " from pstserv"
                    + " left join contrvig on contrvig.codfil=pstserv.codfil "
                    + "                    and contrvig.contrato=pstserv.contrato "
                    + " left join contratos on contratos.codfil = pstserv.codfil "
                    + "                    and contratos.contrato = contrvig.idcontrato"
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                    and ctritens.contrato = pstserv.contrato "
                    + "                    and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " where pstserv.codfil = ? and pstserv.situacao = 'A'";
            PstServ pstserv;
            List<PstServ> retorno = new ArrayList();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.select();
            while (consult.Proximo()) {
                pstserv = new PstServ();
                pstserv.setCodFil(consult.getString("codfil"));
                pstserv.setSecao(consult.getString("secao"));
                pstserv.setCodCli(consult.getString("codcli"));
                pstserv.setLocal(consult.getString("local"));
                pstserv.setContrato(consult.getString("contrato"));
                pstserv.setDescContrato(consult.getString("desccontrato"));
                pstserv.setSituacao(consult.getString("situacao"));
                pstserv.setDt_Situacao(consult.getDate("dt_situacao").toLocalDate());
                pstserv.setInterfExt(consult.getString("interfext"));
                pstserv.setOperador(consult.getString("operador"));
                pstserv.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstserv.setHr_Alter(consult.getString("hr_alter"));
                pstserv.setTipoPosto(consult.getString("tipoposto"));
                pstserv.setTipoPostoDesc(consult.getString("tipopostodesc"));
                retorno.add(pstserv);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar listagem geral dos postos de servico - \r\n" + e.getMessage());
        }
    }

    public List<PstServ> pesquisaPostosSatMobWeb(PstServ posto, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select pstserv.codfil, pstserv.secao, pstserv.codcli, pstserv.local,"
                    + " pstserv.situacao, pstserv.dt_situacao, pstserv.interfext, pstserv.operador, "
                    + " pstserv.dt_alter, pstserv.hr_alter, pstserv.tipoposto, substring(ctritens.descricao,1,25) "
                    + " as tipopostodesc, clientes.ende+' - '+clientes.bairro+' - '+clientes.cidade+'/'+clientes.estado endereco,"
                    + " ctritens.contrato, contratos.descricao desccontrato"
                    + " from pstserv"
                    + " left join contrvig on contrvig.codfil=pstserv.codfil "
                    + "                    and contrvig.contrato=pstserv.contrato "
                    + " left join contratos on contratos.codfil = pstserv.codfil "
                    + "                    and contratos.contrato = contrvig.idcontrato"
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                    and ctritens.contrato = pstserv.contrato "
                    + "                    and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " where pstserv.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?)";
            PstServ pstserv;
            List<PstServ> retorno = new ArrayList();
            if (!posto.getCodFil().equals(new BigDecimal("-1"))) {
                sql = sql + "" + " and pstserv.codfil = ?";
            }
            if (!posto.getSecao().equals("")) {
                sql = sql + "" + " and pstserv.secao = ?";
            }
            if (!posto.getCodCli().equals("")) {
                sql = sql + "" + " and pstserv.codcli = ?";
            }
            if (!posto.getLocal().equals("")) {
                sql = sql + "" + " and pstserv.local like ?";
            }
            if (!posto.getSituacao().equals("")) {
                sql = sql + "" + " and pstserv.situacao = ?";
            }
            if (!posto.getTipoPosto().equals("")) {
                sql = sql + "" + " and pstserv.tipoposto = ?";
            }
            if (!posto.getTipoPostoDesc().equals("")) {
                sql = sql + "" + " and ctritens.descricao like ?";
            }
            if (!posto.getContrato().equals("")) {
                sql = sql + "" + " and ctritens.contrato = ?";
            }

            sql = sql + " order by pstserv.codfil";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }

            if (!posto.getCodFil().equals(new BigDecimal("-1"))) {
                consult.setBigDecimal(posto.getCodFil());
            }
            if (!posto.getSecao().equals("")) {
                consult.setString(posto.getSecao());
            }
            if (!posto.getCodCli().equals("")) {
                consult.setString(posto.getCodCli());
            }
            if (!posto.getLocal().equals("")) {
                consult.setString("%" + posto.getLocal() + "%");
            }
            if (!posto.getSituacao().equals("")) {
                consult.setString(posto.getSituacao());
            }
            if (!posto.getTipoPosto().equals("")) {
                consult.setString(posto.getTipoPosto());
            }
            if (!posto.getTipoPostoDesc().equals("")) {
                consult.setString("%" + posto.getTipoPostoDesc() + "%");
            }
            if (!posto.getContrato().equals("")) {
                consult.setString(posto.getContrato());
            }

            consult.select();
            while (consult.Proximo()) {
                pstserv = new PstServ();
                pstserv.setCodFil(consult.getString("codfil"));
                pstserv.setSecao(consult.getString("secao"));
                pstserv.setCodCli(consult.getString("codcli"));
                pstserv.setLocal(consult.getString("local"));
                pstserv.setContrato(consult.getString("contrato"));
                pstserv.setDescContrato(consult.getString("desccontrato"));
                pstserv.setSituacao(consult.getString("situacao"));
                pstserv.setDt_Situacao(consult.getDate("dt_situacao").toLocalDate());
                pstserv.setInterfExt(consult.getString("interfext"));
                pstserv.setOperador(consult.getString("operador"));
                pstserv.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstserv.setHr_Alter(consult.getString("hr_alter"));
                pstserv.setTipoPosto(consult.getString("tipoposto"));
                pstserv.setTipoPostoDesc(consult.getString("tipopostodesc"));
                retorno.add(pstserv);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar listagem geral dos postos de servico - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de postos
     *
     * @param codfil - código da filial
     * @param secao - código do posto
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstServ> listagemPostosSecaoSatMobWeb(BigDecimal codfil, String secao, Persistencia persistencia) throws Exception {
        try {
            String sql = "select codfil, secao, codcli, local, situacao, dt_situacao, interfext, "
                    + " operador, dt_alter, hr_alter"
                    + " from pstserv"
                    + " where codfil = ?"
                    + " and secao = ?";
            PstServ pstserv;
            List<PstServ> retorno = new ArrayList();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString(secao);
            consult.select();
            while (consult.Proximo()) {
                pstserv = new PstServ();
                pstserv.setCodFil(consult.getString("codfil"));
                pstserv.setSecao(consult.getString("secao"));
                pstserv.setCodCli(consult.getString("codcli"));
                pstserv.setLocal(consult.getString("local"));
                pstserv.setSituacao(consult.getString("situacao"));
                pstserv.setDt_Situacao(consult.getDate("dt_situacao").toLocalDate());
                pstserv.setInterfExt(consult.getString("interfext"));
                pstserv.setOperador(consult.getString("operador"));
                pstserv.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstserv.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(pstserv);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar listagem geral dos postos de servico - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de postos
     *
     * @param codfil - código da filial
     * @param local - código do posto
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstServ> listagemPostosLocalSatMobWeb(BigDecimal codfil, String local, Persistencia persistencia) throws Exception {
        try {
            String sql = "select codfil, secao, codcli, local, situacao, dt_situacao, interfext, "
                    + " operador, dt_alter, hr_alter"
                    + " from pstserv"
                    + " where codfil = ?"
                    + " and local = ?";
            PstServ pstserv;
            List<PstServ> retorno = new ArrayList();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString(local);
            consult.select();
            while (consult.Proximo()) {
                pstserv = new PstServ();
                pstserv.setCodFil(consult.getString("codfil"));
                pstserv.setSecao(consult.getString("secao"));
                pstserv.setCodCli(consult.getString("codcli"));
                pstserv.setLocal(consult.getString("local"));
                pstserv.setSituacao(consult.getString("situacao"));
                pstserv.setDt_Situacao(consult.getDate("dt_situacao").toLocalDate());
                pstserv.setInterfExt(consult.getString("interfext"));
                pstserv.setOperador(consult.getString("operador"));
                pstserv.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstserv.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(pstserv);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar listagem geral dos postos de servico - \r\n" + e.getMessage());
        }
    }

    /**
     * Grava alterações de postos de serviços
     *
     * @param pstserv - objetos pstserv Dados a serem gravados: codcli, local,
     * situacao, interfext, operador Parametros: codfil e secao
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void GravaPstServ(PstServ pstserv, Persistencia persistencia) throws Exception {
        try {
            String sql = "update pstserv set codcli = ?, local = ?, situacao = ?, dt_situacao = ?, interfext = ?, "
                    + " operador = ?, dt_alter = ?, hr_alter = ?, tipoposto = ? "
                    + " where codfil = ? and secao = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstserv.getCodCli());
            consulta.setString(pstserv.getLocal());
            consulta.setString(pstserv.getSituacao());
            consulta.setDate(DataAtual.LC2Date(pstserv.getDt_Situacao()));
            consulta.setString(pstserv.getInterfExt());
            consulta.setString(pstserv.getOperador());
            consulta.setDate(DataAtual.LC2Date(LocalDate.now()));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(pstserv.getTipoPosto());
            consulta.setBigDecimal(pstserv.getCodFil());
            consulta.setString(pstserv.getSecao());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar dados do posto - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca postos de um cliente
     *
     * @param codfil - codigo da filial
     * @param codcli - codigo do cliente
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstServ> ListaPostoCliente(BigDecimal codfil, String codcli, Persistencia persistencia) throws Exception {
        try {
            List<PstServ> retorno = new ArrayList();
            String sql = "select codfil, codcli, local, secao "
                    + " from pstserv "
                    + " where situacao = 'A' "
                    + " and codfil = ?"
                    + " and codcli = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString(codcli);
            consult.select();
            PstServ pst;
            while (consult.Proximo()) {
                pst = new PstServ();
                pst.setCodFil(consult.getString("codfil"));
                pst.setCodCli(consult.getString("codcli"));
                pst.setLocal(consult.getString("local"));
                pst.setSecao(consult.getString("secao"));
                retorno.add(pst);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao busca posto por cliente - \r\n" + e.getMessage());
        }
    }

    /**
     * Obtem os registro da seção do posto
     *
     * @param banco número dos três primeiros digitos do código do cliente
     * @param codFil código da filial
     * @param codigo Sequencial
     * @param tipoPosto tipo do posto 'A' => diurno ou 'B' => noturno
     * @param persistencia percitencia para realizar a consulta
     * @return o retorno da seção
     */
    public String getSequencial(String banco, String codFil, int codigo, String tipoPosto, Persistencia persistencia) throws Exception {
        String identificador = "";
        try {
            String sql = "Select "
                    + "isnull(Max(Isnull(Substring(Substring(Substring(Secao,CHARINDEX('.',Secao)+1, "
                    + "(Len(Secao))),1,Len(Secao)),CHARINDEX('.',Substring(Substring(Secao,CHARINDEX('.',Secao)+1,"
                    + "(Len(Secao))),1,Len(Secao)))+1,3),0)),0)+1 as sequencial "
                    + "from PSTServ "
                    + "Where CodFil = ? "
                    + "and Isnull(Replace(Substring(Secao,1,CHARINDEX('.',Secao)),'.',''),0) = ?"
                    + "  and Isnull(Replace(Substring(Substring(Secao,CHARINDEX('.',Secao)+1, "
                    + "(Len(Secao))),1,CHARINDEX('.',Substring(Secao,CHARINDEX('.',Secao)+1, "
                    + "(Len(Secao))))),'.',''),0) = ?";

            //Inserindo os paremtros e realizando a consulta
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(banco);
            consulta.setInt(codigo);
            consulta.select();

            while (consulta.Proximo()) {
                identificador = consulta.getString("sequencial");
            }
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("Falha ao busca a secao do posto - \r\n" + ex.getMessage());
        }
        return identificador;
    }

    /**
     * Obtem os registro de postos com campos definidos
     *
     * @param posto posto com caracterísitcas a serem buscadas
     * @param persistencia percitencia para realizar a consulta
     * @return o retorno da seção
     * @throws java.lang.Exception
     */
    public List<PstServ> BuscaPostosSatMobWeb(PstServ posto, Persistencia persistencia) throws Exception {
        try {
            String sql = "select pstserv.codfil, pstserv.secao, pstserv.codcli, pstserv.local,"
                    + " pstserv.situacao, pstserv.dt_situacao, pstserv.interfext, pstserv.operador, "
                    + " pstserv.dt_alter, pstserv.hr_alter, pstserv.tipoposto, substring(ctritens.descricao,1,25) "
                    + " as tipopostodesc, clientes.ende+' - '+clientes.bairro+' - '+clientes.cidade+'/'+clientes.estado endereco,"
                    + " ctritens.contrato, contratos.descricao desccontrato"
                    + " from pstserv"
                    + " left join contrvig on contrvig.codfil=pstserv.codfil "
                    + "                    and contrvig.contrato=pstserv.contrato "
                    + " left join contratos on contratos.codfil = pstserv.codfil "
                    + "                    and contratos.contrato = contrvig.idcontrato"
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                    and ctritens.contrato = pstserv.contrato "
                    + "                    and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " where pstserv.codfil = ?";
            if (!posto.getSecao().equals("")
                    || !posto.getLocal().equals("")
                    || !posto.getDescContrato().equals("")) {
                sql += " and (pstserv.secao like ? or pstserv.local like ? or ctritens.descricao like ?)";
            }
            PstServ pstserv;
            List<PstServ> retorno = new ArrayList();
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(posto.getCodFil());
            if (!posto.getSecao().equals("")
                    || !posto.getLocal().equals("")
                    || !posto.getDescContrato().equals("")) {
                consult.setString("%" + posto.getSecao() + "%");
                consult.setString("%" + posto.getLocal() + "%");
                consult.setString("%" + posto.getDescContrato() + "%");
            }

            consult.select();
            while (consult.Proximo()) {
                pstserv = new PstServ();
                pstserv.setCodFil(consult.getString("codfil"));
                pstserv.setSecao(consult.getString("secao"));
                pstserv.setCodCli(consult.getString("codcli"));
                pstserv.setLocal(consult.getString("local"));
                pstserv.setContrato(consult.getString("contrato"));
                pstserv.setDescContrato(consult.getString("desccontrato"));
                pstserv.setSituacao(consult.getString("situacao"));
                pstserv.setDt_Situacao(consult.getDate("dt_situacao").toLocalDate());
                pstserv.setInterfExt(consult.getString("interfext"));
                pstserv.setOperador(consult.getString("operador"));
                pstserv.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstserv.setHr_Alter(consult.getString("hr_alter"));
                pstserv.setTipoPosto(consult.getString("tipoposto"));
                pstserv.setTipoPostoDesc(consult.getString("tipopostodesc"));
                retorno.add(pstserv);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar listagem geral dos postos de servico - \r\n" + e.getMessage());
        }
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de usuarios cadastradas no banco
     *
     * @param filtros - filtros de pesquisa
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalPostosMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) total FROM pstserv \n"
                    + " WHERE  codfil IN (SELECT filiais.codfil \n"
                    + "    FROM saspw \n"
                    + "    INNER JOIN saspwfil ON saspwfil.nome = saspw.nome \n"
                    + "    INNER JOIN filiais ON filiais.codfil = saspwfil.codfilac \n"
                    + "    INNER JOIN paramet ON paramet.filial_pdr = filiais.codfil \n"
                    + "    WHERE saspw.codpessoa = ? AND paramet.path = ?) AND  \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND  \n";
                }
            }
            sql = sql + "pstserv.secao IS NOT null \n";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar usuários - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo da pessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstServ> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<PstServ> postos = new ArrayList();
        try {
            String sql = "SELECT  * \n"
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY local ) AS RowNum, \n"
                    + " pstserv.codfil, pstserv.secao, pstserv.codcli, pstserv.local, \n"
                    + " pstserv.situacao, pstserv.dt_situacao, pstserv.interfext, pstserv.operador, \n"
                    + " pstserv.dt_alter, pstserv.hr_alter, pstserv.tipoposto, substring(ctritens.descricao,1,25) \n"
                    + " as tipopostodesc, clientes.ende+' - '+clientes.bairro+' - '+clientes.cidade+'/'+clientes.estado endereco, \n"
                    + " ctritens.contrato, contratos.descricao desccontrato \n"
                    + "          FROM      PstServ  \n"
                    + "left join contrvig on contrvig.codfil=pstserv.codfil \n"
                    + "                    and contrvig.contrato=pstserv.contrato \n"
                    + " left join contratos on contratos.codfil = pstserv.codfil \n"
                    + "                    and contratos.contrato = contrvig.idcontrato \n"
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil \n"
                    + "                    and ctritens.contrato = pstserv.contrato \n"
                    + "                    and ctritens.tipoposto = pstserv.tipoposto \n"
                    + " left join clientes on clientes.codigo = pstserv.codcli \n"
                    + "                   and clientes.codfil = pstserv.codfil \n"
                    + " WHERE pstserv.codfil in (select filiais.codfil \n"
                    + "                          from saspw \n"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome \n"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac \n"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil \n"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND \n";
                }
            }
            sql = sql + "pstserv.secao IS NOT null) AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ? \n"
                    + "    AND RowNum < ? \n"
                    + "ORDER BY RowNum \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            PstServ pstserv;
            while (consulta.Proximo()) {
                pstserv = new PstServ();
                pstserv.setCodFil(consulta.getString("codfil"));
                pstserv.setSecao(consulta.getString("secao"));
                pstserv.setCodCli(consulta.getString("codcli"));
                pstserv.setLocal(consulta.getString("local"));
                pstserv.setContrato(consulta.getString("contrato"));
                pstserv.setDescContrato(consulta.getString("desccontrato"));
                pstserv.setSituacao(consulta.getString("situacao"));
                pstserv.setDt_Situacao(consulta.getDate("dt_situacao").toLocalDate());
                pstserv.setInterfExt(consulta.getString("interfext"));
                pstserv.setOperador(consulta.getString("operador"));
                pstserv.setDt_Alter(consulta.getDate("dt_alter").toLocalDate());
                pstserv.setHr_Alter(consulta.getString("hr_alter"));
                pstserv.setTipoPosto(consulta.getString("tipoposto"));
                pstserv.setTipoPostoDesc(consulta.getString("tipopostodesc"));
                postos.add(pstserv);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de postos - \r\n" + e.getMessage());
        }
        return postos;
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo da pessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalListaPaginada(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) AS total FROM PstServ \n"
                    + " left join contrvig on contrvig.codfil=pstserv.codfil \n"
                    + "     and contrvig.contrato=pstserv.contrato \n"
                    + " left join contratos on contratos.codfil = pstserv.codfil \n"
                    + "     and contratos.contrato = contrvig.idcontrato \n"
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil \n"
                    + "     and ctritens.contrato = pstserv.contrato \n"
                    + "     and ctritens.tipoposto = pstserv.tipoposto \n"
                    + " left join clientes on clientes.codigo = pstserv.codcli \n"
                    + "     and clientes.codfil = pstserv.codfil \n"
                    + " WHERE pstserv.codfil in (select filiais.codfil \n"
                    + "     from saspw \n"
                    + "     inner join saspwfil on saspwfil.nome = saspw.nome \n"
                    + "     inner join filiais on filiais.codfil = saspwfil.codfilac \n"
                    + "     inner join paramet on paramet.filial_pdr = filiais.codfil \n"
                    + "     where saspw.codpessoa = ? and paramet.path = ?) AND \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND \n";
                }
            }
            sql = sql + "pstserv.secao IS NOT null \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de postos - \r\n" + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
    /**
     * Lista todos os postos pelo local
     *
     * @param query
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> buscarPostos(String query, Persistencia persistencia) throws Exception {
        try {
            List<PstServ> retorno = new ArrayList<>();
            String sql = " select secao, local, codfil from pstserv"
                    + " where local like ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.select();
            PstServ pstServ;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                pstServ.setCodFil(consulta.getString("codFil"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setSecao(consulta.getString("secao"));
                retorno.add(pstServ);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.buscarPostos - " + e.getMessage() + "\r\n"
                    + " select secao, local, codfil from pstserv"
                    + " where local like " + query);
        }
    }

    /**
     * Lista todos os postos de uma filial para o SatMobEW
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> listarPostosSatMobEW(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<PstServ> retorno = new ArrayList<>();
            String sql = " Select pstserv.codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where pstserv.codfil = ? and clientes.latitude is not null and pstserv.situacao <> 'I' "
                    + "    and clientes.latitude <> '' and clientes.longitude is not null and clientes.longitude <> '' "
                    + " order by local asc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            PstServ pstServ;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
                retorno.add(pstServ);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosSatMobEW - " + e.getMessage() + "\r\n"
                    + " Select codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred,"
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where codfil = " + codFil);
        }
    }

    public List<PstServ> listarPostosSatMobEW(Persistencia persistencia) throws Exception {
        try {
            List<PstServ> retorno = new ArrayList<>();
            String sql = " Select pstserv.codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where clientes.latitude is not null and pstserv.situacao <> 'I' "
                    + "    and clientes.latitude <> '' and clientes.longitude is not null and clientes.longitude <> '' "
                    + " order by local asc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            PstServ pstServ;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
                retorno.add(pstServ);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosSatMobEW - " + e.getMessage() + "\r\n"
                    + " Select codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred,"
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where clientes.latitude is not null and pstserv.situacao <> 'I' "
                    + "    and clientes.latitude <> '' and clientes.longitude is not null and clientes.longitude <> '' "
                    + " order by local asc");
        }
    }

    public List<PstServ> listarPostosSatMobEWMatr(String vMatr, Persistencia persistencia) throws Exception {
        try {
            List<PstServ> retorno = new ArrayList<>();
            String sql = "Select pstserv.codfil, pstserv.local, pstserv.secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join Pessoa on Pessoa.Matr = "+vMatr
                    + " Left Join Funcion on Funcion.Secao = PstServ.Secao "
                    + "                  and Funcion.CodFil = PstServ.CodFil"
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where clientes.latitude is not null and pstserv.situacao <> 'I' "
                    + "   and clientes.latitude <> '' and clientes.longitude is not null and clientes.longitude <> '' "
                    + "   and Funcion.Matr = Pessoa.Matr "
                    + " order by local asc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            PstServ pstServ;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
                retorno.add(pstServ);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosSatMobEW - " + e.getMessage() + "\r\n"
                    +"Select pstserv.codfil, pstserv.local, pstserv.secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join Funcion on Funcion.Seca o = PstServ.Secao "
                    + "                  and Funcion.CodFil = PstServ.CodFil"
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where clientes.latitude is not null and pstserv.situacao <> 'I' "
                    + "    and clientes.latitude <> '' and clientes.longitude is not null and clientes.longitude <> '' "
                    + "    Funcion.Matr = "+vMatr
                    + "    and PStServ.CodFil = Funcion.CodFil "
                    + " order by local asc");
        }
    }    
    
    /**
     * Seleciona os dois últimos postos que a pessoa fez check in para validar
     * se é check in ou check out.
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public PstServ validarCheckIns(String matricula, String data, String hora, Persistencia persistencia) throws Exception {
        try {
            String sql = "select top 1 CONVERT(VarChar, rhponto.dtbatida, 112) DtBatida, rhponto.Hora, r.batida, \n"
                    + "pstserv.*, ende, clientes.latitude, clientes.longitude, nred, \n"
                    + "ctritens.TipoPosto+' - '+ctritens.Descricao Descricao \n"
                    + "from rhpontodet r \n"
                    + "left join rhponto on rhponto.batida = r.batida\n"
                    + "                 and rhponto.matr = r.matr\n"
                    + "                 and rhponto.dtcompet = r.dtcompet\n"
                    + "left join funcion on funcion.matr = r.matr\n"
                    + "left join pstserv on pstserv.secao = r.secao\n"
                    + "                  and pstserv.codfil = funcion.codfil\n"
                    + "left join clientes on clientes.codigo = pstserv.codcli \n"
                    + "                 and clientes.codfil = pstserv.codfil \n"
                    + "Left Join ctritens on ctritens.codfil = pstserv.codfil \n"
                    + "               and ctritens.contrato = pstserv.contrato \n"
                    + "              and ctritens.tipoposto = pstserv.tipoposto \n"
                    + "where r.batida > 9000 and r.matr = ? \n"
                    + "order by r.dtcompet desc, r.batida desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();
            PstServ pstServ = null;
            while (consulta.Proximo()) {
                // Se a batida for par, a próxima iteração é check in. Se for impar, carrega o posto que tem que fazer check out.
                int batida = new BigInteger(consulta.getString("batida")).intValue();
                if (batida % 2 == 0) {
                    return null;
                }

                try {
                    LocalDateTime data1 = LocalDateTime.parse(consulta.getString("DtBatida") + consulta.getString("Hora"), DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
                    LocalDateTime data2 = LocalDateTime.parse(data + hora, DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
                    // Se a diferença entre o último check in e o login for maior que ou igual a 24 horas desconsidera
                    if (Math.abs(TimeUnit.MILLISECONDS.toHours(Duration.between(data1, data2).toMillis())) >= 24) {
                        return null;
                    }
                } catch (Exception eData) {
                }

                pstServ = new PstServ();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
            }
            consulta.Close();
            return pstServ;
        } catch (Exception e) {
            throw new Exception("PstServDao.validarCheckIns - " + e.getMessage() + "\r\n"
                    + " select top 1 CONVERT(VarChar, rhponto.dtbatida, 112) DtBatida, rhponto.hora, r.batida, \n"
                    + " pstserv.*, ende, clientes.latitude, clientes.longitude, nred, \n"
                    + "ctritens.TipoPosto+' - '+ctritens.Descricao Descricao \n"
                    + "from rhpontodet r \n"
                    + "left join rhponto on rhponto.batida = r.batida\n"
                    + "                  and rhponto.matr = r.matr\n"
                    + "                  and rhponto.dtcompet = r.dtcompet\n"
                    + "left join pstserv on pstserv.secao = r.secao \n"
                    + "left join clientes on clientes.codigo = pstserv.codcli \n"
                    + "                  and clientes.codfil = pstserv.codfil \n"
                    + "Left Join ctritens on ctritens.codfil = pstserv.codfil \n"
                    + "                and ctritens.contrato = pstserv.contrato \n"
                    + "               and ctritens.tipoposto = pstserv.tipoposto \n"
                    + " where r.batida > 9000 and r.matr = " + matricula + " \n"
                    + " order by r.dtcompet desc, r.batida desc \n");
        }
    }

    public String buscarSecaoFuncion(String matr, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            String retorno = "";

            sql = "SELECT Funcion.Secao\n"
                    + "FROM Funcion \n"
                    + "WHERE Funcion.Matr = ? AND codfil = ? AND Funcao <> 'P'";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(matr);
            consulta.setString(codFil);

            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getString("Secao");
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("buscarSecaoFuncion" + sql);
        }
    }

    /**
     * Lista todos os postos de uma filial para o SatMobEW
     *
     * @param codFil
     * @param codcli
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServTipoPosto> listarPostosClienteSatMobEW(String codFil, String codcli, Persistencia persistencia) throws Exception {
        try {
            List<PstServTipoPosto> retorno = new ArrayList<>();
            String vTop = "";
            if(persistencia.getEmpresa().contains("CONFEDERAL")){ //Carlos 02/08/2022
                   vTop = " top 1 ";
            }
            String sql = "Select "+vTop+" pstserv.codfil, pstserv.HrPadraoIni, pstserv.HrPadraoFim, local, secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, TiposPostos.* "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where pstserv.codfil = ? and clientes.codigo = ? "
                    + "         and pstserv.situacao = 'A' and pstserv.secao not like '%I%' "
                    + " order by local asc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codcli);
            consulta.select();
            PstServ pstServ;
            TipoPosto tipoPosto;
            PstServTipoPosto pstServTipoPosto;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                tipoPosto = new TipoPosto();
                pstServTipoPosto = new PstServTipoPosto();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
                pstServ.setHrPadraoIni(consulta.getString("HrPadraoIni"));
                pstServ.setHrPadraoFim(consulta.getString("HrPadraoFim"));
                tipoPosto.setChdiu1(consulta.getString("chdiu1"));
                tipoPosto.setChnot1(consulta.getString("chnot1"));
                tipoPosto.setChdiu2(consulta.getString("chdiu2"));
                tipoPosto.setChnot2(consulta.getString("chnot2"));
                tipoPosto.setChdiu3(consulta.getString("chdiu3"));
                tipoPosto.setChnot3(consulta.getString("chnot3"));
                tipoPosto.setChdiu4(consulta.getString("chdiu4"));
                tipoPosto.setChnot4(consulta.getString("chnot4"));
                tipoPosto.setChdiu5(consulta.getString("chdiu5"));
                tipoPosto.setChnot5(consulta.getString("chnot5"));
                tipoPosto.setChdiu6(consulta.getString("chdiu6"));
                tipoPosto.setChnot6(consulta.getString("chnot6"));
                tipoPosto.setChdiu7(consulta.getString("chdiu7"));
                tipoPosto.setChnot7(consulta.getString("chnot7"));
                pstServTipoPosto.setPstServ(pstServ);
                pstServTipoPosto.setTipoPosto(tipoPosto);
                retorno.add(pstServTipoPosto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosClienteSatMobEW - " + e.getMessage() + "\r\n"
                    + " Select codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred,"
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, TiposPostos.* "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where codfil = " + codFil + " and clientes.codigo = " + codcli
                    + "         and pstserv.situacao = 'A'"
                    + " order by local asc");
        }
    }

    public List<PstServTipoPosto> listarPostosClienteSatMobEW(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<PstServTipoPosto> retorno = new ArrayList<>();
            String sql = " Select pstserv.codfil, pstserv.HrPadraoIni, pstserv.HrPadraoFim, local, secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, TiposPostos.* "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where pstserv.codfil = ?"
                    + "         and pstserv.situacao = 'A' and pstserv.secao not like '%I%' "
                    + " order by local asc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            PstServ pstServ;
            TipoPosto tipoPosto;
            PstServTipoPosto pstServTipoPosto;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                tipoPosto = new TipoPosto();
                pstServTipoPosto = new PstServTipoPosto();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
                pstServ.setHrPadraoIni(consulta.getString("HrPadraoIni"));
                pstServ.setHrPadraoFim(consulta.getString("HrPadraoFim"));
                tipoPosto.setChdiu1(consulta.getString("chdiu1"));
                tipoPosto.setChnot1(consulta.getString("chnot1"));
                tipoPosto.setChdiu2(consulta.getString("chdiu2"));
                tipoPosto.setChnot2(consulta.getString("chnot2"));
                tipoPosto.setChdiu3(consulta.getString("chdiu3"));
                tipoPosto.setChnot3(consulta.getString("chnot3"));
                tipoPosto.setChdiu4(consulta.getString("chdiu4"));
                tipoPosto.setChnot4(consulta.getString("chnot4"));
                tipoPosto.setChdiu5(consulta.getString("chdiu5"));
                tipoPosto.setChnot5(consulta.getString("chnot5"));
                tipoPosto.setChdiu6(consulta.getString("chdiu6"));
                tipoPosto.setChnot6(consulta.getString("chnot6"));
                tipoPosto.setChdiu7(consulta.getString("chdiu7"));
                tipoPosto.setChnot7(consulta.getString("chnot7"));
                pstServTipoPosto.setPstServ(pstServ);
                pstServTipoPosto.setTipoPosto(tipoPosto);
                retorno.add(pstServTipoPosto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosClienteSatMobEW - " + e.getMessage() + "\r\n"
                    + " Select codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred,"
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, TiposPostos.* "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where Pstserv.codfil = " + codFil
                    + "         and pstserv.situacao = 'A'"
                    + " order by local asc");
        }
    }

    /**
     * Lista todos os postos de uma filial para o SatMobEW
     *
     * @param codFil
     * @param codcli
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PstServ> listarPostosCliente(String codFil, String codcli, Persistencia persistencia) throws Exception {
        try {
            List<PstServ> retorno = new ArrayList<>();
            String sql = " Select pstserv.codfil, pstserv.HrPadraoIni, pstserv.HrPadraoFim, local, secao, ende, clientes.latitude, clientes.longitude, nred, "
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where pstserv.codfil = ? and clientes.codigo = ? "
                    + "         and pstserv.situacao = 'A' and pstserv.secao not like '%I%' "
                    + " order by local asc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codcli);
            consulta.select();
            PstServ pstServ;
            while (consulta.Proximo()) {
                pstServ = new PstServ();
                pstServ.setCodFil(consulta.getString("codfil"));
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                pstServ.setDescContrato(consulta.getString("descricao"));
                pstServ.setCliente(consulta.getString("nred"));
                pstServ.setEndereco(consulta.getString("ende"));
                pstServ.setLatitude(consulta.getString("latitude"));
                pstServ.setLongitude(consulta.getString("longitude"));
                pstServ.setHrPadraoIni(consulta.getString("HrPadraoIni"));
                pstServ.setHrPadraoFim(consulta.getString("HrPadraoFim"));

                pstServ.setDescricao(consulta.getString("secao") + " " + consulta.getString("local"));

                retorno.add(pstServ);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarPostosCliente - " + e.getMessage() + "\r\n"
                    + " Select codfil, local, secao, ende, clientes.latitude, clientes.longitude, nred,"
                    + " ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, TiposPostos.* "
                    + " from pstserv"
                    + " left join clientes on clientes.codigo = pstserv.codcli"
                    + "                    and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where codfil = " + codFil + " and clientes.codigo = " + codcli
                    + "         and pstserv.situacao = 'A'"
                    + " order by local asc");
        }
    }

    public Login listarConfiguracaoMatr(String matr, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "SELECT\n"
                    + "Filiais.codfil FilialCodigo,\n"
                    + "Filiais.Descricao FilialDescricao,\n"
                    + "Filiais.Endereco FilialEndereco,\n"
                    + "Filiais.RazaoSocial FilialNome,\n"
                    + "pstserv.Local PostoLocal,\n"
                    + "pstserv.Secao PostoSecao,\n"
                    + "pstserv.Contrato PostoContrato,\n"
                    + "pstserv.Posto Posto,\n"
                    + "pstserv.TipoPosto PostoTipo,\n"
                    + "Clientes.Codigo ClienteCodigo,\n"
                    + "Clientes.NRed ClienteNred,\n"
                    + "Clientes.Nome ClienteNome,\n"
                    + "Clientes.Ende ClienteEnde,\n"
                    + "Clientes.Bairro ClienteBairro,\n"
                    + "Clientes.Cidade ClienteCidade,\n"
                    + "Clientes.Estado ClienteEstado,\n"
                    + "Clientes.CEP ClienteCep,\n"
                    + "Clientes.Latitude ClienteLat,\n"
                    + "Clientes.Longitude ClienteLon\n"
                    + "FROM Funcion\n"
                    + " JOIN Filiais\n"
                    + "  ON Funcion.codfil = Filiais.codfil\n"
                    + " JOIN Pstserv\n"
                    + "  ON Funcion.secao = pstserv.Secao\n"
                    + "  and Funcion.CodFil = pstServ.Codfil\n"
                    + " JOIN Clientes\n"
                    + "  ON Pstserv.codCli = Clientes.codigo\n"
                    + " AND Pstserv.Codfil = Clientes.codfil\n"
                    + " WHERE Funcion.Matr = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.select();

            Login retorno = new Login();
            Clientes cliente = new Clientes();
            PstServ pstServ = new PstServ();
            Filiais filiais = new Filiais();
            
            while (consulta.Proximo()) {
                pstServ.setLocal(consulta.getString("PostoLocal"));
                pstServ.setSecao(consulta.getString("PostoSecao"));
                pstServ.setContrato(consulta.getString("PostoContrato"));
                pstServ.setPosto(consulta.getString("Posto"));
                pstServ.setTipoPosto(consulta.getString("PostoTipo"));
                
                cliente.setCodigo(consulta.getString("ClienteCodigo"));
                cliente.setNRed(consulta.getString("ClienteNred"));
                cliente.setNome(consulta.getString("ClienteNome"));
                cliente.setEnde(consulta.getString("ClienteEnde"));
                cliente.setCidade(consulta.getString("ClienteCidade"));
                cliente.setEstado(consulta.getString("ClienteEstado"));
                cliente.setBairro(consulta.getString("ClienteBairro"));
                cliente.setCEP(consulta.getString("ClienteCep"));
                cliente.setLatitude(consulta.getString("ClienteLat"));
                cliente.setLongitude(consulta.getString("ClienteLon"));
                
                filiais.setCodFil(consulta.getString("FilialCodigo"));
                filiais.setDescricao(consulta.getString("FilialDescricao"));
                filiais.setEndereco(consulta.getString("FilialEndereco"));
                filiais.setRazaoSocial(consulta.getString("FilialNome"));
                
                retorno.setCliente(cliente);
                retorno.setPstServ(pstServ);
                retorno.setFiliais(filiais);
            }
            consulta.Close();
            
            return retorno;
        } catch (Exception e) {
            throw new Exception("PstServDao.listarConfiguracaoMatr - " + e.getMessage() + sql);
        }
    }
}

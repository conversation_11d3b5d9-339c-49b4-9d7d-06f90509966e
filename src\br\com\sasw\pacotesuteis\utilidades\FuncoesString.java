package br.com.sasw.pacotesuteis.utilidades;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Normalizer;
import java.text.NumberFormat;
import java.util.Locale;
import java.util.Random;
import javax.swing.text.MaskFormatter;

/**
 *
 * <AUTHOR>
 */
public class FuncoesString {

    /**
     * Retorna verdadeiro se for uma string
     *
     * @param s - String a ser verificada
     * @return - booleano
     */
    public static boolean ehString(String s) {
        // cria um array de char
        char[] c = s.toCharArray();
        boolean d = false;
        for (int i = 0; i < c.length; i++) {
            // verifica se o char não é um dígito
            if (Character.isDigit(c[i])) {
                d = true;
                break;
            }
        }
        return d;
    }

    /**
     * Retorna se é verdadeiro se for um dígito
     *
     * @param s - String a ser verificada
     * @return - booleano
     */
    public static boolean ehInteiro(String s) {
        // cria um array de char
        char[] c = s.toCharArray();
        boolean d = true;
        for (int i = 0; i < c.length; i++) {
            // verifica se o char não é um dígito
            if (!Character.isDigit(c[i])) {
                d = false;
                break;
            }
        }
        return d;
    }

    public static String preencherDireita(String texto, int tamanho, String caracter) {
        String retorno = "";
        int qt = tamanho - texto.length();
        try {
            if (qt > 0) {
                for (int i = 0; i < qt; i++) {
                    retorno += caracter;
                }
                retorno = texto + retorno;
            } else if (qt == 0) {
                retorno = texto;
            } else {
                retorno = texto.substring(0, tamanho);
            }
        } catch (Exception e) {
        }
        return retorno;
    }

    public static String preencheCom(String linha_a_preencher, String letra, int tamanho, int direcao) {
        //Checa se Linha a preencher é nula ou branco
        if (linha_a_preencher == null || linha_a_preencher.trim() == "") {
            linha_a_preencher = "";
        }
        //Enquanto Linha a preencher possuir 2 espaços em branco seguidos, substitui por 1 espaço apenas
        while (linha_a_preencher.contains(" ")) {
            linha_a_preencher = linha_a_preencher.replaceAll(" ", " ").trim();
        }
        //Retira caracteres estranhos
        linha_a_preencher = linha_a_preencher.replaceAll("[./-]", "");
        StringBuffer sb = new StringBuffer(linha_a_preencher);
        if (direcao == 1) { //a Esquerda
            for (int i = sb.length(); i < tamanho; i++) {
                sb.insert(0, letra);
            }
        } else if (direcao == 2) {//a Direita
            for (int i = sb.length(); i < tamanho; i++) {
                sb.append(letra);
            }
        }
        return sb.toString();
    }

    /**
     * Retorna um corte de string
     *
     * @param value - String a ser cortada
     * @param inicio - posicao de inicio do corte - incio da string em 0
     * @param tamanho - tamanho maximo que deve ser retornado
     * @return - String retornada já cortada
     */
    public static String RecortaString(String value, int inicio, int tamanho) {
        int fim = inicio + tamanho;
        try {
            if (value.length() < inicio) {
                return "";
            } else if (value.length() > fim) {
                return value.substring(inicio, inicio + tamanho);
            } else {
                return value.substring(inicio);
            }
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * Preenche String com espacos a direita
     *
     * @param value - String a ser preenchida
     * @param qt - quantidade de espacos inseridos
     * @return - retorna a String já com espacos a direitoa
     * @throws Exception - gera excessao caso ocorra erro
     */
    public static String SpaceString(String value, int qt) throws Exception {
        String retorno = value;
        try {
            for (int i = 1; i <= qt; i++) {
                retorno += " ";
            }
        } catch (Exception e) {
            throw new Exception("Erro " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Preenche String com caracteres a direita
     *
     * @param value - String a ser preenchida
     * @param qt - quantidade de espacos inseridos
     * @param c - caracter que sera usado para preencher a string
     * @return - retorna a String já com espacos a direitoa
     * @throws Exception - gera excessao caso ocorra erro
     */
    public static String SpaceString(String value, int qt, String c) throws Exception {
        String retorno = value;
        try {
            for (int i = 1; i <= qt; i++) {
                retorno += c;
            }
        } catch (Exception e) {
            throw new Exception("Erro " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Formata uma String colocando caracteres a esquerda, caso a String seja
     * maior que o tamanho, a funcao recorta a String
     *
     * @param value - String a ser completada
     * @param tamanho - quantidade de caractes que deve ter a String
     * @param Caracter - caractere de preenchimento
     * @return - String já formatada
     */
    public static String PreencheEsquerda(String value, int tamanho, String Caracter) {
        String retorno = "";
        int qt = tamanho - value.length();
        try {
            if (qt > 0) {
                for (int i = 0; i < qt; i++) {
                    retorno += Caracter;
                }
                retorno += value;
            } else if (qt == 0) {
                retorno = value;
            } else {
                retorno = value.substring(0, tamanho);
            }
        } catch (Exception e) {
        }
        return retorno;
    }

    /**
     * Retira todos os caracters especiais
     *
     * @param s - String original
     * @return - String sem caracteres especiais
     */
    public static String formatString(String s) {
        String temp = Normalizer.normalize(s, java.text.Normalizer.Form.NFD);
        return temp.replaceAll("[^\\p{ASCII}]", "");
    }

    /**
     * Retira os caracteres ( ) . / , : - da string
     *
     * @param value - string a ser limpa
     * @param substituto - caractere que ficara no lugar dos caracteres
     * eliminados
     * @return - retorna a string limpa
     */
    public static String limpa3(String value, String substituto) {
        String temp = value;
        try {
            temp = temp.replaceAll("R", substituto);
            temp = temp.replaceAll("\\$", substituto);
            temp = temp.replaceAll(" ", substituto);
            temp = temp.replaceAll(",", ".");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return temp;
    }

    /**
     * Retira os caracteres ( ) . / , : - da string
     *
     * @param value - string a ser limpa
     * @param substituto - caractere que ficara no lugar dos caracteres
     * eliminados
     * @return - retorna a string limpa
     */
    public static String limpa2(String value, String substituto) {
        String temp = value;
        try {
            temp = temp.replaceAll("\\(", substituto);
            temp = temp.replaceAll("\\)", substituto);
            temp = temp.replaceAll("-", substituto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return temp;
    }

    /**
     * Retira os caracteres . / , : - da string
     *
     * @param value - string a ser limpa
     * @param substituto - caractere que ficara no lugar dos caracteres
     * eliminados
     * @return - retorna a string limpa
     */
    public static String limpa(String value, String substituto) {
        String temp = value;
        try {
            temp = temp.replaceAll("\\.", substituto);
            temp = temp.replaceAll("/", substituto);
            temp = temp.replaceAll(",", substituto);
            temp = temp.replaceAll(":", substituto);
            temp = temp.replaceAll("-", substituto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return temp;
    }

    /**
     * Remove todos os acentos e cedilhas de uma string
     *
     * @param s - String que se deseja retirar os acentos
     * @return - String sem acentos
     */
    public static String removeAcento(final String s) {
        try {
            String acentuado = "çÇáéíóúýÁÉÍÓÚÝàèìòùÀÈÌÒÙãõñäëïöüÿÄËÏÖÜÃÕÑâêîôûÂÊÎÔÛ&ª";
            String semAcento = "cCaeiouyAEIOUYaeiouAEIOUaonaeiouyAEIOUAONaeiouAEIOU  ";
            char[] tabela;
            {
                tabela = new char[256];
                for (int i = 0; i < tabela.length; ++i) {
                    tabela[i] = (char) i;
                }
                for (int i = 0; i < acentuado.length(); ++i) {
                    tabela[acentuado.charAt(i)] = semAcento.charAt(i);
                }
            }
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < s.length(); ++i) {
                char ch = s.charAt(i);
                if (ch < 256) {
                    sb.append(tabela[ch]);
                } else {
                    sb.append(ch);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            return s;
        }
    }

    /**
     * Remove todos os acentos e cedilhas de todos os campos de um dado objeto.
     * Necessário realizar cast no objeto de retorno para o tipo original.
     *
     * @param objeto Objeto com campos que se deseja retirar os campos
     * @return object - Cópida do objeto com os campos sem acento.
     * @throws java.lang.Exception
     */
    public static Object removeAcentoObjeto(Object objeto) throws Exception {
        try {
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                if (f.getType().getTypeName().equals("java.lang.String")) {
                    f.setAccessible(true);
                    String remove = (String) f.get(objeto);
                    if (null != remove) {
                        f.set(object, removeAcento(remove));
                    }
                } else {
                    f.setAccessible(true);
                    f.set(object, f.get(objeto));
                }
            }
            return object;
        } catch (Exception e) {
            throw new Exception("Falha ao remover acentos\r\n" + e.getMessage());
        }
    }

    /**
     * Remove todos os acentos e cedilhas de todos os campos de um dado objeto.
     * Necessário realizar cast no objeto de retorno para o tipo original.
     *
     * @param objeto Objeto com campos que se deseja retirar os campos
     * @return object - Cópida do objeto com os campos sem acento.
     * @throws java.lang.Exception
     */
    public static Object removeAcentoObjetoMaiusculo(Object objeto) throws Exception {
        try {
            Object object;
            Class classDef = Class.forName(objeto.getClass().getCanonicalName());
            object = classDef.newInstance();
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field f : fields) {
                if (f.getType().getTypeName().equals("java.lang.String")) {
                    f.setAccessible(true);
                    String remove = (String) f.get(objeto);
                    if (null != remove) {
                        f.set(object, removeAcento(remove).toUpperCase());
                    }
                } else {
                    f.setAccessible(true);
                    f.set(object, f.get(objeto));
                }
            }
            return object;
        } catch (Exception e) {
            throw new Exception("Falha ao remover acentos\r\n" + e.getMessage());
        }
    }

    /**
     * Formata String conforme mascara passada
     *
     * @param texto - string a ser formatada
     * @param mascara - mascara
     * @return - string mascarada
     */
    public static String formatarString(String texto, String mascara) {
        try {
            MaskFormatter mf = new MaskFormatter(mascara);
            mf.setValueContainsLiteralCharacters(false);
            return mf.valueToString(texto);
        } catch (Exception e) {
            return texto;
        }
    }

    /**
     * Pega uma string no format 0.00 e devolve com mascara de moeda padrao
     * brasileiro
     *
     * @param entrada - string com o valor
     * @param Simbolo - se TRUE coloca R$ se FALSE devolve apenas o valor
     * @return - String formatada no formato 0.000,00
     * @throws Exception
     */
    public static String formatarStringMoeda(String entrada, boolean Simbolo) throws Exception {
        try {
            String valor, retorno;
            int casas = 0;
            BigDecimal mbd = new BigDecimal("0.00");
            mbd = mbd.add(new BigDecimal(entrada.replaceAll(",", ".")));
            valor = mbd.setScale(2, RoundingMode.UP).toString();
            casas = valor.length() - 3;
            retorno = valor.substring(casas).replace(".", ",");
            for (int i = 1; i <= casas; i++) {
                if (i % 3 == 0) {
                    retorno = "." + valor.substring(casas - i, casas - (i - 1)) + retorno;
                } else {
                    retorno = valor.substring(casas - i, casas - (i - 1)) + retorno;
                }
            }
            if (retorno.indexOf(".") == 0) {
                retorno = retorno.substring(1);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Erro ao formatarStringMoeda- " + e.getMessage());
        }
    }

    /**
     * Pega uma string no format 0.00 e devolve com mascara de moeda padrao
     * brasileiro
     *
     * @param entrada - string com o valor
     * @param idioma - se TRUE coloca R$ se FALSE devolve apenas o valor
     * @return - String formatada no formato 0.000,00
     * @throws Exception
     */
    public static String formatarStringMoeda(String entrada, String idioma) throws Exception {
        try {
            String valor, retorno;
            int casas;
            BigDecimal mbd = new BigDecimal("0.00");
            mbd = mbd.add(new BigDecimal(entrada.replaceAll(",", ".")));
            valor = mbd.setScale(2, RoundingMode.UP).toString();
            casas = valor.length() - 3;
            retorno = valor.substring(casas).replace(".", ",");
            for (int i = 1; i <= casas; i++) {
                if (i % 3 == 0) {
                    retorno = "." + valor.substring(casas - i, casas - (i - 1)) + retorno;
                } else {
                    retorno = valor.substring(casas - i, casas - (i - 1)) + retorno;
                }
            }
            if (retorno.indexOf(".") == 0) {
                retorno = retorno.substring(1);
            }
            switch (idioma) {
                case "en":
                    retorno = retorno.replace(",", "x");
                    retorno = retorno.replace(".", ",");
                    retorno = retorno.replace("x", ".");
                    retorno = "$" + retorno;
                    break;
                case "pt":
                    retorno = "R$" + retorno;
                default:

            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Erro ao formatarStringMoeda- " + e.getMessage());
        }
    }

    /**
     * Pega uma string no format 0.00 e devolve com mascara de moeda padrao
     * brasileiro
     *
     * @param entrada - string com o valor
     * @param idioma - se TRUE coloca R$ se FALSE devolve apenas o valor
     * @return - String formatada no formato 0.000,00
     * @throws Exception
     */
    public static String formatarStringMoeda(String entrada, Locale locale, String moeda) {
        try {
            NumberFormat nf = NumberFormat.getInstance(locale);
            nf.setMaximumFractionDigits(2);
            nf.setMinimumFractionDigits(2);
            String retorno = moeda + " " + nf.format(new BigDecimal(entrada));
            return retorno;
        } catch (Exception e) {
            e.printStackTrace();
            return entrada;
        }
    }

    public static String getSimboloMoeda(String moeda) {
        if (moeda == null) {
            moeda = "";
        }
        String simbolo;
        switch (moeda) {
            case "USD":
                simbolo = "US$";
                break;
            case "MXN":
//                simbolo = "Mex$";
                simbolo = "MXN$";
                break;
            case "EUR":
                simbolo = "€";
                break;
            case "GBP":
                simbolo = "£";
                break;
            case "CLP":
                simbolo = "CLP$";
                break;
            case "COP":
//                simbolo = "COL$";
                simbolo = "COP$";
                break;
            case "BRL":
            default:
                simbolo = "R$";
        }
        return simbolo;
    }

    public static String getMoedaEN(String moeda) {
        String simbolo;
        switch (moeda) {
            case "USD":
                simbolo = "dollars";
                break;
            case "MXN":
                simbolo = "Mexican pesos";
                break;
            case "CLP":
                simbolo = "Chilean pesos";
                break;
            case "COP":
                simbolo = "Colombian pesos";
                break;
            case "EUR":
                simbolo = "euros";
                break;
            case "GBP":
                simbolo = "pounds";
                break;
            case "BRL":
            default:
                simbolo = "reals";
        }
        return simbolo;
    }

    public static String getMoedaES(String moeda) {
        String simbolo;
        switch (moeda) {
            case "USD":
                simbolo = "dolares";
                break;
            case "MXN":
                simbolo = "pesos mexicanos";
                break;
            case "CLP":
                simbolo = "pesos chilenos";
                break;
            case "COP":
                simbolo = "pesos colombianos";
                break;
            case "EUR":
                simbolo = "euros";
                break;
            case "GBP":
                simbolo = "libras";
                break;
            case "BRL":
            default:
                simbolo = "reales";
        }
        return simbolo;
    }

    public static String getMoeda(String moeda) {
        String simbolo;
        switch (moeda) {
            case "USD":
                simbolo = "dólares";
                break;
            case "MXN":
                simbolo = "pesos mexicanos";
                break;
            case "CLP":
                simbolo = "pesos chilenos";
                break;
            case "COP":
                simbolo = "pesos colombianos";
                break;
            case "EUR":
                simbolo = "euros";
                break;
            case "GBP":
                simbolo = "libras";
                break;
            case "BRL":
            default:
                simbolo = "reais";
        }
        return simbolo;
    }

    /**
     * Retorna uma letra aleatoriamente
     *
     * @param Minuscula true- minuscula false- maiuscula
     * @return - String contendo a letra
     */
    public static String Letra_Aleatoria(boolean Minuscula) {
        String retorno;
        char[] alfabeto = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
        Random pad = new Random();
        retorno = String.valueOf(alfabeto[pad.nextInt(26)]);
        if (!Minuscula) {
            retorno.toUpperCase();
        }
        return retorno;
    }

    public static String RemoverZerosEsquerda(String valor) {
        String retorno = "";
        if (null == valor || valor.equals("")) {
            return retorno;
        }
        try {
            return String.valueOf(Integer.valueOf(valor));
        } catch (Exception e) {

        }

        for (int i = 0; i < valor.length(); i++) {
            if (valor.charAt(i) != '0') {
                retorno = valor.substring(i);
                break;
            }
        }
        return retorno;
    }

    /**
     *
     * @param texto
     * @param delimitadorInicial
     * @param delimitadorFinal
     * @return
     */
    public static String subStrIntoDelim(String texto, String delimitadorInicial, String delimitadorFinal) {
        StringBuilder textoValido = new StringBuilder();
        boolean encontrouPrimeiroDelimitador = false;
        try {
            for (int i = 0; i < texto.length(); i++) {
                if (encontrouPrimeiroDelimitador) {
                    if (!texto.substring(i).startsWith(delimitadorFinal)) {
                        textoValido.append(texto.subSequence(i, i + 1));
                    } else {
                        i = texto.length();
                    }
                } else {
                    if (texto.substring(i).startsWith(delimitadorInicial)) {
                        encontrouPrimeiroDelimitador = true;
                        i += delimitadorInicial.length() - 1;
                    }
                }
            }
            return textoValido.toString();
        } catch (NullPointerException npe) {
            return "";
        }
    }

    /**
     * Converte string de ISO para UTF8
     *
     * @param isoString - string
     * @return
     */
    public static String toUTF8(String isoString) {
        String utf8String = null;
        if (null != isoString && !isoString.equals("")) {
            try {
                byte[] stringBytesISO = isoString.getBytes("ISO-8859-1");
                utf8String = new String(stringBytesISO, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                // As we can't translate just send back the best guess.  
                //System.out.println("UnsupportedEncodingException is: " + e.getMessage());  
                utf8String = isoString;
            }
        } else {
            utf8String = isoString;
        }
        return utf8String;
    }

    /**
     * Recorta a String até o primeiro espaço
     *
     * @param value
     * @param inicio
     * @param caracteres - quantidade de caracteres
     * @return String já recortada
     */
    public static String RecortaAteEspaço(String value, int inicio, int caracteres) {
        if (value == null) {
            return "";
        }
        if (value.length() < inicio) {
            return "";
        }
        if (caracteres <= inicio) {
            return "";
        }
        caracteres = caracteres > value.length() ? value.length() : caracteres;
        String retorno = value.substring(inicio, caracteres);
        int posicao = retorno.indexOf(" ");
        if (posicao == -1) {
            return retorno;
        } else {
            return retorno.substring(0, posicao);
        }
    }
}

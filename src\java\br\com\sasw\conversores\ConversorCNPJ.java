/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import javax.faces.component.UIComponent;
import javax.faces.component.UIViewRoot;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorCNPJ")
public class ConversorCNPJ implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        UIViewRoot viewRoot = FacesContext.getCurrentInstance().getViewRoot();
        java.util.Locale locale = viewRoot.getLocale();
        String cnpj;
        try {
            switch (locale.getLanguage()) {
                case "en":
                    cnpj = value.toString();
                    break;
                default:
                    cnpj = value.toString().substring(0, 2) + "." + value.toString().substring(2, 5) + "."
                            + value.toString().substring(5, 8) + "/" + value.toString().substring(8, 12) + "-"
                            + value.toString().substring(12, 14);
            }
        } catch (Exception e) {
            cnpj = (String) value;
        }
        return cnpj;
    }
}

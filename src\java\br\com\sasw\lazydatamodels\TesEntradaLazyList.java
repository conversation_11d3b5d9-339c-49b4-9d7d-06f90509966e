/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Tesouraria.TesourariaController;
import Dados.Persistencia;
import SasBeans.TesEntrada;
import br.com.sasw.utils.Messages;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class TesEntradaLazyList extends LazyDataModel<TesEntrada> {

    private static final long serialVersionUID = 1L;
    private final TesourariaController tesourariaController;
    private List<TesEntrada> lista;

    public TesEntradaLazyList(Persistencia persistencia) {
        tesourariaController = new TesourariaController(persistencia);
    }

    @Override
    public List<TesEntrada> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        lista = new ArrayList<>();
        try {
            lista = tesourariaController.allTesEntradasPaginada(first, pageSize, filters);
            setRowCount(tesourariaController.contagemTesEntradas(filters));
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return lista;
    }

    /* Remove o '.0' do BigDecimal que às vezes aparece na conversão para string */
    private String toId(TesEntrada entrada) {
        return entrada.getCodFil().toBigInteger()
                + ";" + entrada.getGuia().toBigInteger()
                + ";" + entrada.getSerie();
    }

    @Override
    public Object getRowKey(TesEntrada entrada) {
        return toId(entrada);
    }

    @Override
    public TesEntrada getRowData(String rowKey) {
        for (TesEntrada entrada : lista) {
            if (toId(entrada).equals(rowKey)) {
                return entrada;
            }
        }
        return null;
    }
}

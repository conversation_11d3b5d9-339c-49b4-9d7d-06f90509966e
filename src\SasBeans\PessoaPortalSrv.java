/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class PessoaPortalSrv {

    private String Codigo;
    private String Servico;
    private String Ordem;
    private String Oper_Incl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    private String Descricao;

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getServico() {
        return Servico;
    }

    public void setServico(String Servico) {
        this.Servico = Servico;
    }

    public String getOrdem() {
        return Ordem;
    }

    public void setOrdem(String Ordem) {
        this.Ordem = Ordem;
    }

    public String getOper_Incl() {
        return Oper_Incl;
    }

    public void setOper_Incl(String Oper_Incl) {
        this.Oper_Incl = Oper_Incl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.Codigo);
        hash = 29 * hash + Objects.hashCode(this.Servico);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PessoaPortalSrv other = (PessoaPortalSrv) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        if (!Objects.equals(this.Servico, other.Servico)) {
            return false;
        }
        return true;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>

            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.3/Chart.min.js"></script>
            <ui:fragment rendered="#{solicitacaoSenhaMB.temPermissao}">
                <script
                    src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
                </script>
            </ui:fragment>

            <style>
                body, html{
                    background-color: #000;
                }

                body{
                    min-height:100% !important;
                    height:100% !important;
                }

                #TopoDash{
                    height:60px;
                    padding:0px !important;
                    font-family: 'Trebuchet MS', sans-serif;
                }

                #TopoDash .Empresa{
                    font-size:11pt;
                    font-weight: bold;
                    color:#FFF;
                    text-transform: uppercase;
                    display:block;
                    line-height: 15px;
                    margin-top: 6px;
                    margin-left:8px !important;
                }

                #TopoDash .Filial{
                    font-size:7pt;
                    font-weight: bold;
                    color:#FFF;
                    text-transform: uppercase;
                    display:block;
                    line-height: 8px;
                    margin-left:8px !important;
                    color:#AAA !important;
                }

                #TopoDash .Voltar{
                    background-color:orangered;
                    color:#FFF;
                    border-radius:12px;
                    padding:0px 8px 0px 8px;
                    border: 2px solid #ae2f00;
                    font-size:9pt;
                    cursor:pointer;
                    position:absolute;
                    left:6px;
                    bottom:-23px;
                    height:18px !important;
                    font-size:8pt !important;
                    width:80px;
                    text-align:center;
                }

                #TopoDash .ItemDash{
                    padding:0px 1px 0px 1px !important;
                    height:60px;
                }

                #TopoDash .ItemDash [ref="Titulo"]{
                    font-size:8pt !important;
                    font-weight: 500;
                    color:#BBB;
                    line-height:10px !important;
                    text-transform: uppercase;
                    width:100%;
                    text-align: left;
                    margin-top:12px;
                    padding-left: 14px !Important;
                }

                #TopoDash .ItemDash [ref="Valor"]{
                    font-size:14pt !important;
                    font-weight: 600;
                    color:#FFF;
                    width:100%;
                    text-align:center;
                    margin-top:1px;
                }

                #TopoDash .ItemDashDados{
                    background-image: linear-gradient(#000, #222);
                    height:60px;
                }

                .FundoChart{
                    padding: 5px 5px 20px 5px;
                    margin-bottom: 10px;
                    border-bottom: 1px dotted #BBB;
                    height:298px;
                    margin-top:20px;
                    background-image: linear-gradient(rgba(0,0,0,0.1), #202020);
                }

                .FundoChart table{
                    width:100%;
                }

                .DadosTab div {
                    font-size: 8pt;
                }

                .DadosTab div::-webkit-scrollbar-thumb:vertical {
                    background: #111 !important;
                }

                .DadosTab div:hover::-webkit-scrollbar-thumb:vertical {
                    background: #ff6a00 !important;
                    border: 2px solid #c35201;
                }

                ::-webkit-scrollbar-track-piece {
                    background: #222 !important;
                }

                .Titulo {
                    font-size: 12pt !important;
                    color: #EEE !important;
                    font-weight: bold !important;
                    line-height:18px !important;
                }

                .Titulo span{
                    font-size: 9pt !important;
                    color: #666 !important;
                    font-weight: bold !important;
                }

                ::-webkit-scrollbar {
                    width: 7px;
                    height: 8px;
                    font-weight: 500 !important;
                }

                ::-webkit-scrollbar-track-piece {
                    background: #ccc;
                    border-radius: 20px;
                }

                ::-webkit-scrollbar-thumb:horizontal {
                    width: 10%;
                    background: #999;
                    border-radius: 20px;
                    border-radius: 5px;
                }

                ::-webkit-scrollbar-thumb:vertical {
                    height: 5px;
                    background: #999;
                    border-radius: 25px;
                }

                .BoxMedia{
                    padding:0px 8px 0px 0px !important;
                }

                .BoxMedia div{
                    background-color:#222 !important;
                    border-radius:6px;
                    height:68px;
                }

                .BoxMedia [ref="Titulo"]{
                    color:#999;
                    font-size:10pt;
                    font-weight:500 !important;
                    width:100%;
                    text-align:center;
                    margin:8px 0px 0px 0px !important;
                    text-transform: uppercase;
                }

                .BoxMedia [ref="Valor"]{
                    color:#FFF;
                    font-size:18pt;
                    font-weight:500 !important;
                    width:100%;
                    text-align:center;
                    margin:0px !important;
                }

                @media only screen and (max-width: 3500px) and (min-width: 700px) {
                    [id*="grdResumoDet"] thead tr th{
                        background-color: #020202 !important;
                        color: #DDD !important;
                        border-color: #333 !important; 
                    }
                    
                    [id*="grdResumoDet"] tbody tr td{
                        background-color: rgba(0,0,0,0.89) !important;
                        color: #FFF !important;
                        border-color: #111 !important; 
                    }
                    
                    [id*="grdResumoDet"] tbody tr:nth-child(even) td{
                        background-color: rgba(0,0,0,0.92) !important;
                        color: #FFF !important;
                        border-color: #010101 !important; 
                    }
                    
                    [id*="grdResumoDet"] tbody{
                        max-height: 220px !important
                    }
                    
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    [id*="grdResumoDet"] tbody tr td{
                        background-color: rgba(0,0,0,0.89) !important;
                        color: #FFF !important;
                        border-color: #111 !important; 
                    }
                    
                    [id*="grdResumoDet"] tbody tr:nth-child(even) td{
                        background-color: rgba(0,0,0,0.92) !important;
                        color: #FFF !important;
                        border-color: #010101 !important; 
                    }
                    
                    [id*="grdResumoDet"] tbody tr td span:nth-child(1){
                        min-width: 120px !Important;
                        width: 120px !Important;
                        max-width: 120px !Important;
                        border-right:  thin solid #2F2F2F !Important;
                    }
                    
                    [id*="grdResumoDet"] tbody tr td span:nth-child(2){
                        min-width: calc(100% - 128px) !Important;
                        width: calc(100% - 128px) !Important;
                        max-width: calc(100% - 128px) !Important;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    
                    [id*="grdResumoDet"] tbody tr td:nth-child(2) span:nth-child(1){
                        float: left;
                    }
                    
                    [id*="grdResumoDet"] tbody tr td:nth-child(2) span:nth-child(2){
                        font-size: 10pt !important;
                        font-weight: bold !important;
                        white-space: nowrap;
                        float: left;
                    }
                    
                     [id*="grdResumoDet"] tbody tr td:nth-child(5) span:nth-child(2),
                    [id*="grdResumoDet"] tbody tr td:nth-child(6) span:nth-child(2){
                        font-size: 10pt !important;
                        font-weight: bold !important;
                    }
                    
                    [id*="grdResumoDet"] tbody tr td:not(first-child){
                        border-top: thin solid #333 !Important;
                    }
                    
                    .BoxMedia div{
                        margin-bottom:8px !important;
                    }

                    .BoxMedia [ref="Titulo"]{
                        margin:15px 0px 0px 0px !important;
                        font-size:8pt !important;
                    }

                    .BoxMedia [ref="Valor"]{
                        font-size:12pt !important;
                    }

                    #LinhaDivisao{
                        position:absolute;
                        top:36px;
                        width:100% !important;
                    }

                    #TopoDash .Voltar{
                        left:auto;
                        right:2px !important;
                        top:2px !important;
                        width:70px;
                    }
                }

                input{
                    background-color: transparent !important;
                    color: #FFF !important;
                    width: 100% !important;
                    border: none !important;
                    padding-left: 10px !important;
                    font-weight: 600 !important;
                    font-size: 12pt !important;
                    box-shadow: none !important;
                    outline: none !important;
                }

                .ui-datepicker-trigger{
                    margin-right: 20px !important;
                }

                select, label, div{
                    box-shadow: none !important;
                    outline: none !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{tesEntrada.carregamentoDashboard}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <header style="background-color:#000">
                <div id="TopoDash" class="col-md-12">
                    <h:form id="top" style="background-color: #000; height:100%;">
                        <div class="col-md-4 col-sm-4 col-xs-12" style="padding-left:0px !important;">
                            <label class="Empresa">#{tesEntrada.filialTela.descricao}</label>
                            <label class="Filial">#{tesEntrada.filialTela.endereco} - #{tesEntrada.filialTela.bairro} - #{tesEntrada.filialTela.cidade}/#{tesEntrada.filialTela.UF}</label>
                            <label class="Voltar" onclick="window.history.back();"><i class="fa fa-arrow-circle-left"></i>&nbsp;#{localemsgs.Voltar}</label>
                        </div>
                        <div class="col-md-8 col-sm-8 col-xs-12" style="padding:0px !important;">
                            <p:panel class="col-md-12 col-sm-12 col-xs-12" id="pnlFiltros" style="padding: 0px !important; background-color: transparent; border: none !important">
                                <div class="col-md-3 col-sm-3 col-xs-12 ItemDash">
                                    <div class="ItemDashDados">
                                        <label ref="Titulo">#{localemsgs.Filial}</label>
                                        <p:selectOneMenu id="cboFiliais" 
                                                         style="padding-left: 10px !important; border:none !important;bottom:6px !important; font-size:14pt; color:#FFF !important; font-weight:600 !important; text-align:center !important; font-family: 'Trebuchet MS'" 
                                                         value="#{tesEntrada.codFil}"
                                                         converter="omnifaces.SelectItemsConverter"
                                                         filter="false"
                                                         filterMatchMode="contains">
                                            <f:selectItem itemLabel="#{localemsgs.Todas}" noSelectionOption="true" />
                                            <f:selectItems value="#{tesEntrada.listaFiliais}" var="filial" itemValue="#{filial.codFil.toString()}" itemLabel="#{filial.descricao}" />                                            

                                            <p:ajax listener="#{tesEntrada.carregarTesourariaFuncionario(true)}" update="msgs cboTesourarias cboFuncionarios main" />
                                        </p:selectOneMenu>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-12 ItemDash">
                                    <div class="ItemDashDados">
                                        <label ref="Titulo">#{localemsgs.Tesouraria}</label>
                                        <p:selectOneMenu id="cboTesourarias" 
                                                         style="padding-left: 10px !important; border:none !important;bottom:6px !important; font-size:14pt; color:#FFF !important; font-weight:600 !important; text-align:center !important; font-family: 'Trebuchet MS'"
                                                         value="#{tesEntrada.codTesouraria}">
                                            <f:selectItem itemLabel="#{localemsgs.Todas}" itemValue="" noSelectionOption="true" />
                                            <f:selectItems value="#{tesEntrada.listaTesouraria}" var="tesouraria" itemValue="#{tesouraria.codigo}" itemLabel="#{tesouraria.NRed}" />

                                            <p:ajax listener="#{tesEntrada.carregarFuncionarios(true)}" update="msgs cboFuncionarios main" />
                                        </p:selectOneMenu>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-3 col-xs-12 ItemDash">
                                    <div class="ItemDashDados">
                                        <label ref="Titulo">#{localemsgs.Conferente}</label>
                                        <p:selectOneMenu id="cboFuncionarios" 
                                                         style="padding-left: 10px !important; border:none !important;bottom:6px !important; font-size:14pt; color:#FFF !important; font-weight:600 !important; text-align:center !important; font-family: 'Trebuchet MS'" 
                                                         value="#{tesEntrada.funcionarioSelecionado}">
                                            <f:selectItem itemLabel="#{localemsgs.Todos}" itemValue="" noSelectionOption="true" />
                                            <f:selectItems value="#{tesEntrada.listaFuncionarios}" var="funcionarioList" itemValue="#{funcionarioList.matr}" itemLabel="#{funcionarioList.nome}" />                                            

                                            <p:ajax listener="#{tesEntrada.carregarDashboard}" update="msgs main" />
                                        </p:selectOneMenu>
                                    </div>
                                </div>


                                <div class="col-md-3 col-sm-3 col-xs-12 ItemDash">
                                    <div class="ItemDashDados">
                                        <label ref="Titulo">#{localemsgs.Periodo}</label>
                                        <p:datePicker id="range" selectionMode="range" readonlyInput="true" 
                                                      value="#{tesEntrada.datasSelecionadas}"
                                                      monthNavigator="true" yearNavigator="true" yearRange="2009:2050"
                                                      pattern="#{mascaras.padraoData}" styleClass="calendario" showIcon="true"
                                                      converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                            <p:ajax event="dateSelect" listener="#{tesEntrada.selecionarDatas}" update="msgs main" />
                                        </p:datePicker>
                                    </div>
                                </div>
                            </p:panel>
                        </div>
                        <div id="LinhaDivisao" class="col-md-12" style="height: 3px; background-color:#202020; margin-top:2px;"></div>
                    </h:form>
                </div>
            </header>
            <h:form id="main" style="height:950px; padding:0px 10px 0px 15px; width:100% !important;background: linear-gradient(to bottom, #000, #131313) !important;">
                <p:panel id="tabela" style="padding:0px !important; min-height: 100% !important; background-color:transparent; border:none;">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 28px; margin-bottom: 10px; padding:0px 0px 10px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px !important">
                            <label class="Titulo">#{localemsgs.MediaProdDia}</label>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-money" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Quantidade}</label>
                                <label ref="Valor">#{tesEntrada.dashResumoTotalQtde}</label>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-clock-o" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Horas}</label>
                                <label ref="Valor">#{tesEntrada.dashResumoTotalHoras}</label>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-user" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Conferente}s</label>
                                <label ref="Valor">#{tesEntrada.dashResumoTotalConferentes}</label>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-6 BoxMedia">
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <label ref="Titulo"><i class="fa fa-usd" aria-hidden="true"></i>&nbsp;&nbsp;#{localemsgs.Valor}</label>
                                <label ref="Valor">#{tesEntrada.dashResumoTotalValor}</label>
                            </div>
                        </div>
                    </div>






                    <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnalisePorQtde}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartPorQtde" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnalisePorValor}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartPorValor" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnalisePorQtdeHora}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartPorHora" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnalisePorValorHora}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chartPorHoraValor" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>













                    <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseQtdeDia}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart1" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12" style="padding:0px 10px 0px 10px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="height: 340px !important; margin-bottom:20px !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseValorDia}
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="chart2" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>





                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0px 15px 0px 0px !important;">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoChart" style="margin-bottom:20px !important; border: none !important; background: transparent !important;">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AnaliseContagemDet}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="DadosTab" style="padding-top: 10px !Important;">
                                        <p:dataTable id="grdResumoDet" value="#{tesEntrada.listaDashGride}"
                                                     style="font-size: 12px; max-height: 295px !important" var="grdResumo" rowKey="#{grdResumo.nomeConferente}_#{grdResumo.filialDescricao}"
                                                     resizableColumns="false" selectionMode="single"
                                                     emptyMessage="#{localemsgs.SemRegistros}" reflow="true" scrollable="true"
                                                     styleClass="tabela" class="tabela" disabledSelection="true">
                                            <p:column headerText="#{localemsgs.Filial}" class="text-center">
                                                <h:outputText value="#{grdResumo.filialDescricao}"  class="text-center"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Conferente}" class="text-center">
                                                <h:outputText value="#{grdResumo.nomeConferente}"  class="text-center"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.QtdeDH}" class="text-center">
                                                <h:outputText value="#{grdResumo.qtdeCed}"  class="text-center" converter="conversor0"/>
                                            </p:column> 
                                            <p:column headerText="#{localemsgs.QtdeMD}" class="text-center">
                                                <h:outputText value="#{grdResumo.moedasValor}"  class="text-center" converter="conversor0"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.QuantidadeTotal}" class="text-center">
                                                <h:outputText value="#{grdResumo.qtde}"  class="text-center" converter="conversor0"/>
                                            </p:column> 
                                            <p:column headerText="#{localemsgs.Tempo}" class="text-center">
                                                <h:outputText value="#{grdResumo.tempoHora}"  class="text-center"/>
                                            </p:column> 
                                            <p:column headerText="#{localemsgs.ProdDH_Hr}" class="text-center">
                                                <h:outputText value="#{grdResumo.qtdeHora}"  class="text-center" converter="conversor0"/>
                                            </p:column> 
                                            <p:column headerText="#{localemsgs.ProdMD_Hr}" class="text-center">
                                                <h:outputText value="#{grdResumo.totalMoeda}"  class="text-center"  converter="conversor0"/>
                                            </p:column> 
                                            <p:column headerText="#{localemsgs.ValorRecebido}" class="text-center">
                                                <h:outputText value="#{grdResumo.valorRec}"  class="text-center"  converter="conversormoeda"/>
                                            </p:column> 
                                        </p:dataTable>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </p:panel>
                <script type="text/javascript">
                    // <![CDATA[
                    var CoresGrafico = new Array("#1E90FF", "#878BB6", "#4ACAB4", "#FF8153", "#FFEA88", "#6A5ACD", "#7CFC00", "#191970", "#ADFF2F", "#0000FF", "#00BFFF", "#6B8E23", "#4682B4", "#778899", "#008B8B", "#5F9EA0", "#808000", "#FF00FF", "#8B4513", "#DA70D6", "#CD853F", "#F4A400", "#F08080", "#8B0000", "#FA8072", "#FF0000", "#FFD700", "#4682B4", "#3F51B5", "#87CEFA", "#4682B4", "#B0C4DE", "#ADD8E6", "#B0E0E6", "#AFEEEE", "#00CED1", "#43CD80", "#2E8B57", "#9AFF9A", "#90EE90", "#7CCD7C", "#548B54", "#00FF7F", "#00EE76", "#00CD66", "#008B45", "#00FF00", "#00EE00", "#00CD00", "#008B00", "#7FFF00", "#76EE00", "#66CD00", "#458B00", "#C0FF3E", "#FF83FA", "#8B4789", "#FFBBFF", "#CD96CD", "#E066FF", "#B452CD", "#BF3EFF", "#9A32CD", "#68228B", "#7D26CD", "#551A8B", "#AB82FF", "#8968CD", "#5D478B", "#8B008B", "#8A2BE2", "#A020F0", "#9370DB", "#BA55D3", "#DDA0DD", "#FFF68F", "#CDC673", "#8B864E", "#FFEC8B", "#CDBE70", "#EEEED1", "#FFFF00", "#CDCD00", "#8B8B00", "#FFD700", "#CDAD00", "#FFC125", "#CD9B1D", "#FFB90F", "#CD950C", "#EEC900", "#EEB422", "#FF8247", "#CD6839", "#8B4726", "#CDAA7D", "#FFA54F", "#EE7942", "#EE9A49", "#FF7F24", "#FFD700", "#CD853F", "#F4A460", "#D2691E", "#FF8C00", "#FFA500", "#FF7F50", "#FF4500", "#CD661D", "#8B4513", "#EEC900", "#EE9A00", "#EE7600", "#EE9572", "#8B7500");

                    $(document).ready(function () {
                        CarregarGraficoEstatisticaPorDia();
                        /*CarregarGraficoEstatisticaPorRota();
                         CarregarGraficoEstatisticaPorHora();*/
                        CarregarGraficoEstatisticaAnalisePorQtdeValorHora();
                    });

                    function CarregarGraficoEstatisticaPorDia() {
                        var optionsChart = {
                            scaleGridLineColor: "#303030",
                            responsive: false
                        };

                        var optionsChartValor = {
                            scaleGridLineColor: "#303030",
                            responsive: false,
                            tooltips: {
                                callbacks: {
                                    label: function (tooltipItem, data) {
                                        return data['labels'][tooltipItem['index']] + ': R$ ' + data['datasets'][0]['data'][tooltipItem['index']].formatMoney(2, "", ".", ",").toString();
                                    }
                                }
                            }
                        };

                        // Quantidade por Dia
                        var lineChartData1 = {
                            labels: #{tesEntrada.estatisticaDiaQtdeLabel},
                            datasets: [
                                {
                                    label: "#{localemsgs.Quantidade}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#A020F0",
                                    lineTension: 0.1,
                                    pointBorderColor: "#A020F0",
                                    pointBackgroundColor: "#A020F0",
                                    pointStrokeColor: "transparent",
                                    data: #{tesEntrada.estatisticaDiaQtde},
                                    spanGaps: false
                                }
                            ]
                        };

                        var ctxDiaHora = $('#chart1')[0].getContext("2d");

                        try {
                            var ChartHoras = new Chart(ctxDiaHora, {
                                type: "line",
                                data: lineChartData1,
                                options: optionsChart
                            });

                        } catch (e) {
                            alert(e);
                        }




                        // Valor por Dia
                        var lineChartData2 = {
                            labels: #{tesEntrada.estatisticaDiaValorLabel},
                            datasets: [
                                {
                                    label: "#{localemsgs.Valor}",
                                    backgroundColor: "transparent",
                                    borderWidth: 0.7,
                                    borderColor: "#4cff00",
                                    lineTension: 0.1,
                                    pointBorderColor: "#4cff00",
                                    pointBackgroundColor: "#4cff00",
                                    pointStrokeColor: "transparent",
                                    data: #{tesEntrada.estatisticaDiaValor},
                                    spanGaps: false
                                }
                            ]
                        };

                        var ctxDiaHora = $('#chart2')[0].getContext("2d");

                        try {
                            var ChartHoras = new Chart(ctxDiaHora, {
                                type: "line",
                                data: lineChartData2,
                                options: optionsChartValor
                            });

                        } catch (e) {
                            alert(e);
                        }
                    }

                    function CarregarGraficoEstatisticaAnalisePorQtdeValorHora() {
                        // Análise por quantidade
                        var options = {
                            animateRotate: true,
                            animateScale: false,
                            responsive: false,
                            maintainAspectRatio: true,
                            percentageInnerCutout: 90,
                            segmentStrokeColor: 'transparent',
                            tooltips: {
                                callbacks: {
                                    label: function (tooltipItem, data) {
                                        return data['labels'][tooltipItem['index']] + ': ' + data['datasets'][0]['data'][tooltipItem['index']].toString().split('.')[0];
                                    }
                                }
                            }
                        };

                        var optionsValor = {
                            animateRotate: true,
                            animateScale: false,
                            responsive: false,
                            maintainAspectRatio: true,
                            percentageInnerCutout: 90,
                            segmentStrokeColor: 'transparent',
                            tooltips: {
                                callbacks: {
                                    label: function (tooltipItem, data) {
                                        return data['labels'][tooltipItem['index']] + ': R$ ' + data['datasets'][0]['data'][tooltipItem['index']].formatMoney(2, "", ".", ",").toString();
                                    }
                                }
                            }
                        };

                        var pieTipoServico = {
                            labels: #{tesEntrada.estatisticaQtdeLabel},
                            datasets: [
                                {
                                    data: #{tesEntrada.estatisticaQtde},
                                    backgroundColor: #{tesEntrada.estatisticaCores1}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxTiposServico = document.getElementById("chartPorQtde").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxTiposServico, {
                                type: 'pie',
                                data: pieTipoServico,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }

                        // Análise - Por Valor
                        var pieDataRamosTipo = {
                            labels: #{tesEntrada.estatisticaValorLabel},
                            datasets: [
                                {
                                    data: #{tesEntrada.estatisticaValor},
                                    backgroundColor: #{tesEntrada.estatisticaCores2}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxValoresRamosTipo = document.getElementById("chartPorValor").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxValoresRamosTipo, {
                                type: 'pie',
                                data: pieDataRamosTipo,
                                options: optionsValor
                            });
                        } catch (e) {
                            alert(e);
                        }

                        // Análise - Média Qtde por Hora
                        var pieDataTipoCliente = {
                            labels: #{tesEntrada.estatisticaHoraLabel},
                            datasets: [
                                {
                                    data: #{tesEntrada.estatisticaHora},
                                    backgroundColor: #{tesEntrada.estatisticaCores3}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxValoresTipoCliente = document.getElementById("chartPorHora").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxValoresTipoCliente, {
                                type: 'pie',
                                data: pieDataTipoCliente,
                                options: options
                            });
                        } catch (e) {
                            alert(e);
                        }

                        // Análise - Média Valor por Hora
                        var pieDataMediaValorHora = {
                            labels: #{tesEntrada.estatisticaHoraValorLabel},
                            datasets: [
                                {
                                    data: #{tesEntrada.estatisticaHoraValor},
                                    backgroundColor: #{tesEntrada.estatisticaCores4}
                                }]
                        };

                        Chart.defaults.global.legend.display = false;

                        var ctxValoresHoraValor = document.getElementById("chartPorHoraValor").getContext("2d");

                        try {
                            var myPieChart = new Chart(ctxValoresHoraValor, {
                                type: 'pie',
                                data: pieDataMediaValorHora,
                                options: optionsValor
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }

                    // ]]>
                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>
        </h:body>
    </f:view>
</html>
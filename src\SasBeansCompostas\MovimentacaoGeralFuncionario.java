/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Pessoa;
import SasBeans.TesCofresMov;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class MovimentacaoGeralFuncionario {

    private Pessoa pessoa;
    private List<TesCofresMov> movimentacoes;
    private BigDecimal valorColetas, valorDepositos;
    private BigDecimal totalColetas, totalDepositos;

    public MovimentacaoGeralFuncionario() {
        this.pessoa = new Pessoa();
        this.movimentacoes = new ArrayList<>();
        this.valorColetas = BigDecimal.ZERO;
        this.valorDepositos = BigDecimal.ZERO;
        this.totalColetas = BigDecimal.ZERO;
        this.totalDepositos = BigDecimal.ZERO;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public List<TesCofresMov> getMovimentacoes() {
        return movimentacoes;
    }

    public void setMovimentacoes(List<TesCofresMov> movimentacoes) {
        this.movimentacoes = movimentacoes;
    }

    public BigDecimal getTotalColetas() {
        return totalColetas;
    }

    public void setTotalColetas(BigDecimal totalColetas) {
        this.totalColetas = totalColetas;
    }

    public void setTotalColetas(String totalColetas) {
        try {
            this.totalColetas = new BigDecimal(totalColetas);
        } catch (Exception e) {
            this.totalColetas = BigDecimal.ZERO;
        }
    }

    public BigDecimal getTotalDepositos() {
        return totalDepositos;
    }

    public void setTotalDepositos(BigDecimal totalDepositos) {
        this.totalDepositos = totalDepositos;
    }

    public void setTotalDepositos(String totalDepositos) {
        try {
            this.totalDepositos = new BigDecimal(totalDepositos);
        } catch (Exception e) {
            this.totalDepositos = BigDecimal.ZERO;
        }
    }

    public BigDecimal getValorColetas() {
        return valorColetas;
    }

    public void setValorColetas(BigDecimal valorColetas) {
        this.valorColetas = valorColetas;
    }

    public void setValorColetas(String valorColetas) {
        try {
            this.valorColetas = new BigDecimal(valorColetas);
        } catch (Exception e) {
            this.valorColetas = BigDecimal.ZERO;
        }
    }

    public BigDecimal getValorDepositos() {
        return valorDepositos;
    }

    public void setValorDepositos(BigDecimal valorDepositos) {
        this.valorDepositos = valorDepositos;
    }

    public void setValorDepositos(String valorDepositos) {
        try {
            this.valorDepositos = new BigDecimal(valorDepositos);
        } catch (Exception e) {
            this.valorDepositos = BigDecimal.ZERO;
        }
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.pessoa);
        hash = 89 * hash + Objects.hashCode(this.movimentacoes);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MovimentacaoGeralFuncionario other = (MovimentacaoGeralFuncionario) obj;
        if (!Objects.equals(this.pessoa, other.pessoa)) {
            return false;
        }
        return true;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PedidoRefeicao {

    private BigDecimal Sequencia;
    private String CodFil;
    private String Codcli;
    private String Data;
    private String Solicitante;
    private String Obs;
    private String Operador;
    private String Hr_Alter;
    private String Dt_Alter;
    private List<PedidoRefeicaoItens> pedidoRefeicaoItens;

    private String Nred;
    private String QtdeCafe;
    private String QtdeAlmoco;
    private String QtdeJantar;
    private String QtdeCeia;
    private String TotalGeral;

    private String Situacao;

    public PedidoRefeicao() {
        this.Sequencia = BigDecimal.ZERO;
        this.CodFil = "";
        this.Data = "";
        this.Obs = "";
        this.Solicitante = "";
        this.Operador = "";
        this.Hr_Alter = "";
        this.Dt_Alter = "";
        this.pedidoRefeicaoItens = new ArrayList<>();

        this.Nred = "";
        this.QtdeCafe = "";
        this.QtdeAlmoco = "";
        this.QtdeJantar = "";
        this.QtdeCeia = "";
        this.TotalGeral = "";

        this.Situacao = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodcli() {
        return Codcli;
    }

    public void setCodcli(String Codcli) {
        this.Codcli = Codcli;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getSolicitante() {
        return Solicitante;
    }

    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    public List<PedidoRefeicaoItens> getPedidoRefeicaoItens() {
        return pedidoRefeicaoItens;
    }

    public void setPedidoRefeicaoItens(List<PedidoRefeicaoItens> pedidoRefeicaoItens) {
        this.pedidoRefeicaoItens = pedidoRefeicaoItens;
    }

    public String getNred() {
        return Nred;
    }

    public void setNred(String Nred) {
        this.Nred = Nred;
    }

    public String getQtdeCafe() {
        return QtdeCafe;
    }

    public void setQtdeCafe(String QtdeCafe) {
        this.QtdeCafe = QtdeCafe;
    }

    public String getQtdeAlmoco() {
        return QtdeAlmoco;
    }

    public void setQtdeAlmoco(String QtdeAlmoco) {
        this.QtdeAlmoco = QtdeAlmoco;
    }

    public String getQtdeJantar() {
        return QtdeJantar;
    }

    public void setQtdeJantar(String QtdeJantar) {
        this.QtdeJantar = QtdeJantar;
    }

    public String getQtdeCeia() {
        return QtdeCeia;
    }

    public void setQtdeCeia(String QtdeCeia) {
        this.QtdeCeia = QtdeCeia;
    }

    public String getTotalGeral() {
        return TotalGeral;
    }

    public void setTotalGeral(String TotalGeral) {
        this.TotalGeral = TotalGeral;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }
}

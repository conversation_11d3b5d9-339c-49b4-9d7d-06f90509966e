<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
<f:view locale="#{localeController.currentLocale}">

    <h:head>
        <link rel="icon" href="../assets/img/favicon.png"/>
        <title>#{localemsgs.SatMOB} </title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
        <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/style.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet"/>
        <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        <style>
            @media only screen and (max-width: 2000px) and (min-width: 701px) {
                .DataGrid [role="columnheader"] > span {
                    top: -4px !important;
                    position: relative !important;
                }
            }

            @media only screen and (max-width: 700px) and (min-width: 10px) {

                #divDadosFilial,
                #divDadosFilial div,
                .FilialNome,
                .FilialEndereco,
                .FilialBairroCidade {
                    min-width: 100% !important;
                    width: 100% !important;
                    max-width: 100% !important;
                    text-align: center !important;
                }

                .ui-paginator-top {
                    white-space: normal !important;
                }

                .tabela .ui-datatable-scrollable-body {
                    height: calc(100% - 6.5em);
                }
            }

            @media only screen and (max-width: 2000px) and (min-width: 701px) {
                .DataGrid {
                    width: 100% !important;
                    border: none !important
                }

                .DataGrid thead tr th,
                .DataGrid tbody tr td {
                    min-width: 100px;
                    max-width: 100px;
                    white-space: normal !important;
                    overflow-wrap: break-word !important;
                    word-wrap: break-word !important;
                    -webkit-hyphens: auto !important;
                    -ms-hyphens: auto !important;
                    hyphens: auto !important;
                }

                .DataGrid thead tr th:nth-child(3),
                .DataGrid tbody tr td:nth-child(3),
                .DataGrid thead tr th:nth-child(8),
                .DataGrid tbody tr td:nth-child(8) {
                    min-width: 120px !important;
                    max-width: 120px !important;
                }

                .DataGrid thead tr th,
                .DataGrid tbody tr td {
                    text-align: center !important;
                }
            }

            html, body {
                max-height: 100% !important;
                overflow: hidden !important;
            }

            #divCorporativo {
                bottom: 23px !important;
            }

            #corporativo {
                max-width: 18vw;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
            }

            #corporativo label[ref="lblCheck"] {
                font-size: 11px !important;
                min-width: 75px !important;
                font-weight: 500 !important;
            }

            footer .ui-chkbox-box {
                max-width: 12px !important;
                max-height: 12px !important;
            }

            .ui-dialog .ui-panel-content {
                height: auto !important;
            }

            #formContrato .ui-selectonemenu.ui-state-default {
                background: #fff !important;
            }

            #formContrato .ui-selectonemenu.ui-state-disabled {
                color: #555 !important;
                background: #f7f7f7 !important;
                opacity: 0.7 !important;
            }

            #formPesquisa .ui-radiobutton {
                background: transparent !important;
            }

            .contratovencidoRow, .DataGrid tbody tr.contratovencidoRow:hover td {
                color: green !important;
            }

            .tabela .ui-datatable-scrollable-body {
                height: calc(100% - 9em);
            }

            .botoesDataTable {
                width: 40px;
                margin-top: 8px;
                position: absolute;
                right: -14px;
                top: 50%;
                transform: translateY(-50%);
            }

            .infoSecundaria {
                color: gray;
            }

            .semPaddingLateral {
                padding-left: 0 !important;
                padding-right: 0 !important;
            }

            .debugbox * {
                border: thin dotted red;
            }

            .inputNumber > input {
                width: 100px;
            }

            .vbottom {
                float: none;
                display: inline-block;
                vertical-align: bottom;
            }

            .vbottom2 {
                float: none;
                display: table-cell;
                vertical-align: bottom;
            }

            .equal {
                display: flex;
                display: -webkit-flex;
                flex-wrap: wrap;
            }

            .bottomColumn {
                display: flex;
                flex-flow: column nowrap;
                justify-content: flex-end;
            }

            .asterisco::after {
                content: "(*)";
                color: red;
                font-weight: bold;
            }
        </style>
    </h:head>

    <h:body id="h">
        <p:growl id="msgs"/>

        <div id="body">
            <ui:include src="/tesouraria/entradas/header.xhtml"/>

            <!-- Tabela de numerários -->
            <ui:include src="/tesouraria/entradas/tabela.xhtml" id="DadosTabela"/>

            <!--Pesquisar numerários-->
            <ui:include src="/tesouraria/entradas/pesquisa.xhtml"/>
        </div>

        <ui:insert name="loading">
            <ui:include src="/assets/template/loading.xhtml"/>
        </ui:insert>

        <ui:include src="/tesouraria/entradas/footer.xhtml"/>
    </h:body>
</f:view>
</html>

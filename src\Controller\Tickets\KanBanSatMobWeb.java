/*
 */
package Controller.Tickets;

import Dados.Persistencia;
import SasBeans.KanBan;
import SasBeans.KanBanComent;
import SasBeans.KanBanMov;
import SasBeans.Pessoa;
import SasDaos.KanBanComentDao;
import SasDaos.KanBanDao;
import SasDaos.KanBanMovDao;
import SasDaos.PessoaDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.EnvioEmail;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class KanBanSatMobWeb {

    public List<Pessoa> carregaListaUsuarios(Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            return pessoaDao.listarUsuariosKanban(persistencia);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    public List<KanBan> listarTickets(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<KanBan> retorno;
            KanBanDao kanBanDao = new KanBanDao();
            retorno = kanBanDao.listarTickets(codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Contagem de tickets
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            KanBanDao kanBanDao = new KanBanDao();
            retorno = kanBanDao.totalTicketsMobWeb(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Contagem de tickets
     *
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagemPendentes(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            KanBanDao kanBanDao = new KanBanDao();
            return kanBanDao.contarTicketsPendentes(codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    public String ultimoAcesso(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            KanBanDao kanBanDao = new KanBanDao();
            String data = kanBanDao.dataUltimoAcesso(codPessoa, persistencia);
            PessoaDao pessoaDao = new PessoaDao();
            pessoaDao.ultAcPortal(codPessoa, persistencia);
            return data;
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem de tickets
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<KanBan> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<KanBan> retorno;
            KanBanDao kanBanDao = new KanBanDao();
            retorno = kanBanDao.listaPaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cadastra um novo ticket e envia email de notificação para o responsável e
     * para o Marcos;
     *
     * @param ticket
     * @param operador
     * @param persistencia
     * @throws Exception
     */
    public void cadastrar(KanBan ticket, String operador, Persistencia persistencia) throws Exception {
        try {
            KanBanDao kanBanDao = new KanBanDao();
            KanBanMovDao kanBanMovDao = new KanBanMovDao();
            ticket.setSequencia(new BigDecimal(kanBanDao.getMaxSequencia(persistencia)));
            kanBanDao.inserirTicket(ticket, persistencia);
            KanBanMov kanbanmov = new KanBanMov();
            kanbanmov.setCodPessoa(new BigDecimal(ticket.getCodPessoa()));
            kanbanmov.setData(LocalDate.now());
            kanbanmov.setDt_Alter(LocalDate.now());
            kanbanmov.setFase(ticket.getFase());
            kanbanmov.setHr_Alter(DataAtual.getDataAtual("HORA"));
            kanbanmov.setOperador(operador);
            kanbanmov.setSequencia(ticket.getSequencia());
            kanbanmov.setOrdem(new BigDecimal(kanBanMovDao.getMaxOrdem(ticket, persistencia)));
            kanBanMovDao.inserirMovimentacao(kanbanmov, persistencia);

            PessoaDao pessoadao = new PessoaDao();
            String emailResp = pessoadao.getEmail(kanbanmov.getCodPessoa(), persistencia);
            String nomeResp = pessoadao.obterNomeCompleto(kanbanmov.getCodPessoa(), persistencia);
            String emailMarcos = "<EMAIL>";

            EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", emailResp, nomeResp,
                    "<EMAIL>", "SatMOB", "NOVO TICKET",
                    mensagemCriacaoTicket(nomeResp, ticket.getSequencia().toBigInteger().toString(), operador, DataAtual.getDataAtual("TELA"), ticket.getDescricao(), ticket.getFase()),
                    nomeResp + ", um novo ticket #" + ticket.getSequencia().toBigInteger() + " foi criado por " + operador + " em " + DataAtual.getDataAtual("TELA") + ": " + ticket.getDescricao() + ". Status do ticket: " + ticket.getFase() + ".",
                    "sasw", "xNiadJEj9607", 587);
            if (!ticket.getCodPessoa().equals("1")) {
                EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", emailMarcos, "Marcos",
                        "<EMAIL>", "SatMOB", "NOVO TICKET",
                        mensagemCriacaoTicket(nomeResp, ticket.getSequencia().toBigInteger().toString(), operador, DataAtual.getDataAtual("TELA"), ticket.getDescricao(), ticket.getFase()),
                        "Um novo ticket foi criado para " + nomeResp + " por " + operador + " em " + DataAtual.getDataAtual("TELA") + ": " + ticket.getDescricao() + ". Status do ticket: " + ticket.getFase() + ".",
                        "sasw", "xNiadJEj9607", 587);
                EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", "<EMAIL>", "Fernanda",
                        "<EMAIL>", "SatMOB", "NOVO TICKET",
                        mensagemCriacaoTicket(nomeResp, ticket.getSequencia().toBigInteger().toString(), operador, DataAtual.getDataAtual("TELA"), ticket.getDescricao(), ticket.getFase()),
                        "Um novo ticket foi criado para " + nomeResp + " por " + operador + " em " + DataAtual.getDataAtual("TELA") + ": " + ticket.getDescricao() + ". Status do ticket: " + ticket.getFase() + ".",
                        "sasw", "xNiadJEj9607", 587);
            }
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Muda a fase e/ou o responsável de um ticket e envia um email para o
     * (novo) responsável e para o Marcos;
     *
     * @param ticket
     * @param operador
     * @param persistencia
     * @throws Exception
     */
    public void editar(KanBan ticket, String operador, Persistencia persistencia) throws Exception {
        try {
            KanBanDao kanBanDao = new KanBanDao();
            KanBanMovDao kanBanMovDao = new KanBanMovDao();
            kanBanDao.atualizarTicket(ticket, persistencia);
            KanBanMov kanbanmov = new KanBanMov();
            kanbanmov.setCodPessoa(new BigDecimal(ticket.getCodPessoa()));
            kanbanmov.setData(LocalDate.now());
            kanbanmov.setDt_Alter(LocalDate.now());
            kanbanmov.setFase(ticket.getFase());
            kanbanmov.setHr_Alter(DataAtual.getDataAtual("HORA"));
            kanbanmov.setOperador(operador);
            kanbanmov.setSequencia(ticket.getSequencia());
            kanbanmov.setOrdem(new BigDecimal(kanBanMovDao.getMaxOrdem(ticket, persistencia)));
            kanBanMovDao.inserirMovimentacao(kanbanmov, persistencia);

            PessoaDao pessoadao = new PessoaDao();
            String emailResp = pessoadao.getEmail(kanbanmov.getCodPessoa(), persistencia);
            String nomeResp = pessoadao.obterNomeCompleto(kanbanmov.getCodPessoa(), persistencia);
            String emailMarcos = "<EMAIL>";

            EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", emailResp, nomeResp,
                    "<EMAIL>", "SatMOB", "ATUALIZAÇÃO DE TICKET",
                    mensagemEdicaoTicket(nomeResp, ticket.getSequencia().toBigInteger().toString(), operador, DataAtual.getDataAtual("TELA"), ticket.getDescricao(), ticket.getFase()),
                    nomeResp + ", o ticket #" + ticket.getSequencia().toBigInteger() + " foi atualizado por " + operador + " em " + DataAtual.getDataAtual("TELA") + ": " + ticket.getDescricao() + ". Status do ticket: " + ticket.getFase() + ".",
                    "sasw", "xNiadJEj9607", 587);
            if (!ticket.getCodPessoa().equals("1")) {
                EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", emailMarcos, "Marcos",
                        "<EMAIL>", "SatMOB", "ATUALIZAÇÃO DE TICKET",
                        mensagemEdicaoTicket(nomeResp, ticket.getSequencia().toBigInteger().toString(), operador, DataAtual.getDataAtual("TELA"), ticket.getDescricao(), ticket.getFase()),
                        "O ticket #" + ticket.getSequencia().toBigInteger() + " foi atualizado para " + nomeResp + " por " + operador + " em " + DataAtual.getDataAtual("TELA") + ": " + ticket.getDescricao() + ". Status do ticket: " + ticket.getFase() + ".",
                        "sasw", "xNiadJEj9607", 587);
                EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", "<EMAIL>", "Fernanda",
                        "<EMAIL>", "SatMOB", "ATUALIZAÇÃO TICKET",
                        mensagemEdicaoTicket(nomeResp, ticket.getSequencia().toBigInteger().toString(), operador, DataAtual.getDataAtual("TELA"), ticket.getDescricao(), ticket.getFase()),
                        "O ticket #" + ticket.getSequencia().toBigInteger() + " foi atualizado para " + nomeResp + " por " + operador + " em " + DataAtual.getDataAtual("TELA") + ": " + ticket.getDescricao() + ". Status do ticket: " + ticket.getFase() + ".",
                        "sasw", "xNiadJEj9607", 587);
            }

        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todo o histórico de movimentação de um ticket;
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<KanBanMov> listarMovimentacaoTicket(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            KanBanMovDao kanBanMovDao = new KanBanMovDao();
            return kanBanMovDao.listaMovimentacao(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca um ticket a partir do número de sequência dele
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public KanBan buscarTicketSequencia(String sequencia, Persistencia persistencia) throws Exception {
        try {
            KanBanDao kanBanDao = new KanBanDao();
            return kanBanDao.buscarTicket(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os comentários para um ticket
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<KanBanComent> listarComentarios(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            KanBanComentDao kanbancomentdao = new KanBanComentDao();
            return kanbancomentdao.listaComentarios(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca a ordem do comentário e insere o comentário no banco
     *
     * @param comentario
     * @param persistencia
     * @throws Exception
     */
    public void inserirComentario(KanBanComent comentario, Persistencia persistencia) throws Exception {
        try {
            KanBanComentDao kanbancomentdao = new KanBanComentDao();
            comentario.setOrdem(kanbancomentdao.getOrdem(comentario.getSequencia(), persistencia));
            kanbancomentdao.inserirComentario(comentario, persistencia);
            PessoaDao pessoadao = new PessoaDao();
            String emailResp = pessoadao.getEmail(comentario.getCodPessoa(), persistencia);
            String nomeResp = pessoadao.obterNomeCompleto(comentario.getCodPessoa(), persistencia);

            EnvioEmail.enviaEmailFormatoHtmlSemAnexo("smtplw.com.br", emailResp, nomeResp,
                    "<EMAIL>", "SatMOB", "NOVO COMENTÁRIO TICKET #" + comentario.getSequencia().toBigInteger().toString(),
                    mensagemCriacaoComentario(nomeResp, comentario.getSequencia().toBigInteger().toString(), comentario.getOperador(), DataAtual.getDataAtual("TELA"), comentario.getDescricao()),
                    nomeResp + ", um novo comentário foi adicionado ao ticket #" + comentario.getSequencia().toBigInteger() + " por " + comentario.getOperador() + " em " + DataAtual.getDataAtual("TELA") + ": " + comentario.getDescricao() + ".",
                    "sasw", "xNiadJEj9607", 587);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa logar(String senha, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            return pessoadao.loginKanban(senha, persistencia);
        } catch (Exception e) {
            throw new Exception("kanban.falhageral<message>" + e.getMessage());
        }
    }

    public void cadastrarSenha(String email, String senha, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            String verificaEmail = pessoadao.verificaEmailKanban(email, persistencia);
            if (null == verificaEmail || verificaEmail.equals("")) {
                throw new Exception("emaiInexistenteUsuarioInativo");
            }
            String pwticket = pessoadao.verificaSenhaKanban(email, persistencia);
            if (null != pwticket && !pwticket.equals("")) {
                throw new Exception("SenhaJaCadastrada");
            }
            pwticket = pessoadao.verificaExisteSenhaKanban(senha, persistencia);
            if (senha.equals(pwticket)) {
                throw new Exception("SenhaInvalida");
            } else {
                pessoadao.cadastrarSenhaKanban(email, senha, persistencia);
            }
        } catch (Exception e) {
            switch (e.getMessage()) {
                case "emaiInexistenteUsuarioInativo":
                    throw new Exception("emaiInexistenteUsuarioInativo");
                case "SenhaInvalida":
                    throw new Exception("SenhaInvalida");
                case "SenhaJaCadastrada":
                    throw new Exception("SenhaJaCadastrada");
                default:
                    throw new Exception("kanban.falhageral<message>" + e.getMessage());
            }
        }
    }

    /**
     * Email de criação de um novo Ticket
     *
     * @param nomeResp - reponsável pelo Ticket
     * @param sequencia - número do Ticket
     * @param operador - pessoa que criou o Ticket
     * @param data - data de criação do Ticket
     * @param descricao - breve descrição da atividade
     * @param status - fase do ticket
     * @return
     */
    public String mensagemCriacaoTicket(String nomeResp, String sequencia, String operador, String data, String descricao, String status) {
        String mensagem
                = "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:600px\">"
                + "<tbody><tr>"
                + "<td bgcolor=\"#e93d3d\">"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td style=\"width:150px;text-align:left;\"><img src=\"http://www.satmob.com.br/imagens/logosatmobmail.png\" border=\"0\" alt=\"0\" style=\"display:block\"></td>"
                + "<td style=\"width:150px;text-align:right;\">&nbsp;</td>"
                + "</tr></tbody></table></td></tr><tr><td>"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:255px\">"
                + "<tbody></tbody></table></td></tr></tbody></table></td></tr><tr></tr><tr><td>"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:255px\">"
                + "<tbody><tr>"
                + "<td style=\"font-family:Arial;font-size:18px;font-weight:bold;text-align:center;color:#284235;\"></td>"
                + "</tr><tr>"
                + "<td align=\"center\" > "
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:680px\">"
                + "<tbody><tr>"
                + "<td style=\"font-family: tahoma; font-size: 15px; color: #284235; padding-top: 10px; padding-bottom: 10px; text-align: center;\">"
                + "<span style=\"text-align: center\"><strong>" + nomeResp + ", </strong></span>"
                + "<br>"
                + "<span style=\"text-align: center\"><strong>Um novo ticket #" + sequencia + " foi criado por " + operador + " em " + data + ": </strong></span>"
                + "<p> " + descricao + ""
                + "<br> Status do ticket: <strong>" + status + "</strong>"
                + "</td></tr><tr></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr>"
                + "<td bgcolor=\"#e93d3d\" style=\"padding:20px\" align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "<tbody><tr>"
                + "<td style=\"color:#164b6b;font-family:tahoma;font-weight:bold;font-size:22px;text-align:left;\">&nbsp;</td>"
                + "<td style=\"color:#fff;font-family:tahoma;font-weight:bold;font-size:22px;text-align:right;\">"
                + "<a href=\"https://mobile.sasw.com.br/SatMobWeb/kanbansatellite.xhtml?sequencia=" + sequencia + "\" style=\"text-decoration: none; color: white;\">"
                + "Veja o ticket aqui!</a> "
                + "</td><tr></tbody></table></td></tr><tr>"
                + "<td bgcolor=\"#ffffff\" style=\"padding:10px\" align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "<tbody><tr>"
                + "<td style=\"backpadding-top:5px;color:#164b6b;font-family:tahoma;font-size:10px;text-align:right;\">Powered by Satellite | www.gruposas.com.br</td>"
                + "<tr></tbody></table></td></tr></tbody></table>";
        return mensagem;
    }

    /**
     * Email de edição de um Ticket
     *
     * @param nomeResp - reponsável pelo Ticket
     * @param sequencia - número do Ticket
     * @param operador - pessoa que criou o Ticket
     * @param data - data de criação do Ticket
     * @param descricao - breve descrição da atividade
     * @param status - fase do ticket
     * @return
     */
    public String mensagemEdicaoTicket(String nomeResp, String sequencia, String operador, String data, String descricao, String status) {
        String mensagem
                = "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:600px\">"
                + "<tbody><tr>"
                + "<td bgcolor=\"#e93d3d\">"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td style=\"width:150px;text-align:left;\"><img src=\"http://www.satmob.com.br/imagens/logosatmobmail.png\" border=\"0\" alt=\"0\" style=\"display:block\"></td>"
                + "<td style=\"width:150px;text-align:right;\">&nbsp;</td>"
                + "</tr></tbody></table></td></tr><tr><td>"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:255px\">"
                + "<tbody></tbody></table></td></tr></tbody></table></td></tr><tr></tr><tr><td>"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:255px\">"
                + "<tbody><tr>"
                + "<td style=\"font-family:Arial;font-size:18px;font-weight:bold;text-align:center;color:#284235;\"></td>"
                + "</tr><tr>"
                + "<td align=\"center\" > "
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:680px\">"
                + "<tbody><tr>"
                + "<td style=\"font-family: tahoma; font-size: 15px; color: #284235; padding-top: 10px; padding-bottom: 10px; text-align: center;\">"
                + "<span style=\"text-align: center\"><strong>" + nomeResp + ", </strong></span>"
                + "<br>"
                + "<span style=\"text-align: center\"><strong>O ticket #" + sequencia + " foi atualizado por " + operador + " em " + data + ": </strong></span>"
                + "<p> " + descricao + ""
                + "<br> Status do ticket: <strong>" + status + "</strong>"
                + "</td></tr><tr></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr>"
                + "<td bgcolor=\"#e93d3d\" style=\"padding:20px\" align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "<tbody><tr>"
                + "<td style=\"color:#164b6b;font-family:tahoma;font-weight:bold;font-size:22px;text-align:left;\">&nbsp;</td>"
                + "<td style=\"color:#fff;font-family:tahoma;font-weight:bold;font-size:22px;text-align:right;\">"
                + "<a href=\"https://mobile.sasw.com.br/SatMobWeb/kanbansatellite.xhtml?sequencia=" + sequencia + "\" style=\"text-decoration: none; color: white;\">"
                + "Veja o ticket aqui!</a> "
                + "</td><tr></tbody></table></td></tr><tr>"
                + "<td bgcolor=\"#ffffff\" style=\"padding:10px\" align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "<tbody><tr>"
                + "<td style=\"backpadding-top:5px;color:#164b6b;font-family:tahoma;font-size:10px;text-align:right;\">Powered by Satellite | www.gruposas.com.br</td>"
                + "<tr></tbody></table></td></tr></tbody></table>";
        return mensagem;
    }

    public String mensagemCriacaoComentario(String nomeResp, String sequencia, String operador, String data, String descricao) {
        return "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:600px\">"
                + "<tbody><tr><td bgcolor=\"#e93d3d\">"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr>"
                + "<td style=\"width:150px;text-align:left;\"><img src=\"http://www.satmob.com.br/imagens/logosatmobmail.png\" border=\"0\" alt=\"0\" style=\"display:block\"></td>"
                + "<td style=\"width:150px;text-align:right;\">&nbsp;</td>"
                + "</tr></tbody></table></td></tr><tr><td>"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr><td align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:255px\">"
                + "<tbody></tbody></table></td></tr></tbody></table></td></tr><tr></tr><tr><td>"
                + "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%\">"
                + "<tbody><tr><td align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:255px\">"
                + "<tbody><tr>"
                + "<td style=\"font-family:Arial;font-size:18px;font-weight:bold;text-align:center;color:#284235;\"></td>"
                + "</tr><tr><td align=\"center\" > "
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:680px\">"
                + "<tbody><tr>"
                + "<td style=\"font-family: tahoma; font-size: 15px; color: #284235; padding-top: 10px; padding-bottom: 10px; text-align: center;\">"
                + "<span style=\"text-align: center\"><strong>" + nomeResp + ", </strong></span>"
                + "<br>"
                + "<span style=\"text-align: center\"><strong>Um novo comentário foi adicionado ao ticket #" + sequencia + " por " + operador + " em " + data + ": </strong></span>"
                + "<p> Comentário: <strong>" + descricao + "</strong>"
                + "</td></tr><tr></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr>"
                + "<td bgcolor=\"#e93d3d\" style=\"padding:20px\" align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "<tbody><tr>"
                + "<td style=\"color:#164b6b;font-family:tahoma;font-weight:bold;font-size:22px;text-align:left;\">&nbsp;</td>"
                + "<td style=\"color:#fff;font-family:tahoma;font-weight:bold;font-size:22px;text-align:right;\">"
                + "<a href=\"https://mobile.sasw.com.br/SatMobWeb/kanbansatellite.xhtml?sequencia=" + sequencia + "\" style=\"text-decoration: none; color: white;\">"
                + "Veja o comentário aqui!</a> "
                + "</td><tr></tbody></table></td></tr><tr>"
                + "<td bgcolor=\"#ffffff\" style=\"padding:10px\" align=\"center\" >"
                + "<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:575px\">"
                + "<tbody><tr>"
                + "<td style=\"backpadding-top:5px;color:#164b6b;font-family:tahoma;font-size:10px;text-align:right;\">Powered by Satellite | www.gruposas.com.br</td>"
                + "<tr></tbody></table></td></tr></tbody></table>";
    }
}

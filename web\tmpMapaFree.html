<html lang="pt-BR">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.4.3/css/ol.css" type="text/css">
    <style>
        #divMap {
            height: 100%;
            width: 100%
        }

        #divLoad {
            margin-top: 50px;
            font-family: Calibri;
            font-size: 16pt;
            color: #999;
            width: 100% !important;
            text-align: center !important;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.4.3/build/ol.js"></script>
    <title>Satellite - Posicao do cliente</title>
</head>
<body>
    <div id="divMap">
        <div id="divLoad">
            Carregando Mapa ...
        </div>
    </div>

    <script type="text/javascript">

        // Vari�veis Globais
        let map;
        let marcadorDourado = 'http://mobile.sasw.com.br:9080/satmobile/img/novo_iconedourado_M.png',
            latRecebida = ObterParamURL('lat'),
            lonRecebida = ObterParamURL('lon');


        if(ObterParamURL('marcador') && ObterParamURL('marcador') != '') 
            marcadorDourado = 'https://mobile.sasw.com.br:9091/satmobile/pins/' + ObterParamURL('marcador');

        // No Load da P�gina, verificar se existe parametro de lat/lon e carregar Mapa
        if (latRecebida && null != latRecebida && latRecebida != '' &&
            lonRecebida && null != lonRecebida && lonRecebida != '') {
            // Existem Par�metros
            CriarMapa('divMap', latRecebida, lonRecebida);
            AdicionarMarcador(marcadorDourado, latRecebida, lonRecebida);
            document.getElementById('divLoad').remove();
        }
        else {
            // N�o existem Par�metros
            document.getElementById('divLoad').innerHTML = 'Latitude e Longitude <b>obrigat&oacute;rios</b>!';
        }

        // Fun��o para adicionar marcador(es)
        function AdicionarMarcador(inIcon, inLat, inLon) {
            const iconFeature1 = new ol.Feature({
                geometry: new ol.geom.Point(ol.proj.fromLonLat([inLon, inLat])),
                name: 'icon'
            });

            const iconLayerSource = new ol.source.Vector({
                features: [iconFeature1]
            });

            const iconLayer = new ol.layer.Vector({
                source: iconLayerSource,
                style: new ol.style.Style({
                    image: new ol.style.Icon({
                        anchor: [0.5, 46],
                        anchorXUnits: 'fraction',
                        anchorYUnits: 'pixels',
                        src: inIcon
                    })
                })
            });

            map.addLayer(iconLayer);
        }

        // Fun��o para adicionar mapa(es)
        function CriarMapa(inDivID, inLat, inLon) {
            map = new ol.Map({
                target: inDivID,
                layers: [
                    new ol.layer.Tile({
                        source: new ol.source.OSM()
                    })
                ],
                view: new ol.View({
                    center: ol.proj.fromLonLat([inLon, inLat]),
                    zoom: 15
                })
            });
        }

        // Recuperar/GET dados da URL
        function ObterParamURL(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, "\\$&");
            var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        }

    </script>
</body>
</html>
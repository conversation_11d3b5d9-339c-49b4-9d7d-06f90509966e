/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class BBPedidoAgencia {

    private String agencia;
    private String subAgencia;
    private String dtColeta;
    private String dtEntrega;
    private List<BBPedidoMalote> listaMalotes;

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getSubAgencia() {
        return subAgencia;
    }

    public void setSubAgencia(String subAgencia) {
        this.subAgencia = subAgencia;
    }

    public List<BBPedidoMalote> getListaMalotes() {
        return listaMalotes;
    }

    public void setListaMalotes(List<BBPedidoMalote> listaMalotes) {
        this.listaMalotes = listaMalotes;
    }

    public String getDtColeta() {
        return dtColeta;
    }

    public void setDtColeta(String dtColeta) {
        this.dtColeta = dtColeta;
    }

    public String getDtEntrega() {
        return dtEntrega;
    }

    public void setDtEntrega(String dtEntrega) {
        this.dtEntrega = dtEntrega;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.agencia);
        hash = 97 * hash + Objects.hashCode(this.subAgencia);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BBPedidoAgencia other = (BBPedidoAgencia) obj;
        if (!Objects.equals(this.agencia, other.agencia)) {
            return false;
        }
        if (!Objects.equals(this.subAgencia, other.subAgencia)) {
            return false;
        }
        return true;
    }
}

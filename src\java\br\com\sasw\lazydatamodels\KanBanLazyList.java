package br.com.sasw.lazydatamodels;

import Controller.Tickets.KanBanSatMobWeb;
import Dados.Persistencia;
import SasBeans.KanBan;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class KanBanLazyList extends LazyDataModel<KanBan> {

    private static final long serialVersionUID = 1L;
    private List<KanBan> tickets;
    private final KanBanSatMobWeb kanBanmobweb;
    private Persistencia persistencia;

    public KanBanLazyList(Persistencia pst) {
        this.kanBanmobweb = new KanBanSatMobWeb();
        this.persistencia = pst;
    }

    @Override
    public List<KanBan> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            if (filters.isEmpty()) {
                filters = new HashMap();
                filters.put(" kanban.fase in (?,?,?,?,?,?) ", Arrays.asList("FAZER", "DESENVOLVER", "FILA P/ TESTE", "TESTE", "IMPLANTAR", ""));
            }
            this.tickets = this.kanBanmobweb.listagemPaginada(first, pageSize, filters, this.persistencia);

            // set the total of players
            setRowCount(this.kanBanmobweb.contagem(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.tickets;
    }

    @Override
    public Object getRowKey(KanBan ticket) {
        return ticket.getSequencia();
    }

    @Override
    public KanBan getRowData(String sequencia) {
        for (KanBan ticket : this.tickets) {
            if (ticket.getSequencia().equals(new BigDecimal(sequencia))) {
                return ticket;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

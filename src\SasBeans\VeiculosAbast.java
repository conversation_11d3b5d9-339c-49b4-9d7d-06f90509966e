package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class VeiculosAbast {

    private BigDecimal Sequencia;
    private int Numero;
    private LocalDate Data;
    private BigDecimal CodFil;
    private String CCusto;
    private BigDecimal CodProd;
    private int TipoComb;
    private BigDecimal KM;
    private BigDecimal KMRodados;
    private BigDecimal Qtde;
    private BigDecimal CustoUn;
    private BigDecimal ValorTotal;
    private BigDecimal CodPessoa;
    private BigDecimal Media;
    private String Obs;
    private String Operador;
    private LocalDate Dt_alter;
    private String Hr_alter;

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("1");
        }
    }

    public int getNumero() {
        return Numero;
    }

    public void setNumero(int Numero) {
        this.Numero = Numero;
    }

    public LocalDate getData() {
        return Data;
    }

    public void setData(LocalDate Data) {
        this.Data = Data;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public BigDecimal getCodProd() {
        return CodProd;
    }

    public void setCodProd(String CodProd) {
        try {
            this.CodProd = new BigDecimal(CodProd);
        } catch (Exception e) {
            this.CodProd = new BigDecimal("0");
        }
    }

    public int getTipoComb() {
        return TipoComb;
    }

    public void setTipoComb(int TipoComb) {
        this.TipoComb = TipoComb;
    }

    public BigDecimal getKM() {
        return KM;
    }

    public void setKM(String KM) {
        try {
            this.KM = new BigDecimal(KM);
        } catch (Exception e) {
            this.KM = new BigDecimal("0");
        }
    }

    public BigDecimal getKMRodados() {
        return KMRodados;
    }

    public void setKMRodados(String KMRodados) {
        try {
            this.KMRodados = new BigDecimal(KMRodados);
        } catch (Exception e) {
            this.KMRodados = new BigDecimal("0");
        }
    }

    public BigDecimal getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        try {
            this.Qtde = new BigDecimal(Qtde);
        } catch (Exception e) {
            this.Qtde = new BigDecimal("0");
        }
    }

    public BigDecimal getCustoUn() {
        return CustoUn;
    }

    public void setCustoUn(String CustoUn) {
        try {
            this.CustoUn = new BigDecimal(CustoUn);
        } catch (Exception e) {
            this.CustoUn = new BigDecimal("0");
        }
    }

    public BigDecimal getValorTotal() {
        return ValorTotal;
    }

    public void setValorTotal(String ValorTotal) {
        try {
            this.ValorTotal = new BigDecimal(ValorTotal);
        } catch (Exception e) {
            this.ValorTotal = new BigDecimal("0");
        }
    }

    public BigDecimal getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        try {
            this.CodPessoa = new BigDecimal(CodPessoa);
        } catch (Exception e) {
            this.CodPessoa = new BigDecimal("0");
        }
    }

    public BigDecimal getMedia() {
        return Media;
    }

    public void setMedia(String Media) {
        try {
            this.Media = new BigDecimal(Media);
        } catch (Exception e) {
            this.Media = new BigDecimal("0");
        }
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }
}

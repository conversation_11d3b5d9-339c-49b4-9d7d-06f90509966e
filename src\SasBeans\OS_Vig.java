package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class OS_Vig {

    private String OS;
    private String CodFil;
    private String DtInicio;
    private String DtFim;
    private String Descricao;
    private String TipoOS;
    private String CodSrv;
    private String Cliente;
    private String NRed;
    private String CliDst;
    private String NRedDst;
    private String CliFat;
    private String NRedFat;
    private String KM;
    private String KMTerra;
    private String Distancia;
    private String ViaCxF;
    private int DiasCst;
    private String EntregaSab;
    private String EntregaDom;
    private String EntregaFer;
    private String GTVQtde;
    private String GTVEstMin;
    private String Contrato;
    private String Aditivo;
    private String CCusto;
    private String MsgExtrato;
    private String OSGrp;
    private String Agrupador;
    private String Situacao;
    private String OperIncl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String SitFiscal;
    private String ObsFiscal;
    private String OperFiscal;
    private String Dt_Fiscal;
    private String Hr_Fiscal;
    private String GrpPagamento;
    private String GrpReajuste;

    private String Agencia;
    private String DiaFechaFat;
    private String DiaVencNF;
    private String Horario1;
    private String Horario2;

    private String Identif;

    // Info Cliente
    private String Nome;
    private String Banco;
    private String Endereco;
    private String Bairro;
    private String Cidade;
    private String UF;
    private String CEP;
    private String Fone1;
    private String Email;
    private String Contato;
    private String CNPJ;

    private String DescrContrato;
    
    public String getDiaFechaFat() {
        return DiaFechaFat;
    }

    public void setDiaFechaFat(String DiaFechaFat) {
        this.DiaFechaFat = DiaFechaFat;
    }

    public String getDiaVencNF() {
        return DiaVencNF;
    }

    public void setDiaVencNF(String DiaVencNF) {
        this.DiaVencNF = DiaVencNF;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getGrpPagamento() {
        return GrpPagamento;
    }

    public String getGrpReajuste() {
        return GrpReajuste;
    }

    public String getDtInicio() {
        return DtInicio;
    }

    public void setDtInicio(String DtInicio) {
        this.DtInicio = DtInicio;
    }

    public String getDtFim() {
        return DtFim;
    }

    public void setDtFim(String DtFim) {
        this.DtFim = DtFim;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getTipoOS() {
        return TipoOS;
    }

    public void setTipoOS(String TipoOS) {
        this.TipoOS = TipoOS;
    }

    public String getCodSrv() {
        return CodSrv;
    }

    public void setCodSrv(String CodSrv) {
        this.CodSrv = CodSrv;
    }

    public String getCliente() {
        return Cliente;
    }

    public void setCliente(String Cliente) {
        this.Cliente = Cliente;
    }

    public String getNRed() {
        return NRed;
    }

    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    public String getCliDst() {
        return CliDst;
    }

    public void setCliDst(String CliDst) {
        this.CliDst = CliDst;
    }

    public String getNRedDst() {
        return NRedDst;
    }

    public void setNRedDst(String NRedDst) {
        this.NRedDst = NRedDst;
    }

    public String getCliFat() {
        return CliFat;
    }

    public void setCliFat(String CliFat) {
        this.CliFat = CliFat;
    }

    public String getNRedFat() {
        return NRedFat;
    }

    public void setNRedFat(String NRedFat) {
        this.NRedFat = NRedFat;
    }

    public String getKM() {
        return KM;
    }

    public void setKM(String KM) {
        this.KM = KM;
    }

    public String getKMTerra() {
        return KMTerra;
    }

    public void setKMTerra(String KMTerra) {
        this.KMTerra = KMTerra;
    }

    public String getDistancia() {
        return Distancia;
    }

    public void setDistancia(String Distancia) {
        this.Distancia = Distancia;
    }

    public String getViaCxF() {
        return ViaCxF;
    }

    public void setViaCxF(String ViaCxF) {
        this.ViaCxF = ViaCxF;
    }

    public int getDiasCst() {
        return DiasCst;
    }

    public void setDiasCst(int DiasCst) {
        this.DiasCst = DiasCst;
    }

    public String getEntregaSab() {
        return EntregaSab;
    }

    public void setEntregaSab(String EntregaSab) {
        this.EntregaSab = EntregaSab;
    }

    public String getEntregaDom() {
        return EntregaDom;
    }

    public void setEntregaDom(String EntregaDom) {
        this.EntregaDom = EntregaDom;
    }

    public String getEntregaFer() {
        return EntregaFer;
    }

    public void setEntregaFer(String EntregaFer) {
        this.EntregaFer = EntregaFer;
    }

    public String getGTVQtde() {
        return GTVQtde;
    }

    public void setGTVQtde(String GTVQtde) {
        this.GTVQtde = GTVQtde;
    }

    public String getGTVEstMin() {
        return GTVEstMin;
    }

    public void setGTVEstMin(String GTVEstMin) {
        this.GTVEstMin = GTVEstMin;
    }

    public String getContrato() {
        return Contrato;
    }

    public void setContrato(String Contrato) {
        this.Contrato = Contrato;
    }

    public String getAditivo() {
        return Aditivo;
    }

    public void setAditivo(String Aditivo) {
        this.Aditivo = Aditivo;
    }

    public String getCCusto() {
        return CCusto;
    }

    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    public String getMsgExtrato() {
        return MsgExtrato;
    }

    public void setMsgExtrato(String MsgExtrato) {
        this.MsgExtrato = MsgExtrato;
    }

    public String getOSGrp() {
        return OSGrp;
    }

    public void setOSGrp(String OSGrp) {
        this.OSGrp = OSGrp;
    }

    public String getAgrupador() {
        return Agrupador;
    }

    public void setAgrupador(String Agrupador) {
        this.Agrupador = Agrupador;
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public String getOperIncl() {
        return OperIncl;
    }

    public void setOperIncl(String OperIncl) {
        this.OperIncl = OperIncl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getSitFiscal() {
        return SitFiscal;
    }

    public void setSitFiscal(String SitFiscal) {
        this.SitFiscal = SitFiscal;
    }

    public String getObsFiscal() {
        return ObsFiscal;
    }

    public void setObsFiscal(String ObsFiscal) {
        this.ObsFiscal = ObsFiscal;
    }

    public String getOperFiscal() {
        return OperFiscal;
    }

    public void setOperFiscal(String OperFiscal) {
        this.OperFiscal = OperFiscal;
    }

    public String getDt_Fiscal() {
        return Dt_Fiscal;
    }

    public void setDt_Fiscal(String Dt_Fiscal) {
        this.Dt_Fiscal = Dt_Fiscal;
    }

    public String getHr_Fiscal() {
        return Hr_Fiscal;
    }

    public void setHr_Fiscal(String Hr_Fiscal) {
        this.Hr_Fiscal = Hr_Fiscal;
    }

    public String getHorario1() {
        return Horario1;
    }

    public void setHorario1(String Horario1) {
        this.Horario1 = Horario1;
    }

    public String getHorario2() {
        return Horario2;
    }

    public void setHorario2(String Horario2) {
        this.Horario2 = Horario2;
    }

    public String getEndereco() {
        return Endereco;
    }

    public void setEndereco(String Endereco) {
        this.Endereco = Endereco;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getBanco() {
        return Banco;
    }

    public void setBanco(String Banco) {
        this.Banco = Banco;
    }

    public String getCEP() {
        return CEP;
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getFone1() {
        return Fone1;
    }

    public void setFone1(String Fone1) {
        this.Fone1 = Fone1;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getContato() {
        return Contato;
    }

    public void setContato(String Contato) {
        this.Contato = Contato;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getIdentif() {
        return Identif;
    }

    public void setIdentif(String Identif) {
        this.Identif = Identif;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDescrContrato() {
        return DescrContrato;
    }

    public void setDescrContrato(String DescrContrato) {
        this.DescrContrato = DescrContrato;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OS_Vig other = (OS_Vig) obj;
        if (!Objects.equals(this.OS, other.OS)) {
            return false;
        }
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "OS_Vig{" + "OS=" + OS + ", CodFil=" + CodFil + '}';
    }
}

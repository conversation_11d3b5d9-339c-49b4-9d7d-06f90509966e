/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 03-Jul-2020, 12:13:47
    Author     : SASWRichard
*/

header{
    margin-top: 0px !important;
}

#body{
    height: 100%;
    background: rgb(255,255,255);
    background: -moz-radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 50%, rgba(199,199,199,1) 100%);
    background: -webkit-radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 50%, rgba(199,199,199,1) 100%);
    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 50%, rgba(199,199,199,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#c7c7c7",GradientType=1);
}

#main{
    background-color: transparent !important;
}

.mensagem-topo{
    display: none !important;
    position:absolute;
    top: 0; 
    right: 0;
    z-index: 999;
    float:right;
}

.ui-panel-collapsed-h{
    height: 42px !important;
}

@media only screen and (max-width: 640px) and (min-width: 10px){
    #divTopoTela {
        margin-bottom: 0px !important;
    }
}

@media (max-width: 992px){
    .mensagem{
        display: none !important;
    }
    
    .mensagem-topo{
        width: 100vw;
        height: 100vh;
        display: inline !important;
    }
}
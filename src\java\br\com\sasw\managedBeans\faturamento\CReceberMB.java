/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.faturamento;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.CRecIntegra;
import SasBeans.CReceber;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Rotas;
import SasDaos.CRecebeDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "creceber")
@ViewScoped
public class CReceberMB implements Serializable {

    private Persistencia persistencia, satellite;
    private String codfil, banco, operador, nome, filialDesc, caminho, data1, data2, dataTela, log;
    private BigDecimal codPessoa;
    private ArquivoLog logerro;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private CReceber cReceberSelecionado, paradaSelecionada;
    private List<CReceber> allCReceber, resumoCaixa, resumoCaixaDet, formasPagto, listaResumoForVinculo;
    private List<CRecIntegra> listaTransacoes;
    private CRecIntegra transacao;
    private List<Rotas> rotasSelecao;
    private CRecebeDao cRecebeDao;
    private List<Date> datasSelecionadas;
    private Date dataInicio, dataFim;
    private String resumoCaixaTotalQtde, resumoCaixaTotalValor, rotaSelecionada, formaPagtoSelecionada;
    private boolean corporativo;

    public CReceberMB() throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        dataTela = DataAtual.getDataAtual("SQL");
        rotassatweb = new RotasSatWeb();
        cReceberSelecionado = new CReceber();
        allCReceber = new ArrayList<>();
        resumoCaixa = new ArrayList<>();
        resumoCaixaDet = new ArrayList<>();
        rotasSelecao = new ArrayList<>();
        listaResumoForVinculo = new ArrayList<>();
        listaTransacoes = new ArrayList<>();
        cRecebeDao = new CRecebeDao();
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataFim = c.getTime();

        c = Calendar.getInstance();
        c.setTime(dataFim);
        c.add(Calendar.DATE, -30);

        dataInicio = c.getTime();
        data1 = dataInicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data2 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataInicio); // data inicial
        datasSelecionadas.add(dataFim); // data final

        resumoCaixaTotalQtde = "0";
        resumoCaixaTotalValor = "0";

        corporativo = false;
    }

    public void Persistencias(Persistencia pstLocal) {
        try {
            this.persistencia = pstLocal;
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
            carregarTransacoes();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void selecionaDia() throws Exception {
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataFim = c.getTime();
        dataInicio = c.getTime();

        data1 = dataInicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data2 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataInicio); // data inicial
        datasSelecionadas.add(dataFim); // data final

        rotasSelecao = cRecebeDao.resumoCaixaRotas(this.data1, this.data2, !corporativo ? this.codfil : null, this.persistencia);
        formasPagto = cRecebeDao.resumoCaixaFormas(this.data1, this.data2, !corporativo ? this.codfil : null, this.persistencia);
    }

    public void pesquisarResumoCaixa() throws Exception {
        consultaResumoCaixa();
        consultaResumoCaixaDetalhes();
    }

    public void consultaResumoCaixa() throws Exception {
        try {
            this.resumoCaixa = new ArrayList<>();
            this.resumoCaixa = cRecebeDao.resumoCaixa(this.data1, this.data2, !corporativo ? this.codfil : null, this.rotaSelecionada, this.formaPagtoSelecionada, this.persistencia);
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public String calcularTotalResumoCaixa() {
        Double retorno = 0.0;

        for (CReceber item : this.resumoCaixa) {
            retorno += Double.valueOf(item.getValor().toPlainString());
        }

        return Double.toString(retorno);
    }

    public String calcularTotalResumoCaixaDet(String rota) {
        Double retorno = 0.0;

        for (CReceber item : this.resumoCaixaDet) {
            if (null != item.getRota() && item.getRota().equals(rota)) {
                retorno += Double.valueOf(item.getValor().toPlainString());
            }
        }

        return Double.toString(retorno);
    }
    
    public String calcularTotalResumoCaixaDetOneTotal(String rota) {
        Double retorno = 0.0;

        for (CReceber item : this.resumoCaixaDet) {
            if (null != item.getOnePayTotal() && null != item.getRota() && item.getRota().equals(rota)) {
                retorno += item.getOnePayTotal();
            }
        }

        return Double.toString(retorno);
    }
    
    public String calcularTotalResumoCaixaDetOneTaxa(String rota) {
        Double retorno = 0.0;

        for (CReceber item : this.resumoCaixaDet) {
            if (null != item.getOnePayTaxa() && null != item.getRota() && item.getRota().equals(rota)) {
                retorno += item.getOnePayTaxa();
            }
        }

        return Double.toString(retorno);
    }
    
    public String calcularTotalResumoCaixaDetOneLiquido(String rota) {
        Double retorno = 0.0;

        for (CReceber item : this.resumoCaixaDet) {
            if (null != item.getOnePayLiquido()&& null != item.getRota() && item.getRota().equals(rota)) {
                retorno += item.getOnePayLiquido();
            }
        }

        return Double.toString(retorno);
    }

    public void consultaResumoCaixaDetalhes() throws Exception {
        try {
            this.resumoCaixaDet = new ArrayList<>();
            this.resumoCaixaDet = cRecebeDao.resumoCaixaDet(this.data1, this.data2, !corporativo ? this.codfil : null, this.rotaSelecionada, this.formaPagtoSelecionada, this.persistencia);

            if (this.resumoCaixaDet.size() > 0) {
                this.resumoCaixaTotalQtde = Integer.toString(this.resumoCaixaDet.size());
                this.resumoCaixaTotalValor = "0";
                double valorTotalResumo = 0;

                for (CReceber creceber1 : this.resumoCaixaDet) {
                    valorTotalResumo += creceber1.getValor().doubleValue();
                }

                resumoCaixaTotalValor = Double.toString(valorTotalResumo);
            } else {
                this.resumoCaixaTotalQtde = "0";
                this.resumoCaixaTotalValor = "0";
            }
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void carregarGride() {
        try {
            this.allCReceber = cRecebeDao.listaContasReceber(this.dataTela, this.data1, this.data2, this.codfil, this.persistencia);

            Double Total = 0.0;
            String UltimaData = "";

            for (int I = 0; I < this.allCReceber.size(); I++) {
                if (!UltimaData.equals(this.allCReceber.get(I).getDtVenc())) {
                    Total = 0.0;
                }

                UltimaData = this.allCReceber.get(I).getDtVenc();
                Total += Double.parseDouble(this.allCReceber.get(I).getValor().toPlainString());
                this.allCReceber.get(I).setTotalValor(Total);
            }

            PrimeFaces.current().executeScript("setAtrasos();");
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                carregarGride();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarDatasResumoCaixa(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                rotasSelecao = cRecebeDao.resumoCaixaRotas(this.data1, this.data2, !corporativo ? this.codfil : null, this.persistencia);
                formasPagto = cRecebeDao.resumoCaixaFormas(this.data1, this.data2, !corporativo ? this.codfil : null, this.persistencia);

                rotaSelecionada = "";
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void carregarTransacoes() throws Exception {
        this.listaTransacoes = cRecebeDao.listaTransacoes(this.persistencia);
    }

    public void carregarTransacoesForVinculo() throws Exception {
        if (null != transacao) {
            this.listaResumoForVinculo = cRecebeDao.resumoCaixaForVinculo(this.data1, this.data2, this.codfil, this.rotaSelecionada, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgVinculoTransacao').show()");
        }
        else{
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("SelecioneTransacao"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }
    
    public void salvarVinculoTransacao(CReceber item) throws Exception{
        if (null != transacao &&
                null != item) {
            cRecebeDao.atualizarTransacao(item, this.transacao, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgVinculoTransacao').hide()");
            
            pesquisarResumoCaixa();
            carregarTransacoes();
            
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("DadosSalvosSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
        else{
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("SelecioneParada"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public Persistencia getSatellite() {
        return satellite;
    }

    public void setSatellite(Persistencia satellite) {
        this.satellite = satellite;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public CReceber getcReceberSelecionado() {
        return cReceberSelecionado;
    }

    public void setcReceberSelecionado(CReceber cReceberSelecionado) {
        this.cReceberSelecionado = cReceberSelecionado;
    }

    public List<CReceber> getAllCReceber() {
        return allCReceber;
    }

    public void setAllCReceber(List<CReceber> allCReceber) {
        this.allCReceber = allCReceber;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public List<CReceber> getResumoCaixa() {
        return resumoCaixa;
    }

    public void setResumoCaixa(List<CReceber> resumoCaixa) {
        this.resumoCaixa = resumoCaixa;
    }

    public List<CReceber> getResumoCaixaDet() {
        return resumoCaixaDet;
    }

    public void setResumoCaixaDet(List<CReceber> resumoCaixaDet) {
        this.resumoCaixaDet = resumoCaixaDet;
    }

    public CRecebeDao getcRecebeDao() {
        return cRecebeDao;
    }

    public void setcRecebeDao(CRecebeDao cRecebeDao) {
        this.cRecebeDao = cRecebeDao;
    }

    public String getResumoCaixaTotalQtde() {
        return resumoCaixaTotalQtde;
    }

    public void setResumoCaixaTotalQtde(String resumoCaixaTotalQtde) {
        this.resumoCaixaTotalQtde = resumoCaixaTotalQtde;
    }

    public String getResumoCaixaTotalValor() {
        return resumoCaixaTotalValor;
    }

    public void setResumoCaixaTotalValor(String resumoCaixaTotalValor) {
        this.resumoCaixaTotalValor = resumoCaixaTotalValor;
    }

    public String getRotaSelecionada() {
        return rotaSelecionada;
    }

    public void setRotaSelecionada(String rotaSelecionada) {
        this.rotaSelecionada = rotaSelecionada;
    }

    public String getFormaPagtoSelecionada() {
        return formaPagtoSelecionada;
    }

    public void setFormaPagtoSelecionada(String formaPagtoSelecionada) {
        this.formaPagtoSelecionada = formaPagtoSelecionada;
    }

    public boolean isCorporativo() {
        return corporativo;
    }

    public void setCorporativo(boolean corporativo) {
        this.corporativo = corporativo;
    }

    public List<Rotas> getRotasSelecao() {
        return rotasSelecao;
    }

    public void setRotasSelecao(List<Rotas> rotasSelecao) {
        this.rotasSelecao = rotasSelecao;
    }

    public List<CReceber> getFormasPagto() {
        return formasPagto;
    }

    public void setFormasPagto(List<CReceber> formasPagto) {
        this.formasPagto = formasPagto;
    }

    public List<CRecIntegra> getListaTransacoes() {
        return listaTransacoes;
    }

    public void setListaTransacoes(List<CRecIntegra> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public List<CReceber> getListaResumoForVinculo() {
        return listaResumoForVinculo;
    }

    public void setListaResumoForVinculo(List<CReceber> listaResumoForVinculo) {
        this.listaResumoForVinculo = listaResumoForVinculo;
    }

    public CRecIntegra getTransacao() {
        return transacao;
    }

    public void setTransacao(CRecIntegra transacao) {
        this.transacao = transacao;
    }

    public CReceber getParadaSelecionada() {
        return paradaSelecionada;
    }

    public void setParadaSelecionada(CReceber paradaSelecionada) {
        this.paradaSelecionada = paradaSelecionada;
    }

}

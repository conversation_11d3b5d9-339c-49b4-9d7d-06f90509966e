/*
*/
/* 
    Created on : Jan 4, 2017, 3:17:34 PM
    Author     : Richard
*/

#formPesquisaRapida .ui-radiobutton {
    background: transparent !important;
}

#divCorporativo{
    bottom:23px !important;
}

#corporativo {
    max-width: 10vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#corporativo label[ref="lblCheck"]{
    font-size:11px !important;
    min-width:95px !important;
    font-weight:500 !important;
}

footer .ui-chkbox-box {
    max-width: 12px !important;
    max-height: 12px !important;
}

#formCadastrar .ui-selectonemenu.ui-state-default {
    background: #fff !important;
}

#formCadastrar .ui-selectonemenu.ui-state-disabled {
    color: #555 !important;
    background: #f7f7f7 !important;
    opacity: 0.7 !important;
}

#formPesquisar .ui-radiobutton {
    background: transparent !important;
}

html, body{
    max-height:100% !important;
    overflow:hidden !important;
} 

.gridTitulo{
    width:100px !important;
    font-size:10pt !important;
    text-align:right !important;
    float:left;
    height:15px !important;
    margin-top:2px;
    color:#666 !important;
}

.gridValor{
    width:calc(100% - 100px) !important;
    max-width:calc(100% - 100px) !important;
    font-size:10pt !important;
    text-align:left !important;
    float:left;
    height:20px !important;
    padding-left:8px !important;
    font-weight:bold;
    white-space: nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    margin-top:2px;
    color:#202020 !important;
}

.Cliente{
    padding:0px !important;
}

.Cliente:not([ref="contratovencido"]):not([ref="I"]) .ItemCliente{
    padding:0px !important;
    width:100% !important;
    border:1px solid #CCC !important;
    background:linear-gradient(to bottom, #FAFAFA, #F6F6F6);
    margin-bottom:6px;
    margin-top:4px !important;
    border-radius:6px !important;
    box-shadow:2px 2px 3px #DDD;
    border-top:5px solid #3c8dbc !important;
}

.Cliente:not([ref="contratovencido"]):not([ref="I"]) [ref="status"]{
    color:#3c8dbc !important;
}

.Cliente[ref="I"] .ItemCliente{
    padding:0px !important;
    width:100% !important;
    border:1px solid #CCC !important;
    background:linear-gradient(to bottom, #FAFAFA, #F6F6F6);
    margin-bottom:6px;
    margin-top:4px !important;
    border-radius:6px !important;
    box-shadow:2px 2px 3px #DDD;
    border-top:5px solid red !important;
}

.Cliente[ref="I"] [ref="status"]{
    color:red !important;
}

.Cliente[ref="contratovencido"] .ItemCliente{
    padding:0px !important;
    width:100% !important;
    border:1px solid #CCC !important;
    background:linear-gradient(to bottom, #FAFAFA, #F6F6F6);
    margin-bottom:6px;
    margin-top:4px !important;
    border-radius:6px !important;
    box-shadow:2px 2px 3px #DDD;
    border-top:5px solid #f39c12 !important;
}

.Cliente[ref="contratovencido"] [ref="status"]{
    color:#f39c12 !important;
}

.Cliente [ref="status"]{
    text-shadow: 1px 1px #FFF !important;
}

.ui-grid-col-2{
    text-align:right !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {

    #divDadosFilial,
    #divDadosFilial div,
    .FilialNome,
    .FilialEndereco,
    .FilialBairroCidade{
        min-width:100% !important;
        width:100% !important;
        max-width:100% !important;
        text-align: center !important;
    }

    .gridTitulo,
    .gridValor{
        font-size:8pt !important;
    }

    .BotoesGrid{
        max-width:30% !important;
        float:left;
        top:0;
        right:0;
        bottom:0;
        left:0;
        margin-top:10px;
        margin-bottom:10px;
    }

    [src*="icone_redondo_email"]{
        margin-top:-8px !important;
    }

    [src*="satmob_guias"]{
        margin-top:-2px !important;
    }

    .ui-grid-col-2{
        text-align:left !important;
    }

    .ui-tabs-header{
        min-width:45px !important;
        width: 45px !important;
        max-width: 45px !important;
        font-size:7pt !important;
    }
	
	.ui-tabs-header[data-index="6"]{
        min-width:30px !important;
        width: 30px !important;
        max-width: 30px !important;
        font-size:7pt !important;
    }
	
	.ui-tabs-header[data-index="3"],
	.ui-tabs-header[data-index="4"]{
        min-width:35px !important;
        width: 35px !important;
        max-width: 35px !important;
        font-size:7pt !important;
    }

    .ui-tabs-header a{
        float:left;
        max-width:30px !important;
        width:30px !important;
        height: 30px !important;
        white-space: normal !important;
        padding:0px 2px 0px 2px !important;
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        -webkit-hyphens: auto !important;
        -ms-hyphens: auto !important;
        hyphens: auto !important;
        text-align:center !important;
        font-size:7px !important;
    }

    .ui-tabs-header[data-index="0"] a,
    .ui-tabs-header[data-index="1"] a,
    .ui-tabs-header[data-index="2"] a,
    .ui-tabs-header[data-index="3"] a,
    .ui-tabs-header[data-index="4"] a,
    .ui-tabs-header[data-index="5"] a{
        padding-top:5px !important;
        font-size:7px !important;
        width: 100% !important;
        text-align: center !important;
        height: 25px !important;
        margin: 0px !important;
    }

    .ui-tabs-header[data-index="6"] a{
        padding-top:8px !important;
        font-size:7px !important;
        width: 100% !important;
        text-align: center !important;
        height: 25px !important;
        margin: 0px !important;
    }
    
    .ui-tabs-header[data-index="0"] a,
    .ui-tabs-header[data-index="1"] a,
    .ui-tabs-header[data-index="2"] a,
    .ui-tabs-header[data-index="6"] a{
        top: 3px;
    }
    
    .ui-tabs-header[data-index="5"] a{
        top: -1px;
    }
    .ui-tabs-header[data-index="3"] a,
    .ui-tabs-header[data-index="4"] a{
        top: -6px;
    }
    
    [id*="formCadastrar:dlgCadastrar"] .ui-widget-content{
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }
}

[id*="formEmails"] .DataGrid thead,
[id*="formEmails"] .DataGrid thead tr{
    min-width:calc(100% + 10px) !important;
}

[id*="formEmails"] .DataGrid thead tr th,
[id*="formEmails"] .DataGrid tbody tr td{
    text-align:center !important;
}

.calendario .ui-inputfield{
    width: 100%;
}

.nao-emitida {
    color: blue !important;
}

.cancelada {
    color: red !important;
}

.emitida {
    color: green !important;
}

.upload.ui-fileupload {
    width: 100%;
    height: 100%;
    border: 1px solid grey;
    background: white;
    border-radius: 3px;
    text-align: center;
}

.upload .ui-fileupload-content {
    width: 100%;
    height: 70px;
    border: none;
    background: white;
    border-radius: 3px;
    overflow: auto;
}

.upload .ui-fileupload-content .ui-progressbar,
.upload .ui-fileupload-progress {
    width: 300px;
}

.ui-tabs .ui-tabs-nav.ui-widget-header li{
    background: #ccccff;
}
.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.cidade .ui-autocomplete-panel {
    width: 100% !important;
}
.cidade .ui-autocomplete-input{
    width: 100% !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.cadastrar{
    width: 90vh;
    min-width: 100%;
}

.cadastrarArquivo{
    width: 400px;
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
}

.ui-buttonset .ui-button{
    width: 50%;
}
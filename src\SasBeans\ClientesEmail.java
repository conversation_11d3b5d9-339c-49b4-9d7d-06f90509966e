/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ClientesEmail {

    private String codCli;
    private BigDecimal codFil;
    private String email, nome, operador;
    private LocalDate dt_Alter;
    private String hr_Alter;

    public ClientesEmail() {
        this.codFil = new BigDecimal("0");
        this.email = "";
        this.codCli = "";
        this.nome = "";
        this.operador = "";
        this.dt_Alter = null;
        this.hr_Alter = "";
    }

    public ClientesEmail(ClientesEmail copia) {
        this.codFil = copia.codFil;
        this.email = copia.email;
        this.codCli = copia.codCli;
        this.nome = copia.nome;
        this.operador = copia.operador;
        this.dt_Alter = copia.dt_Alter;
        this.hr_Alter = copia.hr_Alter;
    }

    public BigDecimal getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        try {
            this.codFil = new BigDecimal(codFil);
        } catch (Exception e) {
            this.codFil = new BigDecimal("0");
        }
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCodCli() {
        return codCli;
    }

    public void setCodCli(String codCli) {
        this.codCli = codCli;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getHr_Alter() {
        return hr_Alter;
    }

    public void setHr_Alter(String hr_Alter) {
        this.hr_Alter = hr_Alter;
    }

    public LocalDate getDt_Alter() {
        return dt_Alter;
    }

    public void setDt_Alter(LocalDate dt_Alter) {
        this.dt_Alter = dt_Alter;
    }
}

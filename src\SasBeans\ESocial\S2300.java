/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.ESocial;

/**
 *
 * <AUTHOR>
 */
public class S2300 {

    private int sucesso;
    private String evtTSVInicio_Id;
    private String ideEvento_indRetif;
    private String ideEvento_nrRecibo;
    private String ideEvento_tpAmb;
    private String ideEvento_procEmi;
    private String ideEvento_verProc;
    private String ideEmpregador_tpInsc;
    private String ideEmpregador_nrInsc;
    private String trabalhador_cpfTrab;
    private String trabalhador_nisTrab;
    private String trabalhador_nmTrab;
    private String trabalhador_sexo;
    private String trabalhador_racaCor;
    private String trabalhador_estCiv;
    private String trabalhador_grauInstr;
    private String trabalhador_nmSoc;
    private String nascimento_dtNascto;
    private String nascimento_codMunic;
    private String nascimento_uf;
    private String nascimento_paisNascto;
    private String nascimento_paisNac;
    private String nascimento_nmMae;
    private String nascimento_nmPai;
    private String CTPS_nrCtps;
    private String CTPS_serieCtps;
    private String CTPS_ufCtps;
    private String RIC_nrRic;
    private String RIC_orgaoEmissor;
    private String RIC_dtExped;
    private String RG_nrRg;
    private String RG_orgaoEmissor;
    private String RG_dtExped;
    private String RNE_nrRne;
    private String RNE_orgaoEmissor;
    private String RNE_dtExped;
    private String OC_nrOc;
    private String OC_orgaoEmissor;
    private String OC_dtExped;
    private String OC_dtValid;
    private String CNH_nrRegCnh;
    private String CNH_dtExped;
    private String CNH_ufCnh;
    private String CNH_dtValid;
    private String CNH_dtPriHab;
    private String CNH_categoriaCnh;
    private String brasil_tpLograd;
    private String brasil_dscLograd;
    private String brasil_nrLograd;
    private String brasil_complemento;
    private String brasil_bairro;
    private String brasil_cep;
    private String brasil_codMunic;
    private String brasil_uf;
    private String exterior_paisResid;
    private String exterior_dscLograd;
    private String exterior_nrLograd;
    private String exterior_complemento;
    private String exterior_bairro;
    private String exterior_nmCid;
    private String exterior_codPostal;
    private String trabEstrangeiro_dtChegada;
    private String trabEstrangeiro_classTrabEstrang;
    private String trabEstrangeiro_casadoBr;
    private String trabEstrangeiro_filhosBr;
    private String infoDeficiencia_defFisica;
    private String infoDeficiencia_defVisual;
    private String infoDeficiencia_defAuditiva;
    private String infoDeficiencia_defMental;
    private String infoDeficiencia_defIntelectual;
    private String infoDeficiencia_reabReadap;
    private String infoDeficiencia_observacao;
    private String dependente_tpDep;
    private String dependente_nmDep;
    private String dependente_dtNascto;
    private String dependente_cpfDep;
    private String dependente_depIRRF;
    private String dependente_depSF;
    private String dependente_incTrab;
    private String contato_fonePrinc;
    private String contato_foneAlternat;
    private String contato_emailPrinc;
    private String contato_emailAlternat;
    private String infoTSVInicio_cadIni;
    private String infoTSVInicio_codCateg;
    private String infoTSVInicio_dtInicio;
    private String infoTSVInicio_natAtividade;
    private String cargoFuncao_codCargo;
    private String cargoFuncao_codFuncao;
    private String remuneracao_vrSalFx;
    private String remuneracao_undSalFixo;
    private String remuneracao_dscSalVar;
    private String fgts_opcFGTS;
    private String fgts_dtOpcFGTS;
    private String infoDirigenteSindical_categOrig;
    private String infoDirigenteSindical_cnpjOrigem;
    private String infoDirigenteSindical_dtAdmOrig;
    private String infoDirigenteSindical_matricOrig;
    private String infoTrabCedido_categOrig;
    private String infoTrabCedido_cnpjCednt;
    private String infoTrabCedido_matricCed;
    private String infoTrabCedido_dtAdmCed;
    private String infoTrabCedido_tpRegTrab;
    private String infoTrabCedido_tpRegPrev;
    private String infoTrabCedido_infOnus;
    private String infoEstagiario_natEstagio;
    private String infoEstagiario_nivEstagio;
    private String infoEstagiario_areaAtuacao;
    private String infoEstagiario_nrApol;
    private String infoEstagiario_vlrBolsa;
    private String infoEstagiario_dtPrevTerm;
    private String instEnsino_cnpjInstEnsino;
    private String instEnsino_nmRazao;
    private String instEnsino_dscLograd;
    private String instEnsino_nrLograd;
    private String instEnsino_bairro;
    private String instEnsino_cep;
    private String instEnsino_codMunic;
    private String instEnsino_uf;
    private String ageIntegracao_cnpjAgntInteg;
    private String ageIntegracao_nmRazao;
    private String ageIntegracao_dscLograd;
    private String ageIntegracao_nrLograd;
    private String ageIntegracao_bairro;
    private String ageIntegracao_cep;
    private String ageIntegracao_codMunic;
    private String ageIntegracao_uf;
    private String supervisorEstagio_cpfSupervisor;
    private String supervisorEstagio_nmSuperv;
    private String afastamento_dtIniAfast;
    private String afastamento_codMotAfast;
    private String termino_dtTerm;

    public int getSucesso() {
        return sucesso;
    }

    public void setSucesso(int sucesso) {
        this.sucesso = sucesso;
    }

    public String getEvtTSVInicio_Id() {
        return evtTSVInicio_Id;
    }

    public void setEvtTSVInicio_Id(String evtTSVInicio_Id) {
        this.evtTSVInicio_Id = evtTSVInicio_Id;
    }

    public String getIdeEvento_indRetif() {
        return ideEvento_indRetif;
    }

    public void setIdeEvento_indRetif(String ideEvento_indRetif) {
        this.ideEvento_indRetif = ideEvento_indRetif;
    }

    public String getIdeEvento_nrRecibo() {
        return ideEvento_nrRecibo;
    }

    public void setIdeEvento_nrRecibo(String ideEvento_nrRecibo) {
        this.ideEvento_nrRecibo = ideEvento_nrRecibo;
    }

    public String getIdeEvento_tpAmb() {
        return ideEvento_tpAmb;
    }

    public void setIdeEvento_tpAmb(String ideEvento_tpAmb) {
        this.ideEvento_tpAmb = ideEvento_tpAmb;
    }

    public String getIdeEvento_procEmi() {
        return ideEvento_procEmi;
    }

    public void setIdeEvento_procEmi(String ideEvento_procEmi) {
        this.ideEvento_procEmi = ideEvento_procEmi;
    }

    public String getIdeEvento_verProc() {
        return ideEvento_verProc;
    }

    public void setIdeEvento_verProc(String ideEvento_verProc) {
        this.ideEvento_verProc = ideEvento_verProc;
    }

    public String getIdeEmpregador_tpInsc() {
        return ideEmpregador_tpInsc;
    }

    public void setIdeEmpregador_tpInsc(String ideEmpregador_tpInsc) {
        this.ideEmpregador_tpInsc = ideEmpregador_tpInsc;
    }

    public String getIdeEmpregador_nrInsc() {
        return ideEmpregador_nrInsc;
    }

    public void setIdeEmpregador_nrInsc(String ideEmpregador_nrInsc) {
        this.ideEmpregador_nrInsc = ideEmpregador_nrInsc;
    }

    public String getTrabalhador_cpfTrab() {
        return trabalhador_cpfTrab;
    }

    public void setTrabalhador_cpfTrab(String trabalhador_cpfTrab) {
        this.trabalhador_cpfTrab = trabalhador_cpfTrab;
    }

    public String getTrabalhador_nisTrab() {
        return trabalhador_nisTrab;
    }

    public void setTrabalhador_nisTrab(String trabalhador_nisTrab) {
        this.trabalhador_nisTrab = trabalhador_nisTrab;
    }

    public String getTrabalhador_nmTrab() {
        return trabalhador_nmTrab;
    }

    public void setTrabalhador_nmTrab(String trabalhador_nmTrab) {
        this.trabalhador_nmTrab = trabalhador_nmTrab;
    }

    public String getTrabalhador_sexo() {
        return trabalhador_sexo;
    }

    public void setTrabalhador_sexo(String trabalhador_sexo) {
        this.trabalhador_sexo = trabalhador_sexo;
    }

    public String getTrabalhador_racaCor() {
        return trabalhador_racaCor;
    }

    public void setTrabalhador_racaCor(String trabalhador_racaCor) {
        this.trabalhador_racaCor = trabalhador_racaCor;
    }

    public String getTrabalhador_estCiv() {
        return trabalhador_estCiv;
    }

    public void setTrabalhador_estCiv(String trabalhador_estCiv) {
        this.trabalhador_estCiv = trabalhador_estCiv;
    }

    public String getTrabalhador_grauInstr() {
        return trabalhador_grauInstr;
    }

    public void setTrabalhador_grauInstr(String trabalhador_grauInstr) {
        this.trabalhador_grauInstr = trabalhador_grauInstr;
    }

    public String getTrabalhador_nmSoc() {
        return trabalhador_nmSoc;
    }

    public void setTrabalhador_nmSoc(String trabalhador_nmSoc) {
        this.trabalhador_nmSoc = trabalhador_nmSoc;
    }

    public String getNascimento_dtNascto() {
        return nascimento_dtNascto;
    }

    public void setNascimento_dtNascto(String nascimento_dtNascto) {
        this.nascimento_dtNascto = nascimento_dtNascto;
    }

    public String getNascimento_codMunic() {
        return nascimento_codMunic;
    }

    public void setNascimento_codMunic(String nascimento_codMunic) {
        this.nascimento_codMunic = nascimento_codMunic;
    }

    public String getNascimento_uf() {
        return nascimento_uf;
    }

    public void setNascimento_uf(String nascimento_uf) {
        this.nascimento_uf = nascimento_uf;
    }

    public String getNascimento_paisNascto() {
        return nascimento_paisNascto;
    }

    public void setNascimento_paisNascto(String nascimento_paisNascto) {
        this.nascimento_paisNascto = nascimento_paisNascto;
    }

    public String getNascimento_paisNac() {
        return nascimento_paisNac;
    }

    public void setNascimento_paisNac(String nascimento_paisNac) {
        this.nascimento_paisNac = nascimento_paisNac;
    }

    public String getNascimento_nmMae() {
        return nascimento_nmMae;
    }

    public void setNascimento_nmMae(String nascimento_nmMae) {
        this.nascimento_nmMae = nascimento_nmMae;
    }

    public String getNascimento_nmPai() {
        return nascimento_nmPai;
    }

    public void setNascimento_nmPai(String nascimento_nmPai) {
        this.nascimento_nmPai = nascimento_nmPai;
    }

    public String getCTPS_nrCtps() {
        return CTPS_nrCtps;
    }

    public void setCTPS_nrCtps(String CTPS_nrCtps) {
        this.CTPS_nrCtps = CTPS_nrCtps;
    }

    public String getCTPS_serieCtps() {
        return CTPS_serieCtps;
    }

    public void setCTPS_serieCtps(String CTPS_serieCtps) {
        this.CTPS_serieCtps = CTPS_serieCtps;
    }

    public String getCTPS_ufCtps() {
        return CTPS_ufCtps;
    }

    public void setCTPS_ufCtps(String CTPS_ufCtps) {
        this.CTPS_ufCtps = CTPS_ufCtps;
    }

    public String getRIC_nrRic() {
        return RIC_nrRic;
    }

    public void setRIC_nrRic(String RIC_nrRic) {
        this.RIC_nrRic = RIC_nrRic;
    }

    public String getRIC_orgaoEmissor() {
        return RIC_orgaoEmissor;
    }

    public void setRIC_orgaoEmissor(String RIC_orgaoEmissor) {
        this.RIC_orgaoEmissor = RIC_orgaoEmissor;
    }

    public String getRIC_dtExped() {
        return RIC_dtExped;
    }

    public void setRIC_dtExped(String RIC_dtExped) {
        this.RIC_dtExped = RIC_dtExped;
    }

    public String getRG_nrRg() {
        return RG_nrRg;
    }

    public void setRG_nrRg(String RG_nrRg) {
        this.RG_nrRg = RG_nrRg;
    }

    public String getRG_orgaoEmissor() {
        return RG_orgaoEmissor;
    }

    public void setRG_orgaoEmissor(String RG_orgaoEmissor) {
        this.RG_orgaoEmissor = RG_orgaoEmissor;
    }

    public String getRG_dtExped() {
        return RG_dtExped;
    }

    public void setRG_dtExped(String RG_dtExped) {
        this.RG_dtExped = RG_dtExped;
    }

    public String getRNE_nrRne() {
        return RNE_nrRne;
    }

    public void setRNE_nrRne(String RNE_nrRne) {
        this.RNE_nrRne = RNE_nrRne;
    }

    public String getRNE_orgaoEmissor() {
        return RNE_orgaoEmissor;
    }

    public void setRNE_orgaoEmissor(String RNE_orgaoEmissor) {
        this.RNE_orgaoEmissor = RNE_orgaoEmissor;
    }

    public String getRNE_dtExped() {
        return RNE_dtExped;
    }

    public void setRNE_dtExped(String RNE_dtExped) {
        this.RNE_dtExped = RNE_dtExped;
    }

    public String getOC_nrOc() {
        return OC_nrOc;
    }

    public void setOC_nrOc(String OC_nrOc) {
        this.OC_nrOc = OC_nrOc;
    }

    public String getOC_orgaoEmissor() {
        return OC_orgaoEmissor;
    }

    public void setOC_orgaoEmissor(String OC_orgaoEmissor) {
        this.OC_orgaoEmissor = OC_orgaoEmissor;
    }

    public String getOC_dtExped() {
        return OC_dtExped;
    }

    public void setOC_dtExped(String OC_dtExped) {
        this.OC_dtExped = OC_dtExped;
    }

    public String getOC_dtValid() {
        return OC_dtValid;
    }

    public void setOC_dtValid(String OC_dtValid) {
        this.OC_dtValid = OC_dtValid;
    }

    public String getCNH_nrRegCnh() {
        return CNH_nrRegCnh;
    }

    public void setCNH_nrRegCnh(String CNH_nrRegCnh) {
        this.CNH_nrRegCnh = CNH_nrRegCnh;
    }

    public String getCNH_dtExped() {
        return CNH_dtExped;
    }

    public void setCNH_dtExped(String CNH_dtExped) {
        this.CNH_dtExped = CNH_dtExped;
    }

    public String getCNH_ufCnh() {
        return CNH_ufCnh;
    }

    public void setCNH_ufCnh(String CNH_ufCnh) {
        this.CNH_ufCnh = CNH_ufCnh;
    }

    public String getCNH_dtValid() {
        return CNH_dtValid;
    }

    public void setCNH_dtValid(String CNH_dtValid) {
        this.CNH_dtValid = CNH_dtValid;
    }

    public String getCNH_dtPriHab() {
        return CNH_dtPriHab;
    }

    public void setCNH_dtPriHab(String CNH_dtPriHab) {
        this.CNH_dtPriHab = CNH_dtPriHab;
    }

    public String getCNH_categoriaCnh() {
        return CNH_categoriaCnh;
    }

    public void setCNH_categoriaCnh(String CNH_categoriaCnh) {
        this.CNH_categoriaCnh = CNH_categoriaCnh;
    }

    public String getBrasil_tpLograd() {
        return brasil_tpLograd;
    }

    public void setBrasil_tpLograd(String brasil_tpLograd) {
        this.brasil_tpLograd = brasil_tpLograd;
    }

    public String getBrasil_dscLograd() {
        return brasil_dscLograd;
    }

    public void setBrasil_dscLograd(String brasil_dscLograd) {
        this.brasil_dscLograd = brasil_dscLograd;
    }

    public String getBrasil_nrLograd() {
        return brasil_nrLograd;
    }

    public void setBrasil_nrLograd(String brasil_nrLograd) {
        this.brasil_nrLograd = brasil_nrLograd;
    }

    public String getBrasil_complemento() {
        return brasil_complemento;
    }

    public void setBrasil_complemento(String brasil_complemento) {
        this.brasil_complemento = brasil_complemento;
    }

    public String getBrasil_bairro() {
        return brasil_bairro;
    }

    public void setBrasil_bairro(String brasil_bairro) {
        this.brasil_bairro = brasil_bairro;
    }

    public String getBrasil_cep() {
        return brasil_cep;
    }

    public void setBrasil_cep(String brasil_cep) {
        this.brasil_cep = brasil_cep;
    }

    public String getBrasil_codMunic() {
        return brasil_codMunic;
    }

    public void setBrasil_codMunic(String brasil_codMunic) {
        this.brasil_codMunic = brasil_codMunic;
    }

    public String getBrasil_uf() {
        return brasil_uf;
    }

    public void setBrasil_uf(String brasil_uf) {
        this.brasil_uf = brasil_uf;
    }

    public String getExterior_paisResid() {
        return exterior_paisResid;
    }

    public void setExterior_paisResid(String exterior_paisResid) {
        this.exterior_paisResid = exterior_paisResid;
    }

    public String getExterior_dscLograd() {
        return exterior_dscLograd;
    }

    public void setExterior_dscLograd(String exterior_dscLograd) {
        this.exterior_dscLograd = exterior_dscLograd;
    }

    public String getExterior_nrLograd() {
        return exterior_nrLograd;
    }

    public void setExterior_nrLograd(String exterior_nrLograd) {
        this.exterior_nrLograd = exterior_nrLograd;
    }

    public String getExterior_complemento() {
        return exterior_complemento;
    }

    public void setExterior_complemento(String exterior_complemento) {
        this.exterior_complemento = exterior_complemento;
    }

    public String getExterior_bairro() {
        return exterior_bairro;
    }

    public void setExterior_bairro(String exterior_bairro) {
        this.exterior_bairro = exterior_bairro;
    }

    public String getExterior_nmCid() {
        return exterior_nmCid;
    }

    public void setExterior_nmCid(String exterior_nmCid) {
        this.exterior_nmCid = exterior_nmCid;
    }

    public String getExterior_codPostal() {
        return exterior_codPostal;
    }

    public void setExterior_codPostal(String exterior_codPostal) {
        this.exterior_codPostal = exterior_codPostal;
    }

    public String getTrabEstrangeiro_dtChegada() {
        return trabEstrangeiro_dtChegada;
    }

    public void setTrabEstrangeiro_dtChegada(String trabEstrangeiro_dtChegada) {
        this.trabEstrangeiro_dtChegada = trabEstrangeiro_dtChegada;
    }

    public String getTrabEstrangeiro_classTrabEstrang() {
        return trabEstrangeiro_classTrabEstrang;
    }

    public void setTrabEstrangeiro_classTrabEstrang(String trabEstrangeiro_classTrabEstrang) {
        this.trabEstrangeiro_classTrabEstrang = trabEstrangeiro_classTrabEstrang;
    }

    public String getTrabEstrangeiro_casadoBr() {
        return trabEstrangeiro_casadoBr;
    }

    public void setTrabEstrangeiro_casadoBr(String trabEstrangeiro_casadoBr) {
        this.trabEstrangeiro_casadoBr = trabEstrangeiro_casadoBr;
    }

    public String getTrabEstrangeiro_filhosBr() {
        return trabEstrangeiro_filhosBr;
    }

    public void setTrabEstrangeiro_filhosBr(String trabEstrangeiro_filhosBr) {
        this.trabEstrangeiro_filhosBr = trabEstrangeiro_filhosBr;
    }

    public String getInfoDeficiencia_defFisica() {
        return infoDeficiencia_defFisica;
    }

    public void setInfoDeficiencia_defFisica(String infoDeficiencia_defFisica) {
        this.infoDeficiencia_defFisica = infoDeficiencia_defFisica;
    }

    public String getInfoDeficiencia_defVisual() {
        return infoDeficiencia_defVisual;
    }

    public void setInfoDeficiencia_defVisual(String infoDeficiencia_defVisual) {
        this.infoDeficiencia_defVisual = infoDeficiencia_defVisual;
    }

    public String getInfoDeficiencia_defAuditiva() {
        return infoDeficiencia_defAuditiva;
    }

    public void setInfoDeficiencia_defAuditiva(String infoDeficiencia_defAuditiva) {
        this.infoDeficiencia_defAuditiva = infoDeficiencia_defAuditiva;
    }

    public String getInfoDeficiencia_defMental() {
        return infoDeficiencia_defMental;
    }

    public void setInfoDeficiencia_defMental(String infoDeficiencia_defMental) {
        this.infoDeficiencia_defMental = infoDeficiencia_defMental;
    }

    public String getInfoDeficiencia_defIntelectual() {
        return infoDeficiencia_defIntelectual;
    }

    public void setInfoDeficiencia_defIntelectual(String infoDeficiencia_defIntelectual) {
        this.infoDeficiencia_defIntelectual = infoDeficiencia_defIntelectual;
    }

    public String getInfoDeficiencia_reabReadap() {
        return infoDeficiencia_reabReadap;
    }

    public void setInfoDeficiencia_reabReadap(String infoDeficiencia_reabReadap) {
        this.infoDeficiencia_reabReadap = infoDeficiencia_reabReadap;
    }

    public String getInfoDeficiencia_observacao() {
        return infoDeficiencia_observacao;
    }

    public void setInfoDeficiencia_observacao(String infoDeficiencia_observacao) {
        this.infoDeficiencia_observacao = infoDeficiencia_observacao;
    }

    public String getDependente_tpDep() {
        return dependente_tpDep;
    }

    public void setDependente_tpDep(String dependente_tpDep) {
        this.dependente_tpDep = dependente_tpDep;
    }

    public String getDependente_nmDep() {
        return dependente_nmDep;
    }

    public void setDependente_nmDep(String dependente_nmDep) {
        this.dependente_nmDep = dependente_nmDep;
    }

    public String getDependente_dtNascto() {
        return dependente_dtNascto;
    }

    public void setDependente_dtNascto(String dependente_dtNascto) {
        this.dependente_dtNascto = dependente_dtNascto;
    }

    public String getDependente_cpfDep() {
        return dependente_cpfDep;
    }

    public void setDependente_cpfDep(String dependente_cpfDep) {
        this.dependente_cpfDep = dependente_cpfDep;
    }

    public String getDependente_depIRRF() {
        return dependente_depIRRF;
    }

    public void setDependente_depIRRF(String dependente_depIRRF) {
        this.dependente_depIRRF = dependente_depIRRF;
    }

    public String getDependente_depSF() {
        return dependente_depSF;
    }

    public void setDependente_depSF(String dependente_depSF) {
        this.dependente_depSF = dependente_depSF;
    }

    public String getDependente_incTrab() {
        return dependente_incTrab;
    }

    public void setDependente_incTrab(String dependente_incTrab) {
        this.dependente_incTrab = dependente_incTrab;
    }

    public String getContato_fonePrinc() {
        return contato_fonePrinc;
    }

    public void setContato_fonePrinc(String contato_fonePrinc) {
        this.contato_fonePrinc = contato_fonePrinc;
    }

    public String getContato_foneAlternat() {
        return contato_foneAlternat;
    }

    public void setContato_foneAlternat(String contato_foneAlternat) {
        this.contato_foneAlternat = contato_foneAlternat;
    }

    public String getContato_emailPrinc() {
        return contato_emailPrinc;
    }

    public void setContato_emailPrinc(String contato_emailPrinc) {
        this.contato_emailPrinc = contato_emailPrinc;
    }

    public String getContato_emailAlternat() {
        return contato_emailAlternat;
    }

    public void setContato_emailAlternat(String contato_emailAlternat) {
        this.contato_emailAlternat = contato_emailAlternat;
    }

    public String getInfoTSVInicio_cadIni() {
        return infoTSVInicio_cadIni;
    }

    public void setInfoTSVInicio_cadIni(String infoTSVInicio_cadIni) {
        this.infoTSVInicio_cadIni = infoTSVInicio_cadIni;
    }

    public String getInfoTSVInicio_codCateg() {
        return infoTSVInicio_codCateg;
    }

    public void setInfoTSVInicio_codCateg(String infoTSVInicio_codCateg) {
        this.infoTSVInicio_codCateg = infoTSVInicio_codCateg;
    }

    public String getInfoTSVInicio_dtInicio() {
        return infoTSVInicio_dtInicio;
    }

    public void setInfoTSVInicio_dtInicio(String infoTSVInicio_dtInicio) {
        this.infoTSVInicio_dtInicio = infoTSVInicio_dtInicio;
    }

    public String getInfoTSVInicio_natAtividade() {
        return infoTSVInicio_natAtividade;
    }

    public void setInfoTSVInicio_natAtividade(String infoTSVInicio_natAtividade) {
        this.infoTSVInicio_natAtividade = infoTSVInicio_natAtividade;
    }

    public String getCargoFuncao_codCargo() {
        return cargoFuncao_codCargo;
    }

    public void setCargoFuncao_codCargo(String cargoFuncao_codCargo) {
        this.cargoFuncao_codCargo = cargoFuncao_codCargo;
    }

    public String getCargoFuncao_codFuncao() {
        return cargoFuncao_codFuncao;
    }

    public void setCargoFuncao_codFuncao(String cargoFuncao_codFuncao) {
        this.cargoFuncao_codFuncao = cargoFuncao_codFuncao;
    }

    public String getRemuneracao_vrSalFx() {
        return remuneracao_vrSalFx;
    }

    public void setRemuneracao_vrSalFx(String remuneracao_vrSalFx) {
        this.remuneracao_vrSalFx = remuneracao_vrSalFx;
    }

    public String getRemuneracao_undSalFixo() {
        return remuneracao_undSalFixo;
    }

    public void setRemuneracao_undSalFixo(String remuneracao_undSalFixo) {
        this.remuneracao_undSalFixo = remuneracao_undSalFixo;
    }

    public String getRemuneracao_dscSalVar() {
        return remuneracao_dscSalVar;
    }

    public void setRemuneracao_dscSalVar(String remuneracao_dscSalVar) {
        this.remuneracao_dscSalVar = remuneracao_dscSalVar;
    }

    public String getFgts_opcFGTS() {
        return fgts_opcFGTS;
    }

    public void setFgts_opcFGTS(String fgts_opcFGTS) {
        this.fgts_opcFGTS = fgts_opcFGTS;
    }

    public String getFgts_dtOpcFGTS() {
        return fgts_dtOpcFGTS;
    }

    public void setFgts_dtOpcFGTS(String fgts_dtOpcFGTS) {
        this.fgts_dtOpcFGTS = fgts_dtOpcFGTS;
    }

    public String getInfoDirigenteSindical_categOrig() {
        return infoDirigenteSindical_categOrig;
    }

    public void setInfoDirigenteSindical_categOrig(String infoDirigenteSindical_categOrig) {
        this.infoDirigenteSindical_categOrig = infoDirigenteSindical_categOrig;
    }

    public String getInfoDirigenteSindical_cnpjOrigem() {
        return infoDirigenteSindical_cnpjOrigem;
    }

    public void setInfoDirigenteSindical_cnpjOrigem(String infoDirigenteSindical_cnpjOrigem) {
        this.infoDirigenteSindical_cnpjOrigem = infoDirigenteSindical_cnpjOrigem;
    }

    public String getInfoDirigenteSindical_dtAdmOrig() {
        return infoDirigenteSindical_dtAdmOrig;
    }

    public void setInfoDirigenteSindical_dtAdmOrig(String infoDirigenteSindical_dtAdmOrig) {
        this.infoDirigenteSindical_dtAdmOrig = infoDirigenteSindical_dtAdmOrig;
    }

    public String getInfoDirigenteSindical_matricOrig() {
        return infoDirigenteSindical_matricOrig;
    }

    public void setInfoDirigenteSindical_matricOrig(String infoDirigenteSindical_matricOrig) {
        this.infoDirigenteSindical_matricOrig = infoDirigenteSindical_matricOrig;
    }

    public String getInfoTrabCedido_categOrig() {
        return infoTrabCedido_categOrig;
    }

    public void setInfoTrabCedido_categOrig(String infoTrabCedido_categOrig) {
        this.infoTrabCedido_categOrig = infoTrabCedido_categOrig;
    }

    public String getInfoTrabCedido_cnpjCednt() {
        return infoTrabCedido_cnpjCednt;
    }

    public void setInfoTrabCedido_cnpjCednt(String infoTrabCedido_cnpjCednt) {
        this.infoTrabCedido_cnpjCednt = infoTrabCedido_cnpjCednt;
    }

    public String getInfoTrabCedido_matricCed() {
        return infoTrabCedido_matricCed;
    }

    public void setInfoTrabCedido_matricCed(String infoTrabCedido_matricCed) {
        this.infoTrabCedido_matricCed = infoTrabCedido_matricCed;
    }

    public String getInfoTrabCedido_dtAdmCed() {
        return infoTrabCedido_dtAdmCed;
    }

    public void setInfoTrabCedido_dtAdmCed(String infoTrabCedido_dtAdmCed) {
        this.infoTrabCedido_dtAdmCed = infoTrabCedido_dtAdmCed;
    }

    public String getInfoTrabCedido_tpRegTrab() {
        return infoTrabCedido_tpRegTrab;
    }

    public void setInfoTrabCedido_tpRegTrab(String infoTrabCedido_tpRegTrab) {
        this.infoTrabCedido_tpRegTrab = infoTrabCedido_tpRegTrab;
    }

    public String getInfoTrabCedido_tpRegPrev() {
        return infoTrabCedido_tpRegPrev;
    }

    public void setInfoTrabCedido_tpRegPrev(String infoTrabCedido_tpRegPrev) {
        this.infoTrabCedido_tpRegPrev = infoTrabCedido_tpRegPrev;
    }

    public String getInfoTrabCedido_infOnus() {
        return infoTrabCedido_infOnus;
    }

    public void setInfoTrabCedido_infOnus(String infoTrabCedido_infOnus) {
        this.infoTrabCedido_infOnus = infoTrabCedido_infOnus;
    }

    public String getInfoEstagiario_natEstagio() {
        return infoEstagiario_natEstagio;
    }

    public void setInfoEstagiario_natEstagio(String infoEstagiario_natEstagio) {
        this.infoEstagiario_natEstagio = infoEstagiario_natEstagio;
    }

    public String getInfoEstagiario_nivEstagio() {
        return infoEstagiario_nivEstagio;
    }

    public void setInfoEstagiario_nivEstagio(String infoEstagiario_nivEstagio) {
        this.infoEstagiario_nivEstagio = infoEstagiario_nivEstagio;
    }

    public String getInfoEstagiario_areaAtuacao() {
        return infoEstagiario_areaAtuacao;
    }

    public void setInfoEstagiario_areaAtuacao(String infoEstagiario_areaAtuacao) {
        this.infoEstagiario_areaAtuacao = infoEstagiario_areaAtuacao;
    }

    public String getInfoEstagiario_nrApol() {
        return infoEstagiario_nrApol;
    }

    public void setInfoEstagiario_nrApol(String infoEstagiario_nrApol) {
        this.infoEstagiario_nrApol = infoEstagiario_nrApol;
    }

    public String getInfoEstagiario_vlrBolsa() {
        return infoEstagiario_vlrBolsa;
    }

    public void setInfoEstagiario_vlrBolsa(String infoEstagiario_vlrBolsa) {
        this.infoEstagiario_vlrBolsa = infoEstagiario_vlrBolsa;
    }

    public String getInfoEstagiario_dtPrevTerm() {
        return infoEstagiario_dtPrevTerm;
    }

    public void setInfoEstagiario_dtPrevTerm(String infoEstagiario_dtPrevTerm) {
        this.infoEstagiario_dtPrevTerm = infoEstagiario_dtPrevTerm;
    }

    public String getInstEnsino_cnpjInstEnsino() {
        return instEnsino_cnpjInstEnsino;
    }

    public void setInstEnsino_cnpjInstEnsino(String instEnsino_cnpjInstEnsino) {
        this.instEnsino_cnpjInstEnsino = instEnsino_cnpjInstEnsino;
    }

    public String getInstEnsino_nmRazao() {
        return instEnsino_nmRazao;
    }

    public void setInstEnsino_nmRazao(String instEnsino_nmRazao) {
        this.instEnsino_nmRazao = instEnsino_nmRazao;
    }

    public String getInstEnsino_dscLograd() {
        return instEnsino_dscLograd;
    }

    public void setInstEnsino_dscLograd(String instEnsino_dscLograd) {
        this.instEnsino_dscLograd = instEnsino_dscLograd;
    }

    public String getInstEnsino_nrLograd() {
        return instEnsino_nrLograd;
    }

    public void setInstEnsino_nrLograd(String instEnsino_nrLograd) {
        this.instEnsino_nrLograd = instEnsino_nrLograd;
    }

    public String getInstEnsino_bairro() {
        return instEnsino_bairro;
    }

    public void setInstEnsino_bairro(String instEnsino_bairro) {
        this.instEnsino_bairro = instEnsino_bairro;
    }

    public String getInstEnsino_cep() {
        return instEnsino_cep;
    }

    public void setInstEnsino_cep(String instEnsino_cep) {
        this.instEnsino_cep = instEnsino_cep;
    }

    public String getInstEnsino_codMunic() {
        return instEnsino_codMunic;
    }

    public void setInstEnsino_codMunic(String instEnsino_codMunic) {
        this.instEnsino_codMunic = instEnsino_codMunic;
    }

    public String getInstEnsino_uf() {
        return instEnsino_uf;
    }

    public void setInstEnsino_uf(String instEnsino_uf) {
        this.instEnsino_uf = instEnsino_uf;
    }

    public String getAgeIntegracao_cnpjAgntInteg() {
        return ageIntegracao_cnpjAgntInteg;
    }

    public void setAgeIntegracao_cnpjAgntInteg(String ageIntegracao_cnpjAgntInteg) {
        this.ageIntegracao_cnpjAgntInteg = ageIntegracao_cnpjAgntInteg;
    }

    public String getAgeIntegracao_nmRazao() {
        return ageIntegracao_nmRazao;
    }

    public void setAgeIntegracao_nmRazao(String ageIntegracao_nmRazao) {
        this.ageIntegracao_nmRazao = ageIntegracao_nmRazao;
    }

    public String getAgeIntegracao_dscLograd() {
        return ageIntegracao_dscLograd;
    }

    public void setAgeIntegracao_dscLograd(String ageIntegracao_dscLograd) {
        this.ageIntegracao_dscLograd = ageIntegracao_dscLograd;
    }

    public String getAgeIntegracao_nrLograd() {
        return ageIntegracao_nrLograd;
    }

    public void setAgeIntegracao_nrLograd(String ageIntegracao_nrLograd) {
        this.ageIntegracao_nrLograd = ageIntegracao_nrLograd;
    }

    public String getAgeIntegracao_bairro() {
        return ageIntegracao_bairro;
    }

    public void setAgeIntegracao_bairro(String ageIntegracao_bairro) {
        this.ageIntegracao_bairro = ageIntegracao_bairro;
    }

    public String getAgeIntegracao_cep() {
        return ageIntegracao_cep;
    }

    public void setAgeIntegracao_cep(String ageIntegracao_cep) {
        this.ageIntegracao_cep = ageIntegracao_cep;
    }

    public String getAgeIntegracao_codMunic() {
        return ageIntegracao_codMunic;
    }

    public void setAgeIntegracao_codMunic(String ageIntegracao_codMunic) {
        this.ageIntegracao_codMunic = ageIntegracao_codMunic;
    }

    public String getAgeIntegracao_uf() {
        return ageIntegracao_uf;
    }

    public void setAgeIntegracao_uf(String ageIntegracao_uf) {
        this.ageIntegracao_uf = ageIntegracao_uf;
    }

    public String getSupervisorEstagio_cpfSupervisor() {
        return supervisorEstagio_cpfSupervisor;
    }

    public void setSupervisorEstagio_cpfSupervisor(String supervisorEstagio_cpfSupervisor) {
        this.supervisorEstagio_cpfSupervisor = supervisorEstagio_cpfSupervisor;
    }

    public String getSupervisorEstagio_nmSuperv() {
        return supervisorEstagio_nmSuperv;
    }

    public void setSupervisorEstagio_nmSuperv(String supervisorEstagio_nmSuperv) {
        this.supervisorEstagio_nmSuperv = supervisorEstagio_nmSuperv;
    }

    public String getAfastamento_dtIniAfast() {
        return afastamento_dtIniAfast;
    }

    public void setAfastamento_dtIniAfast(String afastamento_dtIniAfast) {
        this.afastamento_dtIniAfast = afastamento_dtIniAfast;
    }

    public String getAfastamento_codMotAfast() {
        return afastamento_codMotAfast;
    }

    public void setAfastamento_codMotAfast(String afastamento_codMotAfast) {
        this.afastamento_codMotAfast = afastamento_codMotAfast;
    }

    public String getTermino_dtTerm() {
        return termino_dtTerm;
    }

    public void setTermino_dtTerm(String termino_dtTerm) {
        this.termino_dtTerm = termino_dtTerm;
    }

}

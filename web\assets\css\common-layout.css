/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
*/
/* 
    Created on : 03/03/2020, 17:32:46
    Author     : rod
*/

span.primefacesInputFix, input.primefacesInputFix {
    display: block;
    width: 100%;
}

span.primefacesInputFix > input {
    width: 100%;
}

.flexBottom {
    display: flex;
    align-items: flex-end;
}

.flexTop {
    display: flex;
    align-items: flex-start;
}

.ui-datatable-scrollable {
    height: unset !important;
}

@media only screen and (max-width: 2000px) and (min-width: 701px) {
    .grelha thead tr th,
    .grelha tbody tr td {
        min-width: 120px !important;
        max-width: 120px !important;
    }
}    

.tabelaReformulada{
    white-space: pre-line !important;
}

.tabelaReformulada .ui-widget-content {
    background: white;
}

.tabelaReformulada td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}

.tabelaReformulada .ui-datatable-selectable {
    border: 1px solid #dddddd !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}

.tabelaReformulada > * {
    flex-shrink: 0;
}

.tabelaReformulada .ui-datatable-scrollable-header-box {
    background: white;
}
.tabelaReformulada .ui-datatable-data .ui-widget-content {
    border: 1px solid #dddddd !important;
}

.tabelaReformulada .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
}

.tabelaReformulada .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}

.tabelaReformulada > .ui-datatable-scrollable-body {
    background: white !important;
    flex-shrink: 1;
}

.tabelaReformulada{
    display: flex;
    flex-direction: column;
    font-size: 12px;
    background: white;
    padding:0px !important;
    margin:0px !important;
    max-height: 100% !important;
}

.tabelaReformulada tbody tr:nth-child(even){
    background-color:#f3f6f7;
}

.tabelaReformulada tbody tr {
    color:#2a6389;
}

.tabelaReformulada tbody tr:not(.ui-state-highlight):not(.ui-expanded-row-content):hover td {
    background-color:#c7ddf1 !important;
    color:#104e78;
} 

.tabelaReformulada_paginator_top,
.tabelaReformulada_paginator_top div{
    border:none !important
}  

@media only screen and (max-width: 700px) and (min-width: 10px) {
    .tabelaReformulada .ui-datatable-scrollable-body {
        flex-grow: 1;
    }

    .tabelaReformulada tbody tr td {
        font-size:9pt !important;
        padding: 2px 6px 2px 6px !important;
    }

    .tabelaReformulada tbody tr td[role="gridcell"]:not(:first-child) {
        border-top:thin dashed #DDD !important; 
    }

    .tabelaReformulada tbody tr:nth-child(even) td[role="gridcell"]:not(:first-child) {
        border-top:thin dashed #CCC !important; 
    }

    .tabelaReformulada tbody tr td[role="gridcell"]:nth-child(2) {
        font-weight:bold;
    }

    .tabelaReformulada tbody tr td[role="gridcell"]:first-child {
        border-top: thin solid #dce6ef !important;
    }

    .tabelaReformulada tbody tr td[role="gridcell"]:last-child {
        border-bottom: thin solid #dce6ef !important;
    }

    .tabelaReformulada tbody tr {
        color:#505050;
    }
}

@media only screen and (max-width: 2000px) and (min-width: 641px) {
    .grelha{
        border:none !important
    }

    .grelha thead tr th,
    .grelha thead tr td{
        background: linear-gradient(to bottom, #597d98, #4b708d) !important;
        min-height:46px !important;
        color: #FFF !important; 
        border:thin solid #7397b1 !important;
    }

    .grelha tbody tr td{
        border:thin solid #CCC !important;
        font-size:9pt !important;
        text-transform: uppercase !important;
        padding: 6px !important;
        border-left: 0px solid !important;
        border-right: 0px solid !important;
        border-bottom: thin solid #c7ddf1 !important;
    }

    .tabelaReformulada{
        border:none !important
    }

    .tabelaReformulada thead tr th,
    .tabelaReformulada thead tr td{
        background: linear-gradient(to bottom, #597d98, #4b708d) !important;
        min-height: 46px !important;
        color: #FFF !important;
        border:thin solid #7397b1 !important;
    }

    .tabelaReformulada tbody tr td{
        border:thin solid #CCC !important;
        font-size:9pt !important;
        text-transform: uppercase !important;
        padding: 6px !important;
        border-left: 0px solid !important;
        border-right: 0px solid !important;
        border-bottom: thin solid #c7ddf1 !important;
    }

    .grelha [role="columnheader"] > span {
        top: -4px !important;
        position: relative !important;
    }

    .grelha{
        border:none !important
    }

    .grelha thead tr th,
    .grelha tbody tr td {
        white-space: normal !important;
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        -webkit-hyphens: auto !important;
        -ms-hyphens: auto !important;
        hyphens: auto !important;
    }

    .grelha thead tr th,
    .grelha tbody tr td{
        text-align: center !important;
    }
}

.FundoPagina2 {
    height: 100% !important;
    background-color: #FFF;
    border: thin solid #CCC;
    padding:10px 0px 10px 15px !important;
    border-radius: 4px !important;
    border-top:4px solid #3C8DBC !important;
    overflow: hidden !important;
}


#root {
    height: 100%;
    width: 100%;
    background: url(../img/bk.jpg) no-repeat bottom center fixed;
    background-color: #022a48;
    background-size: cover;
    color: white;
    margin: 0;
}

.app {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
}

.fixed-header, .fixed-footer {
    flex-shrink: 0;
}

.main-content {
    overflow: auto;
    flex-grow: 1;
}

/* modifica CSS aplicado ao elemento header */
header.fixed-header {
    height: initial !important;
    left: initial;
    top: initial;
    display: table;
    vertical-align: middle;
    z-index: 999;
    width: 100%;
    overflow: auto;
    position: relative !important;
    box-shadow: -1px 2px 6px #AAA;
    min-height: 10px !important;
    margin-top: 2px !important;
}

/* modifica CSS aplicado ao elemento footer */
footer.fixed-footer {
    height: unset !important;
    max-height: unset !important;
    left: unset !important;
    bottom: unset !important;
    background-color: initial !important;
    position: relative;
    display: table;
    vertical-align: middle;
    z-index: 999;
    width: 100%;
    overflow: auto;
    color: #fff;
    font-size: 11px;
    min-height: 10px !important;
    line-height: 10px !important;
    margin-bottom: 0px !important;
    padding: 0px !important;
}

.footer-body-2 {
    padding:0px !important;
    min-height:10px !important;
    max-height:40px !important;

    display: flex;
    flex-direction: row;
    justify-content: space-around;
    __flex-wrap: nowrap;

    -webkit-box-shadow: 0px -5px 5px 0px rgba(0,0,0,0.15);
    -moz-box-shadow: 0px -5px 5px 0px rgba(0,0,0,0.15);
    box-shadow: 0px -5px 5px 0px rgba(0,0,0,0.15);
    background: #2384cd;
    background: -moz-linear-gradient(top,  #2384cd 0%, #3f6382 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#2384cd), color-stop(100%,#3f6382));
    background: -webkit-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: -o-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: -ms-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2384cd', endColorstr='#3f6382',GradientType=0 );
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TipoPosto;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TiposPostosDao {

    public TipoPosto getTipoPosto(String secao, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select CtrItens.Descricao, CtrItens.TipoPosto, TiposPostos.* "
                    + " from PStServ "
                    + " Left Join CtrItens on  CtrItens.Contrato = PstServ.Contrato "
                    + "                   and CtrItens.TipoPosto = PstServ.TipoPosto "
                    + "                      and CtrItens.CodFil = PstServ.Codfil "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where PstServ.Secao = ? and PstServ.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codFil);
            consulta.select();
            TipoPosto tipoPosto = new TipoPosto();
            while (consulta.Proximo()) {
                tipoPosto.setCodigo(consulta.getString("codigo"));
                tipoPosto.setDescricao(consulta.getString("descricao"));
                tipoPosto.setFrequencia(consulta.getString("frequencia"));
                tipoPosto.setChdiu1(consulta.getString("chdiu1"));
                tipoPosto.setChnot1(consulta.getString("chnot1"));
                tipoPosto.setChdiu2(consulta.getString("chdiu2"));
                tipoPosto.setChnot2(consulta.getString("chnot2"));
                tipoPosto.setChdiu3(consulta.getString("chdiu3"));
                tipoPosto.setChnot3(consulta.getString("chnot3"));
                tipoPosto.setChdiu4(consulta.getString("chdiu4"));
                tipoPosto.setChnot4(consulta.getString("chnot4"));
                tipoPosto.setChdiu5(consulta.getString("chdiu5"));
                tipoPosto.setChnot5(consulta.getString("chnot5"));
                tipoPosto.setChdiu6(consulta.getString("chdiu6"));
                tipoPosto.setChnot6(consulta.getString("chnot6"));
                tipoPosto.setChdiu7(consulta.getString("chdiu7"));
                tipoPosto.setChnot7(consulta.getString("chnot7"));
                tipoPosto.setChdiufer(consulta.getString("chdiufer"));
                tipoPosto.setChnotfer(consulta.getString("chnotfer"));
                tipoPosto.setOperador(consulta.getString("operador"));
                tipoPosto.setHr_alter(consulta.getString("hr_alter"));
                tipoPosto.setDt_alter(consulta.getString("dt_alter"));
            }
            consulta.Close();
            return tipoPosto;
        } catch (Exception e) {
            throw new Exception("TipoPostoDao.getTipoPosto - " + e.getMessage() + "\r\n"
                    + " Select CtrItens.Descricao, CtrItens.TipoPosto, TiposPostos.* "
                    + " from PStServ "
                    + " Left Join CtrItens on  CtrItens.Contrato = PstServ.Contrato "
                    + "                   and CtrItens.TipoPosto = PstServ.TipoPosto "
                    + "                      and CtrItens.CodFil = PstServ.Codfil "
                    + " Left Join TiposPostos on TiposPostos.Codigo  = CtrItens.CodTipo "
                    + " where PstServ.Secao = " + secao + " and PstServ.CodFil = " + codFil);
        }
    }

    /**
     * Obtem o proximo codigo
     *
     * @param persistencia Conexao com o banco de dadoss
     * @return
     * @throws Exception
     */
    public String obterProximoCodigo(Persistencia persistencia) throws Exception {
        String codigo = "0";
        try {
            String sql = "SELECT max(Codigo) + 1 codigo FROM tipospostos";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                codigo = consulta.getString("codigo");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro");
        }
        return codigo;
    }

    /**
     * Recupera registros
     *
     * @param descricao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TipoPosto> recuperaRegistros(String descricao, Persistencia persistencia) throws Exception {
        List<TipoPosto> tiposPostos = new ArrayList<>();
        try {
            String sql = "SELECT * FROM tipospostos WHERE descricao = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(descricao);
            consulta.select();

            TipoPosto tipoPosto = null;
            while (consulta.Proximo()) {
                tipoPosto = new TipoPosto();
                tipoPosto.setCodigo(consulta.getString("codigo"));
                tipoPosto.setDescricao(consulta.getString("descricao"));
                tipoPosto.setFrequencia(consulta.getString("frequencia"));
                tipoPosto.setChdiu1(consulta.getString("chdiu1"));
                tipoPosto.setChnot1(consulta.getString("chnot1"));
                tipoPosto.setChdiu2(consulta.getString("chdiu2"));
                tipoPosto.setChnot2(consulta.getString("chnot2"));
                tipoPosto.setChdiu3(consulta.getString("chdiu3"));
                tipoPosto.setChnot3(consulta.getString("chnot3"));
                tipoPosto.setChdiu4(consulta.getString("chdiu4"));
                tipoPosto.setChnot4(consulta.getString("chnot4"));
                tipoPosto.setChdiu5(consulta.getString("chdiu5"));
                tipoPosto.setChnot5(consulta.getString("chnot5"));
                tipoPosto.setChdiu6(consulta.getString("chdiu6"));
                tipoPosto.setChnot6(consulta.getString("chnot6"));
                tipoPosto.setChdiu7(consulta.getString("chdiu7"));
                tipoPosto.setChnot7(consulta.getString("chnot7"));
                tipoPosto.setChdiufer(consulta.getString("chdiufer"));
                tipoPosto.setChnotfer(consulta.getString("chnotfer"));
                tipoPosto.setOperador(consulta.getString("operador"));
                tipoPosto.setHr_alter(consulta.getString("hr_alter"));
                tipoPosto.setDt_alter(consulta.getString("dt_alter"));
                tiposPostos.add(tipoPosto);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("recupera registros: " + e.getMessage());
        }
        return tiposPostos;
    }

    /**
     * verifica se exite o tipo do posto
     *
     * @param descricao descricao do posto
     * @param persistencia conexão com o banco de dados
     * @return se existe posto
     * @throws Exception
     */
    public boolean existeTipoPosto(String descricao, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM tipospostos WHERE descricao = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(descricao);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocrreu um erro: " + e.getMessage());
        }
        return existe;
    }

    /**
     * Inserir registros no banco de dados
     *
     * @param tipoPosto
     * @param persistencia
     * @throws Exception
     */
    public void inserirRegistros(TipoPosto tipoPosto, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO tipospostos (codigo, descricao, frequencia,"
                    + " chdiu1, chnot1,chdiu2, chnot2,chdiu3, chnot3,chdiu4, chnot4,"
                    + "chdiu5, chnot5,chdiu6, chnot6,chdiu7, chnot7,chdiufer, chnotfer,"
                    + "operador, hr_alter, dt_alter) VALUES (?,?,?,?,?,?,?,?,?,?,?,"
                    + "?,?,?,?,?,?,?,?,?,?,CONVERT(DATE, getDate()))";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tipoPosto.getCodigo());
            consulta.setString(tipoPosto.getDescricao());
            consulta.setString(tipoPosto.getFrequencia());
            consulta.setString(tipoPosto.getChdiu1());
            consulta.setString(tipoPosto.getChnot1());
            consulta.setString(tipoPosto.getChdiu2());
            consulta.setString(tipoPosto.getChnot2());
            consulta.setString(tipoPosto.getChdiu3());
            consulta.setString(tipoPosto.getChnot3());
            consulta.setString(tipoPosto.getChdiu4());
            consulta.setString(tipoPosto.getChnot4());
            consulta.setString(tipoPosto.getChdiu5());
            consulta.setString(tipoPosto.getChnot5());
            consulta.setString(tipoPosto.getChdiu6());
            consulta.setString(tipoPosto.getChnot6());
            consulta.setString(tipoPosto.getChdiu7());
            consulta.setString(tipoPosto.getChnot7());
            consulta.setString(tipoPosto.getChdiufer());
            consulta.setString(tipoPosto.getChnotfer());
            consulta.setString(tipoPosto.getOperador());
            consulta.setString(tipoPosto.getHr_alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
    }

    public void inserirRegistros(TipoPosto tipoPosto, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO tipospostos (codigo, descricao, frequencia,"
                    + " chdiu1, chnot1,chdiu2, chnot2,chdiu3, chnot3,chdiu4, chnot4,"
                    + "chdiu5, chnot5,chdiu6, chnot6,chdiu7, chnot7,chdiufer, chnotfer,"
                    + "operador, hr_alter, dt_alter) VALUES (?,?,?,?,?,?,?,?,?,?,?,"
                    + "?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(tipoPosto.getCodigo());
            consulta.setString(tipoPosto.getDescricao());
            consulta.setString(tipoPosto.getFrequencia());
            consulta.setString(tipoPosto.getChdiu1());
            consulta.setString(tipoPosto.getChnot1());
            consulta.setString(tipoPosto.getChdiu2());
            consulta.setString(tipoPosto.getChnot2());
            consulta.setString(tipoPosto.getChdiu3());
            consulta.setString(tipoPosto.getChnot3());
            consulta.setString(tipoPosto.getChdiu4());
            consulta.setString(tipoPosto.getChnot4());
            consulta.setString(tipoPosto.getChdiu5());
            consulta.setString(tipoPosto.getChnot5());
            consulta.setString(tipoPosto.getChdiu6());
            consulta.setString(tipoPosto.getChnot6());
            consulta.setString(tipoPosto.getChdiu7());
            consulta.setString(tipoPosto.getChnot7());
            consulta.setString(tipoPosto.getChdiufer());
            consulta.setString(tipoPosto.getChnotfer());
            consulta.setString(tipoPosto.getOperador());
            consulta.setString(tipoPosto.getHr_alter());
            consulta.setString(dataAtual);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
    }

}

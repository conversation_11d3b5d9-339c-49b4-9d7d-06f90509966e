/*
 */
package Controller.Produtos;

import Dados.Persistencia;
import SasBeans.Produtos;
import SasDaos.ProdutosDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ProdutosSatMobWeb {

    /**
     * Contagem do cadastro de produtos
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            ProdutosDao produtosdao = new ProdutosDao();
            retorno = produtosdao.totalProdutosMobWeb(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("produtos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de produtos
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Produtos> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<Produtos> retorno;
            ProdutosDao produtosdao = new ProdutosDao();
            retorno = produtosdao.listaPaginada(primeiro, linhas, filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("produtos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Insere um novo produto no banco
     *
     * @param produto
     * @param persistencia
     * @throws Exception
     */
    public void inserir(Produtos produto, Persistencia persistencia) throws Exception {
        try {
            ProdutosDao produtosdao = new ProdutosDao();
            produto.setCodigo(produtosdao.getMaxCodigo(persistencia));
            produtosdao.inserirProduto(produto, persistencia);
        } catch (Exception e) {
            throw new Exception("produtos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Atualiza um produto
     *
     * @param produto
     * @param persistencia
     * @throws Exception
     */
    public void atualizar(Produtos produto, Persistencia persistencia) throws Exception {
        try {
            ProdutosDao produtosdao = new ProdutosDao();
            produtosdao.atualizarProduto(produto, persistencia);
        } catch (Exception e) {
            throw new Exception("produtos.falhageral<message>" + e.getMessage());
        }
    }
}

﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtPgtos/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtPgtos/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtPgtos">
                    <xs:annotation>
                        <xs:documentation>Evento Pagtos Efetuados</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveFopagMensal">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideBenef">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do beneficiario do pagamento</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfBenef">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do beneficiario</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="deps" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de dependentes do beneficiario</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="vrDedDep">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Deducao IRRF relativo a dependentes</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:decimal">
                                                                <xs:totalDigits value="14"/>
                                                                <xs:fractionDigits value="2"/>
                                                                <xs:maxInclusive value="************"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoPgto" maxOccurs="60">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes dos pagamentos efetuados</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtPgto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data do pagamento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="tpPgto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Informar o tipo de pagamento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d{1,2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="indResBr">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Indicativo de residencia do beneficiario do pagamento no Brasil</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="detPgtoFl" minOccurs="0" maxOccurs="200">
                                                        <xs:annotation>
                                                            <xs:documentation>Detalhamento dos pagamentos efetuados apurados em S-1200 ou S-1202</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="perRef" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Periodo de referencia</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="ideDmDev">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Identificador do demonstrativo de pagamento</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="indPgtoTt">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de pagamento total</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[N|S]"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrLiq">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor liquido recebido pelo trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="************"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrRecArq" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Recibo do arquivo</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="40"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="retPgtoTot" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Retencoes efetuadas no ato do pagamento</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="codRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideTabRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="8"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="qtdRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="6"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="9999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="fatorRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrUnit" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor Unitario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor total da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="penAlim" type="TPensaoAlim" minOccurs="0" maxOccurs="99">
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoPgtoParc" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes complementares relacionadas ao pagamento parcial</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="matricula" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Matricula</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="codRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideTabRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="8"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="qtdRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="6"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="9999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="fatorRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrUnit" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor Unitario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor total da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="detPgtoBenPr" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Detalhamento de pagamentos relativos a beneficios previdenciarios</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="perRef">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Periodo de referencia</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="ideDmDev">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Identificador do demonstrativo de pagamento</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="indPgtoTt">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de pagamento total</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[N|S]"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrLiq">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor liquido recebido pelo trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="************"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="retPgtoTot" type="TRubrCaixa" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Retencoes efetuadas no ato do pagamento</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                                <xs:element name="infoPgtoParc" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes complementares relacionadas ao pagamento parcial</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="codRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideTabRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="8"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="qtdRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="6"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="9999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="fatorRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrUnit" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor Unitario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor total da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="detPgtoFer" minOccurs="0" maxOccurs="5">
                                                        <xs:annotation>
                                                            <xs:documentation>Detalhamento dos pagamentos de férias</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="codCateg">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Categoria do Trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{3}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="matricula" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Matricula</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtIniGoz">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data Inicio de gozo das férias</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="qtDias">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Quantidade de dias férias</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d{1,2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrLiq">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor liquido recebido pelo trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="************"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="detRubrFer" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Detalhamento das rubricas</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="codRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideTabRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Preencher com o identificador da tabela de rubricas</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="8"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="qtdRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="6"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="9999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="fatorRubr" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="5"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999.99"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrUnit" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor Unitario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrRubr">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor total da rubrica</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="penAlim" type="TPensaoAlim" minOccurs="0" maxOccurs="99">
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="detPgtoAnt" minOccurs="0" maxOccurs="99">
                                                        <xs:annotation>
                                                            <xs:documentation>Pagamento relativo a competencias anteriores ao inicio de obrigatoriedade dos eventos periOdicos</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="codCateg">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Categoria do Trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{3}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoPgtoAnt" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Detalhamento do pagamento</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpBcIRRF">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de BC do IRRF</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{2}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrBcIRRF">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor BC IRRF</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="************"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="idePgtoExt" type="TNaoResid" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes complementares sobre pagamentos no exterior</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveFopagMensal">
        <xs:annotation>
            <xs:documentation>Identificacao do Evento PeriOdico</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indApuracao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="perApur">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPensaoAlim">
        <xs:sequence>
            <xs:element name="cpfBenef">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>CPF do beneficiario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{11}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtNasctoBenef" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de Nascimento do Beneficiario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nmBenefic">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do Beneficiario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="70"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vlrPensao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor da pensao alimenticia</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="************"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRubrCaixa">
        <xs:annotation>
            <xs:documentation>Rubricas de pagamento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ideTabRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Preencher com o identificador da tabela de rubricas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="8"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="qtdRubr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="6"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="9999.99"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fatorRubr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="5"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999.99"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrUnit" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor Unitario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="************"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor total da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="************"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TNaoResid">
        <xs:annotation>
            <xs:documentation>Endereco no Exterior - Fiscal</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="idePais">
                <xs:annotation>
                    <xs:documentation>Identificacao do Pais onde foi efetuado o pagamento</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="codPais">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Pais</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="3"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="indNIF">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Indicativo do preenchimento do NIF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nifBenef" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número de Identificacao Fiscal - NIF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="20"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="endExt">
                <xs:annotation>
                    <xs:documentation>Informacoes complementares de endereco do beneficiario</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="dscLograd">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Descricao do logradouro</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="100"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrLograd" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do logradouro</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="10"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="complem" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Complemento do logradouro</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="30"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="bairro" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome do bairro/distrito</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="90"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nmCid">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome da Cidade</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="50"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codPostal" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo de Enderecamento Postal</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="4"/>
                                    <xs:maxLength value="12"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class SASPWAcesso {

    private String Nome;
    private String Data;
    private String Hora;
    private String Token;
    private String IMEI;
    private String Parametro;
    private String Codfil;
    private String CodPessoa;
    private String TipoAcesso;
    private String DataLog;

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getToken() {
        return Token;
    }

    public void setToken(String Token) {
        this.Token = Token;
    }

    public String getIMEI() {
        return IMEI;
    }

    public void setIMEI(String IMEI) {
        this.IMEI = IMEI;
    }

    public String getParametro() {
        return Parametro;
    }

    public void setParametro(String Parametro) {
        this.Parametro = Parametro;
    }

    public String getCodfil() {
        return Codfil;
    }

    public void setCodfil(String Codfil) {
        this.Codfil = Codfil;
    }

    public String getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getTipoAcesso() {
        return TipoAcesso;
    }

    public void setTipoAcesso(String TipoAcesso) {
        this.TipoAcesso = TipoAcesso;
    }

    public String getDataLog() {
        return DataLog;
    }

    public void setDataLog(String DataLog) {
        this.DataLog = DataLog;
    }
}

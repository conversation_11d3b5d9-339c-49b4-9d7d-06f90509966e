<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}</title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/cofre_dashboard_geral.css" rel="stylesheet"/>
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>

            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.3/Chart.min.js"></script>
            <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{dashboardSecSaude.Persistencia(login.pp)}"/>
                <f:viewAction action="#{dashboardSecSaude.carregarGraficos()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <header>
                <div id="TopoDash" class="col-md-12">
                    <h:form id="top">
                        <p:poll interval="180" listener="#{dashboardSecSaude.carregarGraficos()}" update="top main msgs" />

                        <div class="col-md-4 col-sm-4 col-xs-12" style="padding-left:0px !important;"> 
                            <label class="Empresa"><h:outputText value="#{dashboardSecSaude.filiais.descricao}" rendered="#{dashboardSecSaude.filiais != null}"/></label>
                            <label class="Filial">
                                <h:outputText value="#{dashboardSecSaude.filiais.endereco}" rendered="#{dashboardSecSaude.filiais != null}"/>
                                <h:outputText value=" - " rendered="#{dashboardSecSaude.filiais != null}"/>
                                <h:outputText value="#{dashboardSecSaude.filiais.bairro}" rendered="#{dashboardSecSaude.filiais != null}"/>
                                <h:outputText value=" - " rendered="#{dashboardSecSaude.filiais != null}"/>
                                <h:outputText value="#{dashboardSecSaude.filiais.cidade}" rendered="#{dashboardSecSaude.filiais != null}"/>
                                <h:outputText value="/" rendered="#{dashboardSecSaude.filiais != null}"/>
                                <h:outputText value="#{dashboardSecSaude.filiais.UF}" rendered="#{dashboardSecSaude.filiais != null}"/>
                            </label>
                            <label class="Voltar" onclick="window.history.back();"><i class="fa fa-arrow-circle-left"></i>&nbsp;#{localemsgs.Voltar}</label>
                        </div>
                        <div class="col-md-8 col-sm-8 col-xs-12" style="padding:0px !important;">                                                        
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ItemDashDados">
                                    <label ref="Titulo">#{localemsgs.Mes}</label>
                                    <p:selectOneMenu id="slcMes" required="true" styleClass="seletor" value="#{dashboardSecSaude.mes}" >
                                        <f:selectItem itemLabel="#{localemsgs.JANUARY.toUpperCase()}" itemValue="01" />
                                        <f:selectItem itemLabel="#{localemsgs.FEBRUARY.toUpperCase()}" itemValue="02" />
                                        <f:selectItem itemLabel="#{localemsgs.MARCH.toUpperCase()}" itemValue="03" />
                                        <f:selectItem itemLabel="#{localemsgs.APRIL.toUpperCase()}" itemValue="04" />
                                        <f:selectItem itemLabel="#{localemsgs.MAY.toUpperCase()}" itemValue="05" />
                                        <f:selectItem itemLabel="#{localemsgs.JUNE.toUpperCase()}" itemValue="06" />
                                        <f:selectItem itemLabel="#{localemsgs.JULY.toUpperCase()}" itemValue="07" />
                                        <f:selectItem itemLabel="#{localemsgs.AUGUST.toUpperCase()}" itemValue="08" />
                                        <f:selectItem itemLabel="#{localemsgs.SEPTEMBER.toUpperCase()}" itemValue="09" />
                                        <f:selectItem itemLabel="#{localemsgs.OCTOBER.toUpperCase()}" itemValue="10" />
                                        <f:selectItem itemLabel="#{localemsgs.NOVEMBER.toUpperCase()}" itemValue="11" />
                                        <f:selectItem itemLabel="#{localemsgs.DECEMBER.toUpperCase()}" itemValue="12" />

                                        <p:ajax listener="#{dashboardSecSaude.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2 col-xs-6 ItemDash">
                                <div class="ImoveisSituacaoAgp">
                                    <label ref="Titulo">#{localemsgs.Ano}</label>
                                    <p:selectOneMenu id="slcAno" styleClass="seletor" value="#{dashboardSecSaude.ano}">
                                        <f:selectItem itemLabel="2017" itemValue="2017" />
                                        <f:selectItem itemLabel="2018" itemValue="2018" />
                                        <f:selectItem itemLabel="2019" itemValue="2019" />
                                        <f:selectItem itemLabel="2020" itemValue="2020" />
                                        <p:ajax listener="#{dashboardSecSaude.carregarGraficos}" update="msgs main top" />
                                    </p:selectOneMenu>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </div>
            </header>
            <h:form id="main">
                <p:panel class="panelPrincipal">                    

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.ImoveisporSituacaoAgp}<br />
                                        <span>#{localemsgs.DescImoveisporSituacaoAgp}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoImoveisSituacaoAgp" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>


                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.ImoveisDiaTrab}<br />
                                        <span>#{localemsgs.DescImoveisDiaTrab}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoImoveisDiaTrab" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.ImoveisporSituacaoDia}<br />
                                        <span>#{localemsgs.DescImoveisporSituacaoDia}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoImoveisSituacaoDia" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>       

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.InspecaoeTratamentoAgp}<br />
                                        <span>#{localemsgs.DescInspecaoeTratamentoAgp}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoInspecaoeTratamentoAgp" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.InspecaoTratamentoTipo}<br />
                                        <span>#{localemsgs.DescInspecaoTratamentoTipo}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoimoveisInspecaoTratamentoTipo" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.DepositosTipoAgp}<br />
                                        <span>#{localemsgs.DescDepositosTipoAgp}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoDepositosTipo" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-6 col-sm-6 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.DepositoAcaoAgp}<br />
                                        <span>#{localemsgs.DescDepositoAcaoAgp}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoDepositosAcao" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>                    

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.DepositosEvolucao}<br />
                                        <span>#{localemsgs.DescDepositosEvolucao}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoNumeroDepositosEvolucao" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.EvolucaoDepositosTipo}<br />
                                        <span>#{localemsgs.DescEvolucaoDepositosTipo}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoEvolucaoDepositosTipo" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>    

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.AmostrasRoedoresAmostras}<br />
                                        <span>#{localemsgs.DescAmostrasRoedoresAmostras}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoAmostrasRoedoresAmostras" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>    

                    <div class="col-md-12 col-sm-12 col-xs-12 BoxTabelasDireita">
                        <div class="col-md-12 col-sm-12 col-xs-12 FundoTabelas">
                            <table>
                                <tr>
                                    <td class="Titulo">
                                        #{localemsgs.InspecionadosxDengue}<br />
                                        <span>#{localemsgs.DescInspecionadosxDengue}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="DadosTab">
                                        <canvas id="graficoInspecionadosxDengue" height="250" style="float: left; width:100%; max-height:250px !important;"></canvas>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>                    

                </p:panel>
                <script type="text/javascript">
                    $(document).ready(function () {
                        AjustarCabecalhos();
                        gerarGraficos();
                    }).on('keyup', '[grid-style="dark"] thead tr th input', function () {
                        $obj = $(this).parents('table');
                        var input = $obj.find('input').val().toLowerCase();
                        $obj.find('tbody tr').filter(function () {
                            $(this).toggle($(this).text().toLowerCase().indexOf(input) > -1);
                        });
                    });
                    function AjustarCabecalhos() {
                        $('[grid-style="dark"] tbody tr:nth-child(1) td').each(function () {
                            $(this).parent('tr').parent('tbody').parent('table').find('thead tr:first-child th:eq(' + $(this).index() + ')').width($(this).width() + 'px');
                            var widthTh = $(this).parent('tr').parent('tbody').parent('table').find('thead tr:first-child th:eq(' + $(this).index() + ')').width();
                            $(this).css('min-width', widthTh + 'px').css('width', widthTh + 'px').css('max-width', widthTh + 'px');
                        });
                        $('[grid-style="dark"] thead tr:nth-child(1) th:last-child').each(function () {
                            var widthTh = $(this).width() + 25;
                            $(this).css('min-width', widthTh + 'px').css('width', widthTh + 'px').css('max-width', widthTh + 'px');
                        });
                    }

                    var imoveisSituacaoAgp = {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                    data: #{dashboardSecSaude.dataImoveisSitAgp},
                                    backgroundColor: [
                    #{dashboardSecSaude.colors}
                                    ],
                                    label: 'Dataset 1'
                                }],
                            labels:
                    #{dashboardSecSaude.labelsImoveisSitAgp}
                        },
                        options: {
                            responsive: true,
                            legend: {
                                display: true,
                                position: 'right',
                            },
                            title: {
                                display: false,
                            },
                            animation: {
                                animateScale: true,
                                animateRotate: true
                            }
                        }
                    };
                    var imoveisSituacaoDia = {
                        type: 'line',
                        data: {
                    #{dashboardSecSaude.labelsImoveisSitDia}
                        },
                        options: {
                            responsive: true,
                            title: {
                                display: false,
                                text: 'Chart.js Line Chart'
                            },
                            tooltips: {
                                mode: 'index',
                                intersect: false,
                            },
                            hover: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                xAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Data'
                                        }
                                    }],
                                yAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Quantidade'
                                        }
                                    }]
                            }
                        }
                    };
                    var imoveisDiaTrab = {
                        type: 'bar',
                        data: {
                    #{dashboardSecSaude.labelsImoveisdiaTrab}
                        },
                        options: {
                            responsive: true,
                            title: {
                                display: false,
                                text: 'Chart.js Line Chart'
                            },
                            legend: {
                                position: 'top',
                            },
                        }
                    };

                    var inspecaoTratamentoAgp = {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                    data: #{dashboardSecSaude.dataInspecaoTratAgp},
                                    backgroundColor: [
                    #{dashboardSecSaude.colors}
                                    ],
                                    label: 'Dataset 1'
                                }],
                            labels:
                    #{dashboardSecSaude.labelInspecaoTratAgp}
                        },
                        options: {
                            responsive: true,
                            legend: {
                                display: true,
                                position: 'right',
                            },
                            title: {
                                display: false,
                            },
                            animation: {
                                animateScale: true,
                                animateRotate: true
                            }
                        }
                    };

                    var depositosTipoAgp = {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                    data: #{dashboardSecSaude.dataDepositoTipoAgp},
                                    backgroundColor: [
                    #{dashboardSecSaude.colors}
                                    ],
                                    label: 'Dataset 1'
                                }],
                            labels:
                    #{dashboardSecSaude.labelDepositoTipoAgp}
                        },
                        options: {
                            responsive: true,
                            legend: {
                                display: true,
                                position: 'right',
                            },
                            title: {
                                display: false,
                            },
                            animation: {
                                animateScale: true,
                                animateRotate: true
                            }
                        }
                    };


                    var imoveisInspecaoTratamentoTipo = {
                        type: 'line',
                        data: {
                    #{dashboardSecSaude.labelsImovisInpecaoTratamentoTipo}
                        },
                        options: {
                            responsive: true,
                            title: {
                                display: false,
                                text: 'Chart.js Line Chart'
                            },
                            tooltips: {
                                mode: 'index',
                                intersect: false,
                            },
                            hover: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                xAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Data'
                                        }
                                    }],
                                yAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Quantidade'
                                        }
                                    }]
                            }
                        }
                    };

                    var numeroDepositosEvolucao = {
                        type: 'line',
                        data: {
                    #{dashboardSecSaude.labelsNumeroDepositosEvolucao}
                        },
                        options: {
                            responsive: true,
                            title: {
                                display: false,
                                text: 'Chart.js Line Chart'
                            },
                            tooltips: {
                                mode: 'index',
                                intersect: false,
                            },
                            hover: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                xAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Data'
                                        }
                                    }],
                                yAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Quantidade'
                                        }
                                    }]
                            }
                        }
                    };

                    var depositosAcaoAgp = {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                    data: #{dashboardSecSaude.dataDepositoAcaoAgp},
                                    backgroundColor: [
                    #{dashboardSecSaude.colors}
                                    ],
                                    label: 'Dataset 1'
                                }],
                            labels:
                    #{dashboardSecSaude.labelDepositoAcaoAgp}
                        },
                        options: {
                            responsive: true,
                            legend: {
                                display: true,
                                position: 'right',
                            },
                            title: {
                                display: false,
                            },
                            animation: {
                                animateScale: true,
                                animateRotate: true
                            }
                        }
                    };

                    var evolucaoDepositosTipo = {
                        type: 'line',
                        data: {
                    #{dashboardSecSaude.labelsEvolucaoDespositosTipo}
                        },
                        options: {
                            responsive: true,
                            title: {
                                display: false,
                                text: 'Chart.js Line Chart'
                            },
                            tooltips: {
                                mode: 'index',
                                intersect: false,
                            },
                            hover: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                xAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Data'
                                        }
                                    }],
                                yAxes: [{
                                        display: true,
                                        scaleLabel: {
                                            display: false,
                                            labelString: 'Quantidade'
                                        }
                                    }]
                            }
                        }
                    };

                    var amostrasRoedoresAmostras = {
                        type: 'bar',
                        data: {
                    #{dashboardSecSaude.labelsamostrasRoedoresAmostras}
                        },
                        options: {
                            responsive: true,
                            title: {
                                display: false,
                                text: 'Chart.js Line Chart'
                            },
                            legend: {
                                position: 'top',
                            },
                        }
                    };

                    var inspecionadosxdengue = {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                    data: #{dashboardSecSaude.dataInspecionadosxDengue},
                                    backgroundColor: [
                    #{dashboardSecSaude.colors}
                                    ],
                                    label: 'Dataset 1'
                                }],
                            labels:
                    #{dashboardSecSaude.labelInspecionadosxDengue}
                        },
                        options: {
                            responsive: true,
                            legend: {
                                display: true,
                                position: 'right',
                            },
                            title: {
                                display: false,
                            },
                            animation: {
                                animateScale: true,
                                animateRotate: true
                            }
                        }
                    };
                    function gerarGraficos() {
                        var ctximoveisSitucaoAgp = document.getElementById('graficoImoveisSituacaoAgp').getContext('2d');
                        window.myDoughnut = new Chart(ctximoveisSitucaoAgp, imoveisSituacaoAgp);
                        var ctximoveisSitucaoDia = document.getElementById('graficoImoveisSituacaoDia').getContext('2d');
                        window.myDoughnut = new Chart(ctximoveisSitucaoDia, imoveisSituacaoDia);
                        var ctximoveisDiaTrab = document.getElementById('graficoImoveisDiaTrab').getContext('2d');
                        window.myDoughnut = new Chart(ctximoveisDiaTrab, imoveisDiaTrab);
                        var ctxinspecaoTratamentoAgp = document.getElementById('graficoInspecaoeTratamentoAgp').getContext('2d');
                        window.myDoughnut = new Chart(ctxinspecaoTratamentoAgp, inspecaoTratamentoAgp);
                        var ctxdepositosTipoAgp = document.getElementById('graficoDepositosTipo').getContext('2d');
                        window.myDoughnut = new Chart(ctxdepositosTipoAgp, depositosTipoAgp);
                        var ctximoveisInspecaoTratamentoTipo = document.getElementById('graficoimoveisInspecaoTratamentoTipo').getContext('2d');
                        window.myDoughnut = new Chart(ctximoveisInspecaoTratamentoTipo, imoveisInspecaoTratamentoTipo);
                        var ctxnumeroDepositosEvolucao = document.getElementById('graficoNumeroDepositosEvolucao').getContext('2d');
                        window.myDoughnut = new Chart(ctxnumeroDepositosEvolucao, numeroDepositosEvolucao);
                        var ctxdepositosAcaoAgp = document.getElementById('graficoDepositosAcao').getContext('2d');
                        window.myDoughnut = new Chart(ctxdepositosAcaoAgp, depositosAcaoAgp);
                        var ctxevolucaoDepositosTipo = document.getElementById('graficoEvolucaoDepositosTipo').getContext('2d');
                        window.myDoughnut = new Chart(ctxevolucaoDepositosTipo, evolucaoDepositosTipo);
                        var ctxamostrasRoedoresAmostras = document.getElementById('graficoAmostrasRoedoresAmostras').getContext('2d');
                        window.myDoughnut = new Chart(ctxamostrasRoedoresAmostras, amostrasRoedoresAmostras);
                        var ctxinspecionadosxdengue = document.getElementById('graficoInspecionadosxDengue').getContext('2d');
                        window.myDoughnut = new Chart(ctxinspecionadosxdengue, inspecionadosxdengue);
                    }
                    ;
                    window.onload = function () {
                        gerarGraficos();
                    };

                </script>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading_circular.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TmktDetPst;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TmktDetPstDao {

    public TmktDetPst ultimoServicoPrestador(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT RowConstrainedResult.* FROM ( "
                    + "     SELECT *, ROW_NUMBER() OVER (ORDER BY Data DESC, Hora DESC) RowNum "
                    + "     FROM ( "
                    + "         (SELECT TOP 10 'ContatosRelat' Tabela, Sequencia, Andamento, Nome, ContatosRelat.CodPessoa, "
                    + "         ContatosRelat.CodFil, Convert(Varchar,Data,112) Data, Hora "
                    + "         FROM ContatosRelat "
                    + "         LEFT JOIN Contatos on Contatos.Codigo = ContatosRelat.CodContato "
                    + "                           and Contatos.CodFil = ContatosRelat.CodFil "
                    + "         WHERE ContatosRelat.CodPessoa = ?) "
                    + "     UNION "
                    + "         (SELECT TOP 10 'TMktDetPst' Tabela, Sequencia, Andamento, Local Nome, TMktDetPst.CodPessoa, "
                    + "         TMktDetPst.CodFil, Convert(Varchar,Data,112) Data, Hora "
                    + "         FROM TMktDetPst "
                    + "         LEFT JOIN PstServ on PstServ.Secao = TMktDetPst.Secao "
                    + "                          and PstServ.CodFil = TMktDetPst.CodFil "
                    + "         WHERE CodPessoa = ?) "
                    + "     ) a) AS RowConstrainedResult "
                    + " WHERE RowNum = 1; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.setString(codPessoa);
            consulta.select();
            TmktDetPst retorno = null;
            while (consulta.Proximo()) {
                retorno = new TmktDetPst();
                retorno.setSequencia(consulta.getString("Sequencia"));
                retorno.setAndamento(consulta.getInt("Andamento"));
                retorno.setContato(consulta.getString("Nome"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setData(consulta.getString("Data"));
                retorno.setHora(consulta.getString("Hora"));
                retorno.setTipoCont(consulta.getString("Tabela"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("TmktDetPstDao.ultimoServicoPrestador - " + e.getMessage() + "\r\n"
                    + " SELECT RowConstrainedResult.* FROM ( "
                    + "     SELECT *, ROW_NUMBER() OVER (ORDER BY Data DESC, Hora DESC) RowNum "
                    + "     FROM ( "
                    + "         (SELECT TOP 10 'ContatosRelat' Tabela, Sequencia, Andamento, Nome, ContatosRelat.CodPessoa, "
                    + "         ContatosRelat.CodFil, Convert(Varchar,Data,112) Data, Hora "
                    + "         FROM ContatosRelat "
                    + "         LEFT JOIN Contatos on Contatos.Codigo = ContatosRelat.CodContato "
                    + "                           and Contatos.CodFil = ContatosRelat.CodFil "
                    + "         WHERE ContatosRelat.CodPessoa = " + codPessoa + ") "
                    + "     UNION "
                    + "         (SELECT TOP 10 'TMktDetPst' Tabela, Sequencia, Andamento, Local Nome, TMktDetPst.CodPessoa, "
                    + "         TMktDetPst.CodFil, Convert(Varchar,Data,112) Data, Hora "
                    + "         FROM TMktDetPst "
                    + "         LEFT JOIN PstServ on PstServ.Secao = TMktDetPst.Secao "
                    + "                          and PstServ.CodFil = TMktDetPst.CodFil "
                    + "         WHERE CodPessoa = " + codPessoa + ") "
                    + "     ) a) AS RowConstrainedResult "
                    + " WHERE RowNum = 1; ");
        }
    }

    public void updateTmkt(BigDecimal seq, TmktDetPst oTmktDetPst, Persistencia persistencia) throws Exception {

        String data_formatada = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("TELA");
        String data_atual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String hora = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA");

        String sql = "update TmktDetPst "
                + "set Andamento = ?, "
                + "Data = ?, "
                + "Hora = ?, "
                + "TipoCont = ?, "
                + "CodPessoa = ?, "
                + "Secao = ?, "
                + "CodFil = ?, "
                + "Historico = ?, "
                + "Detalhes = ?, "
                + "Operador = ?, "
                + "Dt_Alter = ?, "
                + "Hr_alter = ?, "
                + "Situacao = ?, "
                + "Longitude = ?, "
                + "Latitude = ?, "
                + "Precisao = ?"
                + " where Sequencia = ?  ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(0);
            consulta.setString(data_atual);
            consulta.setString(oTmktDetPst.getHora());
            consulta.setString("V");
            consulta.setBigDecimal(oTmktDetPst.getCodPessoa());
            consulta.setString(oTmktDetPst.getSecao());
            consulta.setBigDecimal(oTmktDetPst.getCodFil());
            consulta.setString(oTmktDetPst.getHistorico() + " - " + data_formatada); // Motivos
            String Detalhes = oTmktDetPst.getDetalhes(); // Observação
            consulta.setString(Detalhes);
            consulta.setString(FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora);
            consulta.setString("PD");
            consulta.setString(oTmktDetPst.getLongitude());
            consulta.setString(oTmktDetPst.getLatitude());
            consulta.setString(oTmktDetPst.getPrecisao());
            consulta.setBigDecimal(seq);
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new Exception("Falha ao atualizar dados do posto - " + e.getMessage());
        }
    }

    public void updateTmkt(TmktDetPst oTmktDetPst, String data_atual, String hora_atual, Persistencia persistencia) throws Exception {
        String sql = " update TmktDetPst set Andamento = ?, Data = ?, Hora = ?, TipoCont = ?, CodPessoa = ?, Secao = ?, "
                + " CodFil = ?, Historico = ?, Detalhes = ?, Operador = ?, Dt_Alter = ?, Hr_alter = ?, Situacao = ?, "
                + " Longitude = ?, Latitude = ?, Precisao = ?, fotos = ? where Sequencia = ? ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(0);
            consulta.setString(data_atual);
            consulta.setString(oTmktDetPst.getHora());
            consulta.setString(oTmktDetPst.getTipoCont());
            consulta.setBigDecimal(oTmktDetPst.getCodPessoa());
            consulta.setString(oTmktDetPst.getSecao());
            consulta.setBigDecimal(oTmktDetPst.getCodFil());
            consulta.setString(oTmktDetPst.getHistorico());
            consulta.setString(oTmktDetPst.getDetalhes());
            consulta.setString(FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora_atual);
            consulta.setString("PD");
            consulta.setString(oTmktDetPst.getLongitude());
            consulta.setString(oTmktDetPst.getLatitude());
            consulta.setString(oTmktDetPst.getPrecisao());
            consulta.setString(oTmktDetPst.getFotos());
            consulta.setBigDecimal(oTmktDetPst.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TmktDetPstDao.updateTmkt() - " + e.getMessage() + "\r\n"
                    + " update TmktDetPst set Andamento = " + 0 + ", Data = " + data_atual + ", Hora = " + oTmktDetPst.getHora() + ", "
                    + " TipoCont = " + "V" + ", CodPessoa = " + oTmktDetPst.getCodPessoa() + ", Secao = " + oTmktDetPst.getSecao() + ", "
                    + " CodFil = " + oTmktDetPst.getCodFil() + ", Historico = " + oTmktDetPst.getHistorico() + ", Detalhes = " + oTmktDetPst.getDetalhes() + ", "
                    + " Operador = " + FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10) + ", Dt_Alter = " + data_atual + ", Hr_alter = " + hora_atual + ", "
                    + " Situacao = " + "PD" + ", Longitude = " + oTmktDetPst.getLongitude() + ", Latitude = " + oTmktDetPst.getLatitude() + ","
                    + " Precisao = " + oTmktDetPst.getPrecisao() + " where Sequencia = " + oTmktDetPst.getSequencia() + " ");
        }
    }

    public void updateQtdeFoto(String seq, Integer qtde, Persistencia persistencia) throws Exception {

        String sql = "update TmktDetPst set QtdeFotos = ? "
                + " where Sequencia = ?  ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(qtde);
            consulta.setString(seq);
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new Exception("Falha ao atualizar qtde fotos - " + e.getMessage());
        }
    }

    public Integer getQtdeFt(String seq, Persistencia persistencia) throws Exception {
        Integer qtdFt = 0;
        try {
            Consulta stm = new Consulta("select QtdeFotos from tmktdetpst where Sequencia=? ", persistencia);
            stm.setString(seq);
            stm.select();

            while (stm.Proximo()) {
                qtdFt = stm.getInt("QtdeFotos");
            }
            if (qtdFt == null) {
                qtdFt = 1;
            } else {
                qtdFt++;
            }
            stm.Close();
        } catch (SQLException e) {
            throw new Exception("Falha ao carregar qtde fotos - " + e.getMessage());
        }
        return qtdFt;
    }

    public BigDecimal getSequencia(Persistencia persistencia) throws Exception {
        try {
            Consulta stm = new Consulta("select IsNull(max(sequencia),0)+1 as sequencia from tmktdetpst", persistencia);
            stm.select();
            BigDecimal seq = null;
            while (stm.Proximo()) {
                seq = new BigDecimal(stm.getString("sequencia"));
            }
            //seq = seq.add(new BigDecimal(1));
            stm.Close();
            return seq;
        } catch (Exception e) {
            throw new Exception("TmktDetPstDao.getSequencia - " + e.getMessage() + "\r\n"
                    + "select IsNull(max(sequencia),0)+1 as sequencia from tmktdetpst");
        }
    }

    public Boolean insereTmktDetPst(BigDecimal seq, String matr, TmktDetPst oTmktDetPst, Persistencia persistencia) throws Exception {
        boolean retorno;
        String data_formatada = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("TELA");
        String data_atual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String hora = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA");

        String sql = "insert into TmktDetPst(Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, Secao, CodFil, Historico, Detalhes, "
                + "Operador, Dt_Alter, Hr_alter, Situacao, Longitude, Latitude, Precisao) "
                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(seq);
            consulta.setInt(0);
            consulta.setString(data_atual);
            consulta.setString(oTmktDetPst.getHora());
            consulta.setString("V");
            consulta.setBigDecimal(oTmktDetPst.getCodPessoa());
            consulta.setString(oTmktDetPst.getSecao());
            consulta.setBigDecimal(oTmktDetPst.getCodFil());
            consulta.setString(oTmktDetPst.getHistorico() + " - " + data_formatada); // Motivos
            consulta.setString(oTmktDetPst.getDetalhes());// Observação
            consulta.setString(FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora);
            consulta.setString("PD");
            consulta.setString(oTmktDetPst.getLongitude());
            consulta.setString(oTmktDetPst.getLatitude());
            consulta.setString(oTmktDetPst.getPrecisao());

            consulta.insert();
            consulta.close();

            retorno = true;
        } catch (SQLException e) {
            throw new Exception("Falha ao gravar questionário - " + e.getMessage());
        }
        return retorno;
    }

    public Boolean insereTmktDetPst(TmktDetPst oTmktDetPst, String data_atual, String hora, String data_formatada, Persistencia persistencia) throws Exception {
        boolean retorno;
        String sql = "insert into TmktDetPst(Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, Secao, CodFil, Historico, Detalhes, "
                + " Operador, Dt_Alter, Hr_alter, Situacao, Longitude, Latitude, Precisao) "
                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(oTmktDetPst.getSequencia());
            consulta.setInt(0);
            consulta.setString(data_atual);
            consulta.setString(oTmktDetPst.getHora());
            consulta.setString("V");
            consulta.setBigDecimal(oTmktDetPst.getCodPessoa());
            consulta.setString(oTmktDetPst.getSecao());
            consulta.setBigDecimal(oTmktDetPst.getCodFil());
            consulta.setString(oTmktDetPst.getHistorico() + " - " + data_formatada); // Motivos
            consulta.setString(oTmktDetPst.getDetalhes());
            consulta.setString(FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora);
            consulta.setString("PD");
            consulta.setString(oTmktDetPst.getLongitude());
            consulta.setString(oTmktDetPst.getLatitude());
            consulta.setString(oTmktDetPst.getPrecisao());

            consulta.insert();
            consulta.close();

            retorno = true;
        } catch (SQLException e) {
            throw new Exception("Falha ao gravar questionário - " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Insere uma nova entrada em TmkDetPst. A data formata no histório é
     * inserida no objeto oTmktDetPst previamente.
     *
     * @param oTmktDetPst
     * @param data_atual
     * @param hora_atual
     * @param persistencia
     * @throws Exception
     */
    public void insereTmktDetPst(TmktDetPst oTmktDetPst, String data_atual, String hora_atual, Persistencia persistencia) throws Exception {
        String sql = "insert into TmktDetPst(Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, Secao, CodFil, Historico, Detalhes, "
                + " Operador, Dt_Alter, Hr_alter, Situacao, Longitude, Latitude, Precisao, FiltroWeb) "
                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(oTmktDetPst.getSequencia());
            consulta.setInt(0);
            consulta.setString(oTmktDetPst.getData());
            consulta.setString(oTmktDetPst.getHora());
            consulta.setString(oTmktDetPst.getTipoCont());
            consulta.setBigDecimal(oTmktDetPst.getCodPessoa());
            consulta.setString(oTmktDetPst.getSecao());
            consulta.setBigDecimal(oTmktDetPst.getCodFil());
            consulta.setString(oTmktDetPst.getHistorico());
            consulta.setString(oTmktDetPst.getDetalhes());
            consulta.setString(FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10));
            consulta.setString(data_atual);
            consulta.setString(hora_atual);
            consulta.setString("PD");
            consulta.setString(oTmktDetPst.getLongitude());
            consulta.setString(oTmktDetPst.getLatitude());
            consulta.setString(oTmktDetPst.getPrecisao());
            consulta.setString(oTmktDetPst.isFiltroWeb() ? "1" : "0");

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("TmktDetPstDao.insereTmktDetPst() - " + e.getMessage() + "\r\n"
                    + "insert into TmktDetPst(Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, Secao, CodFil, Historico, Detalhes, "
                    + " Operador, Dt_Alter, Hr_alter, Situacao, Longitude, Latitude, Precisao) "
                    + " values(" + oTmktDetPst.getSequencia() + "," + 0 + "," + oTmktDetPst.getData() + "," + oTmktDetPst.getHora() + "," + "V" + ","
                    + oTmktDetPst.getCodPessoa() + "," + oTmktDetPst.getSecao() + "," + oTmktDetPst.getCodFil() + "," + oTmktDetPst.getHistorico() + ","
                    + oTmktDetPst.getDetalhes() + "," + FuncoesString.RecortaString(oTmktDetPst.getOperador(), 0, 10) + "," + data_atual + "," + hora_atual + ","
                    + "PD" + "," + oTmktDetPst.getLongitude() + "," + oTmktDetPst.getLatitude() + "," + oTmktDetPst.getPrecisao() + ")");

        }
    }

    public List<TmktDetPst> getPostosSuperv(Persistencia persistencia) {
        String data_sql = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String sql2 = "Select Secao From tmktdetpst Where Data = ? ";
        TmktDetPst oTmktDetPst;
        List<TmktDetPst> lTmktDetPst = null;
        try {

            Consulta pesq = new Consulta(sql2, persistencia);
            pesq.setString(data_sql);
//            try {
            pesq.select();

            //Para evitar exceção caso o valor seja nulo
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSecao("-1");

            while (pesq.Proximo()) {
                lTmktDetPst = new ArrayList<TmktDetPst>();
                oTmktDetPst = new TmktDetPst();

                oTmktDetPst.setSecao(pesq.getString("Secao"));
                lTmktDetPst.add(oTmktDetPst);
            }
            pesq.Close();
        } catch (Exception e) {
//            throw new Exception("Falha ao atualizar qtde fotos - " + e.getMessage());
            lTmktDetPst = new ArrayList<TmktDetPst>();
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSecao("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        if (lTmktDetPst != null) {
        } else {
            lTmktDetPst = new ArrayList<TmktDetPst>();
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSecao("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        return lTmktDetPst;
    }

    /**
     * Verifica a existencia da supervisão
     *
     * @param codfil codigo da filial
     * @param secao seção do posto
     * @param persistencia conexão com o banco de dadso
     * @return existe supervisão
     * @throws Exception
     */
    public boolean isSupervisionado(String codfil, String secao, String data, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM tmktdetpst WHERE Secao = ? AND CodFil = ? AND data = ? and TipoCont = 'V' ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codfil);
            consulta.setString(data);
            consulta.select();

            int qtd = 0;
            while (consulta.Proximo()) {
                qtd = consulta.getInt("qtd");
            }

            if (qtd > 0) {
                existe = true;
            }
        } catch (Exception e) {
            throw new Exception("Ocorreu um erro: " + e.getMessage());
        }
        return existe;
    }

    /**
     * Busca por supervisões anteriores (TipoCont = V)
     *
     * @param codfil
     * @param secao
     * @param persistencia
     * @return
     */
    public List<TmktDetPst> getSupervisoes(String codfil, String secao, String data, Persistencia persistencia) {
        String sql2 = "select Sequencia, Historico, Detalhes, Data, Hora, Longitude, Latitude, Precisao "
                + "from TmktDetPst "
                + "where Codfil=? and secao = ? and data = ? and TipoCont = 'V' ";
        TmktDetPst oTmktDetPst;
        List<TmktDetPst> lTmktDetPst = null;
        try {

            Consulta pesq = new Consulta(sql2, persistencia);
            pesq.setString(codfil);
            pesq.setString(secao);
            pesq.setString(data);
            pesq.select();

            //Para evitar exceção caso o valor seja nulo
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSequencia("-1");
            oTmktDetPst.setHistorico("-1");
            oTmktDetPst.setDetalhes("-1");
            oTmktDetPst.setData("-1");
            oTmktDetPst.setHora("-1");
            oTmktDetPst.setLongitude("-1");
            oTmktDetPst.setLatitude("-1");
            oTmktDetPst.setPrecisao("-1");

            while (pesq.Proximo()) {
                lTmktDetPst = new ArrayList<>();
                oTmktDetPst = new TmktDetPst();

                oTmktDetPst.setSequencia(pesq.getString("Sequencia"));
                oTmktDetPst.setHistorico(pesq.getString("Historico"));
                oTmktDetPst.setDetalhes(pesq.getString("Detalhes"));
                oTmktDetPst.setData(pesq.getString("Data"));
                oTmktDetPst.setHora(pesq.getString("Hora"));
                oTmktDetPst.setLongitude(pesq.getString("Longitude"));
                oTmktDetPst.setLatitude(pesq.getString("Latitude"));
                oTmktDetPst.setPrecisao(pesq.getString("Precisao"));
                lTmktDetPst.add(oTmktDetPst);
            }
            pesq.Close();
        } catch (Exception e) {
//            throw new Exception("Falha ao atualizar qtde fotos - " + e.getMessage());
            lTmktDetPst = new ArrayList<>();
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSequencia("-1");
            oTmktDetPst.setHistorico("-1");
            oTmktDetPst.setDetalhes("-1");
            oTmktDetPst.setData("-1");
            oTmktDetPst.setHora("-1");
            oTmktDetPst.setLongitude("-1");
            oTmktDetPst.setLatitude("-1");
            oTmktDetPst.setPrecisao("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        if (lTmktDetPst != null) {
        } else {
            lTmktDetPst = new ArrayList<TmktDetPst>();
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSequencia("-1");
            oTmktDetPst.setHistorico("-1");
            oTmktDetPst.setDetalhes("-1");
            oTmktDetPst.setData("-1");
            oTmktDetPst.setHora("-1");
            oTmktDetPst.setLongitude("-1");
            oTmktDetPst.setLatitude("-1");
            oTmktDetPst.setPrecisao("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        return lTmktDetPst;
    }

    public List<TmktDetPst> getMotivosSuperv(String codfil, String secao, String data_sql, Persistencia persistencia) {
        String sql2 = "select Sequencia, Historico, Detalhes, Data, Hora, Longitude, Latitude, Precisao "
                + "from TmktDetPst "
                + "where Codfil=? and secao = ? and data = ? ";
        TmktDetPst oTmktDetPst;
        List<TmktDetPst> lTmktDetPst = null;
        try {

            Consulta pesq = new Consulta(sql2, persistencia);
            pesq.setString(codfil);
            pesq.setString(secao);
            pesq.setString(data_sql);
            pesq.select();

            //Para evitar exceção caso o valor seja nulo
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSequencia("-1");
            oTmktDetPst.setHistorico("-1");
            oTmktDetPst.setDetalhes("-1");
            oTmktDetPst.setData("-1");
            oTmktDetPst.setHora("-1");
            oTmktDetPst.setLongitude("-1");
            oTmktDetPst.setLatitude("-1");
            oTmktDetPst.setPrecisao("-1");

            while (pesq.Proximo()) {
                lTmktDetPst = new ArrayList<TmktDetPst>();
                oTmktDetPst = new TmktDetPst();

                oTmktDetPst.setSequencia(pesq.getString("Sequencia"));
                oTmktDetPst.setHistorico(pesq.getString("Historico"));
                oTmktDetPst.setDetalhes(pesq.getString("Detalhes"));
                oTmktDetPst.setData(pesq.getString("Data"));
                oTmktDetPst.setHora(pesq.getString("Hora"));
                oTmktDetPst.setLongitude(pesq.getString("Longitude"));
                oTmktDetPst.setLatitude(pesq.getString("Latitude"));
                oTmktDetPst.setPrecisao(pesq.getString("Precisao"));
                lTmktDetPst.add(oTmktDetPst);
            }
            pesq.Close();
        } catch (Exception e) {
//            throw new Exception("Falha ao atualizar qtde fotos - " + e.getMessage());
            lTmktDetPst = new ArrayList<TmktDetPst>();
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSequencia("-1");
            oTmktDetPst.setHistorico("-1");
            oTmktDetPst.setDetalhes("-1");
            oTmktDetPst.setData("-1");
            oTmktDetPst.setHora("-1");
            oTmktDetPst.setLongitude("-1");
            oTmktDetPst.setLatitude("-1");
            oTmktDetPst.setPrecisao("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        if (lTmktDetPst != null) {
        } else {
            lTmktDetPst = new ArrayList<>();
            oTmktDetPst = new TmktDetPst();
            oTmktDetPst.setSequencia("-1");
            oTmktDetPst.setHistorico("-1");
            oTmktDetPst.setDetalhes("-1");
            oTmktDetPst.setData("-1");
            oTmktDetPst.setHora("-1");
            oTmktDetPst.setLongitude("-1");
            oTmktDetPst.setLatitude("-1");
            oTmktDetPst.setPrecisao("-1");
            lTmktDetPst.add(oTmktDetPst);
        }
        return lTmktDetPst;
    }

    /**
     * Busca o número de sequencia de uma supervisão a partir da sequência da
     * rota e do código do posto;
     *
     * @param seqRota
     * @param secao - código do posto
     * @param persistencia
     * @return
     */
    public BigDecimal getSequenciaEdicaoSupervisao(BigDecimal seqRota, String secao, Persistencia persistencia) {
        try {
            String sql = "Select Max(TMktDetPst.Sequencia) Sequencia , "
                    + " Max(PstServ.Local) Local, Max(TMktDetPst.Data) Data, Max(TMktDetPst.Hora) Hora from Rt_Perc "
                    + " left join Rotas on Rotas.Sequencia = Rt_perc.Sequencia"
                    + " left join PstServ on PstServ.CodCli = Rt_perc.CodCli1"
                    + "                  and PstServ.CodFil = Rotas.CodFil"
                    + " left join TMktDetPst on TMktDetPst.Secao = PstServ.Secao"
                    + "                     and TmktDetPst.CodFil = PstServ.CodFil"
                    + "                     and TmkTDetPst.Data = Rotas.Data"
                    + " where Rotas.Sequencia = ?"
                    + "   and PstServ.Secao = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(seqRota);
            consult.setString(secao);
            consult.select();
            BigDecimal sequencia = BigDecimal.ZERO;
            while (consult.Proximo()) {
                sequencia = consult.getBigDecimal("Sequencia");
            }
            consult.Close();
            return sequencia;
        } catch (Exception ex) {
            Logger.getLogger(TmktDetPstDao.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class OperacoesServicos {
    private String Numero;
    private String CodFil;
    private String Data;
    private String DescrFilial;
    private String FuncSubs;
    private String Motivo_Subs;
    private String Hora_Extra;
    private String VR;
    private String VT;
    private String Hospedagem;
    private String Hora1;
    private String Hora4;
    private String HEDiurna;
    private String HENoturna;
    private String Nro_HE;
    private String Motivo_Aus;
    private String FuncAus;
    private String Mesario;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Periodo;
    private String Posto;
    private String RefTipo2;
    private String NumCalculo;
    private String Intraj;
    private String NomeAus;
    private String NomeSubs;
    private String CodSrv;
    private String LocalPosto;
    private String OBS;
    private String Pedido;
    private String Regional;
    private String DescRegiao;
    private String Flag_Excl;
    private String Ocorrencia;
    private String TotalHorasDecimais;
    private boolean PagarTipo2;
    private String IndicadorHoras;

    public String getNumero() {
        return Numero;
    }

    public void setNumero(String Numero) {
        this.Numero = Numero;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getDescrFilial() {
        return DescrFilial;
    }

    public void setDescrFilial(String DescrFilial) {
        this.DescrFilial = DescrFilial;
    }

    public String getFuncSubs() {
        return FuncSubs;
    }

    public void setFuncSubs(String FuncSubs) {
        this.FuncSubs = FuncSubs;
    }

    public String getMotivo_Subs() {
        return Motivo_Subs;
    }

    public void setMotivo_Subs(String Motivo_Subs) {
        this.Motivo_Subs = Motivo_Subs;
    }

    public String getHora_Extra() {
        return Hora_Extra;
    }

    public void setHora_Extra(String Hora_Extra) {
        this.Hora_Extra = Hora_Extra;
    }

    public String getVR() {
        return VR;
    }

    public void setVR(String VR) {
        this.VR = VR;
    }

    public String getVT() {
        return VT;
    }

    public void setVT(String VT) {
        this.VT = VT;
    }

    public String getHospedagem() {
        return Hospedagem;
    }

    public void setHospedagem(String Hospedagem) {
        this.Hospedagem = Hospedagem;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getHora4() {
        return Hora4;
    }

    public void setHora4(String Hora4) {
        this.Hora4 = Hora4;
    }

    public String getHEDiurna() {
        return HEDiurna;
    }

    public void setHEDiurna(String HEDiurna) {
        this.HEDiurna = HEDiurna;
    }

    public String getHENoturna() {
        return HENoturna;
    }

    public void setHENoturna(String HENoturna) {
        this.HENoturna = HENoturna;
    }

    public String getNro_HE() {
        return Nro_HE;
    }

    public void setNro_HE(String Nro_HE) {
        this.Nro_HE = Nro_HE;
    }

    public String getMotivo_Aus() {
        return Motivo_Aus;
    }

    public void setMotivo_Aus(String Motivo_Aus) {
        this.Motivo_Aus = Motivo_Aus;
    }

    public String getFuncAus() {
        return FuncAus;
    }

    public void setFuncAus(String FuncAus) {
        this.FuncAus = FuncAus;
    }

    public String getMesario() {
        return Mesario;
    }

    public void setMesario(String Mesario) {
        this.Mesario = Mesario;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getPeriodo() {
        return Periodo;
    }

    public void setPeriodo(String Periodo) {
        this.Periodo = Periodo;
    }

    public String getPosto() {
        return Posto;
    }

    public void setPosto(String Posto) {
        this.Posto = Posto;
    }

    public String getRefTipo2() {
        return RefTipo2;
    }

    public void setRefTipo2(String RefTipo2) {
        this.RefTipo2 = RefTipo2;
    }

    public String getNumCalculo() {
        return NumCalculo;
    }

    public void setNumCalculo(String NumCalculo) {
        this.NumCalculo = NumCalculo;
    }

    public String getIntraj() {
        return Intraj;
    }

    public void setIntraj(String Intraj) {
        this.Intraj = Intraj;
    }

    public String getNomeAus() {
        return NomeAus;
    }

    public void setNomeAus(String NomeAus) {
        this.NomeAus = NomeAus;
    }

    public String getNomeSubs() {
        return NomeSubs;
    }

    public void setNomeSubs(String NomeSubs) {
        this.NomeSubs = NomeSubs;
    }

    public String getCodSrv() {
        return CodSrv;
    }

    public void setCodSrv(String CodSrv) {
        this.CodSrv = CodSrv;
    }

    public String getLocalPosto() {
        return LocalPosto;
    }

    public void setLocalPosto(String LocalPosto) {
        this.LocalPosto = LocalPosto;
    }

    public String getOBS() {
        return OBS;
    }

    public void setOBS(String OBS) {
        this.OBS = OBS;
    }

    public String getPedido() {
        return Pedido;
    }

    public void setPedido(String Pedido) {
        this.Pedido = Pedido;
    }

    public String getRegional() {
        return Regional;
    }

    public void setRegional(String Regional) {
        this.Regional = Regional;
    }

    public String getDescRegiao() {
        return DescRegiao;
    }

    public void setDescRegiao(String DescRegiao) {
        this.DescRegiao = DescRegiao;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }    

    public String getOcorrencia() {
        return Ocorrencia;
    }

    public void setOcorrencia(String Ocorrencia) {
        this.Ocorrencia = Ocorrencia;
    }

    public String getTotalHorasDecimais() {
        return TotalHorasDecimais;
    }

    public void setTotalHorasDecimais(String TotalHorasDecimais) {
        this.TotalHorasDecimais = TotalHorasDecimais;
    }

    public boolean isPagarTipo2() {
        return PagarTipo2;
    }

    public void setPagarTipo2(boolean PagarTipo2) {
        this.PagarTipo2 = PagarTipo2;
    }

    public String getIndicadorHoras() {
        return IndicadorHoras;
    }

    public void setIndicadorHoras(String IndicadorHoras) {
        this.IndicadorHoras = IndicadorHoras;
    }
}

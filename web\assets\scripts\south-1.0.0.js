/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
var $dlAzulOkgMessage = null;
var $dlgLaranjaOkMessage = null;
var $dlgVermelhoOkMessage = null;
var $dlgVerdeSimNaoMessage = null;
var PontoMenuData = '<div class="LightBox" id="LightBoxPonto" style="display: none"></div>';
PontoMenuData += '       <div class="MenuPonto" style="display: none">';
PontoMenuData += '            <div class="col-md-12 col-sm-12 col-xs-12">';
PontoMenuData += '                <table class="ListaMenu" style="top: 0px !important">';
PontoMenuData += '                    <tr>';
PontoMenuData += '                        <td onclick="top.location.href=\'ponto_mob.xhtml\'"><i class="fa fa-home" aria-hidden="true"></i>In<PERSON>cio</td>';
PontoMenuData += '                    </tr>';
PontoMenuData += '                    <tr>';
PontoMenuData += '                        <td onclick="top.location.href=\'ponto_mob_historico.xhtml\'"><i class="fa fa-history" aria-hidden="true"></i>Histórico</td>';
PontoMenuData += '                    </tr>';
PontoMenuData += '                    <tr>';
PontoMenuData += '                        <td onclick="top.location.href=\'portal_rh.xhtml\'"><i class="fa fa-certificate" aria-hidden="true"></i>Portal RH</td>';
PontoMenuData += '                    </tr>';
//PontoMenuData += '                    <tr>';
//PontoMenuData += '                        <td onclick="top.location.href=\'inspecao_mob.xhtml\'"><i class="fa fa-check-square-o" aria-hidden="true"></i>Inspeções</td>';
//PontoMenuData += '                    </tr>';
PontoMenuData += '                    <tr>';
PontoMenuData += '                        <td style="color: red" onclick="top.location.href=\'index.xhtml\'"><i class="fa fa-sign-out" aria-hidden="true"></i>Sair</td>';
PontoMenuData += '                    </tr>';
PontoMenuData += '                </table>';
PontoMenuData += '            </div>';
PontoMenuData += '        </div>';

$(document).ready(function () {
    try{
    $('[id*="bodyWebPonto"]').prepend(PontoMenuData);

    setInterval(function () {
        if (!$('#btGeralHome').attr('id') &&
                (!ObterParamURL('portal') ||
                        !ObterParamURL('portal') === 'S')) {
            let btHome = '<img id="btGeralHome" src="../assets/img/icone_home_branco.png" style="width:40px;height:40px;position:absolute;margin-top:0px; right:35px; cursor:pointer" title="Home" onclick="top.location.href = \'../menu.xhtml\'" />';
            $('#divBotaoVoltar').prepend(btHome);
        }
    }, 1000);
    }
    catch(e){}
})
        .on('click', '.footer-logos a', function () {
            if (top.location.href.indexOf('/msl/') > -1) {
                window.parent.AtualizarMenu();
            }
        })
        .on('click', '[ref="BarsMenu"]', function () {
            if ($('#LightBoxPonto').css('display') === 'none') {
                $('#LightBoxPonto, .MenuPonto').css('display', '');
            } else
                $('#LightBoxPonto').click();
        })
        .on('click', '#LightBoxPonto', function () {
            $(this).css('display', 'none');
            $('.MenuPonto').css('display', 'none');
        })
        .on('click', '#btRotacionarEsquerda', function () {
            let Rotate = eval($('#tdRelFoto').find('img').attr('rotate')) - 1;

            switch ($('#tdRelFoto').find('img').attr('rotate')) {
                case '0':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(-90deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(-90deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(-90deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '-1':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(-180deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(-180deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(-180deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '2':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(90deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(90deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(90deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());

                    break;

                case '-2':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(-270deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(-270deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(-270deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '3':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(180deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(180deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(180deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '-3':
                case '1':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(0deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(0deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(0deg)');
                    $('#tdRelFoto').find('img').attr('rotate', '0');
                    break;
            }

            $('#tdRelFoto').find('img').css('max-width', '100%');
        })
        .on('click', '#btRotacionarDireita', function () {
            let Rotate = eval($('#tdRelFoto').find('img').attr('rotate')) + 1;

            switch ($('#tdRelFoto').find('img').attr('rotate')) {
                case '0':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(90deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(90deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(90deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '-2':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(-90deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(-90deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(-90deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '1':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(180deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(180deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(180deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '-3':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(-180deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(-180deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(-180deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '2':

                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(270deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(270deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(270deg)');
                    $('#tdRelFoto').find('img').attr('rotate', Rotate.toString());
                    break;

                case '3':
                case '-1':
                    $('#tdRelFoto').find('img').css('-webkit-transform', 'rotate(0deg)');
                    $('#tdRelFoto').find('img').css('-moz-transform', 'rotate(0deg)');
                    $('#tdRelFoto').find('img').css('-o-transform', 'rotate(0deg)');
                    $('#tdRelFoto').find('img').attr('rotate', '0');
                    break;
            }

            $('#tdRelFoto').find('img').css('max-width', '100%');
        })
        .on('change', 'input, textarea, select', function () {
            $(this).removeClass('form-control-required');
        })
        .on('keypress', 'input[south-type="numero"]', function () {
            SomenteNumero(this);
        })
        .on('keyup', 'input[south-type="numero"]', function () {
            SomenteNumero(this);
        })
        .on('keydown', 'input[south-type="numero"]', function () {
            SomenteNumero(this);
        })
        ;


function AbrirFoto(EnderecoFoto, CallBack) {
    /*img = new Image();





    img.onload = function () {*/
        let HTML = '';
        HTML += '<div id="divVisuailzacaoFoto" style="padding: 10px !important; text-align: center; min-width: 100% !important;width: 100% !important;max-width: 100% !important; padding: 0px !important">';
        HTML += '   <center><table style="width: 100%; height: calc(100vh - 210px);">';
        HTML += '      <tr>';
        HTML += '         <td style="vertical-align: middle; text-align: center;" id="tdRelFoto"><i id="btRotacionarEsquerda" class="fa fa-undo" aria-hidden="true" style="display: none; border: 2px solid #CCC; cursor:pointer;background-color: #000; color: #FFF; font-size: 16pt; width: 40px;z-index: 999999 !important; height: 40px; border-radius: 50%; position:absolute; top:0;bottom:0;left:0;margin:auto; text-align: center; padding-top: 8px;"></i><img rotate="0" src="' + EnderecoFoto + '" style="height: auto !important; max-height: calc(100vh - 300px) !important;" /><i id="btRotacionarDireita" class="fa fa-repeat" aria-hidden="true" style="background-color: #000; color: #FFF; font-size: 16pt; width: 40px; height: 40px; border-radius: 50%; position:absolute; top:0;bottom:0;right:0;margin:auto; text-align: center; padding-top: 8px;cursor:pointer;z-index: 999999 !important;border: 2px solid #CCC;display: none;"></i></td>';
        HTML += '      </tr>';
        HTML += '   </table></center>';
        HTML += '</div>';

        $('body').append(HTML);

        $frmFoto = $.alert({
            title: false,
            theme: 'modern',
            type: 'green',
            content: ReplaceAll(HTML, 'id="divVisuailzacaoFoto"', ''),
            closeIcon: true,
            boxWidth: '90%',
            useBootstrap: false,
            buttons: {
                ok: {
                    isHidden: true,
                    text: 'Ok',
                    btnClass: 'objHidden'
                }
            },
            onContentReady: function () {
                $('#divVisuailzacaoFoto').remove();
                $('[id^="btRotacionar"]').css('display', '');
                
                if(typeof CallBack == 'function') CallBack.call();
            }
        });
    /*};

    img.src = EnderecoFoto;*/
}

function CriarAtributos(CodigoLanguage) {
    if (CodigoLanguage &&
            CodigoLanguage.toString() === '3'){
        // EN
        $('input[south-type="monetario"]').mask('#,##0.00', {
            reverse: true
        });
        
        $('input[south-type="hora"]').attr({
            maxlength: "13"
        }).mask('00:00');
    }
    else{
        // PT / ES
        $('input[south-type="monetario"]').mask('#.##0,00', {
            reverse: true
        });
        
        $('input[south-type="hora"]').attr({
            maxlength: "13"
        }).mask('00:00');
    }
   
    $('input[south-type="cpf"]').attr({
        maxlength: "13"
    }).mask('000.000.000-00');


    if (CodigoLanguage &&
            CodigoLanguage.toString() === '3') {
        $('input[south-type="telefone"]').attr({
            maxlength: "12"
        }).mask('0Z0000000000', {
            translation: {
                'Z': {
                    pattern: /[0-9]/, optional: true
                }
            }
        });

        $('input[south-type="cep"]').attr({
            maxlength: "5"
        }).mask('00000');
    } else {
        $('input[south-type="telefone"]').attr({
            maxlength: "15"
        }).mask('(00) Z0000-0000', {
            translation: {
                'Z': {
                    pattern: /[0-9]/, optional: true
                }
            }
        });

        $('input[south-type="cep"]').attr({
            maxlength: "9"
        }).mask('00000-000');
    }
}

function Imprimir(objImpressao) {
    $Dados = objImpressao.clone();
    if ($Dados.find('#btImprimirPontos').attr('id')) {
        $Dados.find('[id^="bt"]').remove();
        $Dados.find('[id*="paginator"]').remove();
        $Dados.find('[src*="pesquisar"]').remove();
        $Dados.find('.ui-datagrid-column').css('float', 'left').css('width', ' 49%').css('margin-left', '4px').css('font-size', '8pt').find('div').css('font-size', '8pt');
        $Dados.find('[id*="TopoGride"]').css('margin-top', '7.5px').css('margin-bottom', '7px').css('margin-left', '5px');
    }

    $Dados.find('table').css('font-family', 'Calibri').css('font-size', '8pt').find('th').css('background-color', '#DDD').css('color', '#000');
    $Dados.find('table').find('tbody tr th').css('background-color', '#DDD').css('color', '#000');
    $Dados.find('table').find('thead tr th').css('background-color', '#DDD').css('color', '#000');
    var conteudo = $Dados.html();
    tela_impressao = window.open('about:blank');

    tela_impressao.document.write(conteudo);
    tela_impressao.window.print();
    tela_impressao.window.close();
}

function Imprimir(Cabecalho, objImpressao) {
    $Dados = objImpressao.clone();
    if ($Dados.find('#btImprimirPontos').attr('id')) {
        $Dados.find('[id^="bt"]').remove();
        $Dados.find('[id*="paginator"]').remove();
        $Dados.find('[src*="pesquisar"]').remove();
        $Dados.find('.ui-datagrid-column').css('float', 'left').css('width', ' 49%').css('margin-left', '4px').css('font-size', '8pt').find('div').css('font-size', '8pt');
        $Dados.find('[id*="TopoGride"]').css('margin-top', '7.5px').css('margin-bottom', '7px').css('margin-left', '5px');
    }

    $Dados.find('table').css('font-family', 'Calibri').css('font-size', '8pt').find('th').css('background-color', '#DDD').css('color', '#000');
    $Dados.find('table').find('tbody tr th').css('background-color', '#DDD').css('color', '#000');
    $Dados.find('table').find('thead tr th').css('background-color', '#DDD').css('color', '#000');
    
    $Dados.find('table').find('tbody tr td').css('border-bottom', 'thin solid #DDD');
    
    var conteudo = $Dados.html();
    tela_impressao = window.open('about:blank');

    tela_impressao.document.write(Cabecalho + conteudo);
    tela_impressao.window.print();
    tela_impressao.window.close();
}

function ImprimirResumoCaixa(Cabecalho, objImpressao, objImpressaoResumo) {
    $Dados = objImpressao.clone();
    $DadosResumo = objImpressaoResumo.clone();
    
    if ($Dados.find('#btImprimirPontos').attr('id')) {
        $Dados.find('[id^="bt"]').remove();
        $Dados.find('[id*="paginator"]').remove();
        $Dados.find('[src*="pesquisar"]').remove();
        $Dados.find('.ui-datagrid-column').css('float', 'left').css('width', ' 49%').css('margin-left', '4px').css('font-size', '8pt').find('div').css('font-size', '8pt');
        $Dados.find('[id*="TopoGride"]').css('margin-top', '7.5px').css('margin-bottom', '7px').css('margin-left', '5px');
    }
    
    $DadosResumo.find('tbody .ui-column-title').remove();
    $DadosResumo.find('thead:eq(0)').remove();
    
    $Dados.find('tbody .ui-column-title').remove();
    $Dados.find('thead:eq(0)').remove();

    $DadosResumo.find('table').css('font-family', 'Calibri').css('font-size', '8pt').find('th').css('background-color', '#DDD').css('color', '#000');
    $DadosResumo.find('table').find('tbody tr th').css('background-color', '#DDD').css('color', '#000');
    $DadosResumo.find('table').find('thead tr th').css('background-color', '#DDD').css('color', '#000');

    $Dados.find('table').css('font-family', 'Calibri').css('font-size', '8pt').find('th').css('background-color', '#DDD').css('color', '#000');
    $Dados.find('table').find('tbody tr th').css('background-color', '#DDD').css('color', '#000');
    $Dados.find('table').find('thead tr th').css('background-color', '#DDD').css('color', '#000');
    
    $DadosResumo.find('table').find('tbody tr td').css('border-bottom', 'thin solid #DDD');
    $DadosResumo.find('table').find('tbody tr td:nth-child(1),tbody tr td:nth-child(3),tbody tr td:nth-child(4),tbody tr td:nth-child(5)').css('text-align', 'center');
    
    $Dados.find('table').find('tbody tr td').css('border-bottom', 'thin solid #DDD');
    $Dados.find('table').find('tbody tr td:nth-child(1),tbody tr td:nth-child(3),tbody tr td:nth-child(4),tbody tr td:nth-child(5)').css('text-align', 'center');
    
    var conteudo = $DadosResumo.html() + $Dados.html();
    tela_impressao = window.open('about:blank');

    tela_impressao.document.write(Cabecalho + conteudo);
    tela_impressao.window.print();
    tela_impressao.window.close();
}

function CriarCabecalho(Imagem, NomeEmpresa, EnderecoEmpresa, CNPJEmpresa, NomeRelatorio) {
    let HTML = '';

    HTML += '<div style="width: 100%; float: left; height: 85px !important;max-height: 85px !important; font-family: Calibri !important">';
    HTML += '  <table style="width: 100%; height: 85px !important;max-height: 85px !important;">';
    HTML += '    <tr>';
    HTML += '      <td style="width: 50px; vertical-align: middle">';
    HTML += '        <img src="' + Imagem + '" style="height: 50px" />';
    HTML += '      </td>';
    HTML += '      <td style="vertical-align: middle">';
    HTML += '         <h3 style="height: 20px; text-align: left; font-size: 10pt !important; padding: 0px !important; margin: 0px !important;">' + NomeEmpresa + '</h3>';
    HTML += '         <h5 style="height: 20px; text-align: left; font-size: 8pt !important; padding: 0px !important; margin: 0px !important;font-weight: 500 !important;">' + EnderecoEmpresa + '</h5>';
    HTML += '         <h5 style="height: 20px; text-align: left; font-size: 8pt !important; padding: 0px !important; margin: 0px !important;font-weight: 500 !important;">' + CNPJEmpresa + '</h5>';
    HTML += '      </td>';
    HTML += '    </tr>';
    HTML += '    <tr>';
    HTML += '      <td colspan="2" style="text-align: center">';
    HTML += '         <h3 style="height: 20px;  font-size: 9pt !important; padding: 0px !important; margin: 0px !important; text-transform: uppercase"><b>' + NomeRelatorio + '</b></h3>';
    HTML += '    </tr>';
    HTML += '  </table>';
    HTML += '</div>';

    return HTML;
}

function ObterParamURL(name, url) {
    if (!url)
        url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
    if (!results)
        return null;
    if (!results[2])
        return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}

function ReplaceAll(str, needle, replacement) {
    return str.split(needle).join(replacement);
}

function SomenteNumero(z) {
    v = z.value;
    v = v.replace(/\D/g, ""); // permite digitar apenas numero

    z.value = v;
}

$.extend({
    MsgBoxAzulOk: function (Titulo, Mensagem, callback) {
        try {
            $dlAzulOkgMessage.close();
        } catch (e) {
        }

        $dlAzulOkgMessage = $.alert({
            icon: 'fa fa-info-circle fa-lg',
            theme: 'modern',
            type: 'blue',
            title: Titulo,
            content: Mensagem,
            buttons: {
                ok: {
                    text: 'Ok',
                    btnClass: 'btn-blue',
                    action: function () {
                        if (typeof callback === 'function') {
                            callback.call();
                        }
                    }
                }
            }
        });
    },
    MsgBoxLaranjaOk: function (Titulo, Mensagem, callback) {
        try {
            $dlgLaranjaOkMessage.close();
        } catch (e) {
        }

        $dlgLaranjaOkMessage = $.alert({
            icon: 'fa fa-info-circle fa-lg',
            theme: 'modern',
            type: 'orange',
            title: Titulo,
            content: Mensagem,
            buttons: {
                ok: {
                    text: 'Ok',
                    btnClass: 'btn-orange',
                    action: function () {
                        if (typeof callback === 'function') {
                            callback.call();
                        }
                    }
                }
            }
        });
    },
    MsgBoxVermelhoOk: function (Titulo, Mensagem, callback) {
        try {
            $dlgVermelhoOkMessage.close();
        } catch (e) {
        }

        $dlgVermelhoOkMessage = $.alert({
            icon: 'fa fa-info-circle fa-lg',
            theme: 'modern',
            type: 'red',
            title: Titulo,
            content: Mensagem,
            buttons: {
                ok: {
                    text: 'Ok',
                    btnClass: 'btn-red',
                    action: function () {
                        if (typeof callback === 'function') {
                            callback.call();
                        }
                    }
                }
            }
        });
    },
    MsgBoxVerdeSimNao: function (Titulo, Mensagem, TextoSim, TextoNao, callBackSim, callBackNao) {
        try {
            $dlgVerdeSimNaoMessage.close();
        } catch (e) {
        }

        $dlgVerdeSimNaoMessage = $.alert({
            icon: 'fa fa-question-circle fa-lg',
            theme: 'modern',
            type: 'green',
            title: Titulo,
            content: Mensagem,
            buttons: {
                ok: {
                    text: TextoSim,
                    btnClass: 'btn-green',
                    action: function () {
                        if (typeof callBackSim === 'function')
                            callBackSim.call();
                    }
                },
                cancel: {
                    text: TextoNao,
                    btnClass: 'btn-red',
                    action: function () {
                        if (typeof callBackNao === 'function')
                            callBackNao.call();
                    }
                }
            }
        });
    },
    ModalDialogCallBack: function (ColumnClass, Icon, inTitle, HTML, Color, IconAction, TextAction, TextCancel, CallBack, CallBackContentReady, Language) {
        $JanelaForm = $.alert({
            icon: Icon,
            type: Color,
            title: inTitle,
            content: HTML,
            columnClass: ColumnClass,
            buttons: {
                cancel: {
                    text: TextCancel,
                    isHidden: !TextCancel ? true : false
                },
                ok: {
                    text: '<i class="' + IconAction + '"></i>&nbsp;&nbsp;' + TextAction,
                    btnClass: 'btn-' + Color,
                    action: function () {
                        if (typeof CallBack === 'function') {
                            CallBack.call();
                            return false;
                        }
                    }
                }
            },
            onContentReady: function () {
                CriarAtributos(Language);

                if (typeof CallBackContentReady === 'function')
                    CallBackContentReady.call();
            }
        });
    },
    ValidaForm: function ($objValid, ExibeMsg, TitleMsg, Msg) {
        let Validacao = true;

        $objValid.find('input[south-required="S"], textarea[south-required="S"]').each(function () {
            if ($(this).val().trim() === '') {
                $(this).addClass('form-control-required');
                Validacao = false;
            }
        });

        $objValid.find('select[south-required="S"]').each(function () {
            if ($(this).val().trim() === '-1') {
                $(this).addClass('form-control-required');
                Validacao = false;
            }
        });

        if (!Validacao && ExibeMsg)
            $.MsgBoxVermelhoOk(TitleMsg, Msg);

        return Validacao;
    }
})
        ;

Number.prototype.formatMoney = function (places, symbol, thousand, decimal) {
    places = !isNaN(places = Math.abs(places)) ? places : 2;
    symbol = symbol !== undefined ? symbol : "$";
    thousand = thousand || ",";
    decimal = decimal || ".";
    var number = this,
            negative = number < 0 ? "-" : "",
            i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
            j = (j = i.length) > 3 ? j % 3 : 0;
    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
};

String.prototype.extenso = function (c) {
    var ex = [
        ["zero", "um", "dois", "três", "quatro", "cinco", "seis", "sete", "oito", "nove", "dez", "onze", "doze", "treze", "quatorze", "quinze", "dezesseis", "dezessete", "dezoito", "dezenove"],
        ["dez", "vinte", "trinta", "quarenta", "cinqüenta", "sessenta", "setenta", "oitenta", "noventa"],
        ["cem", "cento", "duzentos", "trezentos", "quatrocentos", "quinhentos", "seiscentos", "setecentos", "oitocentos", "novecentos"],
        ["mil", "milhão", "bilhão", "trilhão", "quadrilhão", "quintilhão", "sextilhão", "setilhão", "octilhão", "nonilhão", "decilhão", "undecilhão", "dodecilhão", "tredecilhão", "quatrodecilhão", "quindecilhão", "sedecilhão", "septendecilhão", "octencilhão", "nonencilhão"]
    ];
    var a, n, v, i, n = this.replace(c ? /[^,\d]/g : /\D/g, "").split(","), e = " e ", $ = "real", d = "centavo", sl;
    for (var f = n.length - 1, l, j = -1, r = [], s = [], t = ""; ++j <= f; s = []) {
        j && (n[j] = (("." + n[j]) * 1).toFixed(2).slice(2));
        if (!(a = (v = n[j]).slice((l = v.length) % 3).match(/\d{3}/g), v = l % 3 ? [v.slice(0, l % 3)] : [], v = a ? v.concat(a) : v).length)
            continue;
        for (a = -1, l = v.length; ++a < l; t = "") {
            if (!(i = v[a] * 1))
                continue;
            i % 100 < 20 && (t += ex[0][i % 100]) ||
                    i % 100 + 1 && (t += ex[1][(i % 100 / 10 >> 0) - 1] + (i % 10 ? e + ex[0][i % 10] : ""));
            s.push((i < 100 ? t : !(i % 100) ? ex[2][i == 100 ? 0 : i / 100 >> 0] : (ex[2][i / 100 >> 0] + e + t)) +
                    ((t = l - a - 2) > -1 ? " " + (i > 1 && t > 0 ? ex[3][t].replace("ão", "ões") : ex[3][t]) : ""));
        }
        a = ((sl = s.length) > 1 ? (a = s.pop(), s.join(" ") + e + a) : s.join("") || ((!j && (n[j + 1] * 1 > 0) || r.length) ? "" : ex[0][0]));
        a && r.push(a + (c ? (" " + (v.join("") * 1 > 1 ? j ? d + "s" : (/0{6,}$/.test(n[0]) ? "de " : "") + $.replace("l", "is") : j ? d : $)) : ""));
    }
    return r.join(e);
}

String.prototype.lpad = function (padString, length) {
    var str = this;
    while (str.length < length)
        str = padString + str;
    return str;
}

/* COOKIE */
function CriarCookie(nome, valor) {
    var cookie = nome + "=" + escape(valor);

    document.cookie = cookie;
}

function LerCookie(nome) {
    var cookies = document.cookie;
    var prefix = nome + "=";
    var begin = cookies.indexOf("; " + prefix);

    if (begin == -1) {

        begin = cookies.indexOf(prefix);

        if (begin != 0)
            return null;

    } else
        begin += 2;

    var end = cookies.indexOf(";", begin);

    if (end == -1)
        end = cookies.length;

    return unescape(cookies.substring(begin + prefix.length, end));
}

function ExcluirCookie(nome) {
    if (LerCookie(nome)) {
        document.cookie = nome + "=" + "; expires=Thu, 01-Jan-70 00:00:01 GMT";
    }
}

function ConverterFloat(Valor) {
    Valor = Valor.replace(".", "");
    Valor = Valor.replace(",", ".");
    return parseFloat(Valor);
}

function FormatarCNPJ(Valor) {
    try {
        Valor = Valor.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1 $2 $3/$4-$5");
        Valor = ReplaceAll(Valor, ' ', '.');
    } catch (e) {
    }
    return Valor;
}


function FormatarTelefone(v) {
    if (v.length < 11) {
        v = v.replace(/D/g, "");                 //Remove tudo o que não é dígito
        v = v.replace(/^(d{2})(d)/g, "($1) $2"); //Coloca parênteses em volta dos dois primeiros dígitos
        v = v.replace(/(d)(d{4})$/, "$1-$2");    //Coloca hífen entre o quarto e o quinto dígitos
    } else {
        v = v.replace(/\D/g, '').replace(/(\d{2})(\d)(\d{4})(\d{4})$/, '($1) $2 $3-$4')
    }

    return v;
}
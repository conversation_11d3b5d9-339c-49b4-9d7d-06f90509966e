/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class PropCmlMod {

    private BigDecimal Codigo;
    private BigDecimal Tipo;
    private BigDecimal Modelo;
    private String Descricao;
    private String Detalhe;
    private String Operador;
    private LocalDate Dt_alter;
    private String Hr_Alter;
    private Integer imprimeLogo;
    private Integer imprimeTitulo;
    private Integer imprimeAss;

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(BigDecimal Codigo) {
        this.Codigo = Codigo;
    }

    public BigDecimal getTipo() {
        return Tipo;
    }

    public void setTipo(BigDecimal Tipo) {
        this.Tipo = Tipo;
    }

    public BigDecimal getModelo() {
        return Modelo;
    }

    public void setModelo(BigDecimal Modelo) {
        this.Modelo = Modelo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public String getDetalhe() {
        return Detalhe;
    }

    public void setDetalhe(String Detalhe) {
        this.Detalhe = Detalhe;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(LocalDate Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public Integer getImprimeLogo() {
        return imprimeLogo;
    }

    public void setImprimeLogo(Integer imprimeLogo) {
        this.imprimeLogo = imprimeLogo;
    }

    public Integer getImprimeTitulo() {
        return imprimeTitulo;
    }

    public void setImprimeTitulo(Integer imprimeTitulo) {
        this.imprimeTitulo = imprimeTitulo;
    }

    public Integer getImprimeAss() {
        return imprimeAss;
    }

    public void setImprimeAss(Integer imprimeAss) {
        this.imprimeAss = imprimeAss;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.Codigo);
        hash = 59 * hash + Objects.hashCode(this.Tipo);
        hash = 59 * hash + Objects.hashCode(this.Modelo);
        hash = 59 * hash + Objects.hashCode(this.Descricao);
        hash = 59 * hash + Objects.hashCode(this.Detalhe);
        hash = 59 * hash + Objects.hashCode(this.Operador);
        hash = 59 * hash + Objects.hashCode(this.Dt_alter);
        hash = 59 * hash + Objects.hashCode(this.Hr_Alter);
        hash = 59 * hash + Objects.hashCode(this.imprimeLogo);
        hash = 59 * hash + Objects.hashCode(this.imprimeTitulo);
        hash = 59 * hash + Objects.hashCode(this.imprimeAss);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PropCmlMod other = (PropCmlMod) obj;
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

}

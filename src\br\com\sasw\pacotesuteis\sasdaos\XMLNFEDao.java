/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.NFiscal;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class XMLNFEDao {

    public boolean existeNFCeXMLNFE(String numero, int serie, String CNPJ, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE (NOLOCK) \n"
                    + " WHERE \n"
                    + "     Numero = ? AND Serie = ? AND CNPJ = ? AND (Status = 0 OR Status = 1 OR Status = 2)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(numero);
            consulta.setInt(serie);
            consulta.setString(CNPJ);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFEDao.existeNFCeXMLNFE - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE (NOLOCK) \n"
                    + " WHERE \n"
                    + "     Numero = " + numero + " AND Serie = " + serie + " AND CNPJ = " + CNPJ + " AND (Status = 0 OR Status = 1 OR Status = 2)");
        }
    }

    public XMLNFE buscarXMLNFE(String numero, int serie, String CNPJ, Persistencia persistencia) throws Exception {
        try {
            XMLNFE xmlNFE = null;
            String sql = " SELECT \n"
                    + "     TOP 1 * \n"
                    + " FROM \n"
                    + "     XMLNFE (NOLOCK) \n"
                    + " WHERE \n"
                    + "     Numero = ? AND Serie = ? AND CNPJ = ? \n"
                    + " ORDER BY\n"
                    + "     Sequencia DESC\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(numero);
            consulta.setInt(serie);
            consulta.setString(CNPJ);
            consulta.select();
            if (consulta.Proximo()) {
                xmlNFE = new XMLNFE();
                xmlNFE.setSequencia(consulta.getString("Sequencia"));
                xmlNFE.setTOKEN(consulta.getString("TOKEN"));
                xmlNFE.setCNPJ(consulta.getString("CNPJ"));
                xmlNFE.setPraca(consulta.getString("Praca"));
                xmlNFE.setSerie(consulta.getString("Serie"));
                xmlNFE.setNumero(consulta.getString("Numero"));
                xmlNFE.setUF(consulta.getString("UF"));
                xmlNFE.setDt_Nota(consulta.getString("Dt_Nota"));
                xmlNFE.setHr_Nota(consulta.getString("Hr_Nota"));
                xmlNFE.setXML_Envio(consulta.getString("XML_Envio"));
                xmlNFE.setXML_Retorno(consulta.getString("XML_Retorno"));
                xmlNFE.setChaveNFE(consulta.getString("ChaveNFE"));
                xmlNFE.setProtocolo(consulta.getString("Protocolo"));
                xmlNFE.setStatus(consulta.getString("Status"));
                xmlNFE.setDt_Envio(consulta.getString("Dt_Envio"));
                xmlNFE.setHr_Envio(consulta.getString("Hr_Envio"));
                xmlNFE.setDt_Retorno(consulta.getString("Dt_Retorno"));
                xmlNFE.setHr_Retorno(consulta.getString("Hr_Retorno"));
            }
            consulta.close();
            return xmlNFE;
        } catch (Exception e) {
            throw new Exception("XMLNFEDao.existeNFCeXMLNFE - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     TOP 1 * \n"
                    + " FROM \n"
                    + "     XMLNFE\n"
                    + " WHERE \n"
                    + "     Numero = " + numero + " AND Serie = " + serie + " AND CNPJ = " + CNPJ + " \n"
                    + " ORDER BY\n"
                    + "     Sequencia DESC\n");
        }
    }

    /**
     * Retorna a chave da nota inserida
     *
     * @param xmlNFE
     * @param persistencia
     * @return
     * @throws Exception
     */
    public void inserirXMLNFE(XMLNFE xmlNFE, Persistencia persistencia) throws Exception {
        try {
            String sql = " DECLARE @sequencia int;\n"
                    + "DECLARE @cnpj varchar(14);\n"
                    + "SET @cnpj = ?;\n"
                    + "SELECT @sequencia = ISNULL(MAX(ISNULL(sequencia,0)),0) + 1 FROM XMLNFE; \n"
                    + "INSERT INTO XMLNFE \n"
                    + " (Sequencia, TOKEN, CNPJ, Praca, Serie, Numero, UF, Dt_Nota, Hr_Nota, XML_Envio, XML_Retorno, \n"
                    + "ChaveNFE, Protocolo, Status, Dt_Envio, Hr_Envio, Dt_Retorno, Hr_Retorno) \n"
                    + " VALUES (@sequencia, @cnpj + CONVERT(VarChar, @sequencia), @cnpj, ?, ?, ?, ?, ?, ?, ?, ?, "
                    + " ?, ?, ?, ?, ?, ?, ?); \n";
//                    + " SELECT @cnpj + CONVERT(VarChar, @sequencia) TOKEN; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(xmlNFE.getCNPJ());
//            consulta.setString(xmlNFE.getTOKEN());
//            consulta.setString(xmlNFE.getCNPJ());
            consulta.setString(xmlNFE.getPraca());
            consulta.setString(xmlNFE.getSerie());
            consulta.setString(xmlNFE.getNumero());
            consulta.setString(xmlNFE.getUF());
            consulta.setString(xmlNFE.getDt_Nota());
            consulta.setString(xmlNFE.getHr_Nota());
            consulta.setString(xmlNFE.getXML_Envio());
            consulta.setString(xmlNFE.getXML_Retorno());
            consulta.setString(xmlNFE.getChaveNFE());
            consulta.setString(xmlNFE.getProtocolo());
            consulta.setString(xmlNFE.getStatus());
            consulta.setString(xmlNFE.getDt_Envio());
            consulta.setString(xmlNFE.getHr_Envio());
            consulta.setString(xmlNFE.getDt_Retorno());
            consulta.setString(xmlNFE.getHr_Retorno());
            consulta.insert();
//            String sequencia = null;
//            if (consulta.Proximo()) {
//                sequencia = consulta.getString("TOKEN");
//            }
            consulta.close();
//            return sequencia;
        } catch (Exception e) {
            throw new Exception("XMLNFEDao.inserirXMLNFE - " + e.getMessage() + "\r\n"
                    + " DECLARE @sequencia int\n"
                    + "DECLARE @cnpj varchar(14)\n"
                    + "SET @cnpj = ?\n"
                    + "SELECT @sequencia = ISNULL(MAX(ISNULL(sequencia,0)),0) + 1 FROM XMLNFE \n"
                    + "INSERT INTO XMLNFE \n"
                    + " (Sequencia, TOKEN, CNPJ, Praca, Serie, Numero, UF, Dt_Nota, Hr_Nota, XML_Envio, XML_Retorno, \n"
                    + "ChaveNFE, Protocolo, Status, Dt_Envio, Hr_Envio, Dt_Retorno, Hr_Retorno) \n"
                    + " VALUES ( @sequencia, @cnpj + CONVERT(VarChar, @sequencia), @cnpj, "
                    + xmlNFE.getPraca() + ", " + xmlNFE.getSerie() + ", " + xmlNFE.getNumero() + ", " + xmlNFE.getUF() + ", " + xmlNFE.getDt_Nota() + ", "
                    + xmlNFE.getHr_Nota() + ", " + xmlNFE.getXML_Envio() + ", " + xmlNFE.getXML_Retorno() + ", " + xmlNFE.getChaveNFE() + ", "
                    + xmlNFE.getProtocolo() + ", " + xmlNFE.getStatus() + ", " + xmlNFE.getDt_Envio() + ", " + xmlNFE.getHr_Envio() + ", "
                    + xmlNFE.getDt_Retorno() + ", " + xmlNFE.getHr_Retorno() + ") \n");
//                    + " SELECT @cnpj + CONVERT(VarChar, @sequencia) TOKEN ");
        }
    }

    /**
     * Busca notas pelo token (cnpj + sequencia)
     *
     * @param chave
     * @param persistencia
     * @return
     * @throws Exception
     */
    public XMLNFE buscarXMLNFE(String token, Persistencia persistencia) throws Exception {
        try {
            XMLNFE xmlNFE = null;
            String sql = " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE \n"
                    + " WHERE \n"
                    + "     TOKEN = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(token);
            consulta.select();
            if (consulta.Proximo()) {
                xmlNFE = new XMLNFE();
                xmlNFE.setSequencia(consulta.getString("Sequencia"));
                xmlNFE.setTOKEN(consulta.getString("TOKEN"));
                xmlNFE.setCNPJ(consulta.getString("CNPJ"));
                xmlNFE.setPraca(consulta.getString("Praca"));
                xmlNFE.setSerie(consulta.getString("Serie"));
                xmlNFE.setNumero(consulta.getString("Numero"));
                xmlNFE.setUF(consulta.getString("UF"));
                xmlNFE.setDt_Nota(consulta.getString("Dt_Nota"));
                xmlNFE.setHr_Nota(consulta.getString("Hr_Nota"));
                xmlNFE.setXML_Envio(consulta.getString("XML_Envio"));
                xmlNFE.setXML_Retorno(consulta.getString("XML_Retorno"));
                xmlNFE.setChaveNFE(consulta.getString("ChaveNFE"));
                xmlNFE.setProtocolo(consulta.getString("Protocolo"));
                xmlNFE.setStatus(consulta.getString("Status"));
                xmlNFE.setDt_Envio(consulta.getString("Dt_Envio"));
                xmlNFE.setHr_Envio(consulta.getString("Hr_Envio"));
                xmlNFE.setDt_Retorno(consulta.getString("Dt_Retorno"));
                xmlNFE.setHr_Retorno(consulta.getString("Hr_Retorno"));
            }
            consulta.close();
            return xmlNFE;
        } catch (Exception e) {
            throw new Exception("XMLNFEDao.buscarXMLNFE - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE \n"
                    + " WHERE \n"
                    + "     TOKEN = " + token);
        }
    }

    /**
     * Busca notas pelo token (cnpj + sequencia)
     *
     * @param chaveNfe
     * @param dtRetorno
     * @param persistencia
     * @return
     * @throws Exception
     */
    public XMLNFE buscarXMLNFE(String chaveNfe, String dtRetorno, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            XMLNFE xmlNFE = null;
            sql = " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE \n"
                    + " WHERE \n"
                    + "     ChaveNFE = ? \n"
                    + "    AND Dt_Retorno = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(chaveNfe);
            consulta.setString(dtRetorno);
            consulta.select();
            if (consulta.Proximo()) {
                xmlNFE = new XMLNFE();
                xmlNFE.setSequencia(consulta.getString("Sequencia"));
                xmlNFE.setTOKEN(consulta.getString("TOKEN"));
                xmlNFE.setCNPJ(consulta.getString("CNPJ"));
                xmlNFE.setPraca(consulta.getString("Praca"));
                xmlNFE.setSerie(consulta.getString("Serie"));
                xmlNFE.setNumero(consulta.getString("Numero"));
                xmlNFE.setUF(consulta.getString("UF"));
                xmlNFE.setDt_Nota(consulta.getString("Dt_Nota"));
                xmlNFE.setHr_Nota(consulta.getString("Hr_Nota"));
                xmlNFE.setXML_Envio(consulta.getString("XML_Envio"));
                xmlNFE.setXML_Retorno(consulta.getString("XML_Retorno"));
                xmlNFE.setChaveNFE(consulta.getString("ChaveNFE"));
                xmlNFE.setProtocolo(consulta.getString("Protocolo"));
                xmlNFE.setStatus(consulta.getString("Status"));
                xmlNFE.setDt_Envio(consulta.getString("Dt_Envio"));
                xmlNFE.setHr_Envio(consulta.getString("Hr_Envio"));
                xmlNFE.setDt_Retorno(consulta.getString("Dt_Retorno"));
                xmlNFE.setHr_Retorno(consulta.getString("Hr_Retorno"));
            }
            consulta.close();
            return xmlNFE;
        } catch (Exception e) {
            throw new Exception(sql);
        }
    }

    /**
     * Busca notas pendentes para dashboard
     *
     * @param chave
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<NFiscal> buscarDahsboard(Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<NFiscal> Retorno = new ArrayList<>();
            NFiscal nFiscal;
            Consulta consulta;

            try {
                sql = "UPDATE xmlnfse SET dt_retorno = NULL, hr_retorno = NULL WHERE XML_Retorno = '<ConsultarNfseRpsResposta xmlns=\"http://www.abrasf.org.br/ABRASF/arquivos/nfse.xsd\"><ListaMensagemRetorno xmlns=\"http://www.abrasf.org.br/ABRASF/arquivos/nfse.xsd\"><MensagemRetorno><Codigo>E89</Codigo><Mensagem>Não existe na base de dados uma NFS-e emitida para o número de RPS informado</Mensagem><Correcao>Informe o número correto do RPS.</Correcao></MensagemRetorno></ListaMensagemRetorno></ConsultarNfseRpsResposta>'";

                consulta = new Consulta(sql, persistencia);
                consulta.update();
            } catch (Exception ex) {
            }
            
            try {
                sql = "DELETE FROM xmlnfse WHERE (Dt_Retorno is null or dt_envio is null) and dt_nota < '2021-08-10'";

                consulta = new Consulta(sql, persistencia);
                consulta.delete();
            } catch (Exception ex) {
            }

            try {
                sql = "UPDATE xmlnfse SET dt_retorno = NULL, hr_retorno = NULL, dt_envio = NULL, hr_envio = NULL where xml_retorno like '%handshake%'";

                consulta = new Consulta(sql, persistencia);
                consulta.update();
            } catch (Exception ex) {
            }

            sql = "SELECT \n"
                    + "XMLNFSE.Numero, \n"
                    + "XMLNFSE.Serie, \n"
                    + "XMLNFSE.Praca, \n"
                    + "Pracas.NRed PracaNRed, \n"
                    + "CONVERT(varchar,XMLNFSE.Dt_Nota,103) Dt_Nota, \n"
                    + "XMLNFSE.Hr_Nota, \n"
                    + "Clientes.NRed Cliente, \n"
                    + "NFiscal.Valor, \n"
                    + "NFiscal.ISS, \n"
                    + "NFiscal.ICMS, \n"
                    + "CONVERT(varchar,XMLNFSE.Dt_Envio,103) Dt_Envio, \n"
                    + "XMLNFSE.Hr_Envio, \n"
                    + "CONVERT(varchar,XMLNFSE.Dt_Retorno,103) Dt_Retorno, \n"
                    + "XMLNFSE.Hr_Retorno \n"
                    + "from xmlnfse\n"
                    + "left join Pracas   on  Pracas.Codigo = XMLNFSE.Praca\n"
                    + "left join NFiscal  on  NFiscal.Numero = XMLNFSE.Numero\n"
                    + "                   and NFiscal.Praca  = XMLNFSE.Praca\n"
                    + "left join Clientes on  Clientes.Codigo = NFiscal.CliFat\n"
                    + "                   and Clientes.CodFil = NFiscal.CodFil\n"
                    + "where (Dt_Envio is null or Dt_Retorno is null)\n"
                    + "  and NFiscal.Numero > 0\n"
                    + "order by XMLNFSE.Praca, XMLNFSE.Dt_Nota, XMLNFSE.Hr_Nota";

            consulta = new Consulta(sql, persistencia);

            consulta.select();
            while (consulta.Proximo()) {
                nFiscal = new NFiscal();
                nFiscal.setNumero(consulta.getString("Numero"));
                nFiscal.setSerie(consulta.getString("Serie"));
                nFiscal.setPraca(consulta.getString("Praca"));
                nFiscal.setNRedPraca(consulta.getString("PracaNRed"));
                nFiscal.setDt_Nota(consulta.getString("Dt_Nota"));
                nFiscal.setHr_Nota(consulta.getString("Hr_Nota"));
                nFiscal.setNRed(consulta.getString("Cliente"));
                nFiscal.setValor(consulta.getString("Valor"));
                nFiscal.setISS(consulta.getString("ISS"));
                nFiscal.setICMS(consulta.getString("ICMS"));
                nFiscal.setDt_Envio(consulta.getString("Dt_Envio"));
                nFiscal.setHr_Envio(consulta.getString("Hr_Envio"));
                nFiscal.setDt_Retorno(consulta.getString("Dt_Retorno"));
                nFiscal.setHr_Retorno(consulta.getString("Hr_Retorno"));

                Retorno.add(nFiscal);
            }
            consulta.close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("XMLNFEDao.buscarDahsboard - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public XMLNFE buscarChaveXMLNFE(String chave, Persistencia persistencia) throws Exception {
        try {
            XMLNFE xmlNFE = null;
            String sql = " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE \n"
                    + " WHERE \n"
                    + "     ChaveNFE = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(chave);
            consulta.select();
            if (consulta.Proximo()) {
                xmlNFE = new XMLNFE();
                xmlNFE.setSequencia(consulta.getString("Sequencia"));
                xmlNFE.setTOKEN(consulta.getString("TOKEN"));
                xmlNFE.setCNPJ(consulta.getString("CNPJ"));
                xmlNFE.setPraca(consulta.getString("Praca"));
                xmlNFE.setSerie(consulta.getString("Serie"));
                xmlNFE.setNumero(consulta.getString("Numero"));
                xmlNFE.setUF(consulta.getString("UF"));
                xmlNFE.setDt_Nota(consulta.getString("Dt_Nota"));
                xmlNFE.setHr_Nota(consulta.getString("Hr_Nota"));
                xmlNFE.setXML_Envio(consulta.getString("XML_Envio"));
                xmlNFE.setXML_Retorno(consulta.getString("XML_Retorno"));
                xmlNFE.setChaveNFE(consulta.getString("ChaveNFE"));
                xmlNFE.setProtocolo(consulta.getString("Protocolo"));
                xmlNFE.setStatus(consulta.getString("Status"));
                xmlNFE.setDt_Envio(consulta.getString("Dt_Envio"));
                xmlNFE.setHr_Envio(consulta.getString("Hr_Envio"));
                xmlNFE.setDt_Retorno(consulta.getString("Dt_Retorno"));
                xmlNFE.setHr_Retorno(consulta.getString("Hr_Retorno"));
            }
            consulta.close();
            return xmlNFE;
        } catch (Exception e) {
            throw new Exception("XMLNFEDao.buscarXMLNFE - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     XMLNFE \n"
                    + " WHERE \n"
                    + "     TOKEN = " + chave);
        }
    }
}

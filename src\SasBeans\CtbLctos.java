package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class CtbLctos {

    private BigDecimal Sequencia;
    private BigDecimal CodFil;
    private LocalDate data;
    private String contaDeb;
    private String contaCred;
    private BigDecimal valor;
    private BigDecimal codHist;
    private String historico;
    private String detalhes;
    private String ccusto;
    private BigDecimal lote;
    private String operador;
    private LocalDate dt_alter;
    private String hr_alter;
    private String refLan;

    public CtbLctos() {
        this.Sequencia = new BigDecimal("0");
        this.CodFil = new BigDecimal("0");
        this.data = null;
        this.contaDeb = "";
        this.contaCred = "";
        this.valor = new BigDecimal("0");
        this.codHist = new BigDecimal("0");
        this.historico = "";
        this.detalhes = "";
        this.ccusto = "";
        this.lote = new BigDecimal("0");
        this.operador = "";
        this.dt_alter = null;
        this.hr_alter = "";
        this.refLan = "";

    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public LocalDate getData() {
        return data;
    }

    public void setData(LocalDate data) {
        this.data = data;
    }

    public String getContaDeb() {
        return contaDeb;
    }

    public void setContaDeb(String contaDeb) {
        this.contaDeb = contaDeb;
    }

    public String getContaCred() {
        return contaCred;
    }

    public void setContaCred(String contaCred) {
        this.contaCred = contaCred;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(String valor) {
        try {
            this.valor = new BigDecimal(valor);
        } catch (Exception e) {
            this.valor = new BigDecimal("0");
        }
    }

    public BigDecimal getCodHist() {
        return codHist;
    }

    public void setCodHist(String codHist) {
        try {
            this.codHist = new BigDecimal(codHist);
        } catch (Exception e) {
            this.codHist = new BigDecimal("0");
        }
    }

    public String getHistorico() {
        return historico;
    }

    public void setHistorico(String historico) {
        this.historico = historico;
    }

    public String getDetalhes() {
        return detalhes;
    }

    public void setDetalhes(String detalhes) {
        this.detalhes = detalhes;
    }

    public String getCcusto() {
        return ccusto;
    }

    public void setCcusto(String ccusto) {
        this.ccusto = ccusto;
    }

    public BigDecimal getLote() {
        return lote;
    }

    public void setLote(String lote) {
        try {
            this.lote = new BigDecimal(lote);
        } catch (Exception e) {
            this.lote = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public LocalDate getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(LocalDate dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

    public String getRefLan() {
        return refLan;
    }

    public void setRefLan(String refLan) {
        this.refLan = refLan;
    }

}

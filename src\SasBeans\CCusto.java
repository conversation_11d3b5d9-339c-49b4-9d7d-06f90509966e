/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CCusto {

    private String CCusto;
    private String Praca;
    private String NRed;
    private String Descricao;
    private String CNPJ;
    private String CPF;
    private String ContaCTB;
    private String Tipo;
    private String Situacao;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    /**
     * @return the CCusto
     */
    public String getCCusto() {
        return CCusto;
    }

    /**
     * @param CCusto the CCusto to set
     */
    public void setCCusto(String CCusto) {
        this.CCusto = CCusto;
    }

    /**
     * @return the Praca
     */
    public String getPraca() {
        return Praca;
    }

    /**
     * @param Praca the Praca to set
     */
    public void setPraca(String Praca) {
        this.Praca = Praca;
    }

    /**
     * @return the NRed
     */
    public String getNRed() {
        return NRed;
    }

    /**
     * @param NRed the NRed to set
     */
    public void setNRed(String NRed) {
        this.NRed = NRed;
    }

    /**
     * @return the Descricao
     */
    public String getDescricao() {
        return Descricao;
    }

    /**
     * @param Descricao the Descricao to set
     */
    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    /**
     * @return the CNPJ
     */
    public String getCNPJ() {
        return CNPJ;
    }

    /**
     * @param CNPJ the CNPJ to set
     */
    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    /**
     * @return the CPF
     */
    public String getCPF() {
        return CPF;
    }

    /**
     * @param CPF the CPF to set
     */
    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    /**
     * @return the ContaCTB
     */
    public String getContaCTB() {
        return ContaCTB;
    }

    /**
     * @param ContaCTB the ContaCTB to set
     */
    public void setContaCTB(String ContaCTB) {
        this.ContaCTB = ContaCTB;
    }

    /**
     * @return the Tipo
     */
    public String getTipo() {
        return Tipo;
    }

    /**
     * @param Tipo the Tipo to set
     */
    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    /**
     * @return the Situacao
     */
    public String getSituacao() {
        return Situacao;
    }

    /**
     * @param Situacao the Situacao to set
     */
    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    /**
     * @return the Operador
     */
    public String getOperador() {
        return Operador;
    }

    /**
     * @param Operador the Operador to set
     */
    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    /**
     * @return the Dt_Alter
     */
    public String getDt_Alter() {
        return Dt_Alter;
    }

    /**
     * @param Dt_Alter the Dt_Alter to set
     */
    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    /**
     * @return the Hr_Alter
     */
    public String getHr_Alter() {
        return Hr_Alter;
    }

    /**
     * @param Hr_Alter the Hr_Alter to set
     */
    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 59 * hash + Objects.hashCode(this.CCusto);
        hash = 59 * hash + Objects.hashCode(this.Praca);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CCusto other = (CCusto) obj;
        if (!Objects.equals(this.CCusto, other.CCusto)) {
            return false;
        }
        return true;
    }

}

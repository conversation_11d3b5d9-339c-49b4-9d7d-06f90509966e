/*
*/
/* 
    Created on : Jan 2, 2017, 2:44:49 PM
    Author     : Richard
*/

.selecionado {
    background: linear-gradient(to bottom, #597d98, #4b708d) !important;
}

.selecionado div.ui-panel-titlebar, .selecionado ui-panel-titlebar{
    border: none !important;
}

.selecionado .ui-panel-title{
    color: white !important;
}

.noscroll .ui-dialog-content{
    overflow: hidden !important;
}

.ui-dialog-title{
    width: 80% !important;
}

.right{
    float: right;
}

.panelTabela{
    width: 100%;
}

.relatorio .ui-widget-content{
    background: white;
}

.relatorio td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.relatorio .ui-datatable-selectable{
    border: 1px solid #dddddd !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.relatorio .ui-datatable-scrollable-header-box{
    background: white;
}
.relatorio .ui-datatable-data .ui-widget-content{
    border: 1px solid #dddddd !important;
}
.relatorio .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.relatorio .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}
.relatorio .ui-datatable-scrollable-body {
    background: white !important;
}

.relatorio .ui-datatable-scrollable-body{
    height: calc(100vh - 66px - 30px - 28px - 66px - 20px);
    background:transparent;
}

.relatorio .ui-datatable-scrollable-body.toggled{
    height: calc(100vh - 66px - 30px - 28px - 20px);
}
.relatorio{
    width: 90vh;
    min-width: 100%;
}    

.tabs{
    width: 90vh;
    min-width: 100%;
}

.tabs .ui-panelgrid-cell{
    padding-right: 0px !important;
}

@media all and (min-width: 768px) {
    .tabs{
        width: 700px;
    }
    .relatorio{
        width: 700px;
    }
    .tabs .ui-panelgrid-cell{
        padding-right: 15px;
    }
    .panelTabela{
        width: auto;
    }
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.ui-tabs .ui-tabs-nav.ui-widget-header li{
    background: #ccccff;
}
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled{
    opacity: 0.60;
}
.pessoa .ui-autocomplete-panel {
    width: 100% !important;
}
.pessoa .ui-autocomplete-input{
    width: 100% !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.cliente2 .ui-autocomplete-panel {
    width: 275px !important;
}
.cliente2 .ui-autocomplete-input{
    width: 275px !important;
}

.cliente3 .ui-autocomplete-panel {
    width: 280px !important;
}
.cliente3 .ui-autocomplete-input{
    width: 280px !important;
}
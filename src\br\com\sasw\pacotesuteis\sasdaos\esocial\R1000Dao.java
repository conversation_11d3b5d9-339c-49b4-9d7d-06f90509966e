/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.R1000;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class R1000Dao {

    public List<R1000> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<R1000> retorno = new ArrayList<>();
            String sql = " Select Filiais.TipoPessoa ideContri_tpInsc, Filiais.CNPJ ideContri_nrInsc, Filiais.RazaoSocial infoCadastro_nmRazao, "
                    + " Filiais.PorteEmp infoCadastro_classTrib, Rais.NatJur infoCadastro_natJurid, Pessoa.Nome contato_nmCtt, "
                    + " Dirf.CPFRInf contato_cpfCtt, DIRF.DDDRInf+DIRF.FoneRInf contato_foneFixo, DIRF.emailRInf contato_email, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1000' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%aguardando%' "
                    + "                     or z.Xml_Retorno = ''"
                    + "                     or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1000' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and (z.Xml_Retorno like '%<cdRetorno>1%')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "         where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1000' "
                    + "             and z.Compet = ? "
                    + "             and z.Ambiente = ? "
                    + "             and z.Xml_Retorno like '%<cdRetorno>0%')) a) sucesso "
                    + " From Filiais "
                    + " Left join Rais  on Rais.CodFil = Filiais.CodFil "
                    + " Left Join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa  on Pessoa.Codigo = Dirf.CodPessoaRIn "
                    + " Where Filiais.CodFil = ? "
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.select();
            R1000 r1000;
            while (consulta.Proximo()) {
                r1000 = new R1000();
                r1000.setSucesso(consulta.getInt("sucesso"));
                r1000.setIdeEvento_procEmi("1");
                r1000.setIdeEvento_verProc("Satellite Reinf");
                r1000.setIdeEmpregador_tpInsc(consulta.getString("ideContri_tpInsc"));
                r1000.setIdeEmpregador_nrInsc(consulta.getString("ideContri_nrInsc"));
                r1000.setInfoCadastro_nmRazao(consulta.getString("infoCadastro_nmRazao"));
                //r1000.setInfoCadastro_classTrib(consulta.getString("infoCadastro_classTrib"));
                r1000.setInfoCadastro_classTrib("99");
                r1000.setInfoCadastro_indEscrituracao("0");
                r1000.setInfoCadastro_indDesoneracao("0");
                r1000.setInfoCadastro_ndAcordoIsenMulta("0");
                r1000.setInfoCadastro_indSitPJ("0");
                r1000.setContato_nmCtt(consulta.getString("contato_nmCtt"));
                r1000.setContato_cpfCtt(consulta.getString("contato_cpfCtt"));
                r1000.setContato_foneFixo(consulta.getString("contato_foneFixo"));
                r1000.setContato_foneCel("6134030124");
                r1000.setContato_email(consulta.getString("contato_email"));
                r1000.setSoftwareHouse_cnpjSoftHouse("04057947000190");
                r1000.setSoftwareHouse_nmRazao("South American Technology Ltda");
                r1000.setSoftwareHouse_nmCont("Carlos Santos");
                r1000.setSoftwareHouse_telefone("6133440038");
                r1000.setSoftwareHouse_email("<EMAIL>");

                r1000.setIdePeriodo_iniValid(compet);
                r1000.setIdeEvento_tpAmb(ambiente);
                r1000.setId((r1000.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2") + r1000.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001");
                r1000.setIdeEmpregador_tpInsc(r1000.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                r1000.setIdeEmpregador_nrInsc(r1000.getIdeEmpregador_nrInsc().substring(0, 8));
                r1000.setInfoCadastro_classTrib(FuncoesString.PreencheEsquerda(r1000.getInfoCadastro_classTrib(), 2, "0"));

                retorno.add(r1000);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("R1000Dao.get - " + e.getMessage() + "\r\n"
                    + "  Select Filiais.TipoPessoa ideContri_tpInsc, Filiais.CNPJ ideContri_nrInsc, Filiais.RazaoSocial infoCadastro_nmRazao, "
                    + " Filiais.PorteEmp infoCadastro_classTrib, Rais.NatJur infoCadastro_natJurid, Pessoa.Nome contato_nmCtt, "
                    + " Dirf.CPFRInf contato_cpfCtt, DIRF.DDDRInf+DIRF.FoneRInf contato_foneFixo, DIRF.emailRInf contato_email, "
                    + " (select max(sucesso) from  ( "
                    + "     (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "            From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1000' "
                    + "             and z.Compet = " + compet
                    + "             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1000' "
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + " union "
                    + "     (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "         From XmleSocial z  "
                    + "             where z.Identificador = substring(Filiais.CNPJ,1,8) "
                    + "             and z.evento = 'R-1000' "
                    + "             and z.Compet = " + compet
                    + "             and z.Xml_Retorno like '%<cdRetorno>0%')) a) sucesso "
                    + " From Filiais "
                    + " Left join Rais  on Rais.CodFil = Filiais.CodFil "
                    + " Left Join Dirf  on Dirf.CodFil = Filiais.CodFil "
                    + " Left join Pessoa  on Pessoa.Codigo = Dirf.CodPessoaRIn "
                    + " Where Filiais.CodFil = " + codFil
                    + " ORDER BY sucesso asc, substring(Filiais.CNPJ,1,8) asc ");
        }
    }
}

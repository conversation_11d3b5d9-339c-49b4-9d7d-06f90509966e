/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.faturamento;

import Arquivo.ArquivoLog;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import SasBeans.CPagar;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasDaos.CPagarDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

/**
 *
 * <AUTHOR>
 */
@Named(value = "cpagar")
@ViewScoped
public class CPagarMB implements Serializable {
    
    private Persistencia persistencia, satellite;
    private String codfil, banco, operador, nome, filialDesc, caminho, data1, data2, dataTela, log;
    private BigDecimal codPessoa;
    private ArquivoLog logerro;
    private Filiais filiais;
    private final RotasSatWeb rotassatweb;
    private CPagar cPagarSelecionado;
    private List<CPagar> allCPagar;
    private CPagarDao cPagarDao;
    private List<Date> datasSelecionadas;
    private Date dataInicio, dataFim;

    public CPagarMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        filialDesc = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        dataTela = DataAtual.getDataAtual("SQL");
        rotassatweb = new RotasSatWeb();
        cPagarSelecionado = new CPagar();
        allCPagar = new ArrayList<>();
        cPagarDao = new CPagarDao();
        Calendar c = Calendar.getInstance();
        c.setTime(Date.from(Instant.now()));
        dataFim = c.getTime();

        c = Calendar.getInstance();
        c.setTime(dataFim);
        c.add(Calendar.DATE, -30);

        dataInicio = c.getTime();
        data1 = dataInicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        data2 = dataFim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(dataInicio); // data inicial
        datasSelecionadas.add(dataFim); // data final
    }

    public void Persistencias(Persistencia pstLocal) {
        try {
            this.persistencia = pstLocal;
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codfil, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public void carregarGride() {
        try {
            this.allCPagar = cPagarDao.listaContasPagar(this.dataTela, this.data1, this.data2, this.codfil, this.persistencia);

            Double Total = 0.0;
            String UltimaData = "";

            for (int I = 0; I< this.allCPagar.size(); I++) {
                if (!UltimaData.equals(this.allCPagar.get(I).getDtVenc())) {
                    Total = 0.0;
                }

                UltimaData = this.allCPagar.get(I).getDtVenc();
                Total += Double.parseDouble(this.allCPagar.get(I).getValor().toPlainString());
                this.allCPagar.get(I).setTotalValor(Total);
            }

            PrimeFaces.current().executeScript("setAtrasos();");
        } catch (Exception ex) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataInicio = this.datasSelecionadas.get(0);
                this.dataFim = this.datasSelecionadas.get(1);

                carregarGride();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public Persistencia getSatellite() {
        return satellite;
    }

    public void setSatellite(Persistencia satellite) {
        this.satellite = satellite;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getFilialDesc() {
        return filialDesc;
    }

    public void setFilialDesc(String filialDesc) {
        this.filialDesc = filialDesc;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public String getDataTela() {
        return dataTela;
    }

    public void setDataTela(String dataTela) {
        this.dataTela = dataTela;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public List<CPagar> getAllCPagar() {
        return allCPagar;
    }

    public void setAllCPagar(List<CPagar> allCPagar) {
        this.allCPagar = allCPagar;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }
    
}

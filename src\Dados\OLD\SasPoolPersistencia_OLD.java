/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Dados.OLD;

import Dados.Pool.PoolClientesConexoes;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SasPoolPersistencia_OLD {

    private List<DadosBancos_OLD> listaBancos = new ArrayList();
    private List<PoolClientesConexoes> listaConexoes = null;
    private int tamanhoPool = 1;
    private String Caminho;
    private LeArqConect_OLD learqconect = new LeArqConect_OLD();
    private boolean jtds = false;
    private boolean mysql = false;

    /**
     * Define o uso do driver MySql
     *
     * @param mysql
     */
    public void setMysql(boolean mysql) {
        this.mysql = mysql;
    }

    /**
     * Define uso do driver jtds
     *
     * @param jtds
     */
    public void setJtds(boolean jtds) {
        this.jtds = jtds;
    }

    /**
     * /informa ao pool o caminho da mapconect ou arquivo de conexão
     *
     * @param caminho - caminho do arquivo de conexão
     */
    public void setCaminho(String caminho) {
        this.Caminho = caminho;
    }

    /**
     * Devolve o tamanho do pool atual
     *
     * @return
     */
    public int getTamanhoPool() {
        return tamanhoPool;
    }

    /**
     * Determina o tamanho do pool a ser criado
     *
     * @param tamanhopool
     */
    public void setTamanhoPool(int tamanhopool) {
        this.tamanhoPool = tamanhopool;
    }

    /**
     * Devolve uma conexão para a aplicação, diretamente do pool do cliente
     * escolhido
     *
     * @param Empresa - Parâmetro da Empresa
     * @return - Uma Persistencia, uma conexão pronta
     */
    public Persistencia_OLD getConexao(String Empresa) {
        while (true) {
            if (listaConexoes != null) {
                for (PoolClientesConexoes pool1 : listaConexoes) {
                    if (pool1.getEmpresa().equals(Empresa)) {
                        return pool1.getConexao();
                    }
                }
            }
            if (!this.IniciaPoolCliente(Caminho, Empresa)) {
                return null;
            }
//            this.IniciaPoolCliente(Caminho, Empresa);
        }
    }

    /**
     * Devolve todas as persistências existentes.
     *
     * @return - Uma Persistencia, uma conexão pronta
     */

    public List<Persistencia_OLD> getConexoes() {
        List<Persistencia_OLD> persistencias = new ArrayList<>();
        try {
            if (listaConexoes == null) {
                listaBancos = learqconect.MapeiaPoolWeb(Caminho);
            }
            listaConexoes.forEach((pool1) -> {
                persistencias.add(pool1.getConexao());
            });
        } catch (Exception e) {

        }
        return persistencias;
    }

    /**
     * Inicia um Pool para um cliente específico
     *
     * @param caminho - caminho da mapconect
     * @param Empresa - parâmetro da empresa
     * @return
     */
    public boolean IniciaPoolCliente(String caminho, String Empresa) {
        try {
            if (listaConexoes == null) {
                listaConexoes = new ArrayList();
            }
            listaBancos = learqconect.MapeiaPoolWeb(Caminho);
            for (DadosBancos_OLD listaBancos1 : listaBancos) {
                if (listaBancos1.getEmpresa().equals(Empresa)) {
                    PoolClientesConexoes pool = new PoolClientesConexoes();
                    pool.setJtds(jtds);
                    pool.setMysql(mysql);
                    pool.setEmpresa(listaBancos1.getEmpresa());
                    pool.MontaPool(listaBancos1.getIP().replace("*************", "bd.sasw.com.br"), listaBancos1.getLogin(), listaBancos1.getSenha(), tamanhoPool);
                    listaConexoes.add(pool);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            System.err.println(e.getMessage());
            return false;
        }
    }
//**********************************************************************************************************************    

    /**
     * Obsoleta Carrega todo o Pool de conexões para todos os clientes que
     * estiverem na mapconect Não precisa ser chamado, a menos que o programador
     * queira, quando chama-se um cliente específico, seu pool é iniciado. Esse
     * método deixa o primeiro acesso muito lento, mas tem a vantagem de já
     * deixar todas as conexões feitas.
     *
     * @throws Exception - pode gerar exception
     */
    public void IniciaPool() {
        if (listaConexoes == null) {
            listaConexoes = new ArrayList();
            try {
                listaBancos = learqconect.MapeiaPoolWeb(Caminho);
                for (DadosBancos_OLD listaBancos1 : listaBancos) {
                    try {
                        PoolClientesConexoes pool = new PoolClientesConexoes();
                        pool.setEmpresa(listaBancos1.getEmpresa());
                        pool.MontaPool(listaBancos1.getIP(), listaBancos1.getLogin(), listaBancos1.getSenha(), tamanhoPool);
                        listaConexoes.add(pool);
                    } catch (Exception e) {
                        //garante que os outros clientes serão conectados mesmo que algum apresente problema
                    }
                }
            } catch (Exception e) {

            }
        }
    }

    public List<DadosBancos_OLD> obterClientesMapConect() {
        try {
            return learqconect.MapeiaPoolWeb(Caminho);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * Obsoleta Devolve o pool completo para tratamento manual
     *
     * @return
     */
    public List<PoolClientesConexoes> getPool() {
        return listaConexoes;
    }

    /**
     * Obsoleta Devolve o pool de conexões do cliente informado
     *
     * @param Empresa - Parâmetro da empresa que deseja-se o pool
     * @return - list com o pool de persistências
     */
    public List<Persistencia_OLD> getPoolCliente(String Empresa) {
        for (PoolClientesConexoes pool1 : listaConexoes) {
            if (pool1.getEmpresa().equals(Empresa)) {
                return pool1.getPool();
            }
        }
        return null;
    }
}

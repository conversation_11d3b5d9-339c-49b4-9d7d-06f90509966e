<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/recursoshumanos/portalrh.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <script src="../assets/scripts/jquery.mask.js" type="text/javascript"></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                #panelLogs_content{
                    height: 350px !important;
                }
                input:not([id*="opcao"]){
                    min-width: 95px !important;
                    width: 95px !important;
                    max-width: 95px !important;
                }

                #body{
                    min-height: calc(100vh - 40px);
                }

                [id*="formPesquisaRapida"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                [id*="formLogs"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar,
                body .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    background:#FFF !important;
                    border-bottom-color: #CCC !important;
                }

                #formPesquisaRapida .ui-radiobutton {
                    background: transparent !important;
                }

                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: 0px !important;
                        position: relative !important;
                    }

                    .DataGrid[id$="trajetos"] [role="columnheader"] > span {
                        top: -1px !important;
                        position: relative !important;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }

                    .DataGrid thead tr th:nth-child(3),
                    .DataGrid tbody tr td:nth-child(3),
                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2),
                    .DataGrid thead tr th:nth-child(12),
                    .DataGrid tbody tr td:nth-child(12){
                        min-width: 70px;
                        width: 70px;
                        max-width: 70px;
                    }

                    .DataGrid thead tr th:nth-child(2),
                    .DataGrid tbody tr td:nth-child(2),
                    .DataGrid thead tr th:nth-child(4),
                    .DataGrid tbody tr td:nth-child(4),
                    .DataGrid thead tr th:nth-child(11),
                    .DataGrid tbody tr td:nth-child(11),
                    .DataGrid thead tr th:nth-child(6),
                    .DataGrid tbody tr td:nth-child(6),
                    .DataGrid thead tr th:nth-child(8),
                    .DataGrid tbody tr td:nth-child(8){
                        min-width: 130px;
                        width: 130px;
                        max-width: 130px;
                    }

                    .DataGrid thead tr th:nth-child(10),
                    .DataGrid tbody tr td:nth-child(10){
                        min-width: 150px;
                        width: 150px;
                        max-width: 150px;
                    }

                    .DataGrid thead tr th:nth-child(5),
                    .DataGrid tbody tr td:nth-child(5){
                        min-width: 300px;
                    }

                    .DataGrid thead tr th:nth-child(7),
                    .DataGrid tbody tr td:nth-child(7),
                    .DataGrid thead tr th:nth-child(9),
                    .DataGrid tbody tr td:nth-child(9){
                        min-width: 200px;
                    }

                    .FilialNome{
                        margin-top: 11px !important
                    }
                    .FilialBairroCidade, .FilialEndereco{
                        line-height: 10px !important;
                    }
                    
                    #panelLogs_content table thead tr th:nth-child(1),
                    #panelLogs_content table tbody tr td:nth-child(1){
                        min-width: 30px !important;
                        width: 30px !important;
                        max-width: 30px !important;
                    }
                    
                    #panelLogs_content table thead tr th:nth-child(2),
                    #panelLogs_content table tbody tr td:nth-child(2){
                        min-width: 15px !important;
                        width: 15px !important;
                        max-width: 15px !important;
                    }
                    
                    #panelLogs_content .DataGrid thead tr th{
                        padding-top: 2px !important;
                    }
                }

                @media only screen and (max-width: 701px) and (min-width: 0px) {
                    #divCalendario{
                        zoom: 0.75;
                        top: 7px !important;
                        right: 10px !important;
                    }

                    .ui-datepicker{
                        left: auto !important;
                    }
                }
            </style>

            <script type="text/javascript">
                // <![CDATA[

                // ]]>
            </script>
        </h:head>
        <h:body id="body">
            <f:metadata>
                <f:viewAction action="#{funcionario.Persistencias(login.pp, login.satellite)}" />
                <f:viewAction action="#{contracheque.Persistencias(login.pp, login.satellite)}" />
                <f:viewAction action="#{contracheque.setPersistencia(login.pp,login.pool)}" />
                <f:viewAction action="#{contracheque.carregaListLogsContraCheques}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icone_satmob_contratosG.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.LogContraCheque}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Competencia}: "/>
                                        <span>
                                            <h:outputText value="#{contracheque.periodoIni}" converter="conversorCompet"/>
                                            - #{localemsgs.Ate} - 
                                            <h:outputText value="#{contracheque.periodoFim}" converter="conversorCompet"/>
                                        </span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{funcionario.filiais.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{funcionario.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{funcionario.filiais.bairro}, #{funcionario.filiais.cidade}/#{funcionario.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: center !important; padding-top: 4px !important;padding-bottom: 5px !important">
                                    <center><div style="width: 230px">
                                            <label style="margin: 0px 0px 4px 0px; font-size: 11pt; font-weight: 500; color: #555; display: block; width: 100%; text-align: center; background-color: #EEE; border: thin solid #DDD; text-shadow: 1px 1px #FFF; border-radius: 2px;">#{localemsgs.Competencia}</label>

                                            <p:datePicker id="periodoIni" view="month" styleClass="calendario" value="#{contracheque.periodoIni}" pattern="MM/yyyy"
                                                          yearNavigator="true" yearRange="2000:2030" showIcon="true" readonlyInput="true">
                                                <p:ajax event="dateSelect" listener="#{contracheque.carregaListLogsContraCheques}" update="msgs main:tabela cabecalho corporativo" />
                                            </p:datePicker>

                                            <label style="margin-left: 8px; margin-right: 8px; font-size: 11pt; font-weight: 500; color: #555">#{localemsgs.Ate}</label>

                                            <p:datePicker id="periodoFim" view="month" styleClass="calendario" value="#{contracheque.periodoFim}" pattern="MM/yyyy"
                                                          yearNavigator="true" yearRange="2000:2030" showIcon="true" readonlyInput="true">
                                                <p:ajax event="dateSelect" listener="#{contracheque.carregaListLogsContraCheques}" update="msgs main:tabela cabecalho corporativo" />
                                            </p:datePicker>
                                        </div>
                                    </center>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="formLogs" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('formLogs').hide()"/>
                    <p:dialog
                        widgetVar="dlgLogs"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="500"
                        style="min-height:450px !important; height:450px !important; max-height:450px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:10px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_historico.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="Logs" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelLogs" style="background: transparent; height: 350px !important">
                            <p:dataTable id="tabela" 
                                         value="#{contracheque.logList}" 
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         rowKey="#{logs.comando}_#{logs.hora}"
                                         currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} Logs"
                                         var="logs" 
                                         paginator="true" 
                                         reflow="true" 
                                         rows="50" 
                                         rowsPerPageTemplate="50, 100, 200, 300"
                                         paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                         scrollable="true" 
                                         selectionMode="single" 
                                         styleClass="tabela DataGrid" style="background-color: #EEE !important;">
                                <p:column headerText="#{localemsgs.Data}" class="text-center">
                                    <h:outputText value="#{logs.data}" converter="conversorData" class="text-center" />
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora}" class="text-center">
                                    <h:outputText value="#{logs.hora}" class="text-center" />
                                </p:column>
                                <p:column headerText="#{localemsgs.descricao}" class="text-center">
                                    <h:outputText value="#{logs.comando}" class="text-center" />
                                </p:column>
                            </p:dataTable>
                        </p:panel>
                    </p:dialog>
                </h:form>

                <!--Pesquisa rápida funcionário-->
                <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                    <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                    <p:dialog
                        widgetVar="dlgPesquisaRapida"
                        positionType="absolute"
                        responsive="true"
                        focus="opcao"
                        draggable="false"
                        modal="true"
                        closable="true"
                        resizable="false"
                        dynamic="true"
                        showEffect="drop"
                        hideEffect="drop"
                        closeOnEscape="false"
                        width="400"
                        style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_pesquisar.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Pesquisar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <p:panel id="panelPesquisaRapida" style="background: transparent">
                            <div style="display: flex; flex-direction: row; align-items: center; margin-top: 0px;">
                                <div style="flex-grow: 1; min-width: 50%;">
                                    <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}" style="margin-bottom: 10px"/>

                                    <p:selectOneRadio
                                        id="radioOpcoes"
                                        value="#{contracheque.chavePesquisa}"
                                        unselectable="true"
                                        layout="pageDirection">
                                        <f:selectItem itemLabel="#{localemsgs.Matr}" itemValue="MATR" />
                                        <f:selectItem itemLabel="#{localemsgs.Nome}" itemValue="NOME" />
                                        <f:selectItem itemLabel="#{localemsgs.Posto}" itemValue="POSTO" />

                                        <p:ajax update="direita" />
                                    </p:selectOneRadio>
                                </div>

                                <p:outputPanel
                                    id="direita"
                                    style="padding-left: 16px;">
                                    <p:outputPanel>
                                        <p:outputLabel for="opcao" rendered="#{contracheque.chavePesquisa eq 'MATR'}" value="#{localemsgs.Matr}: "/>
                                        <p:outputLabel for="opcao" rendered="#{contracheque.chavePesquisa eq 'NOME'}" value="#{localemsgs.Nome}: "/>
                                        <p:outputLabel for="opcao" rendered="#{contracheque.chavePesquisa eq 'POSTO'}" value="#{localemsgs.Posto}: "/>

                                        <p:inputText
                                            id="opcao"
                                            value="#{contracheque.valorPesquisa}"
                                            style="width: 100%" maxlength="60">
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12" style="text-align:right; padding-right:0px !important">

                                <p:commandLink id="botaoPesquisaRapida" action="#{contracheque.carregaListLogsContraCheques}" update="msgs main:tabela" oncomplete="PF('dlgPesquisaRapida').hide()"
                                               styleClass="btn btn-primary" style="color:#FFF !important">
                                    <i class="fa fa-search" style="margin-right:8px !important"></i>#{localemsgs.Pesquisar}
                                </p:commandLink>
                            </div>

                        </p:panel>
                    </p:dialog>
                </h:form>
                <script>
                    $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                        if (e.keyCode === 13) {
                            $('#botaoPesquisaRapida').click();
                        }
                    });
                </script>


                <h:form id="main">
                    <p:panel style="position: fixed; z-index: 1; right: 5px; bottom: 25px !important; background: transparent; height:200px !important;" id="botoes">
                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisaRapida').show();" update="formPesquisaRapida">
                                <p:graphicImage url="../assets/img/icone_pesquisar_transparente.png" height="50"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Historico}" actionListener="#{contracheque.carregarLogList}" update="msgs formLogs">
                                <p:graphicImage url="../assets/img/icone_historico.png" height="50"/>
                            </p:commandLink>
                        </div>

                        <div style="padding-bottom: 10px">
                            <p:commandLink title="#{localemsgs.Imprimir} #{localemsgs.Contracheque}" actionListener="#{contracheque.dblSelectLog(null)}" update="msgs" >
                                <p:graphicImage url="../assets/img/icone_pdf.png" height="50"/>
                            </p:commandLink>
                        </div>
                    </p:panel>


                    <p:panel class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <p:commandLink id="btImprimeContraCheque" value="Impressao" target="_blank"
                                       action="#{contracheque.imprimirContraChequeLog}" 
                                       update="msgs" styleClass="linkazul"
                                       ajax="false" style="display: none"/>

                        <p:dataTable id="tabela" 
                                     value="#{contracheque.listLogsContraCheques}" 
                                     emptyMessage="#{localemsgs.SemRegistros}"
                                     rowKey="#{listaLogs.funcion.matr}_#{listaLogs.fPMensal.codMovFP}_#{listaLogs.fPMensal.tipoFP}"
                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Contracheque}"
                                     var="listaLogs" 
                                     paginator="true" 
                                     reflow="true" 
                                     rows="100" 
                                     rowsPerPageTemplate="50, 100, 200, 300"
                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                     selection="#{contracheque.logSelecionado}"
                                     scrollable="true" 
                                     selectionMode="single" 
                                     styleClass="tabela DataGrid">
                            <p:ajax event="rowDblselect" listener="#{contracheque.dblSelectLog}" update="msgs"/>
                            <p:column headerText="#{localemsgs.CodFil}">
                                <h:outputText value="#{listaLogs.funcion.codFil}">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>

                            <p:column headerText="#{localemsgs.Competencia}">
                                <h:outputText value="#{listaLogs.fPMensal.competenciaDescr}" converter="conversorCompet" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Tipo}">
                                <h:outputText value="#{listaLogs.fPMensal.tipoFP}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Matr}">
                                <h:outputText value="#{listaLogs.funcion.matr}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Nome}">
                                <h:outputText value="#{listaLogs.funcion.nome}" />
                            </p:column>

                            <p:column headerText="#{localemsgs.secao}">
                                <h:outputText value="#{listaLogs.funcion.secao}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Local}">
                                <h:outputText value="#{listaLogs.funcion.localRef}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.CCusto}">
                                <h:outputText value="#{listaLogs.funcion.CCusto}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Cargo}">
                                <h:outputText value="#{listaLogs.funcion.cargo}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Operador}">
                                <h:outputText value="#{listaLogs.fPMensal.operador}" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Dt_Alter}">
                                <h:outputText value="#{listaLogs.fPMensal.dt_Alter}" converter="conversorData" />
                            </p:column>
                            <p:column headerText="#{localemsgs.Hr_Alter}">
                                <h:outputText value="#{listaLogs.fPMensal.hr_Alter}" converter="conversorHora" />
                            </p:column>
                        </p:dataTable>
                    </p:panel>
                </h:form>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:20px; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <div>
                                <label ref="lblCheck"><h:outputText styleClass="corporativo-label" value="#{localemsgs.Corporativo}: " /></label>
                                <p:selectBooleanCheckbox value="#{contracheque.corporativo}">
                                    <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{contracheque.carregaListLogsContraCheques}" />
                                </p:selectBooleanCheckbox>
                            </div>
                        </h:form>
                    </div>

                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" action="#{localeController.getLocales}">
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25"/>
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>   
</html>
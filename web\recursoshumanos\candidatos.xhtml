<!DOCTYPE html>
<html lang=”pt-br”>
  <head>
    <title>Página Responsiva</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <div>
<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://xmlns.jcp.org/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
<f:view locale="#{localeController.currentLocale}" contentType="text/html">
    <h:head>
        <link rel="icon" href="../assets/img/favicon.png"/>
        <title>
            #{localemsgs.SatMOB}
        </title>
        <link type="text/css" href="../assets/css/candidato.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/style.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet"/>
        <link type="text/css" href="../assets/css/common-layout.css" rel="stylesheet"/>

        <style type="text/css">
            @media only screen and (max-width: 600px) and (min-width: 10px) {

                #divDadosFilial,
                #divDadosFilial div,
                .FilialNome,
                .FilialEndereco,
                .FilialBairroCidade {
                    text-align: center !important;
                }
            }

            @media only screen and (max-width: 600px) and (min-width: 10px) {

                #divDadosFilial,
                #divDadosFilial div,
                .FilialNome,
                .FilialEndereco,
                .FilialBairroCidade {
                    text-align: center !important;
                }

                .ui-paginator-top {
                    white-space: normal !important;
                }

                .tabela .ui-datatable-scrollable-body {
                    height: calc(100% - 6.5em);
                }
            }

            html, body {
                max-height: 100% !important;
                overflow: hidden !important;
            }

            #divCorporativo {
                bottom: 23px !important;
            }

            #corporativo {
                max-width: 18vw;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
            }

            #corporativo label[ref="lblCheck"] {
                font-size: 11px !important;
                min-width: 75px !important;
                font-weight: 500 !important;
            }

            #divTopoTela2 {
                display: flex;
            }

            .tituloPagina {
                font-size: 13pt !important;
                font-weight: bold !important;
                line-height: 25px !important;
                display: block;
                font-family: 'Open Sans', sans-serif !important;
                color: #022B4A !important;
                margin-left: 10px !important;
            }

            .tituloDataHora {
                font-size: 8pt !important;
                font-weight: 600 !important;
                line-height: 10px !important;
                display: block;
                font-family: 'Open Sans', sans-serif !important;
                color: #404040 !important;
                margin-left: 10px !important;
            }

            .equal {
                display: flex;
                display: -webkit-flex;
                flex-wrap: wrap;
                align-content: flex-start;
            }
        </style>
    </h:head>

    <h:body id="root">
        <f:metadata>
            <f:viewParam name="empresa" value="#{candidato.empresa}"/>
            <f:viewAction action="#{login.criarPersistenciaSatellite()}"/>
            <f:viewAction action="#{login.criarPersistenciaEmpresa()}"/>
            <f:viewAction action="#{candidato.Persistencias(login.pp, login.satellite)}"/>
        </f:metadata>

        <p:growl id="msgs"/>

        <div class="app">
            <header class="fixed-header">
                <h:form id="cabecalho">
                    <div class="cabecalho equal" style="padding-top: 4px;">
                        <div id="divTopoTela2" class="col-md-6 col-xs-6">

                            <img src="../assets/img/icone_pessoas.png" height="40" alt="#{localemsgs.Pessoas}"/>

                            <div>
                                <label class="tituloPagina">#{localemsgs.CandidatoTitulo}</label>
                                <label class="tituloDataHora">
                                    <h:outputText value="#{localemsgs.Data}: "/>
                                    <span><h:outputText id="dataDia" value="#{candidato.dataTela}"
                                                        converter="conversorDia"/></span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6 col-xs-6"
                             style="display: flex; justify-content: flex-end; padding: 2px;">
                            <p:commandLink title="Home"
                                           ajax="false"
                                           action="/menu.xhtml?faces-redirect=true">
                                <p:graphicImage url="../assets/img/icone_home_branco.png" height="40"/>
                            </p:commandLink>

                            <p:commandLink title="#{localemsgs.Voltar}"
                                           ajax="false"
                                           onclick="window.history.back();" action="#">
                                <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                            </p:commandLink>
                        </div>
                    </div>
                </h:form>
            </header>

            <div class="main-content">
                <div class="sate-login" style="max-width: 1024px; margin: auto;">
                    <h:form id="main">
                        <p:panel id="cadastrar">
                            <p:wizard
                                    id="wizard"
                                    backLabel="#{localemsgs.Anterior}"
                                    nextLabel="#{localemsgs.Proximo}"
                                    flowListener="#{candidato.handleFlow}"
                            >
                                <p:tab
                                        id="tabCPFEmail"
                                        title="#{localemsgs.CadastroInicial}"
                                >
                                    <p:panelGrid
                                            columns="4"
                                            columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-3,ui-grid-col-4"
                                            layout="grid"
                                            styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="cpf"
                                                       value="#{localemsgs.CPF}: "/>
                                        <p:inputMask id="cpf"
                                                     value="#{candidato.novaPessoa.CPF}"
                                                     maxlength="11"
                                                     required="true"
                                                     mask="#{mascaras.mascaraCPF}">
                                            <p:watermark for="cpf"
                                                         value="#{localemsgs.CPF}"/>
                                        </p:inputMask>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="email"
                                                       value="#{localemsgs.Email}: "/>
                                        <p:inputText id="email"
                                                     value="#{candidato.novaPessoa.email}"
                                                     style="width: 100%"
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Email}"
                                                     maxlength="50">
                                            <p:watermark for="email"
                                                         value="#{localemsgs.Email}"/>
                                        </p:inputText>
                                    </p:panelGrid>
                                </p:tab>

                                <p:tab
                                        id="tabDadosGerais"
                                        title="#{localemsgs.DadosGerais}"
                                >
                                    <p:panelGrid
                                            columns="4"
                                            columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-3,ui-grid-col-4"
                                            layout="grid"
                                            styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="matr"
                                                       value="#{localemsgs.Matr}: "
                                                       rendered="#{candidato.novaPessoa.situacao eq 'F'}"/>
                                        <p:inputText id="matr"
                                                     value="#{candidato.novaPessoa.matr}"
                                                     disabled="true"
                                                     label="#{localemsgs.Matr}"
                                                     maxlength="15"
                                                     style="width: 100%"
                                                     rendered="#{candidato.novaPessoa.situacao eq 'F'}">
                                            <f:convertNumber pattern="000000"/>
                                            <p:watermark for="matr"
                                                         value="#{localemsgs.Matr}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-3,ui-grid-col-4"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="rg"
                                                       value="#{localemsgs.RG}: "/>
                                        <p:inputText id="rg"
                                                     value="#{candidato.novaPessoa.RG}"
                                                     required="true"
                                                     label="#{localemsgs.RG}"
                                                     style="width: 100%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.RG}"
                                                     maxlength="20">
                                            <p:watermark for="rg"
                                                         value="#{localemsgs.RG}"/>
                                        </p:inputText>

                                        <p:outputLabel for="rgorg"
                                                       value="#{localemsgs.RGOrg}: "/>
                                        <p:inputText id="rgorg"
                                                     value="#{candidato.novaPessoa.RGOrgEmis}"
                                                     disabled="false"
                                                     required="true"
                                                     label="#{localemsgs.RGOrg}"
                                                     style="width: 100%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.RGOrg}"
                                                     maxlength="15">
                                            <p:watermark for="rgorg"
                                                         value="#{localemsgs.RGOrg}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="nome"
                                                       value="#{localemsgs.Nome}: "/>
                                        <p:inputText id="nome"
                                                     value="#{candidato.novaPessoa.nome}"
                                                     style="width: 100%"
                                                     required="true"
                                                     label="#{localemsgs.Nome}"
                                                     disabled="false"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Nome}"
                                                     maxlength="50">
                                            <p:watermark for="nome"
                                                         value="#{localemsgs.Nome}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid
                                            columns="4"
                                            columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                            layout="grid"
                                            styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="fone1"
                                                       value="#{localemsgs.Fone1}: "/>
                                        <p:inputMask id="fone1"
                                                     mask="#{mascaras.mascaraFone}"
                                                     value="#{candidato.novaPessoa.fone1}"
                                                     disabled="false"
                                                     maxlength="11"
                                                     style="width: 100%">
                                            <p:watermark for="fone1"
                                                         value="#{localemsgs.Fone1}"/>
                                        </p:inputMask>

                                        <p:outputLabel for="fone2"
                                                       value="#{localemsgs.Fone2}: "/>
                                        <p:inputMask id="fone2"
                                                     mask="#{mascaras.mascaraFone}"
                                                     value="#{candidato.novaPessoa.fone2}"
                                                     disabled="false"
                                                     maxlength="11"
                                                     style="width: 100%">
                                            <p:watermark for="fone2"
                                                         value="#{localemsgs.Fone2}"/>
                                        </p:inputMask>
                                    </p:panelGrid>

                                    <p:panelGrid columns="5"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-4"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="cep"
                                                       value="#{localemsgs.CEP}: "/>
                                        <p:inputMask id="cep"
                                                     value="#{candidato.novaPessoa.CEP}"
                                                     disabled="false"
                                                     mask="#{mascaras.mascaraCEP}"
                                                     required="true"
                                                     label="#{localemsgs.CEP}"
                                                     maxlength="#{mascaras.maxLengthCEP}"
                                                     style="width: 100%">
                                            <p:watermark for="cep"
                                                         value="#{localemsgs.CEP}"/>
                                        </p:inputMask>

                                        <p:commandLink title="#{localemsgs.Pesquisar}"
                                                       style="visibility: #{localeController.currentLocale == 'pt_BR' ? 'visible' : 'hidden'}"
                                                       partialSubmit="true"
                                                       process="@this main:cep"
                                                       id="cep_pesquisa"
                                                       disabled="false"
                                                       update="main:cep main:ende main:bairro main:cidade main:uf msgs"
                                                       action="#{candidato.buscarEndereco}">
                                            <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png"
                                                            height="30"/>
                                        </p:commandLink>

                                        <p:outputLabel for="bairro"
                                                       value="#{localemsgs.Bairro}: "/>
                                        <p:inputText id="bairro"
                                                     value="#{candidato.novaPessoa.bairro}"
                                                     disabled="false"
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Bairro}"
                                                     maxlength="20"
                                                     style="width: 100%">
                                            <p:watermark for="bairro"
                                                         value="#{localemsgs.Bairro}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="ende"
                                                       value="#{localemsgs.Endereco}: "/>
                                        <p:inputText id="ende"
                                                     value="#{candidato.novaPessoa.endereco}"
                                                     disabled="false"
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Endereco}"
                                                     maxlength="50"
                                                     style="width: 100%">
                                            <p:watermark for="ende"
                                                         value="#{localemsgs.Endereco}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="cidade"
                                                       value="#{localemsgs.Cidade}: "/>
                                        <h:panelGroup rendered="#{localeController.currentLocale == 'pt_BR'}">
                                            <p:autoComplete id="cidade"
                                                            value="#{candidato.novaPessoa.cidade}"
                                                            maxlength="40"
                                                            styleClass="cidade"
                                                            disabled="false"
                                                            required="true"
                                                            label="#{localemsgs.Cidade}"
                                                            completeMethod="#{candidato.buscarCidade}"
                                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}"
                                                            scrollHeight="200"
                                                            forceSelection="true"
                                                            placeholder="#{localemsgs.Cidade}"
                                                            style="width: 100%">
                                                <p:ajax event="itemSelect"
                                                        listener="#{candidato.selecionarCidade}"
                                                        update="main:cidade main:uf"/>
                                                <p:watermark for="cidade"
                                                             value="#{localemsgs.Cidade}"/>
                                            </p:autoComplete>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{localeController.currentLocale != 'pt_BR'}">
                                            <p:inputText id="cidade2"
                                                         value="#{candidato.novaPessoa.cidade}"
                                                         maxlength="40"
                                                         styleClass="cidade"
                                                         disabled="false"
                                                         required="true"
                                                         label="#{localemsgs.Cidade}"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cidade}"
                                                         placeholder="#{localemsgs.Cidade}"
                                                         style="width: 100%">
                                                <p:watermark for="cidade2"
                                                             value="#{localemsgs.Cidade}"/>
                                            </p:inputText>
                                        </h:panelGroup>

                                        <p:outputLabel for="uf"
                                                       value="#{localemsgs.UF}: "/>
                                        <p:inputText id="uf"
                                                     value="#{candidato.novaPessoa.UF}"
                                                     disabled="false"
                                                     required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.UF}"
                                                     label="#{localemsgs.UF}"
                                                     maxlength="2"
                                                     style="width: 100%">
                                            <p:watermark for="uf"
                                                         value="#{localemsgs.UF}"/>
                                        </p:inputText>
                                    </p:panelGrid>
                                </p:tab>

                                <p:tab id="tabDadosPessoais"
                                       title="#{localemsgs.DadosPessoais}">
                                    <p:panelGrid columns="4"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="dt_nasc"
                                                       value="#{localemsgs.Dt_Nasc}:"/>
                                        <p:calendar id="dt_nasc"
                                                    value="#{candidato.novaPessoa.dt_nasc}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="dt_nasc"
                                                         value="01/01/1971"/>
                                        </p:calendar>

                                        <p:outputLabel for="altura"
                                                       value="#{localemsgs.Altura}: "/>
                                        <p:inputNumber id="altura"
                                                       value="#{candidato.novaPessoa.altura}"
                                                       disabled="false"
                                                       label="#{localemsgs.Altura}"
                                                       maxlength="3"
                                                       decimalPlaces="0"
                                                       style="width: 100%">
                                            <f:convertNumber pattern="000"/>
                                            <p:watermark for="altura"
                                                         value="#{localemsgs.Altura}"/>
                                        </p:inputNumber>

                                        <p:outputLabel for="peso"
                                                       value="#{localemsgs.Peso}: "/>
                                        <p:inputNumber id="peso"
                                                       value="#{candidato.novaPessoa.peso}"
                                                       disabled="false"
                                                       label="#{localemsgs.Peso}"
                                                       maxlength="3"
                                                       decimalPlaces="0"
                                                       style="width: 100%">
                                            <f:convertNumber pattern="000"/>
                                            <p:watermark for="peso"
                                                         value="#{localemsgs.Peso}"/>
                                        </p:inputNumber>

                                        <p:outputLabel for="sexo"
                                                       value="#{localemsgs.Sexo}: "/>

                                        <p:selectOneMenu value="#{candidato.novaPessoa.sexo}"
                                                         id="sexo"
                                                         disabled="false"
                                                         style="width: 100%">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Masculino}"
                                                          itemValue="M"/>
                                            <f:selectItem itemLabel="#{localemsgs.Feminino}"
                                                          itemValue="F"/>
                                            <f:selectItem itemLabel="#{localemsgs.Outros}"
                                                          itemValue="O"/>
                                        </p:selectOneMenu>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="mae"
                                                       value="#{localemsgs.Mae}: "/>
                                        <p:inputText id="mae"
                                                     value="#{candidato.novaPessoa.mae}"
                                                     disabled="false"
                                                     label="#{localemsgs.Mae}"
                                                     style="width: 100%"
                                                     maxlength="40">
                                            <p:watermark for="mae"
                                                         value="#{localemsgs.Mae}"/>
                                        </p:inputText>

                                        <p:outputLabel for="pis"
                                                       value="#{localemsgs.PIS}: "/>
                                        <p:inputText id="pis"
                                                     value="#{candidato.novaPessoa.PIS}"
                                                     disabled="false"
                                                     label="#{localemsgs.PIS}"
                                                     style="width: 100%"
                                                     maxlength="15">
                                            <p:watermark for="pis"
                                                         value="#{localemsgs.PIS}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="CNH"
                                                       value="#{localemsgs.CNH}: "/>
                                        <p:inputText id="CNH"
                                                     value="#{candidato.novaPessoa.CNH}"
                                                     disabled="false"
                                                     label="#{localemsgs.CNH}"
                                                     style="width: 100%"
                                                     maxlength="15">
                                            <p:watermark for="CNH"
                                                         value="#{localemsgs.CNH}"/>
                                        </p:inputText>

                                        <p:outputLabel for="CNHDtVenc"
                                                       value="#{localemsgs.CNHDtVenc}:"/>
                                        <p:calendar id="CNHDtVenc"
                                                    value="#{candidato.novaPessoa.CNHDtVenc}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="CNHDtVenc" value="01/01/1971"/>
                                        </p:calendar>
                                    </p:panelGrid>
                                </p:tab>

                                <p:tab id="tabFormacao"
                                       title="#{localemsgs.DadosFormacao}"
                                       disabled="false">
                                    <p:panelGrid columns="2"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-10"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="indicacao"
                                                       value="#{localemsgs.Indicacao}: "/>
                                        <p:inputText id="indicacao"
                                                     value="#{candidato.novaPessoa.indicacao}"
                                                     disabled="false"
                                                     label="#{localemsgs.Indicacao}"
                                                     style="width: 100%"
                                                     maxlength="50">
                                            <p:watermark for="indicacao"
                                                         value="#{localemsgs.Indicacao}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="6"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="dt_FormIni"
                                                       value="#{localemsgs.Dt_FormIni}:"/>
                                        <p:calendar id="dt_FormIni"
                                                    value="#{candidato.novaPessoa.dt_FormIni}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="dt_FormIni"
                                                         value="01/01/1971"/>
                                        </p:calendar>

                                        <p:outputLabel for="dt_FormFim"
                                                       value="#{localemsgs.Dt_FormFim}:"/>
                                        <p:calendar id="dt_FormFim"
                                                    value="#{candidato.novaPessoa.dt_FormFim}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="dt_FormFim"
                                                         value="01/01/1971"/>
                                        </p:calendar>

                                        <p:outputLabel for="certific"
                                                       value="#{localemsgs.NCertificado}: "/>
                                        <p:inputText id="certific"
                                                     value="#{candidato.novaPessoa.certific}"
                                                     disabled="false"
                                                     label="#{localemsgs.NCertificado}"
                                                     style="width: 100%"
                                                     maxlength="15">
                                            <p:watermark for="certific"
                                                         value="#{localemsgs.NCertificado}"/>
                                        </p:inputText>

                                        <p:outputLabel for="localForm"
                                                       value="#{localemsgs.LocalForm}: "/>
                                        <p:inputText id="localForm"
                                                     value="#{candidato.novaPessoa.localForm}"
                                                     disabled="false"
                                                     label="#{localemsgs.LocalForm}"
                                                     style="width: 100%"
                                                     maxlength="60">
                                            <p:watermark for="localForm"
                                                         value="#{localemsgs.LocalForm}"/>
                                        </p:inputText>

                                        <p:outputLabel for="dt_Recicl"
                                                       value="#{localemsgs.Dt_Recicl}:"/>
                                        <p:calendar id="dt_Recicl"
                                                    value="#{candidato.novaPessoa.dt_Recicl}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="dt_Recicl"
                                                         value="01/01/1971"/>
                                        </p:calendar>

                                        <p:outputLabel for="dt_VenCurs"
                                                       value="#{localemsgs.Dt_VenCurs}:"/>
                                        <p:calendar id="dt_VenCurs"
                                                    value="#{candidato.novaPessoa.dt_VenCurs}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="dt_VenCurs"
                                                         value="01/01/1971"/>
                                        </p:calendar>
                                    </p:panelGrid>

                                    <p:panelGrid columns="6"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-1,ui-grid-col-1,ui-grid-col-2,ui-grid-col-4"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="reg_PF"
                                                       value="#{localemsgs.Reg_PF}: "/>
                                        <p:inputText id="reg_PF"
                                                     value="#{candidato.novaPessoa.reg_PF}"
                                                     disabled="false"
                                                     label="#{localemsgs.Reg_PF}"
                                                     style="width: 100%"
                                                     maxlength="15">
                                            <p:watermark for="reg_PF"
                                                         value="#{localemsgs.Reg_PF}"/>
                                        </p:inputText>

                                        <p:outputLabel for="reg_PFUF"
                                                       value="#{localemsgs.Reg_PFUF}: "/>
                                        <p:inputText id="reg_PFUF"
                                                     value="#{candidato.novaPessoa.reg_PFUF}"
                                                     disabled="false"
                                                     label="#{localemsgs.Reg_PFUF}"
                                                     style="width: 100%"
                                                     maxlength="2">
                                            <p:watermark for="reg_PFUF"
                                                         value="#{localemsgs.Reg_PFUF}"/>
                                        </p:inputText>

                                        <p:outputLabel for="reg_PFDt"
                                                       value="#{localemsgs.Reg_PFDt}:"/>
                                        <p:calendar id="reg_PFDt"
                                                    value="#{candidato.novaPessoa.reg_PFDt}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="reg_PFDt"
                                                         value="01/01/1971"/>
                                        </p:calendar>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="carNacVig"
                                                       value="#{localemsgs.CarNacVig}: "/>
                                        <p:inputText id="carNacVig"
                                                     value="#{candidato.novaPessoa.carNacVig}"
                                                     disabled="false"
                                                     label="#{localemsgs.Reg_PFUF}"
                                                     style="width: 100%"
                                                     maxlength="15">
                                            <p:watermark for="carNacVig"
                                                         value="#{localemsgs.CarNacVig}"/>
                                        </p:inputText>

                                        <p:outputLabel for="dtValCNV"
                                                       value="#{localemsgs.DtValCNV}:"/>
                                        <p:calendar id="dtValCNV"
                                                    value="#{candidato.novaPessoa.dtValCNV}"
                                                    disabled="false"
                                                    converter="conversorData"
                                                    mask="99/99/9999"
                                                    yearRange="c-125:c+125"
                                                    navigator="true"
                                                    pattern="#{mascaras.padraoData}"
                                                    locale="#{localeController.getCurrentLocale()}"
                                                    styleClass="calendario"
                                                    style="width: 100%">
                                            <p:watermark for="dtValCNV"
                                                         value="01/01/1971"/>
                                        </p:calendar>
                                    </p:panelGrid>

                                    <p:panelGrid columns="7"
                                                 columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-1,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1"
                                                 layout="grid"
                                                 styleClass="ui-panelgrid-blank">
                                        <p:outputLabel value="#{localemsgs.ExtensoesTV}: "/>

                                        <p:outputLabel for="extTV"
                                                       value="#{localemsgs.ExtTV}:"/>
                                        <p:selectBooleanCheckbox id="extTV"
                                                                 value="#{candidato.extTV}"
                                                                 disabled="false"/>

                                        <p:outputLabel for="extSPP"
                                                       value="#{localemsgs.ExtSPP}:"/>
                                        <p:selectBooleanCheckbox id="extSPP"
                                                                 value="#{candidato.extSPP}"
                                                                 disabled="false"/>

                                        <p:outputLabel for="extEscolta"
                                                       value="#{localemsgs.ExtEscolta}:"/>
                                        <p:selectBooleanCheckbox id="extEscolta"
                                                                 value="#{candidato.extEscolta}"
                                                                 disabled="false"/>
                                    </p:panelGrid>

                                    <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important">
                                        <p:dataTable value="#{candidato.cargos}"
                                                     selection="#{candidato.cargosSelecionados}"
                                                     scrollHeight="200"
                                                     scrollable="true"
                                                     style="background: transparent"
                                                     rowKey="#{item.cargo}"
                                                     rowSelectMode="checkbox"
                                                     var="item"
                                                     styleClass="tabelaArquivos">
                                            <f:facet name="header">
                                                #{localemsgs.CargoPretendido}
                                            </f:facet>
                                            <p:column selectionMode="multiple" style="width:16px;text-align:center"/>
                                            <p:column headerText="#{localemsgs.Cargo}" style="width: 30px">
                                                <h:outputText value="#{item.cargo}" title="#{item.cargo}"/>
                                            </p:column>
                                            <p:column headerText="#{localemsgs.Descricao}">
                                                <h:outputText value="#{item.descricao}" title="#{item.descricao}"/>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>
                                </p:tab>

                                <p:tab id="tabDocumentos"
                                       title="#{localemsgs.Documentos}">
                                    <div style="text-align: justify; width: 100%; padding-bottom: 10px">
                                        <h:outputText value="#{localemsgs.ArrasteArquivo}:"/>
                                    </div>

                                    <div style="text-align: center; width: 100%; height: 150px">
                                        <p:fileUpload id="upload"
                                                      fileUploadListener="#{candidato.handleFileUpload}"
                                                      allowTypes="/(\.|\/)(pdf|jpe?g|xls|xlsx|doc|docx)$/"
                                                      label="#{localemsgs.Pesquisar}"
                                                      auto="true"
                                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                                      dragDropSupport="true"
                                                      fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                                      update="msgs @form"
                                                      previewWidth="10"
                                                      skinSimple="true">
                                            <h:outputText value="#{localemsgs.ArrasteAqui}"
                                                          id="ArrasteAqui"
                                                          style="text-align: justify; color: lightgray; top: 30px; position: relative;"/>
                                        </p:fileUpload>
                                    </div>

                                    <p:panel style="background: transparent; border: 1px solid #E6E6E6 !important">
                                        <p:dataTable value="#{candidato.documentos}"
                                                     scrollHeight="200"
                                                     scrollable="true"
                                                     style="background: transparent"
                                                     rowKey="#{documentos.ordem}"
                                                     var="documentos"
                                                     id="arquivos"
                                                     styleClass="tabelaArquivos">
                                            <p:column headerText="#{localemsgs.Arquivos}"
                                                      style="text-align: center">
                                                <p:commandLink
                                                        actionListener="#{candidato.handleFileDownload(documentos)}"
                                                        ajax="false"
                                                        value="#{documentos.descricao}"
                                                        update="msgs">
                                                    <p:fileDownload value="#{candidato.download}"/>
                                                </p:commandLink>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.ModificadoEm}"
                                                      style="width: 110px; text-align: center">
                                                <h:outputText value="#{documentos.dt_alter}"
                                                              converter="conversorData"/>
                                            </p:column>

                                            <p:column style="width: 30px">
                                                <p:commandLink
                                                        actionListener="#{candidato.handleFileDelete(documentos)}"
                                                        update="msgs @form">
                                                    <p:confirm header="#{localemsgs.Confirmacao}"
                                                               message="#{localemsgs.ExcluirDocumento}"
                                                               icon="ui-icon-alert"/>
                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png"
                                                                    height="20"/>
                                                </p:commandLink>

                                                <p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
                                                    <p:commandButton value="#{localemsgs.Sim}"
                                                                     type="button"
                                                                     styleClass="ui-confirmdialog-yes"
                                                                     icon="pi pi-check"/>
                                                    <p:commandButton value="#{localemsgs.Nao}"
                                                                     type="button"
                                                                     styleClass="ui-confirmdialog-no"
                                                                     icon="pi pi-times"/>
                                                </p:confirmDialog>
                                            </p:column>
                                        </p:dataTable>
                                    </p:panel>

                                    <div class="col-md-12 col-sm-12 col-xs-12"
                                         style="text-align: center; padding: 10px 0px 0px 0px !important;">
                                        <p:commandLink id="salvarPessoa"
                                                       action="#{candidato.editar}"
                                                       update="msgs"
                                                       rendered="#{candidato.isEdicao}"
                                                       styleClass="btn btn-primary">
                                            <i class="fa fa-save"
                                               style="margin-right:8px !important"></i>#{localemsgs.Salve}
                                        </p:commandLink>
                                    </div>
                                </p:tab>
                            </p:wizard>
                        </p:panel>
                    </h:form>
                </div>
            </div>

            <footer class="fixed-footer">
                <div class="footer-body-2">
                    <div id="divFooterTimer"
                         style="flex-shrink: 0; min-height:10px !important;max-height:40px !important;">
                        <table class="footer-time" style="min-height:10px !important">
                            <tr>
                                <td>
                                    <p:clock pattern="HH:mm:ss" class="TextoRodape"/>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h:outputText value="#{localeController.mostraData}" class="TextoRodape"/>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div style="flex-shrink: 2; min-height:10px !important;max-height:40px !important;">
                        <table class="footer-user" style="min-height:10px !important">
                            <tr>
                                <td class="rodapeinfos">#{localemsgs.Saudacao}</td>
                                <td><img alt="#{localemsgs.Empresa}"
                                         src="#{login.getLogo(login.empresa.bancoDados)}"
                                         height="47px"
                                         width="59px"/>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div style="flex-shrink: 1; height:40px !important;">
                        <table class="footer-logos"
                               style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <tr>
                                <td><img alt="Logo" src="../assets/img/logo_satweb.png"/></td>
                                <td>
                                    <h:form id="locale">
                                        <p:commandLink actionListener="#{localeController.increment}"
                                                       action="#{localeController.getLocales}"
                                                       update="cabecalho main locale msgs"
                                        >
                                            <p:graphicImage url="../assets/img/#{localeController.number}.png"
                                                            height="25"/>
                                            <f:viewParam name="empresa" value="#{candidato.empresa}"/>
                                        </p:commandLink>
                                    </h:form>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </footer>
        </div>

        <ui:insert name="loading">
            <ui:include src="../assets/template/loading.xhtml"/>
        </ui:insert>
    </h:body>
</f:view>
</html>

    </div>
  </body>
</html>
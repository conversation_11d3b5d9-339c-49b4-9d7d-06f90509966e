/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class OS_VFreq {

    private String OS;
    private String CodFil;
    private String DiaSem;
    private String Hora1;
    private String Tipo;
    private String Hora2;
    private String RotaC;
    private String Dias;
    private String DU;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public OS_VFreq() {
    }

    public OS_VFreq(OS_VFreq original) {
        OS = original.getOS();
        CodFil = original.getCodFil();
        DiaSem = original.getDiaSem();
        Hora1 = original.getHora1();
        Tipo = original.getTipo();
        Hora2 = original.getHora2();
        RotaC = original.getRotaC();
        Dias = original.getDias();
        DU = original.getDU();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
    }

    public String getOS() {
        return OS;
    }

    public void setOS(String OS) {
        this.OS = OS;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getDiaSem() {
        return DiaSem;
    }

    public void setDiaSem(String DiaSem) {
        this.DiaSem = DiaSem;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getHora2() {
        return Hora2;
    }

    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    public String getRotaC() {
        return RotaC;
    }

    public void setRotaC(String RotaC) {
        this.RotaC = RotaC;
    }

    public String getDias() {
        return Dias;
    }

    public void setDias(String Dias) {
        this.Dias = Dias;
    }

    public String getDU() {
        return DU;
    }

    public void setDU(String DU) {
        this.DU = DU;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

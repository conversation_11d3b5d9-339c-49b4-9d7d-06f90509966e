<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">

        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/common-layout.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script __src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .grelha [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {

                    #divDadosFilial,
                    #divDadosFilial div,
                    .FilialNome,
                    .FilialEndereco,
                    .FilialBairroCidade{
                        text-align: center !important;
                    }

                    .ui-paginator-top {
                        white-space: normal !important;
                    }

                    .tabela .ui-datatable-scrollable-body {
                        height: calc(100% - 6.5em);
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .grelha{
                        width:100% !important;
                        border:none !important
                    }

                    .grelha thead tr th,
                    .grelha tbody tr td {
                        min-width: 100px;
                        max-width: 100px;
                        white-space: normal !important;
                        overflow-wrap: break-word !important;
                        word-wrap: break-word !important;
                        -webkit-hyphens: auto !important;
                        -ms-hyphens: auto !important;
                        hyphens: auto !important;
                    }

                    .grelha thead tr th:nth-child(1),
                    .grelha tbody tr td:nth-child(1) {
                        min-width: 90px !important;
                        max-width: 90px !important;
                    }
                    .grelha thead tr th:nth-child(2),
                    .grelha tbody tr td:nth-child(2) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .grelha thead tr th:nth-child(3),
                    .grelha tbody tr td:nth-child(3) {
                        min-width: 80px !important;
                        max-width: 80px !important;
                    }
                    .grelha thead tr th:nth-child(4),
                    .grelha tbody tr td:nth-child(4) {
                        min-width: 70px !important;
                        max-width: 70px !important;
                    }
                    .grelha thead tr th:nth-child(5),
                    .grelha tbody tr td:nth-child(5) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .grelha thead tr th:nth-child(6),
                    .grelha tbody tr td:nth-child(6) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .grelha thead tr th:nth-child(7),
                    .grelha tbody tr td:nth-child(7) {
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .tabelaGuias.grelha thead tr th:nth-child(1),
                    .tabelaGuias.grelha tbody tr td:nth-child(1) {
                        min-width: 50px !important;
                        max-width: 50px !important;
                    }
                    .tabelaGuias.grelha thead tr th:nth-child(2),
                    .tabelaGuias.grelha tbody tr td:nth-child(2) {
                        min-width: 50px !important;
                        max-width: 50px !important;
                    }
                    .tabelaGuias.grelha thead tr th:nth-child(3),
                    .tabelaGuias.grelha tbody tr td:nth-child(3) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    .tabelaGuias.grelha thead tr th:nth-child(4),
                    .tabelaGuias.grelha tbody tr td:nth-child(4) {
                        min-width: 100px !important;
                        max-width: 100px !important;
                    }
                    .tabelaGuias.grelha thead tr th:nth-child(5),
                    .tabelaGuias.grelha tbody tr td:nth-child(5) {
                        min-width: 70px !important;
                        max-width: 70px !important;
                    }
                    .tabelaGuias.grelha thead tr th:nth-child(6),
                    .tabelaGuias.grelha tbody tr td:nth-child(6) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }

                    .grelha thead tr th,
                    .grelha tbody tr td{
                        text-align: center !important;
                    }
                }

                html, body{
                    max-height:100% !important;
                    overflow:hidden !important;
                }

                #divCorporativo{
                    bottom:23px !important;
                }

                #corporativo {
                    max-width: 18vw;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }

                #corporativo label[ref="lblCheck"]{
                    font-size:11px !important;
                    min-width:75px !important;
                    font-weight:500 !important;
                }

                footer .ui-chkbox-box {
                    max-width: 12px !important;
                    max-height: 12px !important;
                }

                .ui-dialog .ui-panel-content {
                    height: auto !important;
                }

                #formContrato .ui-selectonemenu.ui-state-default {
                    background: #fff !important;
                }

                #formContrato .ui-selectonemenu.ui-state-disabled {
                    color: #555 !important;
                    background: #f7f7f7 !important;
                    opacity: 0.7 !important;
                }

                #formPesquisa .ui-radiobutton {
                    background: transparent !important;
                }

                .entradaExcluida, tr.entradaExcluida:hover td {
                    color: red !important;
                }

                .entradaEspecial {
                    background-color: #c2caff !important;
                }

                .entradaValor, tr.entradaValor td {
                    color: #0b0 !important;
                }

                .tabela .ui-datatable-scrollable-body{
                    height: calc(100% - 9em);
                }

                .botoesDataTable {
                    width: 40px;
                    margin-top: 8px;
                    position: absolute;
                    right: -14px; top: 50%;
                    transform: translateY(-50%);
                }

                .infoSecundaria {
                    color: gray;
                }

                .semPaddingLateral {
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }

                .debugbox * {
                    border: thin dotted red;
                }

                .inputNumber > input {
                    width: 100px;
                }

                .footer-buttons {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: nowrap;
                    align-items: center;
                    justify-content: flex-start;
                    margin-left: 20px;
                }

                .footer-buttons > * {
                    margin-left: 10px;
                    margin-right: 10px;
                }

                .boxSelecaoCaixaForte {
                    font-size: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }

                .boxSelecaoFilial {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }

                .boxSelecaoFilial > * {
                    flex-shrink: 0;
                    margin-bottom: 4px;
                }

                .tabelaReformulada .ui-sortable-column-icon.ui-icon:before {
                    color: #FFF !important;
                }

                .tabelaReformulada .ui-state-hover .ui-sortable-column-icon.ui-icon:before {
                    color: black !important;
                }

                .tabelaReformulada .ui-state-hover { 
                    background-color: #E6E6E6 !important; 
                    background-image: none !important;
                    color: black !important;
                }

                #divTopoTela2 {
                    display: flex;
                }

                .tituloPagina{
                    font-size:13pt !important;
                    font-weight:bold !important;
                    line-height: 25px !important;
                    display:block;
                    _position:absolute;
                    _top:3px; 
                    _left:40px;
                    font-family:'Open Sans', sans-serif !important;
                    color:#022B4A !important;
                    margin-left:10px !important;
                }

                .tituloDataHora{
                    font-size:8pt !important;
                    font-weight:600 !important;
                    line-height: 10px !important;
                    display:block;
                    _position:absolute;
                    _top:28px; 
                    _left:40px;
                    font-family:'Open Sans', sans-serif !important;
                    color:#404040 !important;
                    margin-left:10px !important;
                }

                .equal {
                    display: flex;
                    display: -webkit-flex;
                    flex-wrap: wrap;
                    align-content: flex-start;
                }
            </style>
        </h:head>

        <h:body id="root">
            <p:growl id="msgs"/>

            <div class="app">
                <ui:include src="/cxforte/entrada/header.xhtml"/>

                <div class="main-content">
                    <div class="row-no-gutters" style="height: 100%;">
                        <div class="col-sm-6" style="height: 100%; padding: 0;">
                            <ui:include src="/cxforte/entrada/tabela.xhtml"/>
                        </div>
                        <div class="col-sm-6" style="height: 100%; padding: 0;">
                            <div style="background-color: #ECF0F5 !important; padding: 16px 14px 14px 14px !important; height: 100%;">
                                <div class="ui-grid ui-grid-responsive FundoPagina2" style="overflow:hidden !important; padding-right: 12px !important;">
                                    <ui:include src="/cxforte/entrada/tabela_guias.xhtml"/>

                                    <ui:include src="/cxforte/entrada/cadastro_guia.xhtml"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <ui:include src="/cxforte/entrada/footer.xhtml"/>
            </div>

            <ui:include src="entrada/volumes_gtv.xhtml"/>

            <ui:include src="entrada/confirmacao_parada.xhtml"/>

            <ui:include src="entrada/confirmacao_horario.xhtml"/>

            <ui:include src="entrada/utilizar_parada.xhtml"/>

            <ui:include src="entrada/cancelar_operacao.xhtml"/>

            <ui:insert name="loading" >
                <ui:include src="/assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>

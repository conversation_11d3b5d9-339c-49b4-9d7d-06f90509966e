<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:o="http://omnifaces.org/ui"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
    >

    <h:form id="main">
        <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
            <div class="ui-grid-row">
                <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                    <p:panel style="display: inline;">
                        <p:dataTable
                            id="tabelaEscalas"
                            value="#{escalaRotaMB.escalasPaginadas}"
                            selection="#{escalaRotaMB.escalaSelecionada}"
                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Escalas}"
                            var="escala"
                            lazy="true"
                            selectionMode="single"
                            emptyMessage="#{localemsgs.SemRegistros}"
                            paginator="true"
                            rows="25"
                            reflow="true"
                            rowsPerPageTemplate="5,10,15, 20, 25, 50, 100"
                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                            styleClass="tabela"
                            scrollable="true"
                            scrollWidth="100%"
                            paginatorPosition="top"
                            class="tabela DataGrid"
                            style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important; width:100% !important;"
                            >
                            <p:ajax
                                event="rowDblselect"
                                listener="#{escalaRotaMB.ativarModalEdicao()}"
                                update="cadastroEscala msgs"/>

                        </p:dataTable>

                    </p:panel>
                </div>
            </div>
        </div>

        <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 80px !important; background: transparent; height:200px !important;" id="botoes">
            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.Adicionar}"
                               actionListener="#{escalaRotaMB.ativarModalCadastro()}"
                               update="msgs cadastroEscala" >
                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                </p:commandLink>
            </div>
            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.Editar}"
                               actionListener="#{escalaRotaMB.ativarModalEdicao()}"
                               update="msgs cadastroEscala">
                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                </p:commandLink>
            </div>

            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.Pesquisar}"
                               actionListener="#{escalaRotaMB.ativarModalPesquisa()}">
                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                </p:commandLink>
            </div>

            <div style="padding-bottom: 10px;">
                <p:commandLink title="#{localemsgs.LimparFiltros}"
                               action="#{escalaRotaMB.limparPesquisa()}"
                               update="main msgs">
                    <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                </p:commandLink>
            </div>
        </p:panel>
    </h:form>

</ui:composition>
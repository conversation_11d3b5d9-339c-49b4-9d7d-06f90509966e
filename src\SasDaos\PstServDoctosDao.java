/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PstServDoctos;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PstServDoctosDao {

    /**
     * Adiciona registros da tabela PstServDoctos
     *
     * @param PstServDoctos - estrutura de dados da classe PstServDoctos
     * @param persistencia - Conexão ao Banco
     */
    String sql;

    public void adicionaPstServDoctos(PstServDoctos pstServDoctos, Persistencia persistencia) throws Exception {
        sql = "INSERT INTO PstServDoctos (Secao, CodFil, Ordem, Descricao, Operador, <PERSON><PERSON>_<PERSON>er, <PERSON>r_<PERSON>er) "
                + "VALUES(?,?,?,?,?,?,?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pstServDoctos.getSecao());
            consulta.setBigDecimal(pstServDoctos.getCodFil());
            consulta.setBigDecimal(pstServDoctos.getOrdem());
            consulta.setString(pstServDoctos.getDescricao());
            consulta.setString(pstServDoctos.getOperador());
            consulta.setDate(DataAtual.LC2Date(pstServDoctos.getDt_Alter()));
            consulta.setString(pstServDoctos.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception(" Falha ao inserir documento em pstservdocts - " + e.getMessage());
        }
    }

    /**
     * Busca registros da tabela PstServDoctos
     *
     * @param posto - estrutura de dados da classe PstServDoctos
     * @param persistencia - Conexão ao Banco
     * @return
     * @throws Exception
     */
    public List<PstServDoctos> getPostos(PstServDoctos posto, Persistencia persistencia) throws Exception {
        sql = "SELECT * "
                + " FROM PstServDoctos "
                + " WHERE Secao=? and CodFil=? ";
        PstServDoctos pstServDoctos;
        List<PstServDoctos> lpstServDoctos = new ArrayList();

        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(posto.getSecao());
            consult.setBigDecimal(posto.getCodFil());
            consult.select();
            while (consult.Proximo()) {
                pstServDoctos = new PstServDoctos();
                pstServDoctos.setSecao(consult.getString("Secao"));
                pstServDoctos.setCodFil(consult.getBigDecimal("CodFil"));
                pstServDoctos.setOrdem(consult.getBigDecimal("Ordem"));
                pstServDoctos.setDescricao(consult.getString("Descricao"));
                pstServDoctos.setOperador(consult.getString("Operador"));
                pstServDoctos.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstServDoctos.setHr_Alter(consult.getString("Hr_Alter"));

                lpstServDoctos.add(pstServDoctos);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar documentos - " + e.getMessage());
        }

        return lpstServDoctos;
    }

    /**
     * Retorna a maior Ordem de um registros da tabela PstServDoctos
     *
     * @param pstServDoctos - estrutura de dados da classe PstServDoctos
     * @param persistencia - Conexão ao Banco
     * @return
     * @throws Exception
     */
    public BigDecimal getMaxOrdem(PstServDoctos pstServDoctos, Persistencia persistencia) throws Exception {

        sql = "SELECT isnull(MAX(Ordem),0)+1 as MaxOrdem "
                + " FROM PstServDoctos "
                + " WHERE Secao=? and CodFil=? ";

        BigDecimal maxOrdem = new BigDecimal(0);

        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(pstServDoctos.getSecao());
            consult.setBigDecimal(pstServDoctos.getCodFil());

            consult.select();
            while (consult.Proximo()) {
                maxOrdem = consult.getBigDecimal("MaxOrdem");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar maxima ordem dos documentos - " + e.getMessage());
        }

        return maxOrdem;
    }

    /**
     * Verifica se uma descricao ja existe no banco
     *
     * @param posto
     * @param persistencia - Conexão ao Banco
     * @return Boolean
     * @throws Exception
     */
    public Boolean existDescricao(PstServDoctos posto, Persistencia persistencia) throws Exception {

        sql = "SELECT * "
                + " FROM PstServDoctos "
                + " WHERE Secao=? and CodFil=? and Descricao=?";
        PstServDoctos pstServDoctos;
        List<PstServDoctos> lpstServDoctos = new ArrayList();

        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(posto.getSecao());
            consult.setBigDecimal(posto.getCodFil());
            consult.setString(posto.getDescricao());

            consult.select();
            while (consult.Proximo()) {
                pstServDoctos = new PstServDoctos();
                pstServDoctos.setSecao(consult.getString("Secao"));
                pstServDoctos.setCodFil(consult.getBigDecimal("CodFil"));
                pstServDoctos.setOrdem(consult.getBigDecimal("Ordem"));
                pstServDoctos.setDescricao(consult.getString("Descricao"));
                pstServDoctos.setOperador(consult.getString("Operador"));
                pstServDoctos.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                pstServDoctos.setHr_Alter(consult.getString("Hr_Alter"));

                lpstServDoctos.add(pstServDoctos);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar documentos - " + e.getMessage());
        }

        return !lpstServDoctos.isEmpty();
    }

    /**
     * Exclui um codumtno no banco de dados
     *
     * @param pstServDoctos - estrutura de dados da classe PstServDoctos
     * @param persistencia - Conexão ao Banco
     * @throws Exception
     */
    public void excluirDocumento(PstServDoctos pstServDoctos, Persistencia persistencia) throws Exception {

        sql = "DELETE "
                + " FROM PstServDoctos "
                + " WHERE Secao=? and CodFil=? and Ordem=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(pstServDoctos.getSecao());
            consulta.setBigDecimal(pstServDoctos.getCodFil());
            consulta.setBigDecimal(pstServDoctos.getOrdem());

            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception(" Falha ao excluir documento - " + e.getMessage());
        }
    }
}

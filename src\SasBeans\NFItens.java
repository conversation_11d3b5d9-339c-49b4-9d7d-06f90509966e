/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class NFItens {

    private BigDecimal Praca;
    private String Serie;
    private BigDecimal Numero;
    private String TipoPosto;
    private String Descricao;
    private BigDecimal Qtde;
    private BigDecimal Valor_Un;
    private BigDecimal Valor_Tot;
    private BigDecimal CodPro;
    private String CFOP;
    private BigDecimal T_Tribut;
    private BigDecimal BaseICMS;
    private BigDecimal AliqICMS;
    private BigDecimal ICMS;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;

    public BigDecimal getPraca() {
        return Praca;
    }

    public void setPraca(BigDecimal Praca) {
        this.Praca = Praca;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public BigDecimal getNumero() {
        return Numero;
    }

    public void setNumero(BigDecimal Numero) {
        this.Numero = Numero;
    }

    public String getTipoPosto() {
        return TipoPosto;
    }

    public void setTipoPosto(String TipoPosto) {
        this.TipoPosto = TipoPosto;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public BigDecimal getQtde() {
        return Qtde;
    }

    public void setQtde(BigDecimal Qtde) {
        this.Qtde = Qtde;
    }

    public BigDecimal getValor_Un() {
        return Valor_Un;
    }

    public void setValor_Un(BigDecimal Valor_Un) {
        this.Valor_Un = Valor_Un;
    }

    public BigDecimal getValor_Tot() {
        return Valor_Tot;
    }

    public void setValor_Tot(BigDecimal Valor_Tot) {
        this.Valor_Tot = Valor_Tot;
    }

    public BigDecimal getCodPro() {
        return CodPro;
    }

    public void setCodPro(BigDecimal CodPro) {
        this.CodPro = CodPro;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public BigDecimal getT_Tribut() {
        return T_Tribut;
    }

    public void setT_Tribut(BigDecimal T_Tribut) {
        this.T_Tribut = T_Tribut;
    }

    public BigDecimal getBaseICMS() {
        return BaseICMS;
    }

    public void setBaseICMS(BigDecimal BaseICMS) {
        this.BaseICMS = BaseICMS;
    }

    public BigDecimal getAliqICMS() {
        return AliqICMS;
    }

    public void setAliqICMS(BigDecimal AliqICMS) {
        this.AliqICMS = AliqICMS;
    }

    public BigDecimal getICMS() {
        return ICMS;
    }

    public void setICMS(BigDecimal ICMS) {
        this.ICMS = ICMS;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

}

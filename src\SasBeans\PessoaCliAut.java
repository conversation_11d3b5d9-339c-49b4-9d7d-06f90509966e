/*
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class PessoaCliAut {

    private BigDecimal Codigo;
    private String CodFil;
    private String CodCli;
    private String NomeCli;
    private String Operador;
    private String Dt_Alter;
    private String Hr_Alter;
    private String Flag_Excl;

    private String CodGrupo;
    private String Agencia;
    private String SubAgencia;

    private String CodCofre;

    private String Nome;
    private String Email;
    
    private String Pwweb;
    
    private String CliDst;

    public PessoaCliAut() {
        Flag_Excl = "";
    }

    public BigDecimal getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        try {
            this.Codigo = new BigDecimal(Codigo);
        } catch (Exception e) {
            this.Codigo = BigDecimal.ZERO;
        }
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getCodCli() {
        return CodCli;
    }

    public void setCodCli(String CodCli) {
        this.CodCli = CodCli;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getNomeCli() {
        return NomeCli;
    }

    public void setNomeCli(String NomeCli) {
        this.NomeCli = NomeCli;
    }

    public String getFlag_Excl() {
        return Flag_Excl;
    }

    public void setFlag_Excl(String Flag_Excl) {
        this.Flag_Excl = Flag_Excl;
    }

    public String getCodGrupo() {
        return CodGrupo;
    }

    public void setCodGrupo(String CodGrupo) {
        this.CodGrupo = CodGrupo;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getSubAgencia() {
        return SubAgencia;
    }

    public void setSubAgencia(String SubAgencia) {
        this.SubAgencia = SubAgencia;
    }

    public String getCodCofre() {
        return CodCofre;
    }

    public void setCodCofre(String CodCofre) {
        this.CodCofre = CodCofre;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public String getPwweb() {
        return Pwweb;
    }

    public void setPwweb(String Pwweb) {
        this.Pwweb = Pwweb;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 61 * hash + Objects.hashCode(this.Codigo);
        hash = 61 * hash + Objects.hashCode(this.CodFil);
        hash = 61 * hash + Objects.hashCode(this.CodCli);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PessoaCliAut other = (PessoaCliAut) obj;
        if (!Objects.equals(this.CodFil, other.CodFil)) {
            return false;
        }
        if (!Objects.equals(this.CodCli, other.CodCli)) {
            return false;
        }
        if (!Objects.equals(this.Codigo, other.Codigo)) {
            return false;
        }
        return true;
    }

    /**
     * @return the CliDst
     */
    public String getCliDst() {
        return CliDst;
    }

    /**
     * @param CliDst the CliDst to set
     */
    public void setCliDst(String CliDst) {
        this.CliDst = CliDst;
    }
}

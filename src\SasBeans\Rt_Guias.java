package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Rt_Guias {

    private BigDecimal Sequencia;
    private int Parada;
    private String codCli1;
    private String codCli2;
    private BigDecimal Guia;
    private String Serie;
    private String SerieAnt;
    private BigDecimal CodFil;
    private BigDecimal Valor;
    private String valorExtenso;
    private BigDecimal OS;
    private BigDecimal KM;
    private BigDecimal KMTerra;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    private String Observ;
    private String tipoSrv;
    private String er;
    private String horaChegada;
    private String horaSaida;
    private String hora1;
    private String hora2;

    private String razaoSocial;
    private String enderecoFilial;
    private String bairroFilial;
    private String cidadeFilial;
    private String ufFilial;
    private String cepFilial;
    private String cnpjFilial;
    private String foneFilial;

    private String agencia;
    private String agenciaOri;
    private String subAgenciaOri;
    private String nRedOri;
    private String nomeOri;
    private String registroOri;
    private String endOri;
    private String cidadeOri;
    private String bairroOri;
    private String estadoOri;
    private String emailOri;
    private String veiculoOri;
    private String rotaOri;
    private String coletaOri;
    private String CCustoOS;

    private String agenciaDst;
    private String subAgenciaDst;
    private String nRedDst;
    private String nomeDst;
    private String registroDst;
    private String codCliDst;
    private String endDst;
    private String cidadeDst;
    private String bairroDst;
    private String estadoDst;
    private String emailDst;
    private String cepDst;
    private String veiculoDst;
    private String rotaDst;
    private String coletaDst;

    private String nRedFat;
    private String codCliFat;
    private String nomeFat;
    private String endFat;
    private String cidadeFat;
    private String bairroFat;
    private String estadoFat;
    private String cgcFat;
    private String ieFat;
    private String emailFat;

    private String cliCxf;
    private String emailCxf;

    private String autenticacao;
    private String codBarras;
    private String AssRemetente;
    private String AssDestinatario;
    private List<CxFGuiasVol> lacres;

    private Manifest manifest;
    private String ManifestItemLacre;
    private String ManifestItemValor;

    private String Solicitante;
    private String Moeda;
    
    private String GTVeProtocolo;
    private String GTVeChave;
    private String GTVeLink;
    private String GTVeData;
    private String GTVeHora;
    private String guiaString;
    private String AssRemetenteImagem;
    private String AssDestinatarioImagem;

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 79 * hash + Objects.hashCode(this.Sequencia);
        hash = 79 * hash + this.Parada;
        hash = 79 * hash + Objects.hashCode(this.Guia);
        hash = 79 * hash + Objects.hashCode(this.Serie);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Rt_Guias other = (Rt_Guias) obj;
        if (this.Parada != other.Parada) {
            return false;
        }
        if (!Objects.equals(this.Serie, other.Serie)) {
            return false;
        }
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        if (!Objects.equals(this.Guia, other.Guia)) {
            return false;
        }
        return true;
    }

    public String getObserv() {
        return Observ;
    }

    public void setObserv(String Observ) {
        this.Observ = Observ;
    }

    public String getCodCliFat() {
        return codCliFat;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getEnderecoFilial() {
        return enderecoFilial;
    }

    public void setEnderecoFilial(String enderecoFilial) {
        this.enderecoFilial = enderecoFilial;
    }

    public String getBairroFilial() {
        return bairroFilial;
    }

    public void setBairroFilial(String bairroFilial) {
        this.bairroFilial = bairroFilial;
    }

    public String getCidadeFilial() {
        return cidadeFilial;
    }

    public void setCidadeFilial(String cidadeFilial) {
        this.cidadeFilial = cidadeFilial;
    }

    public String getUfFilial() {
        return ufFilial;
    }

    public void setUfFilial(String ufFilial) {
        this.ufFilial = ufFilial;
    }

    public String getCepFilial() {
        return cepFilial;
    }

    public void setCepFilial(String cepFilial) {
        this.cepFilial = cepFilial;
    }

    public String getCnpjFilial() {
        return cnpjFilial;
    }

    public void setCnpjFilial(String cnpjFilial) {
        this.cnpjFilial = cnpjFilial;
    }

    public String getFoneFilial() {
        return foneFilial;
    }

    public void setFoneFilial(String foneFilial) {
        this.foneFilial = foneFilial;
    }

    public void setCodCliFat(String codCliFat) {
        this.codCliFat = codCliFat;
    }

    public String getCodCli1() {
        return codCli1;
    }

    public void setCodCli1(String codCli1) {
        this.codCli1 = codCli1;
    }

    public String getCodCli2() {
        return codCli2;
    }

    public String getTipoSrv() {
        return tipoSrv;
    }

    public void setTipoSrv(String tipoSrv) {
        this.tipoSrv = tipoSrv;
    }

    public void setCodCli2(String codCli2) {
        this.codCli2 = codCli2;
    }

    public String getEmailFat() {
        return emailFat;
    }

    public void setEmailFat(String emailFat) {
        this.emailFat = null == emailFat ? "" : emailFat;
    }

    public String getEmailOri() {
        return emailOri;
    }

    public void setEmailOri(String emailOri) {
        this.emailOri = null == emailOri ? "" : emailOri;
    }

    public String getEmailDst() {
        return emailDst;
    }

    public void setEmailDst(String emailDst) {
        this.emailDst = null == emailDst ? "" : emailDst;
    }

    public String getCgcFat() {
        return cgcFat;
    }

    public void setCgcFat(String cgcFat) {
        this.cgcFat = null == cgcFat ? "" : cgcFat;
    }

    public String getIeFat() {
        return ieFat;
    }

    public void setIeFat(String ieFat) {
        this.ieFat = null == ieFat ? "" : ieFat;
    }

    public String getEr() {
        return er;
    }

    public void setEr(String er) {
        this.er = null == er ? "" : er;
    }

    public String getnRedOri() {
        return nRedOri;
    }

    public String getCepDst() {
        return cepDst;
    }

    public void setCepDst(String cepDst) {
        this.cepDst = null == cepDst ? "" : cepDst;
    }

    public void setnRedOri(String nRedOri) {
        this.nRedOri = null == nRedOri ? "" : nRedOri;
    }

    public String getEndOri() {
        return endOri;
    }

    public void setEndOri(String endOri) {
        this.endOri = null == endOri ? "" : endOri;
    }

    public String getCidadeOri() {
        return cidadeOri;
    }

    public void setCidadeOri(String cidadeOri) {
        this.cidadeOri = null == cidadeOri ? "" : cidadeOri;
    }

    public String getBairroOri() {
        return bairroOri;
    }

    public void setBairroOri(String bairroOri) {
        this.bairroOri = null == bairroOri ? "" : bairroOri;
    }

    public String getEstadoOri() {
        return estadoOri;
    }

    public void setEstadoOri(String estadoOri) {
        this.estadoOri = null == estadoOri ? "" : estadoOri;
    }

    public String getBairroDst() {
        return bairroDst;
    }

    public void setBairroDst(String bairroDst) {
        this.bairroDst = null == bairroDst ? "" : bairroDst;
    }

    public String getEstadoDst() {
        return estadoDst;
    }

    public void setEstadoDst(String estadoDst) {
        this.estadoDst = null == estadoDst ? "" : estadoDst;
    }

    public String getnRedDst() {
        return nRedDst;
    }

    public void setnRedDst(String nRedDst) {
        this.nRedDst = null == nRedDst ? "" : nRedDst;
    }

    public String getEndDst() {
        return endDst;
    }

    public void setEndDst(String endDst) {
        this.endDst = null == endDst ? "" : endDst;
    }

    public String getCidadeDst() {
        return cidadeDst;
    }

    public void setCidadeDst(String cidadeDst) {
        this.cidadeDst = null == cidadeDst ? "" : cidadeDst;
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        try {
            this.Sequencia = new BigDecimal(Sequencia);
        } catch (Exception e) {
            this.Sequencia = new BigDecimal("0");
        }
    }

    public int getParada() {
        return Parada;
    }

    public void setParada(int Parada) {
        this.Parada = Parada;
    }

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        try {
            this.Guia = new BigDecimal(Guia);
        } catch (Exception e) {
            this.Guia = new BigDecimal("0");
        }
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = null == Serie ? "" : Serie;
    }

    public String getSerieAnt() {
        return SerieAnt;
    }

    public void setSerieAnt(String SerieAnt) {
        this.SerieAnt = null == SerieAnt ? "" : SerieAnt;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        try {
            this.Valor = new BigDecimal(Valor);
        } catch (Exception e) {
            this.Valor = new BigDecimal("0");
        }
    }

    public BigDecimal getOS() {
        return OS;
    }

    public void setOS(String OS) {
        try {
            this.OS = new BigDecimal(OS);
        } catch (Exception e) {
            this.OS = new BigDecimal("0");
        }
    }

    public BigDecimal getKM() {
        return KM;
    }

    public void setKM(String KM) {
        try {
            this.KM = new BigDecimal(KM);
        } catch (Exception e) {
            this.KM = new BigDecimal("0");
        }
    }

    public BigDecimal getKMTerra() {
        return KMTerra;
    }

    public void setKMTerra(String KMTerra) {
        try {
            this.KMTerra = new BigDecimal(KMTerra);
        } catch (Exception e) {
            this.KMTerra = new BigDecimal("0");
        }
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = null == Operador ? "" : Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = null == Hr_Alter ? "" : Hr_Alter;
    }

    public String getnRedFat() {
        return nRedFat;
    }

    public void setnRedFat(String nRedFat) {
        this.nRedFat = null == nRedFat ? "" : nRedFat;
    }

    public String getEndFat() {
        return endFat;
    }

    public void setEndFat(String endFat) {
        this.endFat = null == endFat ? "" : endFat;
    }

    public String getCidadeFat() {
        return cidadeFat;
    }

    public void setCidadeFat(String cidadeFat) {
        this.cidadeFat = null == cidadeFat ? "" : cidadeFat;
    }

    public String getBairroFat() {
        return bairroFat;
    }

    public void setBairroFat(String bairroFat) {
        this.bairroFat = null == bairroFat ? "" : bairroFat;
    }

    public String getEstadoFat() {
        return estadoFat;
    }

    public void setEstadoFat(String estadoFat) {
        this.estadoFat = null == estadoFat ? "" : estadoFat;
    }

    public String getHoraChegada() {
        return horaChegada;
    }

    public void setHoraChegada(String horaChegada) {
        this.horaChegada = null == horaChegada ? "" : horaChegada;
    }

    public String getHoraSaida() {
        return horaSaida;
    }

    public void setHoraSaida(String horaSaida) {
        this.horaSaida = null == horaSaida ? "" : horaSaida;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = null == hora1 ? "" : hora1;
    }

    public String getHora2() {
        return hora2;
    }

    public void setHora2(String hora2) {
        this.hora2 = null == hora2 ? "" : hora2;
    }

    public String getVeiculoOri() {
        return veiculoOri;
    }

    public void setVeiculoOri(String veiculoOri) {
        this.veiculoOri = null == veiculoOri ? "" : veiculoOri;
    }

    public String getRotaOri() {
        return rotaOri;
    }

    public void setRotaOri(String rotaOri) {
        this.rotaOri = null == rotaOri ? "" : rotaOri;
    }

    public String getColetaOri() {
        return coletaOri;
    }

    public void setColetaOri(String coletaOri) {
        this.coletaOri = null == coletaOri ? "" : coletaOri;
    }

    public String getVeiculoDst() {
        return veiculoDst;
    }

    public void setVeiculoDst(String veiculoDst) {
        this.veiculoDst = null == veiculoDst ? "" : veiculoDst;
    }

    public String getRotaDst() {
        return rotaDst;
    }

    public void setRotaDst(String rotaDst) {
        this.rotaDst = null == rotaDst ? "" : rotaDst;
    }

    public String getColetaDst() {
        return coletaDst;
    }

    public void setColetaDst(String coletaDst) {
        this.coletaDst = null == coletaDst ? "" : coletaDst;
    }

    public String getNomeFat() {
        return nomeFat;
    }

    public void setNomeFat(String nomeFat) {
        this.nomeFat = nomeFat;
    }

    public String getCliCxf() {
        return cliCxf;
    }

    public void setCliCxf(String cliCxf) {
        this.cliCxf = cliCxf;
    }

    public String getEmailCxf() {
        return emailCxf;
    }

    public void setEmailCxf(String emailCxf) {
        this.emailCxf = emailCxf;
    }

    public String getCodCliDst() {
        return codCliDst;
    }

    public void setCodCliDst(String codCliDst) {
        this.codCliDst = codCliDst;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getValorExtenso() {
        return valorExtenso;
    }

    public void setValorExtenso(String valorExtenso) {
        this.valorExtenso = valorExtenso;
    }

    public String getAutenticacao() {
        return autenticacao;
    }

    public void setAutenticacao(String autenticacao) {
        this.autenticacao = autenticacao;
    }

    public String getAssRemetente() {
        return AssRemetente;
    }

    public void setAssRemetente(String AssRemetente) {
        this.AssRemetente = AssRemetente == null ? "" : AssRemetente;
    }

    public String getAssDestinatario() {
        return AssDestinatario;
    }

    public void setAssDestinatario(String AssDestinatario) {
        this.AssDestinatario = AssDestinatario == null ? "" : AssDestinatario;
    }

    public List<CxFGuiasVol> getLacres() {
        return lacres;
    }

    public void setLacres(List<CxFGuiasVol> lacres) {
        this.lacres = lacres;
    }

    public String getCodBarras() {
        return codBarras;
    }

    public void setCodBarras(String codBarras) {
        this.codBarras = codBarras;
    }

    public String getAgenciaOri() {
        return agenciaOri;
    }

    public void setAgenciaOri(String agenciaOri) {
        this.agenciaOri = agenciaOri;
    }

    public String getSubAgenciaOri() {
        return subAgenciaOri;
    }

    public void setSubAgenciaOri(String subAgenciaOri) {
        this.subAgenciaOri = subAgenciaOri;
    }

    public String getAgenciaDst() {
        return agenciaDst;
    }

    public void setAgenciaDst(String agenciaDst) {
        this.agenciaDst = agenciaDst;
    }

    public String getSubAgenciaDst() {
        return subAgenciaDst;
    }

    public void setSubAgenciaDst(String subAgenciaDst) {
        this.subAgenciaDst = subAgenciaDst;
    }

    public String getNomeOri() {
        return nomeOri;
    }

    public void setNomeOri(String nomeOri) {
        this.nomeOri = nomeOri;
    }

    public String getRegistroOri() {
        return registroOri;
    }

    public void setRegistroOri(String registroOri) {
        this.registroOri = registroOri;
    }

    public Manifest getManifest() {
        return manifest;
    }

    public void setManifest(Manifest manifest) {
        this.manifest = manifest;
    }

    public String getManifestItemLacre() {
        return ManifestItemLacre;
    }

    public void setManifestItemLacre(String ManifestItemLacre) {
        this.ManifestItemLacre = ManifestItemLacre;
    }

    public String getManifestItemValor() {
        return ManifestItemValor;
    }

    public void setManifestItemValor(String ManifestItemValor) {
        this.ManifestItemValor = ManifestItemValor;
    }

    public String getNomeDst() {
        return nomeDst;
    }

    public void setNomeDst(String nomeDst) {
        this.nomeDst = nomeDst;
    }

    public String getRegistroDst() {
        return registroDst;
    }

    public void setRegistroDst(String registroDst) {
        this.registroDst = registroDst;
    }

    public String getSolicitante() {
        return Solicitante;
    }

    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    public String getMoeda() {
        return Moeda;
    }

    public void setMoeda(String Moeda) {
        this.Moeda = Moeda;
    }

    public String getGTVeProtocolo() {
        return GTVeProtocolo;
    }

    public void setGTVeProtocolo(String GTVeProtocolo) {
        this.GTVeProtocolo = GTVeProtocolo;
    }

    public String getGTVeChave() {
        return GTVeChave;
    }

    public void setGTVeChave(String GTVeChave) {
        this.GTVeChave = GTVeChave;
    }

    public String getGTVeLink() {
        return GTVeLink;
    }

    public void setGTVeLink(String GTVeLink) {
        this.GTVeLink = GTVeLink;
    }

    public String getGTVeData() {
        return GTVeData;
    }

    public void setGTVeData(String GTVeData) {
        this.GTVeData = GTVeData;
    }

    public String getGTVeHora() {
        return GTVeHora;
    }

    public void setGTVeHora(String GTVeHora) {
        this.GTVeHora = GTVeHora;
    }

    public String getGuiaString() {
        return guiaString;
    }

    public void setGuiaString(String guiaString) {
        this.guiaString = guiaString;
    }

    public String getAssRemetenteImagem() {
        return AssRemetenteImagem;
    }

    public void setAssRemetenteImagem(String AssRemetenteImagem) {
        this.AssRemetenteImagem = AssRemetenteImagem;
    }

    public String getAssDestinatarioImagem() {
        return AssDestinatarioImagem;
    }

    public void setAssDestinatarioImagem(String AssDestinatarioImagem) {
        this.AssDestinatarioImagem = AssDestinatarioImagem;
    }

    /**
     * @return the CCustoOS
     */
    public String getCCustoOS() {
        return CCustoOS;
    }

    /**
     * @param CCustoOS the CCustoOS to set
     */
    public void setCCustoOS(String CCustoOS) {
        this.CCustoOS = CCustoOS;
    }
}

/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 06/03/2022, 10:41:56
    Author     : marcos
*/

body {
    background-image: linear-gradient(to right,rgba(136, 125, 125, 0.438), rgb(255, 255, 255),rgba(136, 125, 125, 0.438));

}

/* CSS do Grid */
#divCartaoFuncion{
	font-family: Tahoma, Helvetica, sans-serif;
}

#divCartaoFuncion b{
	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
	font-weight: normal;
}

.btn{
	background-color: rgb(33, 156,70);
	padding 5px 10px 5px 10px;
	border: 2px solid rgb(4, 92, 30);
	cursor: pointer;
	transition: background 0.3s;
	border-radius: 5px;
	color: #fff;
}
.btn:hover{
	background-color: rgb(4, 92, 30);
	padding 5px 10px 5px 10px;
	border: 2px solid rgb(4, 92, 30);
	border-radius: 5px;
	color: #fff;
}
.btn:active{
	background-color: rgb(129, 255, 166);	
	color: rgb(4, 92, 30);
}
.modal{
	background-color: rgba(0, 0, 0, .8); 
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: none;
	
}
.modal-content{
   margin: 0 auto;
   margin-top: 5%;
   max-width: 400px;
   background-color: #eee;
   padding: 0;
   box-shadow: 0 0 2px #fff;
}

.modal-header, .modal-body, .modal.footer{
	padding: 10px;
}
.modal-header{
	background-color: #ccc;
}
.modal-header h4{
	font-weight: bold;
	text-align: center;
}
.modal-footer{
	background-color: #ccc;
	display : flex;
	justify-content: center;
	align-items : center;
}


.painelGridBatidas {
  margin-top: 2%;
  border: 2px solid rgb(73, 73, 73) !important;
  border-radius: 15px;
  background-color: white;
  padding: 0 2%;
  box-shadow: 0px 0px 5px #918e8e00;
  color:#112506;
  width: 100%;
  max-width: 500px;
  font-family: Tahoma, Helvetica, sans-serif;
  background-image: linear-gradient(to right,rgba(255, 255, 255) rgb(255, 255, 255),rgba(255, 255, 255));
}
.divGridBatidas-Cab{
  text-align: center;
  color: #000000;
  background-color: white !important;
}

.scrollBoxBatidas{
  margin-top: 1%;
  overflow: hidden; 
  overflow-y: scroll; 
  padding: 1% 1%;
  height: auto;
  overflow-x: auto;
}

.divGridBatidas-Corpo{
  margin-bottom: 2%;
  display: flex; 
  flex-direction: column;
  justify-content: center; 
 
}
.divGridBatidas-Corpo table { 
  border-collapse: collapse;
}
.divGridBatidas-Corpo td{
  padding: 1.7% 1.7%; 
  font-family: Tahoma, Helvetica, sans-serif;
  font-size: 14px;
  text-align: center;
}
.divGridBatidas-tit{
  background-color: #B0C4DE;
  font-family: Tahoma, Helvetica, sans-serif;
  font-size: 13px;
 
  
}
table th  {
  padding: 1px;
  font-size: 14px;
  border: 1px solid;
  font-weight: 100;
}
table td {
  padding: 0x;
  border-left: 1px solid;
  border-right: 1px solid;
  line-height: 2px;
  
}


.modalx {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  font-family: Arial, Helvetica, sans-serif;
  background: rgba(0,0,0,0.8);
  z-index: 99999;
  opacity:0;
  -webkit-transition: opacity 400ms ease-in;
  -moz-transition: opacity 400ms ease-in;
  transition: opacity 400ms ease-in;
  pointer-events: none;
}

.imgLogo {
    text-align: center;
    margin-top: 2%;
}

.acesso{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 5%;

}



.acesso input{
    margin-bottom: 10px;
    padding: 7px;
    border-radius: 12px;
    border-width: 0px;

	
}

#sectionFoto{
    justify-content: center;
    align-items: stretch
    text-align: center;
    margin-top: 1%;
    border: 1px solid;
	max-width: 500px;
	border-radius: 15px;
}

.imgLogob img{
    
    margin: auto;
}

#nav-home {
    background-color: #002172;
  }
  
  nav #container-top{
    display: flex;
    align-items: center;
    justify-content: center;
  
    width: 100%;

    margin: auto;
  }

  nav #container-top img {
    width: 150px;
    margin-left: 20px;
    padding: 15px 0;
  }


  nav ul {
    display: flex;
  }

  nav ul li {
    list-style: none;
  }
  
  nav ul li a {
    text-decoration: none;
    color: white;
    text-transform: uppercase;
    font-size: 1.4rem; 
    padding: 2.4rem;
  }

  @media only screen and (max-width: 440px){

    .acesso{
        margin-top: 2%;
    
    }
    .imgLogob{
          margin-top: 2%;
      }
  .divGridBatidas-Corpo td{
  padding: 1.7% 1.7%; 
  font-family: Tahoma, Helvetica, sans-serif;
  font-size: 14px;
  text-align: center;
  }
  }
  
  
  
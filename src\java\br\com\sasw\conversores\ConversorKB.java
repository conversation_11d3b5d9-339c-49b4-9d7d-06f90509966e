/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.conversores;

import java.math.BigDecimal;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("conversorKB")
public class ConversorKB implements Converter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        return value;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            BigDecimal valorB = new BigDecimal(value.toString());
            return valorB.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_HALF_EVEN).toString() + "KB";
        } catch (Exception e) {
            return value.toString();
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2206;
import SasBeans.ESocial.S2206.AltContratual;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato.Duracao;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato.HorContratual;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato.HorContratual.Horario;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato.LocalTrabalho;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato.LocalTrabalho.LocalTrabGeral;
import SasBeans.ESocial.S2206.AltContratual.InfoContrato.Remuneracao;
import SasBeans.ESocial.S2206.AltContratual.InfoRegimeTrab;
import SasBeans.ESocial.S2206.AltContratual.InfoRegimeTrab.InfoCeletista;
import SasBeans.ESocial.S2206.AltContratual.Vinculo;
import SasBeans.ESocial.S2206.IdeEmpregador;
import SasBeans.ESocial.S2206.IdeEvento;
import SasBeans.ESocial.S2206.IdeVinculo;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2206Dao {

    public List<S2206> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select \n"
                    + "1 ideEmpregador_tpInsc, \n"
                    + "Filiais.CNPJ ideEmpregador_nrInsc,\n"
                    + "Funcion.CPF ideVinculo_cpfTrab,\n"
                    + "Funcion.PIS ideVinculo_nisTrab,\n"
                    + "Funcion.Matr ideVinculo_matricula,\n"
                    //+ " case when Funcion.CodPonto = '' then "
                    //+ " Funcion.Matr  else Funcion.CodPonto "
                    //+ "end ideVinculo_matricula,\n"                    
                    + "Convert(date,FuncionCargos.Data) altContratual_dtAlteracao,\n"
                    + "Case when FuncionCargos.Motivo <> '' then Convert(date,FuncionCargos.Data) else NULL end altContratual_dtEf,\n"
                    + "FuncionCargos.Motivo altContratual_dscAlt, Cargos.Descricao infoContrato_nmCargo, Cargos.CBO, \n"
                    + "1 vinculo_tpRegPrev,\n"
                    + "1 infoCeletista_tpRegJor, \n"
                    + "1 infoCeletista_natAtividade,\n"
                    + "Fornec.CNPJ infoCeletista_cnpjSindCategProf,\n"
                    //+ "convert(bigint, Funcion.Cargo) infoContrato_codCargo, \n"
                    + "Case when Funcion.TrabIntermitente = 'S' then '111'\n"
                    + "       when Funcion.vinculo in ('E','J','M') then '103'\n"
                    + "       when Funcion.TipoADM = 25 then 105 \n"
                    + "       when Funcion.Vinculo in ('A') then '701'\n"
                    + "       else '101' end infoContrato_codCateg, \n"
                    + "Funcion.Salario remuneracao_vrSalFx, \n"
                    + "Funcion.FormaPgto remuneracao_undSalFixo,\n"
                    + " RHHorario.Descricao + ' ' + "
                    + " Case when RHHorario.D1 >0 then 'DOM '+RHHorario.Hora101+' '+RHHorario.Hora102+' '+RHHorario.Hora103+' '+RHHorario.Hora104+' ' else '' end +"
                    + " Case when RHHorario.D2 >0 then 'SEG '+RHHorario.Hora201+' '+RHHorario.Hora202+' '+RHHorario.Hora203+' '+RHHorario.Hora204+' ' else '' end +"
                    + " Case when RHHorario.D3 >0 then 'TER '+RHHorario.Hora301+' '+RHHorario.Hora302+' '+RHHorario.Hora303+' '+RHHorario.Hora304+' ' else '' end +"
                    + " Case when RHHorario.D4 >0 then 'QUA '+RHHorario.Hora401+' '+RHHorario.Hora402+' '+RHHorario.Hora403+' '+RHHorario.Hora404+' ' else '' end +"
                    + " Case when RHHorario.D5 >0 then 'QUI '+RHHorario.Hora501+' '+RHHorario.Hora502+' '+RHHorario.Hora503+' '+RHHorario.Hora504+' ' else '' end +"
                    + " Case when RHHorario.D6 >0 then 'SEX '+RHHorario.Hora601+' '+RHHorario.Hora602+' '+RHHorario.Hora603+' '+RHHorario.Hora604+' ' else '' end +"
                    + " Case when RHHorario.D7 >0 then 'SAB '+RHHorario.Hora701+' '+RHHorario.Hora702+' '+RHHorario.Hora703+' '+RHHorario.Hora704+' ' else '' end dscJorn, "
                    + "1 duracao_tpContr,\n"
                    + "1 localTrabGeral_tpInsc,\n"
                    + "Clientes.CGC localTrabGeral_nrInsc,\n"
                    + "RHHorario.tipocomp, RHHorario.chcomp, RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, RHEscala.Descricao HorContratual_dscTpJorn,   \n"
                    + "RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, \n"
                    + "RHEscala.HrSabNot, RHEscala.HrDomDiu, RHEscala.HrDomNot, \n"
                    + "0 horContratual_tmpParc, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, \n"
                    + "RHHorario.D1, RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7,                 \n"
                    + "                     (select max(sucesso) from  ( \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n"
                    + "                             From XmleSocial z \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2206' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%aguardando%' \n"
                    + "                                         or z.Xml_Retorno = ''\n"
                    + "                                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))\n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2206' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%<ocorrencia>%' \n"
                    + "                                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2206' \n"
                    + "                                  and z.CodFil = ? \n"
                    + "                                  and z.Compet = ? \n"
                    + "                                  and z.Ambiente = ? \n"
                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'\n"
                    + "                                      or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso \n"
                    + "from FuncionCargos\n"
                    + "Left join Filiais  on Filiais.CodFil = FuncionCargos.CodFil\n"
                    + "Left join Funcion  on Funcion.Matr = FuncionCargos.Matr\n"
                    + "Left join Cargos  on Cargos.Cargo = Funcion.Cargo \n"
                    + "Left join Sindicatos  on Sindicatos.Codigo = Funcion.Sindicato \n"
                    + "Left join Fornec on Fornec.Codigo = Sindicatos.CodForn\n"
                    + "Left Join PstServ  on Funcion.Secao  = PstServ.Secao \n"
                    + "                   and Funcion.CodFil = PstServ.CodFil \n"
                    + "Left Join Clientes  on Clientes.Codigo = PstServ.CodCli \n"
                    + "                    and Clientes.CodFil = PstServ.CodFil \n"
                    + "Left Join RHEscala  on RHEscala.Codigo = Funcion.Escala \n"
                    + "                    and RHEscala.CodFil = Funcion.CodFil \n"
                    + "Left Join RHHorario  on  RHHorario.Codigo    = Funcion.Horario \n"
                    + "                    and RHHorario.CodFil    = Funcion.CodFil \n"
                    + "where FuncionCargos.CodFil = ?\n"
                    + "  and Substring(Replace(convert(Varchar,FuncionCargos.Data,111),'/','-'),1,7) = ?\n"
                    + "  and Funcion.Vinculo not in ('D','E','S','A') "
                    + "  and FuncionCargos.Motivo <> 'ADMISSAO'\n"
                    + "  and FuncionCargos.Motivo <> 'INICIO DE CONTRATO'";
//                    + " union "
//                    + "Select \n"
//                    + "	1 ideEmpregador_tpInsc, \n"
//                    + "	Filiais.CNPJ ideEmpregador_nrInsc,\n"
//                    + "	Funcion.CPF ideVinculo_cpfTrab,\n"
//                    + "	Funcion.PIS ideVinculo_nisTrab,\n"
//                    + "	Funcion.Matr ideVinculo_matricula,\n"
//                    + "	Convert(date,RhTransf.Data) altContratual_dtAlteracao,\n"
//                    + "	'' altContratual_dtEf,\n"
//                    + "	'' altContratual_dscAlt, \n"
//                    + "	1 vinculo_tpRegPrev,\n"
//                    + "	1 infoCeletista_tpRegJor, \n"
//                    + "	1 infoCeletista_natAtividade,\n"
//                    + "	Fornec.CNPJ infoCeletista_cnpjSindCategProf,\n"
//                    + "	convert(bigint, Funcion.Cargo) infoContrato_codCargo, \n"
//                    + "	Case when Funcion.TrabIntermitente = 'S' then '111'\n"
//                    + "			when Funcion.vinculo in ('E','J','M') then '103'\n"
//                    + "			when Funcion.Vinculo in ('A') then '701'\n"
//                    + "			else '101' end infoContrato_codCateg, \n"
//                    + "	Funcion.Salario remuneracao_vrSalFx, \n"
//                    + "	Funcion.FormaPgto remuneracao_undSalFixo,\n"
//                    + "	1 duracao_tpContr,\n"
//                    + "	1 localTrabGeral_tpInsc,\n"
//                    + "	Clientes.CGC localTrabGeral_nrInsc,\n"
//                    + "	RHHorario.tipocomp, RHHorario.chcomp, RHEscala.DiasTrbDiu, RHEscala.DiasTrbNot, \n"
//                    + "	RHEscala.DiasFolga, RHEscala.HrDUDiu, RHEscala.HrDUNot, RHEscala.HrSabDiu, \n"
//                    + "	RHEscala.HrSabNot, RHEscala.HrDomDiu, RHEscala.HrDomNot, \n"
//                    + "	0 horContratual_tmpParc, Convert(bigint,((RhHorario.CodFil*10000)+RHHorario.Codigo)) horario_codHorContrat, \n"
//                    + "	RHHorario.D1, RHHorario.D2, RHHorario.D3, RHHorario.D4, RHHorario.D5, RHHorario.D6, RHHorario.D7,                \n"
//                    + "                     (select max(sucesso) from  ( \n"
//                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n"
//                    + "                             From XmleSocial z \n"
//                    + "                             where z.Identificador = Funcion.Matr \n"
//                    + "                                 and z.evento = 'S-2206' \n"
//                    + "                                  and z.CodFil = ? \n"
//                    + "                                  and z.Compet = ? \n"
//                    + "                                  and z.Ambiente = ? \n"
//                    + "                                 and (z.Xml_Retorno like '%aguardando%' \n"
//                    + "                                         or z.Xml_Retorno = ''\n"
//                    + "                                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))\n"
//                    + "                     union \n"
//                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n"
//                    + "                             From XmleSocial z  \n"
//                    + "                             where z.Identificador = Funcion.Matr \n"
//                    + "                                 and z.evento = 'S-2206' \n"
//                    + "                                  and z.CodFil = ? \n"
//                    + "                                  and z.Compet = ? \n"
//                    + "                                  and z.Ambiente = ? \n"
//                    + "                                 and (z.Xml_Retorno like '%<ocorrencia>%' \n"
//                    + "                                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
//                    + "                     union \n"
//                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n"
//                    + "                             From XmleSocial z  \n"
//                    + "                             where z.Identificador = Funcion.Matr \n"
//                    + "                                 and z.evento = 'S-2206' \n"
//                    + "                                  and z.CodFil = ? \n"
//                    + "                                  and z.Compet = ? \n"
//                    + "                                  and z.Ambiente = ? \n"
//                    + "                                 and (z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%'\n"
//                    + "                                      or Xml_Retorno like '%<processamento><cdResposta>202</cdResposta>%') )) a) sucesso \n"                    
//                    + "	from RhTransf\n"
//                    + "	Left join Filiais  on Filiais.CodFil = RhTransf.CodFil\n"
//                    + "	Left join Funcion  on Funcion.Matr = RhTransf.Matr\n"
//                    + "	Left join Sindicatos  on Sindicatos.Codigo = Funcion.Sindicato \n"
//                    + "	Left join Fornec on Fornec.Codigo = Sindicatos.CodForn\n"
//                    + "	Left Join PstServ  on Funcion.Secao  = PstServ.Secao \n"
//                    + "						and Funcion.CodFil = PstServ.CodFil \n"
//                    + "	Left Join Clientes  on Clientes.Codigo = PstServ.CodCli \n"
//                    + "						and Clientes.CodFil = PstServ.CodFil \n"
//                    + "	Left Join RHEscala  on RHEscala.Codigo = Funcion.Escala \n"
//                    + "						and RHEscala.CodFil = Funcion.CodFil \n"
//                    + "	Left Join RHHorario  on  RHHorario.Codigo    = Funcion.Horario \n"
//                    + "						and RHHorario.CodFil    = Funcion.CodFil \n"
//                    + "	where RhTransf.CodFil = ? "
//                    + "		and Substring(Replace(convert(Varchar,RhTransf.Data,111),'/','-'),1,7) = ? "
//                    + "		and Funcion.Situacao <> 'D' \n"
//                    + "		and RhTransf.Matr not in (Select FuncionCargos.matr from FuncionCargos where Substring(Replace(convert(Varchar,FuncionCargos.Data,111),'/','-'),1,7) = Substring(Replace(convert(Varchar,RhTransf.Data,111),'/','-'),1,7))\n"
//                    + "		and RhTransf.CodMotivo in (1,3)";                    
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);

            consulta.setString(codFil);
            consulta.setString(compet);

//            consulta.setString(codFil);
//            consulta.setString(compet);
//            consulta.setString(ambiente);
//
//            consulta.setString(codFil);
//            consulta.setString(compet);
//            consulta.setString(ambiente);
//
//            consulta.setString(codFil);
//            consulta.setString(compet);
//            consulta.setString(ambiente);
//
//            consulta.setString(codFil);
//            consulta.setString(compet);            
            consulta.select();
            List<S2206> retorno = new ArrayList<>();
            S2206 s2206;
            while (consulta.Proximo()) {
                s2206 = new S2206();

                s2206.setSucesso(consulta.getInt("sucesso"));

                s2206.setIdeEvento(new IdeEvento());
                s2206.getIdeEvento().setIdeEvento_indRetif("1");
                s2206.getIdeEvento().setIdeEvento_procEmi("1");
                s2206.getIdeEvento().setIdeEvento_verProc("Satellite eSocial");

                s2206.setIdeEmpregador(new IdeEmpregador());
                s2206.getIdeEmpregador().setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2206.getIdeEmpregador().setIdeEmpregador_nrInsc(FuncoesString.RecortaString(consulta.getString("ideEmpregador_nrInsc"), 0, 8));

                s2206.setIdeVinculo(new IdeVinculo());
                s2206.getIdeVinculo().setIdeVinculo_cpfTrab(consulta.getString("ideVinculo_cpfTrab"));
                s2206.getIdeVinculo().setIdeVinculo_nisTrab(consulta.getString("ideVinculo_nisTrab"));
                s2206.getIdeVinculo().setIdeVinculo_matricula(consulta.getString("ideVinculo_matricula"));

                s2206.setAltContratual(new AltContratual());
                s2206.getAltContratual().setAltContratual_dtAlteracao(consulta.getString("altContratual_dtAlteracao"));
                s2206.getAltContratual().setAltContratual_dtEf(consulta.getString("altContratual_dtEf"));
                s2206.getAltContratual().setAltContratual_dscAlt(consulta.getString("altContratual_dscAlt"));

                s2206.getAltContratual().setAltContratual_vinculo(new Vinculo());
                s2206.getAltContratual().getAltContratual_vinculo().setVinculo_tpRegPrev(consulta.getString("vinculo_tpRegPrev"));

                s2206.getAltContratual().setAltContratual_infoRegimeTrab(new InfoRegimeTrab());
                s2206.getAltContratual().getAltContratual_infoRegimeTrab().setInfoRegimeTrab_infoCeletista(new InfoCeletista());
                s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista()
                        .setInfoCeletista_tpRegJor(consulta.getString("infoCeletista_tpRegJor"));

                s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista()
                        .setInfoCeletista_natAtividade(consulta.getString("infoCeletista_natAtividade"));

                s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista()
                        .setInfoCeletista_cnpjSindCategProf(consulta.getString("infoCeletista_cnpjSindCategProf"));

                s2206.getAltContratual().setAltContratual_infoContrato(new InfoContrato());
                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_nmCargo(consulta.getString("infoContrato_nmCargo"));
                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_codCateg(consulta.getString("infoContrato_codCateg"));

                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_CBO(consulta.getString("CBO"));

                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_remuneracao(new Remuneracao());
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao()
                        .setRemuneracao_vrSalFx(consulta.getString("remuneracao_vrSalFx"));
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao()
                        .setRemuneracao_undSalFixo(consulta.getString("remuneracao_undSalFixo"));

                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_duracao(new Duracao());
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_duracao().setDuracao_tpContr(consulta.getString("duracao_tpContr"));

                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_localTrabalho(new LocalTrabalho());
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho()
                        .setLocalTrabalho_localTrabGeral(new LocalTrabGeral());
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho()
                        .getLocalTrabalho_localTrabGeral().setLocalTrabGeral_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho()
                        .getLocalTrabalho_localTrabGeral().setLocalTrabGeral_nrInsc(consulta.getString("ideEmpregador_nrInsc"));

                s2206.getAltContratual().getAltContratual_infoContrato().setInfoContrato_horContratual(new HorContratual());
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual()
                        .setHorContratual_qtdHrsSem(consulta.getString("tipocomp").equals("S") ? consulta.getString("chcomp")
                                : (new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 1 ? "44"
                                : (new BigDecimal(consulta.getString("chcomp")).compareTo(new BigDecimal("180")) == 0 ? "30"
                                : (new BigDecimal(consulta.getString("chcomp")).divide(new BigDecimal("5")).toPlainString()))));
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual()
                        .setHorContratual_tpJornada(
                                ((consulta.getString("DiasTrbDiu").equals("1")
                                || consulta.getString("DiasTrbNot").equals("1"))
                                && consulta.getString("DiasFolga").equals("1")) ? "2" : "9");
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual()
                        .setHorContratual_tmpParc(consulta.getString("horContratual_tmpParc"));
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual()
                        .setHorContratual_dscTpJorn(consulta.getString("HorContratual_dscTpJorn"));
                s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual()
                        .setHorContratual_dscJorn(consulta.getString("dscJorn"));
                retorno.add(s2206);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception();
        }
    }
}

package SasBeansCompostas;

import SasBeans.ContasFin;
import SasBeans.NFiscal;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class NFiscalContasFin {

    private NFiscal nfiscal;
    private ContasFin contasfin;
    private BigDecimal ReceitaLiquida;
    private String Descricao;

    public NFiscal getNfiscal() {
        return nfiscal;
    }

    public void setNfiscal(NFiscal nfiscal) {
        this.nfiscal = nfiscal;
    }

    public ContasFin getContasfin() {
        return contasfin;
    }

    public void setContasfin(ContasFin contasfin) {
        this.contasfin = contasfin;
    }

    public BigDecimal getReceitaLiquida() {
        return ReceitaLiquida;
    }

    public void setReceitaLiquida(String ReceitaLiquida) {
        try {
            this.ReceitaLiquida = new BigDecimal(ReceitaLiquida);
        } catch (Exception e) {
            this.ReceitaLiquida = new BigDecimal("0");
        }
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }
}

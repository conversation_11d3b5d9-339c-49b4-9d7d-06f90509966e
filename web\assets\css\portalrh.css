@import url(http://fonts.googleapis.com/css?family=Lato:300,400,700,900);

.primeiroacesso{
    display: block;
    color: #000;
    text-align: center;
    width: 500px;
    background-image: url('../img/fundo_titulos.png');
    background-repeat: no-repeat;
    background-size: 100% 60px;
    margin: auto;
}

.form-control{
    min-height: 40px;
}

* {
    outline: 0 !important;
}

.ui-growl-title {
    font-weight: normal;
}

.pnl .ui-widget-content, .pnl .ui-panelgrid-cell {
    color: #022a48;
    border: none;
    font-size: 14px;
}

.pnl {
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: normal;
}

.nestedAccordion .ui-accordion-header{
    font-size: 15px;
    margin-left: 30px;
}

.accordion .ui-state-default{
    border: none;
    background: transparent;
    color: #022a48;
    background: rgba(128, 128, 240, 0.5);
    font-family: Tahoma, Geneva, sans-serif;
    font-weight: bold;
}

.accordion .ui-accordion-content{
    padding: 0px;
}

.dlg{
    top: 150px !important;
}

.dlg .ui-widget-header {
    background: #145B9B;
    color: #022a48;
    font-family: Tahoma, Geneva, sans-serif;
    font-weight: normal;
    text-shadow: 0px 0px 0px rgba(255,255,255,0.7);
}

.linkbranco:hover {
    color: #fff;
    font-weight: bold;
    text-decoration: none;
    text-align: left;
}

.linkbranco {
    padding: 0;
    margin: 0;
    text-decoration: none;
    -webkit-transition: background-color .4s linear, color .4s linear;
    -moz-transition: background-color .4s linear, color .4s linear;
    -o-transition: background-color .4s linear, color .4s linear;
    -ms-transition: background-color .4s linear, color .4s linear;
    transition: background-color .4s linear, color .4s linear;
    color: #fff;
    cursor: pointer;
    font-family: Tahoma, Geneva, sans-serif !important;
    font-size: 14px !important;
}

.linkazul:hover {
    color: #022a48;
    font-weight: bold;
    text-decoration: none;
    text-align: left;
}

.linkazul, .ui-widget-content a{
    padding: 0;
    margin: 0;
    text-decoration: none;
    -webkit-transition: background-color .4s linear, color .4s linear;
    -moz-transition: background-color .4s linear, color .4s linear;
    -o-transition: background-color .4s linear, color .4s linear;
    -ms-transition: background-color .4s linear, color .4s linear;
    transition: background-color .4s linear, color .4s linear;
    color: #022a48;
    cursor: pointer;
    font-family: Tahoma, Geneva, sans-serif !important;
    font-size: 14px !important;
}

.textoazul{
    color: #145B9B;
    font-family: Tahoma, Geneva, sans-serif !important;
    font-size: 14px !important;
}

.titulobalao {
    display: block;
    color: #145B9B;
    text-align: center;
    width: 300px;
    padding-bottom: 10px;
    margin-right: 6px;
    margin-left: 6px;
    padding-top: 5px;
    font-weight: bold;
}

h4{
    font-family: Tahoma, Geneva, sans-serif;
    text-align: justify;
    font-size: 11px;
    padding: 0px;
    margin: 0px 0px 0px 10px;
    color: #145B9B;
    padding-left: 10px;
    padding-right: 15px;
}

.faixaesquerdabalao{
	display:block;
	float:left;
        border-color: #000;
	color:#000;
	text-align:justify;
	width:300px;
	padding-bottom: 6px;
	background-repeat: no-repeat;
	margin-right: 40px;
	margin-left: 6px;
	margin-top: 0px;
	margin-bottom: 0px;
	padding-top: 65px;
	height: 425px;
	background-image: url('../img/fundo_mensagensp.png');
}

.logo{
	display:block;
	float:left;
	color:#145B9B;
	text-align:center;
	width:300px;
	padding-bottom: 6px;
	margin-right: 6px;
	margin-left: 6px;
	padding-top: 20px;
}

h2 {
    font-family: Tahoma, Geneva, sans-serif;
    font-size: 18px;
    padding: 3px;
    margin-top: 0px;
    color: #145B9B;
    padding-left: 3px;
    text-align: center;
}

.icones {
    display: block;
    float: left;
    color: #145B9B;
    text-align: center;
    padding-bottom: 6px;
    margin-right: 10px;
    margin-left: 30px;
    width: 100px;
    height: 140px;
    margin-bottom: 10px;
}

.faixadireita {
    display: block;
    float: right;
    color: #FFF;
    text-align: center;
    width: 300px;
    padding-bottom: 0px;
    margin-right: 6px;
    margin-left: 0px;
    padding-top: 50px;
}

.faixameio {
    display: block;
    float: left;
    color: #FFF;
    text-align: justify;
    width: 300px;
    padding-bottom: 6px;
    background-image: url('../img/fundo_titulos.png');
    background-repeat: no-repeat;
    margin-right: 6px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 0px;
}

a {
    padding: 0;
    margin: 0;
    text-decoration: none;
    -webkit-transition: background-color .4s linear, color .4s linear;
    -moz-transition: background-color .4s linear, color .4s linear;
    -o-transition: background-color .4s linear, color .4s linear;
    -ms-transition: background-color .4s linear, color .4s linear;
    transition: background-color .4s linear, color .4s linear;
    color: #fff;
    cursor: pointer;
    font-family: Tahoma, Geneva, sans-serif !important;
    font-size: 14px !important;
}

.col-4{
    float:left;
    width: 33%
}

.bottomrow {
    width: 960px;
    margin: 0 auto;
    height: 500px;
}

#header{
    background-image: url('../img/fundo_topo.png');
    background-repeat: no-repeat;
    height: 100px;
    border-bottom: solid 1px #fff;
}

.custom-button {
    height: 40px !important;
    width: 114px !important;
    text-shadow: none !important;
    color: #6a4f02 !important;
    background: #ebdc61 !important;
    background: -moz-linear-gradient(top, #ebdc61 0%, #ab8611 100%) !important
    background: -webkit-linear-gradient(top, #ebdc61 0%,#ab8611 100%) !important;
    background: linear-gradient(to bottom, #ebdc61 0%,#ab8611 100%) !important;
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ebdc61', endColorstr='#ab8611',GradientType=0 );
}

.rodapeinfos {
    font-size: 12px;
    line-height: 12px;
}

* { 
    margin: 0;
    padding: 0;
    font-family: Tahoma, Verdana, Segoe, sans-serif;
} 
body {
    background: #000; 
    margin: 0; 
    color: #5a5a5a;
}

html, body {
    height: 100%;
}
.alignleft {
    float: left;
}
.alignright {
    float: right;
}
.aligncenter {
    margin-left: auto;
    margin-right: auto;
    display: block;
    clear: both;
}

/*** altered 26/10/2015 ***/

#h {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 100%;
    height: auto;
    background: url('../img/bk.jpg') no-repeat bottom center fixed;
    background-size: cover;
    overflow: auto;
    /* border: 2px solid red; */
    color: #fff;
    /* background-size: cover; border: 2px solid red; */
}
.h-wrapper {
    position: relative;
    max-width: 714px;
    padding: 420px 15px 15px;
    margin: 0 auto;
    background: url('../img/st_back.png') no-repeat top center; /* */
    background-size: 62%;
}
.mt {
    position: relative;
    margin-bottom: 5px;
    /* margin-top: 450px; */
    /* padding: 0 5px; */
    font-size: 20px;
    line-height: 20px;
}
.sate-login {
    position: relative;
    /* max-width: 682px;
    text-align: center;
    margin: 0 auto; */
    margin-bottom: 10px;
    /* background: #ff0; */
}
.sate-login input[type=text],
.sate-login input[type=password] {
    width: 280px;
}

.rodape-wrapper {
    background: #328fca;
    background: -moz-linear-gradient(top,  #328fca 0%, #16244a 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#328fca), color-stop(100%,#16244a));
    background: -webkit-linear-gradient(top,  #328fca 0%,#16244a 100%);
    background: -o-linear-gradient(top,  #328fca 0%,#16244a 100%);
    background: -ms-linear-gradient(top,  #328fca 0%,#16244a 100%);
    /* background: linear-gradient(to bottom,  #328fca 0%,#16244a 100%); */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#328fca', endColorstr='#16244a',GradientType=0 );
    text-align: center;
    border-radius: 5px;
    padding-bottom: 19px;
    margin-bottom: 5px;
}
.rodape-wrapper ul {
    margin: 0 auto;
    padding: 0;
    list-style: none;
    display: table;
}
.rodape-wrapper ul li {
    display: table-cell;
    height: auto;
    width: auto;
    padding: 14px 15px 0 15px;
}
.rodape-wrapper ul li img {
    max-width: 130px;
}
.rodape-links {
    position: relative;
    /* float: left; */
    width: 100%;
    height: auto;
    /* margin: 15px 0; */
    /* background: #0f0; */
}
.rodape-links ul {
    margin: 0;
    padding: 0;
    list-style: none;
    text-align: center;
}
.rodape-links ul li {
    display: inline-block;
    padding: 5px;
    margin: 0 5px;
    cursor: pointer;
}
.rodape-links ul li img {
    /* background: #0f0; */
    margin: 0 5px;
}
.ui-clock {
    border: 0px;
    background: none;
    font-size: 12px;
    line-height: 12px;
    font-family: sans-serif;
    font-weight:normal;
    color: #fff;
    text-shadow: none;
}

@media all and (max-width: 768px) {
    .h-wrapper {
        background-size: 90%;
    }
    .sate-login input[type=text],
    .sate-login input[type=password] {
        width: 100%;
    }
}

input {
    min-height: 20px;
}
.col-rodape-text {
    font-size: 12px;
    font-weight: bold;
    color: #145B9B;
    text-shadow: black 0.1em 0.1em 0.2em;
}
.col-rodapeinfos {        
    font-size: 12px;
    line-height: 12px;
}
.rodapedata {
    font-size: 9px;
}
.sate-title {
    text-align: left;
    font-size: 20px;
    line-height: 20px;
}
footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    min-height: 63px;
    height: auto;
    overflow: auto;
    color: #fff;
    text-align: center;
    font-size: 12px;
    line-height: normal;
    border-top: 1px solid white;
    background: #2384cd;
    background: -moz-linear-gradient(top,  #2384cd 0%, #3f6382 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#2384cd), color-stop(100%,#3f6382));
    background: -webkit-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: -o-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: -ms-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: linear-gradient(to bottom,  #2384cd 0%,#3f6382 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2384cd', endColorstr='#3f6382',GradientType=0 );
}
footer ul {
    display: table;
    margin: 0;
    padding: 0;
    list-style: none;
    width: 100%;
}
footer ul li {
    display: table-cell;
    vertical-align: middle;
    padding: 7px;
}

@media all and (max-width: 768px) {
    .rodape-wrapper ul li {
        display: block;
        height: auto;
        width: 100%;
        margin: 0;
        padding: 15px 0 0 0;
    }
    footer ul li {
        display: block;
    }
    #textfield2 {
        margin-left: 0;
    }
}

@media all and (max-width: 767px) {
    footer {
        position: relative;
        margin-top: 300px;
    }
}
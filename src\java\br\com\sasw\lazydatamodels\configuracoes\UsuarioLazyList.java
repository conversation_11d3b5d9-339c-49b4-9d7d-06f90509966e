/*
 */
package br.com.sasw.lazydatamodels.configuracoes;

import Dados.Persistencia;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class UsuarioLazyList extends LazyDataModel<UsuarioSatMobWeb> {

    private static final long serialVersionUID = 1L;
    private List<UsuarioSatMobWeb> usuarios;
    private final AcessosSatMobWeb acessossatmobweb;
    private final Persistencia persistencia, satellite;
    private Map filters;
    private final String codPessoa;

    public UsuarioLazyList(String codPessoa, Persistencia persistencia, Persistencia satellite, Map filters) {
        this.acessossatmobweb = new AcessosSatMobWeb();
        this.codPessoa = codPessoa;
        this.persistencia = persistencia;
        this.satellite = satellite;
        this.filters = filters;
    }

    @Override
    public List<UsuarioSatMobWeb> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            this.usuarios = this.acessossatmobweb.listagemUsuarios(first, pageSize, this.filters, this.codPessoa, this.persistencia, this.satellite);

            setRowCount(this.acessossatmobweb.contagemAcessos(this.filters, this.codPessoa, this.persistencia, this.satellite));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.usuarios;
    }

    @Override
    public Object getRowKey(UsuarioSatMobWeb usuario) {
        return usuario.getSaspw().getNome();
    }

    @Override
    public UsuarioSatMobWeb getRowData(String nome) {
        for (UsuarioSatMobWeb usuario : this.usuarios) {
            if (nome.equals(usuario.getSaspw().getNome())) {
                return usuario;
            }
        }
        return null;
    }

    public Map getFilters() {
        return filters;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
}

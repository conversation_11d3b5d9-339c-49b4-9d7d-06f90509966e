/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
*/
/* 
    Created on : 20/07/2020, 13:54:49
    Author     : <PERSON><PERSON><PERSON>
*/

.BotaoPontoMob{
    text-transform: uppercase;
    text-align: center;
    height: 60px !important;
    width: 100% !Important;
    font-size: 10pt !important
}

.DescricaoPonto, .DescricaoLocal{
    font-size: 8pt !important;
    color: #FFF !important;
    display: block;
    font-weight: 500 !important;
    padding: 0px 0px 0px 2px !important;
    height: 12px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left !important;
    margin: 1px 0px 2px 0px !important
}

.DescricaoLocal{
    max-width: calc(100% - 120px) !important; 
}

[id*="btBaterPonto"],
[id*="btInspecao"]{
    padding-top: 18px !important;
}

.LightBox{
    position: absolute;
    top: 0px;
    left: 0px;
    z-index:9999999;
    background-color: rgba(0,0,0,0.6);
    width: 100%;
    height: 100%;
}

.MenuPonto{
    height: 100%;
    width: 250px;
    background-color: #FFF;
    right: 0px;
    position: absolute;
    box-shadow: 2px 2px 4px #222;
    z-index:99999999;
    animation: slideInRight 0.25s ease-in;
}

.ListaMenu{
    width: 100%;
    margin-top: 10px !important;
}

.ListaMenu tr td{
    border-bottom: thin solid #DDD;
    color: #000;
    padding: 15px 4px 15px 4px;
    cursor: pointer;
    font-size: 10pt !Important;
}

.ListaMenu tr td i{
    width: 20px !important;
    text-align: center !important;
    color: #CCC;
    margin-right: 5px;
}

.DetalhesPonto tr td{
    min-width: calc(100% - 0px) !important;
    width: calc(100% - 0px) !important;
    max-width: calc(100% - 0px) !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 8pt !important;
}

.DetalhesPonto tr:first-child td{
    font-size: 9pt !important;
    color: steelblue
}

.batida-saida, .batida-entrada{
    color: #FFF !important;
    border-radius: 30px;
    text-align: center;
    font-weight: bold !important;
    font-size: 8pt !important;
    padding: 2px !important;
    margin-top: 3px !important;
}

.DadosCabecalhoPonto tr td{
    padding: 2px !important;
    font-size: 8pt !important;
}

.batida-saida{
    background-color: red !important;
}

.batida-entrada{
    background-color: forestgreen !important;
}

.tab-pane{
    height: calc(100vh - 170px);
    padding: 0px 8px 0px 8px !important; 
    border-left: thin solid #DDD;
    border-right: thin solid #DDD;
    border-bottom: thin solid #DDD;
    overflow: hidden !important;
    background-color: #FFF !important;
}

@media only screen and (max-width: 768px) and (min-width: 10px) {
    .DetalhesPonto tr td{
        max-width: 110px !important;
    }
}

.btn-primary {
    background-color: #1B83B4 !important;
}

label[ref="mensal-data"]{
    background-color: #002172 !important;
}

li:not(.active) a[data-toggle="tab"]{
    color: #002172 !important;
}

.active a[data-toggle="tab"]{
    color: #000 !important;
}
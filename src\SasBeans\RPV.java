package SasBeans;

/**
 * <AUTHOR>
 */
public class RPV {

    private String rpv;
    private String guia;
    private String serie;
    private String codPessoAut;
    private String data;
    private String hora;
    private String flag_excl;
    private String volumes;

    private float seqRota;
    private float valor;

    private int parada;

    public RPV() {
        rpv = "";
        guia = "";
        serie = "";
        codPessoAut = "";
        data = "";
        hora = "";
        flag_excl = "";
        volumes = "";

        seqRota = 0;
        parada = 0;
        valor = 0;
    }

    public String getVolumes() {
        return volumes;
    }

    public void setVolumes(String volumes) {
        this.volumes = volumes;
    }

    public String getFlag_excl() {
        return flag_excl;
    }

    public void setFlag_excl(String flag_excl) {
        this.flag_excl = flag_excl;
    }

    public float getValor() {
        return valor;
    }

    public void setValor(float valor) {
        this.valor = valor;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public float getSeqRota() {
        return seqRota;
    }

    public void setSeqRota(float seqRota) {
        this.seqRota = seqRota;
    }

    public int getParada() {
        return parada;
    }

    public void setParada(int parada) {
        this.parada = parada;
    }

    public String getCodPessoAut() {
        return codPessoAut;
    }

    public void setCodPessoAut(String codPessoAut) {
        this.codPessoAut = codPessoAut;
    }

    public String getRpv() {
        return rpv;
    }

    public void setRpv(String rpv) {
        this.rpv = rpv;
    }

    public String getGuia() {
        return guia;
    }

    public void setGuia(String guia) {
        this.guia = guia;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }
}

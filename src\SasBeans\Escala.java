package SasBeans;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class Escala {

    private String Rota;
    private String Data;
    private BigDecimal CodFil;
    private String Hora1;
    private String Hora2;
    private String Hora3;
    private String Hora4;
    private BigDecimal HsInterv;
    private BigDecimal HsTot;
    private BigDecimal MatrMot;
    private String HrMot;
    private BigDecimal MatrChe;
    private String HrChe;
    private BigDecimal MatrVig1;
    private String HrVig1;
    private BigDecimal MatrVig2;
    private String HrVig2;
    private BigDecimal MatrVig3;
    private String HrVig3;
    private BigDecimal CodPessoaSup;
    private BigDecimal Veiculo;
    private BigDecimal SeqRota;
    private String Situacao;
    private LocalDate DtUltAckMob;
    private String HrUltAckMob;
    private String Operador;
    private LocalDate Dt_Alter;
    private String Hr_Alter;

    public Escala() {
        this.Rota = "";
        this.Data = null;
        this.CodFil = new BigDecimal("0");
        this.Hora1 = "";
        this.Hora2 = "";
        this.Hora3 = "";
        this.Hora4 = "";
        this.HsInterv = new BigDecimal("0");
        this.HsTot = new BigDecimal("0");
        this.MatrMot = new BigDecimal("0");
        this.HrMot = "";
        this.MatrChe = new BigDecimal("0");
        this.HrChe = "";
        this.MatrVig1 = new BigDecimal("0");
        this.HrVig1 = "";
        this.MatrVig2 = new BigDecimal("0");
        this.HrVig2 = "";
        this.MatrVig3 = new BigDecimal("0");
        this.HrVig3 = "";
        this.Veiculo = new BigDecimal("0");
        this.SeqRota = new BigDecimal("0");
        this.Situacao = "";
        this.DtUltAckMob = null;
        this.HrUltAckMob = "";
        this.Operador = "";
        this.Dt_Alter = null;
        this.Hr_Alter = "";
    }

    public Escala(Escala original) {
        Rota = original.getRota();
        Data = original.getData();
        CodFil = original.getCodFil();
        Hora1 = original.getHora1();
        Hora2 = original.getHora2();
        Hora3 = original.getHora3();
        Hora4 = original.getHora4();
        HsInterv = original.getHsInterv();
        HsTot = original.getHsTot();
        MatrMot = original.getMatrMot();
        HrMot = original.getHrMot();
        MatrChe = original.getMatrChe();
        HrChe = original.getHrChe();
        MatrVig1 = original.getMatrVig1();
        HrVig1 = original.getHrVig1();
        MatrVig2 = original.getMatrVig2();
        HrVig2 = original.getHrVig2();
        MatrVig3 = original.getMatrVig3();
        HrVig3 = original.getHrVig3();
        Veiculo = original.getVeiculo();
        SeqRota = original.getSeqRota();
        Situacao = original.getSituacao();
        DtUltAckMob = original.getDtUltAckMob();
        HrUltAckMob = original.getHrUltAckMob();
        Operador = original.getOperador();
        Dt_Alter = original.getDt_Alter();
        Hr_Alter = original.getHr_Alter();
    }

    public String getRota() {
        return Rota;
    }

    public void setRota(String Rota) {
        this.Rota = Rota;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public BigDecimal getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        try {
            this.CodFil = new BigDecimal(CodFil);
        } catch (Exception e) {
            this.CodFil = new BigDecimal("0");
        }
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getHora2() {
        return Hora2;
    }

    public void setHora2(String Hora2) {
        this.Hora2 = Hora2;
    }

    public String getHora3() {
        return Hora3;
    }

    public void setHora3(String Hora3) {
        this.Hora3 = Hora3;
    }

    public String getHora4() {
        return Hora4;
    }

    public void setHora4(String Hora4) {
        this.Hora4 = Hora4;
    }

    public BigDecimal getHsInterv() {
        return HsInterv;
    }

    public void setHsInterv(String HsInterv) {
        try {
            this.HsInterv = new BigDecimal(HsInterv);
        } catch (Exception e) {
            this.HsInterv = new BigDecimal("0");
        }
    }

    public BigDecimal getHsTot() {
        return HsTot;
    }

    public void setHsTot(String HsTot) {
        try {
            this.HsTot = new BigDecimal(HsTot);
        } catch (Exception e) {
            this.HsTot = new BigDecimal("0");
        }
    }

    public BigDecimal getMatrMot() {
        return MatrMot;
    }

    public void setMatrMot(String MatrMot) {
        try {
            this.MatrMot = new BigDecimal(MatrMot);
        } catch (Exception e) {
            this.MatrMot = new BigDecimal("0");
        }
    }

    public String getHrMot() {
        return HrMot;
    }

    public void setHrMot(String HrMot) {
        this.HrMot = HrMot;
    }

    public BigDecimal getMatrChe() {
        return MatrChe;
    }

    public void setMatrChe(String MatrChe) {
        try {
            this.MatrChe = new BigDecimal(MatrChe);
        } catch (Exception e) {
            this.MatrChe = new BigDecimal("0");
        }
    }

    public String getHrChe() {
        return HrChe;
    }

    public void setHrChe(String HrChe) {
        this.HrChe = HrChe;
    }

    public BigDecimal getMatrVig1() {
        return MatrVig1;
    }

    public void setMatrVig1(String MatrVig1) {
        try {
            this.MatrVig1 = new BigDecimal(MatrVig1);
        } catch (Exception e) {
            this.MatrVig1 = new BigDecimal("0");
        }
    }

    public String getHrVig1() {
        return HrVig1;
    }

    public void setHrVig1(String HrVig1) {
        this.HrVig1 = HrVig1;
    }

    public BigDecimal getMatrVig2() {
        return MatrVig2;
    }

    public void setMatrVig2(String MatrVig2) {
        try {
            this.MatrVig2 = new BigDecimal(MatrVig2);
        } catch (Exception e) {
            this.MatrVig2 = new BigDecimal("0");
        }
    }

    public String getHrVig2() {
        return HrVig2;
    }

    public void setHrVig2(String HrVig2) {
        this.HrVig2 = HrVig2;
    }

    public BigDecimal getMatrVig3() {
        return MatrVig3;
    }

    public void setMatrVig3(String MatrVig3) {
        try {
            this.MatrVig3 = new BigDecimal(MatrVig3);
        } catch (Exception e) {
            this.MatrVig3 = new BigDecimal("0");
        }
    }

    public String getHrVig3() {
        return HrVig3;
    }

    public void setHrVig3(String HrVig3) {
        this.HrVig3 = HrVig3;
    }

    public BigDecimal getCodPessoaSup() {
        return CodPessoaSup;
    }

    public void setCodPessoaSup(String CodPessoaSup) {
        try {
            this.CodPessoaSup = new BigDecimal(CodPessoaSup);
        } catch (Exception e) {
            this.CodPessoaSup = new BigDecimal("0");
        }
    }

    public BigDecimal getVeiculo() {
        return Veiculo;
    }

    public void setVeiculo(String Veiculo) {
        try {
            this.Veiculo = new BigDecimal(Veiculo);
        } catch (Exception e) {
            this.Veiculo = new BigDecimal("0");
        }
    }

    public BigDecimal getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        try {
            this.SeqRota = new BigDecimal(SeqRota);
        } catch (Exception e) {
            this.SeqRota = new BigDecimal("0");
        }
    }

    public String getSituacao() {
        return Situacao;
    }

    public void setSituacao(String Situacao) {
        this.Situacao = Situacao;
    }

    public LocalDate getDtUltAckMob() {
        return DtUltAckMob;
    }

    public void setDtUltAckMob(LocalDate DtUltAckMob) {
        this.DtUltAckMob = DtUltAckMob;
    }

    public String getHrUltAckMob() {
        return HrUltAckMob;
    }

    public void setHrUltAckMob(String HrUltAckMob) {
        this.HrUltAckMob = HrUltAckMob;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public LocalDate getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(LocalDate Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }
}

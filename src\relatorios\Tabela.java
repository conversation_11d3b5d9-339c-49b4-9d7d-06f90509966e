/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorios;

/**
 *
 * <AUTHOR>
 */
public class Tabela {

    /**
     * Substituir @TabelaConteudo com o conteúdo da tabela
     *
     * @return
     */
    public static String tabela() {
        StringBuilder html = new StringBuilder();
        html.append("<table style=\"width:100.0%\" cellspacing=\"5\" cellpadding=\"0\" border=\"0\" summary=\"sasw\">\n");
        html.append("@TabelaConteudo\n");
        html.append("</table>\n");
        return html.toString();
    }

    public static String titulo(int colunas) {
        StringBuilder html = new StringBuilder();
        html.append("<tr>\n");
        html.append("<td rowspan=\"").append(colunas).append("\">\n");
        html.append("@TabelaTitulo\n");
        html.append("</td>\n");
        html.append("</tr>\n");
        return html.toString();
    }

    /**
     * Substituir @TabelaCabecalho para conteúdo do cabeçalho
     *
     * @param colunas
     * @return
     */
    public static String cabecalho(int colunas) {
        StringBuilder html = new StringBuilder();
        html.append("<thead>\n");
        for (int i = 1; i <= colunas; i++) {
            html.append("<th>");
            html.append("@TabelaCabecalho").append(i);
            html.append("</th>");
        }
        html.append("\n</thead>\n");
        return html.toString();
    }

    /**
     * Substituir @EstiloColuna para estilo e @TabelaColuna para conteúdo da
     * tabela
     *
     * @param colunas
     * @return
     */
    public static String linhas(int colunas) {
        StringBuilder html = new StringBuilder();
        html.append("<tr>\n");
        for (int i = 1; i <= colunas; i++) {
            html.append("	<td style=\"@EstiloColuna").append(i).append("\">\n");
            html.append("		@TabelaColuna").append(i).append("\n");
            html.append("	</td>\n");
        }
        html.append("</tr>\n");
        return html.toString();
    }

    /**
     * Substituir @TabelaColunaTotal para conteúdo do total
     *
     * @param colunas
     * @return
     */
    public static String totalizador(int colunas) {
        StringBuilder html = new StringBuilder();
        html.append("<tfoot>\n");
        for (int i = 1; i <= colunas; i++) {
            html.append("	<th>\n");
            html.append("		@TabelaColunaTotal").append(i).append("\n");
            html.append("	</th>\n");
        }
        html.append("</tfoot>\n");
        return html.toString();
    }
}

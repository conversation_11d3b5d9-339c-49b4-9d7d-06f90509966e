/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.lazydatamodels;

import Controller.Guias.GuiasSatWeb;
import Dados.Persistencia;
import SasBeansCompostas.EGtv;
import br.com.sasw.utils.Messages;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class EGtvLazyList extends LazyDataModel<EGtv> {

    private static final long serialVersionUID = 1L;
    private List<EGtv> guias;
    private final GuiasSatWeb guiasweb;
    private Persistencia persistencia;

    public EGtvLazyList(Persistencia pst) {
        this.guiasweb = new GuiasSatWeb();
        this.persistencia = pst;
    }

    @Override
    public List<EGtv> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map filters) {
        try {
            this.guias = this.guiasweb.listaPaginada(first, pageSize, filters, this.persistencia);

            for (EGtv g : this.guias) {
                if (g.getAssinatura().contains("Chefe Equipe")) {
                    g.setAssinatura(g.getAssinatura().replaceFirst("Chefe Equipe", Messages.getMessageS("ChefeEquipe")));
                }
            }
//            
            // set the total of players
            setRowCount(this.guiasweb.totalGuias(filters, this.persistencia));

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.guias;
    }

    @Override
    public Object getRowKey(EGtv guiaCliente) {
        return guiaCliente.getGuia();
    }

    @Override
    public EGtv getRowData(String guia) {
        for (EGtv eGtv : this.guias) {
            if (guia.equals(eGtv.getGuia())) {
                return eGtv;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }
}

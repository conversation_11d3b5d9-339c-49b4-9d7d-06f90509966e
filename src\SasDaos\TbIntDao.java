/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.TbInt;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TbIntDao {

    // CREATE
    public boolean gravaTbInt(TbInt tbint, Persistencia persistencia) {
        boolean retorno;
        String sql;
        sql = "insert into tbint (Tabela,Descricao) "
                + "values (?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbint.getTabela());
            consulta.setString(tbint.getDescricao());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // READ
    public List<TbInt> buscaTbInt(Persistencia persistencia) throws Exception {
        List<TbInt> listTbInt;
        try {
            TbInt tbint;
            Consulta consult = new Consulta("select Tabela, Descricao "
                    + "from tbint", persistencia);
            consult.select();
            listTbInt = new ArrayList();
            while (consult.Proximo()) {
                tbint = new TbInt();
                tbint.setTabela(consult.getInt("Tabela"));
                tbint.setDescricao(consult.getString("Descricao"));
                listTbInt.add(tbint);
            }
            consult.Close();
        } catch (Exception e) {
            listTbInt = null;
            throw new Exception("Falha ao buscar..." + e.getMessage());
        }
        return listTbInt;
    }

    // UPDATE
    public void atualizarTbInt(TbInt tbint, Persistencia persistencia) throws Exception {
        String sql = "update TbInt set Tabela=?, Descricao=? "
                + "where Tabela=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbint.getTabela());
            consulta.setString(tbint.getDescricao());
            consulta.setInt(tbint.getTabela());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao atualizar... " + e.getMessage());
        }
    }

    // DELETE
    public void excluirTbInt(TbInt tbint, Persistencia persistencia) throws Exception {
        String sql;
        sql = "delete from tbint where Tabela=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(tbint.getTabela());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao excluir..." + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class AcessoAutArea {

    public BigDecimal Sequencia;
    public String CodFil;
    public BigDecimal CodArea;
    public String HrEntrada;
    public String HrSaida;
    public String Operador;
    public String Dt_Alter;
    public String Hr_Alter;
    public String Descricao;

    public AcessoAutArea() {
        Sequencia = BigDecimal.ZERO;
        CodFil = "";
        CodArea = BigDecimal.ZERO;
        HrEntrada = "";
        HrSaida = "";
        Operador = "";
        Dt_Alter = "";
        Hr_Alter = "";
        Descricao = "";
    }

    public BigDecimal getSequencia() {
        return Sequencia;
    }

    public void setSequencia(BigDecimal Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public BigDecimal getCodArea() {
        return CodArea;
    }

    public void setCodArea(BigDecimal CodArea) {
        this.CodArea = CodArea;
    }

    public String getHrEntrada() {
        return HrEntrada;
    }

    public void setHrEntrada(String HrEntrada) {
        this.HrEntrada = HrEntrada;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    @Override
    public String toString() {
        return "AcessoAutArea{" + "codArea=" + CodArea.toPlainString()
                + ", sequencia=" + Sequencia.toPlainString() + '}';
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 23 * hash + Objects.hashCode(this.CodArea);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AcessoAutArea other = (AcessoAutArea) obj;
        if (!Objects.equals(this.CodArea, other.CodArea)) {
            return false;
        }
        if (!Objects.equals(this.Sequencia, other.Sequencia)) {
            return false;
        }
        return true;
    }
}

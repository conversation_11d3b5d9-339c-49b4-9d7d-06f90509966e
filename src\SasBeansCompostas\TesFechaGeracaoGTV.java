package SasBeansCompostas;

import SasBeans.Clientes;
import SasBeans.TesFecha;
import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TesFechaGeracaoGTV {

    private TesFecha tesFecha;
    private Clientes clientes;
    private BigDecimal qtde;
    private BigDecimal valor;

    public TesFechaGeracaoGTV() {
    }

    public TesFechaGeracaoGTV(TesFechaGeracaoGTV original) {
        this.tesFecha = new TesFecha(original.getTesFecha());
        this.clientes = new Clientes(original.getClientes());
        this.qtde = original.getQtde();
        this.valor = original.getValor();
    }

    public TesFecha getTesFecha() {
        return tesFecha;
    }

    public void setTesFecha(TesFecha tesFecha) {
        this.tesFecha = tesFecha;
    }

    public Clientes getClientes() {
        return clientes;
    }

    public void setClientes(Clientes clientes) {
        this.clientes = clientes;
    }

    public BigDecimal getQtde() {
        return qtde;
    }

    public void setQtde(BigDecimal qtde) {
        this.qtde = qtde;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    @Override
    public String toString() {
        return "TesFechaGeracaoGTV{" + "tesFecha=" + tesFecha + ", clientes=" + clientes + ", qtde=" + qtde + ", valor=" + valor + '}';
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 67 * hash + Objects.hashCode(this.tesFecha);
        hash = 67 * hash + Objects.hashCode(this.clientes);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TesFechaGeracaoGTV other = (TesFechaGeracaoGTV) obj;
        if (!Objects.equals(this.tesFecha, other.tesFecha)) {
            return false;
        }
        if (!Objects.equals(this.clientes, other.clientes)) {
            return false;
        }
        if (!Objects.equals(this.qtde, other.qtde)) {
            return false;
        }
        if (!Objects.equals(this.valor, other.valor)) {
            return false;
        }
        return true;
    }

}

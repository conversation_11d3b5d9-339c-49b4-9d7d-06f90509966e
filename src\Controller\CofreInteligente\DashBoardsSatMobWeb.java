/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.CofreInteligente;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.TesCofresMov;
import SasBeansCompostas.CofreDashBoardGeral;
import SasBeansCompostas.CofreDashBoardStatus;
import SasBeansCompostas.DashboardCharts;
import SasDaos.FiliaisDao;
import SasDaos.TesCofresMovDao;
import br.com.sasw.pacotesuteis.sasdaos.compostas.CofreDashBoardDao;
import br.com.sasw.pacotesuteis.sasdaos.compostas.DashboardSecSaudeDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DashBoardsSatMobWeb {

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CofreDashBoardStatus> obterEstatisticaPorCofre(Persistencia persistencia) throws Exception {
        try {
            CofreDashBoardDao cofreDashBoardDao = new CofreDashBoardDao();
            return cofreDashBoardDao.obterEstatisticaPorCofre(persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CofreDashBoardStatus> obterEstatisticaPorCofre(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            CofreDashBoardDao cofreDashBoardDao = new CofreDashBoardDao();
            return cofreDashBoardDao.obterEstatisticaPorCofre(filtros, exibirTodos, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CofreDashBoardGeral> obterEstatisticaPorHora(String data, Persistencia persistencia) throws Exception {
        try {
            CofreDashBoardDao cofreDashBoardDao = new CofreDashBoardDao();
            return cofreDashBoardDao.obterEstatisticaPorHora(data, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CofreDashBoardGeral> obterEstatisticaPorHora(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            CofreDashBoardDao cofreDashBoardDao = new CofreDashBoardDao();
            return cofreDashBoardDao.obterEstatisticaPorHora(filtros, exibirTodos, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacaoPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listaPaginadaCliente(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacao(Map filtros, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listaPaginadaCliente(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacao(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listaPaginadaCliente(filtros, exibirTodos, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacaoPorCofre(Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listaMovimentacaoCofre(persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<TesCofresMov> detalhesMovimentacaoPorCofre(Map filtros, boolean exibirTodos, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.listaMovimentacaoCofre(filtros, exibirTodos, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Integer totalMovimentacaoPaginada(Map filtros, Persistencia persistencia) throws Exception {
        try {
            TesCofresMovDao tesCofresMovDao = new TesCofresMovDao();
            return tesCofresMovDao.totalPaginacao(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("DashBoardsSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

}

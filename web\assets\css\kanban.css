/*
*/
/* 
    Created on : 28-Apr-2017, 10:25:33
    Author     : <PERSON>
*/

.ui-datatable-scrollable-body{
    height: calc(100vh - 66px - 30px - 28px - 66px - 20px);
    background:transparent;
}
.ui-datatable-scrollable-body.toggled{
    height: calc(100vh - 66px - 30px - 28px - 20px - 30px);
}

.cabecalho-dashboard{
    width: 16%;
    padding: 10px;
    border: 1px solid black !important;
    height: 100%;
}

.ui-dashboard-column{
    width: 16%;
    padding: 10px;
    padding-bottom: 60px;
    border: 1px solid black;
    height: 100%;
}

.postit-selecionado{
    /* General */
    margin: 10px auto !important;
    padding: 8px !important;
    /* Border */
    border:1px #E8Ds47 solid !important;
    /* Shadow */
    -moz-box-shadow:0px 0px 6px 1px #333333 !important;
    -webkit-box-shadow:0px 0px 6px 1px #333333 !important;
    box-shadow:0px 0px 6px 1px #333333 !important;
    /* Background */
    background: rgba(169,194,217,1);
    background: -moz-linear-gradient(top, rgba(169,194,217,1) 0%, rgba(169,194,217,1) 12%, rgba(155,188,219,1) 19%, rgba(73,155,234,1) 61%);
    background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(169,194,217,1)), color-stop(12%, rgba(169,194,217,1)), color-stop(19%, rgba(155,188,219,1)), color-stop(61%, rgba(73,155,234,1)));
    background: -webkit-linear-gradient(top, rgba(169,194,217,1) 0%, rgba(169,194,217,1) 12%, rgba(155,188,219,1) 19%, rgba(73,155,234,1) 61%);
    background: -o-linear-gradient(top, rgba(169,194,217,1) 0%, rgba(169,194,217,1) 12%, rgba(155,188,219,1) 19%, rgba(73,155,234,1) 61%);
    background: -ms-linear-gradient(top, rgba(169,194,217,1) 0%, rgba(169,194,217,1) 12%, rgba(155,188,219,1) 19%, rgba(73,155,234,1) 61%);
    background: linear-gradient(to bottom, rgba(169,194,217,1) 0%, rgba(169,194,217,1) 12%, rgba(155,188,219,1) 19%, rgba(73,155,234,1) 61%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a9c2d9', endColorstr='#499bea', GradientType=0 );
}

.postit .ui-widget-header, .postit-selecionado .ui-widget-header{
    background-color: transparent !important;
    border-style: none;
    color: black;
}

.postit {
    /* General */
    margin: 10px auto !important;
    padding: 8px !important;
    /* Border */
    border:1px #E8Ds47 solid !important;
    /* Shadow */
    -moz-box-shadow:0px 0px 6px 1px #333333 !important;
    -webkit-box-shadow:0px 0px 6px 1px #333333 !important;
    box-shadow:0px 0px 6px 1px #333333 !important;
    /* Background */
    background: #fefdca; /* Old browsers */
    background: -moz-linear-gradient(top, #fefdca 0%, #f7f381 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#fefdca), color-stop(100%,#f7f381)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #fefdca 0%,#f7f381 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #fefdca 0%,#f7f381 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #fefdca 0%,#f7f381 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fefdca', endColorstr='#f7f381',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #fefdca 0%,#f7f381 100%); /* W3C; A catch-all for everything else */
}

.ui-growl {
    position: fixed;
    top: 20px !important;
    right: 0px !important;
    width: 100%;
    font-size: 1.3em;
}

.botaoCadastrarSenha.ui-state-default, .botaoCadastrarSenha.ui-widget-content.ui-state-default{
    border-radius: 10px;
    border: 1px solid #E93D3D;
    height: 35px;
    width: 110px;
    background: transparent;
    background-color: transparent;
    background-image: none;
    text-shadow: none;
    color: white;
}

.botoesSenha.ui-state-default, .botoesSenha.ui-widget-content.ui-state-default{
    border-radius: 30px;
    border: 1px solid #E93D3D;
    height: 15px;
    width: 15px;
    background: transparent;
    background-color: transparent;
    background-image: none;
    opacity: 1 !important;
}

.botoesSenhaPressionado.ui-state-default, .botoesSenhaPressionado.ui-widget-content.ui-state-default{
    border-radius: 30px;
    border: 1px solid #E93D3D;
    height: 15px;
    width: 15px;
    background: #E93D3D;
    background-color: #E93D3D;
    background-image: none;
    opacity: 1 !important;
}

.botoes.ui-state-default, .botoes.ui-widget-content.ui-state-default{
    border-radius: 50px;
    border: 1px solid #E93D3D;
    height: 80px;
    width: 80px;
    color: white;
    background: transparent;
    background-color: transparent;
    background-image: none;
    text-shadow: none;
    font-size: 1.8em;
}

.botaoNulo.ui-state-default, .botaoNulo.ui-widget-content.ui-state-default{
    border: none !important;
    box-shadow: none;
}

.botaoNulo.ui-state-hover, .botaoNulo.ui-widget-content.ui-state-hover{
    border: none !important;
    box-shadow: none;
    background: none !important;
    background-color: transparent !important;
    text-shadow: none;
}

.botoes.ui-state-hover, .botoes.ui-widget-content.ui-state-hover{
    background: #E93D3D;
    background-color: #E93D3D;
    text-shadow: none;
}

.botoes.ui-state-active, .botoes.ui-widget-content.ui-state-active{
    background: #A93D3D;
    background-color: #A93D3D;
    text-shadow: none;
}

.panel-botoes .ui-widget-content{
    background: transparent !important;
    margin: 0 auto;
    display: block;
}

.panel-botoes.ui-panelgrid.ui-panelgrid-cell{
    padding: 0px;
}

.rodape .ui-widget-content{
    color: #fff;
    font-size: 11px;
}

.borderNone, .borderNone .ui-widget-content{
    border: none !important;
}

.usuario .ui-state-error, .ui-widget-content .ui-state-error, .usuario .ui-selectonemenu .ui-state-error{
    border: 1px solid #287EBF;
    background: #E6E6E6 50% 50% repeat-x;
    color: #287EBF;
    background-image: -moz-linear-gradient(top, #ffffff, #287EBF);
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.cidade .ui-autocomplete-panel {
    width: 100% !important;
}
.cidade .ui-autocomplete-input{
    width: 100% !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.cadastrar{
    width: 90vw;
    min-width: 100%;
}
.no-background{
    background: transparent !important;
}

.cadastrarSenha{
    width: calc(100vw - 30px);
    min-width: 100%;
    background: transparent !important;
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }
    .cadastrarSenha{
        background: transparent !important;
        width: 460px;
    }
    .botoes.ui-state-default, .botoes.ui-widget-content.ui-state-default{
        border-radius: 30px;
        border: 1px solid #E93D3D;
        height: 50px;
        width: 50px;
        color: white;
        background: transparent;
        background-color: transparent;
        background-image: none;
        text-shadow: none;
        font-size: 1em;
    }
    
    .botoes.ui-state-hover, .botoes.ui-widget-content.ui-state-hover{
        background: #E93D3D;
        background-color: #E93D3D;
        text-shadow: none;
    }

    .botoes.ui-state-active, .botoes.ui-widget-content.ui-state-active{
        background: #A93D3D;
        background-color: #A93D3D;
        text-shadow: none;
    }
    
    .ui-growl {
        position: fixed;
        top: 20% !important;
        right: 40% !important;
        width: 301px;
        font-size: 1.1em !important;
    }
}

.ui-buttonset .ui-button{
    width: 50%;
}
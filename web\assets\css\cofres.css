/*
To change this license header, choose License Headers in Project Properties.
To change this template file, choose <PERSON><PERSON> | Templates
and open the template in the editor.
*/
/* 
    Created on : 07/02/2017, 17:25:47
    Author     : Giugliano
*/

.ui-panel-title{
    width: 100% !important;
}

hr{
    border-top: 1px solid #444444 !important;
}

.scrollPanelDialogo{
    padding: 10px;
    height:310px;
    margin:0px !important;
    border:thin solid #CCC !important;
}

.colunaDialogoDireita{
    padding-bottom:4px !important;
    padding-right:0px !important;
    text-align:right;
}

.colunaDialogoEsquerda{
    padding-bottom:4px !important;
    padding-left:8px !important;
    text-align:left;
    white-space:nowrap;
}

.tituloDialogo{
    padding:0px 0px 5px 0px !important;
    margin:8px 1px 8px 1px !important;
    text-align:center !important;
    background-color:#000;
    color:#FFF;
    border-radius:20px;
    box-shadow:2px 2px 3px #CCC;
}

.dialogo{
    border:thin solid #666 !important;
    max-height:570px !important;
    box-shadow:0px 0px 5px #303030 !important;
    overflow: hidden !important;
    padding-bottom:20px !important;
    border-top:4px solid #3C8DBC !important;
    border-radius:8px !important;
    font-family:'Open Sans', sans-serif !important;
    padding:0px 0px 20px 0px !important;
    background-color:#EEE !important;
}

div[id*="divGride"]{
    height:138px !important; 
    max-height:138px !important;
}

.ui-panel[id*="divGrid"] div:nth-child(1):not([class*="colu"]){
    background-color:transparent !important
}

div[id*="pnlDadosMovDiaria"],
div[id*="pnlDadosMovDiaria"] div{
    background-color:#FFF !important
}

div[id*="divGrid"],
div[id*="divGridStatus"]{
    height:270px !important;
}

.Nomeusuario{
    width:auto !important; 
}

@media only screen and (max-width: 700px) and (min-width: 10px) {       
    div[id*="divGride"]{
        height:158px !important; 
        max-height:158px !important;
    }

    div[id*="divGrid"]{
        height:220px !important;
    }

    div[id*="divGridStatus"]{
        height:175px !important;
    }

    .Nomeusuario{
        width:270px !important; 
    }
}

#HeaderCofre{
    color:#022B4A !important;
}

div[id*="pnlBotoes"]{
    width:170px !important;
}

div[id*="divPaiGride"]{
    height:223px !important;
}

@media only screen and (max-width: 1100px) and (min-width: 10px) {                    
.Labels{
        font-size:8pt !important;
        padding-top:1px !important;
    }
}

@media only screen and (max-width: 700px) and (min-width: 10px) {                    

    .Labels{
        float:left !important;
        display:inline-block !important;
        width:55% !important;
        text-align: right !important;
        padding-right:10px !important;
        padding-top:5px !important;
    }

    .ui-paginator-current{
        font-size: 8pt !important;
        margin-right:0px !important;
        margin-left:12px !important;
        padding-left:0px !important;
        padding-right:0px !important;
    }   

    div[id*="divPaiGride"]{
        height:242px !important;
    }
}

@media only screen and (max-width: 425px) and (min-width: 10px) {

    div[id*="pnlBotoes"]{
        width:125px !important;
    }

    div[id*="pnlBotoes"] img{
        height:30px !important;
    }
}

.tabelaMov{
    white-space:pre-line !important;
}

.tabelaMov .ui-widget-content, .tabelaMovGuia .ui-widget-content{
    background: white;
}

.tabelaMov td, .tabelaMovGuia td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaMov .ui-datatable-selectable, .tabelaMovGuia .ui-datatable-selectable{
    border: 1px solid #dddddd !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaMov .ui-datatable-scrollable-header-box, .tabelaMovGuia .ui-datatable-scrollable-header-box{
    background: white;
}
.tabelaMov .ui-datatable-data .ui-widget-content, .tabelaMovGuia .ui-datatable-data .ui-widget-content{
    border: 1px solid #dddddd !important;
}
.tabelaMov .ui-state-highlight , .tabelaMovGuia .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabelaMov .ui-state-hover , .tabelaMovGuia .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}
.tabelaMov .ui-datatable-scrollable-body , .tabelaMovGuia .ui-datatable-scrollable-body {
    background: white !important;
}

.tabelaMov .ui-datatable-scrollable-body{
    height: calc(100vh - 115px - 30px - 28px - 85px - 20px);
    background:transparent;
}

.tabelaMov .ui-datatable-scrollable-body.toggled{
    height: calc(100vh - 115px - 30px - 28px - 20px);
}


.agrupador .ui-state-default{
    background-color: #032a49 !important;
    background-image: none !important;
    color: white;
    text-shadow: none !important;
}

[role=gridcell] {
    white-space:pre-line !important;
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.botao .ui-panelgrid .ui-panelgrid-cell, .botao .ui-panelgrid-cell{
    padding: 0px !important;
}

.p-datepicker{
    min-width: 0px !important;
}

.ui-state-disabled, .ui-widget-content .ui-state-disabled{
    opacity: 0.7;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.ui-dialog .ui-dialog-buttonpane {
    text-align: center;
    background-image: none;
    margin: .5em 0 0 0;
    padding: .3em 1em .5em .4em;
}

.cadastrar{
    width: 90vw;
    min-width: 100%;
}

.cadastrar2{
    width: 90vw;
    min-width: 100%;
    background-color: #EEE!important;
    padding:0px !important;
    margin:10px 0px 0px 0px !important
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 90vw;
    }
    .cadastrar2{
        width: 700px;
    }

    .tabs{
        width: 700px;
    }
    .tabs .ui-panelgrid-cell{
        padding-right: 15px;
    }
    .panelTabela{
        width: auto;
    }
}

/*
*/
/* 
    Created on : Jan 2, 2017, 2:44:49 PM
    Author     : Richard
*/
.right{
    float: right;
}

.panelTabela{
    width: 100%;
}


.tabs{
    width: 90vh;
    min-width: 100%;
}

.tabs .ui-panelgrid-cell{
    padding-right: 0px !important;
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

.ui-tabs .ui-tabs-nav.ui-widget-header li{
    background: #ccccff;
}
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled{
    opacity: 0.60;
}
.pessoa .ui-autocomplete-panel {
    width: 100% !important;
}
.pessoa .ui-autocomplete-input{
    width: 100% !important;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    display: block;
}
.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
    padding-right: 15px;
}

.cliente2 .ui-autocomplete-panel {
    width: 275px !important;
}
.cliente2 .ui-autocomplete-input{
    width: 275px !important;
}

.cliente3 .ui-autocomplete-panel {
    width: 280px !important;
}
.cliente3 .ui-autocomplete-input{
    width: 280px !important;
}
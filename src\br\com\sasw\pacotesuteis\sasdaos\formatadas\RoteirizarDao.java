/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.formatadas;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Pedido;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.formatarHoraSQL;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RoteirizarDao {

    public void roteirizar(List<Pedido> pedidos, Persistencia persistencia) throws Exception {
        try {
            StringBuilder sql = new StringBuilder("Create table #tmpRoteirizarAuto (Pedido float, CodFil Float, Rota Float, \n")
                    .append(" <PERSON><PERSON>ar<PERSON>har(5), Operador VarChar(10), Dt_Alter Date, Hr_Alter VarChar(5));\n");

            for (int i = 0; i < pedidos.size(); i++) {
                sql.append(" INSERT INTO #tmpRoteirizarAuto Values (?,?,?,?,?,?,?);\n");
            }

//            sql += "select * from #tmpRoteirizarAuto;";
            sql.append("Declare @pedido int\n");
            sql.append("Declare @codfil int\n");
            sql.append("Declare @rota int\n");
            sql.append("Declare @seqRota int\n");
            sql.append("Declare @acrescimoMinutos int\n");
            sql.append("Declare @parada int\n");
            sql.append("Declare @cxforte varchar(07)\n");
            sql.append("Declare @cxforteNred Varchar(20)\n");
            sql.append("Declare @cxforteRegiao Varchar(03)\n");
            sql.append("Declare @hora1 varchar(05)\n");
            sql.append("Declare @hora2 varchar(05)\n");
            sql.append("Declare @os int\n");
            sql.append("Declare @codcli1 varchar(07)\n");
            sql.append("Declare @nredOri varchar(20)\n");
            sql.append("Declare @regiaoOri varchar(20)\n");
            sql.append("Declare @codcli2 varchar(07)\n");
            sql.append("Declare @nredDst varchar(20)\n");
            sql.append("Declare @regiaoDst varchar(20)\n");
            sql.append("Declare @data date\n");
            sql.append("Declare @hrX varchar(05)\n");
            sql.append("Declare @paradaX int\n");
            sql.append("Declare @tipo Varchar(02)\n");
            sql.append("Declare @valorDst float\n");
            sql.append("Declare @valor Float\n");
            sql.append("Declare @dPar int\n");
            sql.append("Declare @classifsrv varchar(02)\n");
            sql.append("Declare @operador Varchar(50)\n");
            sql.append("Declare @dt_alter Varchar(10)\n");
            sql.append("Declare @hr_alter Varchar(05)\n");
            sql.append("Declare @er Varchar(02)\n");
            sql.append("Declare @seqParadaOri int\n");
            sql.append("Declare @seqParadaDst int\n");
            sql.append("Declare @cxfExiste int\n");
            sql.append("Declare @horaCxfManha Varchar(05)\n");
            sql.append("Declare @horaCxfTarde Varchar(05)\n");
            sql.append("Declare @seqParadaCxf int\n");
            sql.append("Declare @observ Varchar(20)\n");
            sql.append("set @observ = 'Automatic Routing'\n");
            sql.append("set @acrescimoMinutos = 10\n");
            sql.append("set @cxforte = ''\n");
            sql.append("set @horaCxfManha = '1230'\n");
            sql.append("set @horaCxfTarde = '1900'\n");
            sql.append("\n");
            sql.append("Delete from #tmpRoteirizarAuto where pedido in (Select Numero from Pedido where codfil = #tmpRoteirizarAuto.Codfil and #tmpRoteirizarAuto.Pedido = Pedido.Numero and Situacao <> 'PD')\n");
            sql.append("\n");
            sql.append("\n");
            sql.append("WHILE (SELECT count(*) FROM #tmpRoteirizarAuto) > 0\n");
            sql.append("BEGIN\n");
            sql.append("	set @parada = 0\n");
            sql.append("	set @valorDst = 0\n");
            sql.append("\n");
            sql.append("	Select top 01 \n");
            sql.append("	@pedido = a.Pedido,\n");
            sql.append("	@codfil = a.CodFil, \n");
            sql.append("	@rota = a.Rota,\n");
            sql.append("	@hora1 = a.Hora, \n");
            sql.append("	@operador = a.Operador,\n");
            sql.append("	@dt_alter = a.Dt_Alter,\n");
            sql.append("	@hr_alter = a.Hr_Alter,\n");
            sql.append("	@hora2 = Pedido.Hora1D,\n");
            sql.append("	@os = Pedido.OS,\n");
            sql.append("	@codcli1 = Pedido.CodCli1,\n");
            sql.append("	@nredOri = CliOri.Nred,\n");
            sql.append("	@regiaoOri = CliOri.Regiao,\n");
            sql.append("	@codcli2 = Pedido.CodCli2,\n");
            sql.append("	@nredDst = CliDst.Nred,\n");
            sql.append("	@regiaoDst = CliDst.Regiao,\n");
            sql.append("	@data = Pedido.Data,\n");
            sql.append("	@tipo = Pedido.Tipo,\n");
            sql.append("	@valor = Pedido.Valor,\n");
            sql.append("	@classifsrv = Pedido.ClassifSrv,\n");
            sql.append("	@seqRota = Rotas.Sequencia\n");
            sql.append("	from #tmpRoteirizarAuto a\n");
            sql.append("	Left join Pedido (nolock)  on Pedido.Numero = a.Pedido\n");
            sql.append("								and Pedido.CodFil = a.Codfil\n");
            sql.append("	Inner join Rotas (nolock) on Rotas.Rota = a.Rota\n");
            sql.append("							and Rotas.Data = Pedido.Data\n");
            sql.append("							and Rotas.CodFil = a.CodFil\n");
            sql.append("	Left join Clientes CliOri (nolock)  on CliOri.Codigo = Pedido.CodCli1\n");
            sql.append("										and CliOri.CodFil = Pedido.CodFil\n");
            sql.append("	Left join Clientes CliDst (nolock)  on CliDst.Codigo = Pedido.CodCli2\n");
            sql.append("										and CliDst.CodFil = Pedido.CodFil\n");
            sql.append("	where Pedido.Situacao = 'PD'\n");
            sql.append("	order by a.Hora, CliOri.Regiao\n");
            sql.append("	\n");
            sql.append("	set @operador = Substring('Ger-'+@operador,1,10)\n");
            sql.append("\n");
            sql.append("	if(len(@cxforte) <= 1) begin\n");
            sql.append("		Select top 1 \n");
            sql.append("		@cxforte = Clientes.Codigo,\n");
            sql.append("		@cxforteNred = Clientes.Nred,\n");
            sql.append("		@cxforteRegiao = Clientes.Regiao\n");
            sql.append("		from CxForte \n");
            sql.append("		Left join Clientes  on Clientes.Codigo = CxForte.CodCli\n");
            sql.append("						   and Clientes.CodFil = CxForte.CodFil\n");
            sql.append("		where CxForte.CodFil = @codfil \n");
            sql.append("		Order By DtFecha Desc\n");
            sql.append("	end\n");
            sql.append("\n");
            sql.append("	Select @cxfExiste = Isnull(Count(*),0) from Rt_Perc where Sequencia = @SeqRota and CodCli1 = @cxforte and Hora1 >= '1100' and Hora1 <= '1300' and Flag_Excl <> '*'		\n");
            sql.append("	if(@cxfExiste <= 0) begin\n");
            sql.append("\n");
            sql.append("		WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @horaCxfManha and Flag_Excl <> '*') > 0 BEGIN\n");
            sql.append("			set @horaCxfManha = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@horaCxfManha,1,2)+':'+Substring(@horaCxfManha,3,2))),113),1,5),':','')\n");
            sql.append("	  \n");
            sql.append("			IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @horaCxfManha and Flag_Excl <> '*') = 0\n");
            sql.append("				BREAK  \n");
            sql.append("		ELSE  \n");
            sql.append("			CONTINUE  \n");
            sql.append("		END\n");
            sql.append("\n");
            sql.append("		Select @seqParadaCxf = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota					\n");
            sql.append("		Insert into Rt_Perc (Sequencia, Hora1, Parada, CodCli1, Nred, Regiao, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n");
            sql.append("		Values (@seqRota, @horaCxfManha, @seqParadaCxf, @cxforte, @cxforteNred, @cxforteRegiao, 'E', 'R', @codfil, @operador,@dt_alter,@hr_alter, '', '', '',@observ)	\n");
            sql.append("	end\n");
            sql.append("\n");
            sql.append("	Select @cxfExiste = Isnull(Count(*),0) from Rt_Perc where Sequencia = @SeqRota and CodCli1 = @cxforte and Hora1 >= '1700' and Hora1 <= '2000' and Flag_Excl <> '*'\n");
            sql.append("	if(@cxfExiste <= 0) begin\n");
            sql.append("\n");
            sql.append("		WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @horaCxfTarde and Flag_Excl <> '*') > 0 BEGIN\n");
            sql.append("      \n");
            sql.append("			set @horaCxfTarde = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@horaCxfTarde,1,2)+':'+Substring(@horaCxfTarde,3,2))),113),1,5),':','')\n");
            sql.append("	  \n");
            sql.append("			IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @horaCxfTarde and Flag_Excl <> '*') = 0\n");
            sql.append("				BREAK  \n");
            sql.append("		ELSE  \n");
            sql.append("			CONTINUE  \n");
            sql.append("		END\n");
            sql.append("\n");
            sql.append("		Select @seqParadaCxf = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota					\n");
            sql.append("		Insert into Rt_Perc (Sequencia, Hora1, Parada, CodCli1, Nred, Regiao, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n");
            sql.append("		Values (@seqRota, @horaCxfTarde, @seqParadaCxf, @cxforte, @cxforteNred, @cxforteRegiao, 'E', 'R', @codfil, @operador,@dt_alter,@hr_alter, '', '', '',@observ)	\n");
            sql.append("	end\n");
            sql.append("\n");
            sql.append("\n");
            sql.append("	set @hora1 = Replace(@hora1,':','')\n");
            sql.append("	set @hora2 = Replace(@hora2,':','')\n");
            sql.append("\n");
            sql.append("	set @tipo = 'R'\n");
            sql.append("	if(@codcli1 = @cxforte)\n");
            //sql.append("		set @tipo = 'E'\n");
            sql.append("	if(substring(@codcli1,4,1) = '7')begin\n");
            sql.append("		set @tipo = 'E'		\n");
            sql.append("		set @codcli1 = @cxforte\n");
            sql.append("		set @nredOri = @cxforteNred\n");
            sql.append("		set @regiaoOri = @cxforteRegiao\n");
            sql.append("	end	\n");
            sql.append("\n");
            sql.append("	if(substring(@codcli2,4,1) = '7')begin\n");
            sql.append("		set @codcli2 = @cxforte\n");
            sql.append("		set @nredDst = @cxforteNred\n");
            sql.append("		set @regiaoDst = @cxforteRegiao\n");
            sql.append("	end\n");
            sql.append("	WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @hora1 and Flag_Excl <> '*') > 0 BEGIN\n");
            sql.append("      \n");
            sql.append("		set @hora1 = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@hora1,1,2)+':'+Substring(@hora1,3,2))),113),1,5),':','')\n");
            sql.append("	  \n");
            sql.append("		IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @hora1 and Flag_Excl <> '*') = 0\n");
            sql.append("			BREAK  \n");
            sql.append("	ELSE  \n");
            sql.append("		CONTINUE  \n");
            sql.append("	END\n");
            sql.append("\n");
            sql.append("	if(@tipo = 'R') begin\n");
            sql.append("		\n");
            sql.append("		set @paradaX = 0\n");
            sql.append("		set @hrX = ''\n");
            sql.append("		Select top 01 	  \n");
            sql.append("		@hrX = Rt_Perc.Hora1, \n");
            sql.append("		@paradaX = Rt_Perc.Parada \n");
            sql.append("		from Rt_Perc (Nolock)\n");
            sql.append("		where Rt_Perc.Sequencia = @seqRota\n");
            sql.append("			and Rt_Perc.CodCli1 = @codcli2\n");
            sql.append("			and Rt_Perc.Hora1 > Replace(@hora1,':','')\n");
            sql.append("			and Rt_Perc.Flag_Excl <> '*'\n");
            sql.append("		order by Rt_Perc.Hora1\n");
            sql.append("		Select @seqParadaOri = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota\n");
            sql.append("\n");
            sql.append("		Insert into Rt_Perc (Sequencia, Hora1, Parada, CodFil,  ER, TipoSrv, CodCli1, NRed, Regiao, Valor, \n");
            sql.append("		Pedido, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, CodCli2, Hora1D, Observ, OS)\n");
            sql.append("		values (@seqRota,@hora1,@seqParadaOri,@codfil, @tipo, @classifSrv, @codcli1, \n");
            sql.append("		@nredOri, @regiaoOri, @valor, @pedido, @operador, @dt_alter, @hr_alter, '','','', @codcli2, @hrX, @observ, @os)\n");
            sql.append("\n");
            sql.append("		if(len(@hrX) <= 1) begin\n");
            sql.append("			\n");
            sql.append("                            if(@hora1 > @hora2)\n");
            sql.append("				set @hora2 = @hora1\n");
            sql.append("			--Buscando Hora1 Disponivel\n");
            sql.append("			WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @hora2 and Flag_Excl <> '*') > 0 begin\n");
            sql.append("      \n");
            sql.append("				set @hora2 = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@hora2,1,2)+':'+Substring(@hora2,3,2))),113),1,5),':','')\n");
            sql.append("	  \n");
            sql.append("				IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @hora2 and Flag_Excl <> '*') = 0\n");
            sql.append("					BREAK  \n");
            sql.append("			ELSE  \n");
            sql.append("				CONTINUE  \n");
            sql.append("			END\n");
            sql.append("			\n");
            sql.append("			Select @seqParadaDst = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota					\n");
            sql.append("			Insert into Rt_Perc (Sequencia, Hora1, CodCli1, Nred, Regiao, Parada, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n");
            sql.append("			Values (@seqRota, @hora2,@codcli2, @nredDst, @regiaoDst, @seqParadaDst, 'E', @classifsrv, @codfil, @operador,@dt_alter,@hr_alter, '', '', '',@observ)	\n");
            sql.append("\n");
            sql.append("			set @hrX = @hora2\n");
            sql.append("			set @paradaX = @seqParadaDst\n");
            sql.append("		end\n");
            sql.append("\n");
            sql.append("		Update Rt_Perc set Hora1D = @hrX, dPar = @paradaX where Sequencia = @seqRota and Parada = @seqParadaOri\n");
            sql.append("	\n");
            sql.append("	end\n");
            sql.append("	\n");
            sql.append("	if(@tipo = 'E') begin\n");
            sql.append("\n");
            sql.append("		Select @seqParadaOri = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota\n");
            sql.append("\n");
            sql.append("		Insert into Rt_Perc (Sequencia, Hora1, Parada, CodFil,  ER, TipoSrv, CodCli1, NRed, Regiao, Valor, \n");
            sql.append("		Pedido, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, OS, Observ)\n");
            sql.append("		values (@seqRota,@hora1,@seqParadaOri,@codfil, @tipo, @classifSrv, @codcli2, \n");
            sql.append("		@nredDst, @regiaoDst, @valor, @pedido, @operador, @dt_alter, @hr_alter, '','','', @os, @observ)\n");
            sql.append("\n");
            sql.append("if((Select isnull(Count(*),0) qtde from Clientes where codigo = @codcli2 and CodFil = @codfil and Retorno = 'S') > 0) begin \n");
            sql.append("			Select @seqParadaOri = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota\n");
            sql.append("			set @codcli1 = @cxforte\n");
            sql.append("			set @nredOri = @cxforteNred\n");
            sql.append("			set @regiaoOri = @cxforteRegiao\n");
            sql.append("\n");
            sql.append("			Select top 01 	  \n");
            sql.append("			@hrX = Rt_Perc.Hora1, \n");
            sql.append("			@paradaX = Rt_Perc.Parada \n");
            sql.append("			from Rt_Perc (Nolock)\n");
            sql.append("			where Rt_Perc.Sequencia = @seqRota\n");
            sql.append("				and Rt_Perc.CodCli1 = @codcli1\n");
            sql.append("				--and Rt_Perc.ER = 'E'\n");
            sql.append("				and Rt_Perc.Hora1 > Replace(@hora1,':','')\n");
            sql.append("				and Rt_Perc.Flag_Excl <> '*'\n");
            sql.append("			order by Rt_Perc.Hora1\n");
            sql.append("\n");
            sql.append("			set @hora1 = Replace(Substring(Convert(Varchar,DateAdd(Minute,1,Convert(time,Substring(@hora1,1,2)+':'+Substring(@hora1,3,2))),113),1,5),':','')\n");
            
            sql.append("			Insert into Rt_Perc (Sequencia, Hora1, Parada, CodFil,  ER, TipoSrv, CodCli1, NRed, Regiao, Valor, \n");
            sql.append("			Pedido, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, CodCli2, Hora1D, Observ, OS)\n");
            sql.append("			values (@seqRota,@hora1,@seqParadaOri,@codfil, 'R', @classifSrv, @codcli2, @nredDst, @regiaoDst,\n");
            sql.append("			0, '', @operador, @dt_alter, @hr_alter, '','','', @codcli1, @hrX, @observ, @os)\n");
            sql.append("\n");

            sql.append("			if(len(@hrX) <= 1) begin\n");
            sql.append("				\n");
            sql.append("				if(@hora1 > @hora2)\n");
            sql.append("					set @hora2 = @hora1\n");
            sql.append("			\n");

            sql.append("				WHILE (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @hora2 and Flag_Excl <> '*') > 0 begin\n");
            sql.append("      \n");
            
            sql.append("					set @hora2 = Replace(Substring(Convert(Varchar,DateAdd(Minute,@acrescimoMinutos,Convert(time,Substring(@hora2,1,2)+':'+Substring(@hora2,3,2))),113),1,5),':','')\n");
            sql.append("	  \n");
            sql.append("					IF (SELECT count(*) FROM Rt_Perc (nolock) where Sequencia = @seqRota and Hora1 = @hora2 and Flag_Excl <> '*') = 0\n");
            sql.append("						BREAK  \n");
            sql.append("				ELSE  \n");
            sql.append("					CONTINUE  \n");
            sql.append("				END\n");
            sql.append("			\n");
            
            sql.append("				Select @seqParadaDst = isnull(Max(Parada),0)+1 from Rt_Perc where Sequencia = @seqRota;					\n");
            sql.append("				Insert into Rt_Perc (Sequencia, Hora1, CodCli1, Nred, Regiao, Parada, ER, TipoSrv, CodFil, OperIncl, Dt_Incl, Hr_Incl, OperFech, HrBaixa, Flag_Excl, Observ) \n");
            sql.append("				Values (@seqRota, @hora2,@codcli2, @nredDst, @regiaoDst, @seqParadaDst, 'E', @classifsrv, @codfil, @operador,@dt_alter,@hr_alter, '', '', '',@observ);	\n");
            sql.append("\n");
            sql.append("				set @hrX = @hora2\n");
            sql.append("				set @paradaX = @seqParadaDst\n");
            sql.append("			end\n");
            sql.append("\n");

            sql.append("			Update Rt_Perc set Hora1D = @hrX, dPar = @paradaX where Sequencia = @seqRota and Parada = @seqParadaOri;			\n");
            sql.append("		end            \n");
            sql.append("	end\n");
            sql.append("	\n");
            sql.append("	Update Pedido set \n");
            sql.append("		Situacao = 'OK',\n");
            sql.append("		SeqRota  = @seqRota,\n");
            sql.append("		Parada   = @seqParadaOri,\n");
            sql.append("		Operador = @operador,\n");
            sql.append("		Dt_Alter = @dt_alter,\n");
            sql.append("		Hr_Alter = @hr_alter\n");
            sql.append("	where Numero = @pedido\n");
            sql.append("	  and Codfil = @codfil\n");
            sql.append("\n");
            sql.append("	delete from #tmpRoteirizarAuto where pedido = @pedido and codfil = @codfil\n");
            sql.append("\n");
            sql.append("	IF (SELECT count(*) FROM #tmpRoteirizarAuto) = 0\n");
            sql.append("		BREAK  \n");
            sql.append("	ELSE  \n");
            sql.append("		CONTINUE  \n");
            sql.append("END\n");
            sql.append("Drop table #tmpRoteirizarAuto\n");
            Consulta consulta = new Consulta(sql.toString(), persistencia);

            for (Pedido pedido : pedidos) {
                consulta.setBigDecimal(pedido.getNumero());
                consulta.setBigDecimal(pedido.getCodFil());
                consulta.setString(pedido.getRota());
                consulta.setString(formatarHoraSQL(pedido.getHora1O()));
                consulta.setString(RecortaAteEspaço(pedido.getOperador(), 0, 10));
                consulta.setString(getDataAtual("SQL"));
                consulta.setString(getDataAtual("HORA"));
            }

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RoteirizarDao.roteirizar - " + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.Siates.SiatesSatWeb;
import Dados.Persistencia;
import SasBeans.Siates;
import SasBeans.XMLSiates.Tipo2;
import br.com.sasw.esocial.SocketFactoryDinamico;
import br.com.sasw.lazydatamodels.SiatesLazyList;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.Arquivoxml_type0;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.AuthenticationInfo;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.AuthenticationInfoE;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.Chamado_caixa_type0;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.Info_arquivo_type0;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.Info_fornecedor_type0;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.InputMapping6;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.Retorno_type0;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.SetAceiteRecusa;
import br.com.sasw.managedBeans.recursoshumanos.GSC_RF010_FornecedorExterno_V401_WSServiceStub.SetAceiteRecusaResponse;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.utils.Messages;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.net.URL;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import org.apache.commons.httpclient.protocol.Protocol;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;

/**
 *
 * <AUTHOR>
 */
@Named(value = "siates")
@ViewScoped
public class SiatesMB implements Serializable {

    private final SiatesSatWeb siatesWeb;
    private Siates chamadoSelecionado;
    private final ArquivoLog logerro;
    private final String caminho, operador, filial, banco;
    private String codfil, log, flag, descricao;
    private Date previsaoAntendimento;
    private final BigDecimal codPessoa;
    private Persistencia persistencia;
    private Map filters;
    private int total;
    private LazyDataModel<Siates> chamados = null;
    private Tipo2 aceitaRecusaChamado;
    private final SimpleDateFormat formatExibicao, formatEnvio;
    private final DateTimeFormatter dtf;

    public SiatesMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codfil = (String) fc.getExternalContext().getSessionMap().get("filial");
        filial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        formatExibicao = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        formatEnvio = new SimpleDateFormat("yyyyMMddHHmmss");
        dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssxxx");
        siatesWeb = new SiatesSatWeb();
    }

    public void persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.filters = new HashMap();
            this.filters.put(" pstserv.tipoposto like ? ", "");
            this.filters.put(" pstserv.secao like ? ", "");
            this.filters.put(" pstserv.codcli like ? ", "");
            this.filters.put(" pstserv.local like ? ", "");
            this.filters.put(" ctritens.descricao like ? ", "");
            this.filters.put(" ctritens.contrato like ? ", "");
            this.total = this.siatesWeb.contagem(this.filters, this.persistencia);

            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(getClass().getResourceAsStream("interfort"), "Inter2018".toCharArray());

            String alias = "";
            Enumeration<String> aliasesEnum = ks.aliases();
            while (aliasesEnum.hasMoreElements()) {
                alias = (String) aliasesEnum.nextElement();
                if (ks.isKeyEntry(alias)) {
                    break;
                }
            }

            X509Certificate certificate = (X509Certificate) ks.getCertificate(alias);
            PrivateKey privateKey = (PrivateKey) ks.getKey(alias, "Inter2018".toCharArray());
            SocketFactoryDinamico socketFactoryDinamico = new SocketFactoryDinamico(certificate, privateKey);
            URL cacert = getClass().getResource("siates");
            socketFactoryDinamico.setFileCacerts(cacert.getPath().replace("%20", " "));

            Protocol protocol = new Protocol("https", socketFactoryDinamico, 443);
            Protocol.registerProtocol("https", protocol);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<Siates> getAllChamados() {
        if (this.chamados == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            this.filters.replace("clientes.codFil = ?", "");
            dt.setFilters(this.filters);
            this.chamados = new SiatesLazyList(this.siatesWeb, this.persistencia);
        }
        try {
            this.total = this.siatesWeb.contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.chamados;
    }

    public void rowDblselectTabela(SelectEvent event) {
        this.chamadoSelecionado = (Siates) event.getObject();
        abrirChamado();
    }

    public void abrirChamado() {
        if (null == this.chamadoSelecionado) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneChamado"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            PrimeFaces.current().executeScript("PF('dlgCadastrar').show()");
        }
    }

    public void preparaChamado(String aceiteRecusa) {
        this.flag = aceiteRecusa;
        this.descricao = "";
        try {
            LocalDateTime tempo
                    = LocalDateTime.parse(this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getSolicitacao().getDetalhes().getDetalhe6(),
                            this.dtf);
            this.previsaoAntendimento = Date.from(tempo.atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            this.previsaoAntendimento = new Date();
        }
        PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
        PrimeFaces.current().executeScript("PF('dlgAceitarRecusar').show();");
    }

    public void onPrevisaoAtendimentoSelecionada(SelectEvent event) {
        this.previsaoAntendimento = (Date) event.getObject();
    }

    public void confirmarChamado() {
        this.aceitaRecusaChamado = new Tipo2(new Tipo2.ArquivoXml(
                new Tipo2.Info_arquivo("2",
                        this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_arquivo().getIdarquivo(),
                        this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_arquivo().getDatahorageracaoarquivo(),
                        this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_arquivo().getComunicacao()),
                new Tipo2.Info_fornecedor(this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_fornecedor().getIdfornecedor(),
                        this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_fornecedor().getNomefornecedor()),
                new Tipo2.Retorno(this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_solicitante().getCodigodobanco(),
                        new Tipo2.Chamado_caixa(this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_demandante().getChamado_caixa().getNo_req(),
                                this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_demandante().getChamado_caixa().getNo_wo(),
                                this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_demandante().getChamado_caixa().getNo_inc(),
                                this.chamadoSelecionado.getXMLRetorno().getArquivoxml().getInfo_demandante().getChamado_caixa().getNo_crq()),
                        this.flag,
                        this.chamadoSelecionado.getSequencia().replace(".0", ""),
                        this.formatEnvio.format(this.previsaoAntendimento),
                        this.operador,
                        this.descricao)));

        PrimeFaces.current().executeScript("PF('dlgAceitarRecusar').hide();");
        PrimeFaces.current().executeScript("PF('dlgConfirmarChamado').show();");
    }

    public void processarChamado() {
        try {
            AuthenticationInfo authenticationInfo = new AuthenticationInfo();
            authenticationInfo.setUserName("CETEC72");
            authenticationInfo.setPassword("7562-CETEC");

            AuthenticationInfoE authenticationInfoE = new AuthenticationInfoE();
            authenticationInfoE.setAuthenticationInfo(authenticationInfo);

            GSC_RF010_FornecedorExterno_V401_WSServiceStub stub = new GSC_RF010_FornecedorExterno_V401_WSServiceStub();

            SetAceiteRecusa aceiteRecusa = new SetAceiteRecusa();
            InputMapping6 inputMapping6 = new InputMapping6();

            Arquivoxml_type0 arquivoxml_type0 = new Arquivoxml_type0();

            //Tipo de layout 1-Abertura do Chamado; 2- Aceite/Recusa; 3 - Atualização; 4-Retorno para fornecedor; 5- Complementação/Reiteração
            Info_arquivo_type0 info_arquivo_type0 = new Info_arquivo_type0();
            info_arquivo_type0.setTipoarquivo(this.aceitaRecusaChamado.getArquivoxml().getInfo_arquivo().getTipoarquivo());
            info_arquivo_type0.setIdarquivo(this.aceitaRecusaChamado.getArquivoxml().getInfo_arquivo().getIdarquivo());
            info_arquivo_type0.setDatahorageracaoarquivo(this.aceitaRecusaChamado.getArquivoxml().getInfo_arquivo().getDatahorageracaoarquivo());
            info_arquivo_type0.setComunicacao(this.aceitaRecusaChamado.getArquivoxml().getInfo_arquivo().getComunicacao());

            Info_fornecedor_type0 info_fornecedor_type0 = new Info_fornecedor_type0();
            info_fornecedor_type0.setIdfornecedor(this.aceitaRecusaChamado.getArquivoxml().getInfo_fornecedor().getIdfornecedor());
            info_fornecedor_type0.setNomefornecedor(this.aceitaRecusaChamado.getArquivoxml().getInfo_fornecedor().getNomefornecedor());

            Chamado_caixa_type0 localChamado_caixa = new Chamado_caixa_type0();
            localChamado_caixa.setNo_req(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getChamado_caixa().getNo_req());
            localChamado_caixa.setNo_wo(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getChamado_caixa().getNo_wo());
            localChamado_caixa.setNo_inc(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getChamado_caixa().getNo_inc());
            localChamado_caixa.setNo_crq(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getChamado_caixa().getNo_crq());

            Retorno_type0 retorno_type0 = new Retorno_type0();
            retorno_type0.setCodigodobanco(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getCodigodobanco());
            retorno_type0.setChamado_caixa(localChamado_caixa);
            retorno_type0.setTipo_retorno(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getTipo_retorno());
            retorno_type0.setChamado_fornecedor(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getChamado_fornecedor());
            retorno_type0.setPrevisaoatendimento(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getPrevisaoatendimento());
            retorno_type0.setResponsavelatendimento(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getResponsavelatendimento());
            retorno_type0.setDescricao(this.aceitaRecusaChamado.getArquivoxml().getRetorno().getDescricao());

            arquivoxml_type0.setInfo_arquivo(info_arquivo_type0);
            arquivoxml_type0.setInfo_fornecedor(info_fornecedor_type0);
            arquivoxml_type0.setRetorno(retorno_type0);

            inputMapping6.setArquivoxml(arquivoxml_type0);
            aceiteRecusa.setSetAceiteRecusa(inputMapping6);

            SetAceiteRecusaResponse aceiteRecusaResponse = stub.setAceiteRecusa(aceiteRecusa, authenticationInfoE);
            aceiteRecusaResponse.getSetAceiteRecusaResponse();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public Siates getChamadoSelecionado() {
        return chamadoSelecionado;
    }

    public void setChamadoSelecionado(Siates chamadoSelecionado) {
        this.chamadoSelecionado = chamadoSelecionado;
    }

    public String getFilial() {
        return filial;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getPrevisaoAntendimentoTexto() {
        return this.formatExibicao.format(this.previsaoAntendimento);
    }

    public Date getPrevisaoAntendimento() {
        return previsaoAntendimento;
    }

    public void setPrevisaoAntendimento(Date previsaoAntendimento) {
        this.previsaoAntendimento = previsaoAntendimento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Tipo2 getAceitaRecusaChamado() {
        return aceitaRecusaChamado;
    }

    public void setAceitaRecusaChamado(Tipo2 aceitaRecusaChamado) {
        this.aceitaRecusaChamado = aceitaRecusaChamado;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

}

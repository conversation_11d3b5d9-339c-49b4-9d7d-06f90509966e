/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class XMLGtve {

    public String CNPJ;
    public String Guia;
    public String Serie;
    public String CodCidade;
    public String Dt_GTVE;
    public String Hr_GTVE;
    public String XML_Envio;
    public String XML_Retorno;
    public String Protocolo;
    public String ChaveGTVE;
    public String Dt_Envio;
    public String Hr_Envio;
    public String Dt_Retorno;
    public String Hr_Retorno;
    public String Link;

    public int FatIdGtv;
    public String OpenIdGtv;
    public int OpenIdFilial;
    public String Status;
    public String Dt_EnvioProsegur;
    public String Hr_EnvioProsegur;
    public String RetornoProsegur;
    
    public String AssinaRemetente;
    public String AssinaDestinatario;
    public String AssinaRemetenteImagem;
    public String AssinaDestinatarioImagem;

    public XMLGtve() {

    }

    public XMLGtve(String _CNPJ,
            String _Guia,
            String _Serie,
            String _CodCidade,
            String _Dt_GTVE,
            String _Hr_GTVE,
            String _XML_Envio,
            String _XML_Retorno,
            String _Protocolo,
            String _ChaveGTVE,
            String _Dt_Envio,
            String _Hr_Envio,
            String _Dt_Retorno,
            String _Hr_Retorno,
            String _Link,
            int _FatIdGtv,
            String _OpenIdGtv,
            int _OpenIdFilial) {
        CNPJ = _CNPJ;
        Guia = _Guia;
        Serie = _Serie;
        CodCidade = _CodCidade;
        Dt_GTVE = _Dt_GTVE;
        Hr_GTVE = _Hr_GTVE;
        XML_Envio = _XML_Envio;
        XML_Retorno = _XML_Retorno;
        Protocolo = _Protocolo;
        ChaveGTVE = _ChaveGTVE;
        Dt_Envio = _Dt_Envio;
        Hr_Envio = _Hr_Envio;
        Dt_Retorno = _Dt_Retorno;
        Hr_Retorno = _Hr_Retorno;
        Link = _Link;
        FatIdGtv = _FatIdGtv;
        OpenIdGtv = _OpenIdGtv;
        OpenIdFilial = _OpenIdFilial;
    }

    public XMLGtve(String _CNPJ,
            String _Guia,
            String _Serie,
            String _CodCidade,
            String _Dt_GTVE,
            String _Hr_GTVE,
            String _XML_Envio,
            String _XML_Retorno,
            String _Protocolo,
            String _ChaveGTVE,
            String _Dt_Envio,
            String _Hr_Envio,
            String _Dt_Retorno,
            String _Hr_Retorno,
            String _Link) {
        CNPJ = _CNPJ;
        Guia = _Guia;
        Serie = _Serie;
        CodCidade = _CodCidade;
        Dt_GTVE = _Dt_GTVE;
        Hr_GTVE = _Hr_GTVE;
        XML_Envio = _XML_Envio;
        XML_Retorno = _XML_Retorno;
        Protocolo = _Protocolo;
        ChaveGTVE = _ChaveGTVE;
        Dt_Envio = _Dt_Envio;
        Hr_Envio = _Hr_Envio;
        Dt_Retorno = _Dt_Retorno;
        Hr_Retorno = _Hr_Retorno;
        Link = _Link;
    }

    public String getCNPJ() {
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getCodCidade() {
        return CodCidade;
    }

    public void setCodCidade(String CodCidade) {
        this.CodCidade = CodCidade;
    }

    public String getDt_GTVE() {
        return Dt_GTVE;
    }

    public void setDt_GTVE(String Dt_GTVE) {
        this.Dt_GTVE = Dt_GTVE;
    }

    public String getHr_GTVE() {
        return Hr_GTVE;
    }

    public void setHr_GTVE(String Hr_GTVE) {
        this.Hr_GTVE = Hr_GTVE;
    }

    public String getXML_Envio() {
        return XML_Envio;
    }

    public void setXML_Envio(String XML_Envio) {
        this.XML_Envio = XML_Envio;
    }

    public String getXML_Retorno() {
        return XML_Retorno;
    }

    public void setXML_Retorno(String XML_Retorno) {
        this.XML_Retorno = XML_Retorno;
    }

    public String getProtocolo() {
        return Protocolo;
    }

    public void setProtocolo(String Protocolo) {
        this.Protocolo = Protocolo;
    }

    public String getChaveGTVE() {
        return ChaveGTVE;
    }

    public void setChaveGTVE(String ChaveGTVE) {
        this.ChaveGTVE = ChaveGTVE;
    }

    public String getDt_Envio() {
        return Dt_Envio;
    }

    public void setDt_Envio(String Dt_Envio) {
        this.Dt_Envio = Dt_Envio;
    }

    public String getHr_Envio() {
        return Hr_Envio;
    }

    public void setHr_Envio(String Hr_Envio) {
        this.Hr_Envio = Hr_Envio;
    }

    public String getDt_Retorno() {
        return Dt_Retorno;
    }

    public void setDt_Retorno(String Dt_Retorno) {
        this.Dt_Retorno = Dt_Retorno;
    }

    public String getHr_Retorno() {
        return Hr_Retorno;
    }

    public void setHr_Retorno(String Hr_Retorno) {
        this.Hr_Retorno = Hr_Retorno;
    }

    public String getLink() {
        return Link;
    }
    
    public void setLink(String Link) {
        this.Link = Link;
    }

    public int getFatIdGtv() {
        return FatIdGtv;
    }

    public void setFatIdGtv(int FatIdGtv) {
        this.FatIdGtv = FatIdGtv;
    }

    public String getOpenIdGtv() {
        return OpenIdGtv;
    }

    public void setOpenIdGtv(String OpenIdGtv) {
        this.OpenIdGtv = OpenIdGtv;
    }

    public int getOpenIdFilial() {
        return OpenIdFilial;
    }

    public void setOpenIdFilial(int OpenIdFilial) {
        this.OpenIdFilial = OpenIdFilial;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String Status) {
        this.Status = Status;
    }

    public String getDt_EnvioProsegur() {
        return Dt_EnvioProsegur;
    }

    public void setDt_EnvioProsegur(String Dt_EnvioProsegur) {
        this.Dt_EnvioProsegur = Dt_EnvioProsegur;
    }

    public String getHr_EnvioProsegur() {
        return Hr_EnvioProsegur;
    }

    public void setHr_EnvioProsegur(String Hr_EnvioProsegur) {
        this.Hr_EnvioProsegur = Hr_EnvioProsegur;
    }

    public String getRetornoProsegur() {
        return RetornoProsegur;
    }

    public void setRetornoProsegur(String RetornoProsegur) {
        this.RetornoProsegur = RetornoProsegur;
    }

    public String getAssinaRemetente() {
        return AssinaRemetente;
    }

    public void setAssinaRemetente(String AssinaRemetente) {
        this.AssinaRemetente = AssinaRemetente;
    }

    public String getAssinaDestinatario() {
        return AssinaDestinatario;
    }

    public void setAssinaDestinatario(String AssinaDestinatario) {
        this.AssinaDestinatario = AssinaDestinatario;
    }

    public String getAssinaRemetenteImagem() {
        return AssinaRemetenteImagem;
    }

    public void setAssinaRemetenteImagem(String AssinaRemetenteImagem) {
        this.AssinaRemetenteImagem = AssinaRemetenteImagem;
    }

    public String getAssinaDestinatarioImagem() {
        return AssinaDestinatarioImagem;
    }

    public void setAssinaDestinatarioImagem(String AssinaDestinatarioImagem) {
        this.AssinaDestinatarioImagem = AssinaDestinatarioImagem;
    }
}

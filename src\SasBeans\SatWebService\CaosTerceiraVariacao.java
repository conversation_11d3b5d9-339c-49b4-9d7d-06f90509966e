/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans.SatWebService;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CaosTerceiraVariacao {

    public String accountingDate;
    public String User;
    public String UserName;
    public String UserLevel;
    public String DeviceID;
    public String CustomerCode;
    public CaosCount destDetails;
    public String Time;
    public String Date;
    public Double NOP;
    public Double TransactionID;
    public CaosDetails Details;
    public String operation;
    public String UserOrganization;
    public String TransactionInfo;
    

    public CaosTerceiraVariacao() {
        destDetails = new CaosCount();
        Details = new CaosDetails();
    }

    public class CaosCount {

        public List<CaosDestDetails> count;
        
        public CaosCount(){
            count = new ArrayList<>();
        }

        public List<CaosDestDetails> getCount() {
            return count;
        }

        public void setCount(List<CaosDestDetails> count) {
            this.count = count;
        }
    }

    public class CaosDestDetails {

        public String curr;
        public String sType;
        public Double qty;
        public Double den;
        public String type;
        public Double N;

        public String getCurr() {
            return curr;
        }

        public String getsType() {
            return sType;
        }

        public Double getQty() {
            return qty;
        }

        public Double getDen() {
            return den;
        }

        public String getType() {
            return type;
        }

        public Double getN() {
            return N;
        }        
        
        public void setCurr(String curr) {
            this.curr = curr;
        }

        public void setsType(String sType) {
            this.sType = sType;
        }

        public void setQty(Double qty) {
            this.qty = qty;
        }

        public void setDen(Double den) {
            this.den = den;
        }

        public void setType(String type) {
            this.type = type;
        }

        public void setN(Double N) {
            this.N = N;
        }
    }

    public class CaosDetails {

        public String Account;
        public String Currency;
        public Double total;
        public CaosDetailsCountings countings;
        
        public CaosDetails(){
            countings = new CaosDetailsCountings();
        }

        public String getAccount() {
            return Account;
        }

        public void setAccount(String Account) {
            this.Account = Account;
        }

        public String getCurrency() {
            return Currency;
        }

        public void setCurrency(String Currency) {
            this.Currency = Currency;
        }

        public Double getTotal() {
            return total;
        }

        public void setTotal(Double total) {
            this.total = total;
        }

        
        public CaosDetailsCountings getCountings() {
            return countings;
        }

        public void setCountings(CaosDetailsCountings countings) {
            this.countings = countings;
        }
    }

    public class CaosDetailsCountings {

        public Double valid;
        public List<CaosDetailsCounted> counted;
        
        public CaosDetailsCountings(){
            counted = new ArrayList<>();
        }

        public Double getValid() {
            return valid;
        }

        public void setValid(Double valid) {
            this.valid = valid;
        }

        public List<CaosDetailsCounted> getCounted() {
            return counted;
        }

        public void setCounted(List<CaosDetailsCounted> counted) {
            this.counted = counted;
        }        
    }

    public class CaosDetailsCounted {

        public Double quantity;
        public Double denom;
        public String type;

        public Double getQuantity() {
            return quantity;
        }

        public void setQuantity(Double quantity) {
            this.quantity = quantity;
        }

        public Double getDenom() {
            return denom;
        }

        public void setDenom(Double denom) {
            this.denom = denom;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    public class CaosCheques {

        public CaosCheque cheque;
        
        public CaosCheques(){
            cheque = new CaosCheque();
        }

        public CaosCheque getCheque() {
            return cheque;
        }

        public void setCheque(CaosCheque cheque) {
            this.cheque = cheque;
        }
    }

    public class CaosCheque {

        public Double amount;
        public String curr;
        public String codeline;

        public Double getAmount() {
            return amount;
        }

        public void setAmount(Double amount) {
            this.amount = amount;
        }

        public String getCurr() {
            return curr;
        }

        public void setCurr(String curr) {
            this.curr = curr;
        }

        public String getCodeline() {
            return codeline;
        }

        public void setCodeline(String codeline) {
            this.codeline = codeline;
        }
    }
    
    public String getUser() {
        return User;
    }

    public void setUser(String User) {
        this.User = User;
    }

    public String getCustomerCode() {
        return CustomerCode;
    }

    public void setCustomerCode(String CustomerCode) {
        this.CustomerCode = CustomerCode;
    }

    public CaosCount getDestDetails() {
        return destDetails;
    }

    public void setDestDetails(CaosCount destDetails) {
        this.destDetails = destDetails;
    }

    public String getTime() {
        return Time;
    }

    public void setTime(String Time) {
        this.Time = Time;
    }


    public String getDate() {
        return Date;
    }

    public void setDate(String Date) {
        this.Date = Date;
    }

    public CaosDetails getDetails() {
        return Details;
    }

    public void setDetails(CaosDetails Details) {
        this.Details = Details;
    }

    public String getUserOrganization() {
        return UserOrganization;
    }

    public void setUserOrganization(String UserOrganization) {
        this.UserOrganization = UserOrganization;
    }

    public String getTransactionInfo() {
        return TransactionInfo;
    }

    public void setTransactionInfo(String TransactionInfo) {
        this.TransactionInfo = TransactionInfo;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getDeviceID() {
        return DeviceID;
    }

    public void setDeviceID(String DeviceID) {
        this.DeviceID = DeviceID;
    }

    /*public Double getShiftNumber() {
        return shiftNumber;
    }

    public void setShiftNumber(Double shiftNumber) {
        this.shiftNumber = shiftNumber;
    }*/

    /*public Double getChannelID() {
        return ChannelID;
    }

    public void setChannelID(Double ChannelID) {
        this.ChannelID = ChannelID;
    }

    public Double getDepositDuration() {
        return DepositDuration;
    }

    public void setDepositDuration(Double DepositDuration) {
        this.DepositDuration = DepositDuration;
    }*/

    public Double getNOP() {
        return NOP;
    }

    public void setNOP(Double NOP) {
        this.NOP = NOP;
    }

    public Double getTransactionID() {
        return TransactionID;
    }

    public void setTransactionID(Double TransactionID) {
        this.TransactionID = TransactionID;
    }

    /*public Double getBagID() {
        return bagID;
    }

    public void setBagID(Double bagID) {
        this.bagID = bagID;
    }*/

    public String getAccountingDate() {
        return accountingDate;
    }

    public void setAccountingDate(String accountingDate) {
        this.accountingDate = accountingDate;
    }

    public String getUserName() {
        return UserName;
    }

    public void setUserName(String UserName) {
        this.UserName = UserName;
    }

    public String getUserLevel() {
        return UserLevel;
    }

    public void setUserLevel(String UserLevel) {
        this.UserLevel = UserLevel;
    }
}

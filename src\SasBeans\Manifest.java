/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class Manifest {

    private String CodFil;
    private String SeqRota;
    private String Remessa;
    private String Parada;
    private String CodRemessa;
    private String Qtde;
    private String Valor;
    private String Lacre;
    private String Oper_Incl;
    private String Dt_Incl;
    private String Hr_Incl;
    private String Oper_Baixa;
    private String Dt_Baixa;
    private String Hr_Baixa;

    public String getCodFil() {
        return CodFil;
    }

    public void setCodFil(String CodFil) {
        this.CodFil = CodFil;
    }

    public String getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    public String getRemessa() {
        return Remessa;
    }

    public void setRemessa(String Remessa) {
        this.Remessa = Remessa;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getCodRemessa() {
        return CodRemessa;
    }

    public void setCodRemessa(String CodRemessa) {
        this.CodRemessa = CodRemessa;
    }

    public String getQtde() {
        return Qtde;
    }

    public void setQtde(String Qtde) {
        this.Qtde = Qtde;
    }

    public String getValor() {
        return Valor;
    }

    public void setValor(String Valor) {
        this.Valor = Valor;
    }

    public String getLacre() {
        return Lacre;
    }

    public void setLacre(String Lacre) {
        this.Lacre = Lacre;
    }

    public String getOper_Incl() {
        return Oper_Incl;
    }

    public void setOper_Incl(String Oper_Incl) {
        this.Oper_Incl = Oper_Incl;
    }

    public String getDt_Incl() {
        return Dt_Incl;
    }

    public void setDt_Incl(String Dt_Incl) {
        this.Dt_Incl = Dt_Incl;
    }

    public String getHr_Incl() {
        return Hr_Incl;
    }

    public void setHr_Incl(String Hr_Incl) {
        this.Hr_Incl = Hr_Incl;
    }

    public String getOper_Baixa() {
        return Oper_Baixa;
    }

    public void setOper_Baixa(String Oper_Baixa) {
        this.Oper_Baixa = Oper_Baixa;
    }

    public String getDt_Baixa() {
        return Dt_Baixa;
    }

    public void setDt_Baixa(String Dt_Baixa) {
        this.Dt_Baixa = Dt_Baixa;
    }

    public String getHr_Baixa() {
        return Hr_Baixa;
    }

    public void setHr_Baixa(String Hr_Baixa) {
        this.Hr_Baixa = Hr_Baixa;
    }
}

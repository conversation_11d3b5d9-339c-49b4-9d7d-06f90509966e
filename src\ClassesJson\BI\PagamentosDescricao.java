/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package ClassesJson.BI;

/**
 *
 * <AUTHOR>
 */
public class PagamentosDescricao {

    private double xvalor1; // String CompetPg;
    private double xvalor2; // String CompetPg;
    private double yvalor1; // BigDecimal Valor;
    private double yvalor2; // BigDecimal ValorPago;
    private String titulo; // String Descricao; 

    public double getXvalor1() {
        return xvalor1;
    }

    public void setXvalor1(double xvalor1) {
        this.xvalor1 = xvalor1;
    }

    public double getXvalor2() {
        return xvalor2;
    }

    public void setXvalor2(double xvalor2) {
        this.xvalor2 = xvalor2;
    }

    public double getYvalor1() {
        return yvalor1;
    }

    public void setYvalor1(double yvalor1) {
        this.yvalor1 = yvalor1;
    }

    public double getYvalor2() {
        return yvalor2;
    }

    public void setYvalor2(double yvalor2) {
        this.yvalor2 = yvalor2;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

}

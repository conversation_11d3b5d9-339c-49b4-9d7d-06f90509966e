<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/filiais.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{filiaisMB.Persistencias(login.pp)}" />
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-5 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icone_filiais.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina">#{localemsgs.Filiais}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{filiaisMB.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-5 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <div style="float:left;">
                                        <label class="FilialNome">#{filiaisMB.filialTitulo.descricao}<label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label></label>
                                        <label class="FilialEndereco">#{filiaisMB.filialTitulo.endereco}</label>
                                        <label class="FilialBairroCidade">#{filiaisMB.filialTitulo.bairro}, #{filiaisMB.filialTitulo.cidade}/#{filiaisMB.filialTitulo.UF}</label>
                                    </div>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-2 col-sm-2 col-xs-3">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <div id="mainContainer">
                    <h:form id="main">
                        <p:hotkey bind="a" action="#{filiaisMB.Novo}" oncomplete="PF('dlgCadastrar').show();" update="formCadastrar msgs"/>
                        <p:hotkey bind="p" actionListener="#{filiaisMB.Novo}" oncomplete="PF('dlgPesquisaRapida').show()" update="formPesquisaRapida"/>
                        <p:hotkey bind="e" actionListener="#{filiaisMB.buttonAction}" update="formCadastrar msgs"/>
                        <p:hotkey bind="shift+x" actionListener="#{exportarMB.setTitulo(localemsgs.Filiais)}" oncomplete="PF('dlgExportar').show();"/>

                        <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-12">
                                    <p:panel style="display: inline">
                                        <p:dataTable id="tabela"
                                                     value="#{filiaisMB.allFiliais}"
                                                     paginator="true"
                                                     rows="15"
                                                     lazy="true"
                                                     rowsPerPageTemplate="5,10,15, 20, 25"
                                                     currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Filiais}"
                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                     var="listaFiliais"
                                                     selectionMode="single"
                                                     reflow="true"
                                                     selection="#{filiaisMB.filialSelecionada}"
                                                     emptyMessage="#{localemsgs.SemRegistros}"
                                                     scrollable="true"
                                                     class="tabela DataGrid"
                                                     scrollHeight="100%"
                                                     style="font-size: 12px; background: white"
                                                     rowKey="#{listaFiliais.codFil}"
                                                     >
                                            <p:ajax listener="#{filiaisMB.onRowSelect}" event="rowSelect" />
                                            <p:ajax event="rowDblselect" listener="#{filiaisMB.dblSelect}" update="formCadastrar"/>

                                            <p:column headerText="#{localemsgs.CodFil}" exportable="#{filiaisMB.eFilial}">
                                                <h:outputText value="#{listaFiliais.codFil}">
                                                    <f:convertNumber pattern="0000"/>
                                                </h:outputText>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Descricao}" exportable="#{filiaisMB.eDesc}">
                                                <h:outputText value="#{listaFiliais.descricao}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.RazaoSocial}" exportable="#{filiaisMB.eRazao}">
                                                <h:outputText value="#{listaFiliais.razaoSocial}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Operador}" exportable="#{filiaisMB.eOperador}">
                                                <h:outputText value="#{listaFiliais.operador}"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Dt_Alter}" exportable="#{filiaisMB.eDtAlter}">
                                                <h:outputText value="#{listaFiliais.dt_Alter}" title="#{listaFiliais.dt_Alter}" converter="conversorData"/>
                                            </p:column>

                                            <p:column headerText="#{localemsgs.Hr_Alter}" exportable="#{filiaisMB.eHrAlter}">
                                                <h:outputText value="#{listaFiliais.hr_Alter}"/>
                                            </p:column>
                                        </p:dataTable>

                                        <script>
                                            // <![CDATA[
                                            (function () {
                                                function responsividade() {
                                                    if ($(window).width() <= 640) {
                                                        $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                                    } else {
                                                        $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                                    }
                                                }

                                                $(document).ready(responsividade);
                                                $(window).resize(responsividade);
                                            })();
                                            // ]]>
                                        </script>
                                    </p:panel>
                                </div>
                            </div>
                        </div>

                        <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 100px !important; background: transparent; height:200px !important;" id="botoes">
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Adicionar}" action="#{filiaisMB.Novo}"
                                               oncomplete="PF('dlgCadastrar').show();"
                                               update="formCadastrar msgs">
                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Editar}" actionListener="#{filiaisMB.buttonAction}"
                                               update="formCadastrar msgs">
                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{filiaisMB.Novo}"
                                               oncomplete="PF('dlgPesquisaRapida').show()" update="formPesquisaRapida">
                                    <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.LimparFiltros}" action="#{filiaisMB.LimparFiltros}"
                                               update="msgs main:tabela cabecalho">
                                    <p:graphicImage url="../assets/img/icone_limparfiltro.png" height="40"/>
                                </p:commandLink>
                            </div>
                            <div style="padding-bottom: 10px">
                                <p:commandLink title="#{localemsgs.Exportar}" actionListener="#{exportarMB.setTitulo(localemsgs.Filiais)}"
                                               oncomplete="PF('dlgExportar').show();" >
                                    <p:graphicImage url="../assets/img/icone_satmob_exportar.png" height="40"/>
                                </p:commandLink>
                            </div>
                        </p:panel>
                    </h:form>

                    <!--Cadastrar / Editar -->
                    <h:form class="form-inline" id="formCadastrar">
                        <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                        <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                                  style="max-height:95% !important;max-width:96% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;"
                                  >
                            <script>
                                $(document).ready(function () {
                                    //first unbind the original click event
                                    PF('dlgCadastrar').closeIcon.unbind('click');

                                    //register your own
                                    PF('dlgCadastrar').closeIcon.click(function (e) {
                                        $("#formCadastrar\\:botaoFechar").click();
                                        //should be always called
                                        e.preventDefault();
                                    });
                                })
                            </script>

                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>

                            <f:facet name="header">
                                <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.CadastrarFilial}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                            </f:facet>

                            <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important;" styleClass="cadastrar">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                </p:confirmDialog>

                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important;">
                                    <div class="col-md-2 col-sm-2 col-xs-3">
                                        <p:outputLabel for="codfil" styleClass="descricao" value="#{localemsgs.Filial}: "/>
                                    </div>
                                    <div class="col-md-3 col-sm-3 col-xs-9">
                                        <p:inputText value="#{filiaisMB.filial}" id="codfil" required="true" disabled="#{filiaisMB.flag eq 2}"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}" converter="conversor0"
                                                     style="width: 100%" maxlength="4">
                                            <p:watermark for="codfil" value="#{localemsgs.Filial}"/>
                                        </p:inputText>
                                    </div>

                                    <div class="col-md-2 col-sm-2 col-xs-3">
                                        <p:outputLabel for="rS" value="#{localemsgs.RazaoSocial}: " styleClass="descricao"/>
                                    </div>
                                    <div class="col-md-5 col-sm-5 col-xs-9">
                                        <p:inputText value="#{filiaisMB.novaFilial.razaoSocial}" id="rS" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.RazaoSocial}"
                                                     style="width: 100%;" maxlength="60">
                                            <p:watermark for="rS" value="#{localemsgs.RazaoSocial}"/>
                                        </p:inputText>
                                    </div>

                                    <div class="col-md-2 col-sm-2 col-xs-3">
                                        <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: " styleClass="descricao"/>
                                    </div>
                                    <div class="col-md-10 col-sm-10 col-xs-9">
                                        <p:inputText value="#{filiaisMB.novaFilial.descricao}" id="descricao" required="true"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Descricao}"
                                                     style="width: 100%" maxlength="30">
                                            <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                        </p:inputText>
                                    </div>
                                </div>

                                <div class="form-inline">
                                    <p:commandLink id="cadastro" action="#{filiaisMB.Cadastrar}" update="msgs :main:tabela"
                                                   title="#{localemsgs.Cadastrar}" rendered="#{filiaisMB.flag eq 1}">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>

                                    <p:commandLink id="edicao" action="#{filiaisMB.Editar}" update="msgs :main:tabela"
                                                   title="#{localemsgs.Editar}" rendered="#{filiaisMB.flag eq 2}">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <!--Pesquisar-->
                    <h:form id="formPesquisar" rendered="false">
                        <p:hotkey bind="esc" oncomplete="PF('dlgPesquisar').hide()"/>
                        <p:dialog  widgetVar="dlgPesquisar" positionType="absolute" responsive="true"
                                   draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                                   showEffect="drop" hideEffect="drop" closeOnEscape="false"
                                   style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.PesquisarFilial}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                            </f:facet>

                            <p:panel id="pesquisar" style="background-color: transparent">
                                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0px !important;">
                                    <div class="col-md-2 col-sm-2 col-xs-3">
                                        <p:outputLabel for="codfil" value="#{localemsgs.Filial}: " styleClass="descricao"/>
                                    </div>

                                    <div class="col-md-10 col-sm-10 col-xs-9">
                                        <p:inputText value="#{filiaisMB.novaFilial.codFil}" id="codfil"
                                                     style="width: 100%">
                                            <p:watermark for="codfil" value="#{localemsgs.Filial}"/>
                                        </p:inputText>
                                    </div>

                                    <div class="col-md-2 col-sm-2 col-xs-3">
                                        <p:outputLabel for="descricao" value="#{localemsgs.Descricao}: " styleClass="descricao"/>
                                    </div>

                                    <div class="col-md-10 col-sm-10 col-xs-9">
                                        <p:inputText value="#{filiaisMB.novaFilial.descricao}" id="descricao"
                                                     style="width: 100%">
                                            <p:watermark for="descricao" value="#{localemsgs.Descricao}"/>
                                        </p:inputText>
                                    </div>

                                    <div class="col-md-2 col-sm-2 col-xs-3">
                                        <p:outputLabel for="rS" value="#{localemsgs.RazaoSocial}:" styleClass="descricao"/>
                                    </div>

                                    <div class="col-md-10 col-sm-10 col-xs-9">
                                        <p:inputText value="#{filiaisMB.novaFilial.razaoSocial}" id="rS"
                                                     style="width: 100%">
                                            <p:watermark for="rS" value="#{localemsgs.RazaoSocial}"/>
                                        </p:inputText>
                                    </div>
                                </div>

                                <div class="form-inline">
                                    <p:commandLink action="#{filiaisMB.PesquisaPaginada}" update="msgs main:tabela cabecalho"
                                                   title="#{localemsgs.Pesquisar}" oncomplete="PF('dlgPesquisar').hide()">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <!-- Pesquisa Rápida Posto de Serviço -->
                    <h:form id="formPesquisaRapida" prependId="false" onsubmit="return false;">
                        <p:hotkey bind="esc" oncomplete="PF('dlgPesquisaRapida').hide()"/>
                        <p:dialog
                            widgetVar="dlgPesquisaRapida"
                            positionType="absolute"
                            responsive="true"
                            focus="opcao"
                            draggable="false"
                            modal="true"
                            closable="true"
                            resizable="false"
                            dynamic="true"
                            showEffect="drop"
                            hideEffect="drop"
                            closeOnEscape="false"
                            width="400"
                            style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_postosdeservico.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.PesquisarFilial}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px"/>
                            </f:facet>

                            <p:panel id="panelPesquisaRapida" style="background: transparent">
                                <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
                                    <div style="flex-grow: 1; min-width: 50%;">
                                        <p:outputLabel for="radioOpcoes" value="#{localemsgs.ProcurarPor}: "/>

                                        <p:selectOneRadio
                                            id="radioOpcoes"
                                            value="#{filiaisMB.chavePesquisa}"
                                            unselectable="true"
                                            layout="pageDirection"
                                            >
                                            <f:selectItem itemLabel="#{localemsgs.Codigo}" itemValue="CODFIL" />
                                            <f:selectItem itemLabel="#{localemsgs.Descricao}" itemValue="DESCRICAO" />
                                            <f:selectItem itemLabel="#{localemsgs.RazaoSocial}" itemValue="RAZAOSOCIAL" />

                                            <p:ajax update="direita" />
                                        </p:selectOneRadio>
                                    </div>

                                    <p:outputPanel
                                        id="direita"
                                        style="padding-left: 16px;">
                                        <p:outputPanel>
                                            <p:outputLabel for="opcao" rendered="#{filiaisMB.chavePesquisa eq 'CODFIL'}" value="#{localemsgs.Codigo}: "/>
                                            <p:outputLabel for="opcao" rendered="#{filiaisMB.chavePesquisa eq 'DESCRICAO'}" value="#{localemsgs.Descricao}: "/>
                                            <p:outputLabel for="opcao" rendered="#{filiaisMB.chavePesquisa eq 'RAZAOSOCIAL'}" value="#{localemsgs.RazaoSocial}: "/>

                                            <p:inputText
                                                id="opcao"
                                                value="#{filiaisMB.valorPesquisa}"
                                                style="width: 100%" maxlength="60">
                                            </p:inputText>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </div>

                                <div class="form-inline">
                                    <p:commandLink id="botaoPesquisaRapida"
                                                   action="#{filiaisMB.pesquisarUnico()}"
                                                   update=" :main:tabela :msgs cabecalho"
                                                   oncomplete="PF('dlgPesquisaRapida').hide()"
                                                   title="#{localemsgs.Pesquisar}">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </p:panel>
                        </p:dialog>
                    </h:form>
                    <script>
                        $(document).on('keydown', '#panelPesquisaRapida [id*="opcao"]', function (e) {
                            if (e.keyCode === 13) {
                                $('#botaoPesquisaRapida').click();
                            }
                        });
                    </script>

                    <!--Exportar-->
                    <p:dialog widgetVar="dlgExportar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar"
                              style="height:95% !important; max-height:530px !important;max-width:95% !important; border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <f:facet name="header">
                            <img src="../assets/img/icone_filiais.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.Exportar}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>

                        <h:form class="form-inline">
                            <p:hotkey bind="esc" oncomplete="PF('dlgExportar').hide()"/>
                            <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                            <p:separator />

                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-bottom: 1em;">
                                <div class="col-md-4 col-sm-4 col-xs-6" style="padding: 0.5em;">
                                    <p:selectBooleanCheckbox id="filial" value="#{filiaisMB.eFilial}">
                                        <p:ajax update="labelFilial"/>
                                    </p:selectBooleanCheckbox>

                                    <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}"
                                                   style="margin-left: 8px; #{filiaisMB.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>

                                <div class="col-md-4 col-sm-4 col-xs-6" style="padding: 0.5em;">
                                    <p:selectBooleanCheckbox id="desc" value="#{filiaisMB.eDesc}">
                                        <p:ajax update="labelDesc"/>
                                    </p:selectBooleanCheckbox>

                                    <p:outputLabel id="labelDesc" value="#{localemsgs.Descricao}"
                                                   style="margin-left: 8px; #{filiaisMB.eDesc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>

                                <div class="col-md-4 col-sm-4 col-xs-6" style="padding: 0.5em;">
                                    <p:selectBooleanCheckbox id="razao" value="#{filiaisMB.eRazao}">
                                        <p:ajax update="labelRazao"/>
                                    </p:selectBooleanCheckbox>

                                    <p:outputLabel id="labelRazao" value="#{localemsgs.RazaoSocial}"
                                                   style="margin-left: 8px; #{filiaisMB.eRazao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>

                                <div class="col-md-4 col-sm-4 col-xs-6" style="padding: 0.5em;">
                                    <p:selectBooleanCheckbox id="operador" value="#{filiaisMB.eOperador}">
                                        <p:ajax update="labelOperador"/>
                                    </p:selectBooleanCheckbox>
                                    <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}"
                                                   style="margin-left: 8px; #{filiaisMB.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>

                                <div class="col-md-4 col-sm-4 col-xs-6" style="padding: 0.5em;">
                                    <p:selectBooleanCheckbox id="dtalter" value="#{filiaisMB.eDtAlter}">
                                        <p:ajax update="labelDtAlter"/>
                                    </p:selectBooleanCheckbox>

                                    <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}"
                                                   style="margin-left: 8px; #{filiaisMB.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>

                                <div class="col-md-4 col-sm-4 col-xs-6" style="padding: 0.5em;">
                                    <p:selectBooleanCheckbox id="hralter" value="#{filiaisMB.eHrAlter}">
                                        <p:ajax update="labelHrAlter"/>
                                    </p:selectBooleanCheckbox>

                                    <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}"
                                                   style="margin-left: 8px; #{filiaisMB.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                                </div>

                                <div class="col-md-6 col-sm-6 col-xs-6 text-right" style="padding-top: 1em;">
                                    <p:outputLabel for="pdf" value="#{localemsgs.pdf}" style="font-weight: bold"/>
                                    <h:commandLink id="pdf" actionListener="#{filiaisMB.AtualizaTabela}">
                                        <p:graphicImage url="../assets/img/icone_pdf.png" style="height:40px"/>
                                        <p:dataExporter target="main:tabela" type="pdf" fileName="#{localemsgs.Filiais}"
                                                        preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                                    </h:commandLink>
                                </div>

                                <div class="col-md-6 col-sm-6 col-xs-6 text-left" style="padding-top: 1em;">
                                    <h:commandLink id="xlsx" actionListener="#{filiaisMB.AtualizaTabela}">
                                        <p:graphicImage url="../assets/img/icone_xls.png" style="height:40px"/>
                                        <p:dataExporter target="main:tabela" type="xlsx" fileName="#{localemsgs.Filiais}"/>
                                    </h:commandLink>
                                    <p:outputLabel for="xlsx" value="#{localemsgs.xls}" style="font-weight: bold"/>
                                </div>
                            </div>
                        </h:form>
                    </p:dialog>
                </div>
            </div>

            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" action="#{localeController.getLocales}">
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25"/>
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>

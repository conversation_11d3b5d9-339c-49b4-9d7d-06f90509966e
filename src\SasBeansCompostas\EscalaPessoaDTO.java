/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeansCompostas;

import SasBeans.Escala;
import SasBeans.Funcion;
import SasBeans.Rotas;
import SasBeans.Veiculos;

/**
 *
 * <AUTHOR>
 */
public class EscalaPessoaDTO {

    private Escala escala;
    private Funcion motorista;
    private Rotas rota;
    private Funcion chefeEquipe;
    private Funcion vigilante1;
    private Funcion vigilante2;
    private Funcion vigilante3;
    private Veiculos veiculo;

    public EscalaPessoaDTO() {
        this.escala = new Escala();
        this.motorista = new Funcion();
        this.chefeEquipe = new Funcion();
        this.vigilante1 = new Funcion();
        this.vigilante2 = new Funcion();
        this.vigilante3 = new Funcion();
        this.veiculo = new Veiculos();
        this.rota = new Rotas();
    }

    public EscalaPessoaDTO(EscalaPessoaDTO original) {
        escala = new Escala(original.getEscala());
        motorista = new Funcion(original.getMotorista());
        chefeEquipe = new Funcion(original.getChefeEquipe());
        vigilante1 = new Funcion(original.getVigilante1());
        vigilante2 = new Funcion(original.getVigilante2());
        vigilante3 = new Funcion(original.getVigilante3());
        rota = new Rotas(original.getRota());
        veiculo = new Veiculos(original.getVeiculo());
    }

    public Escala getEscala() {
        return escala;
    }

    public void setEscala(Escala escala) {
        this.escala = escala;
    }

    public Funcion getMotorista() {
        return motorista;
    }

    public void setMotorista(Funcion motorista) {
        this.motorista = motorista;
    }

    public Funcion getChefeEquipe() {
        return chefeEquipe;
    }

    public void setChefeEquipe(Funcion chefeEquipe) {
        this.chefeEquipe = chefeEquipe;
    }

    public Funcion getVigilante1() {
        return vigilante1;
    }

    public void setVigilante1(Funcion vigilante1) {
        this.vigilante1 = vigilante1;
    }

    public Funcion getVigilante2() {
        return vigilante2;
    }

    public void setVigilante2(Funcion vigilante2) {
        this.vigilante2 = vigilante2;
    }

    public Funcion getVigilante3() {
        return vigilante3;
    }

    public void setVigilante3(Funcion vigilante3) {
        this.vigilante3 = vigilante3;
    }

    public Rotas getRota() {
        return rota;
    }

    public void setRota(Rotas rota) {
        this.rota = rota;
    }

    public Veiculos getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(Veiculos veiculo) {
        this.veiculo = veiculo;
    }
}

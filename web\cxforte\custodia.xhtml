<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/pessoas.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid [role="columnheader"] > span {
                        top: -4px !important;
                        position: relative !important;
                    }
                }

                @media only screen and (max-width: 2000px) and (min-width: 701px) {
                    .DataGrid{
                        width:100% !important;
                        border:none !important
                    }

                    #main\:tabela.DataGrid thead tr th,
                    #main\:tabela.DataGrid tbody tr td {
                        min-width: 100px;
                        max-width: 100px;
                        white-space: normal !important;
                        overflow-wrap: break-word !important;
                        word-wrap: break-word !important;
                        -webkit-hyphens: auto !important;
                        -ms-hyphens: auto !important;
                        hyphens: auto !important;
                    }

                    #main\:tabela.DataGrid thead tr th:nth-child(1),
                    #main\:tabela.DataGrid tbody tr td:nth-child(1),
                    #main\:tabela.DataGrid thead tr th:nth-child(3),
                    #main\:tabela.DataGrid tbody tr td:nth-child(3),
                    #main\:tabela.DataGrid thead tr th:nth-child(4),
                    #main\:tabela.DataGrid tbody tr td:nth-child(4),
                    #main\:tabela.DataGrid thead tr th:nth-child(5),
                    #main\:tabela.DataGrid tbody tr td:nth-child(5),
                    #main\:tabela.DataGrid thead tr th:nth-child(6),
                    #main\:tabela.DataGrid tbody tr td:nth-child(6),
                    #main\:tabela.DataGrid thead tr th:nth-child(31) {
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }
                    #main\:tabela.DataGrid thead tr th:nth-child(2),
                    #main\:tabela.DataGrid tbody tr td:nth-child(2) {
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }
                    #main\:tabela.DataGrid thead tr th:nth-child(7),
                    #main\:tabela.DataGrid tbody tr td:nth-child(7),
                    #main\:tabela.DataGrid thead tr th:nth-child(8),
                    #main\:tabela.DataGrid tbody tr td:nth-child(8),
                    #main\:tabela.DataGrid thead tr th:nth-child(9),
                    #main\:tabela.DataGrid tbody tr td:nth-child(9),
                    #main\:tabela.DataGrid thead tr th:nth-child(10),
                    #main\:tabela.DataGrid tbody tr td:nth-child(10),
                    #main\:tabela.DataGrid thead tr th:nth-child(11),
                    #main\:tabela.DataGrid tbody tr td:nth-child(11),
                    #main\:tabela.DataGrid thead tr th:nth-child(12),
                    #main\:tabela.DataGrid tbody tr td:nth-child(12),
                    #main\:tabela.DataGrid thead tr th:nth-child(13),
                    #main\:tabela.DataGrid tbody tr td:nth-child(13),
                    #main\:tabela.DataGrid thead tr th:nth-child(14),
                    #main\:tabela.DataGrid tbody tr td:nth-child(14),
                    #main\:tabela.DataGrid thead tr th:nth-child(15),
                    #main\:tabela.DataGrid tbody tr td:nth-child(15),
                    #main\:tabela.DataGrid thead tr th:nth-child(16),
                    #main\:tabela.DataGrid tbody tr td:nth-child(16),
                    #main\:tabela.DataGrid thead tr th:nth-child(18),
                    #main\:tabela.DataGrid tbody tr td:nth-child(18),
                    #main\:tabela.DataGrid thead tr th:nth-child(20),
                    #main\:tabela.DataGrid tbody tr td:nth-child(20),
                    #main\:tabela.DataGrid thead tr th:nth-child(22),
                    #main\:tabela.DataGrid tbody tr td:nth-child(22),
                    #main\:tabela.DataGrid thead tr th:nth-child(24),
                    #main\:tabela.DataGrid tbody tr td:nth-child(24),
                    #main\:tabela.DataGrid thead tr th:nth-child(26),
                    #main\:tabela.DataGrid tbody tr td:nth-child(26),
                    #main\:tabela.DataGrid thead tr th:nth-child(28),
                    #main\:tabela.DataGrid tbody tr td:nth-child(29),
                    #main\:tabela.DataGrid thead tr th:nth-child(30),
                    #main\:tabela.DataGrid tbody tr td:nth-child(30) {
                        min-width: 90px !important;
                        max-width: 90px !important;
                    }
                    #main\:tabela.DataGrid thead tr th:nth-child(17),
                    #main\:tabela.DataGrid tbody tr td:nth-child(17),
                    #main\:tabela.DataGrid thead tr th:nth-child(19),
                    #main\:tabela.DataGrid tbody tr td:nth-child(19),
                    #main\:tabela.DataGrid thead tr th:nth-child(21),
                    #main\:tabela.DataGrid tbody tr td:nth-child(21),
                    #main\:tabela.DataGrid thead tr th:nth-child(23),
                    #main\:tabela.DataGrid tbody tr td:nth-child(23),
                    #main\:tabela.DataGrid thead tr th:nth-child(25),
                    #main\:tabela.DataGrid tbody tr td:nth-child(25),
                    #main\:tabela.DataGrid thead tr th:nth-child(27),
                    #main\:tabela.DataGrid tbody tr td:nth-child(27),
                    #main\:tabela.DataGrid thead tr th:nth-child(29),
                    #main\:tabela.DataGrid tbody tr td:nth-child(29) {
                        min-width: 70px !important;
                        max-width: 70px !important;
                    }
                    #main\:tabela.DataGrid thead tr th:nth-child(31),
                    #main\:tabela.DataGrid tbody tr td:nth-child(31) {
                        min-width: 150px !important;
                        max-width: 150px !important;
                    }

                    .DataGrid thead tr th,
                    .DataGrid tbody tr td{
                        text-align: center !important;
                    }
                }
                
                #divCalendario{
                    padding-top:6px !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{custodia.iniciarPagina}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-3 col-sm-12 col-xs-12" style="align-self: center; padding-top: 0px !important; padding-left: 4px !important;">
                                    <img src="../assets/img/icones_custodia_CAIXAFORTE.png" height="40" style="margin-top:-6px !important" />
                                    <label class="TituloPagina" style="top: -3px !important">#{localemsgs.Custodia}</label>
                                    <label class="TituloDataHora" style="top: 22px !important">
                                        <h:outputText value="#{localemsgs.Periodo}: "/>
                                        <span>
                                            <h:outputText value="#{custodia.d1}" converter="conversorData" />
                                            <h:outputText value=" - "/>
                                            <h:outputText value="#{custodia.d2}" converter="conversorData"/>
                                        </span>
                                    </label>
                                    <label class="TituloDataHora" style="top: 35px !important">
                                        <h:outputText value="#{localemsgs.CxForte}:"/>
                                        <span style="font-weight: bold; font-size: 12px;">
                                            <h:outputText value="#{custodia.cxForte.codCli} #{custodia.cxForte.NRed}"/>
                                        </span>
                                    </label>
                                </div>

                                <div id="divTopoTela" class="col-md-3 col-sm-12 col-xs-12" style="text-align: center !important; display:none">
                                    <label class="TituloDataHora" style="top: 9px !important">
                                        <table>
                                            <tr>
                                                <td></td>
                                                <td>
                                                    <h:outputText value="#{localemsgs.SaldoTotalCxForte}:"/>
                                                </td>
                                                <td></td>
                                                <td>
                                                    <h:outputText value="#{custodia.cxForte.saldoReal}" converter="conversormoeda" class="celula-right"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="fa fa-square azul" style="border: white solid 1px"></i>
                                                </td>
                                                <td>
                                                    <h:outputText value="#{localemsgs.RemessasPreparadas}:"/>
                                                </td>
                                                <td>
                                                    <h:outputText value="#{custodia.cxForte.remPrepQtde}"/>
                                                </td>
                                                <td>
                                                    <h:outputText value="#{custodia.cxForte.remPrepValor}"
                                                                  style="width: 100%" converter="conversormoeda" class="celula-right"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="fa fa-square preto" style="border: white solid 1px"></i>
                                                </td>
                                                <td>
                                                    <h:outputText value="#{localemsgs.RemessasCustodia}:"/>
                                                </td>
                                                <td>
                                                    <h:outputText value="#{custodia.cxForte.remPendQtde}"/>
                                                </td>
                                                <td>
                                                    <h:outputText value="#{custodia.cxForte.remPendValor}" converter="conversormoeda" class="celula-right"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-12" style="text-align: center !important;">
                                    <label class="FilialNome">
                                        #{custodia.filiais.descricao}
                                        <label id="btTrocarFilial" onclick="top.location.href = '../param.xhtml'">#{localemsgs.TrocarFilial}</label>
                                    </label>
                                    <label class="FilialEndereco">#{custodia.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{custodia.filiais.bairro}, #{custodia.filiais.cidade}/#{custodia.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-3 col-sm-10 col-xs-12">

                                    <!--Botão voltar-->
                                    <p:commandLink action="#{custodia.dataAnterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 20px"/>
                                    </p:commandLink>

                                    <!--Calendário-->
                                    <p:commandLink id="calendar" oncomplete="PF('oCalendarios').show();" styleClass="botao" update="msgs">
                                        <p:graphicImage url="../assets/img/icone_escaladodia.png" style="align-self: center;height: 40px"/>
                                    </p:commandLink>

                                    <!--Botão avançar-->
                                    <p:commandLink action="#{custodia.dataPosterior}" update="main cabecalho msgs">
                                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 20px"/>
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-12">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main">
                    <p:hotkey bind="e" actionListener="#{custodia.exibirGuia}" update="msgs formCadastrar:cadastrar"/>
                    <div class="ui-grid ui-grid-responsive FundoPagina" style="overflow:hidden !important; padding-right:12px !important;">
                        <div class="ui-grid-row">
                            <div id="divFundoGrid" class="ui-grid-col-12" style="overflow:hidden !important;position:relative;">
                                <p:panel style="display: inline">
                                    <p:dataTable id="tabela" value="#{custodia.guias}" paginator="true" rows="15"
                                                 lazy="true" reflow="true" rowsPerPageTemplate="5,10,15, 20, 25"
                                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Guias}"
                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                                 var="cxfguia" selectionMode="single" styleClass="tabela" selection="#{custodia.cxFGuiasSelecionado}"
                                                 emptyMessage="#{localemsgs.SemRegistros}" scrollable="true" class="tabela DataGrid"
                                                 scrollHeight="100%" style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;
                                                 min-height:100% !important; max-width:100% !important; height:100% !important;max-height:100% !important;
                                                 width:100% !important;"
                                                 rowStyleClass="#{cxfguia.rotaSai eq '' or cxfguia.rotaSai eq null ? 'preto' :
                                                                  cxfguia.hr_Saida eq '' or cxfguia.hr_Saida eq null ? 'azul' : 'verde'}">
                                        <p:ajax event="rowDblselect" listener="#{custodia.exibirGuia}" update="formCadastrar msgs"/>
                                        <p:column headerText="#{localemsgs.Guia}" class="text-center">
                                            <h:outputText value="#{cxfguia.guia}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Serie}" class="text-center">
                                            <h:outputText value="#{cxfguia.serie}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRed}" class="text-center">
                                            <h:outputText value="#{cxfguia.NRed}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRedDst}" class="text-center">
                                            <h:outputText value="#{cxfguia.NRedDst}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRedOS}" class="text-center">
                                            <h:outputText value="#{cxfguia.NRedOS}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Valor}" class="text-center">
                                            <h:outputText value="#{cxfguia.valor}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Volumes}" class="text-center">
                                            <h:outputText value="#{cxfguia.volumes}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RotaEnt}" class="text-center">
                                            <h:outputText value="#{cxfguia.rotaEnt}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.OperEnt}" class="text-center">
                                            <h:outputText value="#{cxfguia.operEnt}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.DtEnt}" class="text-center">
                                            <h:outputText value="#{cxfguia.dtEnt}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrEnt}" class="text-center">
                                            <h:outputText value="#{cxfguia.hrEnt}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.RotaSai}" class="text-center">
                                            <h:outputText value="#{cxfguia.rotaSai}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Remessa}" class="text-center">
                                            <h:outputText value="#{cxfguia.remessa}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.OperSai}" class="text-center">
                                            <h:outputText value="#{cxfguia.operSai}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.DtSai}" class="text-center">
                                            <h:outputText value="#{cxfguia.dtSai}" converter="conversorData"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.HrSai}" class="text-center">
                                            <h:outputText value="#{cxfguia.hrSai}" converter="conversorHora"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.VolDh}" class="text-center">
                                            <h:outputText value="#{cxfguia.volDh}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorDh}" class="text-center">
                                            <h:outputText value="#{cxfguia.valorDh}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.VolCh}" class="text-center">
                                            <h:outputText value="#{cxfguia.volCh}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorCh}" class="text-center">
                                            <h:outputText value="#{cxfguia.valorCh}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.VolMd}" class="text-center">
                                            <h:outputText value="#{cxfguia.volMd}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorMd}" class="text-center">
                                            <h:outputText value="#{cxfguia.valorMd}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.VolMet}" class="text-center">
                                            <h:outputText value="#{cxfguia.volMet}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorMet}" class="text-center">
                                            <h:outputText value="#{cxfguia.valorMet}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.VolMEstr}" class="text-center">
                                            <h:outputText value="#{cxfguia.volMEstr}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorMEstr}" class="text-center">
                                            <h:outputText value="#{cxfguia.valorMEstr}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.VolOutr}" class="text-center">
                                            <h:outputText value="#{cxfguia.volOutr}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.ValorOutr}" class="text-center">
                                            <h:outputText value="#{cxfguia.valorOutr}" converter="conversormoeda"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.OS}" class="text-center">
                                            <h:outputText value="#{cxfguia.OS}">
                                                <f:convertNumber pattern="0000"/>
                                            </h:outputText>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Lote}" class="text-center">
                                            <h:outputText value="#{cxfguia.lote}"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.NRedCli}" class="text-center">
                                            <h:outputText value="#{cxfguia.NRedCli}"/>
                                        </p:column>
                                    </p:dataTable>
                                    <script>
                                        // <![CDATA[
                                        $(document).ready(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });

                                        $(window).resize(function () {
                                            if ($(document).width() <= 700)
                                                $('.ui-panel-content').height(($('body').height() - 225) + 'px');
                                            else
                                                $('.ui-panel-content').height(($('body').height() - 148) + 'px');
                                        });
                                        // ]]>
                                    </script>
                                </p:panel>
                            </div>
                        </div>
                    </div>

                    <p:panel style="position: fixed; z-index: 1; right: 1px; bottom: 100px !important; background: transparent; height:200px !important;" id="botoes">

                    </p:panel>
                </h:form>

                <h:form id="calendarios" style="overflow:hidden !important; top:0 !important; right:0 !important;bottom:0 !important;left:0 !important;
                        margin:0 auto !important;">
                    <p:dialog widgetVar="oCalendarios" positionType="absolute"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true" position="center center"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="oCalendarios" onShow="PF('oCalendarios').initPosition()"
                              style="height:95% !important; max-height:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; border-top:4px solid #3C8DBC !important;
                              border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px !important;
                              background-color:#EEE !important; margin:0 auto !important; overflow:auto !important">

                        <div class="ui-grid-row ui-grid-responsive" style="overflow:hidden !important;">
                            <div class="ui-grid-col-6" style="padding-bottom:20px !important;overflow:hidden !important;">
                                <div class="ui-grid-row">
                                    <div style="width: 50%; float: left">
                                        <p:outputLabel for="cal1" value="#{localemsgs.DataInicial}:" title="#{localemsgs.DataInicial}" />
                                    </div>
                                    <div style="width: 50%; float: left">
                                        <p:inputText id="empty" style="width: 0px; z-index: -1; position: fixed" />
                                        <p:inputMask id="cal1" value="#{custodia.d1}" mask="99/99/9999"
                                                     style="width: 85px" converter="conversorData" >
                                            <p:ajax event="blur" listener="#{custodia.escreverData1}" update="cal2 calendario1 calendario2" />
                                        </p:inputMask>
                                    </div>
                                </div>

                                <p:datePicker id="calendario1" value="#{custodia.dataSelecionada1}" inline="true"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario"
                                              converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{custodia.selecionarData1}" update="cal1 cal2 calendario2" />
                                </p:datePicker>

                            </div>
                            <div class="ui-grid-col-6" style="padding-bottom:20px !important;overflow:hidden !important;">
                                <div class="ui-grid-row">
                                    <div style="width: 50%; float: left">
                                        <p:outputLabel for="cal2" value="#{localemsgs.DataFinal}:" title="#{localemsgs.DataFinal}" />
                                    </div>
                                    <div style="width: 50%; float: left">
                                        <p:inputMask id="cal2" value="#{custodia.d2}" mask="99/99/9999"
                                                     style="width: 85px" converter="conversorData" >
                                            <p:ajax event="blur" listener="#{custodia.escreverData2}" update="cal1 calendario1 calendario2" />
                                        </p:inputMask>
                                    </div>
                                </div>

                                <p:datePicker id="calendario2" value="#{custodia.dataSelecionada2}" inline="true"
                                              pattern="#{mascaras.padraoData}" styleClass="calendario"
                                              converter="conversorDate" locale="#{localeController.getCurrentLocale()}">
                                    <p:ajax event="dateSelect" listener="#{custodia.selecionarData2}" update="cal1 cal2 calendario1" />
                                </p:datePicker>
                            </div>
                            <div style="text-align: right; float: right">
                                <p:commandLink action="#{custodia.selecionarData}" style="float: right" update="main cabecalho">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" height="40" />
                                </p:commandLink>
                            </div>
                        </div>

                        <script>
                            function ResizeWindow() {
                                $(window).resize();
                            }
                        </script>
                    </p:dialog>
                </h:form>

                <h:form id="formCadastrar">
                    <p:hotkey bind="esc" oncomplete="PF('dlgCadastrar').hide()"/>
                    <p:dialog widgetVar="dlgCadastrar" positionType="absolute" responsive="true"
                              draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                              showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgCadastrar" styleClass="dialogo"
                              style="height: auto; max-height:95% !important; max-width:95% !important; border:thin solid #666 !important;
                              box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important; border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; background-color:#EEE !important;">
                        <script>
                            $(document).ready(function () {
                                //first unbind the original click event
                                PF('dlgCadastrar').closeIcon.unbind('click');

                                //register your own
                                PF('dlgCadastrar').closeIcon.click(function (e) {
                                    $("#formCadastrar\\:botaoFechar").click();
                                    //should be always called
                                    e.preventDefault();
                                });
                            })
                        </script>
                        <p:commandButton widgetVar="botaoFechar" style="display: none"
                                         oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                            <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                        </p:commandButton>
                        <f:facet name="header">
                            <img src="../assets/img/icones_custodia_CAIXAFORTE.png" height="40" width="40"/>
                            <p:spacer width="5px"/>
                            <h:outputText value="#{localemsgs.DetalhesGuia}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        </f:facet>
                        <p:panel id="cadastrar" style="background-color: transparent; max-width: 100% !important;" class="cadastrar">
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                            </p:confirmDialog>

                            <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color:white !important;">
                                <p:outputLabel value="#{localemsgs.Guia}:"/>
                                <p:column>
                                    <h:outputText value="#{custodia.cxFGuiasSelecionado.guia}" converter="conversor0"/>
                                    <h:outputText value=" #{custodia.cxFGuiasSelecionado.serie}"/>
                                </p:column>

                                <p:outputLabel value="#{localemsgs.Filial}:"/>
                                <p:column>
                                    <h:outputText value="#{custodia.cxFGuiasSelecionado.codFil}">
                                        <f:convertNumber pattern="0000"/>
                                    </h:outputText>
                                </p:column>

                                <p:outputLabel value="#{localemsgs.Valor}:"/>
                                <h:outputText value="#{custodia.cxFGuiasSelecionado.valor}" converter="conversormoeda"/>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color: #EEE !important;">
                                <p:outputLabel for="cliori" value="#{localemsgs.CliOri}:"/>
                                <p:inputText id="cliori" value="#{custodia.cxFGuiasSelecionado.NRed}" disabled="true" style="width: 100%"/>

                                <p:outputLabel for="clidst" value="#{localemsgs.CliDst}:"/>
                                <p:inputText id="clidst" value="#{custodia.cxFGuiasSelecionado.NRedDst}" disabled="true" style="width: 100%"/>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color: #EEE !important;">
                                <p:column>
                                    <h:outputText value="#{localemsgs.Entrada}" style="color:#3C8DBC; margin-left: 0px !important;
                                                  font-weight:bold; font-size: 11pt;" />
                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5"
                                                 layout="grid" styleClass="ui-panelgrid-blank" style="background-color: #EEE !important;">
                                        <p:outputLabel for="rotaEnt" value="#{localemsgs.Rota}:"/>
                                        <p:inputText id="rotaEnt" value="#{custodia.cxFGuiasSelecionado.rotaEnt}" disabled="true" style="width: 100%"/>

                                        <p:outputLabel for="dtEnt" value="#{localemsgs.Data}:"/>
                                        <p:inputText id="dtEnt" value="#{custodia.cxFGuiasSelecionado.dtEnt}" disabled="true"
                                                     style="width: 100%" converter="conversorDia"/>

                                        <p:outputLabel for="hrEnt" value="#{localemsgs.Hora}:"/>
                                        <p:inputText id="hrEnt" value="#{custodia.cxFGuiasSelecionado.hrEnt}" disabled="true"
                                                     style="width: 100%" converter="conversorHora"/>

                                        <p:outputLabel for="operEnt" value="#{localemsgs.Operador}:"/>
                                        <p:inputText id="operEnt" value="#{custodia.cxFGuiasSelecionado.operEnt}" disabled="true" style="width: 100%"/>
                                    </p:panelGrid>
                                </p:column>
                                <p:column>
                                    <h:outputText value="#{localemsgs.Saida}" style="color:#3C8DBC; margin-left: 0px !important;
                                                  font-weight:bold; font-size: 11pt;" />
                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-5"
                                                 layout="grid" styleClass="ui-panelgrid-blank" style="background-color: #EEE !important;">
                                        <p:outputLabel for="rotaSai" value="#{localemsgs.Rota}:"/>
                                        <p:inputText id="rotaSai" value="#{custodia.cxFGuiasSelecionado.rotaSai}" disabled="true" style="width: 100%"/>

                                        <p:outputLabel for="dtSai" value="#{localemsgs.Data}:"/>
                                        <p:inputText id="dtSai" value="#{custodia.cxFGuiasSelecionado.dtSai}" disabled="true"
                                                     style="width: 100%" converter="conversorDia"/>

                                        <p:outputLabel for="hrSai" value="#{localemsgs.Hora}:"/>
                                        <p:inputText id="hrSai" value="#{custodia.cxFGuiasSelecionado.hrSai}" disabled="true"
                                                     style="width: 100%" converter="conversorHora"/>

                                        <p:outputLabel for="operSai" value="#{localemsgs.Operador}:"/>
                                        <p:inputText id="operSai" value="#{custodia.cxFGuiasSelecionado.operSai}" disabled="true" style="width: 100%"/>
                                    </p:panelGrid>
                                </p:column>
                            </p:panelGrid>

                            <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-3,ui-grid-col-3"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="background-color: #EEE !important;">
                                <p:outputLabel for="os" value="#{localemsgs.OS}:"/>
                                <p:column>
                                    <p:inputText id="os" value="#{custodia.cxFGuiasSelecionado.OS}" style="width: calc(100% - 30px); text-align: right"/>
                                    <p:commandLink title="#{localemsgs.Pesquisar}" actionListener="#{custodia.pesquisarOS}" update="msgs">
                                        <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                    </p:commandLink>
                                </p:column>

                                <p:outputLabel for="NRed" value="#{localemsgs.Trajeto}:"/>
                                <p:inputText id="NRed" value="#{custodia.osSelecionada.NRed}" disabled="true" style="width: 100%"/>
                                <p:inputText value="#{custodia.osSelecionada.NRedDst}" disabled="true" style="width: 100%"/>

                                <p:column/>
                                <p:column/>

                                <p:outputLabel for="NRedFat" value="#{localemsgs.Faturar}:"/>
                                <p:inputText id="NRedFat" value="#{custodia.osSelecionada.NRedFat}" disabled="true" style="width: 100%"/>
                                <p:inputText value="#{custodia.osSelecionada.descricao}" disabled="true" style="width: 100%"/>

                            </p:panelGrid>

                            <p:panel style="background: #FFF; border: 1px solid #E6E6E6 !important; height: 200px">
                                <p:dataTable value="#{custodia.cxfGuiasVolLista}" scrollHeight="200" scrollable="true"
                                             resizableColumns="true" styleClass="tabela"
                                             emptyMessage="#{localemsgs.SemRegistros}" class="tabela DataGrid"
                                             style="font-size: 12px; background: white;  padding:0px !important; margin:0px !important;
                                             min-height:100% !important; max-width:100% !important; height:100% !important;
                                             max-height:100% !important;
                                             width:100% !important;"
                                             var="cxfguiasvol">
                                    <p:column headerText="#{localemsgs.Ordem}" style="width: 65px; text-align: center">
                                        <h:outputText value="#{cxfguiasvol.ordem}" converter="conversor0"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Qtde}" style="width: 65px; text-align: center">
                                        <h:outputText value="#{cxfguiasvol.qtde}" converter="conversor0"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Descricao}" style="width: 65px; text-align: center">
                                        <h:outputText value="#{cxfguiasvol.tipo}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Lacre}" style="width: 65px; text-align: center">
                                        <h:outputText value="#{cxfguiasvol.lacre}"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Valor}" style="width: 65px; text-align: center">
                                        <h:outputText value="#{cxfguiasvol.valor}" converter="conversormoeda"/>
                                    </p:column>
                                    <p:column headerText="#{localemsgs.Obs}" style="width: 65px; text-align: center">
                                        <h:outputText value="#{cxfguiasvol.obs}"/>
                                    </p:column>
                                </p:dataTable>
                            </p:panel>
                        </p:panel>
                    </p:dialog>
                </h:form>

            </div>

            <ui:insert name="loading" >
            </ui:insert>
            <ui:include src="../assets/template/loading.xhtml" />

            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div id="divCorporativo" style="position:absolute; bottom:30px !important; left:5px; max-height:10px !important;">
                        <h:form id="corporativo">
                            <table style="min-height:10px !important">
                                <tr>
                                    <td colspan="2">
                                        <p:selectBooleanCheckbox itemLabel="#{localemsgs.MostrarRemessas}" value="#{custodia.remessas}">
                                            <p:ajax update="msgs main:tabela" listener="#{custodia.mostrarRemessas}" />
                                        </p:selectBooleanCheckbox>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p:selectBooleanCheckbox itemLabel="#{localemsgs.MostrarHistoricoGeral}" value="#{custodia.historicoGeral}">
                                            <p:ajax update="msgs main:tabela" listener="#{custodia.mostrarHistoricoGeral}" />
                                        </p:selectBooleanCheckbox>
                                    </td>
                                    <td>
                                        <i class="fa fa-square verde" style="border: white solid 1px"></i>
                                        <h:outputText value=" #{localemsgs.RemessaEfetivada}"/>
                                    </td>
                                </tr>
                            </table>
                        </h:form>
                    </div>
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-5 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;
                             max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-5 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-2 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
            <script>
                $("#footer-toggle").click(function (e) {
                    e.preventDefault();
                    $("footer").toggleClass("toggled");
                    $(".footer-toggler").toggleClass("toggled");
                    $(".status").toggleClass("toggled");
                    $("#body").toggleClass("toggled");
                    $(".ui-datatable-scrollable-body").toggleClass("toggled");
                });
            </script>
        </h:body>
    </f:view>
</html>
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.PortalSrv;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PortalSrvDao {

    public List<PortalSrv> listarServicos(Persistencia persistencia) throws Exception {
        try {
            List<PortalSrv> retorno = new ArrayList<>();
            PortalSrv portalSrv;
            String sql = " SELECT * FROM PortalSrv ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            while (consulta.Proximo()) {
                portalSrv = new PortalSrv();
                portalSrv.setCodigo(consulta.getString("Codigo"));
                portalSrv.setDescricao(consulta.getString("Descricao"));
                portalSrv.setOperador(consulta.getString("Operador"));
                portalSrv.setDt_alter(consulta.getString("Dt_alter"));
                portalSrv.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(portalSrv);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PortalSrvDao.listarServicos - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM PortalSrv ");
        }
    }
}

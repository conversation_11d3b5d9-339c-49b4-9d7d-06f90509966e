/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.utilidades;

import Arquivo.ArquivoLog;
import java.util.Map;

/**
 * Realiza a construção dos logs
 *
 * <AUTHOR>
 */
public class Logs {

    private String localizacaoReal = "";
    private String localizacaoTracer = "";
    private String param = "";

    private ArquivoLog logerro = null;

    public Logs(String localizacaoReal, String param) {
        this.logerro = new ArquivoLog();
        this.param = param;
        this.localizacaoReal = localizacaoReal;
        localizacaoTracer = localizacaoReal + "tracers\\" + param + "\\" + DataAtual.getDataAtual("SQL") + "Trace_" + param + "_";
    }

    /**
     * Realiza o carregamento dos parametros
     *
     * @param parametros parametros enviadas
     * @param codpessoa codigo pessoa
     */
    public void criarTracerParametros(Map<String, String[]> parametros, String codpessoa) {
        String localTracer = localizacaoTracer + codpessoa + ".txt";
        try {
            logerro.Grava(Trace.Tracer(parametros), localTracer);
        } catch (Exception e) {
            System.out.println("ex >> " + e.getMessage());
        }
    }

    public void criarTracerMensagem(String mensagem, String codpessoa) {
        String localTracer = localizacaoTracer + codpessoa + ".txt";
        try {
            logerro.Grava(Trace.Tracer(mensagem), localTracer);
        } catch (Exception e) {
            System.out.println("ex >> " + e.getMessage());
        }
    }

}

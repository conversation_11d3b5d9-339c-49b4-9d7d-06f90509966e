package br.com.sasw.utils;

/**
 *
 * <AUTHOR>
 */
public enum Permissoes {
    ACESSO_AO_SISTEMA(100, "Acesso ao sistema"),
    OPERACOES(10000, "Operações"),
    ATENDIMENTO_A_CLIENTES(10100, "Atendimento a clientes"),
    PEDIDOS_DE_TRANSPORTE(10101, "Pedidos de Transporte"),
    IMPORTACAO_DE_PEDIDOS(10102, "Importacao de pedidos"),
    GERENCIAMENTO_DE_FECHADURAS(10103, "Gerenciamento de fechaduras"),
    FECHADURAS_ABERTURA(10104, "Fechaduras - Abertura"),
    FECHADURAS_VISUALIZAR_SENHA_MANAGER(10105, "Fechaduras - Visualizar senha manager"),
    FECHADURAS_VISUALIZAR_SENHA_USUARIO1(10106, "Fechaduras - Visualizar senha usuario1"),
    FECHADURAS_VISUALIZAR_SENHA_USUARIO2(10107, "<PERSON>cha<PERSON>ras - Visualizar senha usuario2"),
    FECHADURAS_AUDITORIA(10108, "Fechaduras - Auditoria"),
    FECHADURAS_LIBERACAO_AUTOMATICA_DE_SENHAS(10109, "Fechaduras - Liberação automática de senhas"),
    PROGRAMACAO_DE_SERVICOS(10200, "Programacao de servicos"),
    ROTAS(10201, "Rotas"),
    MODELO_DE_ROTAS(10202, "Modelo de Rotas"),
    GERACAO_DE_ROTAS_X_MODELOS(10203, "Geracao de Rotas x Modelos"),
    AJUSTAR_SEQUENCIA_DE_PARADAS(10204, "Ajustar Sequencia de Paradas"),
    RELATORIOS(10205, "Relatorios"),
    SENHAS(10206, "Senhas"),
    GERENCIAL(10207, "Gerencial"),
    GERENCIAL_GRAFICOS(10208, "Gerencial Gráficos"),
    GERENCIAL_GRAFICOS_AVANCADO(10209, "Gerencial Gráficos Avançado"),
    CONTROLE_OPERACIONAL_TV(10210, "Controle operacional TV"),
    OPERACAO_DE_SERVICO(10211, "Operação de Serviço"),
    OPERACAO_DE_SERVICOII(10212, "Operação de ServiçoII"),
    ROTEIRO_PREDEFINIDO(10213, "Roteiro Predefinido"),
    MOVIMENTO_DE_ROTA(10214, "Movimento de Rota"),
    FINALIZACAO_DE_PROGRAMACAO(10215, "Finalização de Programação"),
    OPERACOES_GUIAS_MOBILE(10216, "Operações Guias Mobile"),
    ROTAS_ACESSO_PROGRAMACAO_FUTURA(10217, "Rotas - acesso programação futura"),
    ROTAS_DESPESAS(10218, "Rotas - Despesas"),
    GERENCIAL_DE_ROTAS_BI(10219, "Gerencial de Rotas (BI)"),
    ROTAS_OCORRENCIAS_DE_LOGISTICA(10220, "Rotas - Ocorrências de Logística"),
    ESCALAS_POR_ROTA(10221, "Escalas por rota"),
    ESCALA_AUTOMATICA(10222, "Escala automatica"),
    ESCALA_INDIVIDUAL(10223, "Escala Individual"),
    FOLGAS(10224, "Folgas"),
    PRE_ESCALA(10225, "Pré-Escala"),
    GRUPO_DE_ESCALAS(10226, "Grupo de Escalas"),
    RELATORIOS_ESCALAS(10228, "Relatórios"),
    ALTERACAO_ESCALA_DIARIA(10229, "Alteração Escala diária"),
    ATM(10240, "ATM"),
    ATM_INTERVENCOES(10241, "ATM Intervenções"),
    ATM_OCORRENCIAS(10242, "ATM Ocorrências"),
    ATM_RELATORIOS(10243, "ATM Relatorios"),
    TIPOS_DE_AST(10244, "Tipos de AST (Assistência tecnica)"),
    CAIXA_FORTE(10250, "Caixa-Forte"),
    CONTROLE_DE_MOVIMENTACAO(10251, "Controle de Movimentação"),
    ENTRADAS(10252, "Entradas"),
    SAIDAS(10253, "Saidas"),
    RELATORIOS_CAIXA(10254, "Relatorios"),
    CAIXA_FORTE_CONTROLE_DE_REMESSAS(10257, "Caixa-Forte - Controle de Remessas"),
    CAIXA_FORTE_CANCELAR_BAIXA_DE_REMESSA(10258, "Caixa-Forte - Cancelar baixa de remessa"),
    CAIXA_FORTE_EXCLUSAO_GERENCIAL_DE_GUIA(10259, "Caixa-Forte - Exclusão gerencial de guia"),
    TESOURARIA(10260, "Tesouraria"),
    ENTRADA_DE_NUMERARIO(10261, "Entrada de numerário"),
    SAIDAS_DE_NUMERARIO(10262, "Saidas de numerário"),
    CONTAS_TESOURARIA(10263, "Contas Tesouraria"),
    SEQUENCIA_DE_GTV(10264, "Sequencia de GTV"),
    RECONTAGEM_DE_NUMERARIO(10265, "Recontagem de Numerário"),
    CUSTODIA(10266, "Custodia"),
    RECALCULO_DE_SALDOS(10267, "Recalculo de Saldos"),
    CONTROLE_DE_LOTES(10268, "Controle de lotes"),
    RELATORIOS_TESOURARIA(10269, "Relatorios"),
    TRANSFERENCIA_DE_LOTES(10270, "Transferência de lotes"),
    TESOURARIA_CADASTRO_DE_MOEDAS(10271, "Tesouraria - Cadastro de moedas"),
    TESOURARIA_FECHAMENTO_DE_MOVIMENTO(10272, "Tesouraria - Fechamento de movimento"),
    TESOURARIA_ALTERACAO_DE_RECONTAGEM(10273, "Tesouraria - Alteração de recontagem"),
    TESOURARIA_CONFERENTE(10274, "Tesouraria - Conferente"),
    TESOURARIA_CUSTODIA_DETALHES_DO_MOVIMENTO(10275, "Tesouraria - Custodia detalhes do movimento"),
    TESOURARIA_MOVIMENTACAO_DE_COFRES(10276, "Tesouraria - Movimentação de cofres"),
    TESOURARIA_GERENCIAMENTO_DE_COFRES(10277, "Tesouraria - Gerenciamento de cofres"),
    TESOURARIA_AUTOMATIZADA(10278, "Tesouraria automatizada"),
    TESOURARIA_CONTROLE_DE_MOVIMENTACAO(10279, "Tesouraria - Controle de Movimentação"),
    SEQUENCIA_DE_MALOTE(10280, "Sequencia de Malote"),
    TESOURARIA_CADASTRO_DE_CUSTODIA_ME(10281, "Tesouraria - Cadastro de Custódia ME"),
    VIGILANCIA_PATRIMONIAL(10300, "Vigilancia patrimonial"),
    VP_CONTROLE_POSTOS(10301, "VP Controle Postos"),
    VP_ESCALA_POR_POSTO(10302, "VP Escala por posto"),
    VP_ESCALA_AUTOMATICA(10303, "VP Escala automática"),
    VP_FOLHA_DE_PONTO(10304, "VP Folha de Ponto"),
    VP_RELATORIOS(10305, "VP Relatórios"),
    VP_CONTROLE_POSTOS_EDICAO_DE_DATA(10306, "VP Controle Postos (edição de data)"),
    OPERACOES_DE_SERVICO(10311, "Operacoes de Serviço"),
    OPERACOES_DE_SERVICO_DETALHES(10312, "Operacoes de serviço (Detalhes)"),
    QUESTIONARIO_DE_SUPERVISAO(10313, "Questionário de supervisão"),
    CONTROLE_OPERACIONAL_DE_EQUIPAMENTOS(10316, "Controle Operacional de Equipamentos"),
    COMERCIAL(20000, "Comercial"),
    CONTATOS(20101, "Contatos"),
    PROPOSTAS(20102, "Propostas"),
    TELEMARKETING(20103, "Telemarketing"),
    CONTRATOS(20104, "Contratos"),
    TIPOS_DE_SERVICO_DO_CLIENTE(20105, "Tipos de Serviço do cliente"),
    DOCUMENTOS(20106, "Documentos"),
    PROCESSOS(20107, "Processos"),
    MODELOS_DE_CARTAS(20108, "Modelos de Cartas"),
    EMISSAO_DE_CARTAS(20109, "Emissão de cartas"),
    INTERFACE_COMERCIAL(20110, "Interface comercial"),
    CONTRATOS_ITENS(20111, "Contratos Itens"),
    CONTRATOS_REAJUSTES(20112, "Contratos Reajustes"),
    CONTRATOS_ANEXOS(20113, "Contratos Anexos"),
    PROPOSTAS_MODELOS(20114, "Propostas - Modelos"),
    CONTRATOS_EFETIVOS(20115, "Contratos Efetivos"),
    FREQUENCIAS(20116, "Frequencias"),
    PROPOSTAS_APROVACAO_GERACAO_OS(20121, "Propostas - Aprovação/Geração OS"),
    PROPOSTAS_APROVACAO_GERACAO_DE_VENDA(20122, "Propostas - Aprovação/Geração de Venda"),
    LIBERACAO_OPERACIONAL_PROPOSTA(20201, "Liberacao operacional proposta"),
    GRUPOS_DE_REAJUSTE(20301, "Grupos de reajuste"),
    CONTRATOS_CONTROLE(20302, "Contratos controle"),
    MAPA_DE_COMISSOES(20303, "Mapa de comissões"),
    GERENCIAL_POR_CENTRO_DE_CUSTOS(20304, "Gerencial por Centro de Custos"),
    WORKFLOW_PROPOSTAS_LIBERACAO(20401, "WorkFlow Propostas liberação"),
    COMUNICACOES(20411, "Comunicações"),
    COMUNICACOES_ADMINISTRADOR(20412, "Comunicações Administrador"),
    COMUNICACOES_OPERACOES(20413, "Comunicações Operacoes"),
    VENDAS(21000, "Vendas"),
    VENDAS_GERAL(21101, "Vendas"),
    VENDAS_DESPACHO(21102, "Vendas Despacho"),
    VENDAS_TIPO_DE_OPERACAO(21121, "Vendas Tipo de Operação"),
    CONDICOES_DE_PAGAMENTO(21901, "Condições de pagamento"),
    FORMAS_DE_PAGAMENTO(21902, "Formas de pagamento"),
    NATUREZA_DA_OPERACAO(21903, "Natureza da operação"),
    TIPOS_DE_TRIBUTACAO(21904, "Tipos de tributação"),
    FATURAMENTO(30000, "Faturamento"),
    TRANSP_VALORES(30100, "Transp.Valores"),
    FECHAMENTO_DE_ROTA(30101, "Fechamento de rota"),
    GERAR_LANCAMENTOS(30102, "Gerar lançamentos"),
    GUIAS_DE_FATURAMENTO(30103, "Guias de faturamento"),
    CALCULAR_FATURAMENTO_TV(30104, "Calcular faturamento TV"),
    GERACAO_DE_NOTAS_FISCAIS(30105, "Geração de notas fiscais"),
    PLANILHA_DE_FATURAMENTO(30106, "Planilha de faturamento"),
    GUIAS_REJEITADAS(30107, "Guias rejeitadas"),
    EXCLUSAO_PLANILHA_NF(30108, "Exclusão Planilha NF"),
    PLANILHA_DE_FATURAMENTO_DETALHES(30109, "Planilha de faturamento Detalhes"),
    ORDENS_DE_SERVICO(30111, "Ordens de Serviço"),
    CONJUGACOES(30112, "Conjugações"),
    FATURAMENTO_POSICAO_FECHAMENTOS_POR_PERIODO(30116, "Faturamento - Posição de fechamentos por período"),
    FECHA_GTV(30121, "Fecha GTV"),
    FECHA_GTV_MANUAL(30122, "Fecha GTV Manual"),
    GERAR_OS_RT_GUIAS_X_ROTAS(30123, "Gerar OS Rt Guias x Rotas"),
    FATTV_VERIFICA_CONCOMITANCIAS(30124, "FatTV - Verifica concomitancias"),
    FATTV_VERIFICA_GUIAS_FATURADAS_NF(30125, "FatTV - Verifica Guias Faturadas NF"),
    FATTV_AUTORIZA_GUIAS_FATURAMENTO_FECHADO(30126, "FatTV - Autoriza Guias Faturamento Fechado"),
    MALOTE(30127, "Malote"),
    OS_ITENS_EXTRAORDINARIOS(30131, "OS - Itens extraordinários"),
    FATURAMENTO_LEGADO_PRODUTOS(30141, "Faturamento Legado Produtos"),
    FATURAMENTO_LEGADO_CLIENTES(30142, "Faturamento Legado Clientes"),
    FATURAMENTO_INTERF_ITAU(30151, "Faturamento Interf Itau"),
    FATURAMENTO_INTERF_BRADESCO(30152, "Faturamento Interf Bradesco"),
    FATURAMENTO_INTERF_REAL(30153, "Faturamento Interf Real"),
    FATURAMENTO_INTERF_TB(30154, "Faturamento Interf TB"),
    FATURAMENTO_VIGILANCIA(30200, "Faturamento Vigilância"),
    CALCULAR_FATURAMENTO_SERVICOS(30201, "Calcular faturamento serviços"),
    GERAR_FATURAS(30202, "Gerar faturas"),
    FATURAMENTO_MANUAL(30203, "Faturamento manual"),
    PLANILHA_DE_FATURAMENTO_2(30204, "Planilha de faturamento"),
    FATURAMENTO_ELETRONICA(30300, "Faturamento Eletrônica"),
    NOTAS_FISCAIS_FATURAMENTO_VENDAS(30400, "Notas Fiscais/Faturamento vendas"),
    NOTAS_FISCAIS(30401, "Notas Fiscais"),
    HISTORICO_DE_FATURAMENTO(30402, "Historico de faturamento"),
    TIPO_DE_COBRANCA(30403, "Tipo de Cobrança"),
    AGRUPADOR_DE_NF(30404, "Agrupador de NF"),
    RESUMO_DE_NF(30405, "Resumo de NF"),
    RANKING_DE_FATURAMENTO(30406, "Ranking de faturamento"),
    PRACAS_DE_FATURAMENTO(30407, "Praças de Faturamento"),
    GERACAO_DE_OS(30408, "Geração de OS"),
    NF_ALTERACAO_COM_EXTRATO_GERADO(30409, "NF-Alteração com extrato gerado"),
    ALTERAR_NUMERO_DE_NOTA_FISCAL_EMITIDA(30410, "Alterar número de nota fiscal emitida"),
    FATURAR_VENDAS(30411, "Faturar vendas"),
    CODIGOS_FISCAIS(30901, "Codigos Fiscais"),
    PERFIS_FISCAIS(30902, "Perfis Fiscais"),
    RECURSOS_HUMANOS(40000, "Recursos humanos"),
    TRANSFERENCIA_DE_FUNCIONARIOS(40101, "Transferencia de Funcionários"),
    AVISO_PREVIO(40102, "Aviso prévio"),
    PESSOAS(40103, "Pessoas"),
    TURNOS_DE_ESCALA(40104, "Turnos de Escala"),
    HORARIOS_DE_TRABALHO(40105, "Horários de Trabalho"),
    TABELA_DE_VERBAS(40106, "Tabela de Verbas"),
    SINDICATOS(40107, "Sindicatos"),
    CARGOS(40108, "Cargos"),
    CALENDARIO(40109, "Calendário"),
    CID(40110, "CID"),
    FUNCIONARIOS(40111, "Funcionários"),
    POSTOS_DE_SERVICO(40112, "Postos de Serviço"),
    GRAU_DE_RISCO_DE_ACIDENTES(40113, "Grau de Risco de Acidentes"),
    TRANSFERENCIA_ENTRE_FILIAIS(40114, "Transferência entre filiais"),
    TRANSFERENCIA_ENTRE_EMPRESAS(40115, "Transferência entre empresas"),
    ACESSO_AOS_DADOS_DE_DIRETORES(40116, "Acesso aos dados de diretores"),
    FICHA_DE_FUNCIONARIOS_IMPRESSAO(40120, "Ficha de funcionários(Impressão)"),
    FICHA_DE_FUNCIONARIOS(40121, "Ficha de funcionários"),
    FUNCIONARIOS_SALARIOS(40122, "Funcionários (Salários)"),
    FUNCIONARIOS_RESUMO(40123, "Funcionários (Resumo)"),
    FUNCIONARIOS_REAJUSTES(40124, "Funcionários (Reajustes)"),
    FUNCIONARIOS_REAJUSTES_DE_VERBAS_FIXAS(40125, "Funcionários (Reajustes de verbas fixas)"),
    FUNCIONARIOS_ALTERACAO_DE_SITUACAO(40126, "Funcionários (Alteração de situação)"),
    FUNCIONARIOS_ALTERACAO_DE_MATRICULA(40127, "Funcionários (Alteração de matrícula)"),
    MOVIMENTACAO_DE_CARGOS_E_SALARIOS(40131, "Movimentação de Cargos e Salários"),
    POSTOS_DE_SERVICO_EQUIPAMENTOS(40151, "Postos de serviço (Equipamentos)"),
    POSTOS_DE_SERVICO_DEPENDENCIAS(40152, "Postos de serviço (Dependencias)"),
    POSTOS_DE_SERVICO_HISTORICO(40153, "Postos de serviço (Histórico)"),
    POSTOS_DE_SERVICO_RESTRICOES(40154, "Postos de serviço (Restrições)"),
    PESSOAS_DADOS_PESSOAIS(40161, "Pessoas (Dados pessoais)"),
    PESSOAS_PERFIL_PROFISSIONAL(40162, "Pessoas (Perfil profissional)"),
    PESSOAS_DOCUMENTOS(40163, "Pessoas (Documentos)"),
    PESSOAS_AVALIACAO(40164, "Pessoas (Avaliação)"),
    PESSOAS_PRE_SELECAO(40165, "Pessoas (Pré-seleção)"),
    CIAT_COMUNICACAO_DE_ACIDENTE_DE_TRABALHO(40166, "CIAT Comunicação de Acidente de Trabalho"),
    MONITORAMENTO_DA_SAUDE_DO_TRABALHADOR(40167, "Monitoramento da Saúde do Trabalhador"),
    ADMINISTRADOR_DO_PORTAL(40191, "Administrador do portal"),
    CONTROLE_DE_PONTO(40200, "Controle de Ponto"),
    CALCULAR_HORAS_AUTOMATICO(40201, "Calcular horas - Automatico"),
    BARRAMENTO_DE_FOLHA_DE_PONTO(40202, "Barramento de folha de ponto"),
    CALCULAR_HORAS_ELETRONICO(40203, "Calcular horas - Eletronico"),
    REGISTROS_DE_PONTO(40204, "Registros de ponto"),
    VERIFICAR_INCONSISTENCIAS(40205, "Verificar inconsistências"),
    IMPORTACAO_RELOGIO(40206, "Importação relógio"),
    VERIFICA_PONTO_X_ESCALA(40207, "Verifica ponto x escala"),
    PLANILHA_DE_PONTO(40210, "Planilha de ponto"),
    ADMINISTRACAO_DE_CALCULO(40211, "Administração de Cálculo"),
    REGISTRO_DE_PONTO_CARGA_SUPERIOR(40212, "Registro de Ponto Carga Superior"),
    IMPRESSAO_FOLHA_DE_PONTO(40213, "Impressão Folha de Ponto"),
    FOLHA_DE_PAGAMENTO(40300, "Folha de pagamento"),
    LANCAMENTOS_DE_FOLHA(40301, "Lançamentos de Folha"),
    VERBAS_FOLHA_PAGAMENTO(40302, "Verbas folha pagamento"),
    INTERFACES_FP(40303, "Interfaces FP"),
    INTERFACE_RM(40304, "Interface RM"),
    PERIODOS_DE_MOVIMENTO(40311, "Periodos de movimento"),
    RESCISOES(40312, "Rescisões"),
    FERIAS(40313, "Férias"),
    CALCULO_GERAL_DE_FOLHA(40321, "Cálculo Geral de folha"),
    CALCULO_INDIVIDUAL_DE_FOLHA(40322, "Cálculo Individual de Folha"),
    GERACAO_DE_LANCAMENTOS_DE_FOLHA(40323, "Geração de Lançamentos de Folha"),
    IMPORTACAO_DE_LANCAMENTOS_DE_FOLHA(40324, "Importação de Lançamentos de Folha"),
    RESCISOES_DESTRAVAR(40325, "Rescisões (Destravar)"),
    TABELAS_DA_FOLHA_DE_PAGAMENTO(40351, "Tabelas da folha de pagamento"),
    BENEFICIOS(40400, "Beneficios"),
    VT_OPTANTES(40401, "VT Optantes"),
    VT_GERACAO(40402, "VT Geração"),
    VT_RELACAO(40403, "VT Relação"),
    VT_LINHAS(40404, "VT Linhas"),
    VA_OPTANTES(40411, "VA Optantes"),
    VA_GERACAO(40412, "VA Geração"),
    VA_RELACAO(40413, "VA Relação"),
    VA_CONVENIOS(40414, "VA Convênios"),
    UNIFORMES(40500, "Uniformes"),
    PEDIDOS_DE_UNIFORMES(40501, "Pedidos de uniformes"),
    UNIFORMES_A_SUBSTITUIR(40502, "Uniformes a substituir"),
    ENTREGA_DE_UNIFORMES(40503, "Entrega de uniformes"),
    CADASTROS(50000, "Cadastros"),
    SATELLITE(50100, "Satellite"),
    CLIENTES(50101, "Clientes"),
    REGIAO_PARA_LOCALIZACAO(50102, "Região para Localização"),
    RISCO_OPERACIONAL(50103, "Risco Operacional"),
    BANCOS(50104, "Bancos"),
    ARMAS(50105, "Armas"),
    VEICULOS(50106, "Veículos"),
    REGIONAIS(50107, "Regionais"),
    MATERIAIS_OPERACIONAIS(50108, "Materiais operacionais"),
    TABELAS(50109, "Tabelas"),
    MUNICIPIOS(50110, "Municipios"),
    COLETES(50111, "Coletes"),
    RAMOS_ATIVIDADE(50112, "Ramos atividade"),
    ESPECIALIDADES_FUNCIONARIO(50113, "Especialidades Funcionário"),
    CHAVES(50114, "Chaves"),
    MUNICOES(50115, "Municoes"),
    EAGLE(50200, "Eagle"),
    PRODUTOS(50201, "Produtos"),
    FORNECEDORES(50202, "Fornecedores"),
    PRODUTOS_MANUTENCAO_DE_ESTOQUES(50203, "Produtos - Manutenção de Estoques"),
    CLIENTES_EAGLE(50204, "Clientes Eagle"),
    CONTROLE_DE_VEICULOS(51000, "Controle de Veiculos"),
    VEICULOS_ABASTECIMENTOS(51001, "Veiculos - Abastecimentos"),
    VEICULOS_MODELOS(51002, "Veiculos - Modelos"),
    VEICULOS_ANALISE_ABASTECIMENTOS(51003, "Veiculos - Analise Abastecimentos"),
    VEICULOS_MANUTENCAO(51004, "Veiculos - Manutenção"),
    GERAL(59900, "Geral"),
    FILIAIS(59901, "Filiais"),
    CENTROS_DE_CUSTO(59902, "Centros de Custo"),
    APOIO(60000, "Apoio"),
    INTERFACES(60100, "Interfaces"),
    SISEV(60101, "SISEV"),
    INTERFACE_LIDER(60102, "Interface LIDER"),
    FERRAMENTAS_DO_ADMINISTRADOR(60103, "Ferramentas do Administrador"),
    CONVERSAO_DBF(60104, "Conversão DBF"),
    IMPORTACAO(60105, "Importação"),
    INTEGRIDADE(60111, "Integridade"),
    SELECIONAR_BANCO_DE_DADOS(60112, "Selecionar banco de dados"),
    CONTROLE_DE_USUARIOS_SENHAS(60113, "Controle de Usuários/Senhas"),
    USUARIOS_SENHAS_LIBERAR_ACESSOS(60114, "Usuarios/Senhas Liberar acessos"),
    USUARIOS_SENHAS_LIBERAR_FILIAIS(60115, "Usuarios/Senhas Liberar filiais"),
    ADMINISTRACAO_DE_BANCO_DE_DADOS(60116, "Administração de Banco de Dados"),
    BI_SATELLITE(60117, "BI Satellite"),
    PARAMETROS_DO_SISTEMA(60118, "Parâmetros do sistema"),
    PARAMETROS_SATELLITE_SERVER(60119, "Parâmetros Satellite Server"),
    MANUTENCAO_DE_GEOREFERENCIA(60120, "Manutenção de georreferência"),
    LINGUAGEM_DO_SISTEMA(60121, "Linguagem do sistema"),
    SEGURANCA(90000, "Segurança"),
    ACESSO_SOLICITACAO_DE_AUTORIZACAO(90101, "Acesso - Solicitação de autorização"),
    CONTROLE_DE_ACESSO(90102, "Controle de acesso"),
    RELATORIO_DE_ACESSOS(90103, "Relatório de Acessos"),
    AREAS_DE_SEGURANCA(90104, "Áreas de segurança"),
    AUTORIZAR_ACESSO(90105, "Autorizar acesso"),
    AUTORIZAR_ACESSO_MESMO_DIA(90106, "Autorizar acesso mesmo dia"),
    ACESSO_A_AREA_RESTRITA(90107, "Acesso a área restrita"),
    FUNCIONARIOS_DADOS_DE_FORMACAO(90110, "Funcionários dados de formação"),
    CHAVES_MOVIMENTACAO_MODELO_I(90111, "Chaves - Movimentação (modelo I)"),
    CHAVES_MOVIMENTACAO_MODELO_II(90112, "Chaves - Movimentação (modelo II)"),
    CHAVES_MOVIMENTACAO_MODELO_III(90113, "Chaves - Movimentação (modelo III)"),
    CHAVES_MOVIMENTACAO_MODELO_III_DUPLICADO(90114, "Chaves - Movimentação (modelo III)"),
    LIMITES_DE_SEGURO(90115, "Limites de seguro"),
    FINANCEIRO(101000, "Financeiro"),
    CONTAS_A_RECEBER(101100, "Contas a Receber"),
    CONTAS_A_RECEBER_DUPLICADO(101101, "Contas a receber"),
    ORDEM_DE_CREDITO(101102, "Ordem de Crédito"),
    CONTAS_A_RECEBER_ALTERACAO_DE_NOSSO_NUMERO(101111, "Contas a receber - alteração de nosso número"),
    CONTAS_A_RECEBER_CENTRAL_DE_BOLETOS(101112, "Contas a receber - Central de boletos"),
    CONTAS_A_RECEBER_REMESSAS_DE_TITULOS_PARA_COBRANCA(101113, "Contas a receber - Remessas de títulos para cobran"),
    CONTAS_A_RECEBER_COBRANCA_ELETRONICA(101114, "Contas a receber - Cobrança Eletrônica"),
    CONTAS_A_PAGAR(101200, "Contas a Pagar"),
    CONTAS_A_PAGAR_DUPLICADO(101201, "Contas a pagar"),
    ORDEM_DE_PAGAMENTO(101202, "Ordem de Pagamento"),
    AUTORIZAR_PAGAMENTO_CONTAS(101203, "Autorizar pagamento contas"),
    FUNDO_FIXO(101211, "Fundo Fixo"),
    MOVIMENTACAO_BANCARIA(101300, "Movimentação bancária"),
    BANCOS_FINANCEIRO(101301, "Bancos"),
    CONTAS_BANCARIAS(101302, "Contas bancárias"),
    TRANSFERENCIA_BANCARIA(101303, "Transferencia bancária"),
    RESUMO_FINANCEIRO(101401, "Resumo Financeiro"),
    MOVIMENTO_DE_CONTAS_FINANCEIRAS(101402, "Movimento de contas Financeiras"),
    FLUXO_DE_CAIXA(101403, "Fluxo de caixa"),
    FLUXO_DE_CAIXA_ANALISE_DIARIA(101406, "Fluxo de caixa - análise diária"),
    FECHAMENTO_FINANCEIRO(101411, "Fechamento financeiro"),
    CONTABILIDADE(101500, "Contabilidade"),
    CONTAS_CONTABEIS(101501, "Contas contábeis"),
    LANCAMENTOS_CONTABEIS(101502, "Lançamentos contábeis"),
    CONTAS_CONTABEIS_DE_REFERENCIA_SRF(101503, "Contas contábeis de referência (SRF)"),
    GERACAO_AUTOMATICA_DE_LANCAMENTOS_CONTABEIS(101504, "Geração automática de lançamentos contábeis"),
    PERIODOS_DE_FECHAMENTO_CONTABIL(101511, "Periodos de fechamento contábil"),
    GRUPO_CONTAS_FINANCEIRAS(101901, "Grupo Contas financeiras"),
    CONTAS_FINANCEIRAS(101902, "Contas financeiras"),
    TIPOS_DE_TITULO(101903, "Tipos de título"),
    CONTROLE_DE_ESTOQUE(102100, "Controle de estoque"),
    ORDEM_DE_COMPRA(102101, "Ordem de compra"),
    RECEBE_ORDEM_DE_COMPRA(102102, "Recebe Ordem de Compra"),
    AUTORIZA_DE_ORDEM_DE_COMPRA(102103, "Autoriza de Ordem de Compra"),
    COTACOES(102106, "Cotações"),
    PEDIDO_DE_MATERIAL(102201, "Pedido de material"),
    CAIXAS(102202, "Caixas"),
    BAIXA_DE_PEDIDOS_DE_MATERIAL(102203, "Baixa de pedidos de material"),
    AUTORIZA_DE_PEDIDOS_DE_MATERIAL(102204, "Autoriza de pedidos de material"),
    CONTRATANTES(200001, "Contratantes"),
    CLIENTES_FASTCOB(200002, "Clientes Fastcob"),
    CEP(200011, "CEP"),
    TITULOS_PARA_COBRANCA(201001, "Titulos para cobrança"),
    DEBITOS_DO_CLIENTE(201011, "Debitos do cliente"),
    CALCULO(202001, "Cálculo"),
    INDICES(202002, "Indices"),
    TABELA_DE_CALCULO(202003, "Tabela de cálculo"),
    ROTAS_MOBILE_TRANSPORTE(300001, "Rotas mobile Transporte"),
    PONTO_MOBILE(300002, "Ponto mobile"),
    PORTAL_RH(300003, "Portal RH"),
    ROTAS_MOBILE_PATRIMONIAL(300004, "Rotas mobile patrimonial"),
    ABASTECIMENTO_MOBILE(300005, "Abastecimento Mobile"),
    VERFICACAO_CHAVES(300006, "Verficacao Chaves"),
    VERIFICACAO_GUIAS_E_LACRES(300007, "Verificacao Guias e Lacres"),
    ADICAO_DE_PARADA_ROTA_TV(300008, "Adicao de Parada Rota TV"),
    RELATORIOS_SATMOB_EW(541100, "Relatórios SatmobEW"),
    INSPECOES_SATELLITE(541113, "Inspeções Satellite"),
    TESOURARIA_SANGRIAS(610265, "Tesouraria - Sangrias"),
    TRAJETOS_DE_ROTAS(910202, "Trajetos de Rotas"),
    GERENCIAL_PONTUALIDADE(910207, "Gerencial Pontualidade"),
    GERENCIAL_ROTAS(910208, "Gerencial Rotas"),
    GERENCIAL_ROTAS_OPERACIONAL(910210, "Gerencial Rotas Operacional"),
    ESTATISTICA_TRANSP_VALORES(910211, "Estatistica Transp Valores"),
    COMUNICACOES_COAF(910212, "Comunicações COAF");

    private final Integer codigo;
    private final String descricao;

    Permissoes(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

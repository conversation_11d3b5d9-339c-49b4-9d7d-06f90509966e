<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMob}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body>

            <f:metadata>
                <f:viewAction action="#{rotasContainer.Persistencia(login.pp, login.satellite)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>

                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>

                <ui:define name="infoFilial">
                    <div class="ui-grid-row">
                        <h:outputText value="#{rotasContainer.filiais.descricao}" class="negrito"/>
                    </div>
                    <div class="ui-grid-row">
                        #{rotasContainer.filiais.endereco}
                    </div>
                    <div class="ui-grid-row">
                        #{rotasContainer.filiais.bairro}<h:outputText value=", " rendered="#{rotasContainer.filiais.bairro ne null and container.filiais.bairro ne ''}"/>#{rotasContainer.filiais.cidade}/#{rotasContainer.filiais.UF}
                    </div>
                </ui:define>

                <ui:define name="seletorData">
                    <h:outputText value="#{localemsgs.Data}: "/>
                    <h:outputText id="dataDia" value="#{rotasContainer.dataTela}" converter="conversorDia"/>
                    <p:commandLink action="#{rotasContainer.rotaAnterior}" update="main:tabela header">
                        <p:graphicImage url="../assets/images/botao_anterior.png" style="height: 40px"/>  
                    </p:commandLink>

                    <p:calendar id="calendario" showOn="button" navigator="true" styleClass="calendario" converter="conversorData"
                                pattern="#{mascaras.padraoData}" value="#{rotasContainer.dataTela}"
                                locale="#{localeController.getCurrentLocale()}">
                        <p:ajax event="dateSelect" listener="#{rotasContainer.selecionarData}" update="main header" />
                    </p:calendar>
                    

                    <p:commandLink action="#{rotasContainer.rotaPosterior}" update="main:tabela header">
                        <p:graphicImage url="../assets/images/botao_proximo.png" style="height: 40px"/>  
                    </p:commandLink>
                </ui:define>


                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="main"> 
                            <p:panel id="painelPesquisa">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1">
                                    <p:tab title="#{localemsgs.Pesquisar}">
                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3 pesquisa,ui-grid-col-3 pesquisa,
                                                     ui-grid-col-3 pesquisa,ui-grid-col-3 pesquisa" 
                                                     layout="grid">
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaRota" value="#{localemsgs.Rota}: "/>
                                                <p:inputText id="pesquisaRota" value="#{rotasContainer.pesquisaRotaRota}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaRota" value="#{localemsgs.Rota}"/>
                                                </p:inputText>  
                                            </p:panelGrid>  
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaVeiculo" value="#{localemsgs.Veiculo}: "/>
                                                <p:inputText id="pesquisaVeiculo" value="#{rotasContainer.pesquisaRotaVeiculo}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaVeiculo" value="#{localemsgs.Veiculo}"/>
                                                </p:inputText>  
                                            </p:panelGrid>  
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaMotorista" value="#{localemsgs.Motorista}: "/>
                                                <p:inputText id="pesquisaMotorista" value="#{rotasContainer.pesquisaRotaMotorista}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaMotorista" value="#{localemsgs.Motorista}"/>
                                                </p:inputText>  
                                            </p:panelGrid>  
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaChEquip" value="#{localemsgs.ChEquipe}: "/>
                                                <p:inputText id="pesquisaChEquip" value="#{rotasContainer.pesquisaRotaChEquip}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaChEquip" value="#{localemsgs.ChEquipe}"/>
                                                </p:inputText>  
                                            </p:panelGrid>  
                                        </p:panelGrid>
                                        <p:commandButton action="#{rotasContainer.pesquisar}" update="msgs main:tabela main:painelPesquisa" styleClass="botao" value="#{localemsgs.Pesquisar}">                           
                                        </p:commandButton>
                                        <p:commandButton action="#{rotasContainer.limparPesquisa}" update="msgs main:tabela main:painelPesquisa" styleClass="botao" value="#{localemsgs.Limpar}">                           
                                        </p:commandButton>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:panel>

                            <p:panel styleClass="painelCadastro">
                                <p:dataGrid id="tabela" value="#{rotasContainer.allRotas}" paginator="true" rows="50" lazy="true"
                                            rowsPerPageTemplate="5,10,15,20,25,50"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.RotasValores}"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                            var="listaRotas" emptyMessage="#{localemsgs.SemRegistros}"
                                            columns="1">
                                    <p:panel header="#{localemsgs.Rota} #{listaRotas.rota} - #{localemsgs.Veiculo}: #{listaRotas.veiculo} - #{localemsgs.VeiculoModelo}: #{listaRotas.modeloVeiculo} - #{localemsgs.Placa}: #{listaRotas.placa} " style="text-justify">                                        
                                        <div class="ui-g">
                                            <span class="text-blue">#{localemsgs.Horario}: #{listaRotas.hrLargada} - #{listaRotas.hrChegada} #{localemsgs.Intervalo}: #{listaRotas.hrIntIni} - #{listaRotas.hrIntFim} #{localemsgs.TotalHoras}: #{listaRotas.hsTotal}</span>
                                            <div class="ui-g-12 ui-lg-12">
                                                <b>#{localemsgs.ChefeEquipe}:</b> #{localemsgs.ChEquipe}: <h:outputText value="#{listaRotas.nome}"/> - #{localemsgs.Motorista}: <h:outputText value="#{listaRotas.nomeMotorista}"/>
                                            </div>
                                            <div class="ui-g-12 ui-md-6 ui-lg-4">
                                                #{localemsgs.Data}: <h:outputText value="#{listaRotas.data}" converter="conversorDia" />
                                            </div>
                                            <div class="ui-g-12 ui-md-6 ui-lg-8">
                                                #{localemsgs.Paradas}: <h:outputText value="#{listaRotas.qtdTrajetos}">
                                                    <f:convertNumber pattern="0000"/>
                                                </h:outputText>     
                                            </div>
                                            <div class="ui-g-12 ui-md-6 ui-lg-4">
                                                #{localemsgs.ValorTotal}: <h:outputText value="#{listaRotas.valor}" converter="conversormoeda"/>
                                            </div>
                                            <div class="ui-g-12 ui-md-6 ui-lg-8">
                                                #{localemsgs.Volumes}: 
                                                <h:outputText value="#{listaRotas.qtdVolumes}">
                                                    <f:convertNumber pattern="0000"/>
                                                </h:outputText>
                                            </div>
                                            <div class="ui-g-12 ui-md-6 ui-lg-2">
                                                <p:commandLink title="#{localemsgs.Editar}" action="#{rotasContainer.listaTrajetos(listaRotas)}" update="msgs">
                                                    <h:outputText styleClass="fa fa-edit" style="margin:0 auto;"/>
                                                </p:commandLink>
                                            </div>

                                            <div class="ui-g-12 ui-md-6 ui-lg-2">
                                                <p:commandLink title="#{localemsgs.Escala}" update="msgs cadastroEscala"
                                                               action="#{rotasContainer.abrirEscala(listaRotas)}">
                                                    <h:outputText styleClass="fa fa-calendar" style="margin:0 auto;"/>
                                                </p:commandLink>
                                            </div>

                                            <div class="ui-g-12 ui-md-6 ui-lg-2">
                                                <p:commandLink title="#{localemsgs.Trajetos}" action="#{rotasContainer.carregarMapa('1', listaRotas.sequencia, listaRotas.codFil.toString())}" update="msgs">
                                                    <h:outputText styleClass="fas fa-map-marked-alt" style="margin:0 auto;"/>
                                                </p:commandLink>
                                            </div>

                                            <div class="ui-g-12 ui-md-6 ui-lg-2">
                                                <p:commandLink title="#{localemsgs.Trajetos}" action="#{rotasContainer.carregarMapa('2', listaRotas.sequencia, listaRotas.codFil.toString())}" update="msgs">
                                                    <h:outputText styleClass="fas fa-location-arrow" style="margin:0 auto;"/>
                                                </p:commandLink>
                                            </div>                                                                                 
                                        </div>
                                    </p:panel>
                                </p:dataGrid> 
                            </p:panel>
                            <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">

                                <div style="padding-bottom: 10px;">
                                    <p:commandLink title="#{localemsgs.Adicionar}"
                                                   update="msgs" action="#{rotasContainer.prepararCadastro}">
                                        <p:graphicImage url="../assets/images/icone_redondo_adicionar.png" height="40"/>
                                    </p:commandLink>
                                </div>

                                <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                            </p:panel>
                        </h:form>                        

                        <!-- MODAL DE ROTAS-->
                        <h:form id="cadastroRota"> 
                            <p:dialog styleClass="box-primary"  widgetVar="dlgCadastrar" minHeight="500" modal="true"  appendTo="@(body)"
                                      id="dlgCadastrar" responsive="true" dynamic="true" style="background-size: 750px 430px;">
                                <script>
                                    $(document).ready(function() {
                                    //first unbind the original click event
                                    PF('dlgCadastrar').closeIcon.unbind('click');
                                            //register your own
                                            PF('dlgCadastrar').closeIcon.click(function(e) {
                                    $("#cadastroRota\\:botaoFechar").click();
                                            //should be always called
                                            e.preventDefault();
                                    });
                                    }; );
                                </script>
                                <p:commandButton widgetVar="botaoFechar" style="display: none"
                                                 oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                                </p:commandButton>
                                <f:facet name="header">
                                    <img src="../assets/img/icones_satmob_carroforte.png" height="40" width="40"/>
                                    <p:spacer width="5px"/>
                                    <h:outputText value="#{localemsgs.CadastrarRota}" style="color:black" /> 
                                </f:facet>
                                <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                                    <p:confirmDialog global="true">
                                        <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                        <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                    </p:confirmDialog>
                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="subFil" value="#{localemsgs.Filial}:"  />
                                        <p:selectOneMenu id="subFil" value="#{rotasContainer.filial}" converter="omnifaces.SelectItemsConverter" 
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%" label=""
                                                         disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}">
                                            <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}" 
                                                           itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                            <p:ajax event="itemSelect" listener="#{rotasContainer.SelecionarFilial}"
                                                    update="cadastroRota:rutas"/>
                                        </p:selectOneMenu>
                                    </p:panelGrid>

                                    <p:panelGrid columns="6" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-2,ui-grid-col-2,ui-grid-col-3" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="rutas" value="#{localemsgs.Rota}:"/>
                                        <p:inputText id="rutas" value="#{rotasContainer.novaRota.rota}"
                                                     required="true" label="#{localemsgs.Rota}"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                                     style="width: 100%"
                                                     maxlength="3" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}">
                                            <p:watermark for="rutas" value="#{localemsgs.Rota}"/>
                                        </p:inputText> 

                                        <p:outputLabel for="data" value="#{localemsgs.Data}: " />
                                        <p:inputMask id="data" value="#{rotasContainer.novaRota.data}" mask="99/99/9999"
                                                     required="true" label="#{localemsgs.Data}"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                     style="width: 100%"
                                                     maxlength="8" placeholder="00/00/0000"
                                                     converter="conversorData" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}">
                                        </p:inputMask>

                                        <p:outputLabel for="tpvei" value="#{localemsgs.TpVeic}: "/>
                                        <p:selectOneMenu value="#{rotasContainer.novaRota.tpVeic}" 
                                                         style="width: 100%"  required="true"
                                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                                         id="tpvei" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}">
                                            <f:selectItem itemLabel="#{localemsgs.Selecione}"/>
                                            <f:selectItem itemLabel="#{localemsgs.Forte}" itemValue="F"/>
                                            <f:selectItem itemLabel="#{localemsgs.Leve}" itemValue="L"/>
                                            <f:selectItem itemLabel="#{localemsgs.Moto}" itemValue="M"/>
                                            <f:selectItem itemLabel="#{localemsgs.Pesado}" itemValue="P"/>
                                            <f:selectItem itemLabel="#{localemsgs.Aeronave}" itemValue="A"/>
                                            <f:selectItem itemLabel="#{localemsgs.Nenhum}" itemValue="N"/>

                                            <p:ajax update="msgs infoForte" oncomplete="PF('dlgCadastrar').initPosition()"/>
                                        </p:selectOneMenu>
                                    </p:panelGrid>

                                    <p:panelGrid columns="10" columnClasses="ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1,
                                                 ui-grid-col-2,ui-grid-col-1,ui-grid-col-2,ui-grid-col-1" id="infoForte"
                                                 layout="grid" styleClass="ui-panelgrid-blank" >
                                        <p:outputLabel for="viagem" value="#{localemsgs.Viagem}: "
                                                       rendered="#{rotasContainer.novaRota.tpVeic == 'F' }"/>
                                        <p:inputText id="viagem" value="#{rotasContainer.novaRota.viagem}"
                                                     style="width: 100%" validatorMessage="#{localemsgs.Viagem}"
                                                     maxlength="1" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}"
                                                     rendered="#{rotasContainer.novaRota.tpVeic == 'F' }">
                                            <f:validateRegex pattern="[SYNsyn]"  />
                                            <p:ajax update="msgs" event="keyup" />
                                        </p:inputText>

                                        <p:outputLabel for="atm" value="#{localemsgs.ATM}: "
                                                       rendered="#{rotasContainer.novaRota.tpVeic == 'F' }"/>
                                        <p:inputText id="atm" value="#{rotasContainer.novaRota.ATM}"
                                                     style="width: 100%"
                                                     maxlength="1" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}"
                                                     rendered="#{rotasContainer.novaRota.tpVeic == 'F' }">
                                            <f:validateRegex pattern="[SYNsyn]"  />
                                            <p:ajax update="msgs" event="keyup" />
                                        </p:inputText>

                                        <p:outputLabel for="bacen" value="#{localemsgs.Bacen}: "
                                                       rendered="#{rotasContainer.novaRota.tpVeic == 'F' }"/>
                                        <p:inputText id="bacen" value="#{rotasContainer.novaRota.BACEN}"
                                                     style="width: 100%"
                                                     maxlength="1" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}"
                                                     rendered="#{rotasContainer.novaRota.tpVeic == 'F' }">
                                            <f:validateRegex pattern="[SYNsyn]"  />
                                            <p:ajax update="msgs" event="keyup" />
                                        </p:inputText>

                                        <p:outputLabel for="aeroporto" value="#{localemsgs.Aeroporto}: "
                                                       rendered="#{rotasContainer.novaRota.tpVeic == 'F' }"/>
                                        <p:inputText id="aeroporto" value="#{rotasContainer.novaRota.aeroporto}"
                                                     style="width: 100%"
                                                     maxlength="1" disabled="#{rotasContainer.flag eq 2 or container.novaRota.flag_Excl eq '*'}"
                                                     rendered="#{rotasContainer.novaRota.tpVeic == 'F' }">
                                            <f:validateRegex pattern="[SYNsyn]"  />
                                            <p:ajax update="msgs" event="keyup" />
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2, ui-grid-col-2, ui-grid-col-1, ui-grid-col-2"
                                                 layout="grid" styleClass="ui-panelgrid-blank">

                                        <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}: "/>
                                        <p:inputMask id="hrLargada" value="#{rotasContainer.novaRota.hrLargada}"
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true" placeholder="00:00" 
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                                     maxlength="4" style="width: 105%" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="hrChegada" value="#{localemsgs.a}" style="text-align: center; width: 100%"/>
                                        <p:inputMask id="hrChegada" value="#{rotasContainer.novaRota.hrChegada}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true"  placeholder="00:00"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                                     maxlength="4" style="width: 105%" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" 
                                                    listener="#{rotasContainer.calculaHorasTrabalhadas}" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo}: "/>
                                        <p:inputMask id="hrIntIni" value="#{rotasContainer.novaRota.hrIntIni}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true" placeholder="00:00" style="width: 105%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                                     maxlength="4" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="hrIntFim" value="#{localemsgs.a}" style="text-align: center; width: 100%"/>
                                        <p:inputMask id="hrIntFim" value="#{rotasContainer.novaRota.hrIntFim}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true"  placeholder="00:00" 
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                                     maxlength="4" style="width: 105%" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" 
                                                    listener="#{rotasContainer.calculaHorasTrabalhadas}" event="blur"/>
                                        </p:inputMask>


                                        <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}: " escape="false"/>
                                        <p:inputText id="totalhr" value="#{rotasContainer.novaRota.hsTotal}" 
                                                     maxlength="4" style="width: 100%"
                                                     disabled="true">
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="obs" value="#{localemsgs.Obs}: " />
                                        <p:inputText id="obs" value="#{rotasContainer.novaRota.observacao}" style="width: 100%"
                                                     label="#{localemsgs.Obs}" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}" >
                                            <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:commandLink rendered="#{rotasContainer.flag eq 1}" id="cadastro"
                                                       update="main:tabela :msgs" actionListener="#{rotasContainer.cadastrar}"
                                                       title="#{localemsgs.Cadastrar}" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                        <p:commandLink rendered="#{rotasContainer.flag eq 2}" id="edit" action="#{rotasContainer.editar}"
                                                       update=":msgs main:tabela cadastrar"
                                                       title="#{localemsgs.Editar}" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                            <f:viewAction action="#{rotasContainer.ListarData}"/>
                                            <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                        </p:commandLink>
                                    </p:panelGrid>


                                    <p:panelGrid columns="1" columnClasses="ui-grid-col-12"
                                                 layout="grid" styleClass="ui-panelgrid-blank tabs" id="panelTabelaSupervisao"
                                                 style="background-color: transparent;"
                                                 rendered="#{rotasContainer.flag eq 2}">
                                        <p:panel id="status">
                                            <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="30"
                                                 width="30" rendered="#{rotasContainer.flag eq 2}"/>
                                            <p:spacer width="5"/>
                                            <h:outputText value="#{localemsgs.Trajetos}" style="font-size: 14px; font-weight: bold"/>
                                        </p:panel>
                                        <p:panelGrid columns="2">
                                            <p:panel class="panelTabela">
                                                <p:dataTable id="trajetos" value="#{rotasContainer.trajetos}" sortBy="#{listaTrajetos.hora1}"
                                                             style="font-size: 12px" var="listaTrajetos" rowKey="#{listaTrajetos.parada}" 
                                                             styleClass="tabela" resizableColumns="true" scrollable="true" selectionMode="single"
                                                             scrollHeight="150" scrollWidth="100%"
                                                             emptyMessage="#{localemsgs.SemRegistros}" rendered="#{rotasContainer.flag eq 2}"
                                                             selection="#{rotasContainer.trajetoSelecionado}">  
                                                    <p:ajax event="rowDblselect" update="msgs cadastroTrajeto" 
                                                            listener="#{rotasContainer.selecionarTrajeto}"/>
                                                    <p:column headerText="#{localemsgs.Parada}" style="width: 47px; font-size: 12px;
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.parada}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hora1}" 
                                                              style="width: 50px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.hora1}" converter="conversorHora"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.ER}" 
                                                              style="width: 25px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.ER}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.CodCli1}" 
                                                              style="width: 60px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.codCli1}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.NRed}" style="width: 150px; 
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.NRed}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.CodCli2}" 
                                                              style="width: 60px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.codCli2}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.DPar}" 
                                                              style="width: 35px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.DPar}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hora1D}" 
                                                              style="width: 50px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.hora1D}" converter="conversorHora"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Valor}" style="width: 80px; 
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.valor}" converter="conversormoeda"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.tipoServ}" 
                                                              style="width: 100px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.tipoSrv}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Obs}" style="width: 200px;
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.observ}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Pedido}" style="width: 60px;
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.pedido}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Operador}" style="width: 80px; 
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.operador}"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 80px; 
                                                              #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.dt_Alter}" converter="conversorData"/>
                                                    </p:column>
                                                    <p:column headerText="#{localemsgs.Hr_Alter}" 
                                                              style="width: 70px; #{listaTrajetos.flag_Excl eq '*' ? 'color:cyan' : 'color:black'}">
                                                        <h:outputText value="#{listaTrajetos.hr_Alter}"/>
                                                    </p:column> 
                                                </p:dataTable>
                                                <div class="ui-grid ui-grid-responsive">
                                                    <div class="ui-grid-row">
                                                        <div class="ui-grid-col-6">
                                                            <h:outputText id="qtdParadas" value="#{localemsgs.QtdParadas}: #{rotasContainer.trajetos.size()}"
                                                                          style="font-size: 12px"/>
                                                        </div>
                                                        <div class="ui-grid-col-6">
                                                            <p:outputLabel for="checkboxCliente" value="#{localemsgs.ExibirExcluidos}: "
                                                                           style="font-size: 12px"/>
                                                            <p:selectBooleanCheckbox 
                                                                id="checkboxCliente"
                                                                value="#{rotasContainer.exclFlag}" 
                                                                style="font-size: 12px"
                                                                disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                                                <p:ajax update="msgs trajetos qtdParadas" listener="#{rotasContainer.listaTrajetos}" />
                                                            </p:selectBooleanCheckbox>
                                                        </div>
                                                    </div>
                                                </div>
                                            </p:panel>
                                            <p:panel style="width: 30px;">
                                                <p:commandLink title="#{localemsgs.Adicionar}" action="#{rotasContainer.novoTrajeto}"
                                                               update="cadastroTrajeto" disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">    
                                                    <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="30"/>
                                                </p:commandLink>
                                                <p:spacer height="20px"/>
                                                <p:commandLink title="#{localemsgs.Editar}" update="cadastroTrajeto msgs"
                                                               disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}"
                                                               actionListener="#{rotasContainer.abrirTrajeto}">
                                                    <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                                </p:commandLink>
                                                <p:spacer height="20px"/>
                                                <p:commandLink title="#{localemsgs.Excluir}" actionListener="#{rotasContainer.excluirTrajeto}"
                                                               action="#{rotasContainer.listaTrajetos}" update="cadastroTrajeto cadastroRota:trajetos msgs"
                                                               disabled="#{rotasContainer.novaRota.flag_Excl eq '*'}">
                                                    <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ExcluirTrajeto}" icon="ui-icon-alert" />
                                                </p:commandLink>
                                            </p:panel> 
                                        </p:panelGrid>
                                    </p:panelGrid>
                                </p:panel>
                            </p:dialog>                    
                        </h:form>

                        <!-- MODAL DE ESCALA -->                                                                     
                        <h:form id="cadastroEscala"> 
                            <p:dialog styleClass="box-primary"  widgetVar="dlgEscala" minHeight="500" modal="true"  appendTo="@(body)"
                                      id="dlgEscala" responsive="true" dynamic="true" style="background-size: 750px 430px;">
                                <script>
                                            $(document).ready(function() {
                                    //first unbind the original click event
                                    PF('dlgEscala').closeIcon.unbind('click');
                                            //register your own
                                            PF('dlgEscala').closeIcon.click(function(e) {
                                    $("#cadastroEscala\\:botaoFechar").click();
                                            //should be always called
                                            e.preventDefault();
                                    });
                                    }; );
                                </script>
                                <p:commandButton widgetVar="botaoFechar" style="display: none"
                                                 oncomplete="PF('dlgEscala').hide()" id="botaoFechar">
                                    <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                                </p:commandButton>
                                <f:facet name="header">
                                    <h:outputText value="#{localemsgs.CadastrarEscala}" style="color:black" /> 
                                </f:facet>
                                <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastrar">
                                    <p:confirmDialog global="true">
                                        <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                        <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                    </p:confirmDialog>
                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="filial" value="#{localemsgs.Filial}:"  />
                                        <p:selectOneMenu id="filial" value="#{rotasContainer.filial}" converter="omnifaces.SelectItemsConverter" 
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                         filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                            <f:selectItems value="#{login.filiais}" var="filiais" itemValue="#{filiais}" 
                                                           itemLabel="#{filiais.descricao}" noSelectionValue=""/>
                                        </p:selectOneMenu>

                                        <p:outputLabel for="data" value="#{localemsgs.Data}: " />
                                        <p:inputMask id="data" value="#{rotasContainer.escala.data}" mask="99/99/9999"
                                                     required="true" label="#{localemsgs.Data}"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                     style="width: 100%"
                                                     maxlength="8" placeholder="00/00/0000"
                                                     converter="conversorData" disabled="true"/>
                                    </p:panelGrid>

                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-8,ui-grid-col-2" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">

                                        <p:outputLabel for="rota" value="#{localemsgs.Rota}:"  />
                                        <p:selectOneMenu id="rota" value="#{rotasContainer.rotaSelecionada}" converter="omnifaces.SelectItemsConverter" 
                                                         required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Rota}"
                                                         style="width: 100%" label="">
                                            <f:selectItems value="#{rotasContainer.rotasSelecao}" var="rotaSelecao" itemValue="#{rotaSelecao}" 
                                                           itemLabel="#{rotaSelecao.rota}"
                                                           />
                                            <p:ajax event="itemSelect" update="cadastroEscala:cadastrar msgs" listener="#{rotasContainer.selecionarRotaEscala}"/>
                                        </p:selectOneMenu>
                                        <p:inputText id="rotaSeq" value="#{rotasContainer.rotaSelecionada.sequencia}"
                                                     label="#{localemsgs.Rota}" style="width: 100%"
                                                     disabled="true" converter="conversor0">
                                            <p:watermark for="rotaSeq" value="#{localemsgs.Rota}"/>
                                        </p:inputText> 
                                    </p:panelGrid>

                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2, ui-grid-col-2, ui-grid-col-1, ui-grid-col-2"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="hrLargada" value="#{localemsgs.Horario}: "/>
                                        <p:inputMask id="hrLargada" value="#{rotasContainer.escala.hora1}"
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true" placeholder="00:00" 
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Largada}"
                                                     maxlength="4" style="width: 105%" >
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="hrChegada" value="#{localemsgs.a}" style="text-align: center; width: 100%"/>
                                        <p:inputMask id="hrChegada" value="#{rotasContainer.escala.hora4}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true"  placeholder="00:00"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Chegada}"
                                                     maxlength="4" style="width: 105%">
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" 
                                                    listener="#{rotasContainer.calculaHorasTrabalhadasEscala}" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="hrIntIni" value="#{localemsgs.Intervalo}: "/>
                                        <p:inputMask id="hrIntIni" value="#{rotasContainer.escala.hora2}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true" placeholder="00:00" style="width: 105%"
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_Intini}"
                                                     maxlength="4" >
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="hrIntFim" value="#{localemsgs.a}" style="text-align: center; width: 100%"/>
                                        <p:inputMask id="hrIntFim" value="#{rotasContainer.escala.hora3}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     required="true"  placeholder="00:00" 
                                                     requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Hr_IntFim}"
                                                     maxlength="4" style="width: 105%">
                                            <p:ajax process="@this" update="msgs totalhr" partialSubmit="true" 
                                                    listener="#{rotasContainer.calculaHorasTrabalhadasEscala}" event="blur"/>
                                        </p:inputMask>

                                        <p:outputLabel for="totalhr" value="#{localemsgs.Hr_Total}: " escape="false"/>
                                        <p:inputText id="totalhr" value="#{rotasContainer.escala.hsTot}" 
                                                     maxlength="4" style="width: 100%"
                                                     disabled="true">
                                            <f:convertNumber maxFractionDigits="2"/>
                                        </p:inputText>
                                    </p:panelGrid>

                                    <p:panelGrid columns="5" columnClasses="ui-grid-col-2, ui-grid-col-2, ui-grid-col-4, ui-grid-col-2, ui-grid-col-2"
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:outputLabel for="motorista" value="#{localemsgs.Motorista}: "/>
                                        <p:inputText id="matrMotorista" value="#{rotasContainer.motorista.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                        <p:autoComplete id="motorista" value="#{rotasContainer.motorista}" 
                                                        inputStyle="width: 100%"
                                                        completeMethod="#{rotasContainer.buscarPessoas}" 
                                                        var="motoristaSelecao" itemLabel="#{motoristaSelecao.nome}" itemValue="#{motoristaSelecao}"
                                                        scrollHeight="250">
                                            <o:converter converterId="omnifaces.ListConverter" list="#{rotasContainer.listaPessoa}"/>
                                            <p:column>
                                                <h:outputText value=" #{motoristaSelecao.nome}" style="#{motoristaSelecao.funcao eq 'M' or chEquipeSelecao.funcao eq 'T' 
                                                                        ? null : 'color: red'}"/>
                                            </p:column>
                                            <p:ajax event="itemSelect" listener="#{rotasContainer.selecionarMotorista}" 
                                                    update="matrMotorista hrMotorista motorista msgs"/>
                                        </p:autoComplete>
                                        <p:outputLabel for="hrMotorista" value="#{localemsgs.Hora}: "/>
                                        <p:inputMask id="hrMotorista" value="#{rotasContainer.escala.hrMot}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     placeholder="00:00" style="width: 100%"
                                                     maxlength="4" />

                                        <p:outputLabel for="chEquipe" value="#{localemsgs.ChEquipe}: "/>
                                        <p:inputText id="matrChEquipe" value="#{rotasContainer.chEquipe.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                        <p:autoComplete id="chEquipe" value="#{rotasContainer.chEquipe}"
                                                        inputStyle="width: 100%"
                                                        completeMethod="#{rotasContainer.buscarPessoas}" 
                                                        var="chEquipeSelecao" itemLabel="#{chEquipeSelecao.nome}" itemValue="#{chEquipeSelecao}"
                                                        scrollHeight="250">
                                            <p:column>
                                                <h:outputText value=" #{chEquipeSelecao.nome}" style="#{chEquipeSelecao.funcao eq 'C' or chEquipeSelecao.funcao eq 'T' 
                                                                        ? null : 'color: red'}"/>
                                            </p:column>
                                            <o:converter converterId="omnifaces.ListConverter" list="#{rotasContainer.listaPessoa}"/>
                                            <p:ajax event="itemSelect" listener="#{rotasContainer.selecionarChEquipe}" 
                                                    update="matrChEquipe hrChEquipe panelInfoChEquipe chEquipe msgs"/>
                                        </p:autoComplete>
                                        <p:outputLabel for="hrChEquipe" value="#{localemsgs.Hora}: "/>
                                        <p:inputMask id="hrChEquipe" value="#{rotasContainer.escala.hrChe}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     placeholder="00:00" style="width: 100%"
                                                     maxlength="4" />
                                    </p:panelGrid>

                                    <p:panelGrid columns="6" columnClasses="ui-grid-col-2, ui-grid-col-2, ui-grid-col-2, ui-grid-col-2, 
                                                 ui-grid-col-3, ui-grid-col-1" id="panelInfoChEquipe"
                                                 layout="grid" styleClass="ui-panelgrid-blank">                 
                                        <p:outputLabel for="codigoChEquipe" value="#{localemsgs.CodPessoa}: " rendered="#{rotasContainer.infoChEquipe}"/>
                                        <p:inputText id="codigoChEquipe" value="#{rotasContainer.chEquipe.codigo}" converter="conversor0"
                                                     disabled="true" style="width: 100%" rendered="#{rotasContainer.infoChEquipe}"/>

                                        <p:outputLabel for="senhaChEquipe" value="#{localemsgs.SenhaMobile}: " rendered="#{rotasContainer.infoChEquipe}"/>
                                        <p:inputText id="senhaChEquipe" value="#{rotasContainer.chEquipe.PWWeb}" rendered="#{rotasContainer.infoChEquipe}"
                                                     disabled="true" style="width: 100%"/>

                                        <p:outputLabel for="permissaoChEquipe" value="#{localemsgs.PermissaoRotas}: " rendered="#{rotasContainer.infoChEquipe}"/>
                                        <p:inputText id="permissaoChEquipe" value="#{rotasContainer.permissaoRota ? localemsgs.OK :  localemsgs.Nao}" rendered="#{rotasContainer.infoChEquipe}"
                                                     disabled="true" style="width: 100%; #{rotasContainer.permissaoRota ? 'color:green' : 'color:red'}"/>
                                    </p:panelGrid>

                                    <p:panelGrid columns="5" columnClasses="ui-grid-col-2, ui-grid-col-2, ui-grid-col-4, ui-grid-col-2, ui-grid-col-2"
                                                 layout="grid" styleClass="ui-panelgrid-blank">                                
                                        <p:outputLabel for="vigilante1" value="#{localemsgs.Vigilante1}: "/>
                                        <p:inputText id="matrVigilante1" value="#{rotasContainer.vigilante1.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                        <p:autoComplete id="vigilante1" value="#{rotasContainer.vigilante1}"
                                                        inputStyle="width: 100%"
                                                        completeMethod="#{rotasContainer.buscarPessoas}" 
                                                        var="vigilante1Selecao" itemLabel="#{vigilante1Selecao.nome}" itemValue="#{vigilante1Selecao}"
                                                        scrollHeight="250">
                                            <o:converter converterId="omnifaces.ListConverter" list="#{rotasContainer.listaPessoa}"/>
                                            <p:ajax event="itemSelect" listener="#{rotasContainer.selecionarVigilante1}" 
                                                    update="matrVigilante1 hrVigilante1 msgs"/>
                                            <p:watermark for="vigilante1" value="#{localemsgs.Vigilante1}" />
                                        </p:autoComplete>
                                        <p:outputLabel for="hrVigilante1" value="#{localemsgs.Hora}: "/>
                                        <p:inputMask id="hrVigilante1" value="#{rotasContainer.escala.hrVig1}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     placeholder="00:00" style="width: 100%"
                                                     maxlength="4" />

                                        <p:outputLabel for="vigilante2" value="#{localemsgs.Vigilante2}: "/>
                                        <p:inputText id="matrVigilante2" value="#{rotasContainer.vigilante2.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                        <p:autoComplete id="vigilante2" value="#{rotasContainer.vigilante2}"
                                                        inputStyle="width: 100%"
                                                        completeMethod="#{rotasContainer.buscarPessoas}" 
                                                        var="vigilante2Selecao" itemLabel="#{vigilante2Selecao.nome}" itemValue="#{vigilante2Selecao}"
                                                        scrollHeight="250">
                                            <o:converter converterId="omnifaces.ListConverter" list="#{rotasContainer.listaPessoa}"/>
                                            <p:ajax event="itemSelect" listener="#{rotasContainer.selecionarVigilante2}" 
                                                    update="matrVigilante2 hrVigilante2 msgs"/>
                                            <p:watermark for="vigilante2" value="#{localemsgs.Vigilante2}" />
                                        </p:autoComplete>
                                        <p:outputLabel for="hrVigilante2" value="#{localemsgs.Hora}: "/>
                                        <p:inputMask id="hrVigilante2" value="#{rotasContainer.escala.hrVig2}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     placeholder="00:00" style="width: 100%"
                                                     maxlength="4" />

                                        <p:outputLabel for="vigilante3" value="#{localemsgs.Vigilante3}: "/>
                                        <p:inputText id="matrVigilante3" value="#{rotasContainer.vigilante3.matr}" disabled="true" style="width: 100%" converter="conversor0"/>
                                        <p:autoComplete id="vigilante3" value="#{rotasContainer.vigilante3}"
                                                        inputStyle="width: 100%"
                                                        completeMethod="#{rotasContainer.buscarPessoas}" 
                                                        var="vigilante3Selecao" itemLabel="#{vigilante3Selecao.nome}" itemValue="#{vigilante3Selecao}"
                                                        scrollHeight="250">
                                            <o:converter converterId="omnifaces.ListConverter" list="#{rotasContainer.listaPessoa}"/>
                                            <p:ajax event="itemSelect" listener="#{rotasContainer.selecionarVigilante3}" 
                                                    update="matrVigilante3 hrVigilante3 msgs"/>
                                            <p:watermark for="vigilante3" value="#{localemsgs.Vigilante3}" />
                                        </p:autoComplete>
                                        <p:outputLabel for="hrVigilante3" value="#{localemsgs.Hora}: "/>
                                        <p:inputMask id="hrVigilante3" value="#{rotasContainer.escala.hrVig3}" 
                                                     mask="#{mascaras.mascaraHora}" converter="conversorHora"
                                                     placeholder="00:00" style="width: 100%"
                                                     maxlength="4" />
                                    </p:panelGrid>

                                    <p:panelGrid columns="4" columnClasses="ui-grid-col-2, ui-grid-col-2, ui-grid-col-4, ui-grid-col-4"
                                                 layout="grid" styleClass="ui-panelgrid-blank">  
                                        <p:outputLabel for="veiculo" value="#{localemsgs.Veiculo}: "/>
                                        <p:inputText id="numeroVeiculo" value="#{rotasContainer.veiculo.numero}" disabled="true" 
                                                     style="width: 100%" converter="conversor0"/>

                                        <p:selectOneMenu id="veiculo" value="#{rotasContainer.veiculo}" converter="omnifaces.SelectItemsConverter" 
                                                         filter="true" filterMatchMode="contains" style="width: 100%" label="">
                                            <f:selectItems value="#{rotasContainer.veiculos}" var="veiculoSelecao" itemValue="#{veiculoSelecao}" 
                                                           itemLabel="#{veiculoSelecao}" noSelectionValue=""/>
                                            <p:ajax event="itemSelect" update="numeroVeiculo msgVeiculo msgs"
                                                    process="@this" partialSubmit="true"
                                                    listener="#{rotasContainer.selecionarVeiculo}"/>
                                        </p:selectOneMenu>
                                        <h:outputText id="msgVeiculo" value="#{rotasContainer.msgVeiculo}"/>
                                    </p:panelGrid>

                                    <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-1, ui-grid-col-9" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">

                                        <p:commandButton actionListener="#{rotasContainer.cadastrarEscala(true)}" update="main:tabela msgs" styleClass="botao"
                                                         value="#{localemsgs.Cadastrar}" rendered="#{rotasContainer.flagEscala eq 1}">
                                        </p:commandButton> 

                                        <p:commandButton rendered="#{rotasContainer.flagEscala eq 2}" id="edit" actionListener="#{rotasContainer.editarEscala(true)}"
                                                         update=":msgs main:tabela cadastrar"
                                                         title="#{localemsgs.Editar}" styleClass="botao">
                                        </p:commandButton>

                                        <p:selectBooleanCheckbox value="#{rotasContainer.infoChEquipe}" id="senhaMobile"
                                                                 style="width: 100%; text-align: right">
                                            <p:ajax update="panelInfoChEquipe" partialSubmit="true" process="@this"
                                                    event="change" oncomplete="PF('dlgEscala').initPosition()"/>
                                        </p:selectBooleanCheckbox>
                                        <p:outputLabel for="senhaMobile" value="#{localemsgs.senhaMobile}"/>
                                    </p:panelGrid>
                                </p:panel>
                            </p:dialog>

                            <p:dialog widgetVar="dlgFuncionarioFolga" positionType="absolute" responsive="true"
                                      draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                      showEffect="drop" hideEffect="drop" closeOnEscape="false"
                                      style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                                <f:facet name="header">
                                    <img src="../assets/imaged/icone_clientes.png" height="40" width="40"/> 
                                    <p:spacer width="5px"/>
                                    <h:outputText value="#{localemsgs.FuncionariosFolga}" style="color:black" /> 
                                </f:facet>   
                                <p:panel id="panelFuncionarioFolga" style="background-color: transparent" styleClass="cadastrar2">
                                    <h:outputText value="#{rotasContainer.folgas.size() > 1 ? localemsgs.AceitarFuncionariosFolga :  localemsgs.AceitarFuncionarioFolga}"/>
                                    <p:dataTable id="tabelaFuncionarioFolga" value="#{rotasContainer.folgas}"
                                                 style="font-size: 12px" var="folgas"
                                                 styleClass="tabela" scrollWidth="100%"
                                                 resizableColumns="true" scrollable="true" scrollHeight="85" > 
                                        <p:column headerText="#{localemsgs.Matr}" style="width: 47px; font-size: 12px;">
                                            <h:outputText value="#{folgas.matr}" converter="conversor0"/>
                                        </p:column>
                                        <p:column headerText="#{localemsgs.Funcion}">
                                            <h:outputText value="#{folgas.nome_Guer}"/>
                                        </p:column>
                                    </p:dataTable>

                                    <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                 layout="grid" styleClass="ui-panelgrid-blank">
                                        <p:commandLink title="#{localemsgs.Avancar}" rendered="#{rotasContainer.flagEscala eq 1}"
                                                       update="main:tabela :msgs" actionListener="#{rotasContainer.cadastrarEscala(true)}">
                                            <p:graphicImage url="../assets/images/icone_redondo_editar.png" height="40"/>
                                        </p:commandLink>

                                        <p:commandLink title="#{localemsgs.Avancar}" rendered="#{rotasContainer.flagEscala eq 2}"
                                                       update="main:tabela :msgs" actionListener="#{rotasContainer.editarEscala(true)}">
                                            <p:graphicImage url="../assets/images/icone_redondo_editar.png" height="40"/>
                                        </p:commandLink>

                                        <p:commandLink title="#{localemsgs.Cancelar}" oncomplete="PF('dlgFuncionarioFolga').hide()">
                                            <p:graphicImage url="../assets/images/icone_redondo_editar.png" height="40"/>
                                        </p:commandLink>
                                    </p:panelGrid>

                                </p:panel>
                            </p:dialog>
                        </h:form>
                    </div>
                </ui:define>
            </ui:composition>
        </h:body>
    </f:view>
</html>

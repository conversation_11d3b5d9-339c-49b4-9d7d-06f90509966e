<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}" contentType="text/html" >
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png"/>
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <link type="text/css" href="../assets/css/portalrh.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body id="h">
            <p:growl id="msgs"/>
            <div id="header"></div>
            <h:form id="main">
                <div class="bottomrow">
                    <div class="primeiroacesso">
                        <h2>
                            <h:outputText value="#{localemsgs.Acesso1}"/>
                        </h2>
                        
                        &nbsp;
                        
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row" style="padding-bottom: 5px; color:#022a48">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:outputLabel for="campo1" rendered="#{login.rand eq 1}" value="#{localemsgs.RGn}: " />
                                    <p:outputLabel for="campo1" rendered="#{login.rand eq 2}" value="#{localemsgs.CidadeRes}: " />
                                    <p:outputLabel for="campo1" rendered="#{login.rand eq 3}" value="#{localemsgs.CidadeRes}: " />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:inputText id="campo1" value="#{login.validacao1}"/>
                                </div>
                            </div>

                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start;color:#022a48">
                                    <p:outputLabel for="campo2" rendered="#{login.rand eq 1}" value="#{localemsgs.CPFn}: " />
                                    <p:outputLabel for="campo2" rendered="#{login.rand eq 2}" value="#{localemsgs.CPFn}: " />
                                    <p:outputLabel for="campo2" rendered="#{login.rand eq 3}" value="#{localemsgs.Dt_Nascn}: " />
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:inputText id="campo2" value="#{login.validacao2}"/>
                                </div>
                            </div>
                            <div class="ui-grid-row">
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:commandLink id="voltar" action="#{login.voltar}" style="margin: auto" update="msgs novasenha"
                                                   title="#{localemsgs.Concluido}">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                                <div class="ui-grid-col-6" style="align-self: flex-start">
                                    <p:commandLink id="cadastro" action="#{login.Validar}" style="margin: auto" update="msgs novasenha"
                                                   title="#{localemsgs.Concluido}">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </h:form>
            
            <h:form id="novasenha"> 
                <p:dialog header="#{localemsgs.NovaSenha}" widgetVar="dlgDigiteSenha" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" 
                          width="440" showEffect="drop" hideEffect="drop">
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="nova" value="#{localemsgs.Senha}:"/>
                        <p:password id="nova" match="confirmar" autocomplete="off"
                                    validatorMessage="Senhas não conferem" value="#{login.validacao3}"/>

                        <p:outputLabel for="confirmar" value="#{localemsgs.Confirmacao}:"/>
                        <p:password id="confirmar" autocomplete="off" value="#{login.validacao3}"/>
                    </p:panelGrid>
                    &nbsp;
                    <div class="pnl">
                        <p:commandLink id="pesquisa" action="#{login.Validar2}"
                                       update="msgs"
                                       title="#{localemsgs.NovaSenha}">
                            <p:graphicImage url="../assets/img/icone_adicionar.png"  width="40" height="40" />
                        </p:commandLink>
                    </div>
                </p:dialog>
            </h:form>
            
            <ui:insert name="loading" >
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>
        </h:body>
    </f:view>
</html>


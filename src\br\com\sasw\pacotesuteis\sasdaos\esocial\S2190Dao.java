/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2190;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2190Dao {

    public List<S2190> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            List<S2190> retorno = new ArrayList<>();
            String sql = "Select Funcion.CPF infoRegPrelim_cpfTrab, \n"
                    + "                     substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) infoRegPrelim_dtNascto,\n"
                    + "                     substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoRegPrelim_dtAdm,"
                    + "                     Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, \n"
                    + "                     (select max(sucesso) from  ( \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n"
                    + "                             From XmleSocial z \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = ?\n"
                    + "                                 and z.Compet = ?\n"
                    + "                                 and z.Ambiente = ?\n"
                    + "                                 and (z.Xml_Retorno like '%aguardando%' \n"
                    + "                                         or z.Xml_Retorno = ''\n"
                    + "                                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))\n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = ?\n"
                    + "                                 and z.Compet = ?\n"
                    + "                                 and z.Ambiente = ?\n"
                    + "                                 and (z.Xml_Retorno like '%<ocorrencia>%' \n"
                    + "                                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = ?\n"
                    + "                                 and z.Compet = ?\n"
                    + "                                 and z.Ambiente = ?\n"
                    + "                                 and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso \n"
                    + "                     from FPMensal \n"
                    + "                     Left Join Funcion  on Funcion.Matr = FPMensal.Matr \n"
                    + "                     Left Join Filiais on Filiais.codFil = FPMensal.CodFil\n"
                    + "                     where TipoFP = 'MEN' \n"
                    + "                     and Funcion.codfil = ?\n"
                    + "                     and Funcion.situacao <> 'D' \n"
                    + "                     and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) \n"
                    + "                                                 from FPPeriodos where DtInicio = ?) \n"
                    + "                     ORDER BY sucesso asc, Funcion.Matr asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet + "-01");
            consulta.select();
            S2190 s2190;
            while (consulta.Proximo()) {
                s2190 = new S2190();
                s2190.setIdeEvento_procEmi("1");
                s2190.setIdeEvento_verProc("Satellite eSocial");
                s2190.setSucesso(consulta.getInt("sucesso"));
                s2190.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2190.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2190.setInfoRegPrelim_cpfTrab(consulta.getString("infoRegPrelim_cpfTrab"));
                s2190.setInfoRegPrelim_dtNascto(consulta.getString("infoRegPrelim_dtNascto"));
                s2190.setInfoRegPrelim_dtAdm(consulta.getString("infoRegPrelim_dtAdm"));
                retorno.add(s2190);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2190Dao.get - " + e.getMessage() + "\r\n"
                    + "Select Funcion.CPF infoRegPrelim_cpfTrab, \n"
                    + "                     substring(replace(convert(varchar,Funcion.Dt_Nasc,111),'/','-'),0,11) nascimento_dtNascto,\n"
                    + "                     substring(replace(convert(varchar,Funcion.Dt_Admis,111),'/','-'),0,11) infoCeletista_dtAdm,"
                    + "                     Filiais.CNPJ ideEmpregador_nrInsc,  Filiais.TipoPessoa ideEmpregador_tpInsc, \n"
                    + "                     (select max(sucesso) from  ( \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso \n"
                    + "                             From XmleSocial z \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = " + codFil
                    + "                                 and z.Compet = " + compet
                    + "                                 and z.Ambiente = " + ambiente
                    + "                                 and (z.Xml_Retorno like '%aguardando%' \n"
                    + "                                         or z.Xml_Retorno = ''\n"
                    + "                                         or z.xml_retorno like '%Solicitação de consulta incorreta - Erro Certificado.%'))\n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = " + codFil
                    + "                                 and z.Compet = " + compet
                    + "                                 and z.Ambiente = " + ambiente
                    + "                                 and (z.Xml_Retorno like '%<ocorrencia>%' \n"
                    + "                                         or z.Xml_Retorno like '%A regra de precedência na transmissão de eventos não foi seguida%') ) \n"
                    + "                     union \n"
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso \n"
                    + "                             From XmleSocial z  \n"
                    + "                             where z.Identificador = Funcion.CPF \n"
                    + "                                 and z.evento = 'S-2190' \n"
                    + "                                 and z.CodFil = " + codFil
                    + "                                 and z.Compet = " + compet
                    + "                                 and z.Ambiente = " + ambiente
                    + "                                 and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso \n"
                    + "                     from FPMensal \n"
                    + "                     Left Join Funcion  on Funcion.Matr = FPMensal.Matr \n"
                    + "                     Left Join Filiais on Filiais.codFil = FPMensal.CodFil\n"
                    + "                     where TipoFP = 'MEN' \n"
                    + "                     and Funcion.codfil = " + codFil
                    + "                     and Funcion.situacao <> 'D' \n"
                    + "                     and CodMovFp = (Select substring(replace(convert(varchar, Max(DtFinal), 111), '/',''),3,4) \n"
                    + "                                                 from FPPeriodos where DtInicio = " + compet + "-01) \n"
                    + "                     ORDER BY sucesso asc, Funcion.Matr asc ");
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class RastrearEW {

    private String Sequencia;
    private String CodPessoa;
    private String CodContato;
    private String Data;
    private String Hora;
    private String Latitude;
    private String Longitude;
    private String Acuracia;
    private String Distancia;
    private String DtTransf;
    private String HrTransf;
    private String Origem;
    
    private String Nome;

    public String getSequencia() {
        return Sequencia;
    }

    public void setSequencia(String Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getCodPessoa() {
        return CodPessoa;
    }

    public void setCodPessoa(String CodPessoa) {
        this.CodPessoa = CodPessoa;
    }

    public String getCodContato() {
        return CodContato;
    }

    public void setCodContato(String CodContato) {
        this.CodContato = CodContato;
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }

    public String getAcuracia() {
        return Acuracia;
    }

    public void setAcuracia(String Acuracia) {
        this.Acuracia = Acuracia;
    }

    public String getDistancia() {
        return Distancia;
    }

    public void setDistancia(String Distancia) {
        this.Distancia = Distancia;
    }

    public String getDtTransf() {
        return DtTransf;
    }

    public void setDtTransf(String DtTransf) {
        this.DtTransf = DtTransf;
    }

    public String getHrTransf() {
        return HrTransf;
    }

    public void setHrTransf(String HrTransf) {
        this.HrTransf = HrTransf;
    }

    public String getOrigem() {
        return Origem;
    }

    public void setOrigem(String Origem) {
        this.Origem = Origem;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String Nome) {
        this.Nome = Nome;
    }
    
    
}

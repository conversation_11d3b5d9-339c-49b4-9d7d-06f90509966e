/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

import java.math.BigDecimal;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class RHEscala {

    private BigDecimal codFil;
    private String codigo;
    private String descricao;
    private String tipocomp;
    private BigDecimal chComp;
    private BigDecimal hrIntraJor;
    private BigDecimal vtDias;
    private BigDecimal tolerancia;
    private BigDecimal diasTrbDiu;
    private BigDecimal diasTrbNot;
    private BigDecimal diasFolga;
    private BigDecimal hsAbonoFer;
    private BigDecimal hsAbonoFalta;
    private String folgaSab;
    private String folgaDom;
    private String folgaFer;
    private String folgaSdf;
    private BigDecimal hrDUDiu;
    private BigDecimal hrDUDiuNot;
    private BigDecimal hrDUNotDiu;
    private BigDecimal hrDUNot;
    private BigDecimal hrSabDiu;
    private BigDecimal hrSabDiuNot;
    private BigDecimal hrSabNotDiu;
    private BigDecimal hrSabNot;
    private BigDecimal hrDomDiu;
    private BigDecimal hrDomDiuNot;
    private BigDecimal hrDomNotDiu;
    private BigDecimal hrDomNot;
    private String diasDeTrabalho;
    private String diuTurno;
    private String operador;
    private String dt_alter;
    private String hr_alter;

    public RHEscala() {
        this.codFil = new BigDecimal("0");
        this.codigo = "";
        this.descricao = "";
        this.tipocomp = "";
        this.chComp = new BigDecimal("0");
        this.hrIntraJor = new BigDecimal("0");
        this.vtDias = new BigDecimal("0");
        this.tolerancia = new BigDecimal("0");
        this.diasTrbDiu = new BigDecimal("0");
        this.diasTrbNot = new BigDecimal("0");
        this.diasFolga = new BigDecimal("0");
        this.hsAbonoFer = new BigDecimal("0");
        this.hsAbonoFalta = new BigDecimal("0");
        this.folgaSab = "";
        this.folgaDom = "";
        this.folgaFer = "";
        this.folgaSdf = "";
        this.hrDUDiu = new BigDecimal("0");
        this.hrDUDiuNot = new BigDecimal("0");
        this.hrDUNotDiu = new BigDecimal("0");
        this.hrDUNot = new BigDecimal("0");
        this.hrSabDiu = new BigDecimal("0");
        this.hrSabDiuNot = new BigDecimal("0");
        this.hrSabNotDiu = new BigDecimal("0");
        this.hrSabNot = new BigDecimal("0");
        this.hrDomDiu = new BigDecimal("0");
        this.hrDomDiuNot = new BigDecimal("0");
        this.hrDomNotDiu = new BigDecimal("0");
        this.hrDomNot = new BigDecimal("0");
        this.diasDeTrabalho = "";
        this.diuTurno = "";
        this.operador = "";
        this.dt_alter = "";
        this.hr_alter = "";
    }

    public BigDecimal getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        try {
            this.codFil = new BigDecimal(codFil);
        } catch (Exception e) {
            this.codFil = new BigDecimal("0");
        }
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipocomp() {
        return tipocomp;
    }

    public void setTipocomp(String tipocomp) {
        this.tipocomp = tipocomp;
    }

    public BigDecimal getChComp() {
        return chComp;
    }

    public void setChComp(String chComp) {
        try {
            this.chComp = new BigDecimal(chComp);
        } catch (Exception e) {
            this.chComp = new BigDecimal("0");
        }
    }

    public BigDecimal getHrIntraJor() {
        return hrIntraJor;
    }

    public void setHrIntraJor(String hrIntraJor) {
        try {
            this.hrIntraJor = new BigDecimal(hrIntraJor);
        } catch (Exception e) {
            this.hrIntraJor = new BigDecimal("0");
        }
    }

    public BigDecimal getVtDias() {
        return vtDias;
    }

    public void setVtDias(String vtDias) {
        try {
            this.vtDias = new BigDecimal(vtDias);
        } catch (Exception e) {
            this.vtDias = new BigDecimal("0");
        }
    }

    public BigDecimal getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(String tolerancia) {
        try {
            this.tolerancia = new BigDecimal(tolerancia);
        } catch (Exception e) {
            this.tolerancia = new BigDecimal("0");
        }
    }

    public BigDecimal getDiasTrbDiu() {
        return diasTrbDiu;
    }

    public void setDiasTrbDiu(String diasTrbDiu) {
        try {
            this.diasTrbDiu = new BigDecimal(diasTrbDiu);
        } catch (Exception e) {
            this.diasTrbDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getDiasTrbNot() {
        return diasTrbNot;
    }

    public void setDiasTrbNot(String diasTrbNot) {
        try {
            this.diasTrbNot = new BigDecimal(diasTrbNot);
        } catch (Exception e) {
            this.diasTrbNot = new BigDecimal("0");
        }
    }

    public BigDecimal getDiasFolga() {
        return diasFolga;
    }

    public void setDiasFolga(String diasFolga) {
        try {
            this.diasFolga = new BigDecimal(diasFolga);
        } catch (Exception e) {
            this.diasFolga = new BigDecimal("0");
        }
    }

    public BigDecimal getHsAbonoFer() {
        return hsAbonoFer;
    }

    public void setHsAbonoFer(String hsAbonoFer) {
        try {
            this.hsAbonoFer = new BigDecimal(hsAbonoFer);
        } catch (Exception e) {
            this.hsAbonoFer = new BigDecimal("0");
        }
    }

    public BigDecimal getHsAbonoFalta() {
        return hsAbonoFalta;
    }

    public void setHsAbonoFalta(String hsAbonoFalta) {
        try {
            this.hsAbonoFalta = new BigDecimal(hsAbonoFalta);
        } catch (Exception e) {
            this.hsAbonoFalta = new BigDecimal("0");
        }
    }

    public String getFolgaSab() {
        return folgaSab;
    }

    public void setFolgaSab(String folgaSab) {
        this.folgaSab = folgaSab;
    }

    public String getFolgaDom() {
        return folgaDom;
    }

    public void setFolgaDom(String folgaDom) {
        this.folgaDom = folgaDom;
    }

    public String getFolgaFer() {
        return folgaFer;
    }

    public void setFolgaFer(String folgaFer) {
        this.folgaFer = folgaFer;
    }

    public String getFolgaSdf() {
        return folgaSdf;
    }

    public void setFolgaSdf(String folgaSdf) {
        this.folgaSdf = folgaSdf;
    }

    public BigDecimal getHrDUDiu() {
        return hrDUDiu;
    }

    public void setHrDUDiu(String hrDUDiu) {
        try {
            this.hrDUDiu = new BigDecimal(hrDUDiu);
        } catch (Exception e) {
            this.hrDUDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDUDiuNot() {
        return hrDUDiuNot;
    }

    public void setHrDUDiuNot(String hrDUDiuNot) {
        try {
            this.hrDUDiuNot = new BigDecimal(hrDUDiuNot);
        } catch (Exception e) {
            this.hrDUDiuNot = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDUNotDiu() {
        return hrDUNotDiu;
    }

    public void setHrDUNotDiu(String hrDUNotDiu) {
        try {
            this.hrDUNotDiu = new BigDecimal(hrDUNotDiu);
        } catch (Exception e) {
            this.hrDUNotDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDUNot() {
        return hrDUNot;
    }

    public void setHrDUNot(String hrDUNot) {
        try {
            this.hrDUNot = new BigDecimal(hrDUNot);
        } catch (Exception e) {
            this.hrDUNot = new BigDecimal("0");
        }
    }

    public BigDecimal getHrSabDiu() {
        return hrSabDiu;
    }

    public void setHrSabDiu(String hrSabDiu) {
        try {
            this.hrSabDiu = new BigDecimal(hrSabDiu);
        } catch (Exception e) {
            this.hrSabDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getHrSabDiuNot() {
        return hrSabDiuNot;
    }

    public void setHrSabDiuNot(String hrSabDiuNot) {
        try {
            this.hrSabDiuNot = new BigDecimal(hrSabDiuNot);
        } catch (Exception e) {
            this.hrSabDiuNot = new BigDecimal("0");
        }
    }

    public BigDecimal getHrSabNotDiu() {
        return hrSabNotDiu;
    }

    public void setHrSabNotDiu(String hrSabNotDiu) {
        try {
            this.hrSabNotDiu = new BigDecimal(hrSabNotDiu);
        } catch (Exception e) {
            this.hrSabNotDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getHrSabNot() {
        return hrSabNot;
    }

    public void setHrSabNot(String hrSabNot) {
        try {
            this.hrSabNot = new BigDecimal(hrSabNot);
        } catch (Exception e) {
            this.hrSabNot = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDomDiu() {
        return hrDomDiu;
    }

    public void setHrDomDiu(String hrDomDiu) {
        try {
            this.hrDomDiu = new BigDecimal(hrDomDiu);
        } catch (Exception e) {
            this.hrDomDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDomDiuNot() {
        return hrDomDiuNot;
    }

    public void setHrDomDiuNot(String hrDomDiuNot) {
        try {
            this.hrDomDiuNot = new BigDecimal(hrDomDiuNot);
        } catch (Exception e) {
            this.hrDomDiuNot = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDomNotDiu() {
        return hrDomNotDiu;
    }

    public void setHrDomNotDiu(String hrDomNotDiu) {
        try {
            this.hrDomNotDiu = new BigDecimal(hrDomNotDiu);
        } catch (Exception e) {
            this.hrDomNotDiu = new BigDecimal("0");
        }
    }

    public BigDecimal getHrDomNot() {
        return hrDomNot;
    }

    public void setHrDomNot(String hrDomNot) {
        try {
            this.hrDomNot = new BigDecimal(hrDomNot);
        } catch (Exception e) {
            this.hrDomNot = new BigDecimal("0");
        }
    }

    public String getDiasDeTrabalho() {
        return diasDeTrabalho;
    }

    public void setDiasDeTrabalho(String diasDeTrabalho) {
        this.diasDeTrabalho = diasDeTrabalho;
    }

    public String getDiuTurno() {
        return diuTurno;
    }

    public void setDiuTurno(String diuTurno) {
        this.diuTurno = diuTurno;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getDt_alter() {
        return dt_alter;
    }

    public void setDt_alter(String dt_alter) {
        this.dt_alter = dt_alter;
    }

    public String getHr_alter() {
        return hr_alter;
    }

    public void setHr_alter(String hr_alter) {
        this.hr_alter = hr_alter;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 59 * hash + Objects.hashCode(this.codigo);
        hash = 59 * hash + Objects.hashCode(this.descricao);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RHEscala other = (RHEscala) obj;
        if (!Objects.equals(this.codigo, other.codigo)) {
            return false;
        }
        return true;
    }
}

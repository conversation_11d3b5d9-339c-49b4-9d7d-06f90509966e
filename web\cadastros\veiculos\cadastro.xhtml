<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{text.SPMMoneta}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
        </h:head>   

        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:define name="seletorData">
                <h:outputText value="#{text.Data}: "/>
                <h:outputText id="dataDia" value="#{login.dataTela}" converter="conversorDia"/>
            </ui:define>

            <div id="body">
                <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                    <h:form id="main">
                        <p:panel id="cadastrar" styleClass="painelCadastro">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="filial" value="#{text.Filial}:"/>
                                <p:selectOneMenu id="filial" value="#{veiculos.filial}" converter="omnifaces.SelectItemsConverter"
                                                 filter="true" filterMatchMode="contains" 
                                                 style="width: 100%" disabled="#{veiculos.flag eq 2}">
                                    <f:selectItems value="#{login.filiais}" var="filial" itemValue="#{filial}"
                                                   itemLabel="#{filial.descricao}" noSelectionValue=""/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="numero" value="#{text.Numero}:" />
                                <p:inputText value="#{veiculos.veiculo.numero}"
                                             id="numero" style="width: 100%">
                                    <p:watermark for="numero" value="#{text.Numero}"/>
                                </p:inputText>

                                <p:outputLabel for="placa" value="#{text.Placa}:" />
                                <p:inputText value="#{veiculos.veiculo.placa}" maxlength="7"
                                             id="placa" style="width: 100%">
                                    <p:watermark for="placa" value="#{text.Placa}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="situacao" value="#{text.Tipo}:"/>
                                <p:selectOneMenu value="#{veiculos.veiculo.tipo}" style="width: 100%" required="true" id="situacao">
                                    <f:selectItem itemLabel="#{text.Selecione}"/>
                                    <f:selectItem itemLabel="#{text.CarroForte}" itemValue="F"/>
                                    <f:selectItem itemLabel="#{text.CarroLeve}" itemValue="L"/>
                                    <f:selectItem itemLabel="#{text.Moto}" itemValue="M"/>
                                    <f:selectItem itemLabel="#{text.Pesado}" itemValue="P"/>
                                    <f:selectItem itemLabel="#{text.Blindado}" itemValue="B"/>
                                    <f:selectItem itemLabel="#{text.Particular}" itemValue="R"/>
                                </p:selectOneMenu>
                            </p:panelGrid>

                            <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank" id="panelModelo">    
                                <p:outputLabel for="modelo" value="#{text.Modelo}:" rendered="#{!veiculos.cadastroNovaModelo}"/>
                                <p:autoComplete id="modelo" value="#{veiculos.modelo}" completeMethod="#{veiculos.buscarModelos}"
                                                required="true" label="#{text.Modelo}" rendered="#{!veiculos.cadastroNovaModelo}"
                                                requiredMessage="#{text.Obrigatorio}: #{text.Modelo}"  scrollHeight="200"
                                                inputStyle="width: 100%" placeholder="#{text.Modelo}" forceSelection="true"
                                                var="modelo" itemLabel="#{modelo.descricao}" itemValue="#{modelo}">
                                    <p:column>
                                        <i class="fas fa-plus" style="#{modelo.codigo eq '-1' ? 'display: inline; font-size: 10px;' : 'display: none'}"></i>
                                        <h:outputText value=" #{modelo.descricao}" style="#{modelo.codigo eq '-1' ? 'font-weight: bold' : ''}"/>
                                    </p:column>
                                    <o:converter converterId="omnifaces.ListIndexConverter" list="#{veiculos.buscaModelos}" />
                                    <p:ajax event="itemSelect" listener="#{veiculos.selecionarModelo}" update="msgs main:panelModelo"/>
                                </p:autoComplete>

                                <p:outputLabel for="novoModelo" value="#{text.Modelo}:" rendered="#{veiculos.cadastroNovaModelo}"/>
                                <p:inputText value="#{veiculos.modelo.descricao}" rendered="#{veiculos.cadastroNovaModelo}" id="novoModelo" style="width: 100%">
                                    <p:watermark for="novoModelo" value="#{text.Modelo}"/>
                                </p:inputText>
                            </p:panelGrid>

                            <p:commandButton action="#{veiculos.cadastrar}" update="msgs" styleClass="botao"
                                             value="#{text.Cadastrar}" rendered="#{veiculos.flag eq 1}">                           
                            </p:commandButton>                          

                            <p:commandButton action="#{veiculos.editar}" update="msgs" styleClass="botao"
                                             value="#{text.Editar}" rendered="#{veiculos.flag eq 2}">                           
                            </p:commandButton> 
                        </p:panel>
                    </h:form>
                </div>
            </div>
        </h:body>
    </f:view>
</html>

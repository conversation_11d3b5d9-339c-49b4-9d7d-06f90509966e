<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
        </h:head>
        <h:body>

            <f:metadata>
                <f:viewAction action="#{pedidosContainer.Persistencia(login.pp)}"/>
            </f:metadata>

            <p:growl id="msgs" showDetail="true"/>

            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>

                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>

                <ui:define name="infoFilial">
                    <div class="ui-grid-row">
                        <h:outputText value="#{pedidosContainer.filiais.descricao}" class="negrito"/>
                    </div>
                    <div class="ui-grid-row">
                        #{pedidosContainer.filiais.endereco}
                    </div>
                    <div class="ui-grid-row">
                        #{pedidosContainer.filiais.bairro}<h:outputText value=", " rendered="#{pedidosContainer.filiais.bairro ne null and container.filiais.bairro ne ''}"/>#{pedidosContainer.filiais.cidade}/#{pedidosContainer.filiais.UF}
                    </div>
                </ui:define>

                <ui:define name="seletorData">
                    <h:outputText value="#{localemsgs.Data}: "/>
                    <h:outputText id="dataDia" value="#{pedidosContainer.dataTela}" converter="conversorDia"/>
                    <p:commandLink action="#{pedidosContainer.pedidoAnterior}" update="main:tabela header main:painelPesquisa">
                        <p:graphicImage url="../assets/img/botao_anterior.png" style="height: 40px"/>  
                    </p:commandLink>

                    <p:calendar id="calendario" showOn="button" navigator="true" styleClass="calendario" converter="conversorData"
                                pattern="#{mascaras.padraoData}" value="#{pedidosContainer.dataTela}"
                                locale="#{localeController.getCurrentLocale()}">
                        <p:ajax event="dateSelect" listener="#{pedidosContainer.selecionarData}" update="main header main:painelPesquisa" />
                    </p:calendar>

                    <p:commandLink action="#{pedidosContainer.pedidoPosterior}" update="main:tabela header main:painelPesquisa">
                        <p:graphicImage url="../assets/img/botao_proximo.png" style="height: 40px"/>  
                    </p:commandLink>
                </ui:define>

                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="main" > 
                            <p:confirmDialog global="true">
                                <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                                <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                            </p:confirmDialog>

                            <p:panel  id="painelPesquisa">
                                <p:accordionPanel styleClass="painelCadastro" activeIndex="1">
                                    <p:tab title="#{localemsgs.Pesquisar}">
                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3 pesquisa,ui-grid-col-3 pesquisa,
                                                     ui-grid-col-3 pesquisa,ui-grid-col-3 pesquisa" 
                                                     layout="grid">
                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaTipo" value="#{localemsgs.EntregaRecolhimento} "/>   
                                                <p:selectOneMenu id="pesquisaTipo" value="#{pedidosContainer.pesquisaTipo}">
                                                    <f:selectItem itemLabel="[ #{localemsgs.Todos} ]" itemValue="T" />
                                                    <f:selectItem itemLabel="#{localemsgs.Entrega}" itemValue="E" />
                                                    <f:selectItem itemLabel="#{localemsgs.Recolhimento}" itemValue="R" />
                                                </p:selectOneMenu>
                                            </p:panelGrid> 

                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaCliente" value="#{localemsgs.Cliente}/#{localemsgs.Destino} "/>
                                                <p:inputText id="pesquisaCliente" value="#{pedidosContainer.pesquisaCliente}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaCliente" value="#{localemsgs.Cliente}"/>
                                                </p:inputText>  
                                            </p:panelGrid>  

                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaNumero" value="#{localemsgs.numero} #{localemsgs.Pedido} "/>
                                                <p:inputText id="pesquisaNumero" value="#{pedidosContainer.pesquisaNumero}"
                                                             style="width: 100%">
                                                    <p:watermark for="pesquisaNumero" value="#{localemsgs.Numero}"/>
                                                </p:inputText>  
                                            </p:panelGrid> 

                                            <p:panelGrid columns="1" layout="grid" columnClasses="pesquisa">
                                                <p:outputLabel for="pesquisaData" value="#{localemsgs.SomenteDataSelecionada}"/>

                                                <p:selectOneMenu id="pesquisaData" value="#{pedidosContainer.pesquisaSomenteData}">
                                                    <f:selectItem itemLabel="#{localemsgs.Nao}" itemValue="N" />
                                                    <f:selectItem itemLabel="#{localemsgs.Sim}" itemValue="S" />
                                                </p:selectOneMenu>
                                            </p:panelGrid>  
                                        </p:panelGrid>
                                        <p:commandButton action="#{pedidosContainer.pesquisar}" 
                                                         update="msgs main:tabela main:painelPesquisa" 
                                                         styleClass="botao" value="#{localemsgs.Pesquisar}"/>
                                        <p:commandButton action="#{pedidosContainer.limparPesquisaBt}" 
                                                         update="msgs main:tabela main:painelPesquisa" 
                                                         styleClass="botao" value="#{localemsgs.LimparFiltros}"
                                                         style="white-space: nowrap !important"/>
                                    </p:tab>
                                </p:accordionPanel>
                            </p:panel>

                            <p:panel styleClass="painelCadastro">
                                <p:dataGrid id="tabela" value="#{pedidosContainer.allPedidos}" paginator="true" rows="50" lazy="true"
                                            rowsPerPageTemplate="5,10,15,20,25,50"
                                            currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pedidos}"
                                            paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                            var="listaPedidos" emptyMessage="#{localemsgs.SemRegistros}"
                                            columns="1">
                                    <label class="lblTipoPedido" ref="#{listaPedidos.tipoNoPedido.toLowerCase()}"><h:outputText value="#{listaPedidos.tipoNoPedido.toUpperCase()}" converter="tradutor" /></label>
                                    <i class='fa fa-file-alt'></i>
                                    <p:panel header="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#{localemsgs.Pedido} #{listaPedidos.numero.toBigInteger().toString()}"
                                             style="color:#000 !important;">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Data}: " />
                                                <h:outputText value="#{listaPedidos.data}" class="negrito-normal" converter="conversorData"/>
                                            </p:column>

                                            <p:column>
                                                <h:outputText value="#{localemsgs.OS}: "/>
                                                <h:outputText value="#{listaPedidos.OS}" class="negrito-normal">
                                                    <f:convertNumber pattern="0000"/>
                                                </h:outputText>
                                            </p:column>

                                            <p:column>
                                                <h:outputText value="#{localemsgs.Origem}: "/>
                                                <h:outputText value="#{listaPedidos.codCli1} - #{listaPedidos.NRed1}" class="negrito-normal"/>
                                            </p:column>

                                            <p:column>
                                                <h:outputText value="#{localemsgs.Horario}: "/>
                                                <h:outputText value="#{listaPedidos.hora1O}" class="negrito-normal" converter="conversorHora"/>
                                                <h:outputText value=" - " class="negrito-normal" />
                                                <h:outputText value="#{listaPedidos.hora2O}" class="negrito-normal" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:column>
                                                <h:outputText class="Titulo" value="#{localemsgs.Endereco}: "/>
                                                <h:outputText value="#{listaPedidos.ende1}, #{listaPedidos.bairro1} - #{listaPedidos.cidade1}/#{listaPedidos.uf1}"
                                                              class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText class="Titulo" value="#{localemsgs.Regiao}: "/>
                                                <h:outputText value="#{listaPedidos.regiaoDesc1}" class="negrito-normal"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">   
                                            <p:column>                                                      
                                                <h:outputText class="negrito-maior" value="#{localemsgs.Destino}: "/>
                                                <h:outputText value="#{listaPedidos.codCli2} - #{listaPedidos.NRed2}" class="negrito-maior"/>
                                            </p:column>

                                            <p:column>
                                                <h:outputText value="#{localemsgs.Horario}: "/>
                                                <h:outputText value="#{listaPedidos.hora1D}" class="negrito-normal" converter="conversorHora"/>
                                                <h:outputText value=" - " class="negrito-normal" />
                                                <h:outputText value="#{listaPedidos.hora2D}" class="negrito-normal" converter="conversorHora"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="1" columnClasses="ui-grid-col-12" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:column>
                                                <h:outputText class="Titulo" value="#{localemsgs.Endereco}: "/>
                                                <h:outputText value="#{listaPedidos.ende2}, #{listaPedidos.bairro2} - #{listaPedidos.cidade2}/#{listaPedidos.uf2}" 
                                                              class="negrito-normal"/>
                                            </p:column>
                                            <p:column>
                                                <h:outputText class="Titulo" value="#{localemsgs.Regiao}: "/>
                                                <h:outputText value="#{listaPedidos.regiaoDesc2}" class="negrito-normal"/>
                                            </p:column>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-4,ui-grid-col-8" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">       

                                            <p:commandLink title="#{localemsgs.Editar}" update="msgs main:tabela" 
                                                           actionListener="#{pedidosContainer.abrirPedido(listaPedidos)}">
                                                <p:graphicImage url="../assets/img/icone_redondo_editar.png" height="30"/>
                                            </p:commandLink>
                                            <p:commandLink title="#{localemsgs.Excluir}" update="msgs main:tabela" rendered="#{listaPedidos.situacao.equals('PD')}"
                                                           actionListener="#{pedidosContainer.excluirPedido(listaPedidos)}">
                                                <p:confirm header="#{localemsgs.Atencao}" message="#{localemsgs.ConfirmaExclusao} #{localemsgs.Pedido}: #{listaPedidos.numero.toBigInteger()}" icon="pi pi-exclamation-triangle" />
                                                <p:graphicImage url="../assets/img/icone_redondo_excluir.png" height="30"/>
                                            </p:commandLink>                                            
                                        </p:panelGrid>

                                        <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank" style="background-color: whitesmoke; border:thin solid #DDD;margin-top:6px; border-radius: 2px; padding-left: 6px !important; padding-right: 6px !important;">                                            
                                            <p:column>
                                                <h:outputText value="#{localemsgs.Obs} " style="font-weight:bold; text-shadow:1px 1px #FFF;font-size:9pt !important; color: steelblue;"/>
                                                <h:outputText value="#{listaPedidos.obs.toUpperCase()}" style="display:block; " />
                                            </p:column>
                                        </p:panelGrid>
                                    </p:panel>
                                </p:dataGrid> 
                            </p:panel>
                            <p:panel style="position: fixed; z-index: 1; right: 5px; top: 100px; background: transparent" id="botoes">

                                <div style="padding-bottom: 10px;">
                                    <p:commandLink title="#{localemsgs.Pedido}" actionListener="#{pedidosContainer.preCadastro()}"
                                                   update="cadastrarPedido">
                                        <p:graphicImage url="../assets/img/icone_redondo_adicionar.png" height="40"/>
                                    </p:commandLink>
                                </div>

                                <p:draggable for="botoes" axis="y" opacity="0.3" cursor="grabbing" id="arraste"/>
                            </p:panel>
                        </h:form>                        
                    </div>

                    <h:form id="cadastrarPedido" class="form-inline">
                        <p:dialog styleClass="box-primary" widgetVar="dlgPedido" minHeight="500" modal="true" 
                                  id="dlgPedido" responsive="true" dynamic="true" style="background-size: 750px 430px;">
                            <script>
                                $(document).ready(function () {
                                    //first unbind the original click event
                                    PF('dlgPedido').closeIcon.unbind('click');

                                    //register your own
                                    PF('dlgPedido').closeIcon.click(function (e) {
                                        $("#cadastrarPedido\\:botaoFechar").click();
                                        //should be always called
                                        e.preventDefault();
                                    });
                                })
                            </script>
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgPedido').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert"/> 
                            </p:commandButton>
                            <f:facet name="header">
                                <img src="../assets/img/icone_solicitarpedidos.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Pedido}" style="color:#022a48"/>
                            </f:facet>
                            <p:panel id="cadastrar" style="background-color: transparent" styleClass="cadastro">
                                <p:confirmDialog global="true">
                                    <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" />
                                    <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
                                </p:confirmDialog>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="tipoServico" value="#{localemsgs.Servico}:"/>
                                    <p:selectOneRadio id="tipoServico" value="#{pedidosContainer.tipoPedido}" style="width: 100%">
                                        <f:selectItem itemLabel="#{localemsgs.Recolhimento}"  itemValue="recolhimento"/>
                                        <f:selectItem itemLabel="#{localemsgs.Entrega}"  itemValue="suprimento"/>
                                        <p:ajax event="change" listener="#{pedidosContainer.tipoServico}"/>
                                    </p:selectOneRadio>
                                    <p:outputLabel for="cliente" value="#{localemsgs.Cliente}:" indicateRequired="false"/>
                                    <p:autoComplete id="cliente" value="#{pedidosContainer.os_vig}" completeMethod="#{pedidosContainer.buscarOS}" 
                                                    minQueryLength="3" scrollHeight="200" var="cont" itemValue="#{cont}" itemLabel="#{cont.NRed}"
                                                    forceSelection="true" emptyMessage="#{localemsgs.ClienteNaoEncontradoOuSemCacambas}" required="true"
                                                    requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Cliente}">
                                        <p:column>
                                            <p:column>
                                                <i class="fas fa-plus" style="#{cont.OS eq '-1' ? 'display: inline; font-size: 10px;' : 'display: none'}"></i>
                                                <h:outputText value=" #{cont.NRed}" class="negrito-normal"/>
                                            </p:column>
                                            <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                                         layout="grid" styleClass="ui-panelgrid-blank" rendered="#{cont.OS ne '-1'}">
                                                <p:outputLabel for="osPedido" value="#{localemsgs.OS}:"/>
                                                <h:outputText id="osPedido" value="#{cont.OS}" class="negrito-normal">
                                                    <f:convertNumber pattern="0000"/>
                                                </h:outputText>

                                                <p:outputLabel for="enderecoPedido" value="#{localemsgs.Endereco}:"/>
                                                <h:outputText id="enderecoPedido" value="#{cont.endereco}" class="negrito-normal"/>

                                                <p:column>
                                                </p:column>
                                                <p:column>
                                                    <h:outputText value="#{cont.bairro} - #{cont.cidade}/#{cont.UF}" class="negrito-normal"/>
                                                </p:column>
                                            </p:panelGrid>
                                        </p:column>
                                        <p:ajax event="itemSelect" listener="#{pedidosContainer.selecionarCliente}" update="msgs cadastrarPedido:cadastrar"
                                                oncomplete="PF('dlgPedido').initPosition()"/>
                                        <p:watermark for="cliente" value="#{localemsgs.Cliente}" class="negrito-normal"/>
                                        <o:converter converterId="omnifaces.ListIndexConverter" list="#{pedidosContainer.listaOsVig}" />
                                    </p:autoComplete>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                             layout="grid" styleClass="ui-panelgrid-blank" rendered="#{pedidosContainer.os_vig ne null}">

                                    <p:outputLabel value="#{localemsgs.Endereco}:"/>
                                    <h:outputText value="#{pedidosContainer.os_vig.endereco}" class="negrito-normal"/>

                                    <p:column>
                                    </p:column>
                                    <p:column>
                                        <h:outputText value="#{pedidosContainer.os_vig.bairro} - #{pedidosContainer.os_vig.cidade}/#{pedidosContainer.os_vig.UF}" class="negrito-normal"/>
                                    </p:column>
                                </p:panelGrid>

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-3, ui-grid-col-3, ui-grid-col-3, ui-grid-col-3"
                                             layout="grid">
                                    <p:outputLabel value="#{localemsgs.QtdeCacambas}:"/>
                                    <p:inputText id="qtdeCacambas" value="#{pedidosContainer.pedido.qtdeCacambas}"  style="width: 100%">
                                        <p:watermark for="qtdeCacambas" value="#{localemsgs.QtdeCacambas}"/>
                                    </p:inputText>

                                    <p:outputLabel value="#{localemsgs.Orcamento}:"/>
                                    <p:inputText id="qtdeOrcamento" value="#{pedidosContainer.os_vig.GTVQtde}"  style="width: 100%" disabled="true">
                                        <p:watermark for="qtdeOrcamento" value="#{localemsgs.Orcamento}"/>
                                    </p:inputText>

                                </p:panelGrid>                                

                                <p:panelGrid columns="4" columnClasses="ui-grid-col-3, ui-grid-col-3, ui-grid-col-3, ui-grid-col-3"
                                             layout="grid">
                                    <p:outputLabel for="data" value="#{localemsgs.Data}:" indicateRequired="false"/>                                    
                                    <p:datePicker value="#{pedidosContainer.datad}" readonlyInput="true" id="data" 
                                                  pattern="#{mascaras.padraoData}" styleClass="calendario" 
                                                  required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Data}"
                                                  converter="conversorDate" locale="#{localeController.getCurrentLocale()}" />
                                </p:panelGrid>

                                <p:panelGrid columns="5" columnClasses="ui-grid-col-3, ui-grid-col-1, ui-grid-col-2, ui-grid-col-1, ui-grid-col-2"
                                             layout="grid">
                                    <p:outputLabel value="#{localemsgs.Horario}:"/>

                                    <p:outputLabel for="hora1" value="#{localemsgs.HoraDe}:" indicateRequired="false"/>
                                    <p:inputMask mask="#{mascaras.mascaraHora}" value="#{pedidosContainer.hora1}" 
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.InicioHorario}"
                                                 id="hora1" style="width: 100%" converter="conversorHora">
                                        <p:watermark for="hora1" value="#{localemsgs.Hora}"/>
                                    </p:inputMask>

                                    <p:outputLabel for="hora2" value="#{localemsgs.Ate}:" indicateRequired="false"/>
                                    <p:inputMask mask="#{mascaras.mascaraHora}" value="#{pedidosContainer.hora2}" 
                                                 required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.FinalHorario}"
                                                 id="hora2" style="width: 100%" converter="conversorHora">
                                        <p:watermark for="hora2" value="#{localemsgs.Ate}"/>
                                    </p:inputMask>
                                </p:panelGrid>

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-3, ui-grid-col-9" layout="grid">
                                    <p:outputLabel for="obs" value="#{localemsgs.Obs}:"/>
                                    <p:inputTextarea id="obs" value="#{pedidosContainer.pedido.obs}" label="#{localemsgs.Obs}"
                                                     style="width: 100%" rows="3" maxlength="80">
                                        <p:watermark for="obs" value="#{localemsgs.Obs}"/>
                                    </p:inputTextarea>

                                    <p:outputLabel for="solicitante" value="#{localemsgs.Solicitante}:"/>
                                    <p:inputText id="solicitante" value="#{pedidosContainer.pedido.solicitante}"   style="width: 100%">
                                        <p:watermark for="solicitante" value="#{localemsgs.Solicitante}"/>
                                    </p:inputText>

                                    <p:outputLabel for="pedidoCliente" value="#{localemsgs.PedidoCliente}:"/>
                                    <p:inputText id="pedidoCliente" value="#{pedidosContainer.pedido.pedidoCliente}"  style="width: 100%">
                                        <p:watermark for="pedidoCliente" value="#{localemsgs.Pedido} #{localemsgs.Cliente}"/>
                                    </p:inputText>
                                </p:panelGrid>  

                                <div class="form-inline">
                                    <p:commandLink id="cadastro" action="#{pedidosContainer.cadastrarPedido}"
                                                   title="#{localemsgs.Cadastrar}" update="msgs main:tabela">
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </div>

                            </p:panel>
                        </p:dialog>
                    </h:form>

                    <!-- Cadastrar novo cliente -->
                    <h:form id="formCadastrar" >
                        <p:dialog styleClass="box-primary" widgetVar="dlgCadastrar" minHeight="500" modal="true" 
                                  onShow="PF('dlgCadastrar').initPosition()"
                                  id="dlgCadastrar" responsive="true" dynamic="true" style="background-size: 750px 430px;">
                            <script>
                                $(document).ready(function () {
                                    //first unbind the original click event
                                    PF('dlgCadastrar').closeIcon.unbind('click');

                                    //register your own
                                    PF('dlgCadastrar').closeIcon.click(function (e) {
                                        $("#formCadastrar\\:botaoFechar").click();
                                        //should be always called
                                        e.preventDefault();
                                    });
                                })
                            </script>
                            <p:commandButton widgetVar="botaoFechar" style="display: none"
                                             oncomplete="PF('dlgCadastrar').hide()" id="botaoFechar">
                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                            </p:commandButton>
                            <f:facet name="header">
                                <img src="../assets/img/icone_clientes.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Clientes}" style="color:#022a48" /> 
                            </f:facet>
                            <p:panel id="cadastrar" styleClass="max-900">
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:outputLabel for="tpCli" value="#{localemsgs.TpCli}: "  />
                                    <p:selectOneMenu id="tpCli" value="#{pedidosContainer.novoCliente.tpCli}"
                                                     required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}"
                                                     styleClass="filial" style="width: 100%"
                                                     filter="true" filterMatchMode="contains">
                                        <f:selectItem itemLabel="#{localemsgs.EstabelecimentoComercialResidencia}" itemValue="0"/>
                                        <f:selectItem itemLabel="#{localemsgs.Cofre}" itemValue="4"/>
                                        <f:selectItem itemLabel="#{localemsgs.Tesouraria}" itemValue="7"/>
                                        <f:selectItem itemLabel="#{localemsgs.TransportadoraValores}" itemValue="8"/>
                                        <f:selectItem itemLabel="#{localemsgs.ATM}" itemValue="9"/>
                                    </p:selectOneMenu>                         

                                    <p:outputLabel for="nred" value="#{localemsgs.NRed}: " />
                                    <p:inputText id="nred" value="#{pedidosContainer.novoCliente.NRed}"
                                                 required="true" label="#{localemsgs.NRed}" style="width: 100%"
                                                 requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.NRed}"
                                                 maxlength="20">
                                        <p:watermark for="nred" value="#{localemsgs.NRed}"/>
                                    </p:inputText>
                                </p:panelGrid>
                                <p:tabView id="tabs" activeIndex="0" dynamic="true" orientation="left" onTabShow="PF('dlgCadastrar').initPosition()">
                                    <p:tab id="tabDados" title="#{localemsgs.Identificacao}">
                                        <p:confirmDialog global="true">
                                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                                        </p:confirmDialog>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">                                        
                                            <p:outputLabel for="nome" value="#{localemsgs.Nome}: " />
                                            <p:inputText id="nome" value="#{pedidosContainer.novoCliente.nome}"
                                                         label="#{localemsgs.Nome}" style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="nome" value="#{localemsgs.Nome}"/>
                                            </p:inputText>  

                                            <p:outputLabel for="regiao" value="#{localemsgs.Regiao}:"/>
                                            <p:selectOneMenu id="regiao" value="#{pedidosContainer.regiao}" converter="omnifaces.SelectItemsConverter"
                                                             styleClass="filial" style="width: 100%;"
                                                             filter="true" filterMatchMode="contains">
                                                <f:selectItems value="#{pedidosContainer.regioes}" var="regiao" itemValue="#{regiao}"
                                                               itemLabel="#{regiao.regiao} - #{regiao.descricao}" noSelectionValue=""/>
                                            </p:selectOneMenu>
                                        </p:panelGrid>


                                        <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-1,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank"> 
                                            <p:outputLabel for="cep" value="#{localemsgs.CEP}: "/>                   
                                            <p:inputText id="cep" value="#{pedidosContainer.novoCliente.CEP}"
                                                         maxlength="8" style="width: 100%">
                                                <p:watermark for="cep" value="#{localemsgs.CEP}"/>
                                            </p:inputText>

                                            <p:commandLink title="#{localemsgs.Pesquisar}"
                                                           partialSubmit="true" process="@this formCadastrar:tabs:cep" id="cep_pesquisa"
                                                           update="formCadastrar:tabs:cep formCadastrar:tabs:ende formCadastrar:tabs:bairro formCadastrar:tabs:cidade formCadastrar:tabs:estado msgs"
                                                           actionListener="#{pedidosContainer.buscarEndereco}">
                                                <p:graphicImage url="../assets/img/icone_redondo_pesquisar.png" height="30"/>
                                                <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.AtualizarCEP}" icon="ui-icon-alert" />
                                                <p:dialog header="#{localemsgs.Aviso}" widgetVar="dlgOk" resizable="false"
                                                          draggable="false" closable="true" width="300">
                                                    <div class="form-inline">
                                                        <h:outputText value="#{localemsgs.CompletarEndereco}" style="text-align: center"/>
                                                        <p:spacer height="20px"/>
                                                    </div>
                                                    <p:commandButton value="#{localemsgs.OK}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"
                                                                     onclick="PF('dlgOk').hide();" />
                                                </p:dialog>    
                                            </p:commandLink>

                                            <p:outputLabel for="bairro" value="#{localemsgs.Bairro}: "/>
                                            <p:inputText id="bairro" value="#{pedidosContainer.novoCliente.bairro}"
                                                         label="#{localemsgs.Bairro}" style="width: 100%"
                                                         maxlength="25">
                                                <f:validateLength maximum="25"/>
                                                <p:watermark for="bairro" value="#{localemsgs.Bairro}"/>
                                            </p:inputText> 
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="ende" value="#{localemsgs.Endereco}: "/>
                                            <p:inputText id="ende" value="#{pedidosContainer.novoCliente.ende}"
                                                         style="width: 100%"
                                                         maxlength="60">
                                                <p:watermark for="ende" value="#{localemsgs.Endereco}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-6,ui-grid-col-2,ui-grid-col-2" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="cidade" value="#{localemsgs.Cidade}: "/>
                                            <p:autoComplete id="cidade" value="#{pedidosContainer.novoCliente.cidade}" styleClass="cidade" 
                                                            completeMethod="#{pedidosContainer.buscarCidade}" scrollHeight="200"
                                                            maxlength="25" forceSelection="true" style="width: 100%">
                                                <p:ajax event="itemSelect" listener="#{pedidosContainer.selecionarCidade}"
                                                        update="formCadastrar:tabs:cidade formCadastrar:tabs:estado"/>
                                                <p:watermark for="cidade" value="#{localemsgs.Cidade}"/>
                                            </p:autoComplete>

                                            <p:outputLabel for="estado" value="#{localemsgs.UF}: "/>
                                            <p:inputText id="estado" value="#{pedidosContainer.novoCliente.estado}" style="width: 100%"
                                                         label="#{localemsgs.UF}" disabled="true" maxlength="2">
                                                <p:watermark for="estado" value="#{localemsgs.UF}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="fone1" value="#{localemsgs.Fone1}: "/>
                                            <p:inputMask id="fone1" value="#{pedidosContainer.novoCliente.fone1}" style="width: 100%"
                                                         label="#{localemsgs.Fone1}" maxlength="11" mask="#{mascaras.mascaraFone}">
                                                <p:watermark for="fone1" value="#{localemsgs.Fone1}"/>
                                            </p:inputMask>

                                            <p:outputLabel for="fone2" value="#{localemsgs.Fone2}: "/>
                                            <p:inputMask id="fone2" value="#{pedidosContainer.novoCliente.fone2}" style="width: 100%"
                                                         label="#{localemsgs.Fone2}" maxlength="11" mask="#{mascaras.mascaraFone}">
                                                <p:watermark for="fone2" value="#{localemsgs.Fone2}"/>
                                            </p:inputMask>

                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="atividade" value="#{localemsgs.Atividade}: "/>
                                            <p:selectOneMenu id="atividade" value="#{pedidosContainer.novoCliente.ramoAtiv}" converter="omnifaces.SelectItemsConverter"
                                                             styleClass="filial" style="width: 100%;"
                                                             filter="true" filterMatchMode="contains">
                                                <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="0" noSelectionOption="true"/>
                                                <f:selectItems value="#{pedidosContainer.ramosAtiv}" var="ramosAtiv" itemValue="#{ramosAtiv.codigo}"
                                                               itemLabel="#{ramosAtiv.codigo} - #{ramosAtiv.descricao}"/>
                                            </p:selectOneMenu>

                                            <p:outputLabel for="email" value="#{localemsgs.Email}: "/>
                                            <p:inputText id="email" value="#{pedidosContainer.novoCliente.email}" style="width: 100%"
                                                         label="#{localemsgs.Email}" maxlength="80">
                                                <p:watermark for="email" value="#{localemsgs.Email}"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="cnpj" value="#{localemsgs.CNPJCPF}: "/>
                                            <p:inputText id="cnpj" value="#{pedidosContainer.cpfcnpj}"
                                                         label="#{localemsgs.CNPJCPF}" style="width: 100%">
                                                <p:watermark for="cnpj" value="#{localemsgs.CNPJCPF}"/>
                                                <p:ajax event="blur" listener="#{pedidosContainer.mascaraCNPJCPF}"
                                                        update="formCadastrar:tabs:cnpj msgs"/>
                                            </p:inputText>
                                        </p:panelGrid>

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">

                                            <p:outputLabel for="ierg" value="#{localemsgs.IERG}: "/>
                                            <p:inputText id="ierg" value="#{pedidosContainer.ierg}"
                                                         label="#{localemsgs.IERG}" style="width: 100%">
                                                <p:watermark for="ierg" value="#{localemsgs.IERG}"/>
                                            </p:inputText>

                                            <p:outputLabel for="im" value="#{localemsgs.Insc_Munic}: "/>
                                            <p:inputText id="im" value="#{pedidosContainer.novoCliente.insc_Munic}"
                                                         label="#{localemsgs.Insc_Munic}" style="width: 100%">
                                                <p:watermark for="im" value="#{localemsgs.Insc_Munic}"/>
                                            </p:inputText>
                                        </p:panelGrid>   
                                    </p:tab>
                                    <p:tab id="tabOrcamento" title="#{localemsgs.Orcamento}">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="qtdeCacambas" value="#{localemsgs.Orcamento}: " />
                                            <p:inputText id="qtdeCacambas" value="#{pedidosContainer.qtdeCacambas}"
                                                         label="#{localemsgs.Orcamento}" maxlength="2" style="width: 100%">
                                                <p:watermark for="qtdeCacambas" value="#{localemsgs.Orcamento}"/>
                                            </p:inputText>
                                        </p:panelGrid>                                         
                                    </p:tab>
                                    <p:tab id="tabInterface" title="#{localemsgs.Interface}">
                                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="interfext" value="#{localemsgs.InterfExt}: " />
                                            <p:inputText id="interfext" value="#{pedidosContainer.novoCliente.interfExt}"
                                                         label="#{localemsgs.InterfExt}" maxlength="80" style="width: 100%">
                                                <p:watermark for="interfext" value="#{localemsgs.InterfExt}"/>
                                            </p:inputText>
                                        </p:panelGrid> 

                                        <p:panelGrid columns="5" columnClasses="ui-grid-col-2,ui-grid-col-3,ui-grid-col-2,ui-grid-col-3,ui-grid-col-2" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="latitude" value="#{localemsgs.Latitude}: " />
                                            <p:inputText id="latitude" value="#{pedidosContainer.novoCliente.latitude}"
                                                         label="#{localemsgs.Latitude}" style="width: 100%">
                                                <p:watermark for="latitude" value="#{localemsgs.Latitude}"/>
                                            </p:inputText>

                                            <p:outputLabel for="longitude" value="#{localemsgs.Longitude}: " />
                                            <p:inputText id="longitude" value="#{pedidosContainer.novoCliente.longitude}"
                                                         label="#{localemsgs.Longitude}" style="width: 100%">
                                                <p:watermark for="longitude" value="#{localemsgs.Longitude}"/>
                                            </p:inputText>

                                            <p:commandLink title="#{localemsgs.Mapa}"
                                                           update="msgs" action="#{pedidosContainer.posicaoCliente}">
                                                <p:graphicImage url="../assets/img/icone_redondo_mapa.png" height="30"/>
                                            </p:commandLink>
                                        </p:panelGrid> 

                                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" 
                                                     layout="grid" styleClass="ui-panelgrid-blank">
                                            <p:outputLabel for="codExt" value="#{localemsgs.CodExt}: " />
                                            <p:inputText id="codExt" value="#{pedidosContainer.novoCliente.codExt}"
                                                         label="#{localemsgs.CodExt}" maxlength="80" style="width: 100%">
                                                <p:watermark for="codExt" value="#{localemsgs.CodExt}"/>
                                            </p:inputText>

                                            <p:outputLabel for="codPtoCli" value="#{localemsgs.CodPtoCli}: " />
                                            <p:inputText id="codPtoCli" value="#{pedidosContainer.novoCliente.codPtoCli}"
                                                         label="#{localemsgs.CodPtoCli}" maxlength="80" style="width: 100%">
                                                <p:watermark for="codPtoCli" value="#{localemsgs.CodPtoCli}"/>
                                            </p:inputText>

                                        </p:panelGrid> 
                                    </p:tab>
                                </p:tabView>    

                                <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" 
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:commandLink id="cadastro" action="#{pedidosContainer.cadastrarCliente}"
                                                   update="msgs"
                                                   title="#{localemsgs.Cadastrar}" >
                                        <p:graphicImage url="../assets/img/icone_adicionar.png" width="40" height="40" />
                                    </p:commandLink>
                                </p:panelGrid>
                            </p:panel>
                        </p:dialog>

                        <p:dialog widgetVar="dlgAdicionarRegiao" id="dlgAdicionarRegiao" responsive="true" header="#{localemsgs.Regioes}"
                                  draggable="false" modal="true" closable="true" dynamic="true"
                                  resizable="false" width="400" showEffect="drop" closeOnEscape="false" hideEffect="fade" styleClass="dialogo">
                            <p:panelGrid columns="2" id="addRegiao" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="novaRegiao" value="#{localemsgs.CodigoRegiao}:"/>
                                <p:inputText id="novaRegiao" value="#{pedidosContainer.novaRegiao.regiao}"
                                             style="width: 100%"> 
                                    <p:watermark for="novaRegiao" value="#{localemsgs.CodigoRegiao}"/>
                                </p:inputText>

                                <p:outputLabel for="novaRegiaoDescricao" value="#{localemsgs.Descricao}:"/>
                                <p:inputText id="novaRegiaoDescricao" value="#{pedidosContainer.novaRegiao.descricao}"
                                             style="width: 100%"> 
                                    <p:watermark for="novaRegiaoDescricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>

                                <p:outputLabel for="novaRegiaoArea" value="#{localemsgs.Abrangencia}:"/>
                                <p:inputText id="novaRegiaoArea" value="#{pedidosContainer.novaRegiao.area}"
                                             style="width: 100%"> 
                                    <p:watermark for="novaRegiaoArea" value="#{localemsgs.Abrangencia}"/>
                                </p:inputText>

                                <p:outputLabel for="novaRegiaoLocal" value="#{localemsgs.TipoLocalizacao}:"/>
                                <p:selectOneMenu id="novaRegiaoLocal" value="#{pedidosContainer.novaRegiao.local}" 
                                                 styleClass="filial" style="width: 100%">
                                    <f:selectItem itemLabel="#{localemsgs.Selecione}" itemValue="" noSelectionOption="true"/>
                                    <f:selectItem itemLabel="#{localemsgs.Urbano}" itemValue="1"/>
                                    <f:selectItem itemLabel="#{localemsgs.Interurbano}" itemValue="2"/>
                                </p:selectOneMenu>  

                                <p:commandLink  action="#{pedidosContainer.cadastrarRegiao}"
                                                process="@this novaRegiao novaRegiaoDescricao novaRegiaoArea novaRegiaoLocal" 
                                                update="formCadastrar:tabs:regiao msgs" 
                                                style="float: left; top:20px; left: 350px;">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30"/>
                                </p:commandLink>
                            </p:panelGrid>
                        </p:dialog>

                        <p:dialog widgetVar="dlgAdicionarRamosAtiv" id="dlgAdicionarRamosAtiv" responsive="true" header="#{localemsgs.Atividades}"
                                  draggable="false" modal="true" closable="true" dynamic="true"
                                  resizable="false" width="400" showEffect="drop" closeOnEscape="false" hideEffect="fade" styleClass="dialogo">
                            <p:panelGrid columns="2" id="addRamosAtiv" columnClasses="ui-grid-col-4,ui-grid-col-8"
                                         layout="grid" styleClass="ui-panelgrid-blank">
                                <p:outputLabel for="novaRamosAtivCodigo" value="#{localemsgs.Codigo}:"/>
                                <p:inputText id="novaRamosAtivCodigo" value="#{pedidosContainer.ramoAtiv.codigo}"
                                             style="width: 100%"> 
                                    <p:watermark for="novaRamosAtivCodigo" value="#{localemsgs.Codigo}"/>
                                </p:inputText>

                                <p:outputLabel for="novaRamosAtivDescricao" value="#{localemsgs.Descricao}:"/>
                                <p:inputText id="novaRamosAtivDescricao" value="#{pedidosContainer.ramoAtiv.descricao}"
                                             style="width: 100%"> 
                                    <p:watermark for="novaRamosAtivDescricao" value="#{localemsgs.Descricao}"/>
                                </p:inputText>

                                <p:commandLink  action="#{pedidosContainer.cadastrarRamosAtiv}"
                                                process="@this novaRamosAtivCodigo novaRamosAtivDescricao" 
                                                update="formCadastrar:tabs:atividade msgs" 
                                                style="float: left; top:20px; left: 350px;">
                                    <p:graphicImage url="../assets/img/icone_adicionar.png" width="30" height="30"/>
                                </p:commandLink>
                            </p:panelGrid>
                        </p:dialog>

                        <p:dialog id="dlgMapaCliente" widgetVar="dlgMapaCliente" positionType="absolute" responsive="true"
                                  draggable="false" modal="true" closable="true" resizable="false" dynamic="true" 
                                  showEffect="drop" hideEffect="drop" closeOnEscape="false" 
                                  style=" background-image: url('assets/img/menu_fundo.png'); background-size: 750px 430px;">
                            <f:facet name="header">
                                <img src="../assets/img/icone_satmob_trajetos_40x40.png" height="40" width="40"/>
                                <p:spacer width="5px"/>
                                <h:outputText value="#{localemsgs.Clientes}" style="color:black" />
                            </f:facet>
                            <p:panel style="width:calc(100vw - 100px);height:calc(100vh - 100px);background-color: transparent" styleClass="cadastrar">
                                <p:gmap id="gmap" center="#{pedidosContainer.centroMapa}" zoom="16" type="TERRAIN" 
                                        style="height:85vh" model="#{pedidosContainer.mapaCliente}" />
                            </p:panel>
                        </p:dialog>
                    </h:form>
                </ui:define>
            </ui:composition>
        </h:body>
    </f:view>
</html>
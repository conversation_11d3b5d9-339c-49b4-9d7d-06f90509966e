/*
 */
package SasBeans;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class TesEntDif {

    BigDecimal Guia;
    String Serie;
    Integer Sequencia;
    String Tipo;
    String Docto;
    String Sangria;
    Integer CodMotivo;
    BigDecimal Valor;
    String Agencia;
    String Conta;
    Integer Camera;
    String Hora;
    String HoraFim;
    BigDecimal Tempo;
    BigDecimal MatrConf;
    BigDecimal MatrCoord;
    String Obs;
    String Historico;
    String DtMovto;
    String Operador;
    String Dt_Alter;
    String Hr_Alter;

    public BigDecimal getGuia() {
        return Guia;
    }

    public void setGuia(BigDecimal Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public Integer getSequencia() {
        return Sequencia;
    }

    public void setSequencia(Integer Sequencia) {
        this.Sequencia = Sequencia;
    }

    public String getTipo() {
        return Tipo;
    }

    public void setTipo(String Tipo) {
        this.Tipo = Tipo;
    }

    public String getDocto() {
        return Docto;
    }

    public void setDocto(String Docto) {
        this.Docto = Docto;
    }

    public String getSangria() {
        return Sangria;
    }

    public void setSangria(String Sangria) {
        this.Sangria = Sangria;
    }

    public Integer getCodMotivo() {
        return CodMotivo;
    }

    public void setCodMotivo(Integer CodMotivo) {
        this.CodMotivo = CodMotivo;
    }

    public BigDecimal getValor() {
        return Valor;
    }

    public void setValor(BigDecimal Valor) {
        this.Valor = Valor;
    }

    public String getAgencia() {
        return Agencia;
    }

    public void setAgencia(String Agencia) {
        this.Agencia = Agencia;
    }

    public String getConta() {
        return Conta;
    }

    public void setConta(String Conta) {
        this.Conta = Conta;
    }

    public Integer getCamera() {
        return Camera;
    }

    public void setCamera(Integer Camera) {
        this.Camera = Camera;
    }

    public String getHora() {
        return Hora;
    }

    public void setHora(String Hora) {
        this.Hora = Hora;
    }

    public String getHoraFim() {
        return HoraFim;
    }

    public void setHoraFim(String HoraFim) {
        this.HoraFim = HoraFim;
    }

    public BigDecimal getTempo() {
        return Tempo;
    }

    public void setTempo(BigDecimal Tempo) {
        this.Tempo = Tempo;
    }

    public BigDecimal getMatrConf() {
        return MatrConf;
    }

    public void setMatrConf(BigDecimal MatrConf) {
        this.MatrConf = MatrConf;
    }

    public BigDecimal getMatrCoord() {
        return MatrCoord;
    }

    public void setMatrCoord(BigDecimal MatrCoord) {
        this.MatrCoord = MatrCoord;
    }

    public String getObs() {
        return Obs;
    }

    public void setObs(String Obs) {
        this.Obs = Obs;
    }

    public String getHistorico() {
        return Historico;
    }

    public void setHistorico(String Historico) {
        this.Historico = Historico;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getHr_Alter() {
        return Hr_Alter;
    }

    public void setHr_Alter(String Hr_Alter) {
        this.Hr_Alter = Hr_Alter;
    }

    public String getDtMovto() {
        return DtMovto;
    }

    public void setDtMovto(String DtMovto) {
        this.DtMovto = DtMovto;
    }

    public String getDt_Alter() {
        return Dt_Alter;
    }

    public void setDt_Alter(String Dt_Alter) {
        this.Dt_Alter = Dt_Alter;
    }
}
